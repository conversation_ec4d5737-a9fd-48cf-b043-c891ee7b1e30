<template>
  <view class="chart-container">
    <view class="chart-header">
      <text class="chart-title">分数分布</text>
    </view>
    <view class="chart-content">
      <canvas
          id="scoreDistributionChart"
          canvas-id="scoreDistributionChart"
          class="chart-canvas"
          @touchend="touchEnd"
          @touchmove="touchMove"
          @touchstart="touchStart"
      ></canvas>
    </view>
    <view class="chart-legend">
      <view
          v-for="(item, index) in legendData"
          :key="index"
          :class="item.class"
          class="legend-item"
      >
        <view :style="{ backgroundColor: item.color }" class="legend-color"></view>
        <text class="legend-label">{{ item.label }}</text>
        <text class="legend-value">{{ item.value }}人</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {ref, onMounted, watch} from 'vue'
import uCharts from '@qiun/ucharts'
import {columnChartConfig, durationDistributionColors} from './chartConfig'

interface ScoreDistribution {
  excellent: number
  good: number
  moderate: number
  pass: number
  fail: number
}

interface Props {
  data: ScoreDistribution
}

const props = defineProps<Props>()

const chartInstance = ref<any>(null)
const legendData = ref<any[]>([
  {label: '优秀(90-100)', value: 5, color: durationDistributionColors[0], class: 'excellent'},
  {label: '良好(80-89)', value: 12, color: durationDistributionColors[1], class: 'good'},
  {label: '中等(70-79)', value: 18, color: durationDistributionColors[2], class: 'moderate'},
  {label: '及格(60-69)', value: 15, color: durationDistributionColors[3], class: 'pass'},
  {label: '不及格(0-59)', value: 8, color: durationDistributionColors[4], class: 'fail'}
])

// 图表配置 - 改为柱状图
const chartConfig = {
  type: 'column',
  context: null as any,
  width: 300,
  height: 250,
  categories: ['优秀', '良好', '中等', '及格', '不及格'],
  series: [
    {
      name: '分数分布',
      data: [5, 12, 18, 15, 8]
    }
  ],
  color: durationDistributionColors,
  legend: {
    show: false
  },
  extra: {
    column: {
      width: 25,
      activeBgColor: '#000000',
      activeBgOpacity: 0.08
    }
  },
  yAxis: {
    gridType: 'dash',
    dashLength: 2,
    data: []
  },
  xAxis: {
    disableGrid: true
  },
  animation: true,
  background: '#FFFFFF',
  padding: [15, 15, 15, 15],
  dataLabel: true,
  dataPointShape: false
}

// 更新图表数据
const updateChartData = () => {
  if (!props.data) return

  const excellent = Number(props.data.excellent) || 0
  const good = Number(props.data.good) || 0
  const moderate = Number(props.data.moderate) || 0
  const pass = Number(props.data.pass) || 0
  const fail = Number(props.data.fail) || 0

  const data = [excellent, good, moderate, pass, fail]

  // 检查是否有数据
  const total = data.reduce((sum, val) => sum + val, 0)
  if (total > 0) {
    chartConfig.series[0].data = data

    // 更新图例数据
    legendData.value = [
      {label: '优秀(90-100)', value: data[0], color: durationDistributionColors[0], class: 'excellent'},
      {label: '良好(80-89)', value: data[1], color: durationDistributionColors[1], class: 'good'},
      {label: '中等(70-79)', value: data[2], color: durationDistributionColors[2], class: 'moderate'},
      {label: '及格(60-69)', value: data[3], color: durationDistributionColors[3], class: 'pass'},
      {label: '不及格(0-59)', value: data[4], color: durationDistributionColors[4], class: 'fail'}
    ]
  }
  // 更新图表
  if (chartInstance.value) {
    chartInstance.value.updateData({
      series: chartConfig.series
    })
  }
}

// 初始化图表
const initChart = () => {

  const query = uni.createSelectorQuery()
  query.select('#scoreDistributionChart')
      .fields({node: true, size: true})
      .exec((res) => {
        if (res[0] && res[0].node) {
          try {
            const canvas = res[0].node
            const ctx = canvas.getContext('2d')

            if (!ctx) {
              return
            }

            // 设置图表配置
            const config = {
              ...chartConfig,
              context: ctx,
              width: res[0].width || 300,
              height: res[0].height || 250
            }
            console.log(config)
            // 创建图表实例
            chartInstance.value = new uCharts(config)

            // 更新数据
            updateChartData()
          } catch (error) {
            console.error('初始化图表失败:', error)
          }
        } else {
          console.error('Canvas元素未找到')
        }
      })
}

// 触摸事件处理
const touchStart = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.touchLegend(e)
    chartInstance.value.showToolTip(e)
  }
}

const touchMove = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.scroll(e)
  }
}

const touchEnd = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.touchLegend(e)
  }
}

// 监听数据变化
watch(() => props.data, updateChartData, {deep: true})

onMounted(() => {
  setTimeout(() => {
    initChart()
  }, 300)
})
</script>

<style lang="scss" scoped>
.chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.chart-content {
  display: flex;
  justify-content: center;
}

.chart-canvas {
  width: 300px;
  height: 250px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border-radius: 6px;
  background: #f8faff;
  min-width: 80px;

  &.excellent {
    background: rgba(16, 185, 129, 0.1);
  }

  &.good {
    background: rgba(59, 130, 246, 0.1);
  }

  &.moderate {
    background: rgba(245, 158, 11, 0.1);
  }

  &.pass {
    background: rgba(139, 92, 246, 0.1);
  }

  &.fail {
    background: rgba(239, 68, 68, 0.1);
  }
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  flex-shrink: 0;
}

.legend-label {
  font-size: 11px;
  color: #4a5568;
  white-space: nowrap;
}

.legend-value {
  font-size: 11px;
  font-weight: 600;
  color: #1a1d2e;
}
</style>
