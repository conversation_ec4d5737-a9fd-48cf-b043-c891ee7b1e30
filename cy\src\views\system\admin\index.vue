<template>
  <div class="system-admin">
    <div class="page-container">
      <div class="page-toolbar">
        <div class="toolbar-left">
          <h3>管理员管理</h3>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索姓名或用户名"
            style="width: 250px; margin-right: 12px;"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" :icon="Plus" @click="handleAdd">新增管理员</el-button>
          <el-button :icon="Upload" @click="handleImport">导入管理员</el-button>
          <el-button :icon="Download" @click="handleExport">导出列表</el-button>
        </div>
      </div>

      <el-table
        :data="adminList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar" />
          </template>
        </el-table-column>
        <el-table-column prop="realName" label="姓名" min-width="100" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="department" label="部门" min-width="120" />
        <el-table-column prop="phone" label="手机号" min-width="130" />
        <el-table-column prop="lastLoginTime" label="最后登录" min-width="150" />
        <el-table-column prop="createTime" label="创建时间" min-width="150" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="280">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleEdit(row)">编辑</el-button>
            <el-button text type="warning" @click="handleResetPassword(row)">重置密码</el-button>
            <el-button text type="info" @click="handleSetPermission(row)">设置权限</el-button>
            <el-button text type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 新增/编辑管理员弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑管理员' : '新增管理员'"
      width="600px"
      destroy-on-close
    >
      <el-form :model="adminForm" :rules="adminRules" ref="adminFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="realName">
              <el-input v-model="adminForm.realName" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="adminForm.username" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="!isEdit">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input v-model="adminForm.password" type="password" placeholder="请输入密码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input v-model="adminForm.confirmPassword" type="password" placeholder="请确认密码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="adminForm.phone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="adminForm.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属部门" prop="departmentId">
              <el-cascader
                v-model="selectedDepartment"
                :options="departmentOptions"
                :props="{ 
                  value: 'id',
                  label: 'name',
                  children: 'children',
                  expandTrigger: 'hover',
                  checkStrictly: true 
                }"
                @change="handleDepartmentChange"
                placeholder="请选择部门"
                style="width: 100%;"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="adminForm.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="adminForm.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 权限设置弹窗 -->
    <el-dialog v-model="permissionDialogVisible" title="设置权限" width="600px">
      <div v-if="currentAdmin">
        <h4>{{ currentAdmin.realName }} - 权限设置</h4>
        <el-divider />
        <el-tree
          :data="permissionTree"
          :props="treeProps"
          node-key="id"
          show-checkbox
          :default-checked-keys="checkedPermissions"
          ref="permissionTreeRef"
        >
          <template #default="{ node, data }">
            <span style="display: flex; align-items: center;">
              <el-icon style="margin-right: 6px;"><component :is="data.icon || 'Setting'" /></el-icon>
              <span>{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </div>
      <template #footer>
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSavePermissions">保存</el-button>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      title="导入管理员"
      template-name="管理员"
      template-url="/api/admin/import/template"
      upload-action="/api/admin/import"
      import-rules="请确保Excel文件格式正确，必填字段不能为空"
      @success="handleImportSuccess"
    />

    <!-- 导出对话框 -->
    <ExportDialog
      v-model="exportDialogVisible"
      title="导出管理员"
      export-url="/api/admin/export"
      :support-csv="true"
      :support-range="true"
      :support-time-range="true"
      :selected-count="selectedAdmins.length"
      :fields="[
        { key: 'realName', label: '姓名', required: true },
        { key: 'username', label: '用户名', required: true },
        { key: 'phone', label: '手机号' },
        { key: 'email', label: '邮箱' },
        { key: 'department', label: '部门' },
        { key: 'lastLoginTime', label: '最后登录时间' },
        { key: 'createTime', label: '创建时间' },
        { key: 'status', label: '状态' }
      ]"
      :default-params="{
        keyword: searchForm.keyword,
        departmentId: searchForm.departmentId,
        selectedIds: selectedAdmins.map(admin => admin.id)
      }"
      description="可选择导出全部数据、当前页面数据或已选择的数据"
      @success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import {
  Search,
  Plus,
  Upload,
  Download,
  Setting,
  User,
  Document,
  Menu
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAdminList, getAdminById, addAdmin, updateAdmin, deleteAdmin, 
         updateAdminStatus, resetAdminPassword, getAdminPermissions, 
         setAdminPermissions, exportAdminList, type Admin } from '@/api/admin'
import { getDepartmentTree, type Department } from '@/api/department'
import ImportDialog from '@/components/ImportExport/ImportDialog.vue'
import ExportDialog from '@/components/ImportExport/ExportDialog.vue'

const loading = ref(false)
const dialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const isEdit = ref(false)
const selectedAdmins = ref<Admin[]>([])
const currentAdmin = ref<Admin | null>(null)
const checkedPermissions = ref<number[]>([])
const adminList = ref<Admin[]>([])
const departmentTree = ref<Department[]>([])
const selectedDepartment = ref<number[]>([])

// 导入导出相关
const importDialogVisible = ref(false)
const exportDialogVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  departmentId: undefined as number | undefined,
  status: undefined as number | undefined
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 管理员表单
const adminForm = reactive<{
  id: number | undefined
  realName: string
  username: string
  password: string
  confirmPassword: string
  phone: string
  email: string
  departmentId: number | number[] | undefined
  status: number
  remark: string
}>({
  id: undefined,
  realName: '',
  username: '',
  password: '',
  confirmPassword: '',
  phone: '',
  email: '',
  departmentId: undefined,
  status: 1,
  remark: ''
})

// 表单验证规则
const adminRules = {
  realName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度3-20位', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度6-20位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== adminForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  departmentId: [{ required: true, message: '请选择部门', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 树形配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 计算部门选项，用于级联选择器
const departmentOptions = computed(() => {
  return departmentTree.value
})

// 权限树数据 - 从接口获取
const permissionTree = ref([
  {
    id: 1,
    name: '系统管理',
    icon: 'Setting',
    children: [
      { id: 11, name: '管理员管理', icon: 'User' },
      { id: 12, name: '部门管理', icon: 'OfficeBuilding' }
    ]
  },
  {
    id: 2,
    name: '学员管理',
    icon: 'UserFilled',
    children: [
      { id: 21, name: '学员信息', icon: 'User' },
      { id: 22, name: '学员详情', icon: 'Document' }
    ]
  },
  {
    id: 3,
    name: '论坛管理',
    icon: 'ChatDotRound',
    children: [
      { id: 31, name: '帖子管理', icon: 'ChatLineRound' },
      { id: 32, name: '评论管理', icon: 'Comment' }
    ]
  }
])

const adminFormRef = ref()
const permissionTreeRef = ref()

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadAdminList()
}

// 加载管理员列表
const loadAdminList = async () => {
  loading.value = true
  try {
    const res = await getAdminList({
      keyword: searchForm.keyword,
      departmentId: searchForm.departmentId,
      page: pagination.page,
      size: pagination.size
    })
    
    adminList.value = res.list
    pagination.total = res.total
  } catch (error) {
    console.error('加载管理员列表失败:', error)
    ElMessage.error('加载管理员列表失败')
  } finally {
    loading.value = false
  }
}

// 加载部门树
const loadDepartmentTree = async () => {
  try {
    const res = await getDepartmentTree()
    departmentTree.value = res
  } catch (error) {
    console.error('加载部门树失败:', error)
    ElMessage.error('加载部门树失败')
  }
}

// 新增管理员
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑管理员
const handleEdit = async (row: Admin) => {
  isEdit.value = true
  resetForm()
  
  try {
    const adminDetail = await getAdminById(row.id)
    Object.assign(adminForm, adminDetail)
    
    // 设置部门选择器的值
    if (adminDetail.departmentId) {
      // 查找部门的完整路径
      const departmentPath = findDepartmentPath(departmentTree.value, adminDetail.departmentId)
      if (departmentPath.length > 0) {
        selectedDepartment.value = departmentPath
      }
    }
  } catch (error) {
    console.error('获取管理员详情失败:', error)
    ElMessage.error('获取管理员详情失败')
    return
  }
  
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(adminForm, {
    id: undefined,
    realName: '',
    username: '',
    password: '',
    confirmPassword: '',
    phone: '',
    email: '',
    departmentId: undefined,
    status: 1,
    remark: ''
  })
  // 清空部门选择
  selectedDepartment.value = []
  adminFormRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = () => {
  adminFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 确保在API调用前，提交的数据是正确的
        const formData = { ...adminForm };
        
        if (isEdit.value) {
          // 编辑管理员 - 使用类型断言
          await updateAdmin(formData as Partial<Admin>);
          ElMessage.success('编辑成功');
        } else {
          // 新增管理员 - 使用类型断言
          await addAdmin(formData as Partial<Admin> & { password: string });
          ElMessage.success('新增成功');
        }
        
        dialogVisible.value = false;
        loadAdminList();
      } catch (error) {
        console.error('保存管理员失败:', error);
        ElMessage.error('保存失败');
      }
    }
  });
}

// 状态变化
const handleStatusChange = async (row: Admin) => {
  try {
    await updateAdminStatus(row.id, row.status)
    const status = row.status ? '启用' : '禁用'
    ElMessage.success(`${status}成功`)
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新状态失败')
    // 恢复状态
    row.status = row.status ? 0 : 1
  }
}

// 重置密码
const handleResetPassword = (row: Admin) => {
  ElMessageBox.confirm('确定要重置该管理员的密码吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await resetAdminPassword(row.id)
      ElMessage.success(`密码重置成功，新密码为：${res.password}`)
    } catch (error) {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  })
}

// 设置权限
const handleSetPermission = async (row: Admin) => {
  currentAdmin.value = row
  
  try {
    const res = await getAdminPermissions(row.id)
    checkedPermissions.value = res.permissions
    permissionDialogVisible.value = true
  } catch (error) {
    console.error('获取权限失败:', error)
    ElMessage.error('获取权限失败')
  }
}

// 保存权限
const handleSavePermissions = async () => {
  if (!currentAdmin.value) return
  
  const checkedKeys = permissionTreeRef.value.getCheckedKeys()
  const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys()
  const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]
  
  try {
    await setAdminPermissions(currentAdmin.value.id, allCheckedKeys)
    ElMessage.success('权限设置成功')
    permissionDialogVisible.value = false
  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('保存权限失败')
  }
}

// 删除管理员
const handleDelete = (row: Admin) => {
  ElMessageBox.confirm('确定要删除该管理员吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteAdmin(row.id)
      ElMessage.success('删除成功')
      loadAdminList()
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

// 导入管理员
const handleImport = () => {
  importDialogVisible.value = true
}

// 导出管理员
const handleExport = () => {
  exportDialogVisible.value = true
}

// 导入成功回调
const handleImportSuccess = () => {
  loadAdminList()
  ElMessage.success('导入完成')
}

// 导出成功回调
const handleExportSuccess = () => {
  ElMessage.success('导出完成')
}

// 表格选择变化
const handleSelectionChange = (selection: Admin[]) => {
  selectedAdmins.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  loadAdminList()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadAdminList()
}

const handleDepartmentChange = (value: number[]) => {
  selectedDepartment.value = value;
  // 如果选择了部门，取数组的最后一个值作为实际部门ID
  if (value && value.length > 0) {
    adminForm.departmentId = value[value.length - 1];
  } else {
    adminForm.departmentId = undefined;
  }
}

// 根据部门ID查找完整部门路径
const findDepartmentPath = (departments: Department[], targetId: number, currentPath: number[] = []): number[] => {
  for (const dept of departments) {
    // 创建当前路径
    const newPath = [...currentPath, dept.id]
    
    // 如果找到目标ID，返回路径
    if (dept.id === targetId) {
      return newPath
    }
    
    // 递归查找子部门
    if (dept.children && dept.children.length > 0) {
      const foundPath = findDepartmentPath(dept.children, targetId, newPath)
      if (foundPath.length > 0) {
        return foundPath
      }
    }
  }
  
  // 没找到返回空数组
  return []
}

onMounted(() => {
  loadAdminList()
  loadDepartmentTree()
})
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-tree) {
  max-height: 400px;
  overflow-y: auto;
}

.system-admin {
  padding: 20px;
}

.page-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style> 