# 矿山学习社区系统后台管理 - 项目开发说明

## 项目概述
**项目名称**：矿山学习社区系统后台管理  
**主要用途**：为成远矿业学习论坛APP提供后台管理界面，便于管理员或教务人员对论坛内容、用户等进行管理。

---

## 系统架构模块

### 1. 看板模块（Dashboard）

#### 1.1 数据看板页面
**页面路径**：`/dashboard`  
**页面功能**：展示系统核心数据统计  
**包含功能**：
- 学习统计卡片：今日学习人数、本月学习时长、课程完成率
- 考试统计卡片：今日考试人数、平均分数、合格率
- 论坛活跃统计卡片：今日发帖数、评论数、活跃用户数
- 数据趋势图表：学习趋势、考试趋势、论坛活跃趋势
- 快捷操作入口：发布公告、发布考试、审核帖子

**按钮组件**：
- 刷新数据按钮
- 时间范围选择器（今天、本周、本月、自定义）
- 导出报表按钮

---

### 2. 系统管理模块

#### 2.1 管理员管理页面
**页面路径**：`/system/admin`  
**页面功能**：管理系统管理员账户  
**包含功能**：
- 管理员列表表格：姓名、用户名、部门、创建时间、状态
- 搜索筛选：按姓名、用户名、部门搜索
- 分页功能

**按钮组件**：
- 新增管理员按钮
- 编辑按钮（每行）
- 删除按钮（每行）
- 重置密码按钮（每行）
- 批量删除按钮
- 导出列表按钮

**弹窗表单**：
- 新增/编辑管理员弹窗：姓名、用户名、密码、确认密码、部门选择、角色权限

#### 2.2 部门管理页面
**页面路径**：`/system/department`  
**页面功能**：管理组织架构部门信息  
**包含功能**：
- 左侧树形结构：展示部门层级关系
- 右侧部门详情：部门名称、负责人、上级部门、创建时间
- 拖拽排序功能

**按钮组件**：
- 新增部门按钮
- 编辑部门按钮
- 删除部门按钮
- 展开/收起全部按钮
- 刷新树形结构按钮

**弹窗表单**：
- 新增/编辑部门弹窗：部门名称、上级部门、负责人选择、部门描述

---

### 3. APP首页管理模块

#### 3.1 首页内容管理页面
**页面路径**：`/app-home/content`  
**页面功能**：管理APP首页展示内容  
**包含功能**：
- Tab切换：轮播图管理、新闻管理、公告管理
- 轮播图列表：图片预览、标题、链接、排序、状态
- 新闻列表：标题、发布时间、阅读量、状态
- 公告列表：标题、发布时间、置顶状态、重要程度

**按钮组件**：
- 新增内容按钮（根据当前Tab）
- 编辑按钮（每行）
- 删除按钮（每行）
- 启用/禁用按钮（每行）
- 置顶按钮（新闻/公告）
- 预览按钮（每行）
- 批量操作按钮

**弹窗表单**：
- 轮播图表单：图片上传、标题、跳转链接、排序、启用状态
- 新闻表单：标题、封面图、富文本内容、发布时间、是否置顶
- 公告表单：标题、富文本内容、重要程度、发布时间、是否置顶

---

### 4. 学员管理模块

#### 4.1 学员信息管理页面
**页面路径**：`/student/info`  
**页面功能**：管理学员基本信息  
**页面布局**：左右分栏布局  
**包含功能**：
- 左侧：部门树形结构，点击部门筛选学员
- 右侧：学员列表表格（姓名、手机号、所属部门、注册时间、状态）
- 搜索筛选：按姓名、手机号搜索
- 学员详情查看

**按钮组件**：
- 新增学员按钮
- 编辑按钮（每行）
- 删除按钮（每行）
- 重置密码按钮（每行）
- 查看积分按钮（每行）
- 查看帖子按钮（每行）
- 查看学习记录按钮（每行）
- 查看考试记录按钮（每行）
- 批量导入按钮
- 导出学员按钮
- 批量删除按钮

**弹窗表单**：
- 新增/编辑学员弹窗：姓名、手机号、密码、所属部门、入职时间
- 批量导入弹窗：Excel模板下载、文件上传、导入结果展示

#### 4.2 学员详情页面
**页面路径**：`/student/detail/:id`  
**页面功能**：查看学员详细信息  
**包含功能**：
- 基本信息Tab：姓名、手机号、部门、注册时间等
- 积分记录Tab：积分变动记录列表
- 帖子记录Tab：发布的帖子列表
- 学习记录Tab：学习进度、学习时长统计
- 考试记录Tab：考试历史、成绩统计

**按钮组件**：
- 返回列表按钮
- 编辑学员按钮
- 重置密码按钮
- 导出记录按钮

---

### 5. 论坛内容管理模块

#### 5.1 帖子/评论管理页面
**页面路径**：`/forum/posts`  
**页面功能**：管理论坛帖子和评论内容  
**包含功能**：
- Tab切换：帖子管理、评论管理
- 帖子列表：标题、作者、板块、发布时间、状态、精华、置顶
- 评论列表：评论内容、帖子标题、评论者、评论时间、状态
- 搜索筛选：按标题、作者、板块、时间范围筛选
- 内容预览功能

**按钮组件**：
- 查看详情按钮（每行）
- 审核通过按钮（每行）
- 审核拒绝按钮（每行）
- 删除按钮（每行）
- 加精按钮（每行）
- 置顶按钮（每行）
- 批量审核按钮
- 批量删除按钮
- 导出列表按钮

**弹窗表单**：
- 审核拒绝弹窗：拒绝原因选择/填写
- 帖子详情弹窗：完整内容展示、操作历史

#### 5.2 板块分类管理页面
**页面路径**：`/forum/categories`  
**页面功能**：管理论坛板块分类  
**包含功能**：
- 分类列表：分类名称、描述、帖子数量、创建时间、状态
- 排序功能：拖拽调整分类显示顺序
- 分类统计：各分类帖子数量统计

**按钮组件**：
- 新增分类按钮
- 编辑按钮（每行）
- 删除按钮（每行）
- 启用/禁用按钮（每行）
- 上移/下移按钮（每行）

**弹窗表单**：
- 新增/编辑分类弹窗：分类名称、分类描述、分类图标、排序、启用状态

#### 5.3 违规内容处理页面
**页面路径**：`/forum/violation`  
**页面功能**：管理违禁词和违规内容  
**包含功能**：
- Tab切换：违禁词管理、违规内容列表
- 违禁词列表：违禁词、添加时间、使用次数
- 违规内容列表：内容摘要、违禁词、作者、发布时间、处理状态
- 批量处理功能

**按钮组件**：
- 新增违禁词按钮
- 编辑按钮（每行）
- 删除按钮（每行）
- 查看详情按钮（每行）
- 标记处理按钮（每行）
- 忽略违规按钮（每行）
- 批量处理按钮
- 导入违禁词按钮

**弹窗表单**：
- 新增/编辑违禁词弹窗：违禁词内容、替换词、严重程度
- 违规内容详情弹窗：完整内容、违禁词标记、处理操作

---

### 6. 课程管理模块

#### 6.1 学习资源管理页面
**页面路径**：`/course/resources`  
**页面功能**：管理各类学习资源  
**包含功能**：
- Tab切换：文件资源、视频资源、文章资源、课程管理
- 文件列表：文件名、大小、上传时间、下载次数
- 视频列表：视频名、时长、大小、观看次数
- 文章列表：文章标题、作者、发布时间、阅读量
- 课程列表：课程名称、章节数、学习人数、创建时间

**按钮组件**：
- 批量上传按钮（文件/视频）
- 新增文章按钮
- 新增课程按钮
- 编辑按钮（每行）
- 删除按钮（每行）
- 预览按钮（每行）
- 下载按钮（文件/视频）
- 批量删除按钮

**弹窗表单**：
- 批量上传弹窗：文件选择、进度显示、上传结果
- 新增/编辑文章弹窗：标题、作者、富文本内容、标签
- 新增/编辑课程弹窗：课程名称、课程介绍、可见部门、是否允许快进

#### 6.2 课程详情页面
**页面路径**：`/course/detail/:id`  
**页面功能**：管理课程章节内容  
**包含功能**：
- 课程基本信息：名称、介绍、学习人数、创建时间
- 章节树形结构：大章节、小章节层级展示
- 章节内容管理：从资源库选择内容
- 拖拽排序功能：调整章节顺序

**按钮组件**：
- 返回列表按钮
- 新增大章节按钮
- 新增小章节按钮
- 编辑按钮（每个章节）
- 删除按钮（每个章节）
- 预览课程按钮
- 发布课程按钮

**弹窗表单**：
- 新增大章节弹窗：章节名称、章节描述
- 新增小章节弹窗：章节名称、选择资源（文件/视频/文章）、学习要求

#### 6.3 学习记录页面
**页面路径**：`/course/records`  
**页面功能**：查看和管理学员学习记录  
**包含功能**：
- 学习统计图表：学习时长趋势、课程完成率、活跃学员排行
- 学习记录列表：学员姓名、课程名称、学习进度、学习时长、最后学习时间
- 搜索筛选：按学员、课程、部门、时间范围筛选
- 进度统计：各课程完成情况统计

**按钮组件**：
- 查看详情按钮（每行）
- 重置进度按钮（每行）
- 导出记录按钮
- 刷新统计按钮

**弹窗表单**：
- 学习详情弹窗：详细学习轨迹、学习时长分布、章节完成情况

---

### 7. 试题考试模块

#### 7.1 题库管理页面
**页面路径**：`/exam/question-bank`  
**页面功能**：管理试题题库  
**页面布局**：左右分栏布局  
**包含功能**：
- 左侧：题库分类列表，可新增、编辑、删除题库
- 右侧：题目列表表格（题目类型、题目内容、难度、创建时间）
- 题目类型：单选、多选、判断、填空、简答
- 搜索筛选：按题目类型、难度、关键词筛选

**按钮组件**：
- 新增题库按钮（左侧）
- 编辑题库按钮（左侧每行）
- 删除题库按钮（左侧每行）
- 新增题目按钮（右侧）
- 编辑题目按钮（右侧每行）
- 删除题目按钮（右侧每行）
- 批量导入题目按钮
- 导出题目按钮
- 预览题目按钮（每行）

**弹窗表单**：
- 新增/编辑题库弹窗：题库名称、题库描述、适用范围
- 新增/编辑题目弹窗：题目类型、题目内容、选项设置、正确答案、解析、难度
- 批量导入弹窗：模板下载、文件上传、导入结果

#### 7.2 试卷管理页面
**页面路径**：`/exam/paper`  
**页面功能**：管理考试试卷  
**包含功能**：
- 试卷列表：试卷名称、题目数量、总分、创建时间、使用次数
- 试卷预览：完整试卷内容展示
- 试卷统计：各题型分布、难度分布

**按钮组件**：
- 新增试卷按钮
- 编辑试卷按钮（每行）
- 删除试卷按钮（每行）
- 复制试卷按钮（每行）
- 预览试卷按钮（每行）
- 试卷统计按钮（每行）

**弹窗表单**：
- 新增/编辑试卷弹窗：试卷名称、试卷描述、考试时长、及格分数

#### 7.3 试卷编辑页面
**页面路径**：`/exam/paper/edit/:id`  
**页面功能**：编辑试卷内容  
**页面布局**：左右分栏布局  
**包含功能**：
- 左侧：题库题目列表，可按题型、难度筛选
- 右侧：试卷题目列表，支持拖拽排序
- 题目分数设置：为每道题目设置分数
- 试卷预览功能

**按钮组件**：
- 返回列表按钮
- 保存试卷按钮
- 预览试卷按钮
- 添加题目按钮（左侧每行）
- 移除题目按钮（右侧每行）
- 上移/下移按钮（右侧每行）
- 清空试卷按钮

#### 7.4 考试管理页面
**页面路径**：`/exam/management`  
**页面功能**：管理考试安排和记录  
**包含功能**：
- Tab切换：考试安排、考试记录
- 考试列表：考试名称、试卷、参考部门、开始时间、结束时间、状态
- 考试记录列表：考生姓名、考试名称、得分、答题时长、提交时间、阅卷状态

**按钮组件**：
- 新增考试按钮
- 编辑考试按钮（每行）
- 删除考试按钮（每行）
- 开始考试按钮（每行）
- 结束考试按钮（每行）
- 查看记录按钮（每行）
- 阅卷按钮（每行）
- 导出成绩按钮
- 成绩统计按钮

**弹窗表单**：
- 新增/编辑考试弹窗：考试名称、选择试卷、参考部门、开始时间、结束时间、考试时长

#### 7.5 阅卷页面
**页面路径**：`/exam/marking/:recordId`  
**页面功能**：在线阅卷功能  
**包含功能**：
- 考生基本信息：姓名、考试名称、答题时长
- 题目列表：题目内容、考生答案、参考答案
- 评分功能：为主观题打分
- 评语功能：添加评语和建议

**按钮组件**：
- 返回列表按钮
- 保存评分按钮
- 提交阅卷按钮
- 上一题按钮
- 下一题按钮
- 自动评分按钮（客观题）

---

### 8. 积分管理模块

#### 8.1 积分规则页面
**页面路径**：`/points/rules`  
**页面功能**：管理积分获取规则  
**包含功能**：
- 规则列表表格：规则描述、规则标识、积分值、每日上限、每日次数上限、状态
- 规则分类：登录积分、学习积分、论坛积分、考试积分等
- 规则统计：各规则使用次数统计

**按钮组件**：
- 新增规则按钮
- 编辑规则按钮（每行）
- 删除规则按钮（每行）
- 启用/禁用按钮（每行）
- 规则统计按钮
- 导出规则按钮

**弹窗表单**：
- 新增/编辑规则弹窗：规则名称、规则描述、规则标识、积分值、每日上限、每日次数上限、启用状态

#### 8.2 积分记录页面
**页面路径**：`/points/records`  
**页面功能**：查看积分变动记录  
**包含功能**：
- 积分记录列表：学员姓名、规则描述、积分变动、余额、变动时间
- 搜索筛选：按学员、规则类型、时间范围筛选
- 积分统计：总积分发放、各规则积分分布

**按钮组件**：
- 手动加减积分按钮
- 导出记录按钮
- 积分统计按钮
- 清除记录按钮
- 刷新按钮

**弹窗表单**：
- 手动调整积分弹窗：选择学员、积分变动、变动原因

#### 8.3 商品管理页面
**页面路径**：`/points/goods`  
**页面功能**：管理积分兑换商品  
**包含功能**：
- 商品列表：商品名称、商品图片、所需积分、库存、兑换次数、状态
- 商品分类：实物商品、虚拟商品等
- 库存管理：库存预警、补货提醒

**按钮组件**：
- 新增商品按钮
- 编辑商品按钮（每行）
- 删除商品按钮（每行）
- 上架/下架按钮（每行）
- 补货按钮（每行）
- 商品统计按钮

**弹窗表单**：
- 新增/编辑商品弹窗：商品名称、商品图片、商品描述、所需积分、库存数量、商品分类、是否上架

#### 8.4 兑换记录页面
**页面路径**：`/points/exchange`  
**页面功能**：管理积分兑换记录  
**包含功能**：
- 兑换记录列表：学员姓名、商品名称、所需积分、兑换时间、发货状态、收货地址
- 状态管理：待发货、已发货、已收货、已取消
- 物流跟踪：发货信息、物流单号

**按钮组件**：
- 发货按钮（每行）
- 取消兑换按钮（每行）
- 查看详情按钮（每行）
- 批量发货按钮
- 导出记录按钮
- 物流查询按钮（每行）

**弹窗表单**：
- 发货弹窗：物流公司、物流单号、发货备注
- 兑换详情弹窗：完整兑换信息、收货地址、联系方式

---

## 技术架构建议

### 前端技术栈
- **框架**：Vue 3 + TypeScript
- **UI组件库**：Element Plus / Ant Design Vue
- **状态管理**：Pinia
- **路由管理**：Vue Router 4
- **HTTP请求**：Axios
- **富文本编辑器**：Quill.js / TinyMCE
- **图表组件**：ECharts
- **文件上传**：vue-simple-uploader

### 后端技术栈
- **框架**：Spring Boot
- **数据库**：MySQL
- **缓存**：Redis
- **文件存储**：阿里云OSS / 本地存储
- **权限管理**：Spring Security + JWT

### 数据库设计要点
1. 用户权限表（管理员、学员）
2. 部门组织架构表
3. 学习资源表（文件、视频、文章、课程）
4. 论坛相关表（帖子、评论、板块）
5. 考试相关表（题库、试卷、考试、记录）
6. 积分相关表（规则、记录、商品、兑换）

### 开发优先级建议
1. **第一阶段**：用户认证、部门管理、基础数据管理
2. **第二阶段**：学员管理、课程管理、学习记录
3. **第三阶段**：论坛管理、考试管理
4. **第四阶段**：积分系统、数据统计看板
5. **第五阶段**：系统优化、性能提升

### 部署建议
- **开发环境**：Docker容器化部署
- **生产环境**：Nginx + Spring Boot + MySQL + Redis
- **监控**：日志收集、性能监控、异常告警 