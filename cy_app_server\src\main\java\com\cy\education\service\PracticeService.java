package com.cy.education.service;

import com.cy.education.model.entity.PracticeRecord;
import com.cy.education.model.entity.PracticeStats;
import com.cy.education.model.entity.exam.ExamBank;
import com.cy.education.model.entity.exam.ExamQuestion;

import java.util.List;
import java.util.Map;

/**
 * 练习功能Service接口
 */
public interface PracticeService {
    
    /**
     * 获取允许练习的题库列表
     * @return 题库列表
     */
    List<ExamBank> getAvailableBanks();
    
    /**
     * 获取用户的练习统计信息
     * @param userId 用户ID
     * @return 练习统计信息
     */
    List<PracticeStats> getUserPracticeStats(Integer userId);
    
    /**
     * 获取用户在指定题库的练习统计信息
     * @param userId 用户ID
     * @param bankId 题库ID
     * @return 练习统计信息
     */
    PracticeStats getUserPracticeStatsByBank(Integer userId, Integer bankId);
    
    /**
     * 开始练习
     * @param userId 用户ID
     * @param bankId 题库ID (null表示全部题库)
     * @param type 练习类型 (normal/wrong)
     * @return 练习记录ID
     */
    Integer startPractice(Integer userId, Integer bankId, String type);
    
    /**
     * 获取下一道题
     * @param recordId 练习记录ID
     * @return 题目信息
     */
    ExamQuestion getNextQuestion(Integer recordId);
    
    /**
     * 获取下一道题及进度信息
     * @param recordId 练习记录ID
     * @return 题目信息和进度
     */
    Map<String, Object> getNextQuestionWithProgress(Integer recordId);
    
    /**
     * 提交答案
     * @param recordId 练习记录ID
     * @param questionId 题目ID
     * @param userAnswer 用户答案
     * @return 答题结果
     */
    Map<String, Object> submitAnswer(Integer recordId, Integer questionId, String userAnswer);
    
    /**
     * 完成练习
     * @param recordId 练习记录ID
     * @return 练习结果
     */
    Map<String, Object> completePractice(Integer recordId);
    
    /**
     * 获取错题本
     * @param userId 用户ID
     * @param bankId 题库ID (null表示全部题库)
     * @return 错题列表
     */
    List<ExamQuestion> getWrongQuestions(Integer userId, Integer bankId);
    
    /**
     * 获取用户的练习记录
     * @param userId 用户ID
     * @return 练习记录列表
     */
    List<PracticeRecord> getUserPracticeRecords(Integer userId);
    
    /**
     * 获取练习记录详情
     * @param recordId 练习记录ID
     * @return 练习记录详情
     */
    Map<String, Object> getPracticeRecordDetail(Integer recordId);

    /**
     * 清空错题本
     * @param userId 用户ID
     * @param bankId 题库ID (null表示全部题库)
     * @return 操作结果
     */
    Map<String, Object> clearWrongQuestions(Integer userId, Integer bankId);
}