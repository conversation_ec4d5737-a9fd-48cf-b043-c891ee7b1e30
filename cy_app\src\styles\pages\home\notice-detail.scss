// 通知详情页样式

.notice-title {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 12px;
}

.meta-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 12px;
  color: var(--text-tertiary);
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-light);
}

.notice-body {
  font-size: 16px;
  line-height: 1.8;
  color: var(--text-secondary);

  :deep(p) {
    margin-bottom: 1em;
  }

  :deep(h3) {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 1.5em 0 1em;
  }

  :deep(ul) {
    padding-left: 1.5em;
    margin-bottom: 1em;
  }

  :deep(li) {
    margin-bottom: 0.5em;
  }
}

.attachment-section {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--border-light);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: 12px;
  transition: var(--transition-fast);

  &:active {
    background-color: var(--bg-tertiary);
  }
}

.file-icon {
  font-size: 24px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: var(--text-tertiary);
}

.download-btn {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: var(--primary-color-soft);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;

  .icon {
    font-size: 18px;
    font-weight: bold;
  }
}

.meta-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.importance-tag {
  display: inline-block;
  padding: 2px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  margin-right: 14px;
  color: #fff;
  max-width: 60px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  box-sizing: border-box;
  text-align: center;
  &.level-1 {
    background: #409eff; // 蓝色 一般
  }
  &.level-2 {
    background: #ff9900; // 橙色 重要
  }
  &.level-3 {
    background: #f56c6c; // 红色 紧急
  }
}