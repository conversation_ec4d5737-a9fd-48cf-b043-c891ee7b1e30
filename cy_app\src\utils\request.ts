// 兼容 uni-app 的请求工具，移除 element-plus/路由依赖，适配小程序/app

const BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

function getToken() {
  // 兼容 uni-app 和 H5
  return uni.getStorageSync('token') || localStorage.getItem('token') || ''
}

function showError(msg: string) {
  uni.showToast({
    title: msg,
    icon: 'none',
    duration: 3000
  })
}

function request<T>(options: UniApp.RequestOptions): Promise<T> {
  return new Promise((resolve, reject) => {
    uni.request({
      ...options,
      url: BASE_URL + options.url,
      header: {
        ...(options.header || {}),
        Authorization: getToken() ? `Bearer ${getToken()}` : ''
      },
      success(res) {
        const data = res.data as any

        // 兼容标准格式 {code: 200, data: ...}
        if (data && (data.code === 200 || data.code === 0)) {
          resolve(data.data)
          return
        }

        // 兼容PK API格式 {success: true, list: [], ...}
        if (data && data.success === true) {
          resolve(data as T)
          return
        }

        // 兼容直接返回数组的格式
        if (Array.isArray(data)) {
          resolve(data as T)
          return
        }

        // 兼容其他对象格式（没有明确的错误标识）
        if (typeof data === 'object' && data !== null && !data.hasOwnProperty('success') && !data.hasOwnProperty('code')) {
          resolve(data as T)
          return
        }

        // 明确的错误响应
        if (data && (data.success === false || data.code !== 200)) {
          showError(data.message || '请求失败')
          reject(data)
          return
        }

        // 其他情况作为错误处理
        showError(data?.message || '请求失败')
        reject(data)
      },
      fail(err) {
        showError('网络请求失败')
        reject(err)
      }
    })
  })
}

export function get<T>(url: string, params?: any): Promise<T> {
  return request<T>({
    url,
    method: 'GET',
    data: params
  })
}

export function post<T>(url: string, data?: any): Promise<T> {
  return request<T>({
    url,
    method: 'POST',
    data
  })
}

export function put<T>(url: string, data?: any): Promise<T> {
  return request<T>({
    url,
    method: 'PUT',
    data
  })
}

export function del<T>(url: string, params?: any): Promise<T> {
  return request<T>({
    url,
    method: 'DELETE',
    data: params
  })
}

/**
 * 文件上传（统一风格，自动带token，返回data，错误自动toast）
 * @param url 上传接口地址
 * @param filePath 文件路径
 * @param name 表单字段名
 * @param formData 额外表单数据
 */
export function upload<T>(url: string, filePath: string, name = 'file', formData?: Record<string, any>): Promise<T> {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: BASE_URL + url,
      filePath,
      name,
      formData,
      header: {
        Authorization: getToken() ? `Bearer ${getToken()}` : ''
      },
      success: (res) => {
        try {
          const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
          if (data && (data.code === 200 || data.code === 0)) {
            resolve(data.data)
          } else {
            showError(data.message || '上传失败')
            reject(data)
          }
        } catch (e) {
          showError('上传失败')
          reject(e)
        }
      },
      fail: (err) => {
        showError('上传失败')
        reject(err)
      }
    })
  })
}

export default {
  get,
  post,
  put,
  del,
  upload
}
