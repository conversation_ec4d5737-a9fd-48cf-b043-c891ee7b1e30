-- 论坛用户交互相关表

-- 帖子点赞表
CREATE TABLE IF NOT EXISTS `forum_post_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '帖子ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_user` (`post_id`,`user_id`) COMMENT '用户帖子点赞唯一索引',
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子点赞表';

-- 帖子收藏表
CREATE TABLE IF NOT EXISTS `forum_post_collects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '帖子ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_user` (`post_id`,`user_id`) COMMENT '用户帖子收藏唯一索引',
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子收藏表';

-- 用户关注表
CREATE TABLE IF NOT EXISTS `forum_user_follows` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `follower_id` int(11) NOT NULL COMMENT '关注者ID',
  `following_id` int(11) NOT NULL COMMENT '被关注者ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_follower_following` (`follower_id`,`following_id`) COMMENT '关注关系唯一索引',
  KEY `idx_follower_id` (`follower_id`),
  KEY `idx_following_id` (`following_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';

-- 评论点赞表
CREATE TABLE IF NOT EXISTS `forum_comment_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `comment_id` int(11) NOT NULL COMMENT '评论ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`,`user_id`) COMMENT '用户评论点赞唯一索引',
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞表';

-- 为现有表添加缺失的字段

-- 为forum_posts表添加收藏数和分享数字段
ALTER TABLE `forum_posts`
ADD COLUMN IF NOT EXISTS `collect_count` int(11) NOT NULL DEFAULT 0 COMMENT '收藏数' AFTER `like_count`,
    ADD COLUMN IF NOT EXISTS `share_count` int (11) NOT NULL DEFAULT 0 COMMENT '分享数' AFTER `collect_count`,
    ADD COLUMN IF NOT EXISTS `images` text NULL COMMENT '图片列表（JSON格式）' AFTER `is_essence`;

-- 为forum_comments表添加回复目标用户字段
ALTER TABLE `forum_comments`
ADD COLUMN IF NOT EXISTS `reply_to_id` int(11) NULL COMMENT '回复目标用户ID' AFTER `parent_id`;

-- 插入一些测试数据（可选）
-- INSERT INTO forum_post_likes (post_id, user_id) VALUES (1, 1), (1, 2), (2, 1);
-- INSERT INTO forum_post_collects (post_id, user_id) VALUES (1, 1), (2, 1);
-- INSERT INTO forum_user_follows (follower_id, following_id) VALUES (1, 2), (2, 1);
-- INSERT INTO forum_comment_likes (comment_id, user_id) VALUES (1, 1), (1, 2);
