package com.cy.education.model.params;

import lombok.Data;

/**
 * 积分记录查询参数
 */
@Data
public class PointsQueryParam {

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 积分类型
     */
    private String type;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 排序方向：asc/desc
     */
    private String sortOrder;
}
