<template>
  <div class="record-detail">
    <div class="page-header">
      <h2>考试记录详情</h2>
      <el-button @click="handleBack">返回列表</el-button>
    </div>

    <div v-loading="loading" class="detail-content">
      <el-card v-if="!loading && recordInfo.id">
        <template #header>
          <div class="record-header">
            <div class="student-info">
              <h3>{{ recordInfo.examName }}</h3>
              <div class="info-details">
                <span>考生：{{ recordInfo.studentName }}</span>
                <span>部门：{{ recordInfo.departmentName }}</span>
                <span>提交时间：{{ recordInfo.submitTime }}</span>
                <span>答题时长：{{ formatDuration(recordInfo.duration) }}</span>
              </div>
            </div>
          </div>
        </template>
        
        <div class="answer-list">
          <div v-for="(section, sectionIndex) in recordInfo.sections" :key="sectionIndex" class="section-item">
            <h4 class="section-title">{{ section.name }}</h4>
            
            <div v-for="(question, questionIndex) in section.questions" :key="questionIndex" class="question-item">
              <div class="question-header">
                <span class="question-index">{{ questionIndex + 1 }}.</span>
                <span class="question-type">[{{ getQuestionTypeName(question.type) }}]</span>
                <span class="question-score">{{ question.score }}分</span>
              </div>
              
              <div class="question-content" v-html="question.content"></div>
              
              <!-- 选择题选项 -->
              <div v-if="['single', 'multiple'].includes(question.type)" class="question-options">
                <div 
                  v-for="(option, optionIndex) in question.options" 
                  :key="optionIndex" 
                  class="option-item"
                  :class="{
                    'selected': isOptionSelected(question, optionIndex),
                    'correct': isOptionCorrect(question, optionIndex),
                    'wrong': isOptionWrong(question, optionIndex)
                  }"
                >
                  <span class="option-label">{{ String.fromCharCode(65 + optionIndex) }}.</span>
                  <span class="option-content" v-html="option.content"></span>
                </div>
              </div>
              
              <!-- 判断题选项 -->
              <div v-if="question.type === 'judge'" class="question-options">
                <div 
                  class="option-item"
                  :class="{
                    'selected': question.studentAnswer === 'true',
                    'correct': question.studentAnswer === 'true' && question.answer === 'true',
                    'wrong': question.studentAnswer === 'true' && question.answer === 'false'
                  }"
                >
                  <span class="option-label">A.</span>
                  <span class="option-content">正确</span>
                </div>
                <div 
                  class="option-item"
                  :class="{
                    'selected': question.studentAnswer === 'false',
                    'correct': question.studentAnswer === 'false' && question.answer === 'false',
                    'wrong': question.studentAnswer === 'false' && question.answer === 'true'
                  }"
                >
                  <span class="option-label">B.</span>
                  <span class="option-content">错误</span>
                </div>
              </div>
              
              <!-- 填空题/简答题答案 -->
              <div v-if="['blank', 'essay'].includes(question.type)" class="student-answer">
                <div class="answer-label">考生答案：</div>
                <div class="answer-content" v-html="question.studentAnswer || '未作答'"></div>
              </div>
              
              <!-- 答案和评分 -->
              <div class="question-result">
                <div class="result-item">
                  <span class="result-label">参考答案：</span>
                  <span class="result-content" v-html="formatReferenceAnswer(question)"></span>
                </div>
                <div class="result-item">
                  <span class="result-label">得分：</span>
                  <span class="result-content" :class="{'result-correct': question.isCorrect, 'result-wrong': !question.isCorrect}">
                    {{ question.markingScore || 0 }}/{{ question.score }}
                  </span>
                </div>
                <div v-if="question.markingComment" class="result-item">
                  <span class="result-label">评语：</span>
                  <span class="result-content">{{ question.markingComment }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
      
      <el-empty v-else-if="!loading" description="未找到考试记录" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

// 添加API导入
import { getExamRecordDetail } from '@/api/exam'

const route = useRoute()
const router = useRouter()
const recordId = route.params.id

// 加载状态
const loading = ref(false)

// 考试记录信息
const recordInfo = reactive({
  id: '',
  studentId: '',
  studentName: '',
  departmentId: '',
  departmentName: '',
  examId: '',
  examName: '',
  score: 0,
  totalScore: 0,
  passingScore: 0,
  duration: 0,
  submitTime: '',
  markingStatus: '',
  sections: []
})

// 获取考试记录详情
const fetchRecordDetail = async () => {
  loading.value = true
  try {
    // 调用后端API获取考试记录详情
    const response = await getExamRecordDetail(recordId as string)
    console.log('获取考试记录详情成功:', response)
    
    if (response) {
      // 处理API返回的数据
      const record = response as any
      
      // 基本信息
      Object.assign(recordInfo, {
        id: record.id,
        studentId: record.userId,
        studentName: record.userName,
        departmentId: record.departmentId,
        departmentName: record.departmentName,
        examId: record.examId,
        examName: record.examTitle,
        score: record.score,
        totalScore: record.totalScore,
        passingScore: record.passingScore,
        duration: record.duration || 0,
        submitTime: record.endTime ? formatDateTime(record.endTime) : '',
        markingStatus: record.status
      })
      
      // 处理答题记录，组织成sections结构
      if (record.answers && record.answers.length > 0) {
        // 按题型分组
        const questionTypes: Record<string, { name: string, questions: any[] }> = {
          'single': { name: '一、单选题', questions: [] },
          'multiple': { name: '二、多选题', questions: [] },
          'judgment': { name: '三、判断题', questions: [] },
          'fill': { name: '四、填空题', questions: [] },
          'essay': { name: '五、简答题', questions: [] }
        }
        
        // 处理答题记录
        record.answers.forEach((answer: any) => {
          const question = {
            id: answer.questionId,
            type: answer.questionType || 'single', // 题目类型
            content: answer.questionContent || '题目内容',
            options: answer.options ? JSON.parse(answer.options) : [],
            answer: answer.correctAnswer || '',
            studentAnswer: answer.answer || '',
            score: answer.totalScore || 0,
            markingScore: answer.score || 0,
            isCorrect: answer.isCorrect,
            markingComment: answer.comment || ''
          }
          
          // 将题目添加到对应类型的分组中
          const questionType = mapQuestionType(question.type);
          if (questionTypes[questionType]) {
            questionTypes[questionType].questions.push(question)
          }
        })
        
        // 将分组转换为数组
        recordInfo.sections = Object.values(questionTypes).filter(section => section.questions.length > 0) as any
      }
    }
  } catch (error) {
    console.error('获取考试记录详情失败:', error)
    ElMessage.error('获取考试记录详情失败，请重试')
  } finally {
    loading.value = false
  }
}

// 将后端题型映射到前端题型
const mapQuestionType = (backendType: string): string => {
  const typeMap: Record<string, string> = {
    'single': 'single',
    'multiple': 'multiple',
    'judge': 'judgment',
    'judgment': 'judgment',
    'fill': 'fill',
    'blank': 'fill',
    'essay': 'essay'
  }
  return typeMap[backendType] || 'single'
}

// 获取题型名称
const getQuestionTypeName = (type) => {
  const typeMap = {
    'single': '单选题',
    'multiple': '多选题',
    'judge': '判断题',
    'blank': '填空题',
    'essay': '简答题'
  }
  return typeMap[type] || '未知题型'
}

// 格式化答题时长
const formatDuration = (minutes) => {
  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}小时${mins}分钟`
  }
}

// 格式化参考答案
const formatReferenceAnswer = (question) => {
  switch (question.type) {
    case 'single':
      return `${question.answer}. ${question.options[question.answer.charCodeAt(0) - 65]?.content || ''}`
    case 'multiple':
      return question.answer.split('').map(letter => {
        const index = letter.charCodeAt(0) - 65
        return `${letter}. ${question.options[index]?.content || ''}`
      }).join('<br>')
    case 'judge':
      return question.answer === 'true' ? '正确' : '错误'
    default:
      return question.answer
  }
}

// 判断选项是否被选中
const isOptionSelected = (question, optionIndex) => {
  const letter = String.fromCharCode(65 + optionIndex)
  if (question.type === 'single') {
    return question.studentAnswer === letter
  } else if (question.type === 'multiple') {
    return question.studentAnswer && question.studentAnswer.includes(letter)
  }
  return false
}

// 判断选项是否正确
const isOptionCorrect = (question, optionIndex) => {
  const letter = String.fromCharCode(65 + optionIndex)
  const isSelected = isOptionSelected(question, optionIndex)
  
  if (question.type === 'single') {
    return isSelected && question.answer === letter
  } else if (question.type === 'multiple') {
    const letterInAnswer = question.answer.includes(letter)
    return isSelected && letterInAnswer
  }
  
  return false
}

// 判断选项是否错误
const isOptionWrong = (question, optionIndex) => {
  const letter = String.fromCharCode(65 + optionIndex)
  const isSelected = isOptionSelected(question, optionIndex)
  
  if (question.type === 'single') {
    return isSelected && question.answer !== letter
  } else if (question.type === 'multiple') {
    const letterInAnswer = question.answer.includes(letter)
    return (isSelected && !letterInAnswer) || (!isSelected && letterInAnswer)
  }
  
  return false
}

// 返回列表
const handleBack = () => {
  router.push('/exam/management?tab=records')
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (e) {
    console.error('日期格式化错误:', e)
    return dateTime
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchRecordDetail()
})
</script>

<style scoped>
.record-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.detail-content {
  margin-bottom: 30px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.student-info h3 {
  margin: 0 0 10px 0;
  font-weight: 500;
}

.info-details {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  font-size: 14px;
  color: #606266;
}

.section-title {
  padding: 10px;
  background-color: #f5f7fa;
  margin-top: 20px;
  margin-bottom: 15px;
  border-radius: 4px;
  font-weight: 500;
}

.question-item {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #ebeef5;
}

.question-header {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.question-index {
  font-weight: bold;
  margin-right: 5px;
}

.question-type {
  color: #409eff;
  margin-right: 10px;
  font-size: 14px;
}

.question-score {
  color: #ff9900;
  font-size: 14px;
}

.question-content {
  line-height: 1.6;
  margin-bottom: 15px;
}

.question-options {
  margin-left: 20px;
  margin-bottom: 15px;
}

.option-item {
  padding: 8px;
  margin-bottom: 8px;
  display: flex;
  border-radius: 4px;
}

.option-item.selected {
  background-color: #f0f9ff;
}

.option-item.correct {
  background-color: #f0f9eb;
}

.option-item.wrong {
  background-color: #fef0f0;
}

.option-label {
  margin-right: 8px;
  font-weight: 500;
}

.student-answer {
  margin-left: 20px;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.answer-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.answer-content {
  white-space: pre-line;
  line-height: 1.6;
}

.question-result {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.result-item {
  margin-bottom: 10px;
}

.result-label {
  font-weight: bold;
  color: #606266;
  margin-right: 5px;
}

.result-content {
  line-height: 1.6;
}

.result-correct {
  color: #67c23a;
  font-weight: bold;
}

.result-wrong {
  color: #f56c6c;
  font-weight: bold;
}
</style> 