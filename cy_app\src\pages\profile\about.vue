<template>
  <view class="privacy-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">关于我们</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <view class="privacy-section">
        <view class="section-title">成远教育</view>
        <view class="section-subtitle">V1.0.0</view>
        <view class="content-section">
          <view class="section-item">
            <view class="item-content">
              成远教育致力于为企业提供优质的在线培训服务
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.privacy-container {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.content-container {
  padding: 20px;
}

.privacy-section {
  background: #fff;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
}

.section-subtitle {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-bottom: 24px;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-item {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 20px;

  &:last-child {
    border-bottom: none;
  }
}

.item-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.item-content {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
}
</style>

