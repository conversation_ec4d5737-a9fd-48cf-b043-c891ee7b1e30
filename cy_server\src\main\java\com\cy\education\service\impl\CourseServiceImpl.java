package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.Course;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.CourseRepository;
import com.cy.education.service.CourseService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class CourseServiceImpl extends ServiceImpl<CourseRepository, Course> implements CourseService {

    @Override
    public PageResponse<Course> getCourseList(Integer page, Integer size, String name) {
        QueryWrapper<Course> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }
        queryWrapper.orderByDesc("create_time");

        // Select all fields except the large 'structure' field for the list view
        queryWrapper.select(Course.class, i -> !i.getColumn().equals("structure"));

        Page<Course> coursePage = baseMapper.selectPage(new Page<>(page, size), queryWrapper);
        return new PageResponse<>(coursePage.getRecords(), coursePage.getTotal());
    }

    @Override
    public Course getCourseWithSanitizedStructure(Long id) {
        // For now, this just gets the course.
        // In the future, this method will be responsible for preparing the course data for preview,
        // which means fetching resource details but excluding sensitive content like URLs.
        // The frontend would then request each resource's content individually.
        return baseMapper.selectById(id);
    }
} 