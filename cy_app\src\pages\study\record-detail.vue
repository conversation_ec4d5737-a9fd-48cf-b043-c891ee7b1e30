<template>
  <view class="page-container">
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">学习详情</text>
        <view class="placeholder"/>
      </view>
    </view>

    <view v-if="loading" class="loading-box">
      <up-loading-icon color="#667eea" size="20"/>
      <text>加载中...</text>
    </view>
    <view v-else-if="!courseDetail" class="empty-box">
      <up-empty text="暂无数据"/>
    </view>
    <view v-else class="course-detail">
      <!-- 课程基本信息 -->
      <view class="course-info">
        <text class="course-title">{{ courseDetail.courseName }}</text>
        <view class="progress-section">
          <text class="progress-number">进度：{{ courseDetail.progress }}%</text>
          <text class="duration">学习时长：{{ formatDuration(courseDetail.duration) }}</text>
          <text class="completed-status" :class="courseDetail.completed === 1 ? 'completed' : 'incomplete'">
            {{ courseDetail.completed === 1 ? '已完成' : '未完成' }}
          </text>
        </view>
      </view>
      <!-- 课程结构 -->
      <view class="structure-list">
        <view v-for="chapter in courseDetail.structure" :key="chapter.id" class="chapter-block">
          <view class="chapter-title">{{ chapter.label }}</view>
          <view v-for="lesson in chapter.children" :key="lesson.id" class="lesson-item">
            <view class="lesson-label">{{ lesson.label }}</view>
            <view class="lesson-progress">
              <text class="progress-number">{{ lesson.progress }}%</text>
              <text class="duration">{{ formatDuration(lesson.duration) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {getCourseDetail, type CourseDetailRecordVO} from '@/api/course'
import {onLoad} from '@dcloudio/uni-app'

const courseDetail = ref<CourseDetailRecordVO | null>(null)
const loading = ref(true)

const goBack = () => {
  uni.navigateBack()
}

function formatDuration(seconds: number) {
  if (!seconds) return ''
  const mins = Math.floor(seconds / 60)
  const hours = Math.floor(mins / 60)
  const remainMins = mins % 60
  if (hours > 0) {
    return `${hours}小时${remainMins}分钟`
  }
  return `${mins}分钟`
}

function loadDetail(courseId: number) {
  loading.value = true
  getCourseDetail(courseId)
      .then(res => {
        courseDetail.value = res
      })
      .catch(() => {
        uni.showToast({title: '加载失败', icon: 'none'})
        courseDetail.value = null
      })
      .finally(() => {
        loading.value = false
      })
}

onLoad((options) => {
  const courseId = Number(options.courseId)
  if (courseId) {
    loadDetail(courseId)
  } else {
    loading.value = false
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/pages/study/record-detail.scss';
</style>
