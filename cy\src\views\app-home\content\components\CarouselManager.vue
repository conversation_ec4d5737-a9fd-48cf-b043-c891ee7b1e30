<template>
    <div class="carousel-manager">
      <div class="manager-header">
        <div class="header-left">
          <h2 class="section-title">轮播图列表</h2>
          <p class="section-desc">管理首页轮播图显示内容</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="handleAdd">新增轮播图</el-button>
        </div>
      </div>
  
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input v-model="searchForm.keyword" placeholder="请输入轮播图标题" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
  
      <div class="carousel-table-container">
        <CarouselTable 
          :loading="loading" 
          :carousel-list="carouselList" 
          :total="total"
          :current-page="searchForm.pageNum"
          :page-size="searchForm.pageSize"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @edit="handleEdit"
          @delete="handleDelete"
          @toggle-status="handleStatusChange"
          @preview="handlePreview"
        />
      </div>
  
      <!-- 轮播图表单对话框 -->
      <CarouselForm 
        v-model="dialogVisible"
        :is-edit="formType === 'edit'"
        :carousel-data="formData"
        @submit="handleFormSubmit"
      />
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import CarouselTable from './CarouselTable.vue'
  import CarouselForm from './CarouselForm.vue'
  import { getCarouselList, getCarouselById, addCarousel, updateCarousel, deleteCarousel, updateCarouselStatus, type CarouselItem } from '@/api/content'
  
  // 搜索表单
  const searchForm = reactive({
    status: undefined as number | undefined,
    keyword: '',
    pageNum: 1,
    pageSize: 10
  })
  
  // 数据列表
  const carouselList = ref<CarouselItem[]>([])
  const total = ref(0)
  const loading = ref(false)
  
  // 表单相关
  const dialogVisible = ref(false)
  const formType = ref<'add' | 'edit'>('add')
  const formData = ref<Partial<CarouselItem>>({})
  
  // 初始化数据
  onMounted(() => {
    fetchCarouselList()
  })
  
  // 获取轮播图列表
  const fetchCarouselList = async () => {
    loading.value = true
    try {
      const res = await getCarouselList(searchForm)
      console.log(res)
      carouselList.value = res.list
      total.value = res.total
    } catch (error) {
      console.error('获取轮播图列表失败', error)
      ElMessage.error('获取轮播图列表失败')
    } finally {
      loading.value = false
    }
  }
  
  // 搜索
  const handleSearch = () => {
    searchForm.pageNum = 1
    fetchCarouselList()
  }
  
  // 重置搜索
  const resetSearch = () => {
    searchForm.status = undefined
    searchForm.keyword = ''
    searchForm.pageNum = 1
    fetchCarouselList()
  }
  
  // 页码变化
  const handlePageChange = (page: number) => {
    searchForm.pageNum = page
    fetchCarouselList()
  }
  
  // 每页条数变化
  const handleSizeChange = (size: number) => {
    searchForm.pageSize = size
    searchForm.pageNum = 1
    fetchCarouselList()
  }
  
  // 新增轮播图
  const handleAdd = () => {
    formType.value = 'add'
    formData.value = {
      status: 1,
      sort: 0,
      title: '',
      imageUrl: '',
      link: ''
    }
    dialogVisible.value = true
  }
  
  // 编辑轮播图
  const handleEdit = async (row: CarouselItem | number) => {
    try {
      loading.value = true
      const id = typeof row === 'number' ? row : row.id
      const res = await getCarouselById(id)
      if (res) {
        formData.value = res
        formType.value = 'edit'
        dialogVisible.value = true
      } else {
        ElMessage.error('轮播图不存在或已被删除')
      }
    } catch (error) {
      console.error('获取轮播图详情失败', error)
      ElMessage.error('获取轮播图详情失败')
    } finally {
      loading.value = false
    }
  }
  
  // 删除轮播图
  const handleDelete = (row: CarouselItem | number) => {
    const id = typeof row === 'number' ? row : row.id
    
    ElMessageBox.confirm('确定要删除该轮播图吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        loading.value = true
        await deleteCarousel(id)
        ElMessage.success('删除成功')
        if (carouselList.value.length === 1 && searchForm.pageNum > 1) {
          searchForm.pageNum--
        }
        fetchCarouselList()
      } catch (error) {
        console.error('删除轮播图失败', error)
        ElMessage.error('删除轮播图失败')
      } finally {
        loading.value = false
      }
    }).catch(() => {})
  }
  
  // 切换状态
  const handleStatusChange = async (row: CarouselItem | number, status?: number) => {
    try {
      loading.value = true
      // 如果row是对象，直接使用对象中的id和反向状态
      // 如果row是id，则使用传入的status
      const id = typeof row === 'number' ? row : row.id
      const newStatus = typeof row === 'number' ? status! : (row.status === 1 ? 0 : 1)
      
      await updateCarouselStatus(id, newStatus)
      ElMessage.success('状态更新成功')
      fetchCarouselList()
    } catch (error) {
      console.error('更新轮播图状态失败', error)
      ElMessage.error('更新轮播图状态失败')
    } finally {
      loading.value = false
    }
  }
  
  // 表单提交
  const handleFormSubmit = async (data: Partial<CarouselItem>) => {
    try {
      loading.value = true
      if (formType.value === 'add') {
        await addCarousel(data)
        ElMessage.success('添加成功')
      } else {
        await updateCarousel(data)
        ElMessage.success('更新成功')
      }
      dialogVisible.value = false
      fetchCarouselList()
    } catch (error) {
      console.error('保存轮播图失败', error)
      ElMessage.error('保存轮播图失败')
    } finally {
      loading.value = false
    }
  }
  
  // 预览轮播图
  const handlePreview = (row: CarouselItem) => {
    // 在新窗口中打开图片
    window.open(row.imageUrl, '_blank');
  }
  </script>
  
  <style scoped>
  .el-select{
  min-width: 100px;
}
  .carousel-manager {
    height: 100%;
  }
  
  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #1f2937;
  }
  
  .section-desc {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
  }
  
  .search-form {
    margin-bottom: 24px;
    background-color: #fff;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .carousel-table-container {
    background-color: #fff;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  </style>