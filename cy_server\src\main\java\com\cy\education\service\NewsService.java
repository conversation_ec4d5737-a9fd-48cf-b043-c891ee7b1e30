package com.cy.education.service;

import com.cy.education.model.entity.News;
import com.cy.education.model.vo.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;

/**
 * 新闻服务接口
 */
public interface NewsService {
    
    /**
     * 分页查询新闻列表
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    PageResponse<News> listNews(ContentQueryParam param);
    
    /**
     * 获取新闻详情
     * 
     * @param id 新闻ID
     * @return 新闻信息
     */
    News getNewsById(Integer id);
    
    /**
     * 新增新闻
     * 
     * @param news 新闻信息
     * @return 新闻ID
     */
    Integer addNews(News news);
    
    /**
     * 更新新闻
     * 
     * @param news 新闻信息
     * @return 是否成功
     */
    boolean updateNews(News news);
    
    /**
     * 删除新闻
     * 
     * @param id 新闻ID
     * @return 是否成功
     */
    boolean deleteNews(Integer id);
    
    /**
     * 更新新闻状态
     * 
     * @param id 新闻ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateNewsStatus(Integer id, Integer status);
    
    /**
     * 更新新闻置顶状态
     * 
     * @param id 新闻ID
     * @param isTop 是否置顶
     * @return 是否成功
     */
    boolean updateNewsTopStatus(Integer id, Boolean isTop);
} 