package com.cy.education.service.impl.home;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.home.Carousel;
import com.cy.education.model.params.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.CarouselMapper;
import com.cy.education.service.home.CarouselService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class CarouselServiceImpl implements CarouselService {

    @Autowired
    private CarouselMapper carouselMapper;

    @Override
    public PageResponse<Carousel> listCarousels(ContentQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<Carousel> queryWrapper = new LambdaQueryWrapper<>();

        // 默认只查询已发布的轮播图
        queryWrapper.eq(Carousel::getStatus, 1);

        // 关键词搜索
        if (!StringUtils.isEmpty(param.getKeyword())) {
            queryWrapper.like(Carousel::getTitle, param.getKeyword());
        }

        // 排序
        queryWrapper.orderByAsc(Carousel::getSort).orderByDesc(Carousel::getCreatedAt);

        // 分页查询
        Page<Carousel> page = new Page<>(param.getPageNum(), param.getPageSize());
        Page<Carousel> resultPage = carouselMapper.selectPage(page, queryWrapper);

        // 转换为自定义分页响应对象
        return PageResponse.of(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    }

    @Override
    public Carousel getCarouselById(Integer id) {
        Carousel carousel = carouselMapper.selectById(id);
        if (carousel == null) {
            throw new BusinessException("轮播图不存在");
        }
        return carousel;
    }
}
