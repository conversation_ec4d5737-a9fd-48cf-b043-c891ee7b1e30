<template>
  <view class="record-detail">
    <!-- 顶部统计卡片 -->
    <view class="stats-card">
      <view class="stats-header">
        <text class="stats-title">练习结果</text>
        <text class="stats-date">{{ formatDate(recordDetail.startTime) }}</text>
      </view>
      
      <view class="stats-content">
        <view class="score-section">
          <text class="score-value">{{ recordDetail.score || 0 }}</text>
          <text class="score-label">总分</text>
        </view>
        
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">{{ recordDetail.totalQuestions || 0 }}</text>
            <text class="stat-label">总题数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value correct">{{ recordDetail.correctCount || 0 }}</text>
            <text class="stat-label">正确</text>
          </view>
          <view class="stat-item">
            <text class="stat-value wrong">{{ recordDetail.wrongCount || 0 }}</text>
            <text class="stat-label">错误</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ Math.round((recordDetail.correctCount || 0) / (recordDetail.totalQuestions || 1) * 100) }}%</text>
            <text class="stat-label">正确率</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 答题详情列表 -->
    <view class="answers-list">
      <view class="list-header">
        <text class="list-title">答题详情</text>
      </view>
      
      <view 
        v-for="(answer, index) in answersList" 
        :key="index"
        class="answer-item"
        @click="toggleAnswerDetail(index)"
      >
        <view class="answer-header">
          <text class="question-index">第{{ index + 1 }}题</text>
          <view class="answer-status" :class="{ correct: answer.correct, wrong: !answer.correct }">
            <text v-if="answer.correct" class="status-icon">✓</text>
            <text v-else class="status-icon">✗</text>
            <text class="status-text">{{ answer.correct ? '正确' : '错误' }}</text>
          </view>
        </view>
        
        <view class="question-preview">
          <text class="question-text">{{ answer.questionContent }}</text>
        </view>
        
        <view class="answer-preview">
          <text class="answer-label">你的答案：</text>
          <text class="answer-text">{{ answer.userAnswer || '未作答' }}</text>
        </view>
        
        <view class="answer-preview" v-if="!answer.correct">
          <text class="answer-label">正确答案：</text>
          <text class="answer-text correct">{{ answer.correctAnswer }}</text>
        </view>

        <!-- 展开的详情 -->
        <view v-if="expandedIndex === index" class="answer-detail-expanded">
          <view class="question-detail">
            <view class="question-type">{{ getQuestionTypeText(answer.type) }}</view>
            
            <!-- 选择题选项 -->
            <view class="options" v-if="answer.type === 'single' || answer.type === 'multiple'">
              <view 
                v-for="(option, optIndex) in answer.options" 
                :key="optIndex"
                class="option-item"
                :class="{ 
                  'correct': isCorrectOption(option, answer),
                  'wrong': isWrongOption(option, answer)
                }"
              >
                <view class="option-key">{{ option.key }}.</view>
                <view class="option-content">{{ option.content }}</view>
                <view class="option-status">
                  <text v-if="isCorrectOption(option, answer)" class="correct-icon">✓</text>
                  <text v-else-if="isWrongOption(option, answer)" class="wrong-icon">✗</text>
                </view>
              </view>
            </view>
            
            <!-- 判断题选项 -->
            <view class="options" v-if="answer.type === 'judge'">
              <view 
                class="option-item"
                :class="{ 
                  'correct': answer.correctAnswer === 'A',
                  'wrong': answer.userAnswer === 'A' && answer.correctAnswer !== 'A'
                }"
              >
                <view class="option-key">A.</view>
                <view class="option-content">正确</view>
                <view class="option-status">
                  <text v-if="answer.correctAnswer === 'A'" class="correct-icon">✓</text>
                  <text v-else-if="answer.userAnswer === 'A'" class="wrong-icon">✗</text>
                </view>
              </view>
              <view 
                class="option-item"
                :class="{ 
                  'correct': answer.correctAnswer === 'B',
                  'wrong': answer.userAnswer === 'B' && answer.correctAnswer !== 'B'
                }"
              >
                <view class="option-key">B.</view>
                <view class="option-content">错误</view>
                <view class="option-status">
                  <text v-if="answer.correctAnswer === 'B'" class="correct-icon">✓</text>
                  <text v-else-if="answer.userAnswer === 'B'" class="wrong-icon">✗</text>
                </view>
              </view>
            </view>
            
            <!-- 答案解析 -->
            <view class="explanation" v-if="answer.explanation">
              <text class="explanation-title">答案解析：</text>
              <text class="explanation-content">{{ answer.explanation }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getPracticeRecordDetail } from '@/api/practice'

// 页面参数
const recordId = ref<number>(0)

// 数据
const recordDetail = ref<any>({})
const answersList = ref<any[]>([])
const expandedIndex = ref<number | null>(null)

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'single': '单选题',
    'multiple': '多选题',
    'judge': '判断题',
    'fill': '填空题',
    'essay': '简答题'
  }
  return typeMap[type] || '未知类型'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 切换题目详情显示
const toggleAnswerDetail = (index: number) => {
  if (expandedIndex.value === index) {
    expandedIndex.value = null
  } else {
    expandedIndex.value = index
  }
}

// 判断选项是否正确
const isCorrectOption = (option: any, answer: any) => {
  return answer.correctAnswer?.includes(option.key)
}

// 判断选项是否被用户选择但错误
const isWrongOption = (option: any, answer: any) => {
  return answer.userAnswer?.includes(option.key) && !answer.correctAnswer?.includes(option.key)
}

// 加载练习记录详情
const loadRecordDetail = async () => {
  try {
    const result = await getPracticeRecordDetail(recordId.value)
    console.log('练习记录详情:', result)
    
    if (result) {
      recordDetail.value = (result as any).record || {}
      answersList.value = ((result as any).answers || []).map(ans => ({
        ...ans,
        correct: ans.userAnswer === ans.correctAnswer
      }))
    }
  } catch (error) {
    console.error('加载练习记录详情失败:', error)
    uni.showToast({
      title: '加载详情失败',
      icon: 'none'
    })
  }
}

// 页面加载
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options
  
  recordId.value = parseInt(options.recordId || '0')
  
  if (!recordId.value) {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
    uni.navigateBack()
    return
  }
  
  // 加载练习记录详情
  loadRecordDetail()
})
</script>

<style lang="scss" scoped>
.record-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

.stats-card {
  margin: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  
  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .stats-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .stats-date {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .stats-content {
    .score-section {
      text-align: center;
      margin-bottom: 40rpx;
      
      .score-value {
        font-size: 72rpx;
        font-weight: bold;
        color: #ff6b35;
        display: block;
      }
      
      .score-label {
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 20rpx;
      
      .stat-item {
        text-align: center;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        
        .stat-value {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          display: block;
          
          &.correct {
            color: #34c759;
          }
          
          &.wrong {
            color: #ff3b30;
          }
        }
        
        .stat-label {
          font-size: 24rpx;
          color: #666;
          margin-top: 8rpx;
          display: block;
        }
      }
    }
  }
}

.answers-list {
  margin: 0 30rpx;
  
  .list-header {
    margin-bottom: 20rpx;
    
    .list-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .answer-item {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .answer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .question-index {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
      
      .answer-status {
        display: flex;
        align-items: center;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        
        &.correct {
          background: rgba(52, 199, 89, 0.1);
          color: #34c759;
        }
        
        &.wrong {
          background: rgba(255, 59, 48, 0.1);
          color: #ff3b30;
        }
        
        .status-icon {
          margin-right: 8rpx;
          font-weight: bold;
        }
      }
    }
    
    .question-preview {
      margin-bottom: 15rpx;
      
      .question-text {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
      }
    }
    
    .answer-preview {
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;
      
      .answer-label {
        font-size: 26rpx;
        color: #666;
        margin-right: 10rpx;
      }
      
      .answer-text {
        font-size: 26rpx;
        color: #333;
        
        &.correct {
          color: #34c759;
        }
      }
    }
  }
}

.answer-detail-expanded {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;

  .question-detail {
    .question-type {
      display: inline-block;
      background: #007aff;
      color: #fff;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      margin-bottom: 20rpx;
    }
    
    .options {
      margin-bottom: 30rpx;
      
      .option-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        margin-bottom: 15rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 12rpx;
        
        &.correct {
          border-color: #34c759;
          background: rgba(52, 199, 89, 0.1);
        }
        
        &.wrong {
          border-color: #ff3b30;
          background: rgba(255, 59, 48, 0.1);
        }
        
        .option-key {
          width: 50rpx;
          height: 50rpx;
          background: #f0f0f0;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
          color: #666;
          margin-right: 15rpx;
        }
        
        .option-content {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
        
        .option-status {
          margin-left: 15rpx;
          
          .correct-icon {
            color: #34c759;
            font-size: 32rpx;
            font-weight: bold;
          }
          
          .wrong-icon {
            color: #ff3b30;
            font-size: 32rpx;
            font-weight: bold;
          }
        }
      }
    }
    
    .explanation {
      margin-top: 30rpx;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      border-left: 6rpx solid #007aff;
      
      .explanation-title {
        font-size: 28rpx;
        color: #007aff;
        font-weight: bold;
        margin-bottom: 15rpx;
      }
      
      .explanation-content {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}
</style>