<template>
  <el-card class="box-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span>部门结构</span>
        <div>
          <el-button text :icon="Expand" @click="expandAll">展开全部</el-button>
          <el-button text :icon="Fold" @click="collapseAll">收起全部</el-button>
        </div>
      </div>
    </template>
    <el-tree
      ref="treeRef"
      :data="departmentTree"
      :props="treeProps"
      node-key="id"
      default-expand-all
      :expand-on-click-node="false"
      draggable
      @node-drop="handleDrop"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        <div class="custom-tree-node">
          <div>
            <el-icon><OfficeBuilding /></el-icon>
            <span style="margin-left: 6px;">{{ node.label }}</span>
          </div>
          <div class="tree-node-actions" v-if="!node.disabled">
            <el-button text size="small" @click.stop="$emit('add-child', data)">
              <el-icon><Plus /></el-icon>
            </el-button>
            <el-button text size="small" @click.stop="$emit('edit', data)">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button text size="small" @click.stop="$emit('delete', data, node)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </template>
    </el-tree>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Plus,
  Edit,
  Delete,
  OfficeBuilding,
  Expand,
  Fold
} from '@element-plus/icons-vue'
import type { ElTree } from 'element-plus'

interface Props {
  departmentTree: any[]
  treeProps: {
    children: string
    label: string
  }
}

const props = defineProps<Props>()
const emit = defineEmits(['node-click', 'node-drop', 'add-child', 'edit', 'delete'])

const treeRef = ref<InstanceType<typeof ElTree>>()

// 展开所有节点
const expandAll = () => {
  treeRef.value?.expandAll()
}

// 收起所有节点
const collapseAll = () => {
  treeRef.value?.collapseAll()
}

// 处理拖拽排序
const handleDrop = (draggingNode: any, dropNode: any, dropType: string) => {
  emit('node-drop', draggingNode, dropNode, dropType)
}

// 处理节点点击
const handleNodeClick = (data: any) => {
  emit('node-click', data)
}

// 暴露方法
defineExpose({
  expandAll,
  collapseAll,
  treeRef
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tree-node-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.custom-tree-node:hover .tree-node-actions {
  opacity: 1;
}
</style> 