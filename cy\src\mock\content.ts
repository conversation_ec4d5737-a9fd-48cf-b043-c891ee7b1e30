import { generateId, mockResponse, paginateData } from './utils'
import type { CarouselItem, NewsItem, NoticeItem } from '@/api/content'

// 轮播图模拟数据
let carouselData: CarouselItem[] = [
  {
    id: 1,
    title: '安全生产月活动启动',
    imageUrl: 'https://via.placeholder.com/1920x1080/2c3e50/ffffff?text=安全生产月',
    link: 'https://example.com/safety',
    sort: 1,
    status: 1,
    createTime: '2023-05-20 10:00:00'
  },
  {
    id: 2,
    title: '年度技术培训',
    imageUrl: 'https://via.placeholder.com/1920x1080/3498db/ffffff?text=技术培训',
    link: 'https://example.com/training',
    sort: 2,
    status: 1,
    createTime: '2023-05-18 14:30:00'
  },
  {
    id: 3,
    title: '公司文化建设',
    imageUrl: 'https://via.placeholder.com/1920x1080/e74c3c/ffffff?text=文化建设',
    link: 'https://example.com/culture',
    sort: 3,
    status: 0,
    createTime: '2023-05-15 09:15:00'
  },
  {
    id: 4,
    title: '员工技能竞赛',
    imageUrl: 'https://via.placeholder.com/1920x1080/f39c12/ffffff?text=技能竞赛',
    link: 'https://example.com/competition',
    sort: 4,
    status: 1,
    createTime: '2023-05-10 16:45:00'
  },
  {
    id: 5,
    title: '绿色环保倡议',
    imageUrl: 'https://via.placeholder.com/1920x1080/27ae60/ffffff?text=绿色环保',
    link: 'https://example.com/environment',
    sort: 5,
    status: 1,
    createTime: '2023-05-08 11:20:00'
  }
]

// 新闻模拟数据
let newsData: NewsItem[] = [
  {
    id: 1,
    title: '公司荣获行业安全标兵称号',
    coverUrl: 'https://via.placeholder.com/800x450/2c3e50/ffffff?text=安全标兵',
    content: `<p>我公司在年度安全评比中表现突出，荣获行业安全标兵称号。这一荣誉的获得，体现了公司长期以来对安全生产工作的高度重视和不懈努力。</p>
    <p>公司始终坚持"安全第一、预防为主、综合治理"的方针，建立健全了安全生产责任制，完善了各项安全管理制度。</p>
    <p>未来，公司将继续加强安全管理，为员工创造更加安全的工作环境。</p>`,
    publishTime: '2023-05-15 14:00:00',
    viewCount: 1256,
    status: 1,
    isTop: true,
    createTime: '2023-05-15 10:00:00'
  },
  {
    id: 2,
    title: '公司开展安全培训活动',
    coverUrl: 'https://via.placeholder.com/800x450/3498db/ffffff?text=安全培训',
    content: `<p>为提高员工安全意识，公司组织开展了安全培训活动。培训内容包括安全生产法律法规、安全操作规程、应急处置等。</p>
    <p>此次培训邀请了行业专家进行授课，采用理论讲解与实际操作相结合的方式，确保培训效果。</p>
    <p>通过培训，员工安全意识得到进一步提升，为公司安全生产打下了坚实基础。</p>`,
    publishTime: '2023-05-10 09:30:00',
    viewCount: 856,
    status: 1,
    isTop: false,
    createTime: '2023-05-09 16:20:00'
  },
  {
    id: 3,
    title: '公司技术创新取得突破',
    coverUrl: 'https://via.placeholder.com/800x450/e74c3c/ffffff?text=技术创新',
    content: `<p>经过技术部门不懈努力，公司在矿山安全技术创新方面取得重大突破。新技术的应用将大大提高作业效率和安全性。</p>
    <p>该技术已申请多项专利，并在实际生产中得到成功应用，获得了良好的经济和社会效益。</p>
    <p>公司将继续加大技术创新投入，推动企业高质量发展。</p>`,
    publishTime: '2023-05-05 11:00:00',
    viewCount: 1024,
    status: 1,
    isTop: false,
    createTime: '2023-05-04 15:40:00'
  },
  {
    id: 4,
    title: '员工风采展示活动圆满举办',
    coverUrl: 'https://via.placeholder.com/800x450/f39c12/ffffff?text=员工风采',
    content: `<p>公司成功举办了员工风采展示活动，充分展现了员工的精神风貌和专业技能。</p>
    <p>活动包括技能比赛、文艺表演、优秀员工表彰等环节，吸引了全体员工的积极参与。</p>
    <p>此次活动增强了团队凝聚力，激发了员工的工作热情。</p>`,
    publishTime: '2023-04-28 16:00:00',
    viewCount: 678,
    status: 1,
    isTop: false,
    createTime: '2023-04-27 14:30:00'
  }
]

// 公告模拟数据
let noticeData: NoticeItem[] = [
  {
    id: 1,
    title: '关于加强安全生产工作的通知',
    content: `<p>各部门、各单位：</p>
    <p>为进一步加强安全生产工作，提高安全防范意识，现将有关事项通知如下：</p>
    <ol>
      <li>严格落实安全生产责任制，各级管理人员要切实履行安全生产职责。</li>
      <li>加强安全教育培训，提高员工安全意识和操作技能。</li>
      <li>定期开展安全检查，及时发现和消除安全隐患。</li>
      <li>完善应急预案，确保突发事件能够得到及时有效处置。</li>
    </ol>
    <p>特此通知。</p>`,
    publishTime: '2023-05-18 09:00:00',
    importance: 3,
    status: 1,
    isTop: true,
    createTime: '2023-05-17 16:30:00'
  },
  {
    id: 2,
    title: '5月份员工培训计划',
    content: `<p>各位员工：</p>
    <p>根据公司年度培训计划，5月份将开展以下培训活动：</p>
    <ul>
      <li>安全生产培训：5月8日-5月12日</li>
      <li>技能提升培训：5月15日-5月19日</li>
      <li>职业素养培训：5月22日-5月26日</li>
    </ul>
    <p>请各位员工按时参加培训，具体安排另行通知。</p>`,
    publishTime: '2023-05-12 10:30:00',
    importance: 2,
    status: 1,
    isTop: false,
    createTime: '2023-05-11 15:20:00'
  },
  {
    id: 3,
    title: '关于调整工作时间的通知',
    content: `<p>各部门：</p>
    <p>根据季节变化和生产需要，从6月1日起，公司上班时间调整为：</p>
    <p>夏季作息时间：上午8:00-12:00，下午14:30-18:30</p>
    <p>请各部门做好相应调整，确保工作正常进行。</p>`,
    publishTime: '2023-05-25 08:00:00',
    importance: 1,
    status: 0,
    isTop: false,
    createTime: '2023-05-08 11:10:00'
  },
  {
    id: 4,
    title: '关于开展安全月活动的通知',
    content: `<p>为贯彻落实国家安全生产方针政策，营造安全生产浓厚氛围，公司决定开展安全月活动。</p>
    <p>活动时间：6月1日-6月30日</p>
    <p>活动主题：人人讲安全、个个会应急</p>
    <p>各部门要高度重视，精心组织，确保活动取得实效。</p>`,
    publishTime: '2023-05-30 14:00:00',
    importance: 3,
    status: 1,
    isTop: false,
    createTime: '2023-05-29 10:45:00'
  }
]

// 轮播图接口
export const carouselMockHandlers = [
  // 获取轮播图列表
  {
    url: '/content/carousel/list',
    method: 'GET',
    handler: (params: any) => {
      const { pageNum = 1, pageSize = 10, status, keyword } = params
      
      let filteredData = [...carouselData]
      
      // 状态筛选
      if (status !== undefined) {
        filteredData = filteredData.filter(item => item.status === Number(status))
      }
      
      // 关键字搜索
      if (keyword) {
        filteredData = filteredData.filter(item => 
          item.title.includes(keyword)
        )
      }
      
      // 按排序字段和创建时间排序
      filteredData.sort((a, b) => {
        if (a.sort !== b.sort) {
          return a.sort - b.sort
        }
        return new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
      })
      
      const result = paginateData(filteredData, pageNum, pageSize)
      return mockResponse(result, 200, '获取成功')
    }
  },
  
  // 获取轮播图详情
  {
    url: '/content/carousel/:id',
    method: 'GET',
    handler: (params: any) => {
      console.log('获取轮播图详情参数:', params)
      const id = Number(params?.id)
      if (!id) {
        console.error('轮播图ID无效:', params)
        return mockResponse(null, 404, 'ID参数无效')
      }
      
      const carousel = carouselData.find(item => item.id === id)
      if (carousel) {
        return mockResponse(carousel, 200, '获取成功')
      }
      return mockResponse(null, 404, '轮播图不存在')
    }
  },
  
  // 新增轮播图
  {
    url: '/content/carousel',
    method: 'POST',
    handler: (data: Partial<CarouselItem>) => {
      const newCarousel: CarouselItem = {
        id: Number(generateId()),
        title: data.title!,
        imageUrl: data.imageUrl!,
        link: data.link!,
        sort: data.sort || 0,
        status: data.status || 1,
        createTime: new Date().toLocaleString()
      }
      carouselData.unshift(newCarousel)
      return mockResponse({ id: newCarousel.id }, 200, '新增成功')
    }
  },
  
  // 编辑轮播图
  {
    url: '/content/carousel/:id',
    method: 'PUT',
    handler: (data: Partial<CarouselItem>, params: any) => {
      const index = carouselData.findIndex(item => item.id === Number(params.id))
      if (index !== -1) {
        carouselData[index] = {
          ...carouselData[index],
          ...data,
          updateTime: new Date().toLocaleString()
        }
        return mockResponse({ success: true }, 200, '编辑成功')
      }
      return mockResponse({ success: false }, 404, '轮播图不存在')
    }
  },
  
  // 删除轮播图
  {
    url: '/content/carousel/:id',
    method: 'DELETE',
    handler: (data: any, params: any) => {
      const index = carouselData.findIndex(item => item.id === Number(params.id))
      if (index !== -1) {
        carouselData.splice(index, 1)
        return mockResponse({ success: true }, 200, '删除成功')
      }
      return mockResponse({ success: false }, 404, '轮播图不存在')
    }
  },
  
  // 切换轮播图状态
  {
    url: '/content/carousel/:id/status',
    method: 'PUT',
    handler: (data: { status: number }, params: any) => {
      const carousel = carouselData.find(item => item.id === Number(params.id))
      if (carousel) {
        carousel.status = data.status
        carousel.updateTime = new Date().toLocaleString()
        return mockResponse({ success: true }, 200, '状态更新成功')
      }
      return mockResponse({ success: false }, 404, '轮播图不存在')
    }
  }
]

// 新闻接口
export const newsMockHandlers = [
  // 获取新闻列表
  {
    url: '/content/news/list',
    method: 'GET',
    handler: (params: any) => {
      const { pageNum = 1, pageSize = 10, status, isTop, keyword } = params
      
      let filteredData = [...newsData]
      
      // 状态筛选
      if (status !== undefined) {
        filteredData = filteredData.filter(item => item.status === Number(status))
      }
      
      // 置顶筛选
      if (isTop !== undefined) {
        filteredData = filteredData.filter(item => item.isTop === Boolean(isTop))
      }
      
      // 关键字搜索
      if (keyword) {
        filteredData = filteredData.filter(item => 
          item.title.includes(keyword) || item.content.includes(keyword)
        )
      }
      
      // 按置顶和发布时间排序
      filteredData.sort((a, b) => {
        if (a.isTop !== b.isTop) {
          return b.isTop ? 1 : -1
        }
        return new Date(b.publishTime).getTime() - new Date(a.publishTime).getTime()
      })
      
      const result = paginateData(filteredData, pageNum, pageSize)
      return mockResponse(result, 200, '获取成功')
    }
  },
  
  // 获取新闻详情
  {
    url: '/content/news/:id',
    method: 'GET',
    handler: (params: any) => {
      console.log('获取新闻详情参数:', params)
      const id = Number(params?.id)
      if (!id) {
        console.error('新闻ID无效:', params)
        return mockResponse(null, 404, 'ID参数无效')
      }
      
      const news = newsData.find(item => item.id === id)
      if (news) {
        // 增加阅读量
        news.viewCount++
        return mockResponse(news, 200, '获取成功')
      }
      return mockResponse(null, 404, '新闻不存在')
    }
  },
  
  // 新增新闻
  {
    url: '/content/news',
    method: 'POST',
    handler: (data: Partial<NewsItem>) => {
      const newNews: NewsItem = {
        id: Number(generateId()),
        title: data.title!,
        coverUrl: data.coverUrl,
        content: data.content!,
        publishTime: data.publishTime!,
        viewCount: 0,
        status: data.status || 1,
        isTop: data.isTop || false,
        createTime: new Date().toLocaleString()
      }
      newsData.unshift(newNews)
      return mockResponse({ id: newNews.id }, 200, '新增成功')
    }
  },
  
  // 编辑新闻
  {
    url: '/content/news/:id',
    method: 'PUT',
    handler: (data: Partial<NewsItem>, params: any) => {
      const index = newsData.findIndex(item => item.id === Number(params.id))
      if (index !== -1) {
        newsData[index] = {
          ...newsData[index],
          ...data,
          updateTime: new Date().toLocaleString()
        }
        return mockResponse({ success: true }, 200, '编辑成功')
      }
      return mockResponse({ success: false }, 404, '新闻不存在')
    }
  },
  
  // 删除新闻
  {
    url: '/content/news/:id',
    method: 'DELETE',
    handler: (data: any, params: any) => {
      const index = newsData.findIndex(item => item.id === Number(params.id))
      if (index !== -1) {
        newsData.splice(index, 1)
        return mockResponse({ success: true }, 200, '删除成功')
      }
      return mockResponse({ success: false }, 404, '新闻不存在')
    }
  },
  
  // 切换新闻状态
  {
    url: '/content/news/:id/status',
    method: 'PUT',
    handler: (data: { status: number }, params: any) => {
      const news = newsData.find(item => item.id === Number(params.id))
      if (news) {
        news.status = data.status
        news.updateTime = new Date().toLocaleString()
        return mockResponse({ success: true }, 200, '状态更新成功')
      }
      return mockResponse({ success: false }, 404, '新闻不存在')
    }
  },
  
  // 切换新闻置顶状态
  {
    url: '/content/news/:id/top',
    method: 'PUT',
    handler: (data: { isTop: boolean }, params: any) => {
      const news = newsData.find(item => item.id === Number(params.id))
      if (news) {
        news.isTop = data.isTop
        news.updateTime = new Date().toLocaleString()
        return mockResponse({ success: true }, 200, '置顶状态更新成功')
      }
      return mockResponse({ success: false }, 404, '新闻不存在')
    }
  }
]

// 公告接口
export const noticeMockHandlers = [
  // 获取公告列表
  {
    url: '/content/notice/list',
    method: 'GET',
    handler: (params: any) => {
      const { pageNum = 1, pageSize = 10, status, isTop, importance, keyword } = params
      
      let filteredData = [...noticeData]
      
      // 状态筛选
      if (status !== undefined) {
        filteredData = filteredData.filter(item => item.status === Number(status))
      }
      
      // 置顶筛选
      if (isTop !== undefined) {
        filteredData = filteredData.filter(item => item.isTop === Boolean(isTop))
      }
      
      // 重要程度筛选
      if (importance !== undefined) {
        filteredData = filteredData.filter(item => item.importance === Number(importance))
      }
      
      // 关键字搜索
      if (keyword) {
        filteredData = filteredData.filter(item => 
          item.title.includes(keyword) || item.content.includes(keyword)
        )
      }
      
      // 按置顶、重要程度和发布时间排序
      filteredData.sort((a, b) => {
        if (a.isTop !== b.isTop) {
          return b.isTop ? 1 : -1
        }
        if (a.importance !== b.importance) {
          return b.importance - a.importance
        }
        return new Date(b.publishTime).getTime() - new Date(a.publishTime).getTime()
      })
      
      const result = paginateData(filteredData, pageNum, pageSize)
      return mockResponse(result, 200, '获取成功')
    }
  },
  
  // 获取公告详情
  {
    url: '/content/notice/:id',
    method: 'GET',
    handler: (params: any) => {
      console.log('获取公告详情参数:', params)
      const id = Number(params?.id)
      if (!id) {
        console.error('公告ID无效:', params)
        return mockResponse(null, 404, 'ID参数无效')
      }
      
      const notice = noticeData.find(item => item.id === id)
      if (notice) {
        return mockResponse(notice, 200, '获取成功')
      }
      return mockResponse(null, 404, '公告不存在')
    }
  },
  
  // 新增公告
  {
    url: '/content/notice',
    method: 'POST',
    handler: (data: Partial<NoticeItem>) => {
      const newNotice: NoticeItem = {
        id: Number(generateId()),
        title: data.title!,
        content: data.content!,
        publishTime: data.publishTime!,
        importance: data.importance || 1,
        status: data.status || 1,
        isTop: data.isTop || false,
        createTime: new Date().toLocaleString()
      }
      noticeData.unshift(newNotice)
      return mockResponse({ id: newNotice.id }, 200, '新增成功')
    }
  },
  
  // 编辑公告
  {
    url: '/content/notice/:id',
    method: 'PUT',
    handler: (data: Partial<NoticeItem>, params: any) => {
      const index = noticeData.findIndex(item => item.id === Number(params.id))
      if (index !== -1) {
        noticeData[index] = {
          ...noticeData[index],
          ...data,
          updateTime: new Date().toLocaleString()
        }
        return mockResponse({ success: true }, 200, '编辑成功')
      }
      return mockResponse({ success: false }, 404, '公告不存在')
    }
  },
  
  // 删除公告
  {
    url: '/content/notice/:id',
    method: 'DELETE',
    handler: (data: any, params: any) => {
      const index = noticeData.findIndex(item => item.id === Number(params.id))
      if (index !== -1) {
        noticeData.splice(index, 1)
        return mockResponse({ success: true }, 200, '删除成功')
      }
      return mockResponse({ success: false }, 404, '公告不存在')
    }
  },
  
  // 切换公告状态
  {
    url: '/content/notice/:id/status',
    method: 'PUT',
    handler: (data: { status: number }, params: any) => {
      const notice = noticeData.find(item => item.id === Number(params.id))
      if (notice) {
        notice.status = data.status
        notice.updateTime = new Date().toLocaleString()
        return mockResponse({ success: true }, 200, '状态更新成功')
      }
      return mockResponse({ success: false }, 404, '公告不存在')
    }
  },
  
  // 切换公告置顶状态
  {
    url: '/content/notice/:id/top',
    method: 'PUT',
    handler: (data: { isTop: boolean }, params: any) => {
      const notice = noticeData.find(item => item.id === Number(params.id))
      if (notice) {
        notice.isTop = data.isTop
        notice.updateTime = new Date().toLocaleString()
        return mockResponse({ success: true }, 200, '置顶状态更新成功')
      }
      return mockResponse({ success: false }, 404, '公告不存在')
    }
  }
] 