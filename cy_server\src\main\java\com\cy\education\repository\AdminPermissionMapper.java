package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.AdminPermission;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 管理员权限关联Mapper接口
 */
@Repository
public interface AdminPermissionMapper extends BaseMapper<AdminPermission> {
    
    /**
     * 批量插入管理员权限关联
     *
     * @param adminId 管理员ID
     * @param permissionIds 权限ID列表
     * @return 插入数量
     */
    int batchInsert(@Param("adminId") Integer adminId, @Param("permissionIds") List<Integer> permissionIds);
    
    /**
     * 根据管理员ID删除权限关联
     *
     * @param adminId 管理员ID
     * @return 删除数量
     */
    int deleteByAdminId(@Param("adminId") Integer adminId);
    
    /**
     * 根据管理员ID查询权限ID列表
     *
     * @param adminId 管理员ID
     * @return 权限ID列表
     */
    List<Integer> selectPermissionIdsByAdminId(@Param("adminId") Integer adminId);
} 