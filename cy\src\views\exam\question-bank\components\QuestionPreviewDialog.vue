<template>
  <el-dialog
    v-model="visible"
    title="题目预览"
    width="650px"
    destroy-on-close
  >
    <div v-if="question" class="question-preview">
      <div class="question-header">
        <el-tag :type="getTypeTagType(question.type)" class="question-type">
          {{ getQuestionTypeName(question.type) }}
        </el-tag>
      </div>
      <div class="question-content" v-html="formatContent(question.title)"></div>
      
      <!-- 选择题选项 -->
      <div v-if="question.type === 'single' || question.type === 'multiple'" class="question-options">
        <div v-for="(option, index) in parseOptions(question.options)" :key="index" class="question-option">
          <div class="option-label">{{ String.fromCharCode(65 + index) }}.</div>
          <div class="option-content" v-html="option"></div>
        </div>
      </div>
      
      <!-- 判断题选项 -->
      <div v-if="question.type === 'judgment'" class="question-options">
        <div class="question-option">
          <div class="option-label">A.</div>
          <div class="option-content">正确</div>
        </div>
        <div class="question-option">
          <div class="option-label">B.</div>
          <div class="option-content">错误</div>
        </div>
      </div>
      
      <!-- 正确答案 -->
      <div class="correct-answer">
        <div class="answer-title">正确答案：</div>
        <div class="answer-content">
          {{ formatAnswer(question.correctAnswer, question.type) }}
        </div>
      </div>
      
      <!-- 题目解析 -->
      <div v-if="question.explanation" class="question-explanation">
        <div class="explanation-title">题目解析：</div>
        <div class="explanation-content" v-html="question.explanation"></div>
      </div>
    </div>
    <el-empty v-else description="暂无题目数据" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface QuestionType {
  value: string
  label: string
}

// Props declaration
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  question: {
    type: Object,
    default: null
  },
  questionTypes: {
    type: Array as () => QuestionType[],
    default: () => []
  }
})

// Emits declaration
const emit = defineEmits(['update:modelValue'])

// Reactive state
const visible = ref(props.modelValue)

// Watch for prop changes
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// Get question type name
const getQuestionTypeName = (type: string) => {
  try {
    const found = props.questionTypes.find((item: QuestionType) => {
      return item && typeof item === 'object' && item.value === type
    })
    return found && found.label ? String(found.label) : '未知类型'
  } catch (error) {
    return '未知类型'
  }
}

// Get question type tag style
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'single': 'primary',
    'multiple': 'success',
    'judgment': 'info',
    'fill': 'warning',
    'essay': 'danger'
  }
  return typeMap[type] || 'default'
}

// Format question content
const formatContent = (content: string) => {
  return content.replace(/【】/g, '<span style="display:inline-block;min-width:80px;border-bottom:1px solid #000;"></span>')
}

// Get option label
const getOptionLabel = (label: string) => {
  return label
}

// Get multiple answer
const getMultipleAnswer = (answer: string | string[]) => {
  if (Array.isArray(answer)) {
    return answer.join('、')
  }
  return answer?.split('').join('、') || ''
}

// Get judgment answer
const getJudgmentAnswer = (answer: string) => {
  if (answer === 'true' || answer === 'A') return '正确'
  if (answer === 'false' || answer === 'B') return '错误'
  return answer
}

// Parse options
const parseOptions = (options: any) => {
  try {
  if (typeof options === 'string') {
      return [options];
  } else if (Array.isArray(options)) {
      // 处理可能的两种情况：
      // 1. 字符串数组 ['选项A', '选项B', ...]
      // 2. 对象数组 [{id: 'A', content: '选项A'}, ...]
      return options.map(option => {
        if (typeof option === 'object' && option !== null && 'content' in option) {
          return option.content;
        }
        return String(option);
      });
    } else if (options && typeof options === 'object') {
      // 处理单个选项对象
      return ['content' in options ? options.content : String(options)];
  } else {
      return [];
    }
  } catch (error) {
    console.error('解析选项出错:', error);
    return [];
  }
}

// Format answer
const formatAnswer = (answer: string | string[], type: string) => {
  if (type === 'single') {
    return getOptionLabel(answer as string)
  } else if (type === 'multiple') {
    return getMultipleAnswer(answer)
  } else if (type === 'judgment') {
    return getJudgmentAnswer(answer as string)
  } else {
    return answer
  }
}
</script>

<style scoped>
.question-preview {
  padding: 16px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-content {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
}

.question-options {
  margin-bottom: 16px;
}

.question-option {
  display: flex;
  margin-bottom: 8px;
}

.option-label {
  flex-shrink: 0;
  width: 24px;
  font-weight: bold;
}

.option-content {
  flex: 1;
}

.correct-answer {
  display: flex;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.answer-title {
  flex-shrink: 0;
  width: 80px;
  font-weight: bold;
}

.answer-content {
  flex: 1;
  color: #409eff;
}

.question-explanation {
  display: flex;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.explanation-title {
  flex-shrink: 0;
  width: 80px;
  font-weight: bold;
}

.explanation-content {
  flex: 1;
  color: #67c23a;
}
</style> 