<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">考试统计</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <view v-if="loading" class="loading-state">
        <uni-load-more status="loading"/>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="!examInfo" class="error-state">
        <up-icon color="#8e8e93" name="info-circle" size="48"></up-icon>
        <text class="error-text">考试信息加载失败</text>
        <view class="retry-btn" @click="retryLoad">
          <text class="retry-text">重新加载</text>
        </view>
      </view>

      <!-- 考试信息卡片 -->
      <view v-if="examInfo" class="exam-info-card">
        <view class="exam-header">
          <view class="exam-title-section">
            <text class="exam-title">{{ examInfo.title }}</text>
            <view :class="getStatusClass(examInfo.status)" class="exam-status">
              <text class="status-text">{{ getStatusText(examInfo.status) }}</text>
            </view>
          </view>
        </view>

        <view class="exam-meta-row">
          <view class="meta-item">
            <view class="meta-icon">
              <up-icon color="#667eea" name="clock" size="18"></up-icon>
            </view>
            <view class="meta-content">
              <text class="meta-label">考试时长</text>
              <text class="meta-value">{{ examInfo.duration }}分钟</text>
            </view>
          </view>
          <view class="meta-divider"></view>
          <view class="meta-item">
            <view class="meta-icon">
              <up-icon color="#f093fb" name="star" size="18"></up-icon>
            </view>
            <view class="meta-content">
              <text class="meta-label">总分</text>
              <text class="meta-value">{{ examInfo.paper?.totalScore || 0 }}分</text>
            </view>
          </view>
          <view class="meta-divider"></view>
          <view class="meta-item">
            <view class="meta-icon">
              <up-icon color="#43e97b" name="checkmark-circle" size="18"></up-icon>
            </view>
            <view class="meta-content">
              <text class="meta-label">及格分</text>
              <text class="meta-value">{{ examInfo.paper?.passingScore || 0 }}分</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 统计内容 -->
      <view v-if="examInfo && examStatistics" class="statistics-content">
        <!-- 基础统计 -->
        <view class="section-card">
          <view class="section-header">
            <view class="section-icon">
              <up-icon color="#667eea" name="file-text" size="20"></up-icon>
            </view>
            <text class="section-title">基础统计</text>
          </view>
          <view class="stats-grid">
            <view class="stat-item">
              <view class="stat-icon">
                <up-icon color="#667eea" name="account" size="24"></up-icon>
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ examStatistics.totalParticipants || 0 }}</text>
                <text class="stat-label">总参与人数</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon">
                <up-icon color="#43e97b" name="checkmark" size="24"></up-icon>
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ examStatistics.completedCount || 0 }}</text>
                <text class="stat-label">已完成人数</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon">
                <up-icon color="#f59e0b" name="clock" size="24"></up-icon>
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ examStatistics.ongoingCount || 0 }}</text>
                <text class="stat-label">进行中人数</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon">
                <up-icon color="#f093fb" name="star" size="24"></up-icon>
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ examStatistics.averageScore || 0 }}</text>
                <text class="stat-label">平均分</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 分数统计 -->
        <view class="section-card">
          <view class="section-header">
            <view class="section-icon">
              <up-icon color="#43e97b" name="file-text" size="20"></up-icon>
            </view>
            <text class="section-title">分数统计</text>
          </view>
          <view class="score-stats-section">
            <view class="score-stats-grid">
              <view class="score-stat-item">
                <text class="score-stat-label">通过率</text>
                <text class="score-stat-value pass-rate">{{ examStatistics.passRate || 0 }}%</text>
              </view>
              <view class="score-stat-item">
                <text class="score-stat-label">最高分</text>
                <text class="score-stat-value highest">{{ examStatistics.highestScore || 0 }}</text>
              </view>
              <view class="score-stat-item">
                <text class="score-stat-label">最低分</text>
                <text class="score-stat-value lowest">{{ examStatistics.lowestScore || 0 }}</text>
              </view>
              <view class="score-stat-item">
                <text class="score-stat-label">中位数</text>
                <text class="score-stat-value median">{{ examStatistics.medianScore || 0 }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 分数分布图表 -->
        <view class="section-card">
          <ScoreDistributionChart
              v-if="examStatistics.scoreDistribution"
              :data="examStatistics.scoreDistribution"
          />
        </view>

        <!-- 完成时间分布图表 -->
        <view class="section-card">
          <DurationDistributionChart
              v-if="examStatistics.durationDistribution"
              :data="examStatistics.durationDistribution"
          />
        </view>

        <!-- 考试次数统计 -->
        <view v-if="examStatistics.attemptStats" class="section-card">
          <view class="section-header">
            <view class="section-icon">
              <up-icon color="#f59e0b" name="file-text" size="20"></up-icon>
            </view>
            <text class="section-title">考试次数统计</text>
          </view>
          <view class="attempt-stats-grid">
            <view class="attempt-stat-item">
              <text class="attempt-stat-label">平均次数</text>
              <text class="attempt-stat-value">{{ examStatistics.attemptStats.averageAttempts || 0 }}</text>
            </view>
            <view class="attempt-stat-item">
              <text class="attempt-stat-label">最大次数</text>
              <text class="attempt-stat-value">{{ examStatistics.attemptStats.maxAttempts || 0 }}</text>
            </view>
            <view class="attempt-stat-item">
              <text class="attempt-stat-label">最小次数</text>
              <text class="attempt-stat-value">{{ examStatistics.attemptStats.minAttempts || 0 }}</text>
            </view>
            <view class="attempt-stat-item">
              <text class="attempt-stat-label">参与用户</text>
              <text class="attempt-stat-value">{{ examStatistics.attemptStats.totalUsers || 0 }}</text>
            </view>
          </view>
        </view>

        <!-- 部门统计图表 -->
        <view v-if="examStatistics.departmentStats && examStatistics.departmentStats.length > 0" class="section-card">
          <DepartmentStatsChart :data="examStatistics.departmentStats"/>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {ref} from 'vue'
import {getExamDetail, type ExamVO, getExamStats, type ExamStats} from '@/api/exam'
import {onLoad} from "@dcloudio/uni-app";
import {
  ScoreDistributionChart,
  DurationDistributionChart,
  DepartmentStatsChart
} from '@/components/Charts'

// 考试信息
const examInfo = ref<ExamVO | null>(null)
const examStatistics = ref<ExamStats | null>(null)
const loading = ref(true)

onLoad(async (options) => {
  const examId = options?.id;
  if (!examId) {
    uni.showToast({
      title: '考试ID不能为空',
      icon: 'none'
    })
    loading.value = false
    return
  }

  // 保存考试ID到本地存储，用于重试
  uni.setStorageSync('currentExamId', examId)

  // 加载考试详情和统计
  await loadExamDetail(examId)
  await loadExamStats(examId)
})

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '未开始'
    case 2:
      return '进行中'
    case 3:
      return '已结束'
    default:
      return '草稿'
  }
}

// 获取状态样式类名
const getStatusClass = (status: number) => {
  switch (status) {
    case 1:
      return 'upcoming'
    case 2:
      return 'ongoing'
    case 3:
      return 'completed'
    default:
      return 'draft'
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 重新加载
const retryLoad = () => {
  loading.value = true
  examInfo.value = null
  // 重新执行onLoad逻辑
  const examId = uni.getStorageSync('currentExamId')
  if (examId) {
    loadExamDetail(examId)
    loadExamStats(examId)
  }
}

// 加载考试详情
const loadExamDetail = async (examId: string) => {
  try {
    const data = await getExamDetail(Number(examId))
    if (data) {
      examInfo.value = data
    } else {
      uni.showToast({
        title: '考试信息不存在',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '加载考试信息失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const loadExamStats = async (examId: string) => {
  try {
    const stats = await getExamStats(Number(examId))
    console.log('获取到的考试统计数据:', stats)
    examStatistics.value = stats
  } catch (error) {
    console.error('加载考试统计信息失败:', error)
    uni.showToast({
      title: '加载考试统计信息失败',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>

// 加载状态
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

// 错误状态
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-text {
  font-size: 16px;
  color: #8e8e93;
  margin: 16px 0 24px 0;
}

.retry-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.retry-text {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
}

.exam-info-card,
.section-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.exam-header {
  margin-bottom: 20px;
}

.exam-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.exam-title {
  flex: 1;
  font-size: 20px;
  font-weight: 600;
  color: #1a1d2e;
  line-height: 1.3;
}

.exam-status {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;

  &.upcoming {
    background: #fff2e8;
    color: #fa8c16;
  }

  &.ongoing {
    background: #f6ffed;
    color: #52c41a;
  }

  &.completed {
    background: #f0f0f0;
    color: #666;
  }
}

.exam-meta-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 10px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.meta-divider {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.2), transparent);
}

.meta-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
  text-align: center;
}

.meta-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.meta-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: center;
}

.meta-label {
  font-size: 11px;
  color: #8e8e93;
  font-weight: 500;
}

.meta-value {
  font-size: 13px;
  color: #1a1d2e;
  font-weight: 600;
}

// 区域样式
.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.section-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

// 统计数据
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8faff;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.stat-label {
  font-size: 12px;
  color: #8e8e93;
}

// 分数统计
.score-stats-section {
  padding: 16px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.score-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.score-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
}

.score-stat-label {
  font-size: 12px;
  color: #8e8e93;
}

.score-stat-value {
  font-size: 16px;
  font-weight: 600;

  &.pass-rate {
    color: #10b981;
  }

  &.highest {
    color: #f59e0b;
  }

  &.lowest {
    color: #ef4444;
  }

  &.median {
    color: #3b82f6;
  }
}

// 考试次数统计
.attempt-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.attempt-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
}

.attempt-stat-label {
  font-size: 12px;
  color: #8e8e93;
}

.attempt-stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #f59e0b;
}
</style>
