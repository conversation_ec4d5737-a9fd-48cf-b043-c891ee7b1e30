<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.StudentMapper">

    <!-- 分页查询学员列表，并关联部门信息 -->
    <select id="selectStudentPage" resultType="com.cy.education.model.entity.Student">
        SELECT s.*, d.name AS department_name
        FROM students s
        LEFT JOIN departments d ON s.department_id = d.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (s.username LIKE CONCAT('%', #{keyword}, '%')
                     OR s.name LIKE CONCAT('%', #{keyword}, '%')
                     OR s.phone LIKE CONCAT('%', #{keyword}, '%')
                     OR s.employee_id LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="departmentIds != null and departmentIds.size() > 0">
                AND s.department_id IN
                <foreach collection="departmentIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="status != null">
                AND s.status = #{status}
            </if>
        </where>
        ORDER BY s.created_at DESC
    </select>

    <!-- 根据ID查询学员详情，并关联部门信息 -->
    <select id="selectStudentById" resultType="com.cy.education.model.entity.Student">
        SELECT s.*, d.name AS department_name
        FROM students s
        LEFT JOIN departments d ON s.department_id = d.id
        WHERE s.id = #{id}
    </select>

</mapper> 