import { get, post, del } from '@/utils/request'

/**
 * 管理员接口数据类型
 */
export interface Admin {
  id: number
  realName: string
  username: string
  departmentId: number
  department?: string
  phone: string
  email?: string
  avatar: string
  lastLoginTime?: string
  createTime: string
  status: number
  permissions?: number[]
  remark?: string
}

/**
 * 获取管理员列表
 * @param params 查询参数
 */
export function getAdminList(params: {
  keyword?: string
  departmentId?: number
  status?: number
  page: number
  size: number
}) {
  return get<{
    list: Admin[]
    total: number
  }>('/admin/list', params)
}

/**
 * 获取管理员详情
 * @param id 管理员ID
 */
export function getAdminById(id: number) {
  return get<Admin>(`/admin/${id}`)
}

/**
 * 添加管理员
 * @param data 管理员信息
 */
export function addAdmin(data: Partial<Admin> & { password: string }) {
  return post<{ id: number }>('/admin/add', data)
}

/**
 * 更新管理员信息
 * @param data 管理员信息
 */
export function updateAdmin(data: Partial<Admin>) {
  return post<{ success: boolean }>('/admin/update', data)
}

/**
 * 删除管理员
 * @param id 管理员ID
 */
export function deleteAdmin(id: number) {
  return del<{ success: boolean }>(`/admin/${id}`)
}

/**
 * 更新管理员状态
 * @param id 管理员ID
 * @param status 状态：0禁用，1启用
 */
export function updateAdminStatus(id: number, status: number) {
  return post<{ success: boolean }>('/admin/status', { id, status })
}

/**
 * 重置管理员密码
 * @param id 管理员ID
 */
export function resetAdminPassword(id: number) {
  return post<{ password: string }>('/admin/reset-password', { id })
}

/**
 * 获取管理员权限
 * @param id 管理员ID
 */
export function getAdminPermissions(id: number) {
  return get<{ permissions: number[] }>(`/admin/permissions/${id}`)
}

/**
 * 设置管理员权限
 * @param id 管理员ID
 * @param permissions 权限ID列表
 */
export function setAdminPermissions(id: number, permissions: number[]) {
  return post<{ success: boolean }>('/admin/permissions', { id, permissions })
}

/**
 * 导出管理员列表
 * @param params 查询参数
 */
export function exportAdminList(params: {
  keyword?: string
  departmentId?: number
  status?: number
}) {
  return get<Blob>('/admin/export', params, {
    responseType: 'blob'
  } as any)
} 