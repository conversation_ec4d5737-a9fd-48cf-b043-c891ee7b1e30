/**
 * 通知已读状态管理工具
 */

const NOTICE_READ_KEY = 'notice_read_status'

/**
 * 获取所有已读通知的ID列表
 */
export const getReadNoticeIds = (): number[] => {
    try {
        const readIds = uni.getStorageSync(NOTICE_READ_KEY)
        return readIds ? JSON.parse(readIds) : []
    } catch (error) {
        console.error('获取已读通知ID失败:', error)
        return []
    }
}

/**
 * 标记通知为已读
 * @param noticeId 通知ID
 */
export const markNoticeAsRead = (noticeId: number): void => {
    try {
        const readIds = getReadNoticeIds()
        if (!readIds.includes(noticeId)) {
            readIds.push(noticeId)
            uni.setStorageSync(NOTICE_READ_KEY, JSON.stringify(readIds))
        }
    } catch (error) {
        console.error('标记通知已读失败:', error)
    }
}

/**
 * 批量标记通知为已读
 * @param noticeIds 通知ID数组
 */
export const markNoticesAsRead = (noticeIds: number[]): void => {
    try {
        const readIds = getReadNoticeIds()
        const newReadIds = [...new Set([...readIds, ...noticeIds])]
        uni.setStorageSync(NOTICE_READ_KEY, JSON.stringify(newReadIds))
    } catch (error) {
        console.error('批量标记通知已读失败:', error)
    }
}

/**
 * 检查通知是否已读
 * @param noticeId 通知ID
 * @returns 是否已读
 */
export const isNoticeRead = (noticeId: number): boolean => {
    const readIds = getReadNoticeIds()
    return readIds.includes(noticeId)
}

/**
 * 清除所有已读状态
 */
export const clearReadStatus = (): void => {
    try {
        uni.removeStorageSync(NOTICE_READ_KEY)
    } catch (error) {
        console.error('清除已读状态失败:', error)
    }
}

/**
 * 获取未读通知数量
 * @param noticeList 通知列表
 * @returns 未读数量
 */
export const getUnreadCount = (noticeList: any[]): number => {
    return noticeList.filter(notice => !isNoticeRead(notice.id)).length
}
