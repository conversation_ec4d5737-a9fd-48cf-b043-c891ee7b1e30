<template>
	<view class="notice-list">
		<text class="page-title">公告通知</text>
		<view v-for="notice in noticeList" :key="notice.id" class="notice-item" @tap="goToDetail(notice)">
			<view class="notice-info">
				<text class="notice-title">{{ notice.title }}</text>
				<text class="notice-summary">{{ notice.summary }}</text>
				<text class="notice-date">{{ notice.date }}</text>
			</view>
			<view v-if="notice.isNew" class="new-badge">新</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const noticeList = ref([
	{
		id: '1',
		title: '关于开展安全生产培训的通知',
		summary: '为提高员工安全意识，公司将组织安全生产培训...',
		date: '2024-01-15',
		isNew: true
	},
	{
		id: '2',
		title: '数字化转型培训课程上线',
		summary: '数字化转型相关课程已上线，请各部门积极参与...',
		date: '2024-01-10',
		isNew: false
	}
])

const goToDetail = (notice: any) => {
	uni.navigateTo({
		url: `/pages/notice/detail?id=${notice.id}`
	})
}
</script>

<style lang="scss" scoped>
.notice-list {
	padding: var(--spacing-md);
}

.page-title {
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: var(--spacing-md);
}

.notice-item {
	background: var(--bg-primary);
	padding: var(--spacing-md);
	margin-bottom: var(--spacing-md);
	border-radius: var(--radius-md);
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	position: relative;
}

.notice-info {
	flex: 1;
}

.notice-title {
	display: block;
	font-size: 28rpx;
	color: var(--text-primary);
	font-weight: 600;
	margin-bottom: 8rpx;
}

.notice-summary {
	display: block;
	font-size: 24rpx;
	color: var(--text-secondary);
	margin-bottom: 8rpx;
	line-height: 1.5;
}

.notice-date {
	font-size: 24rpx;
	color: var(--text-tertiary);
}

.new-badge {
	background: #ff4d4f;
	color: #FFFFFF;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
}
</style> 