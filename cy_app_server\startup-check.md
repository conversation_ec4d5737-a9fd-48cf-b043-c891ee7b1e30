# PK功能启动检查清单

## 1. 数据库准备

### 1.1 执行PK表创建脚本
```sql
-- 执行以下SQL脚本创建PK相关表
source cy_app_server/src/main/resources/db/pk_tables.sql
```

### 1.2 验证表是否创建成功
```sql
-- 检查表是否存在
SHOW TABLES LIKE 'pk_%';

-- 应该看到以下表：
-- pk_room
-- pk_participant  
-- pk_question
-- pk_answer
-- pk_match_queue
```

## 2. 应用启动

### 2.1 启动应用
```bash
cd cy_app_server
mvn spring-boot:run
```

### 2.2 检查启动日志
查看控制台输出，确保没有以下错误：
- Bean creation failed
- Mapper not found
- Table doesn't exist

## 3. 功能验证

### 3.1 测试Mapper注入
访问：`http://localhost:8090/api/pk/mapper-test/injection`

期望结果：
```json
{
  "success": true,
  "message": "所有Mapper注入成功",
  "pkRoomMapper": "注入成功",
  "pkParticipantMapper": "注入成功",
  "pkAnswerMapper": "注入成功",
  "pkQuestionMapper": "注入成功",
  "examQuestionMapper": "注入成功"
}
```

### 3.2 测试数据库连接
访问：`http://localhost:8090/api/pk/mapper-test/database`

期望结果：
```json
{
  "success": true,
  "message": "数据库连接测试完成",
  "databaseConnection": "正常",
  "examQuestionCount": 数字
}
```

### 3.3 测试PK表
访问：`http://localhost:8090/api/pk/mapper-test/tables`

期望结果：
```json
{
  "success": true,
  "message": "PK表检查完成",
  "tableStatus": {
    "pk_room": "存在，记录数: 0",
    "pk_participant": "存在，记录数: 0",
    "pk_answer": "存在，记录数: 0",
    "pk_question": "存在，记录数: 0"
  }
}
```

### 3.4 测试PK服务
访问：`http://localhost:8090/api/pk/test/service`

期望结果：
```json
{
  "success": true,
  "message": "PK服务测试完成",
  "pkServiceAvailable": true,
  "webSocketServiceAvailable": true,
  "onlineCount": 0
}
```

## 4. 常见问题解决

### 4.1 Mapper Bean not found
**问题**: `Field pkRoomMapper required a bean of type 'PkRoomMapper' that could not be found`

**解决方案**:
1. 确保@MapperScan扫描了正确的包：`com.cy.education.repository`
2. 确保Mapper接口有@Mapper注解
3. 重启应用

### 4.2 Table doesn't exist
**问题**: `Table 'test.pk_room' doesn't exist`

**解决方案**:
1. 执行PK表创建脚本：`pk_tables.sql`
2. 检查数据库连接配置
3. 确保有创建表的权限

### 4.3 WebSocket连接失败
**问题**: WebSocket连接失败

**解决方案**:
1. 确保添加了`spring-boot-starter-websocket`依赖
2. 检查WebSocket配置类
3. 检查防火墙设置

## 5. 前端测试

### 5.1 访问PK测试页面
在uni-app中访问：`/pages/pk/test`

### 5.2 测试功能
1. 点击"测试获取题库列表"
2. 点击"测试开始匹配"  
3. 点击"测试WebSocket连接"

## 6. 完整流程测试

### 6.1 PK主页面
访问：`/pages/pk/index`

### 6.2 快速匹配流程
1. 选择题库、题数、时限
2. 点击"开始匹配"
3. 等待匹配结果

### 6.3 房间对战流程
1. 点击"房间对战"
2. 创建房间或加入房间
3. 等待对手加入
4. 双方准备后开始游戏

## 7. 日志监控

### 7.1 关键日志
- Mapper扫描日志
- WebSocket连接日志
- PK服务调用日志
- 数据库操作日志

### 7.2 错误日志
- Bean创建失败
- SQL执行错误
- WebSocket连接错误
- 业务逻辑错误

## 8. 性能监控

### 8.1 系统状态
访问：`http://localhost:8090/api/pk/test/status`

### 8.2 监控指标
- 内存使用情况
- WebSocket在线用户数
- 数据库连接状态
- 系统响应时间
