<template>
  <div class="study-analytics-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>学习数据分析</h2>
        <p class="page-description">查看学习数据统计、趋势分析和部门对比</p>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
        />
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 总体统计卡片 -->
    <div class="overview-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total-students">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats.totalStudents }}</div>
                <div class="stat-label">总学员数</div>
                <div class="stat-change positive">
                  <el-icon><CaretTop /></el-icon>
                  +{{ overviewStats.newStudents }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active-students">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats.activeStudents }}</div>
                <div class="stat-label">活跃学员</div>
                <div class="stat-change positive">
                  <el-icon><CaretTop /></el-icon>
                  +{{ overviewStats.activeGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon study-time">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ formatDuration(overviewStats.totalStudyTime) }}</div>
                <div class="stat-label">总学习时长</div>
                <div class="stat-change positive">
                  <el-icon><CaretTop /></el-icon>
                  +{{ overviewStats.timeGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completion-rate">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overviewStats.completionRate }}%</div>
                <div class="stat-label">平均完成率</div>
                <div class="stat-change positive">
                  <el-icon><CaretTop /></el-icon>
                  +{{ overviewStats.rateGrowth }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 学习趋势图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>学习时长趋势</span>
              <el-select v-model="trendPeriod" size="small" @change="updateTrendChart">
                <el-option label="最近7天" value="7d" />
                <el-option label="最近30天" value="30d" />
                <el-option label="最近90天" value="90d" />
              </el-select>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 部门对比图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>部门学习对比</span>
              <el-select v-model="comparisonMetric" size="small" @change="updateComparisonChart">
                <el-option label="学习时长" value="studyTime" />
                <el-option label="活跃学员" value="activeStudents" />
                <el-option label="完成率" value="completionRate" />
              </el-select>
            </div>
          </template>
          <div ref="comparisonChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <!-- 课程热度排行 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>热门课程排行</span>
          </template>
          <div ref="courseRankingChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 学习完成率分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>学习完成率分布</span>
          </template>
          <div ref="completionDistributionChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 部门详细统计表格 -->
    <el-card class="table-card">
      <template #header>
        <span>部门学习统计详情</span>
      </template>
      <el-table :data="departmentStats" stripe style="width: 100%">
        <el-table-column prop="departmentName" label="部门名称" width="150" />
        <el-table-column prop="totalStudents" label="总学员数" width="100" align="center" />
        <el-table-column prop="activeStudents" label="活跃学员" width="100" align="center" />
        <el-table-column prop="totalStudyTime" label="总学习时长" width="120" align="center">
          <template #default="{ row }">
            {{ formatDuration(row.totalStudyTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="averageStudyTime" label="人均学习时长" width="130" align="center">
          <template #default="{ row }">
            {{ formatDuration(row.averageStudyTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="completionRate" label="完成率" width="100" align="center">
          <template #default="{ row }">
            <el-progress
              :percentage="row.completionRate"
              :stroke-width="8"
              :show-text="false"
              :color="getProgressColor(row.completionRate)"
            />
            <span class="progress-text">{{ row.completionRate }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="热门课程" min-width="200">
          <template #default="{ row }">
            <template v-if="row.topCourses && row.topCourses.length > 0">
              <el-tag
                v-for="course in row.topCourses.slice(0, 3)"
                :key="course.courseId"
                size="small"
                class="course-tag"
              >
                {{ course.courseName }}
              </el-tag>
              <span v-if="row.topCourses.length > 3" class="more-courses">
                +{{ row.topCourses.length - 3 }}门
              </span>
            </template>
            <span v-else class="no-data">暂无数据</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 活跃学员排行 -->
    <el-card class="table-card">
      <template #header>
        <span>活跃学员排行榜</span>
      </template>
      <el-table :data="activeStudents" stripe style="width: 100%">
        <el-table-column label="排名" width="80" align="center">
          <template #default="{ $index }">
            <div class="rank-badge" :class="getRankClass($index)">
              {{ $index + 1 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="userName" label="学员姓名" width="120" />
        <el-table-column prop="departmentName" label="部门" width="120" />
        <el-table-column prop="totalStudyTime" label="学习时长" width="120" align="center">
          <template #default="{ row }">
            {{ formatDuration(row.totalStudyTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastStudyTime" label="最后学习时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastStudyTime) }}
          </template>
        </el-table-column>
        <el-table-column label="学习状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStudyStatusType(row.lastStudyTime)">
              {{ getStudyStatusText(row.lastStudyTime) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  User,
  UserFilled,
  Clock,
  TrendCharts,
  CaretTop
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getDepartmentStatistics,
  getActiveStudents,
  type DepartmentStudyStatistics
} from '@/api/study'

// 图表实例
const trendChartRef = ref<HTMLDivElement>()
const comparisonChartRef = ref<HTMLDivElement>()
const courseRankingChartRef = ref<HTMLDivElement>()
const completionDistributionChartRef = ref<HTMLDivElement>()

let trendChart: echarts.ECharts | null = null
let comparisonChart: echarts.ECharts | null = null
let courseRankingChart: echarts.ECharts | null = null
let completionDistributionChart: echarts.ECharts | null = null

// 数据状态
const loading = ref(false)
const dateRange = ref<[string, string] | null>(null)
const trendPeriod = ref('30d')
const comparisonMetric = ref('studyTime')

// 统计数据
const overviewStats = ref({
  totalStudents: 0,
  activeStudents: 0,
  totalStudyTime: 0,
  completionRate: 0,
  newStudents: 0,
  activeGrowth: 0,
  timeGrowth: 0,
  rateGrowth: 0
})

const departmentStats = ref<DepartmentStudyStatistics[]>([])
const activeStudents = ref<Array<{
  userId: number
  userName: string
  departmentName: string
  totalStudyTime: number
  lastStudyTime: string
}>>([])

// 获取部门统计数据
const fetchDepartmentStats = async () => {
  try {
    // 调用真实的部门学习统计API
    const response = await fetch('/api/study/records/department-stats', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      const data = result.data || []
      departmentStats.value = data

      // 计算总体统计
      if (data.length > 0) {
        overviewStats.value = {
          totalStudents: data.reduce((sum, dept) => sum + (dept.totalStudents || 0), 0),
          activeStudents: data.reduce((sum, dept) => sum + (dept.activeStudents || 0), 0),
          totalStudyTime: data.reduce((sum, dept) => sum + (dept.totalStudyTime || 0), 0),
          completionRate: Math.round(
            data.reduce((sum, dept) => sum + (dept.completionRate || 0), 0) / data.length
          ),
          newStudents: 0, // 新增学员数，需要后端提供
          activeGrowth: 0, // 活跃度增长，需要后端提供
          timeGrowth: 0, // 学习时长增长，需要后端提供
          rateGrowth: 0 // 完成率增长，需要后端提供
        }
      } else {
        // 没有数据时的默认值
        overviewStats.value = {
          totalStudents: 0,
          activeStudents: 0,
          totalStudyTime: 0,
          completionRate: 0,
          newStudents: 0,
          activeGrowth: 0,
          timeGrowth: 0,
          rateGrowth: 0
        }
      }
    } else {
      console.error('获取部门统计失败:', response.status)
      ElMessage.error('获取部门统计失败')
    }
  } catch (error) {
    console.error('获取部门统计失败:', error)
    ElMessage.error('获取部门统计失败')
  }
}

// 获取活跃学员数据
const fetchActiveStudents = async () => {
  try {
    const data = await getActiveStudents(20)
    activeStudents.value = data
  } catch (error) {
    console.error('获取活跃学员失败:', error)
    ElMessage.error('获取活跃学员失败')
  }
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  trendChart = echarts.init(trendChartRef.value)
  updateTrendChart()
}

// 更新趋势图表
const updateTrendChart = () => {
  if (!trendChart) return

  // 生成模拟数据
  const days = trendPeriod.value === '7d' ? 7 : trendPeriod.value === '30d' ? 30 : 90
  const dates = []
  const studyTimes = []

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString())
    studyTimes.push(Math.floor(Math.random() * 500) + 100)
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}<br/>学习时长: ${formatDuration(data.value * 60)}`
      }
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '学习时长(分钟)',
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [{
      name: '学习时长',
      type: 'line',
      data: studyTimes,
      smooth: true,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
          { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
        ])
      },
      lineStyle: {
        color: '#409EFF'
      },
      itemStyle: {
        color: '#409EFF'
      }
    }]
  }

  trendChart.setOption(option)
}

// 初始化对比图表
const initComparisonChart = () => {
  if (!comparisonChartRef.value) return

  comparisonChart = echarts.init(comparisonChartRef.value)
  updateComparisonChart()
}

// 更新对比图表
const updateComparisonChart = () => {
  if (!comparisonChart || !departmentStats.value.length) return

  const departments = departmentStats.value.map(dept => dept.departmentName)
  let data: number[] = []
  let unit = ''

  switch (comparisonMetric.value) {
    case 'studyTime':
      data = departmentStats.value.map(dept => Math.floor(dept.totalStudyTime / 3600))
      unit = '小时'
      break
    case 'activeStudents':
      data = departmentStats.value.map(dept => dept.activeStudents)
      unit = '人'
      break
    case 'completionRate':
      data = departmentStats.value.map(dept => dept.completionRate)
      unit = '%'
      break
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: departments,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: unit
    },
    series: [{
      type: 'bar',
      data: data,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#67C23A' },
          { offset: 1, color: '#85CE61' }
        ])
      }
    }]
  }

  comparisonChart.setOption(option)
}

// 初始化课程排行图表
const initCourseRankingChart = () => {
  if (!courseRankingChartRef.value) return

  courseRankingChart = echarts.init(courseRankingChartRef.value)

  // 模拟课程数据
  const courseData = [
    { name: 'Java基础教程', value: 156 },
    { name: 'Vue.js实战', value: 142 },
    { name: 'Spring Boot开发', value: 128 },
    { name: 'MySQL数据库', value: 115 },
    { name: 'Python入门', value: 98 }
  ]

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}人学习'
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      data: courseData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        formatter: '{b}\n{c}人'
      }
    }]
  }

  courseRankingChart.setOption(option)
}

// 初始化完成率分布图表
const initCompletionDistributionChart = () => {
  if (!completionDistributionChartRef.value) return

  completionDistributionChart = echarts.init(completionDistributionChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}人 ({d}%)'
    },
    series: [{
      type: 'pie',
      radius: '70%',
      data: [
        { value: 45, name: '0-20%', itemStyle: { color: '#F56C6C' } },
        { value: 32, name: '21-40%', itemStyle: { color: '#E6A23C' } },
        { value: 28, name: '41-60%', itemStyle: { color: '#409EFF' } },
        { value: 35, name: '61-80%', itemStyle: { color: '#67C23A' } },
        { value: 60, name: '81-100%', itemStyle: { color: '#909399' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  completionDistributionChart.setOption(option)
}

// 日期范围变化
const handleDateRangeChange = () => {
  // 这里可以根据日期范围重新获取数据
  refreshData()
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      fetchDepartmentStats(),
      fetchActiveStudents()
    ])

    // 更新图表
    nextTick(() => {
      updateTrendChart()
      updateComparisonChart()
    })
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds) return '0分钟'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 获取排名样式
const getRankClass = (index: number) => {
  if (index === 0) return 'rank-gold'
  if (index === 1) return 'rank-silver'
  if (index === 2) return 'rank-bronze'
  return 'rank-normal'
}

// 获取学习状态类型
const getStudyStatusType = (lastStudyTime: string) => {
  if (!lastStudyTime) return 'info'

  const now = new Date()
  const lastTime = new Date(lastStudyTime)
  const diffDays = Math.floor((now.getTime() - lastTime.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays <= 1) return 'success'
  if (diffDays <= 7) return 'warning'
  return 'danger'
}

// 获取学习状态文本
const getStudyStatusText = (lastStudyTime: string) => {
  if (!lastStudyTime) return '未学习'

  const now = new Date()
  const lastTime = new Date(lastStudyTime)
  const diffDays = Math.floor((now.getTime() - lastTime.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays <= 1) return '活跃'
  if (diffDays <= 7) return '一般'
  return '不活跃'
}

// 窗口大小变化处理
const handleResize = () => {
  trendChart?.resize()
  comparisonChart?.resize()
  courseRankingChart?.resize()
  completionDistributionChart?.resize()
}

// 组件挂载
onMounted(async () => {
  await refreshData()

  nextTick(() => {
    initTrendChart()
    initComparisonChart()
    initCourseRankingChart()
    initCompletionDistributionChart()
  })

  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  trendChart?.dispose()
  comparisonChart?.dispose()
  courseRankingChart?.dispose()
  completionDistributionChart?.dispose()

  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.study-analytics-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.overview-stats {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 28px;
  color: white;
}

.stat-icon.total-students {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active-students {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.study-time {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completion-rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 6px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-change {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
  width: 100%;
}

.table-card {
  margin-bottom: 20px;
}

.progress-text {
  margin-left: 8px;
  font-size: 12px;
  color: #606266;
}

.course-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  color: white;
}

.rank-badge.rank-gold {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #333;
}

.rank-badge.rank-silver {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  color: #333;
}

.rank-badge.rank-bronze {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
}

.rank-badge.rank-normal {
  background: #909399;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-progress-bar__outer) {
  background-color: #f0f2f5;
}

@media (max-width: 1200px) {
  .charts-row .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-start;
  }

  .overview-stats .el-col {
    margin-bottom: 16px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}
</style>
