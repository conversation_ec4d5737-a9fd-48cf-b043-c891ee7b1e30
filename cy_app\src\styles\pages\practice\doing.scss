.practice-doing {
  min-height: 100vh;
  background: #f5f6fa;
  display: flex;
  flex-direction: column;
}

// 进度指示器
.progress-section {
  padding: 16px 20px;
  background: #ffffff;
  border-bottom: 1px solid #f0f2f5;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-text {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.progress-percent {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
}

.progress-bar {
  height: 6px;
  background: #f0f2f5;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

// 题目卡片
.question-card {
  margin: 16px 20px 100px 20px; // 底部留出空间给操作栏
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  flex: 1;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.question-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-text {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

.question-score {
  display: flex;
  align-items: center;
}

.score-text {
  font-size: 14px;
  color: #fa709a;
  font-weight: 600;
}

.question-content {
  margin-bottom: 20px;
}

.question-text {
  font-size: 16px;
  color: #1a1d2e;
  line-height: 1.6;
}

// 选择题选项
.question-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8faff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.option-item:active {
  background: #f0f4ff;
}

.option-item.selected {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.option-item.correct {
  background: rgba(67, 233, 123, 0.1);
  border-color: #43e97b;
}

.option-item.wrong {
  background: rgba(245, 108, 108, 0.1);
  border-color: #f56c6c;
}

.option-selector {
  flex-shrink: 0;
}

.selector-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #e8eaed;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.option-item.selected .selector-circle {
  border-color: #667eea;
  background: #667eea;
}

.option-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-label {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
  min-width: 20px;
}

.option-text {
  font-size: 16px;
  color: #1a1d2e;
  line-height: 1.5;
}

.option-status {
  flex-shrink: 0;
}

// 判断题选项
.judgment-options {
  display: flex;
  gap: 16px;
}

.judgment-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px 16px;
  background: #f8faff;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.judgment-item:active {
  background: #f0f4ff;
}

.judgment-item.selected {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.judgment-item.correct {
  background: rgba(67, 233, 123, 0.1);
  border-color: #43e97b;
}

.judgment-item.wrong {
  background: rgba(245, 108, 108, 0.1);
  border-color: #f56c6c;
}

.judgment-icon {
  flex-shrink: 0;
}

.judgment-text {
  font-size: 16px;
  font-weight: 500;
  color: #1a1d2e;
}

// 填空题输入
.fill-input {
  margin-top: 16px;
}

// 简答题输入
.essay-input {
  margin-top: 16px;
}

.word-count {
  margin-top: 8px;
  text-align: right;
}

.count-text {
  font-size: 12px;
  color: #8e8e93;
}

// 答案解析
.answer-analysis {
  margin-top: 20px;
  padding: 16px;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 8px;
}

.analysis-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.analysis-title {
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
}

.correct-answer {
  margin-bottom: 12px;
}

.answer-label {
  font-size: 14px;
  font-weight: 600;
  color: #1a1d2e;
}

.answer-text {
  font-size: 14px;
  color: #43e97b;
  font-weight: 500;
}

.explanation {
  margin-top: 8px;
}

.explanation-text {
  font-size: 14px;
  color: #1a1d2e;
  line-height: 1.5;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 1px solid #f0f2f5;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 999;
  height: 70px;
}

.action-left,
.action-right {
  min-width: 80px;
}

.action-center {
  flex: 1;
  margin: 0 16px;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f8faff;
  border: 1px solid #e8eaed;
  border-radius: 20px;
  transition: all 0.2s ease;
}

.nav-btn:active {
  background: #f0f4ff;
  transform: scale(0.95);
}

.nav-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-text {
  font-size: 14px;
  color: #1a1d2e;
  font-weight: 500;
}

.submit-btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
}

.submit-btn.disabled {
  opacity: 0.6;
  pointer-events: none;
  background: #e8eaed;
  box-shadow: none;
}

.submit-btn .btn-text {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

// 弹窗样式
.complete-dialog,
.exit-dialog {
  width: 320px;
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
}

.dialog-header {
  padding: 24px 24px 16px;
  text-align: center;
}

.header-icon {
  margin-bottom: 12px;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 8px;
}

.dialog-subtitle {
  font-size: 14px;
  color: #8e8e93;
}

.dialog-content {
  padding: 16px 24px;
}

.dialog-message {
  font-size: 14px;
  color: #8e8e93;
  line-height: 1.5;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 16px 12px;
  background: #f8faff;
  border-radius: 8px;
  border: 1px solid #e8eaed;
}

.stat-icon {
  margin-bottom: 8px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #1a1d2e;
}

.stat-label {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  padding: 16px 24px 24px;
}

.action-btn {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.action-btn.secondary {
  background: #f8faff;
  border: 1px solid #e8eaed;
}

.action-btn.secondary:active {
  background: #f0f4ff;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.action-btn.primary:active {
  transform: scale(0.98);
}

.action-btn.danger {
  background: linear-gradient(135deg, #f56c6c 0%, #e74c3c 100%);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

.action-btn.danger:active {
  transform: scale(0.98);
}

.action-btn .btn-text {
  font-size: 14px;
  font-weight: 600;
  color: #1a1d2e;
}

.action-btn.primary .btn-text,
.action-btn.danger .btn-text {
  color: #ffffff;
}

// 响应式设计
@media (max-width: 375px) {
  .question-card {
    margin: 12px 16px;
    padding: 16px;
  }

  .option-item {
    padding: 12px;
  }

  .judgment-item {
    padding: 16px 12px;
  }

  .bottom-actions {
    padding: 10px 16px;
    height: 66px;
  }
}
