package com.cy.education.service.impl.study;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.study.StudyLog;
import com.cy.education.model.params.StudyRecordQueryParams;
import com.cy.education.repository.StudyLogMapper;
import com.cy.education.service.study.StudyLogService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 学习日志服务实现类
 */
@Service
public class StudyLogServiceImpl extends ServiceImpl<StudyLogMapper, StudyLog> implements StudyLogService {

    @Override
    public IPage<StudyLog> getStudyLogList(StudyRecordQueryParams params) {
        // 创建分页对象
        Page<StudyLog> page = new Page<>(params.getPageNum(), params.getPageSize());

        // 查询学习日志
        IPage<StudyLog> logIPage = baseMapper.selectPageWithDetails(
                page,
                params.getStudentId(),
                params.getCourseId(),
                null, // lessonId
                params.getStartTime(),
                params.getEndTime()
        );

        return logIPage;
    }

    @Override
    public StudyLog recordStudyLog(StudyLog studyLog) {
        // 保存学习日志
        save(studyLog);

        return studyLog;
    }

    @Override
    public List<Map<String, Object>> getStudyTimeTrend(Integer studentId, Integer days) {
        // 获取学习时间趋势
        return baseMapper.getStudyTimeTrend(studentId, days);
    }

    @Override
    public List<StudyLog> getCourseActivities(Integer courseId, Integer limit) {
        // 获取课程活动记录
        return baseMapper.getCourseActivities(courseId, limit);
    }
}
