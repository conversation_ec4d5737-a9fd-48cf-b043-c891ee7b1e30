<template>
  <view class="page-container">
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="backToExamList">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">考试结果</text>
        <view class="placeholder"></view>
      </view>
    </view>
    <view class="exam-result">
      <!-- 得分卡片 -->
      <view class="score-card">
        <view class="score-circle">
          <text class="score-main">{{ examResult.correctCount * singleScore }}</text>
          <text class="score-divider">/</text>
          <text class="score-total">{{ examResult.totalCount * singleScore }}</text>
        </view>
        <text class="score-label">得分</text>
      </view>
      <!-- 统计卡片 -->
      <view class="stats-row-card">
        <view class="stat-item">
          <text class="stat-value">{{ examResult.totalCount }}</text>
          <text class="stat-label">总题数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value correct">{{ examResult.correctCount }}</text>
          <text class="stat-label">答对</text>
        </view>
        <view class="stat-item">
          <text class="stat-value wrong">{{ examResult.wrongCount }}</text>
          <text class="stat-label">答错</text>
        </view>
        <view class="stat-item">
          <text class="stat-value unanswered">{{ examResult.unansweredCount }}</text>
          <text class="stat-label">未答</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ processTime(examResult.usedTime) }}</text>
          <text class="stat-label">用时</text>
        </view>
      </view>
      <!-- 答题卡区 -->
      <view class="answer-sheet-section card mx-3 mt-3">
        <view class="section-title">答题卡</view>
        <view class="answer-sheet-grid">
          <view
              v-for="(q, idx) in examResult.questions"
              :key="q.questionId"
              :class="{
              correct: q.isCorrect === true,
              wrong: q.isCorrect === false,
              unanswered: q.isCorrect === null || q.userAnswer == null || q.userAnswer === ''
            }"
              class="answer-sheet-item"
              @click="showQuestionDetail(q)"
          >
            <text class="sheet-number">{{ idx + 1 }}</text>
          </view>
        </view>
      </view>
      <!-- 题目详情弹窗 -->
      <up-popup :show="showDetail" mode="center" round="16" @close="showDetail = false">
        <view class="question-detail-popup">
          <view class="popup-header">
            <text class="popup-title">第{{ detailQuestion?.questionNumber }}题 · {{ detailQuestion?.typeName }}</text>
            <up-icon color="#8e8e93" name="close" size="20" style="position:absolute;right:16rpx;top:16rpx;"
                    @click="showDetail = false"/>
          </view>
          <view class="question-body">
            <text class="question-text strong">{{ detailQuestion?.question }}</text>
            <view v-if="detailQuestion?.options && detailQuestion.options.length > 0" class="options-list">
              <view v-for="opt in detailQuestion.options" :key="opt.key" :class="{
                  correct: detailQuestion.correctAnswer && opt.key === detailQuestion.correctAnswer,
                  selected: detailQuestion.userAnswer && (Array.isArray(detailQuestion.userAnswer) ? detailQuestion.userAnswer.includes(opt.key) : detailQuestion.userAnswer === opt.key)
                }"
                    class="option-item">
                <text class="option-key">{{ opt.key }}.</text>
                <text class="option-text">{{ opt.text }}</text>
              </view>
            </view>
            <view class="answer-info-row">
              <view class="answer-label user">你的答案：</view>
              <view
                  :class="{correct: detailQuestion.isCorrect === true, wrong: detailQuestion.isCorrect === false, unanswered: detailQuestion.isCorrect === null}"
                  class="answer-value">
                {{ detailQuestion.userAnswer || '未作答' }}
              </view>
            </view>
            <view class="answer-info-row">
              <view class="answer-label correct">正确答案：</view>
              <view class="answer-value correct">{{ detailQuestion.correctAnswer }}</view>
            </view>
            <view v-if="detailQuestion.explanation" class="explanation-area">
              <view class="explanation-icon">💡</view>
              <view class="explanation-content">{{ detailQuestion.explanation }}</view>
            </view>
          </view>
        </view>
      </up-popup>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, onMounted, computed} from 'vue'
import {type ExamResult, getExamResult, type QuestionDetail} from '@/api/exam'



const examId = ref('')
const examResult = ref<ExamResult>({
  totalCount: 0,
  correctCount: 0,
  wrongCount: 0,
  unansweredCount: 0,
  usedTime: 0,
  questions: []
})
const showDetail = ref(false)
const detailQuestion = ref<QuestionDetail | null>(null)

// 假设每题分值相同，自动推算单题分值
const singleScore = computed(() => {
  if (examResult.value.totalCount > 0 && examResult.value.questions.length > 0) {
    // 取第一题的分值，如果后端有分值字段可直接用
    return examResult.value.questions[0].score || 1
  }
  return 1
})

onMounted(async () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  examId.value = currentPage.options?.recordId || ''
  const data = await getExamResult(Number(examId.value))
  // 兼容后端未返回score字段时，补充score
  if (data.questions && data.questions.length > 0 && data.questions[0].score === undefined) {
    data.questions.forEach(q => q.score = 1)
  }
  examResult.value = data
})

const processTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

function showQuestionDetail(q: QuestionDetail) {
  detailQuestion.value = q
  showDetail.value = true
}

const retakeExam = () => {
  uni.showModal({
    title: '重新考试',
    content: '确定要重新考试吗？之前的成绩将被覆盖。',
    success: (res) => {
      if (res.confirm) {
        uni.redirectTo({
          url: `/pages/exam/taking?examId=${examId.value}`
        })
      }
    }
  })
}

const backToExamList = () => {
  uni.reLaunch({url: '/pages/exam/index'})
}
</script>

<style lang="scss" scoped>
.score-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 16rpx;
  margin: 0 24rpx 16rpx 24rpx;
  padding: 20rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.score-circle {
  display: flex;
  align-items: baseline;
  font-size: 64rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.score-main {
  font-size: 72rpx;
  font-weight: bold;
  color: rgba(61, 61, 61, 0.99);
}

.score-divider {
  font-size: 48rpx;
  margin: 0 8rpx;
  color: rgba(61, 61, 61, 0.99);
}

.score-total {
  font-size: 48rpx;
  color: rgba(61, 61, 61, 0.99);
}

.score-label {
  font-size: 28rpx;
  color: rgba(61, 61, 61, 0.99);
  margin-top: 4rpx;
}

.stats-row-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-radius: 16rpx;
  margin: 0 24rpx 16rpx 24rpx;
  padding: 20rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 80rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  margin: 2rpx 0 0 0;
}

.stat-label {
  font-size: 22rpx;
  color: #8e8e93;
}

.stat-value.correct {
  color: #43e97b;
}

.stat-value.wrong {
  color: #f56c6c;
}

.stat-value.unanswered {
  color: #bfbfbf;
}

.answer-sheet-section {
  background: #fff;
  border-radius: 16rpx;
  margin: 0 24rpx 16rpx 24rpx;
  padding: 24rpx 0 16rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-left: 24rpx;
  margin-bottom: 16rpx;
}

.answer-sheet-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: flex-start;
  margin: 0 24rpx 0 24rpx;
}

.answer-sheet-item {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  cursor: pointer;
  border: 2rpx solid transparent;
  transition: background 0.2s, border 0.2s;
}

.answer-sheet-item.correct {
  background: #e6f7ff;
  color: #43e97b;
  border-color: #43e97b;
}

.answer-sheet-item.wrong {
  background: #fff1f0;
  color: #f56c6c;
  border-color: #f56c6c;
}

.answer-sheet-item.unanswered {
  background: #f0f0f0;
  color: #bfbfbf;
  border-color: #bfbfbf;
}

.answer-sheet-item:hover,
.answer-sheet-item:active {
  box-shadow: 0 0 8rpx #4f8cff33;
  border-color: #4f8cff;
}

.sheet-number {
  z-index: 1;
}

.question-detail-popup {
  padding: 32rpx 24rpx 24rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  max-width: 90vw;
  width: 100%;
  min-width: 0;
  min-height: 200rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.popup-header {
  position: relative;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}

.popup-title {
  color: #222;
}

.question-body {
  font-size: 28rpx;
  color: #333;
}

.question-text.strong {
  font-weight: bold;
  font-size: 30rpx;
  margin-bottom: 18rpx;
  display: block;
}

.options-list {
  margin: 16rpx 0;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.option-item {
  display: flex;
  align-items: center;
  background: #f7f8fa;
  border-radius: 12rpx;
  padding: 12rpx 16rpx;
  font-size: 26rpx;
  margin-bottom: 0;
  border: 2rpx solid transparent;
  transition: background 0.2s, border 0.2s;
}

.option-item.selected {
  background: #e6f7ff;
  border-color: #4f8cff;
}

.option-item.correct {
  background: #e6ffe6;
  border-color: #43e97b;
  color: #43e97b;
}

.option-key {
  font-weight: bold;
  margin-right: 8rpx;
}

.answer-info-row {
  display: flex;
  align-items: center;
  margin: 10rpx 0 0 0;
  font-size: 26rpx;
}

.answer-label {
  min-width: 100rpx;
  font-weight: bold;
  margin-right: 8rpx;

  &.user {
    color: #4f8cff;
  }

  &.correct {
    color: #43e97b;
  }
}

.answer-value {
  font-weight: bold;

  &.correct {
    color: #43e97b;
  }

  &.wrong {
    color: #f56c6c;
  }

  &.unanswered {
    color: #bfbfbf;
  }
}

.explanation-area {
  display: flex;
  align-items: flex-start;
  background: #f6faff;
  border-radius: 10rpx;
  padding: 14rpx 16rpx;
  margin-top: 18rpx;
  font-size: 25rpx;
}

.explanation-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}

.explanation-content {
  color: #666;
  line-height: 1.6;
}
.result-actions {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  margin: 32rpx 0 0 0;
}
</style>
