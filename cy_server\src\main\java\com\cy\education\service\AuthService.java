package com.cy.education.service;

import com.cy.education.model.params.ChangePasswordParams;
import com.cy.education.model.params.LoginParams;
import com.cy.education.model.params.UpdateUserInfoParams;
import com.cy.education.model.vo.LoginResponseVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 认证服务接口
 */
public interface AuthService {
    
    /**
     * 管理员登录
     *
     * @param loginParams 登录参数
     * @return 登录响应
     */
    LoginResponseVO login(LoginParams loginParams);
    
    /**
     * 管理员登出
     *
     * @return 是否成功
     */
    boolean logout();
    
    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    LoginResponseVO.UserInfoVO getCurrentUserInfo();
    
    /**
     * 更新用户信息
     *
     * @param params 更新参数
     * @return 是否成功
     */
    boolean updateUserInfo(UpdateUserInfoParams params);
    
    /**
     * 修改密码
     *
     * @param params 修改密码参数
     * @return 是否成功
     */
    boolean changePassword(ChangePasswordParams params);
    
    /**
     * 上传头像
     *
     * @param file 头像文件
     * @return 头像URL
     */
    String uploadAvatar(MultipartFile file);

    /**
     * 根据用户ID获取用户详细信息
     * @param userId 用户ID
     * @return 用户信息
     */
    LoginResponseVO.UserInfoVO getUserDetailById(Integer userId);
} 