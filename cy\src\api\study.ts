import { get, post, put, del } from '@/utils/request'

/**
 * 学习记录接口数据类型
 */
export interface StudyRecord {
  id?: number
  userId: number
  courseId: number
  lessonId?: number
  resourceId?: number
  resourceType?: 'video' | 'file' | 'article'
  progress: number // 学习进度（百分比）
  duration: number // 学习时长（秒）
  completed: number // 是否完成：0未完成，1已完成
  lastPosition?: number // 最后学习位置（秒）
  lastStudyTime?: string
  createdAt?: string
  updatedAt?: string
  // 关联信息（非数据库字段）
  studentName?: string
  departmentName?: string
  courseName?: string
  lessonName?: string
}

/**
 * 学习记录VO
 */
export interface StudyRecordVO extends StudyRecord {
  studentName: string
  departmentName: string
  courseName: string
  lessonName?: string
}

/**
 * 学习日志
 */
export interface StudyLog {
  id?: number
  recordId?: number
  userId: number
  courseId: number
  lessonId?: number
  resourceId?: number
  resourceType?: 'video' | 'file' | 'article'
  startPosition?: number
  endPosition?: number
  duration: number
  progress: number
  completed: number
  studyTime: string
  createdAt?: string
}

/**
 * 学习统计数据
 */
export interface StudyStatistics {
  totalStudyTime: number // 总学习时长（秒）
  lastStudyTime?: string // 最后学习时间
  completedLessons: number[] // 已完成课时ID列表
  studyCount: number // 学习次数
  studyTrend: Array<{
    date: string
    duration: number
  }> // 学习趋势数据
}

/**
 * 学习记录查询参数
 */
export interface StudyRecordQueryParams {
  page?: number
  size?: number
  studentId?: number
  courseId?: number
  departmentId?: number
  startTime?: string
  endTime?: string
  completed?: number
  keyword?: string
}

/**
 * 部门学习统计
 */
export interface DepartmentStudyStatistics {
  departmentId: number
  departmentName: string
  totalStudents: number
  activeStudents: number
  totalStudyTime: number
  averageStudyTime: number
  completionRate: number
  topCourses: Array<{
    courseId: number
    courseName: string
    studyCount: number
  }>
}

/**
 * 获取用户学习记录
 */
export function getUserStudyRecords(userId: number, courseId?: number) {
  return get<StudyRecordVO[]>(`/study/records/user/${userId}`, { courseId })
}

/**
 * 获取课程学习记录
 */
export function getCourseStudyRecords(courseId: number) {
  return get<StudyRecordVO[]>(`/study/records/course/${courseId}`)
}

/**
 * 获取课时学习记录
 */
export function getLessonStudyRecords(lessonId: number) {
  return get<StudyRecordVO[]>(`/study/records/lesson/${lessonId}`)
}

/**
 * 保存学习记录
 */
export function saveStudyRecord(data: StudyRecord) {
  return post<{ id: number }>('/study/records/save', data)
}

/**
 * 获取学习记录详情
 */
export function getStudyRecordById(id: number) {
  return get<StudyRecordVO>(`/study/records/${id}`)
}

/**
 * 删除学习记录
 */
export function deleteStudyRecord(id: number) {
  return del<boolean>(`/study/records/${id}`)
}

/**
 * 获取用户课程学习进度
 */
export function getUserCourseProgress(userId: number, courseId: number) {
  return get<{
    progress: number
    completed: number
    duration: number
    lastStudyTime?: string
    completedLessons: number[]
  }>('/study/records/progress', { userId, courseId })
}

/**
 * 记录资源访问日志
 */
export function logResourceAccess(data: {
  userId: number
  courseId: number
  lessonId?: number
  resourceId: number
  resourceType: string
  duration?: number
  progress?: number
  completed?: number
}) {
  return post<boolean>('/study/records/access/log', data)
}

/**
 * 获取学习记录列表（分页）
 */
export function getStudyRecordList(params: StudyRecordQueryParams) {
  return get<{
    list: StudyRecordVO[]
    total: number
  }>('/study/records/list', params)
}

/**
 * 获取学习统计数据
 */
export function getStudyStatistics(studentId: number, courseId?: number) {
  return get<StudyStatistics>('/study/records/statistics', { studentId, courseId })
}

/**
 * 获取部门学习统计
 */
export function getDepartmentStatistics() {
  return get<DepartmentStudyStatistics[]>('/study/records/department-statistics')
}

/**
 * 获取活跃学员列表
 */
export function getActiveStudents(limit?: number) {
  return get<Array<{
    userId: number
    userName: string
    departmentName: string
    totalStudyTime: number
    lastStudyTime: string
  }>>('/study/records/active-students', { limit })
}

/**
 * 导出学习记录
 */
export function exportStudyRecords(params?: any) {
  return post<Blob>('/api/study/records/export', params, {
    responseType: 'blob',
    showLoading: true
  })
}
