/**
 * 模拟XHR请求拦截器
 * 这是一个更直接的模拟数据实现方式，直接拦截XMLHttpRequest
 */
import { delay } from './utils'
import { mockLogin, mockLogout, mockGetUserInfo } from './user'

// 模拟接口开关
const enableXHRMock = import.meta.env.VITE_ENABLE_MOCK === 'true' || false
const enableXHRMock = false
const enableUserMock = false // 禁用用户相关的模拟接口

/**
 * 保存原始XMLHttpRequest
 */
const OriginalXHR = window.XMLHttpRequest

/**
 * 模拟XHR响应
 */
interface MockResponse {
  status: number
  statusText: string
  response: any
  responseText: string
  headers: Record<string, string>
}

/**
 * 模拟请求配置
 */
interface MockConfig {
  url: string | RegExp
  method: string
  response: (config: any) => Promise<any>
}

// 全局配置数组
let mockConfigs: MockConfig[] = []

/**
 * 处理模拟数据请求
 */
async function handleMockRequest(method: string, url: string, data: any): Promise<MockResponse | null> {
  console.log('模拟XHR拦截请求:', method, url)
  
  // 统一处理URL，移除域名部分，如果有'/api'前缀，保留它用于匹配
  let path = url
  if (url.includes('//')) {
    const urlObj = new URL(url)
    path = urlObj.pathname + urlObj.search
  }
  
  console.log('处理请求路径:', path)
  
  // 缩短延迟时间
  const isLoginRequest = path.includes('/api/user/login')
  await delay(isLoginRequest ? 20 : 50)
  
  let mockResult = null
  
  try {
    // 首先尝试匹配配置数组中的接口
    for (const config of mockConfigs) {
      let isMatch = false;
      
      // 检查URL匹配
      if (typeof config.url === 'string') {
        // 精确匹配路径，避免短路径匹配长路径
        const pathWithoutQuery = path.split('?')[0]; // 移除查询参数进行匹配
        const configUrlWithoutQuery = config.url.split('?')[0];
        isMatch = pathWithoutQuery === configUrlWithoutQuery;
      } else if (config.url instanceof RegExp) {
        isMatch = config.url.test(path);
      }
      
      // 检查方法匹配
      if (isMatch && config.method.toUpperCase() === method.toUpperCase()) {
        // 构造请求配置对象
        const requestConfig = {
          url: path,
          method,
          data: typeof data === 'string' ? JSON.parse(data) : data,
          headers: {}
        };
        
        // 调用响应处理函数
        mockResult = await config.response(requestConfig);
        if (mockResult) {
          console.log('从配置中找到匹配的模拟接口:', path);
          break;
        }
      }
    }
    
    // 如果配置中没有匹配的，则尝试内置的接口
    if (!mockResult) {
      // 登录接口 - 根据enableUserMock开关决定是否拦截
      if (enableUserMock && path.includes('/api/user/login') && method === 'POST') {
        const parsedData = typeof data === 'string' ? JSON.parse(data) : data
        console.log('解析登录请求数据:', { 
          username: parsedData.username, 
          password: '******',
          remember: parsedData.remember 
        })
        mockResult = mockLogin(parsedData.username, parsedData.password)
        console.log('模拟登录响应结果:', JSON.stringify(mockResult))
      }
      // 登出接口
      else if (enableUserMock && path.includes('/api/user/logout') && method === 'POST') {
        mockResult = mockLogout()
        console.log('模拟登出响应结果:', mockResult)
      }
      // 获取用户信息接口
      else if (enableUserMock && path.includes('/api/user/info') && method === 'GET') {
        // 从localStorage获取token
        const token = localStorage.getItem('token') || ''
        console.log('获取用户信息请求, token:', token)
        mockResult = mockGetUserInfo(token)
        console.log('模拟用户信息响应结果:', mockResult)
      }
    }
    
    // 如果没有匹配的模拟接口，返回null表示不拦截
    if (!mockResult) {
      console.log('没有匹配的模拟接口，不拦截请求')
      return null
    }
    
    console.log('返回模拟数据:', JSON.stringify(mockResult))
    
    // 构造模拟响应
    const responseJSON = JSON.stringify(mockResult)
    return {
      status: 200,
      statusText: 'OK',
      response: mockResult,
      responseText: responseJSON,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  } catch (error) {
    console.error('处理模拟请求时出错:', error)
    throw error
  }
}

/**
 * 设置XHR拦截器 - 完全重写版本
 */
export function setupXHRMock() {
  console.log('启用XHR请求拦截器 - 完全重写版本')
  
  // 创建模拟XMLHttpRequest类
  class MockXHR implements XMLHttpRequest {
    // 常量定义
    static readonly UNSENT: number = 0;
    static readonly OPENED: number = 1;
    static readonly HEADERS_RECEIVED: number = 2;
    static readonly LOADING: number = 3;
    static readonly DONE: number = 4;
    
    // XMLHttpRequest 接口所需的常量
    readonly UNSENT: 0 = 0;
    readonly OPENED: 1 = 1;
    readonly HEADERS_RECEIVED: 2 = 2;
    readonly LOADING: 3 = 3;
    readonly DONE: 4 = 4;
    
    // 原始XHR属性
    public onreadystatechange: ((this: XMLHttpRequest, ev: Event) => any) | null = null
    public readyState: number = 0
    public response: any = ''
    public responseText: string = ''
    public responseType: XMLHttpRequestResponseType = ''
    public responseURL: string = ''
    public responseXML: Document | null = null
    public status: number = 0
    public statusText: string = ''
    public timeout: number = 0
    public upload: XMLHttpRequestUpload = {} as XMLHttpRequestUpload
    public withCredentials: boolean = false
    public onabort: ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null = null
    public onerror: ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null = null
    public onload: ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null = null
    public onloadend: ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null = null
    public onloadstart: ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null = null
    public onprogress: ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null = null
    public ontimeout: ((this: XMLHttpRequest, ev: ProgressEvent) => any) | null = null
    
    // 内部属性
    private originalXHR: XMLHttpRequest = new OriginalXHR()
    private method: string = ''
    private url: string = ''
    private async: boolean = true
    private requestHeaders: Record<string, string> = {}
    private requestData: any = null
    private isMocked: boolean = false
    private eventListeners: Record<string, Function[]> = {}
    private mockPromise: Promise<MockResponse | null> | null = null
    
    constructor() {
      // 代理原始XHR的事件处理
      this.originalXHR.onreadystatechange = (e: Event) => {
        if (!this.isMocked && this.originalXHR.readyState === 4) {
          // 将原始XHR的响应复制到模拟XHR
          this.readyState = this.originalXHR.readyState
          this.response = this.originalXHR.response
          this.responseText = this.originalXHR.responseText
          this.responseURL = this.originalXHR.responseURL
          this.responseXML = this.originalXHR.responseXML
          this.status = this.originalXHR.status
          this.statusText = this.originalXHR.statusText
          
          // 触发事件
          this.dispatchEvent('readystatechange', e)
        }
      }
      
      // 代理其他事件
      const events = ['load', 'error', 'timeout', 'abort', 'loadstart', 'loadend', 'progress'] as const
      events.forEach(event => {
        const eventName = `on${event}` as keyof XMLHttpRequest
        this.originalXHR[eventName] = ((e: Event) => {
          if (!this.isMocked) {
            this.dispatchEvent(event, e as ProgressEvent)
          }
        }) as any
      })
    }
    
    // 标准XHR方法
    open(method: string, url: string, async: boolean = true, username?: string, password?: string): void {
      console.log(`MockXHR.open: ${method} ${url}`, { async })
      this.method = method
      this.url = url
      this.async = async
      this.requestHeaders = {}
      this.readyState = 1
      this.dispatchEvent('readystatechange')
      
      // 也在原始XHR上调用open，以防后续需要使用
      this.originalXHR.open(method, url, async, username, password)
    }
    
    send(data?: any): void {
      console.log(`MockXHR.send 请求开始: ${this.method} ${this.url}`)
      this.requestData = data
      
      // 尝试模拟请求
      this.mockPromise = handleMockRequest(this.method, this.url, data)
      
      // 异步处理模拟请求
      setTimeout(async () => {
        try {
          const mockResponse = await this.mockPromise
          
          if (mockResponse) {
            console.log(`MockXHR 获取到模拟响应: ${this.url}`, mockResponse)
            this.isMocked = true
            
            // 设置XHR属性
            this.status = mockResponse.status
            this.statusText = mockResponse.statusText
            this.response = mockResponse.response
            this.responseText = mockResponse.responseText
            
            // 状态2: 请求已发送
            this.readyState = 2
            this.dispatchEvent('readystatechange')
            
            // 状态3: 正在接收响应
            this.readyState = 3
            this.dispatchEvent('readystatechange')
            
            // 等待一个微小的时间，模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 10))
            
            // 状态4: 响应已完成
            this.readyState = 4
            this.dispatchEvent('readystatechange')
            
            // 触发加载完成事件
            const progressEvent = this.createProgressEvent('load')
            this.dispatchEvent('load', progressEvent)
            this.dispatchEvent('loadend', progressEvent)
          } else {
            console.log(`MockXHR 没有模拟响应，使用原始XHR: ${this.url}`)
            // 如果没有模拟响应，使用原始XHR
            this.isMocked = false
            this.originalXHR.send(data)
          }
        } catch (error) {
          console.error(`MockXHR 处理请求失败: ${this.url}`, error)
          // 触发错误事件
          this.dispatchEvent('error', this.createProgressEvent('error'))
        }
      }, 0)
    }
    
    abort(): void {
      if (this.isMocked) {
        console.log(`MockXHR.abort: ${this.url}`)
        this.dispatchEvent('abort', this.createProgressEvent('abort'))
      } else {
        this.originalXHR.abort()
      }
    }
    
    setRequestHeader(name: string, value: string): void {
      console.log(`MockXHR.setRequestHeader: ${name}=${value}`)
      this.requestHeaders[name] = value
      this.originalXHR.setRequestHeader(name, value)
    }
    
    getResponseHeader(name: string): string | null {
      if (this.isMocked) {
        return null // 可以扩展以支持模拟响应头
      }
      return this.originalXHR.getResponseHeader(name)
    }
    
    getAllResponseHeaders(): string {
      if (this.isMocked) {
        return '' // 可以扩展以支持模拟响应头
      }
      return this.originalXHR.getAllResponseHeaders()
    }
    
    overrideMimeType(mime: string): void {
      this.originalXHR.overrideMimeType(mime)
    }
    
    // 事件处理
    addEventListener<K extends keyof XMLHttpRequestEventMap>(
      type: K,
      listener: (this: XMLHttpRequest, ev: XMLHttpRequestEventMap[K]) => any,
      options?: boolean | AddEventListenerOptions
    ): void;
    addEventListener(
      type: string,
      listener: EventListenerOrEventListenerObject,
      options?: boolean | AddEventListenerOptions
    ): void {
      console.log(`MockXHR.addEventListener: ${type}`)
      if (!this.eventListeners[type]) {
        this.eventListeners[type] = []
      }
      this.eventListeners[type].push(listener as Function)
      this.originalXHR.addEventListener(type, listener, options)
    }
    
    removeEventListener<K extends keyof XMLHttpRequestEventMap>(
      type: K,
      listener: (this: XMLHttpRequest, ev: XMLHttpRequestEventMap[K]) => any,
      options?: boolean | EventListenerOptions
    ): void;
    removeEventListener(
      type: string,
      listener: EventListenerOrEventListenerObject,
      options?: boolean | EventListenerOptions
    ): void {
      if (this.eventListeners[type]) {
        this.eventListeners[type] = this.eventListeners[type].filter(l => l !== listener)
      }
      this.originalXHR.removeEventListener(type, listener, options)
    }
    
    dispatchEvent(type: string, event?: Event): boolean {
      console.log(`MockXHR.dispatchEvent: ${type}`, { readyState: this.readyState })
      
      // 如果没有提供事件，创建一个新的事件
      const evt = event || new Event(type)
      
      // 触发特定的事件处理器
      if (type === 'readystatechange' && typeof this.onreadystatechange === 'function') {
        try {
          this.onreadystatechange.call(this as unknown as XMLHttpRequest, evt)
        } catch (e) {
          console.error(`调用onreadystatechange失败:`, e)
        }
      } else if (type === 'load' && typeof this.onload === 'function') {
        try {
          this.onload.call(this as unknown as XMLHttpRequest, evt as ProgressEvent)
        } catch (e) {
          console.error(`调用onload失败:`, e)
        }
      } else if (type === 'error' && typeof this.onerror === 'function') {
        try {
          this.onerror.call(this as unknown as XMLHttpRequest, evt as ProgressEvent)
        } catch (e) {
          console.error(`调用onerror失败:`, e)
        }
      } else if (type === 'timeout' && typeof this.ontimeout === 'function') {
        try {
          this.ontimeout.call(this as unknown as XMLHttpRequest, evt as ProgressEvent)
        } catch (e) {
          console.error(`调用ontimeout失败:`, e)
        }
      } else if (type === 'abort' && typeof this.onabort === 'function') {
        try {
          this.onabort.call(this as unknown as XMLHttpRequest, evt as ProgressEvent)
        } catch (e) {
          console.error(`调用onabort失败:`, e)
        }
      } else if (type === 'loadstart' && typeof this.onloadstart === 'function') {
        try {
          this.onloadstart.call(this as unknown as XMLHttpRequest, evt as ProgressEvent)
        } catch (e) {
          console.error(`调用onloadstart失败:`, e)
        }
      } else if (type === 'loadend' && typeof this.onloadend === 'function') {
        try {
          this.onloadend.call(this as unknown as XMLHttpRequest, evt as ProgressEvent)
        } catch (e) {
          console.error(`调用onloadend失败:`, e)
        }
      } else if (type === 'progress' && typeof this.onprogress === 'function') {
        try {
          this.onprogress.call(this as unknown as XMLHttpRequest, evt as ProgressEvent)
        } catch (e) {
          console.error(`调用onprogress失败:`, e)
        }
      }
      
      // 触发通过addEventListener添加的监听器
      if (this.eventListeners[type]) {
        this.eventListeners[type].forEach(listener => {
          try {
            listener.call(this as unknown as XMLHttpRequest, evt)
          } catch (e) {
            console.error(`调用事件监听器失败:`, e)
          }
        })
      }
      
      return true
    }
    
    // 创建进度事件
    private createProgressEvent(type: string): ProgressEvent {
      return new ProgressEvent(type, {
        lengthComputable: true,
        loaded: 1,
        total: 1
      })
    }
  }
  
  // 替换全局XMLHttpRequest
  window.XMLHttpRequest = MockXHR as any
  
  // 返回清理函数
  return () => {
    window.XMLHttpRequest = OriginalXHR
  }
}

/**
 * 设置模拟接口配置
 * @param configs 模拟接口配置数组
 */
export function setupMock(configs: MockConfig[]) {
  console.log('设置模拟接口配置:', configs.length, '个接口')
  mockConfigs = configs
  return setupXHRMock()
}

// 根据开关决定是否自动设置XHR拦截器
export const xhrMockDispose = enableXHRMock ? setupXHRMock() : (() => {
  console.log('XHR模拟已禁用')
  return () => {} // 返回空的清理函数
})() 