package com.cy.education.service.impl.home;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.home.Notice;
import com.cy.education.model.params.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.NoticeMapper;
import com.cy.education.service.home.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class NoticeServiceImpl implements NoticeService {

    @Autowired
    private NoticeMapper noticeMapper;

    @Override
    public PageResponse<Notice> listNotices(ContentQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<Notice> queryWrapper = new LambdaQueryWrapper<>();

        // 默认只查询已发布的公告
        queryWrapper.eq(Notice::getStatus, 1);

        // 置顶条件
        if (param.getIsTop() != null) {
            queryWrapper.eq(Notice::getIsTop, param.getIsTop());
        }

        // 重要程度条件
        if (param.getImportance() != null) {
            queryWrapper.eq(Notice::getImportance, param.getImportance());
        }

        // 关键词搜索（标题和内容）
        if (!StringUtils.isEmpty(param.getKeyword())) {
            queryWrapper.and(wrapper ->
                    wrapper.like(Notice::getTitle, param.getKeyword())
                            .or()
                            .like(Notice::getContent, param.getKeyword())
            );
        }

        // 排序（置顶优先，然后按重要程度降序，最后按发布时间降序）
        queryWrapper.orderByDesc(Notice::getIsTop, Notice::getImportance, Notice::getPublishTime);

        // 分页查询
        Page<Notice> page = new Page<>(param.getPageNum(), param.getPageSize());
        Page<Notice> resultPage = noticeMapper.selectPage(page, queryWrapper);

        // 转换为自定义分页响应对象
        return PageResponse.of(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    }

    @Override
    public Notice getNoticeById(Integer id) {
        Notice notice = noticeMapper.selectById(id);
        if (notice == null) {
            throw new BusinessException("公告不存在");
        }
        return notice;
    }
}
