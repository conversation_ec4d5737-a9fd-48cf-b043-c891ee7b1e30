package com.cy.education.model.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量添加题目到试卷的参数
 */
@Data
@ApiModel(description = "批量添加题目参数")
public class AddQuestionsParams {

    @ApiModelProperty(value = "题目列表", required = true)
    @NotNull(message = "题目列表不能为空")
    private List<ExamPaperQuestionParams> questions;
} 