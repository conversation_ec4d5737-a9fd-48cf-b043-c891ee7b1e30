<template>
  <el-dialog v-model="visible" title="积分记录" width="800px" destroy-on-close @close="handleClose">
    <div class="dialog-header">
      <span>学员：{{ student?.name }}</span>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        @change="loadData"
        style="margin-left: 20px;"
      />
    </div>
    
    <el-table :data="records" v-loading="loading" max-height="400">
      <el-table-column prop="createdAt" label="时间" width="150" />
      <el-table-column prop="points" label="积分变动" width="100">
        <template #default="{ row }">
          <span :class="row.points > 0 ? 'text-success' : 'text-danger'">
            {{ row.points > 0 ? '+' : '' }}{{ row.points }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" width="100" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column prop="balance" label="余额" width="100" />
      <el-table-column prop="operator" label="操作员" width="100" />
    </el-table>
    
    <div class="dialog-pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadData"
        @current-change="loadData"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { getPointsRecordList, type PointsRecord, type PointsQueryParams } from '@/api/points'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  student: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const records = ref<PointsRecord[]>([])
const dateRange = ref<[string, string] | null>(null)

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 监听props变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.student) {
    loadData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadData = async () => {
  if (!props.student) return
  
  loading.value = true
  try {
    const params: PointsQueryParams = {
      page: pagination.page,
      limit: pagination.limit,
      userId: props.student.id.toString()
    }
    
    if (dateRange.value) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }
    
    const response = await getPointsRecordList(params)
    records.value = response.list || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('加载积分记录失败:', error)
    ElMessage.error('加载积分记录失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  records.value = []
  dateRange.value = null
  pagination.page = 1
}
</script>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  font-weight: 600;
}

.dialog-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.text-success {
  color: #67c23a;
  font-weight: 600;
}

.text-danger {
  color: #f56c6c;
  font-weight: 600;
}
</style>
