<template>
  <view class="bind-phone-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">绑定手机</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <view class="form-section">
        <view class="form-title">绑定手机号</view>

        <!-- 手机号输入 -->
        <view class="form-item">
          <view class="form-label">手机号</view>
          <up-input
              v-model="formData.phone"
              :border="false"
              customStyle="background: #f8f9fa; border-radius: 12px; padding: 16px;"
              maxlength="11"
              placeholder="请输入手机号"
              type="number"
          ></up-input>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
          <up-button
              :disabled="!isFormValid"
              customStyle="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 12px; height: 48px;"
              text="确认绑定"
              type="primary"
              @click="handleSubmit"
          ></up-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {getVerificationCode, updateUserInfo} from '@/api/user'

export default {
  data() {
    return {
      formData: {
        phone: '',
        code: ''
      },
      countdown: 0,
      timer: null
    }
  },

  computed: {
    isPhoneValid() {
      const phoneRegex = /^1[3-9]\d{9}$/
      return phoneRegex.test(this.formData.phone)
    },

    isCodeValid() {
      return true
    },

    canSendCode() {
      return this.isPhoneValid
    },

    isFormValid() {
      return this.isPhoneValid && this.isCodeValid
    }
  },

  onUnload() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },

  methods: {
    async handleSendCode() {
      if (!this.canSendCode) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({
          title: '发送中...'
        })

        await getVerificationCode(this.formData.phone)

        uni.hideLoading()
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })

        this.startCountdown()

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: error.message || '发送失败',
          icon: 'none'
        })
      }
    },

    startCountdown() {
      this.countdown = 60
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.timer)
        }
      }, 1000)
    },

    async handleSubmit() {
      if (!this.isFormValid) {
        uni.showToast({
          title: '请完善信息',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({
          title: '绑定中...'
        })

        await updateUserInfo({
          id: uni.getStorageSync('userInfo').id,
          phone: this.formData.phone
        })

        uni.hideLoading()
        uni.showToast({
          title: '绑定成功',
          icon: 'success'
        })

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: error.message || '绑定失败',
          icon: 'none'
        })
      }
    },

    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/study/bind.scss";
</style>
