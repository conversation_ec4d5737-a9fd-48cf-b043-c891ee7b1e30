<template>
  <view class="pk-page">
    <!-- 导航栏 -->
    <up-navbar title="PK对战" :border="false" :background="navbarBg">
      <template #left>
        <up-icon name="arrow-left" size="20" color="#fff" @click="goBack"></up-icon>
      </template>
      <template #right>
        <up-icon name="setting" size="20" color="#fff" @click="goToDebug"></up-icon>
      </template>
    </up-navbar>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- PK模式选择 -->
      <view class="mode-section">
        <view class="section-title">选择对战模式</view>
        <view class="mode-cards">
          <view class="mode-card" @click="showMatchDialog = true">
            <view class="mode-icon">
              <up-icon name="search" size="32" color="#667eea"></up-icon>
            </view>
            <view class="mode-content">
              <text class="mode-title">快速匹配</text>
              <text class="mode-desc">系统自动匹配对手</text>
            </view>
          </view>
          <view class="mode-card" @click="showRoomDialog = true">
            <view class="mode-icon">
              <up-icon name="home" size="32" color="#f59e0b"></up-icon>
            </view>
            <view class="mode-content">
              <text class="mode-title">房间对战</text>
              <text class="mode-desc">创建或加入房间</text>
            </view>
          </view>
        </view>
      </view>

      <!-- PK历史 -->
      <view class="history-section">
        <view class="section-title">
          <text>对战历史</text>
          <text class="more-text" @click="goToHistory">查看更多</text>
        </view>
        <view class="history-list" v-if="historyList.length > 0">
          <view class="history-item" v-for="item in historyList" :key="item.id">
            <view class="history-left">
              <view class="history-result" :class="getResultClass(item)">
                {{ getResultText(item) }}
              </view>
              <view class="history-info">
                <text class="history-bank">{{ item.bankName || '题库' + (item.bankId || '') }}</text>
                <text class="history-time">{{ formatTime(item.joinedAt) }}</text>
              </view>
            </view>
            <view class="history-right">
              <text class="history-score">{{ item.score }}分</text>
            </view>
          </view>
        </view>
        <view class="empty-state" v-else>
          <up-icon name="file-text" size="48" color="#c7c7cc"></up-icon>
          <text class="empty-text">暂无对战记录</text>
        </view>
      </view>
    </view>

    <!-- 快速匹配对话框 -->
    <up-popup :show="showMatchDialog" mode="center" border-radius="16">
      <view class="match-dialog">
        <view class="dialog-title">快速匹配</view>
        <view class="dialog-content">
          <view class="form-item">
            <text class="form-label">选择题库</text>
            <up-picker v-model="matchForm.bankIndex" :columns="bankColumns" @confirm="onBankConfirm">
              <view class="picker-display">
                {{ matchForm.bankName || '请选择题库' }}
                <up-icon name="arrow-down" size="16" color="#c7c7cc"></up-icon>
              </view>
            </up-picker>
          </view>
          <view class="form-item">
            <text class="form-label">题目数量</text>
            <up-picker v-model="matchForm.questionCountIndex" :columns="questionCountColumns" @confirm="onQuestionCountConfirm">
              <view class="picker-display">
                {{ matchForm.questionCount }}题
                <up-icon name="arrow-down" size="16" color="#c7c7cc"></up-icon>
              </view>
            </up-picker>
          </view>
          <view class="form-item">
            <text class="form-label">时间限制</text>
            <up-picker v-model="matchForm.timeLimitIndex" :columns="timeLimitColumns" @confirm="onTimeLimitConfirm">
              <view class="picker-display">
                {{ Math.floor(matchForm.timeLimit / 60) }}分钟
                <up-icon name="arrow-down" size="16" color="#c7c7cc"></up-icon>
              </view>
            </up-picker>
          </view>
        </view>
        <view class="dialog-actions">
          <up-button text="取消" @click="showMatchDialog = false"></up-button>
          <up-button type="primary" text="开始匹配" @click="startMatch"></up-button>
        </view>
      </view>
    </up-popup>

    <!-- 房间对话框 -->
    <up-popup :show="showRoomDialog" mode="center" border-radius="16">
      <view class="room-dialog">
        <view class="dialog-title">房间对战</view>
        <view class="dialog-content">
          <view class="room-actions">
            <up-button type="primary" text="创建房间" @click="createRoom"></up-button>
            <up-button text="加入房间" @click="showJoinRoomDialog = true"></up-button>
          </view>
        </view>
        <view class="dialog-actions">
          <up-button text="取消" @click="showRoomDialog = false"></up-button>
        </view>
      </view>
    </up-popup>

    <!-- 加入房间对话框 -->
    <up-popup :show="showJoinRoomDialog" mode="center" border-radius="16">
      <view class="join-room-dialog">
        <view class="dialog-title">加入房间</view>
        <view class="dialog-content">
          <up-input v-model="roomCode" placeholder="请输入房间码" maxlength="6"></up-input>
        </view>
        <view class="dialog-actions">
          <up-button text="取消" @click="showJoinRoomDialog = false"></up-button>
          <up-button type="primary" text="加入" @click="joinRoom"></up-button>
        </view>
      </view>
    </up-popup>

    <!-- 匹配中对话框 -->
    <up-popup :show="showMatchingDialog" mode="center" border-radius="16" :closeable="false">
      <view class="matching-dialog">
        <view class="matching-content">
          <up-loading-icon mode="circle" size="48"></up-loading-icon>
          <text class="matching-text">正在匹配对手...</text>
          <text class="matching-tip">预计等待时间：{{ matchingTime }}秒</text>
        </view>
        <view class="dialog-actions">
          <up-button text="取消匹配" @click="cancelMatch"></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script>
import { getAvailableBanks } from '@/api/practice'
import { getUserPkHistory, startMatch, cancelMatch, joinRoom } from '@/api/pk'

export default {
  data() {
    return {
      navbarBg: {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      },
      showMatchDialog: false,
      showRoomDialog: false,
      showJoinRoomDialog: false,
      showMatchingDialog: false,
      matchingTime: 0,
      matchingTimer: null,
      roomCode: '',
      
      // 匹配表单
      matchForm: {
        bankId: null,
        bankName: '',
        bankIndex: [0],
        questionCount: 10,
        questionCountIndex: [0],
        timeLimit: 300,
        timeLimitIndex: [0]
      },
      
      // 选择器数据
      bankList: [],
      bankColumns: [[]],
      questionCountColumns: [[
        { text: '5题', value: 5 },
        { text: '10题', value: 10 },
        { text: '15题', value: 15 },
        { text: '20题', value: 20 }
      ]],
      timeLimitColumns: [[
        { text: '3分钟', value: 180 },
        { text: '5分钟', value: 300 },
        { text: '10分钟', value: 600 },
        { text: '15分钟', value: 900 }
      ]],
      
      // 历史记录
      historyList: [],
      
      // WebSocket连接
      websocket: null,
      userId: null
    }
  },
  
  onLoad() {
    // 获取用户ID
    const userInfo = uni.getStorageSync('userInfo')
    this.userId = userInfo?.id

    console.log('用户信息:', userInfo)
    console.log('用户ID:', this.userId)

    if (!this.userId) {
      console.warn('未获取到用户ID，可能需要重新登录')
      uni.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      })
      // 可以考虑跳转到登录页面
      // uni.navigateTo({ url: '/pages/login/index' })
      return
    }

    this.loadBankList()
    this.loadHistory()
    this.initWebSocket()
  },
  
  onUnload() {
    this.closeWebSocket()
    if (this.matchingTimer) {
      clearInterval(this.matchingTimer)
    }
  },
  
  methods: {
    goBack() {
      uni.navigateBack()
    },

    // 进入调试页面
    goToDebug() {
      uni.navigateTo({
        url: '/pages/pk/debug'
      })
    },
    
    // 加载题库列表
    async loadBankList() {
      try {
        const res = await getAvailableBanks()
        this.bankList = Array.isArray(res) ? res : (res?.list || [])
        this.bankColumns = [this.bankList.map(bank => ({
          text: bank.name,
          value: bank.id
        }))]

        if (this.bankList.length > 0) {
          this.matchForm.bankId = this.bankList[0].id
          this.matchForm.bankName = this.bankList[0].name
        }
      } catch (error) {
        console.error('加载题库失败:', error)
      }
    },
    
    // 加载历史记录
    async loadHistory() {
      try {
        console.log('PK主页面 - 开始加载历史记录，用户ID:', this.userId)
        const res = await getUserPkHistory(this.userId, 1, 5)
        console.log('PK主页面 - 历史记录响应:', res)

        // 由于request函数已经处理了响应格式，这里直接使用
        if (res && res.success === true) {
          this.historyList = Array.isArray(res.list) ? res.list : []
          console.log('PK主页面 - 历史记录加载成功，数量:', this.historyList.length)

          if (this.historyList.length === 0) {
            console.log('PK主页面 - 历史记录为空，这是正常情况')
          }
        } else {
          // 这种情况不应该发生，因为request函数会处理错误
          this.historyList = []
          console.warn('PK主页面 - 意外的响应格式:', res)
        }
      } catch (error) {
        console.error('PK主页面 - 加载历史记录异常:', error)
        this.historyList = []

        // 只在真正的网络错误时显示toast
        if (error && error.message && !error.message.includes('请求失败')) {
          uni.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none',
            duration: 2000
          })
        }
      }
    },
    
    // 选择器确认事件
    onBankConfirm(e) {
      const index = e.value[0]
      this.matchForm.bankId = this.bankList[index].id
      this.matchForm.bankName = this.bankList[index].name
    },
    
    onQuestionCountConfirm(e) {
      const index = e.value[0]
      this.matchForm.questionCount = this.questionCountColumns[0][index].value
    },
    
    onTimeLimitConfirm(e) {
      const index = e.value[0]
      this.matchForm.timeLimit = this.timeLimitColumns[0][index].value
    },
    
    // 开始匹配
    async startMatch() {
      if (!this.matchForm.bankId) {
        uni.showToast({ title: '请选择题库', icon: 'none' })
        return
      }
      
      this.showMatchDialog = false
      this.showMatchingDialog = true
      this.matchingTime = 0
      
      // 启动计时器
      this.matchingTimer = setInterval(() => {
        this.matchingTime++
      }, 1000)
      
      try {
        // 发送匹配请求
        this.sendWebSocketMessage({
          type: 'match_start',
          data: {
            bankId: this.matchForm.bankId,
            questionCount: this.matchForm.questionCount,
            timeLimit: this.matchForm.timeLimit
          }
        })
      } catch (error) {
        console.error('开始匹配失败:', error)
        this.showMatchingDialog = false
        if (this.matchingTimer) {
          clearInterval(this.matchingTimer)
        }
      }
    },
    
    // 取消匹配
    cancelMatch() {
      this.sendWebSocketMessage({
        type: 'match_cancel'
      })
      
      this.showMatchingDialog = false
      if (this.matchingTimer) {
        clearInterval(this.matchingTimer)
      }
    },
    
    // 创建房间
    createRoom() {
      // TODO: 实现创建房间功能
      uni.showToast({ title: '功能开发中', icon: 'none' })
    },
    
    // 加入房间
    async joinRoom() {
      if (!this.roomCode) {
        uni.showToast({ title: '请输入房间码', icon: 'none' })
        return
      }
      
      try {
        this.sendWebSocketMessage({
          type: 'room_join',
          data: {
            roomCode: this.roomCode
          }
        })
        
        this.showJoinRoomDialog = false
        this.roomCode = ''
      } catch (error) {
        console.error('加入房间失败:', error)
      }
    },
    
    // 查看历史
    goToHistory() {
      uni.navigateTo({
        url: '/pages/pk/history'
      })
    },
    
    // 获取结果样式
    getResultClass(item) {
      if (!item) return 'draw'

      // 根据分数判断胜负（这里需要根据实际的PK结果数据结构调整）
      if (item.isWinner === true) {
        return 'win'
      } else if (item.isWinner === false) {
        return 'lose'
      } else {
        // 如果没有胜负信息，暂时显示为平局
        return 'draw'
      }
    },

    // 获取结果文本
    getResultText(item) {
      if (!item) return '未知'

      const resultClass = this.getResultClass(item)
      const textMap = {
        'win': '胜利',
        'lose': '失败',
        'draw': '平局'
      }
      return textMap[resultClass] || '未知'
    },
    
    // 格式化时间
    formatTime(time) {
      return new Date(time).toLocaleDateString()
    },
    
    // WebSocket相关方法
    initWebSocket() {
      if (!this.userId) {
        console.warn('用户ID不存在，无法建立WebSocket连接')
        return
      }

      // 先测试后端是否可用
      this.testBackendConnection().then((isAvailable) => {
        if (isAvailable) {
          this.connectWebSocket()
        } else {
          console.warn('后端服务不可用，跳过WebSocket连接')
        }
      })
    },

    // 测试后端连接
    async testBackendConnection() {
      try {
        // 使用uni.request来测试后端是否可用
        const response = await new Promise((resolve, reject) => {
          uni.request({
            url: 'http://localhost:8090/api/pk/test/health',
            method: 'GET',
            timeout: 3000,
            success: (res) => {
              resolve(res)
            },
            fail: (error) => {
              reject(error)
            }
          })
        })

        console.log('后端健康检查响应:', response)
        return response.statusCode === 200
      } catch (error) {
        console.log('后端服务不可用:', error)
        return false
      }
    },

    // 连接WebSocket
    connectWebSocket() {
      // 根据环境配置WebSocket地址
      const baseUrl = process.env.NODE_ENV === 'development' ? 'localhost:8090' : 'your-server-domain.com'
      const wsUrl = `ws://${baseUrl}/websocket/pk/${this.userId}`

      console.log('尝试连接WebSocket:', wsUrl)

      this.websocket = uni.connectSocket({
        url: wsUrl,
        success: () => {
          console.log('WebSocket连接请求发送成功')
        },
        fail: (error) => {
          console.error('WebSocket连接请求失败:', error)
          // 延迟重连
          setTimeout(() => {
            this.connectWebSocket()
          }, 5000)
        }
      })

      this.websocket.onOpen(() => {
        console.log('WebSocket连接已建立')
        // 发送心跳包
        this.sendWebSocketMessage({
          type: 'heartbeat'
        })
      })

      this.websocket.onMessage((res) => {
        try {
          console.log('收到WebSocket消息:', res.data)
          const message = JSON.parse(res.data)
          this.handleWebSocketMessage(message)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      })

      this.websocket.onError((error) => {
        console.error('WebSocket发生错误:', error)
        // 连接错误时延迟重连
        setTimeout(() => {
          this.connectWebSocket()
        }, 5000)
      })

      this.websocket.onClose((res) => {
        console.log('WebSocket连接关闭:', res)
        // 如果不是主动关闭，则尝试重连
        if (res.code !== 1000 && res.code !== 1001) {
          setTimeout(() => {
            this.connectWebSocket()
          }, 5000)
        }
      })
    },
    
    closeWebSocket() {
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
    },
    
    sendWebSocketMessage(message) {
      if (this.websocket) {
        try {
          message.userId = this.userId
          message.timestamp = Date.now()

          const messageStr = JSON.stringify(message)
          console.log('发送WebSocket消息:', messageStr)

          this.websocket.send({
            data: messageStr,
            success: () => {
              console.log('WebSocket消息发送成功')
            },
            fail: (error) => {
              console.error('WebSocket消息发送失败:', error)
            }
          })
        } catch (error) {
          console.error('WebSocket消息发送异常:', error)
        }
      } else {
        console.warn('WebSocket未连接，无法发送消息')
      }
    },
    
    handleWebSocketMessage(message) {
      console.log('收到WebSocket消息:', message)
      
      switch (message.type) {
        case 'match_success':
          this.onMatchSuccess(message.data)
          break
        case 'match_cancel':
          this.onMatchCancel()
          break
        case 'room_join':
          this.onRoomJoin(message.data)
          break
        case 'error':
          uni.showToast({ title: message.data, icon: 'none' })
          break
      }
    },
    
    onMatchSuccess(data) {
      this.showMatchingDialog = false
      if (this.matchingTimer) {
        clearInterval(this.matchingTimer)
      }
      
      uni.showToast({ title: '匹配成功！', icon: 'success' })
      
      // 跳转到对战页面
      setTimeout(() => {
        uni.navigateTo({
          url: `/pages/pk/battle?roomId=${data.roomId}`
        })
      }, 1000)
    },
    
    onMatchCancel() {
      this.showMatchingDialog = false
      if (this.matchingTimer) {
        clearInterval(this.matchingTimer)
      }
    },
    
    onRoomJoin(data) {
      this.showRoomDialog = false
      
      // 跳转到房间页面
      uni.navigateTo({
        url: `/pages/pk/room?roomId=${data.roomId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/pk/index.scss";
</style>
