package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.ForumPost;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 论坛帖子Mapper接口
 */
@Repository
public interface ForumPostMapper extends BaseMapper<ForumPost> {
    
    /**
     * 查询帖子列表（带作者和分类信息）
     * 
     * @param page 分页对象
     * @param categoryId 分类ID
     * @param keyword 搜索关键词
     * @param status 状态
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT p.*, s.name as author, s.avatar as author_avatar, c.name as category " +
            "FROM forum_posts p " +
            "LEFT JOIN students s ON p.author_id = s.id " +
            "LEFT JOIN forum_categories c ON p.category_id = c.id " +
            "<where> " +
            "<if test='categoryId != null'> AND p.category_id = #{categoryId} </if> " +
            "<if test='keyword != null and keyword != \"\"'> AND (p.title LIKE CONCAT('%', #{keyword}, '%') OR p.content LIKE CONCAT('%', #{keyword}, '%')) </if> " +
            "<if test='status != null'> AND p.status = #{status} </if> " +
            "AND p.status != 3 " + // 不显示已删除的帖子
            "</where> " +
            "ORDER BY p.is_top DESC, p.created_at DESC" +
            "</script>")
    IPage<ForumPost> selectPostPage(Page<ForumPost> page, 
                                  @Param("categoryId") Integer categoryId,
                                  @Param("keyword") String keyword,
                                  @Param("status") Integer status);
    
    /**
     * 更新帖子回复数
     * 
     * @param postId 帖子ID
     * @return 影响行数
     */
    @Update("UPDATE forum_posts SET reply_count = (SELECT COUNT(*) FROM forum_comments WHERE post_id = #{postId} AND status = 1) WHERE id = #{postId}")
    int updateReplyCount(@Param("postId") Integer postId);
    
    /**
     * 更新帖子浏览量
     * 
     * @param postId 帖子ID
     * @return 影响行数
     */
    @Update("UPDATE forum_posts SET view_count = view_count + 1 WHERE id = #{postId}")
    int incrementViewCount(@Param("postId") Integer postId);
    
    /**
     * 更新帖子置顶状态
     * 
     * @param postId 帖子ID
     * @param isTop 是否置顶
     * @return 影响行数
     */
    @Update("UPDATE forum_posts SET is_top = #{isTop} WHERE id = #{postId}")
    int updateTopStatus(@Param("postId") Integer postId, @Param("isTop") Boolean isTop);
    
    /**
     * 更新帖子精华状态
     * 
     * @param postId 帖子ID
     * @param isEssence 是否精华
     * @return 影响行数
     */
    @Update("UPDATE forum_posts SET is_essence = #{isEssence} WHERE id = #{postId}")
    int updateEssenceStatus(@Param("postId") Integer postId, @Param("isEssence") Boolean isEssence);
    
    /**
     * 更新帖子审核状态
     * 
     * @param postId 帖子ID
     * @param status 状态
     * @return 影响行数
     */
    @Update("UPDATE forum_posts SET status = #{status} WHERE id = #{postId}")
    int updateStatus(@Param("postId") Integer postId, @Param("status") Integer status);
    
    /**
     * 假删除帖子（更新状态为已删除）
     * 
     * @param postId 帖子ID
     * @return 影响行数
     */
    @Update("UPDATE forum_posts SET status = 3 WHERE id = #{postId}")
    int softDeletePost(@Param("postId") Integer postId);
    
    /**
     * 通过ID获取帖子信息，包括已删除的帖子
     * 
     * @param postId 帖子ID
     * @return 帖子信息
     */
    @Select("SELECT p.*, s.name as author, s.avatar as author_avatar, c.name as category " +
            "FROM forum_posts p " +
            "LEFT JOIN students s ON p.author_id = s.id " +
            "LEFT JOIN forum_categories c ON p.category_id = c.id " +
            "WHERE p.id = #{postId}")
    ForumPost selectPostWithDetailsById(@Param("postId") Integer postId);
} 