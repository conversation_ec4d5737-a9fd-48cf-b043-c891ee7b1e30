package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.points.PointsProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 积分商品数据访问层
 */
@Mapper
public interface PointsProductMapper extends BaseMapper<PointsProduct> {
    /**
     * 更新商品库存（减少）
     *
     * @param id 商品ID
     * @param count 减少数量
     * @return 更新行数
     */
    @Update("UPDATE points_product SET stock = stock - #{count}, exchange_count = exchange_count + #{count} " +
            "WHERE id = #{id} AND stock >= #{count}")
    int decreaseStock(@Param("id") Integer id, @Param("count") int count);
}
