package com.cy.education.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.forum.ForumCategory;
import com.cy.education.model.entity.forum.ForumComment;
import com.cy.education.model.entity.forum.ForumPost;
import com.cy.education.model.params.ForumCategoryQueryParam;
import com.cy.education.model.params.ForumPostQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.service.forum.ForumCategoryService;
import com.cy.education.service.forum.ForumCommentService;
import com.cy.education.service.forum.ForumInteractionService;
import com.cy.education.service.forum.ForumPostService;
import com.cy.education.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 论坛管理控制器
 */
@Api(tags = "论坛管理接口")
@RestController
@RequestMapping("/forum")
@Slf4j
public class ForumController {

    @Autowired
    private ForumCategoryService forumCategoryService;

    @Autowired
    private ForumPostService forumPostService;

    @Autowired
    private ForumCommentService forumCommentService;

    @Autowired
    private ForumInteractionService forumInteractionService;

    // =========================== 论坛分类接口 ===========================

    /**
     * 获取分类列表（树形结构）
     */
    @ApiOperation("获取分类列表（树形结构）")
    @GetMapping("/category/list")
    public ApiResponse<List<ForumCategory>> getCategoryList(ForumCategoryQueryParam param) {
        try {
            List<ForumCategory> categoryList = forumCategoryService.getCategoryTree(param);
            return ApiResponse.success(categoryList);
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            return ApiResponse.error("获取分类列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取分类详情
     */
    @ApiOperation("获取分类详情")
    @GetMapping("/category/{id}")
    public ApiResponse<ForumCategory> getCategoryById(@PathVariable Integer id) {
        try {
            ForumCategory category = forumCategoryService.getCategoryById(id);
            return ApiResponse.success(category);
        } catch (Exception e) {
            log.error("获取分类详情失败", e);
            return ApiResponse.error("获取分类详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取父分类列表（用于下拉选择）
     */
    @ApiOperation("获取父分类列表")
    @GetMapping("/category/parents")
    public ApiResponse<List<ForumCategory>> getParentCategories() {
        try {
            List<ForumCategory> parentList = forumCategoryService.getParentCategories();
            return ApiResponse.success(parentList);
        } catch (Exception e) {
            log.error("获取父分类列表失败", e);
            return ApiResponse.error("获取父分类列表失败: " + e.getMessage());
        }
    }

    // =========================== 论坛帖子接口 ===========================

    /**
     * 获取帖子列表
     */
    @ApiOperation("获取帖子列表")
    @GetMapping("/post/list")
    public ApiResponse<Map<String, Object>> getPostList(ForumPostQueryParam param) {
        try {
            IPage<ForumPost> page = forumPostService.getPostPage(SecurityUtil.getCurrentUserId(), null, param);
            Map<String, Object> result = new HashMap<>();
            result.put("list", page.getRecords());
            result.put("total", page.getTotal());
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取帖子列表失败", e);
            return ApiResponse.error("获取帖子列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取帖子列表
     */
    @ApiOperation("获取用户帖子列表")
    @GetMapping("/{userId}/post/list")
    public ApiResponse<Map<String, Object>> getPostListByUserId(@PathVariable Integer userId, ForumPostQueryParam param) {
        try {
            IPage<ForumPost> page = forumPostService.getPostPage(SecurityUtil.getCurrentUserId(), userId, param);
            Map<String, Object> result = new HashMap<>();
            result.put("list", page.getRecords());
            result.put("total", page.getTotal());
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取帖子列表失败", e);
            return ApiResponse.error("获取帖子列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户收藏的帖子列表
     */
    @ApiOperation("获取用户收藏的帖子列表")
    @GetMapping("/{userId}/collect/list")
    public ApiResponse<Map<String, Object>> getCollectedPostsByUserId(@PathVariable Integer userId, ForumPostQueryParam param) {
        try {
            IPage<ForumPost> page = forumPostService.getCollectedPostsPage(userId, param);
            Map<String, Object> result = new HashMap<>();
            result.put("list", page.getRecords());
            result.put("total", page.getTotal());
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取用户收藏帖子列表失败", e);
            return ApiResponse.error("获取用户收藏帖子列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取帖子详情
     */
    @ApiOperation("获取帖子详情")
    @GetMapping("/post/{id}")
    public ApiResponse<ForumPost> getPostById(@PathVariable Integer id) {
        try {
            ForumPost post = forumPostService.getPostById(id);
            // 增加浏览量
            forumPostService.incrementViewCount(id);
            return ApiResponse.success(post);
        } catch (Exception e) {
            log.error("获取帖子详情失败", e);
            return ApiResponse.error("获取帖子详情失败: " + e.getMessage());
        }
    }

    // =========================== 论坛评论接口 ===========================

    /**
     * 获取帖子的评论列表（树形结构）
     */
    @ApiOperation("获取帖子的评论列表")
    @GetMapping("/post/{postId}/comments")
    public ApiResponse<List<ForumComment>> getPostComments(
            @PathVariable Integer postId,
            @RequestParam(defaultValue = "latest") String sortBy) {
        try {
            List<ForumComment> comments = forumCommentService.getPostComments(postId, sortBy);
            return ApiResponse.success(comments);
        } catch (Exception e) {
            log.error("获取帖子评论列表失败", e);
            return ApiResponse.error("获取帖子评论列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取评论详情
     */
    @ApiOperation("获取评论详情")
    @GetMapping("/comment/{id}")
    public ApiResponse<ForumComment> getCommentById(@PathVariable Integer id) {
        try {
            ForumComment comment = forumCommentService.getCommentById(id);
            return ApiResponse.success(comment);
        } catch (Exception e) {
            log.error("获取评论详情失败", e);
            return ApiResponse.error("获取评论详情失败: " + e.getMessage());
        }
    }

    // =========================== 用户交互接口 ===========================

    /**
     * 点赞帖子
     */
    @ApiOperation("点赞帖子")
    @PostMapping("/post/{id}/like")
    public ApiResponse<Map<String, Object>> likePost(@PathVariable Integer id) {
        try {
            boolean success = forumInteractionService.likePost(id, SecurityUtil.getCurrentUserId());
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "点赞成功" : "点赞失败");
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("点赞帖子失败", e);
            return ApiResponse.error("点赞失败: " + e.getMessage());
        }
    }

    /**
     * 取消点赞帖子
     */
    @ApiOperation("取消点赞帖子")
    @DeleteMapping("/post/{id}/like")
    public ApiResponse<Map<String, Object>> unlikePost(@PathVariable Integer id) {
        try {
            boolean success = forumInteractionService.unlikePost(id, SecurityUtil.getCurrentUserId());
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "取消点赞成功" : "取消点赞失败");
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("取消点赞帖子失败", e);
            return ApiResponse.error("取消点赞失败: " + e.getMessage());
        }
    }

    /**
     * 收藏帖子
     */
    @ApiOperation("收藏帖子")
    @PostMapping("/post/{id}/collect")
    public ApiResponse<Map<String, Object>> collectPost(@PathVariable Integer id) {
        try {
            boolean success = forumInteractionService.collectPost(id, SecurityUtil.getCurrentUserId());
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "收藏成功" : "收藏失败");
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("收藏帖子失败", e);
            return ApiResponse.error("收藏失败: " + e.getMessage());
        }
    }

    /**
     * 取消收藏帖子
     */
    @ApiOperation("取消收藏帖子")
    @DeleteMapping("/post/{id}/collect")
    public ApiResponse<Map<String, Object>> uncollectPost(@PathVariable Integer id) {
        try {
            boolean success = forumInteractionService.uncollectPost(id, SecurityUtil.getCurrentUserId());
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "取消收藏成功" : "取消收藏失败");
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("取消收藏帖子失败", e);
            return ApiResponse.error("取消收藏失败: " + e.getMessage());
        }
    }

    /**
     * 关注用户
     */
    @ApiOperation("关注用户")
    @PostMapping("/user/{id}/follow")
    public ApiResponse<Map<String, Object>> followUser(@PathVariable Integer id) {
        try {
            boolean success = forumInteractionService.followUser(id, SecurityUtil.getCurrentUserId());
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "关注成功" : "关注失败");
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("关注用户失败", e);
            return ApiResponse.error("关注失败: " + e.getMessage());
        }
    }

    /**
     * 取消关注用户
     */
    @ApiOperation("取消关注用户")
    @DeleteMapping("/user/{id}/follow")
    public ApiResponse<Map<String, Object>> unfollowUser(@PathVariable Integer id) {
        try {
            boolean success = forumInteractionService.unfollowUser(id, SecurityUtil.getCurrentUserId());
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "取消关注成功" : "取消关注失败");
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("取消关注用户失败", e);
            return ApiResponse.error("取消关注失败: " + e.getMessage());
        }
    }

    /**
     * 点赞评论
     */
    @ApiOperation("点赞评论")
    @PostMapping("/comment/{id}/like")
    public ApiResponse<Map<String, Object>> likeComment(@PathVariable Integer id) {
        try {
            boolean success = forumInteractionService.likeComment(id, SecurityUtil.getCurrentUserId());
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "点赞成功" : "点赞失败");
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("点赞评论失败", e);
            return ApiResponse.error("点赞失败: " + e.getMessage());
        }
    }

    /**
     * 取消点赞评论
     */
    @ApiOperation("取消点赞评论")
    @DeleteMapping("/comment/{id}/like")
    public ApiResponse<Map<String, Object>> unlikeComment(@PathVariable Integer id) {
        try {
            boolean success = forumInteractionService.unlikeComment(id, SecurityUtil.getCurrentUserId());
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "取消点赞成功" : "取消点赞失败");
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("取消点赞评论失败", e);
            return ApiResponse.error("取消点赞失败: " + e.getMessage());
        }
    }

    /**
     * 发表评论
     */
    @ApiOperation("发表评论")
    @PostMapping("/comment")
    public ApiResponse<ForumComment> createComment(@RequestBody Map<String, Object> param) {
        try {
            Integer postId = (Integer) param.get("postId");
            String content = (String) param.get("content");
            Integer parentId = (Integer) param.get("parentId");
            Integer replyToId = (Integer) param.get("replyToId");

            if (postId == null || content == null || content.trim().isEmpty()) {
                return ApiResponse.validateFailed("帖子ID和评论内容不能为空");
            }

            ForumComment comment = forumCommentService.createComment(postId, content, SecurityUtil.getCurrentUserId(), parentId,
                    replyToId);
            return ApiResponse.success(comment);
        } catch (Exception e) {
            log.error("发表评论失败", e);
            return ApiResponse.error("发表评论失败: " + e.getMessage());
        }
    }

    /**
     * 删除评论
     */
    @ApiOperation("删除评论")
    @DeleteMapping("/comment/{id}")
    public ApiResponse<Map<String, Object>> deleteComment(@PathVariable Integer id) {
        try {
            boolean success = forumCommentService.deleteCommentByUser(id, SecurityUtil.getCurrentUserId());
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "删除成功" : "删除失败");
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("删除评论失败", e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 创建帖子
     */
    @ApiOperation("创建帖子")
    @PostMapping("/post")
    public ApiResponse<ForumPost> createPost(@RequestBody Map<String, Object> param) {
        try {
            String title = (String) param.get("title");
            String content = (String) param.get("content");
            Integer categoryId = (Integer) param.get("categoryId");
            List<String> images = (List<String>) param.get("images");

            // 参数验证
            if (title == null || title.trim().isEmpty()) {
                return ApiResponse.validateFailed("帖子标题不能为空");
            }
            if (content == null || content.trim().isEmpty()) {
                return ApiResponse.validateFailed("帖子内容不能为空");
            }
            if (categoryId == null) {
                return ApiResponse.validateFailed("请选择帖子分类");
            }
            if (title.length() > 50) {
                return ApiResponse.validateFailed("帖子标题不能超过50个字符");
            }
            if (content.length() > 2000) {
                return ApiResponse.validateFailed("帖子内容不能超过2000个字符");
            }

            ForumPost post = forumPostService.createPost(title, content, categoryId, images, SecurityUtil.getCurrentUserId());
            return ApiResponse.success(post);
        } catch (Exception e) {
            log.error("创建帖子失败", e);
            return ApiResponse.error("创建帖子失败: " + e.getMessage());
        }
    }
}
