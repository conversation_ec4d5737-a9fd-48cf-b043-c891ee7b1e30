<template>
  <view class="change-password-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">修改密码</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <view class="form-section">
        <view class="form-title">请输入密码信息</view>

        <!-- 旧密码 -->
        <view class="form-item">
          <view class="form-label">当前密码</view>
          <up-input
              v-model="formData.oldPassword"
              :border="false"
              customStyle="background: #f8f9fa; border-radius: 12px; padding: 16px;"
              placeholder="请输入当前密码"
              type="password"
              @blur="handleBlur"
              @focus="handleFocus"
          ></up-input>
        </view>

        <!-- 新密码 -->
        <view class="form-item">
          <view class="form-label">新密码</view>
          <up-input
              v-model="formData.newPassword"
              :border="false"
              customStyle="background: #f8f9fa; border-radius: 12px; padding: 16px;"
              placeholder="请输入新密码（6-20位字符）"
              type="password"
              @blur="handleBlur"
              @focus="handleFocus"
          ></up-input>
          <view class="password-tips">
            <text class="tip-text">密码要求：</text>
            <text :class="{ active: passwordStrength.length }" class="tip-item">• 至少6位字符</text>
            <text :class="{ active: passwordStrength.hasNumber }" class="tip-item">• 包含数字</text>
            <text :class="{ active: passwordStrength.hasLetter }" class="tip-item">• 包含字母</text>
          </view>
        </view>

        <!-- 确认密码 -->
        <view class="form-item">
          <view class="form-label">确认新密码</view>
          <up-input
              v-model="formData.confirmPassword"
              :border="false"
              customStyle="background: #f8f9fa; border-radius: 12px; padding: 16px;"
              placeholder="请再次输入新密码"
              type="password"
              @blur="handleBlur"
              @focus="handleFocus"
          ></up-input>
          <view v-if="formData.confirmPassword && !isPasswordMatch" class="error-tip">
            <up-icon color="#ff6b6b" name="error-circle" size="14"></up-icon>
            <text class="error-text">两次输入的密码不一致</text>
          </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
          <up-button
              :disabled="!isFormValid"
              customStyle="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 12px; height: 48px;"
              text="确认修改"
              type="primary"
              @click="handleSubmit"
          ></up-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {changePassword} from '@/api/user'

export default {
  data() {
    return {
      formData: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordStrength: {
        length: false,
        hasNumber: false,
        hasLetter: false
      }
    }
  },

  computed: {
    // 检查密码强度
    isPasswordValid() {
      return this.passwordStrength.length &&
          this.passwordStrength.hasNumber &&
          this.passwordStrength.hasLetter
    },

    // 检查密码匹配
    isPasswordMatch() {
      return this.formData.newPassword === this.formData.confirmPassword
    },

    // 表单验证
    isFormValid() {
      return this.formData.oldPassword &&
          this.isPasswordValid &&
          this.isPasswordMatch
    }
  },

  watch: {
    // 监听新密码变化，检查密码强度
    'formData.newPassword'(newPassword) {
      this.checkPasswordStrength(newPassword)
    }
  },

  methods: {
    // 检查密码强度
    checkPasswordStrength(password) {
      this.passwordStrength = {
        length: password.length >= 6,
        hasNumber: /\d/.test(password),
        hasLetter: /[a-zA-Z]/.test(password)
      }
    },

    // 输入框聚焦
    handleFocus() {
      // 可以添加聚焦时的样式变化
    },

    // 输入框失焦
    handleBlur() {
      // 可以添加失焦时的验证
    },

    // 提交表单
    async handleSubmit() {
      if (!this.isFormValid) {
        uni.showToast({
          title: '请完善密码信息',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({
          title: '修改中...'
        })

        await changePassword(this.formData.oldPassword, this.formData.newPassword)

        uni.hideLoading()
        uni.showToast({
          title: '密码修改成功',
          icon: 'success'
        })

        // 清除信息 重新登录
        setTimeout(() => {
          uni.clearStorageSync()
          uni.reLaunch({
            url: '/pages/login/index'
          })
        }, 1500)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: error.message || '密码修改失败',
          icon: 'none'
        })
      }
    },

    // 返回上一页
    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.change-password-container {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.content-container {
  padding: 20px;
}

.form-section {
  background: #fff;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.form-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 24px;
  text-align: center;
}

.form-item {
  margin-bottom: 24px;
}

.form-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.password-tips {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.tip-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.tip-item {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 4px;
  transition: color 0.3s ease;

  &.active {
    color: #43e97b;
  }
}

.error-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #fff5f5;
  border-radius: 6px;
  border-left: 3px solid #ff6b6b;
}

.error-text {
  font-size: 12px;
  color: #ff6b6b;
}

.submit-section {
  margin-top: 32px;
}

</style>
