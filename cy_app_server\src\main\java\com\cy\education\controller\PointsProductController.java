package com.cy.education.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.points.PointsProduct;
import com.cy.education.model.params.PointsProductQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.points.PointsProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 积分商品控制器
 */
@Api(tags = "积分商品")
@RestController
@RequestMapping("/product")
public class PointsProductController {

    @Autowired
    private PointsProductService pointsProductService;

    /**
     * 获取积分商品列表
     */
    @ApiOperation("获取积分商品列表")
    @GetMapping("/list")
    public ApiResponse<PageResponse<PointsProduct>> getProductList(PointsProductQueryParam param) {
        IPage<PointsProduct> page = pointsProductService.page(param);
        return ApiResponse.success(PageResponse.from(page));
    }

    /**
     * 获取积分商品详情
     */
    @ApiOperation("获取积分商品详情")
    @GetMapping("/{id}")
    public ApiResponse<PointsProduct> getProductDetail(@PathVariable Integer id) {
        PointsProduct product = pointsProductService.getById(id);
        return ApiResponse.success(product);
    }
}
