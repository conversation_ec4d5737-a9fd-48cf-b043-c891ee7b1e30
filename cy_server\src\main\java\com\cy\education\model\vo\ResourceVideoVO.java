package com.cy.education.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 视频资源VO
 */
@Data
public class ResourceVideoVO {
    
    /**
     * 视频ID
     */
    private Integer id;
    
    /**
     * 视频名称
     */
    private String name;
    
    /**
     * 视频时长(秒)
     */
    private Integer duration;
    
    /**
     * 文件大小(字节)
     */
    private Long size;
    
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * 观看次数
     */
    private Integer viewCount;
    
    /**
     * 视频URL
     */
    private String url;
    
    /**
     * 封面URL
     */
    private String coverUrl;
    
    /**
     * 视频描述
     */
    private String description;
    
    /**
     * 上传用户ID
     */
    private Integer uploadUserId;
    
    /**
     * 上传用户名称
     */
    private String uploadUserName;
    
    /**
     * 标签列表
     */
    private List<String> tags;
} 