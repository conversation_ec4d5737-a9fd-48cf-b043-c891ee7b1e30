<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.PracticeStatisticsMapper">

    <!-- 获取学员练习统计列表 -->
    <select id="selectPracticeStatistics" resultType="com.cy.education.model.vo.PracticeStatisticsVO">
        SELECT 
            ps.user_id,
            s.name as student_name,
            d.name as department_name,
            ps.bank_id,
            eb.name as bank_name,
            ps.total_questions,
            ps.answered_questions,
            ps.correct_count,
            ps.wrong_count,
            ps.accuracy_rate,
            ps.last_practice_time,
            COUNT(pr.id) as practice_count,
            COALESCE(SUM(TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)), 0) as total_practice_minutes
        FROM practice_stats ps
        LEFT JOIN students s ON ps.user_id = s.id
        LEFT JOIN departments d ON s.department_id = d.id
        LEFT JOIN exam_bank eb ON ps.bank_id = eb.id
        LEFT JOIN practice_record pr ON ps.user_id = pr.user_id AND ps.bank_id = pr.bank_id AND pr.status = 'completed'
        <where>
            <if test="studentName != null and studentName != ''">
                AND s.name LIKE CONCAT('%', #{studentName}, '%')
            </if>
            <if test="bankId != null">
                AND ps.bank_id = #{bankId}
            </if>
            <if test="bankName != null and bankName != ''">
                AND eb.name LIKE CONCAT('%', #{bankName}, '%')
            </if>
            <if test="departmentId != null">
                AND s.department_id = #{departmentId}
            </if>
            <if test="startTime != null and startTime != ''">
                AND ps.last_practice_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND ps.last_practice_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY ps.user_id, ps.bank_id
        ORDER BY ps.last_practice_time DESC
    </select>

    <!-- 获取学员练习统计详情 -->
    <select id="selectPracticeStatisticsDetail" resultType="java.util.HashMap">
        SELECT 
            ps.*,
            s.name as student_name,
            d.name as department_name,
            eb.name as bank_name,
            COUNT(pr.id) as practice_count,
            COALESCE(SUM(TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)), 0) as total_practice_minutes
        FROM practice_stats ps
        LEFT JOIN students s ON ps.user_id = s.id
        LEFT JOIN departments d ON s.department_id = d.id
        LEFT JOIN exam_bank eb ON ps.bank_id = eb.id
        LEFT JOIN practice_record pr ON ps.user_id = pr.user_id AND ps.bank_id = pr.bank_id AND pr.status = 'completed'
        WHERE ps.user_id = #{userId} AND ps.bank_id = #{bankId}
        GROUP BY ps.user_id, ps.bank_id
    </select>

    <!-- 获取题库练习统计概览 -->
    <select id="selectBankPracticeOverview" resultType="java.util.HashMap">
        SELECT 
            eb.name as bank_name,
            eb.description as bank_description,
            COUNT(DISTINCT ps.user_id) as student_count,
            SUM(ps.answered_questions) as total_answered,
            SUM(ps.correct_count) as total_correct,
            SUM(ps.wrong_count) as total_wrong,
            AVG(ps.accuracy_rate) as avg_accuracy_rate,
            COUNT(pr.id) as total_practice_count,
            COALESCE(SUM(TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)), 0) as total_practice_minutes
        FROM exam_bank eb
        LEFT JOIN practice_stats ps ON eb.id = ps.bank_id
        LEFT JOIN practice_record pr ON ps.user_id = pr.user_id AND ps.bank_id = pr.bank_id AND pr.status = 'completed'
        WHERE eb.id = #{bankId}
        GROUP BY eb.id
    </select>

    <!-- 获取全部练习统计概览 -->
    <select id="selectAllPracticeOverview" resultType="java.util.HashMap">
        SELECT 
            COUNT(DISTINCT ps.user_id) as total_students,
            COUNT(DISTINCT ps.bank_id) as total_banks,
            SUM(ps.answered_questions) as total_answered,
            SUM(ps.correct_count) as total_correct,
            SUM(ps.wrong_count) as total_wrong,
            AVG(ps.accuracy_rate) as avg_accuracy_rate,
            COUNT(pr.id) as total_practice_count,
            COALESCE(SUM(TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)), 0) as total_practice_minutes
        FROM practice_stats ps
        LEFT JOIN practice_record pr ON ps.user_id = pr.user_id AND ps.bank_id = pr.bank_id AND pr.status = 'completed'
    </select>

    <!-- 获取学员练习记录 -->
    <select id="selectStudentPracticeRecords" resultType="java.util.HashMap">
        SELECT 
            pr.*,
            eb.name as bank_name,
            TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time) as practice_minutes
        FROM practice_record pr
        LEFT JOIN exam_bank eb ON pr.bank_id = eb.id
        WHERE pr.user_id = #{userId}
        <if test="bankId != null">
            AND pr.bank_id = #{bankId}
        </if>
        ORDER BY pr.created_at DESC
    </select>

</mapper> 