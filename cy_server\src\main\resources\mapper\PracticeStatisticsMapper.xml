<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.PracticeStatisticsMapper">

    <!-- 获取学员练习统计列表 -->
    <select id="selectPracticeStatistics" resultType="com.cy.education.model.vo.PracticeStatisticsVO">
        SELECT
            ps.user_id as userId,
            s.name as studentName,
            s.student_number as studentNumber,
            d.name as departmentName,
            ps.bank_id as bankId,
            eb.name as bankName,
            ps.total_questions as totalQuestions,
            ps.answered_questions as answeredQuestions,
            ps.correct_count as correctCount,
            ps.wrong_count as wrongCount,
            ps.accuracy_rate as accuracyRate,
            ps.last_practice_time as lastPracticeTime,
            COUNT(pr.id) as practiceCount,
            COALESCE(SUM(TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)), 0) as totalPracticeMinutes,
            CASE
                WHEN COUNT(pr.id) > 0
                THEN ROUND(COALESCE(SUM(TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)), 0) / COUNT(pr.id), 2)
                ELSE 0
            END as avgPracticeMinutes,
            COALESCE(wrong_counts.wrong_question_count, 0) as wrongQuestionCount,
            CASE
                WHEN ps.total_questions > 0
                THEN ROUND((ps.answered_questions * 100.0 / ps.total_questions), 2)
                ELSE 0
            END as progressRate,
            ps.created_at as createdAt,
            ps.updated_at as updatedAt
        FROM practice_stats_complete_view ps
        LEFT JOIN students s ON ps.user_id = s.id
        LEFT JOIN departments d ON s.department_id = d.id
        LEFT JOIN exam_bank eb ON ps.bank_id = eb.id
        LEFT JOIN practice_record pr ON ps.user_id = pr.user_id AND ps.bank_id = pr.bank_id AND pr.status = 'completed'
        LEFT JOIN (
            SELECT
                pr.user_id,
                eq.bank_id,
                COUNT(DISTINCT pa.question_id) as wrong_question_count
            FROM practice_answer pa
            JOIN practice_record pr ON pa.record_id = pr.id
            JOIN exam_question eq ON pa.question_id = eq.id
            WHERE pa.is_correct = 0
            GROUP BY pr.user_id, eq.bank_id
        ) wrong_counts ON ps.user_id = wrong_counts.user_id AND ps.bank_id = wrong_counts.bank_id
        <where>
            <if test="studentName != null and studentName != ''">
                AND s.name LIKE CONCAT('%', #{studentName}, '%')
            </if>
            <if test="studentNumber != null and studentNumber != ''">
                AND s.student_number LIKE CONCAT('%', #{studentNumber}, '%')
            </if>
            <if test="bankId != null">
                AND ps.bank_id = #{bankId}
            </if>
            <if test="bankName != null and bankName != ''">
                AND eb.name LIKE CONCAT('%', #{bankName}, '%')
            </if>
            <if test="departmentId != null">
                AND s.department_id = #{departmentId}
            </if>
            <if test="minAccuracyRate != null">
                AND ps.accuracy_rate >= #{minAccuracyRate}
            </if>
            <if test="maxAccuracyRate != null">
                AND ps.accuracy_rate &lt;= #{maxAccuracyRate}
            </if>
            <if test="startTime != null and startTime != ''">
                AND ps.last_practice_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND ps.last_practice_time &lt;= #{endTime}
            </if>
            <if test="onlyWithRecords != null and onlyWithRecords == true">
                AND ps.answered_questions > 0
            </if>
        </where>
        GROUP BY ps.user_id, ps.bank_id
        <choose>
            <when test="sortField != null and sortField != ''">
                ORDER BY ${sortField}
                <if test="sortOrder != null and sortOrder == 'desc'">DESC</if>
                <if test="sortOrder == null or sortOrder != 'desc'">ASC</if>
            </when>
            <otherwise>
                ORDER BY ps.last_practice_time DESC, s.name ASC
            </otherwise>
        </choose>
        <if test="page != null and size != null">
            LIMIT #{size} OFFSET #{page}
        </if>
    </select>

    <!-- 获取学员练习统计详情 -->
    <select id="selectPracticeStatisticsDetail" resultType="java.util.HashMap">
        SELECT
            ps.user_id as userId,
            s.name as studentName,
            s.student_number as studentNumber,
            d.name as departmentName,
            ps.bank_id as bankId,
            eb.name as bankName,
            ps.total_questions as totalQuestions,
            ps.answered_questions as answeredQuestions,
            ps.correct_count as correctCount,
            ps.wrong_count as wrongCount,
            ps.accuracy_rate as accuracyRate,
            ps.last_practice_time as lastPracticeTime,
            COUNT(pr.id) as practiceCount,
            COALESCE(SUM(TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)), 0) as totalPracticeMinutes
        FROM practice_stats_complete_view ps
        LEFT JOIN students s ON ps.user_id = s.id
        LEFT JOIN departments d ON s.department_id = d.id
        LEFT JOIN exam_bank eb ON ps.bank_id = eb.id
        LEFT JOIN practice_record pr ON ps.user_id = pr.user_id AND ps.bank_id = pr.bank_id AND pr.status = 'completed'
        WHERE ps.user_id = #{userId} AND ps.bank_id = #{bankId}
        GROUP BY ps.user_id, ps.bank_id
    </select>

    <!-- 获取题库练习统计概览 -->
    <select id="selectBankPracticeOverview" resultType="java.util.HashMap">
        SELECT
            eb.id as bankId,
            eb.name as bankName,
            eb.description as bankDescription,
            (SELECT COUNT(*) FROM exam_question eq WHERE eq.bank_id = eb.id AND eq.deleted = 0) as totalQuestions,
            COUNT(DISTINCT ps.user_id) as studentCount,
            SUM(ps.answered_questions) as totalAnswered,
            SUM(ps.correct_count) as totalCorrect,
            SUM(ps.wrong_count) as totalWrong,
            AVG(ps.accuracy_rate) as avgAccuracyRate,
            COUNT(pr.id) as totalPracticeCount,
            COALESCE(SUM(TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)), 0) as totalPracticeMinutes
        FROM exam_bank eb
        LEFT JOIN practice_stats_complete_view ps ON eb.id = ps.bank_id
        LEFT JOIN practice_record pr ON ps.user_id = pr.user_id AND ps.bank_id = pr.bank_id AND pr.status = 'completed'
        WHERE eb.id = #{bankId}
        GROUP BY eb.id
    </select>

    <!-- 获取全部练习统计概览 -->
    <select id="selectAllPracticeOverview" resultType="java.util.HashMap">
        SELECT
            COUNT(DISTINCT s.id) as totalStudents,
            COUNT(DISTINCT eb.id) as totalBanks,
            SUM(COALESCE(eq_counts.total_questions, 0)) as totalQuestions,
            SUM(COALESCE(ps.answered_questions, 0)) as totalAnsweredQuestions,
            SUM(COALESCE(ps.correct_count, 0)) as totalCorrectCount,
            SUM(COALESCE(ps.wrong_count, 0)) as totalWrongCount,
            CASE
                WHEN SUM(COALESCE(ps.answered_questions, 0)) > 0
                THEN ROUND(SUM(COALESCE(ps.correct_count, 0)) * 100.0 / SUM(COALESCE(ps.answered_questions, 0)), 2)
                ELSE 0
            END as overallAccuracyRate,
            COUNT(pr.id) as totalPracticeRecords,
            COALESCE(SUM(TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)), 0) as totalPracticeMinutes
        FROM students s
        CROSS JOIN exam_bank eb
        LEFT JOIN (
            SELECT bank_id, COUNT(*) as total_questions
            FROM exam_question
            WHERE deleted = 0
            GROUP BY bank_id
        ) eq_counts ON eb.id = eq_counts.bank_id
        LEFT JOIN practice_stats_complete_view ps ON s.id = ps.user_id AND eb.id = ps.bank_id
        LEFT JOIN practice_record pr ON ps.user_id = pr.user_id AND ps.bank_id = pr.bank_id AND pr.status = 'completed'
        WHERE eb.is_practice_enabled = 1 AND eb.deleted = 0 AND s.deleted = 0
    </select>

    <!-- 获取学员练习记录 -->
    <select id="selectStudentPracticeRecords" resultType="java.util.HashMap">
        SELECT
            pr.id,
            pr.type,
            pr.total_questions as totalQuestions,
            pr.answered_questions as answeredQuestions,
            pr.correct_count as correctCount,
            pr.wrong_count as wrongCount,
            pr.score,
            pr.status,
            pr.start_time as startTime,
            pr.end_time as endTime,
            pr.created_at as createdAt,
            eb.name as bankName,
            CASE
                WHEN pr.start_time IS NOT NULL AND pr.end_time IS NOT NULL
                THEN TIMESTAMPDIFF(MINUTE, pr.start_time, pr.end_time)
                ELSE 0
            END as practiceMinutes
        FROM practice_record pr
        LEFT JOIN exam_bank eb ON pr.bank_id = eb.id
        WHERE pr.user_id = #{userId}
        <if test="bankId != null">
            AND pr.bank_id = #{bankId}
        </if>
        ORDER BY pr.created_at DESC
    </select>

</mapper> 