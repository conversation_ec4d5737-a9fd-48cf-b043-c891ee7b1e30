import {get, post, put, del} from '@/utils/request'

// =========================== 通用接口 ===========================
export interface PageQuery {
    pageNum?: number;
    pageSize?: number;
    status?: number;
    keyword?: string;
}

export interface PageResponse<T> {
    list: T[];
    total: number;
    pageNum: number;
    pageSize: number;
}

// =========================== 论坛分类接口 ===========================
export interface ForumCategory {
    id: number;
    name: string;
    description?: string;
    parentId?: number;
    sort: number;
    status: number;
    createTime: string;
    updateTime?: string;
    children?: ForumCategory[];
}

export interface ForumCategoryQueryParam extends PageQuery {
    parentId?: number;
    name?: string;
}

// =========================== 论坛帖子接口 ===========================
export interface ForumPost {
    id: number;
    title: string;
    content: string;
    authorId: number;
    authorName: string;
    authorAvatar?: string;
    categoryId: number;
    categoryName?: string;
    isTop: boolean;
    isEssence: boolean;
    isHot: boolean;
    likeCount: number;
    commentCount: number;
    viewCount: number;
    isLiked?: boolean;
    isCollected?: boolean;
    isFollowing?: boolean;
    images?: string[];
    tags?: string[];
    status: number;
    createTime: string;
    updateTime?: string;
}

// 帖子详情接口（包含更多信息）
export interface ForumPostDetail extends ForumPost {
    author: {
        id: number;
        name: string;
        avatar?: string;
        title?: string;
        isFollowing?: boolean;
    };
    category: {
        id: number;
        name: string;
        color?: string;
    };
    shareCount: number;
    collectCount: number;
}

export interface ForumPostQueryParam extends PageQuery {
    categoryId?: number;
    categoryIds?: number[];
    authorId?: number;
    isTop?: boolean;
    isEssence?: boolean;
    sortBy?: 'latest' | 'hot' | 'essence';
}

// =========================== 论坛评论接口 ===========================
export interface ForumComment {
    id: number;
    content: string;
    postId: number;
    authorId: number;
    authorName: string;
    authorAvatar?: string;
    parentId?: number;
    replyToId?: number;
    replyToName?: string;
    likeCount: number;
    isLiked?: boolean;
    status: number;
    createTime: string;
    updateTime?: string;
    children?: ForumComment[];
    replies?: ForumComment[];
}

// 评论创建参数
export interface CreateCommentParam {
    postId: number;
    content: string;
    parentId?: number;
    replyToId?: number;
}

export interface ForumCommentQueryParam extends PageQuery {
    postId?: number;
    parentId?: number;
    authorId?: number;
    sortBy?: 'time' | 'hot';
}

// =========================== API 函数 ===========================

// 分类相关API
export const getCategoryList = (params?: ForumCategoryQueryParam) =>
    get<ForumCategory[]>('/forum/category/list', params)

export const getCategoryById = (id: number) =>
    get<ForumCategory>(`/forum/category/${id}`)

export const getParentCategories = () =>
    get<ForumCategory[]>('/forum/category/parents')

// 帖子相关API
export const getPostList = (params: ForumPostQueryParam) =>
    get<PageResponse<ForumPost>>('/forum/post/list', params)

export const getPostListByUserId = (userId: number, params: ForumPostQueryParam) =>
    get<PageResponse<ForumPost>>(`/forum/${userId}/post/list`, params)

export const getCollectedPostsByUserId = (userId: number, params: ForumPostQueryParam) =>
    get<PageResponse<ForumPost>>(`/forum/${userId}/collect/list`, params)

export const getPostById = (id: number) =>
    get<ForumPostDetail>(`/forum/post/${id}`)

// 帖子操作API
export const likePost = (postId: number) =>
    post(`/forum/post/${postId}/like`)

export const unlikePost = (postId: number) =>
    del(`/forum/post/${postId}/unlike`)

export const collectPost = (postId: number) =>
    post(`/forum/post/${postId}/collect`)

export const uncollectPost = (postId: number) =>
    del(`/forum/post/${postId}/collect`)

export const followUser = (userId: number) =>
    post(`/forum/user/${userId}/follow`)

export const unfollowUser = (userId: number) =>
    del(`/forum/user/${userId}/follow`)

// 评论相关API
export const getPostComments = (postId: number, params?: { sortBy?: string }) =>
    get<ForumComment[]>(`/forum/post/${postId}/comments`, params)

export const getCommentById = (id: number) =>
    get<ForumComment>(`/forum/comment/${id}`)

export const createComment = (params: CreateCommentParam) =>
    post<ForumComment>('/forum/comment', params)

export const likeComment = (commentId: number) =>
    post(`/forum/comment/${commentId}/like`)

export const unlikeComment = (commentId: number) =>
    del(`/forum/comment/${commentId}/like`)

export const deleteComment = (commentId: number) =>
    del(`/forum/comment/${commentId}`)


// 帖子创建API
export const createPost = (params: any) =>
    post<ForumPost>('/forum/post', params)
