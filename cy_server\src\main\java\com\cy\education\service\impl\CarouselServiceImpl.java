package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.Carousel;
import com.cy.education.model.vo.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.CarouselMapper;
import com.cy.education.service.CarouselService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 轮播图服务实现
 */
@Service
public class CarouselServiceImpl implements CarouselService {

    @Autowired
    private CarouselMapper carouselMapper;

    @Override
    public PageResponse<Carousel> listCarousels(ContentQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<Carousel> queryWrapper = new LambdaQueryWrapper<>();
        
        // 状态条件
        if (param.getStatus() != null) {
            queryWrapper.eq(Carousel::getStatus, param.getStatus());
        }
        
        // 关键词搜索
        if (!StringUtils.isEmpty(param.getKeyword())) {
            queryWrapper.like(Carousel::getTitle, param.getKeyword());
        }
        
        // 排序
        queryWrapper.orderByAsc(Carousel::getSort).orderByDesc(Carousel::getCreatedAt);
        
        // 分页查询
        Page<Carousel> page = new Page<>(param.getPageNum(), param.getPageSize());
        Page<Carousel> resultPage = carouselMapper.selectPage(page, queryWrapper);
        
        // 转换为自定义分页响应对象
        return PageResponse.of(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    }

    @Override
    public Carousel getCarouselById(Integer id) {
        Carousel carousel = carouselMapper.selectById(id);
        if (carousel == null) {
            throw new BusinessException("轮播图不存在");
        }
        return carousel;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addCarousel(Carousel carousel) {
        // 设置默认值
        if (carousel.getSort() == null) {
            carousel.setSort(0);
        }
        if (carousel.getStatus() == null) {
            carousel.setStatus(1);
        }
        
        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        carousel.setCreatedAt(now);
        carousel.setUpdatedAt(now);
        
        int result = carouselMapper.insert(carousel);
        if (result <= 0) {
            throw new BusinessException("添加轮播图失败");
        }
        
        return carousel.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCarousel(Carousel carousel) {
        // 检查轮播图是否存在
        Carousel existingCarousel = carouselMapper.selectById(carousel.getId());
        if (existingCarousel == null) {
            throw new BusinessException("轮播图不存在");
        }
        
        // 设置更新时间
        carousel.setUpdatedAt(LocalDateTime.now());
        
        return carouselMapper.updateById(carousel) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCarousel(Integer id) {
        // 检查轮播图是否存在
        Carousel carousel = carouselMapper.selectById(id);
        if (carousel == null) {
            throw new BusinessException("轮播图不存在");
        }
        
        return carouselMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCarouselStatus(Integer id, Integer status) {
        // 检查轮播图是否存在
        Carousel carousel = carouselMapper.selectById(id);
        if (carousel == null) {
            throw new BusinessException("轮播图不存在");
        }
        
        // 检查状态是否合法
        if (status != 0 && status != 1) {
            throw new BusinessException("状态值不合法，应为0或1");
        }
        
        // 创建更新对象
        Carousel updateCarousel = new Carousel();
        updateCarousel.setId(id);
        updateCarousel.setStatus(status);
        updateCarousel.setUpdatedAt(LocalDateTime.now());
        
        return carouselMapper.updateById(updateCarousel) > 0;
    }
} 