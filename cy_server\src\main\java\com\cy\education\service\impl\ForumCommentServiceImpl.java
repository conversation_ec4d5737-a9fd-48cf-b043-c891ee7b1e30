package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.ForumComment;
import com.cy.education.model.vo.ForumCommentQueryParam;
import com.cy.education.repository.ForumCommentMapper;
import com.cy.education.repository.ForumPostMapper;
import com.cy.education.service.ForumCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 论坛评论服务实现
 */
@Service
public class ForumCommentServiceImpl implements ForumCommentService {

    @Autowired
    private ForumCommentMapper forumCommentMapper;
    
    @Autowired
    private ForumPostMapper forumPostMapper;
    
    // 状态常量
    private static final int STATUS_PENDING = 0;
    private static final int STATUS_APPROVED = 1;
    private static final int STATUS_REJECTED = 2;
    private static final int STATUS_DELETED = 3;

    @Override
    public IPage<ForumComment> getCommentPage(ForumCommentQueryParam param) {
        // 创建分页对象
        Page<ForumComment> page = new Page<>(param.getPage(), param.getSize());
        
        // 调用Mapper查询
        return forumCommentMapper.selectCommentPage(page, param.getPostId(), param.getKeyword(), param.getStatus());
    }

    @Override
    public List<ForumComment> getPostComments(Integer postId) {
        // 查询帖子的顶级评论
        List<ForumComment> topComments = forumCommentMapper.selectTopCommentsByPostId(postId);
        
        // 查询所有子评论并按照父评论ID分组
        List<ForumComment> allChildComments = new ArrayList<>();
        for (ForumComment topComment : topComments) {
            allChildComments.addAll(getChildComments(topComment.getId()));
        }
        
        Map<Integer, List<ForumComment>> childrenMap = allChildComments.stream()
                .collect(Collectors.groupingBy(comment -> comment.getParentId() != null ? comment.getParentId() : -1));
        
        // 构建树形结构
        buildCommentTree(topComments, childrenMap);
        
        return topComments;
    }
    
    /**
     * 递归获取所有子评论
     */
    private List<ForumComment> getChildComments(Integer parentId) {
        List<ForumComment> children = forumCommentMapper.selectChildCommentsByParentId(parentId);
        List<ForumComment> allChildren = new ArrayList<>(children);
        
        for (ForumComment child : children) {
            allChildren.addAll(getChildComments(child.getId()));
        }
        
        return allChildren;
    }
    
    /**
     * 构建评论树形结构
     */
    private void buildCommentTree(List<ForumComment> comments, Map<Integer, List<ForumComment>> childrenMap) {
        for (ForumComment comment : comments) {
            List<ForumComment> children = childrenMap.get(comment.getId());
            if (children != null && !children.isEmpty()) {
                comment.setChildren(children);
                buildCommentTree(children, childrenMap);
            }
        }
    }

    @Override
    public ForumComment getCommentById(Integer id) {
        // 查询评论详情
        ForumComment comment = forumCommentMapper.selectCommentWithDetailsById(id);
        
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        
        return comment;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewComment(Integer id, Integer status) {
        // 检查状态是否合法
        if (!isValidStatus(status)) {
            throw new BusinessException("无效的状态值，应为1(已通过)或2(已拒绝)");
        }
        
        // 检查评论是否存在
        ForumComment comment = forumCommentMapper.selectById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        
        // 更新状态
        boolean result = forumCommentMapper.updateStatus(id, status) > 0;
        
        // 如果状态更新成功，更新帖子的回复数
        if (result) {
            forumPostMapper.updateReplyCount(comment.getPostId());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteComment(Integer id) {
        // 检查评论是否存在
        ForumComment comment = forumCommentMapper.selectById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        
        // 假删除评论（更新状态为已删除）
        boolean result = forumCommentMapper.softDeleteComment(id) > 0;
        
        // 更新帖子的回复数
        forumPostMapper.updateReplyCount(comment.getPostId());
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchReviewComments(List<Integer> ids, Integer status) {
        // 检查状态是否合法
        if (!isValidStatus(status)) {
            throw new BusinessException("无效的状态值，应为1(已通过)或2(已拒绝)");
        }
        
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // 获取评论关联的帖子ID，用于后续更新回复数
        LambdaQueryWrapper<ForumComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ForumComment::getId, ids);
        List<ForumComment> comments = forumCommentMapper.selectList(queryWrapper);
        List<Integer> postIds = comments.stream()
                .map(ForumComment::getPostId)
                .distinct()
                .collect(Collectors.toList());
        
        // 遍历批量更新
        boolean success = true;
        for (Integer id : ids) {
            if (forumCommentMapper.updateStatus(id, status) <= 0) {
                success = false;
            }
        }
        
        // 更新关联帖子的回复数
        for (Integer postId : postIds) {
            forumPostMapper.updateReplyCount(postId);
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteComments(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // 获取评论关联的帖子ID，用于后续更新回复数
        LambdaQueryWrapper<ForumComment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ForumComment::getId, ids);
        List<ForumComment> comments = forumCommentMapper.selectList(queryWrapper);
        List<Integer> postIds = comments.stream()
                .map(ForumComment::getPostId)
                .distinct()
                .collect(Collectors.toList());
        
        // 遍历批量假删除
        boolean success = true;
        for (Integer id : ids) {
            try {
                if (forumCommentMapper.softDeleteComment(id) <= 0) {
                    success = false;
                }
            } catch (Exception e) {
                success = false;
            }
        }
        
        // 更新关联帖子的回复数
        for (Integer postId : postIds) {
            forumPostMapper.updateReplyCount(postId);
        }
        
        return success;
    }
    
    /**
     * 检查状态是否合法
     * 
     * @param status 状态
     * @return 是否合法
     */
    private boolean isValidStatus(Integer status) {
        return status == STATUS_APPROVED || status == STATUS_REJECTED;
    }
} 