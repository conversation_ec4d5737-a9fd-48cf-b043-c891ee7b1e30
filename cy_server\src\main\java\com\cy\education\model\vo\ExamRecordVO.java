package com.cy.education.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 考试记录VO类
 */
@Data
@ApiModel("考试记录信息")
public class ExamRecordVO {
    
    /**
     * 记录ID
     */
    @ApiModelProperty("记录ID")
    private Integer id;
    
    /**
     * 考试ID
     */
    @ApiModelProperty("考试ID")
    private Integer examId;
    
    /**
     * 考试标题
     */
    @ApiModelProperty("考试标题")
    private String examTitle;
    
    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Integer userId;
    
    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;
    
    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    private Integer departmentId;
    
    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String departmentName;
    
    /**
     * 得分
     */
    @ApiModelProperty("得分")
    private Integer score;
    
    /**
     * 总分
     */
    @ApiModelProperty("总分")
    private Integer totalScore;
    
    /**
     * 及格分数
     */
    @ApiModelProperty("及格分数")
    private Integer passingScore;
    
    /**
     * 是否通过
     */
    @ApiModelProperty("是否通过")
    private Boolean passed;
    
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;
    
    /**
     * 实际用时(分钟)
     */
    @ApiModelProperty("实际用时(分钟)")
    private Integer duration;
    
    /**
     * 状态
     */
    @ApiModelProperty("状态(not_started-未开始,in_progress-进行中,completed-已完成,timeout-超时)")
    private String status;
    
    /**
     * 考试次数
     */
    @ApiModelProperty("考试次数")
    private Integer attemptNumber;
    
    /**
     * 答题记录
     */
    @ApiModelProperty("答题记录")
    private List<ExamAnswerVO> answers;
    
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;
} 