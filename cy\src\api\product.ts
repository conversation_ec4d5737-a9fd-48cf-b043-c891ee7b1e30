import { get, post, put, del } from '@/utils/request'

/**
 * 商品接口数据类型
 */
export interface Product {
  id: string
  name: string
  description: string
  image: string // 商品图片
  points: number // 兑换所需积分
  category: string
  stock: number
  lowStockThreshold?: number // 库存预警阈值
  limitPerUser: number // 每人限购数量，0表示不限制
  exchangeCount: number // 已兑换数量
  status: 'active' | 'inactive' // 状态：active(上架)、inactive(下架)
  startTime?: string
  endTime?: string
  tags: string[]
  createdAt: string
  updatedAt: string
}

export interface ProductCategory {
  id: string
  name: string
  description?: string
  count: number
}

export interface ProductStatistics {
  totalProducts: number
  activeProducts: number
  totalExchanges: number
  outOfStockCount: number
  lowStockProducts: number // 库存不足商品数
  categoryDistribution: {
    category: string
    count: number
  }[]
  popularProducts: {
    id: string
    name: string
    exchangeCount: number
  }[]
  hotProducts?: any[] // 热门商品
}

export interface ProductQueryParams {
  page?: number
  limit?: number
  keyword?: string
  name?: string // 商品名称
  category?: string
  status?: string
  minPoints?: number
  maxPoints?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

/**
 * 获取商品列表
 * @param params 查询参数
 */
export function getProductList(params?: ProductQueryParams) {
  return get<{ list: Product[]; total: number }>('/product/list', params)
}

/**
 * 获取商品详情
 * @param id 商品ID
 */
export function getProductDetail(id: string) {
  return get<Product>(`/product/${id}`)
}

/**
 * 创建商品
 * @param data 商品数据
 */
export function createProduct(data: Partial<Product>) {
  return post<{ id: string }>('/product', data)
}

/**
 * 更新商品信息
 * @param id 商品ID
 * @param data 商品数据
 */
export function updateProduct(id: string, data: Partial<Product>) {
  return put<{ success: boolean }>(`/product/${id}`, data)
}

/**
 * 删除商品
 * @param id 商品ID
 */
export function deleteProduct(id: string) {
  return del<{ success: boolean }>(`/product/${id}`)
}

/**
 * 上架/下架商品
 * @param id 商品ID
 * @param status 商品状态
 */
export function changeProductStatus(id: string, status: 'active' | 'inactive') {
  return post<{ success: boolean }>(`/product/${id}/status`, { status })
}

/**
 * 更新商品库存
 * @param id 商品ID
 * @param count 补充库存数量
 */
export function updateProductStock(id: string, count: number) {
  return put<{ success: boolean }>(`/product/${id}/stock`, { count })
}

/**
 * 获取商品分类列表
 */
export function getProductCategories() {
  return get<ProductCategory[]>('/product/categories')
}

/**
 * 创建商品分类
 * @param data 分类数据
 */
export function createProductCategory(data: { name: string; description?: string }) {
  return post<{ id: string }>('/product/category/create', data)
}

/**
 * 更新商品分类
 * @param id 分类ID
 * @param data 分类数据
 */
export function updateProductCategory(id: string, data: { name: string; description?: string }) {
  return put<{ success: boolean }>(`/product/category/update/${id}`, data)
}

/**
 * 删除商品分类
 * @param id 分类ID
 */
export function deleteProductCategory(id: string) {
  return del<{ success: boolean }>(`/product/category/delete/${id}`)
}

/**
 * 批量更新商品状态
 * @param ids 商品ID数组
 * @param status 商品状态
 */
export function batchUpdateProductStatus(ids: string[], status: 'active' | 'inactive') {
  return post<{ success: boolean; failedCount: number }>('/product/batch-status', { ids, status })
}

/**
 * 获取商品统计数据
 */
export function getProductStatistics() {
  return get<ProductStatistics>('/product/statistics')
}
