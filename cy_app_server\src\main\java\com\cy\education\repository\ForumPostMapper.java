package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.forum.ForumPost;
import com.cy.education.model.params.ForumPostQueryParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 论坛帖子Mapper接口
 */
@Repository
public interface ForumPostMapper extends BaseMapper<ForumPost> {

    /**
     * 查询帖子列表（带作者和分类信息，包含用户交互状态）
     *
     * @param page       分页对象
     * @param categoryId 分类ID
     * @param keyword    搜索关键词
     * @param status     状态
     * @param sortBy     排序方式：latest-最新, hot-最热, essence-精华
     * @param userId     发布帖子用户ID
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT p.*, s.name as author, s.avatar as author_avatar, c.name as category, " +
            "CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, " +
            "CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected " +
            "FROM forum_posts p " +
            "LEFT JOIN students s ON p.author_id = s.id " +
            "LEFT JOIN forum_categories c ON p.category_id = c.id " +
            "LEFT JOIN forum_post_likes pl ON p.id = pl.post_id AND pl.user_id = #{loginUserId} " +
            "LEFT JOIN forum_post_collects pc ON p.id = pc.post_id AND pc.user_id = #{loginUserId} " +
            "<where> " +
            "<if test='userId != null'> AND p.author_id = #{userId} </if> " +
            "<if test='categoryId != null'> AND p.category_id = #{categoryId} </if> " +
            "<if test='keyword != null and keyword != \"\"'> AND (p.title LIKE CONCAT('%', #{keyword}, '%') OR p.content LIKE CONCAT('%', #{keyword}, '%')) </if> " +
            "<if test='status != null'> AND p.status = #{status} </if> " +
            "</where> " +
            "ORDER BY " +
            "<choose> " +
            "<when test='sortBy == \"hot\"'> " +
            "p.is_top DESC, (p.like_count + p.reply_count) DESC, p.created_at DESC " +
            "</when> " +
            "<when test='sortBy == \"essence\"'> " +
            "p.is_top DESC, p.is_essence DESC, p.created_at DESC " +
            "</when> " +
            "<otherwise> " +
            "p.is_top DESC, p.created_at DESC " +
            "</otherwise> " +
            "</choose>" +
            "</script>")
    IPage<ForumPost> selectPostPage(Page<ForumPost> page,
                                    @Param("categoryId") Integer categoryId,
                                    @Param("keyword") String keyword,
                                    @Param("status") Integer status,
                                    @Param("sortBy") String sortBy,
                                    @Param("loginUserId") Integer loginUserId,
                                    @Param("userId") Integer userId);

    @Select("<script>" +
            "SELECT p.*, s.name as author, s.avatar as author_avatar, c.name as category, " +
            "CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, " +
            "CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected " +
            "FROM forum_posts p " +
            "LEFT JOIN students s ON p.author_id = s.id " +
            "LEFT JOIN forum_categories c ON p.category_id = c.id " +
            "LEFT JOIN forum_post_likes pl ON p.id = pl.post_id AND pl.user_id = #{userId} " +
            "INNER JOIN forum_post_collects pc ON p.id = pc.post_id AND pc.user_id = #{userId} " +
            "<where> " +
            "<if test='categoryId != null'> AND p.category_id = #{categoryId} </if> " +
            "<if test='keyword != null and keyword != \"\"'> AND (p.title LIKE CONCAT('%', #{keyword}, '%') OR p.content LIKE CONCAT('%', #{keyword}, '%')) </if> " +
            "<if test='status != null'> AND p.status = #{status} </if> " +
            "</where> " +
            "ORDER BY " +
            "<choose> " +
            "<when test='sortBy == \"hot\"'> " +
            "p.is_top DESC, (p.like_count + p.reply_count) DESC, p.created_at DESC " +
            "</when> " +
            "<when test='sortBy == \"essence\"'> " +
            "p.is_top DESC, p.is_essence DESC, p.created_at DESC " +
            "</when> " +
            "<otherwise> " +
            "p.is_top DESC, p.created_at DESC " +
            "</otherwise> " +
            "</choose>" +
            "</script>")
    IPage<ForumPost> getCollectedPostsPage(Page<ForumPost> page,
                                           @Param("categoryId") Integer categoryId,
                                           @Param("keyword") String keyword,
                                           @Param("status") Integer status,
                                           @Param("sortBy") String sortBy,
                                           @Param("userId") Integer userId);

    /**
     * 更新帖子回复数
     *
     * @param postId 帖子ID
     * @return 影响行数
     */
    @Update("UPDATE forum_posts SET reply_count = (SELECT COUNT(*) FROM forum_comments WHERE post_id = #{postId} AND status = 1) WHERE id = #{postId}")
    int updateReplyCount(@Param("postId") Integer postId);

    /**
     * 更新帖子浏览量
     *
     * @param postId 帖子ID
     * @return 影响行数
     */
    @Update("UPDATE forum_posts SET view_count = view_count + 1 WHERE id = #{postId}")
    int incrementViewCount(@Param("postId") Integer postId);

    /**
     * 假删除帖子（更新状态为已删除）
     *
     * @param postId 帖子ID
     * @return 影响行数
     */
    @Update("UPDATE forum_posts SET status = 3 WHERE id = #{postId}")
    int softDeletePost(@Param("postId") Integer postId);

    /**
     * 通过ID获取帖子信息，包括已删除的帖子，包含用户交互状态
     *
     * @param postId 帖子ID
     * @param userId 当前用户ID
     * @return 帖子信息
     */
    @Select("SELECT p.*, s.name as author, s.avatar as author_avatar, c.name as category, " +
            "CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked, " +
            "CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected, " +
            "CASE WHEN f.id IS NOT NULL THEN 1 ELSE 0 END as is_following " +
            "FROM forum_posts p " +
            "LEFT JOIN students s ON p.author_id = s.id " +
            "LEFT JOIN forum_categories c ON p.category_id = c.id " +
            "LEFT JOIN forum_post_likes pl ON p.id = pl.post_id AND pl.user_id = #{userId} " +
            "LEFT JOIN forum_post_collects pc ON p.id = pc.post_id AND pc.user_id = #{userId} " +
            "LEFT JOIN forum_user_follows f ON f.follower_id = #{userId} AND f.following_id = p.author_id " +
            "WHERE p.id = #{postId}")
    ForumPost selectPostWithDetailsById(@Param("postId") Integer postId, @Param("userId") Integer userId);

}
