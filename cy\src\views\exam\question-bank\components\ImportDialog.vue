<template>
  <el-dialog
    v-model="visible"
    title="批量导入题目"
    width="500px"
    destroy-on-close
  >
    <div class="import-container">
      <div class="import-step">
        <div class="step-title">第一步：下载模板</div>
        <div class="step-content">
          <el-button type="primary" :icon="Download" @click="$emit('download-template')">下载Excel模板</el-button>
        </div>
      </div>
      <div class="import-step">
        <div class="step-title">第二步：选择题库</div>
        <div class="step-content">
          <el-select v-model="importForm.bankId" placeholder="请选择题库" style="width: 100%;">
            <el-option
              v-for="item in bankList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
      </div>
      <div class="import-step">
        <div class="step-title">第三步：上传文件</div>
        <div class="step-content">
          <el-upload
            class="upload-container"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            :file-list="fileList"
          >
            <template #trigger>
              <el-button type="primary" :icon="Upload">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                请上传Excel文件，文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :disabled="!importForm.file">开始导入</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Download, Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// Props declaration
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  bankList: {
    type: Array,
    required: true
  },
  currentBankId: {
    type: Number,
    default: null
  }
})

// Emits declaration
const emit = defineEmits(['update:modelValue', 'download-template', 'submit'])

// Reactive refs
const visible = ref(props.modelValue)
const fileList = ref<any[]>([])

// Form data
const importForm = reactive({
  bankId: props.currentBankId,
  file: null as File | null
})

// Watch for props changes
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(() => props.currentBankId, (newVal) => {
  if (newVal) {
    importForm.bankId = newVal
  }
})

// Handle file change
const handleFileChange = (file: any) => {
  importForm.file = file.raw
}

// Handle form submit
const handleSubmit = () => {
  if (!importForm.bankId) {
    ElMessage.warning('请选择题库')
    return
  }
  
  if (!importForm.file) {
    ElMessage.warning('请上传文件')
    return
  }
  
  emit('submit', { ...importForm })
  visible.value = false
  // Reset form after submit
  importForm.file = null
  fileList.value = []
}
</script>

<style scoped>
.import-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.import-step {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
}

.step-title {
  font-weight: bold;
  margin-bottom: 16px;
}

.upload-container {
  width: 100%;
}
</style> 