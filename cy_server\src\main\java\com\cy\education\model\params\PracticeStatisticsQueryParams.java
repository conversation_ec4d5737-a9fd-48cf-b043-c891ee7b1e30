package com.cy.education.model.params;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 练习统计查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PracticeStatisticsQueryParams extends PageParams {
    
    /**
     * 学员姓名
     */
    private String studentName;
    
    /**
     * 题库ID
     */
    private Integer bankId;
    
    /**
     * 题库名称
     */
    private String bankName;
    
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    


    /**
     * 学员学号
     */
    private String studentNumber;

    /**
     * 最小正确率
     */
    private Double minAccuracyRate;

    /**
     * 最大正确率
     */
    private Double maxAccuracyRate;

    /**
     * 最小练习次数
     */
    private Integer minPracticeCount;

    /**
     * 最大练习次数
     */
    private Integer maxPracticeCount;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向 (asc/desc)
     */
    private String sortOrder;

    /**
     * 是否只显示有练习记录的学员
     */
    private Boolean onlyWithRecords;
}