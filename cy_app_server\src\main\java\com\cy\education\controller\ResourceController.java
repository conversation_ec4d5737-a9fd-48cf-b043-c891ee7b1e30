package com.cy.education.controller;

import com.cy.education.model.entity.study.Resource;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.service.study.ResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/resources")
public class ResourceController {

    @Autowired
    private ResourceService resourceService;

    @GetMapping("/{id}")
    public ApiResponse<Resource> getResourceById(@PathVariable Long id) {
        Resource resource = resourceService.getById(id);
        if (resource == null) {
            return ApiResponse.error("Resource not found");
        }
        return ApiResponse.success(resource);
    }
}
