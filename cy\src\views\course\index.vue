<template>
  <div class="app-container">
    <el-card>
      <!-- Filter bar -->
      <div class="filter-container">
        <el-input v-model="query.name" placeholder="课程名称" style="width: 200px;" class="filter-item" @keyup.enter="handleFilter" />
        <el-button class="filter-item" type="primary" :icon="Search" @click="handleFilter">
          搜索
        </el-button>
        <el-button class="filter-item" style="margin-left: 10px;" type="primary" :icon="Plus" @click="handleCreate">
          新增课程
        </el-button>
      </div>

      <!-- Table -->
      <el-table v-loading="loading" :data="list" border fit highlight-current-row style="width: 100%;">
        <el-table-column label="ID" prop="id" align="center" width="80" />
        <el-table-column label="课程封面" align="center" width="150">
          <template #default="{row}">
            <img :src="row.coverImageUrl || defaultCover" alt="封面" style="width: 100px; height: 56px; object-fit: cover;">
          </template>
        </el-table-column>
        <el-table-column label="课程名称" prop="name" />
        <el-table-column label="状态" align="center" width="100">
          <template #default="{row}">
            <el-tag :type="row.status === 'published' ? 'success' : 'info'">
              {{ row.status === 'published' ? '已发布' : '草稿' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" align="center" />
        <el-table-column label="操作" align="center" width="320" class-name="small-padding fixed-width">
          <template #default="{row}">
            <el-button type="primary" size="small" :icon="View" @click="handleEdit(row.id)">详情</el-button>
            <el-button type="success" size="small" :icon="Monitor" @click="handlePreview(row.id)">预览</el-button>
            <el-button v-if="row.status!=='published'" type="success" size="small" :icon="Upload" @click="handlePublish(row)">上架</el-button>
            <el-button v-if="row.status==='published'" type="info" size="small" :icon="Download" @click="handleWithdraw(row)">下架</el-button>
            <el-button size="small" type="danger" :icon="Delete" @click="handleDelete(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <el-pagination
        v-show="total > 0"
        v-model:current-page="query.page"
        v-model:page-size="query.size"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Plus, View, Monitor, Upload, Download, Delete } from '@element-plus/icons-vue';
import { getCourseList, deleteCourse, updateCourse, Course, CourseQuery, createCourse } from '@/api/course';
import defaultCover from '@/assets/logo.svg'; // A default image

const router = useRouter();
const list = ref<Course[]>([]);
const total = ref(0);
const loading = ref(true);
const query = reactive<CourseQuery>({
  page: 1,
  size: 10,
  name: undefined,
});

const getList = async () => {
  loading.value = true;
  try {
    const response: any = await getCourseList(query);
    list.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('获取课程列表失败');
  } finally {
    loading.value = false;
  }
};

const handleFilter = () => {
  query.page = 1;
  getList();
};

const handleCreate = async () => {
    try {
        const newCourse: Course = { name: '新的课程', status: 'draft' };
        const response: any = await createCourse(newCourse);
        ElMessage.success('创建成功，正在跳转到详情页...');
        router.push(`/course/detail/${response.id}`);
    } catch (error) {
        ElMessage.error('创建课程失败');
    }
};

const handleEdit = (id: number) => {
  router.push(`/course/detail/${id}`);
};

const handlePreview = (id: number) => {
  router.push(`/course/preview/${id}`);
};

const handlePublish = async (row: Course) => {
    try {
        await ElMessageBox.confirm('确定要上架该课程吗？', '提示', { type: 'warning' });
        await updateCourse(row.id!, { ...row, status: 'published' });
        ElMessage.success('上架成功');
        getList();
    } catch (error) {
        if (error !== 'cancel') ElMessage.error('上架失败');
    }
};

const handleWithdraw = async (row: Course) => {
    try {
        await ElMessageBox.confirm('确定要下架该课程吗？', '提示', { type: 'warning' });
        await updateCourse(row.id!, { ...row, status: 'draft' });
        ElMessage.success('下架成功');
        getList();
    } catch (error) {
        if (error !== 'cancel') ElMessage.error('下架失败');
    }
};

const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('删除课程将无法恢复，确定删除吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    await deleteCourse(id);
    ElMessage.success('删除成功');
    getList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
.filter-item {
  margin-right: 10px;
}
</style> 