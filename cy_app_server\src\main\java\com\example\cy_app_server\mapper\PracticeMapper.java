package com.example.cy_app_server.mapper;

import com.example.cy_app_server.model.PracticeRecord;
import com.example.cy_app_server.model.PracticeStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface PracticeMapper {
    List<Map<String, Object>> getAvailableBanks();

    List<Map<String, Object>> getUserPracticeStats(@Param("userId") Integer userId);

    List<Map<String, Object>> getWrongQuestions(@Param("userId") Integer userId, @Param("bankId") Integer bankId);

    Integer getWrongQuestionsCount(@Param("userId") Integer userId, @Param("bankId") Integer bankId);

    Integer getBankQuestionsCount(@Param("bankId") Integer bankId);

    void createPracticeRecord(PracticeRecord record);

    PracticeStats getPracticeStats(@Param("userId") Integer userId, @Param("bankId") Integer bankId);

    void createPracticeStats(PracticeStats stats);

    void updatePracticeStats(PracticeStats stats);

    void createPracticeAnswer(@Param("recordId") Integer recordId, @Param("questionId") Integer questionId, @Param("userAnswer") String userAnswer);

    PracticeRecord getPracticeRecordById(@Param("recordId") Integer recordId);

    List<Map<String, Object>> getUserAnswersForBank(@Param("userId") Integer userId, @Param("bankId") Integer bankId);
    
    Map<String, Object> getPracticeRecord(@Param("recordId") Integer recordId);

    List<Map<String, Object>> getPracticeAnswers(@Param("recordId") Integer recordId);

    void updatePracticeRecord(PracticeRecord record);
}
