<template>
  <div class="oss-upload-test">
    <h2>OSS上传测试</h2>
    
    <div class="upload-section">
      <h3>图片上传</h3>
      <el-upload
        action="#"
        :http-request="handleImageUpload"
        :show-file-list="true"
        :limit="5"
        accept="image/*"
        list-type="picture-card"
      >
        <el-icon><Plus /></el-icon>
      </el-upload>
      
      <div v-if="imageUrls.length > 0" class="preview">
        <h4>上传结果:</h4>
        <div v-for="(url, index) in imageUrls" :key="index" class="preview-item">
          <img :src="url" alt="uploaded image" />
          <div class="url">{{ url }}</div>
        </div>
      </div>
    </div>
    
    <div class="upload-section">
      <h3>视频上传</h3>
      <el-upload
        action="#"
        :http-request="handleVideoUpload"
        :show-file-list="true"
        :limit="2"
        accept="video/*"
      >
        <el-button type="primary">选择视频</el-button>
      </el-upload>
      
      <div v-if="videoProgress > 0 && videoProgress < 100" class="progress">
        <el-progress :percentage="videoProgress" />
      </div>
      
      <div v-if="videoUrls.length > 0" class="preview">
        <h4>上传结果:</h4>
        <div v-for="(url, index) in videoUrls" :key="index" class="preview-item">
          <video :src="url" controls style="max-width: 400px; max-height: 300px;"></video>
          <div class="url">{{ url }}</div>
        </div>
      </div>
    </div>
    
    <div class="upload-section">
      <h3>文档上传</h3>
      <el-upload
        action="#"
        :http-request="handleDocumentUpload"
        :show-file-list="true"
        :limit="3"
        accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
      >
        <el-button type="primary">选择文档</el-button>
      </el-upload>
      
      <div v-if="documentUrls.length > 0" class="preview">
        <h4>上传结果:</h4>
        <div v-for="(url, index) in documentUrls" :key="index" class="preview-item">
          <div class="file-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="url">{{ url }}</div>
        </div>
      </div>
    </div>
    
    <div v-if="error" class="error-message">
      <el-alert
        title="上传错误"
        type="error"
        :description="error"
        show-icon
        :closable="true"
        @close="error = ''"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Document } from '@element-plus/icons-vue'
import { getImageSignature, getVideoSignature, getDocumentSignature, uploadToOss } from '@/api/oss'

// 上传结果URL
const imageUrls = ref<string[]>([])
const videoUrls = ref<string[]>([])
const documentUrls = ref<string[]>([])

// 视频上传进度
const videoProgress = ref(0)

// 错误信息
const error = ref('')

// 处理图片上传
const handleImageUpload = async ({ file }: { file: File }) => {
  try {
    // 获取OSS上传签名
    const signature = await getImageSignature()
    console.log('图片上传签名:', signature)
    
    // 上传图片到OSS
    const { url } = await uploadToOss(file, signature)
    
    // 保存上传结果
    imageUrls.value.push(url)
    
    ElMessage.success('图片上传成功')
  } catch (err) {
    console.error('图片上传失败:', err)
    error.value = err instanceof Error ? err.message : String(err)
    ElMessage.error('图片上传失败')
  }
}

// 处理视频上传
const handleVideoUpload = async ({ file }: { file: File }) => {
  try {
    // 重置进度
    videoProgress.value = 0
    
    // 获取OSS上传签名
    const signature = await getVideoSignature()
    console.log('视频上传签名:', signature)
    
    // 上传视频到OSS，并监控进度
    const { url } = await uploadToOss(file, signature, (percent) => {
      videoProgress.value = percent
    })
    
    // 保存上传结果
    videoUrls.value.push(url)
    
    ElMessage.success('视频上传成功')
  } catch (err) {
    console.error('视频上传失败:', err)
    error.value = err instanceof Error ? err.message : String(err)
    ElMessage.error('视频上传失败')
  }
}

// 处理文档上传
const handleDocumentUpload = async ({ file }: { file: File }) => {
  try {
    // 获取OSS上传签名
    const signature = await getDocumentSignature()
    console.log('文档上传签名:', signature)
    
    // 上传文档到OSS
    const { url } = await uploadToOss(file, signature)
    
    // 保存上传结果
    documentUrls.value.push(url)
    
    ElMessage.success('文档上传成功')
  } catch (err) {
    console.error('文档上传失败:', err)
    error.value = err instanceof Error ? err.message : String(err)
    ElMessage.error('文档上传失败')
  }
}
</script>

<style scoped>
.oss-upload-test {
  padding: 20px;
}

.upload-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.preview {
  margin-top: 20px;
}

.preview-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px dashed #e0e0e0;
  border-radius: 4px;
}

.preview-item img {
  max-width: 300px;
  max-height: 200px;
  margin-bottom: 10px;
}

.file-icon {
  font-size: 40px;
  color: #909399;
  margin-bottom: 10px;
}

.url {
  word-break: break-all;
  color: #606266;
  font-size: 12px;
}

.progress {
  margin: 20px 0;
  width: 100%;
}

.error-message {
  margin-top: 20px;
}
</style> 