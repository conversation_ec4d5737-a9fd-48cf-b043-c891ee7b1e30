<template>
  <div class="content-management">
    <div class="page-container">
      <div class="page-header">
        <h1>首页内容管理</h1>
        <p class="description">管理首页的轮播图、新闻动态和通知公告</p>
      </div>

      <!-- Tab 切换 -->
      <el-tabs v-model="activeTab" type="card" class="content-tabs">
        <el-tab-pane label="轮播图管理" name="carousel">
          <CarouselManager />
        </el-tab-pane>
        <el-tab-pane label="新闻动态" name="news">
          <NewsManager />
        </el-tab-pane>
        <el-tab-pane label="通知公告" name="notice">
          <NoticeManager />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CarouselManager from './components/CarouselManager.vue'
import NewsManager from './components/NewsManager.vue'
import NoticeManager from './components/NoticeManager.vue'

// 当前激活的tab
const activeTab = ref('carousel')
</script>

<style scoped>


.content-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-container {
  flex: 1;
  padding: 20px;
  background-color: #f5f7fa;
  overflow: auto;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-header .description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.content-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

.content-tabs :deep(.el-tabs__content) {
  padding: 0;
}
</style> 