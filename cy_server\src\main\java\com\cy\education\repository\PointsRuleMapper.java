package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.PointsRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 积分规则数据访问层
 */
@Mapper
public interface PointsRuleMapper extends BaseMapper<PointsRule> {

    /**
     * 增加规则使用次数
     *
     * @param id 规则ID
     * @return 更新行数
     */
    @Update("UPDATE points_rule SET used_count = used_count + 1 WHERE id = #{id}")
    int incrementUsedCount(@Param("id") Integer id);

    /**
     * 根据规则编码查询规则
     *
     * @param code 规则编码
     * @return 规则对象
     */
    @Select("SELECT * FROM points_rule WHERE code = #{code} AND status = 1")
    PointsRule findByCode(@Param("code") String code);
}
