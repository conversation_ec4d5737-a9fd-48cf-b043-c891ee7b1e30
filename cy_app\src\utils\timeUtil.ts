export const formatDate = (timeStr: string): string => {
    if (!timeStr) return ''
    return timeStr.split(' ')[0]
}

export const formatTime = (timeStr: string): string => {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) {
        return '刚刚';
    } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`;
    } else {
        const days = Math.floor(diff / 86400000);
        if (days < 7) {
            return `${days}天前`;
        }
        return timeStr.split(' ')[0];
    }
}
