package com.cy.education.controller;

import com.cy.education.service.DashboardService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Dashboard控制器测试类
 */
@WebMvcTest(DashboardController.class)
public class DashboardControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DashboardService dashboardService;

    @Test
    public void testGetStatistics() throws Exception {
        // 准备测试数据
        Map<String, Object> mockStatistics = new HashMap<>();
        mockStatistics.put("totalStudents", 100L);
        mockStatistics.put("totalCourses", 50L);
        mockStatistics.put("totalExams", 30L);
        mockStatistics.put("totalPosts", 200L);

        when(dashboardService.getStatistics()).thenReturn(mockStatistics);

        // 执行测试
        mockMvc.perform(get("/dashboard/statistics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalStudents").value(100))
                .andExpect(jsonPath("$.data.totalCourses").value(50))
                .andExpect(jsonPath("$.data.totalExams").value(30))
                .andExpect(jsonPath("$.data.totalPosts").value(200));
    }

    @Test
    public void testGetRecentData() throws Exception {
        // 准备测试数据
        Map<String, Object> mockRecentData = new HashMap<>();
        mockRecentData.put("recentStudents", new java.util.ArrayList<>());
        mockRecentData.put("recentCourses", new java.util.ArrayList<>());
        mockRecentData.put("recentExams", new java.util.ArrayList<>());
        mockRecentData.put("recentPosts", new java.util.ArrayList<>());

        when(dashboardService.getRecentData()).thenReturn(mockRecentData);

        // 执行测试
        mockMvc.perform(get("/dashboard/recent"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.recentStudents").isArray())
                .andExpect(jsonPath("$.data.recentCourses").isArray())
                .andExpect(jsonPath("$.data.recentExams").isArray())
                .andExpect(jsonPath("$.data.recentPosts").isArray());
    }
}
