# 导入导出问题修复指南

## 问题总结

1. **考试管理页面无法进入** - `Cannot destructure property 'render' of 'undefined'`
2. **学员导入导出不好用**
3. **学习记录导入导出不好用**
4. **题库试题导入导出不好用**

## 已修复的问题

### 1. 题库页面API路径修复

**问题**: 题库导入API路径错误
**修复**: 
```vue
<!-- 修复前 -->
upload-action="/api/question/import"

<!-- 修复后 -->
upload-action="/api/exam/question/import"
```

### 2. API路径统一修复

**问题**: 试卷导出API路径不一致
**修复**: 
```typescript
// 修复前
export function exportExamPaper(examId: number, options: ExamPaperExportOptions) {
  return post(`/api/exam/${examId}/export`, options, { responseType: 'blob' })
}

// 修复后
export function exportExamPaper(examId: number, options: ExamPaperExportOptions) {
  return post(`/api/exam/paper/${examId}/export`, options, { responseType: 'blob' })
}
```

## 需要进一步检查的问题

### 1. 考试管理页面错误

**错误信息**: `Cannot destructure property 'render' of 'undefined'`

**可能原因**:
- ECharts组件导入问题
- 组件渲染时数据未准备好
- Vue组件生命周期问题

**检查步骤**:
1. 检查浏览器控制台的完整错误信息
2. 检查ECharts相关组件是否正确导入
3. 检查数据初始化是否正确

### 2. 后端服务状态

**需要确认**:
- 后端服务是否正常启动
- 导入导出接口是否正确响应
- 数据库连接是否正常

## 测试步骤

### 1. 后端服务测试

```bash
# 启动后端服务
cd cy_server
mvn spring-boot:run

# 检查服务状态
curl http://localhost:8080/api/admin/import/template
curl http://localhost:8080/api/exam/question/import/template
```

### 2. 前端功能测试

#### 管理员导入导出测试
1. 访问管理员管理页面
2. 点击"下载模板"按钮
3. 填写模板数据
4. 上传文件测试导入
5. 测试导出功能

#### 题目导入导出测试
1. 访问题库管理页面
2. 点击"导入题目"按钮
3. 下载模板并填写数据
4. 测试导入功能
5. 测试导出功能

#### 学员导入导出测试
1. 访问学员管理页面
2. 测试现有的导入导出功能
3. 检查是否有错误信息

#### 学习记录导出测试
1. 访问学习记录页面
2. 测试导出功能
3. 检查导出文件格式

### 3. 考试管理页面修复

如果考试管理页面仍然无法访问，尝试以下步骤：

1. **清除浏览器缓存**
2. **检查控制台错误**
3. **临时禁用ECharts组件**

```vue
<!-- 临时修复方案：注释掉ECharts相关代码 -->
<template>
  <div class="exam-records">
    <!-- 保留基本功能，暂时移除图表 -->
    <!-- <div ref="deptStatsChartRef"></div> -->
  </div>
</template>

<script setup lang="ts">
// 临时注释掉ECharts导入
// import * as echarts from 'echarts/core'
// import { BarChart, PieChart } from 'echarts/charts'
</script>
```

## 完整的API接口列表

### 管理员模块
- `GET /api/admin/import/template` - 下载导入模板
- `POST /api/admin/import` - 批量导入管理员
- `POST /api/admin/export` - 导出管理员列表

### 题目模块
- `GET /api/exam/question/import/template` - 下载题目导入模板
- `POST /api/exam/question/import` - 批量导入题目
- `POST /api/exam/question/export` - 导出题目列表

### 试卷模块
- `POST /api/exam/paper/{paperId}/export` - 导出单个试卷
- `POST /api/exam/paper/batch-export` - 批量导出试卷

### 学员模块（现有实现）
- `GET /api/student/export` - 导出学员列表
- `POST /api/student/import` - 批量导入学员

### 学习记录模块
- `POST /api/learning-record/export` - 导出学习记录
- `POST /api/learning-statistics/export` - 导出学习统计

### 考试记录模块
- `POST /api/exam-record/export` - 导出考试记录
- `POST /api/exam/{examId}/statistics/export` - 导出考试统计

## 调试建议

### 1. 开启详细日志

在浏览器控制台中检查：
- 网络请求状态
- JavaScript错误信息
- Vue组件渲染错误

### 2. 后端日志检查

检查后端控制台输出：
- 接口调用日志
- 数据库操作日志
- 异常堆栈信息

### 3. 分步测试

1. **先测试模板下载** - 确认后端接口正常
2. **再测试数据导入** - 确认文件上传和解析正常
3. **最后测试数据导出** - 确认查询和文件生成正常

## 常见问题解决

### 1. 404错误
- 检查API路径是否正确
- 确认后端控制器路径映射
- 检查前端代理配置

### 2. 文件上传失败
- 检查文件大小限制
- 确认文件格式支持
- 检查后端文件处理逻辑

### 3. 导出文件为空
- 检查查询条件是否正确
- 确认数据库中有数据
- 检查导出字段映射

### 4. 前端组件错误
- 检查组件导入路径
- 确认组件props传递正确
- 检查组件生命周期

## 下一步行动

1. **立即测试后端服务** - 确认所有API接口正常响应
2. **修复考试管理页面** - 解决ECharts渲染问题
3. **完善错误处理** - 添加更详细的错误提示
4. **优化用户体验** - 改进导入导出流程

## 联系支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误信息
2. 后端服务的启动日志
3. 具体的操作步骤和预期结果
4. 网络请求的详细信息（F12 -> Network）

这样可以更准确地定位和解决问题。
