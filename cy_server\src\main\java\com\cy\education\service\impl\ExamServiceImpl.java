package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BadRequestException;
import com.cy.education.exception.NotFoundException;
import com.cy.education.model.entity.ExamExam;
import com.cy.education.model.entity.ExamExamDepartment;
import com.cy.education.model.entity.ExamPaper;
import com.cy.education.model.params.ExamParams;
import com.cy.education.model.params.ExamQueryParams;
import com.cy.education.model.vo.ExamPaperVO;
import com.cy.education.model.vo.ExamVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.ExamExamDepartmentMapper;
import com.cy.education.repository.ExamExamMapper;
import com.cy.education.repository.ExamPaperMapper;
import com.cy.education.service.DepartmentService;
import com.cy.education.service.ExamService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 考试服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExamServiceImpl implements ExamService {

    private final ExamExamMapper examExamMapper;
    private final ExamExamDepartmentMapper examExamDepartmentMapper;
    private final ExamPaperMapper examPaperMapper;
    private final DepartmentService departmentService;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public PageResponse<ExamVO> listExams(ExamQueryParams params) {
        // 构建分页对象
        Page<ExamExam> page = new Page<>(params.getPage(), params.getSize());
        
        // 分页查询考试列表
        IPage<ExamExam> examPage = examExamMapper.selectExamPage(
            page, 
            params.getKeyword(),
            params.getPaperId(),
            params.getDepartmentId(),
            params.getStatus(),
            params.getIsPublished(),
            params.getSortBy(),
            params.getSortOrder()
        );
        
        // 转换为VO对象
        List<ExamVO> voList = examPage.getRecords().stream().map(exam -> {
            ExamVO vo = new ExamVO();
            BeanUtils.copyProperties(exam, vo);
            
            // 查询试卷信息
            ExamPaper paper = examPaperMapper.selectById(exam.getPaperId());
            if (paper != null) {
                ExamPaperVO paperVO = new ExamPaperVO();
                BeanUtils.copyProperties(paper, paperVO);
                vo.setPaper(paperVO);
            }
            
            // 查询部门ID列表
            List<Integer> departmentIds = examExamDepartmentMapper.selectDepartmentIdsByExamId(exam.getId());
            vo.setDepartmentIds(departmentIds);
            
            // 查询部门名称列表
            if (departmentIds != null && !departmentIds.isEmpty()) {
                Map<Integer, String> departmentMap = departmentService.getDepartmentMapByIds(departmentIds);
                List<String> departments = departmentIds.stream()
                    .map(departmentMap::get)
                    .filter(StringUtils::hasText)
                    .collect(Collectors.toList());
                vo.setDepartments(departments);
            }
            
            return vo;
        }).collect(Collectors.toList());
        
        return PageResponse.of(voList, examPage.getTotal(), params.getPage(), params.getSize());
    }

    @Override
    public ExamVO getExamDetail(Integer id) {
        // 查询考试基本信息
        ExamExam exam = examExamMapper.selectById(id);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        
        // 转换为VO对象
        ExamVO vo = new ExamVO();
        BeanUtils.copyProperties(exam, vo);
        
        // 查询试卷信息
        ExamPaper paper = examPaperMapper.selectById(exam.getPaperId());
        if (paper != null) {
            ExamPaperVO paperVO = new ExamPaperVO();
            BeanUtils.copyProperties(paper, paperVO);
            vo.setPaper(paperVO);
        }
        
        // 查询部门ID列表
        List<Integer> departmentIds = examExamDepartmentMapper.selectDepartmentIdsByExamId(id);
        vo.setDepartmentIds(departmentIds);
        
        // 查询部门名称列表
        if (departmentIds != null && !departmentIds.isEmpty()) {
            Map<Integer, String> departmentMap = departmentService.getDepartmentMapByIds(departmentIds);
            List<String> departments = departmentIds.stream()
                .map(departmentMap::get)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
            vo.setDepartments(departments);
        }
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createExam(ExamParams params, String createdBy) {
        // 检查考试标题是否已存在
        LambdaQueryWrapper<ExamExam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamExam::getTitle, params.getTitle());
        if (examExamMapper.selectCount(queryWrapper) > 0) {
            throw new BadRequestException("考试标题已存在");
        }
        
        // 检查试卷是否存在
        ExamPaper paper = examPaperMapper.selectById(params.getPaperId());
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        
        // 检查试卷是否已发布
        if (!paper.getIsPublished()) {
            throw new BadRequestException("试卷未发布，不能用于考试");
        }
        
        // 创建考试实体对象
        ExamExam exam = new ExamExam();
        BeanUtils.copyProperties(params, exam);
        
        // 转换时间字符串为LocalDateTime
        exam.setStartTime(LocalDateTime.parse(params.getStartTime(), DATE_TIME_FORMATTER));
        exam.setEndTime(LocalDateTime.parse(params.getEndTime(), DATE_TIME_FORMATTER));
        
        // 设置考试状态
        exam.setStatus(0); // 草稿状态
        exam.setIsPublished(false); // 默认未发布
        
        exam.setCreatedBy(createdBy);
        exam.setCreatedAt(LocalDateTime.now());
        exam.setUpdatedAt(LocalDateTime.now());
        
        // 插入数据库
        examExamMapper.insert(exam);
        
        // 插入考试部门关联
        examExamDepartmentMapper.batchInsert(exam.getId(), params.getDepartmentIds());
        
        return exam.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExam(Integer id, ExamParams params) {
        // 查询考试是否存在
        ExamExam exam = examExamMapper.selectById(id);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        
        // 如果考试已发布且已开始，则不允许修改
        if (exam.getIsPublished() && 
            (exam.getStatus() == 2 || exam.getStatus() == 3)) { // 进行中或已结束
            throw new BadRequestException("考试已开始或已结束，不能修改");
        }
        
        // 检查考试标题是否已存在(排除自身)
        if (!exam.getTitle().equals(params.getTitle())) {
            LambdaQueryWrapper<ExamExam> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExamExam::getTitle, params.getTitle());
            queryWrapper.ne(ExamExam::getId, id);
            if (examExamMapper.selectCount(queryWrapper) > 0) {
                throw new BadRequestException("考试标题已存在");
            }
        }
        
        // 检查试卷是否存在
        ExamPaper paper = examPaperMapper.selectById(params.getPaperId());
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        
        // 检查试卷是否已发布
        if (!paper.getIsPublished()) {
            throw new BadRequestException("试卷未发布，不能用于考试");
        }
        
        // 更新考试信息
        BeanUtils.copyProperties(params, exam);
        
        // 转换时间字符串为LocalDateTime
        exam.setStartTime(LocalDateTime.parse(params.getStartTime(), DATE_TIME_FORMATTER));
        exam.setEndTime(LocalDateTime.parse(params.getEndTime(), DATE_TIME_FORMATTER));
        
        exam.setUpdatedAt(LocalDateTime.now());
        
        // 更新数据库
        boolean result = examExamMapper.updateById(exam) > 0;
        
        // 更新考试部门关联
        examExamDepartmentMapper.deleteByExamId(id);
        examExamDepartmentMapper.batchInsert(id, params.getDepartmentIds());
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteExam(Integer id) {
        // 查询考试是否存在
        ExamExam exam = examExamMapper.selectById(id);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        
        // 如果考试已发布且已开始，则不允许删除
        if (exam.getIsPublished() && 
            (exam.getStatus() == 2 || exam.getStatus() == 3)) { // 进行中或已结束
            throw new BadRequestException("考试已开始或已结束，不能删除");
        }
        
        // 删除考试部门关联
        examExamDepartmentMapper.deleteByExamId(id);
        
        // 删除考试
        return examExamMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishExam(Integer id, Boolean isPublished) {
        // 查询考试是否存在
        ExamExam exam = examExamMapper.selectById(id);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        
        // 如果考试已开始或已结束，则不允许修改发布状态
        if (exam.getStatus() == 2 || exam.getStatus() == 3) { // 进行中或已结束
            throw new BadRequestException("考试已开始或已结束，不能修改发布状态");
        }
        
        // 如果发布状态没有变化，则直接返回成功
        if (exam.getIsPublished().equals(isPublished)) {
            return true;
        }
        
        // 更新考试发布状态
        exam.setIsPublished(isPublished);
        
        // 如果发布考试，则更新状态为未开始(1)
        if (isPublished) {
            exam.setStatus(1); // 未开始
        } else {
            exam.setStatus(0); // 草稿
        }
        
        exam.setUpdatedAt(LocalDateTime.now());
        
        return examExamMapper.updateById(exam) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamStatus(Integer id, Integer status) {
        // 查询考试是否存在
        ExamExam exam = examExamMapper.selectById(id);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        
        // 如果状态没有变化，则直接返回成功
        if (exam.getStatus().equals(status)) {
            return true;
        }
        
        // 状态只能是0-草稿,1-未开始,2-进行中,3-已结束
        if (status < 0 || status > 3) {
            throw new BadRequestException("无效的考试状态");
        }
        
        // 更新考试状态
        exam.setStatus(status);
        exam.setUpdatedAt(LocalDateTime.now());
        
        return examExamMapper.updateById(exam) > 0;
    }
} 