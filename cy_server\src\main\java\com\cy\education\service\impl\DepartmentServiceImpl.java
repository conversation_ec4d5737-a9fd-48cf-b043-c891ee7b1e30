package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.Department;
import com.cy.education.model.entity.Student;
import com.cy.education.repository.DepartmentMapper;
import com.cy.education.repository.StudentMapper;
import com.cy.education.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门服务实现
 */
@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private StudentMapper studentMapper;

    @Override
    public List<Department> listDepartments(Integer parentId, boolean includeChildren) {
        if (includeChildren) {
            // 返回部门及其子部门树形结构
            return departmentMapper.selectDepartmentTreeList(parentId);
        } else {
            // 仅返回指定父级的直接子部门
            return departmentMapper.selectDepartmentList(parentId);
        }
    }

    @Override
    public List<Department> getDepartmentTree() {
        // 1. 获取所有部门
        List<Department> allDepartments = departmentMapper.selectList(null);
        
        // 2. 构建树形结构
        // 用于存储ID到部门的映射，便于快速查找
        Map<Integer, Department> departmentMap = new HashMap<>();
        // 用于存储顶级部门
        List<Department> rootDepartments = new ArrayList<>();
        
        // 第一次遍历，建立ID到部门的映射
        for (Department dept : allDepartments) {
            // 初始化子部门列表
            dept.setChildren(new ArrayList<>());
            // 建立映射关系
            departmentMap.put(dept.getId(), dept);
            // 将createdAt映射为createTime字段
            dept.setCreateTime(dept.getCreatedAt().toString());
        }
        
        // 第二次遍历，构建树形结构
        for (Department dept : allDepartments) {
            Integer parentId = dept.getParentId();
            if (parentId == null) {
                // 如果没有父部门ID，则为顶级部门
                rootDepartments.add(dept);
            } else {
                // 如果有父部门ID，则添加到父部门的子部门列表中
                Department parentDept = departmentMap.get(parentId);
                if (parentDept != null) {
                    parentDept.getChildren().add(dept);
                }
            }
        }
        
        // 3. 为每个部门设置学员数量
        for (Department dept : allDepartments) {
            Integer studentCount = departmentMapper.countStudentsByDepartment(dept.getId());
            dept.setStudentCount(studentCount);
        }
        
        // 4. 按排序字段排序
        sortDepartmentTree(rootDepartments);
        
        return rootDepartments;
    }
    
    /**
     * 递归对部门树进行排序
     */
    private void sortDepartmentTree(List<Department> departments) {
        if (departments == null || departments.isEmpty()) {
            return;
        }
        
        // 按sort字段和id排序
        departments.sort((d1, d2) -> {
            if (d1.getSort() == null && d2.getSort() == null) {
                return d1.getId().compareTo(d2.getId());
            }
            if (d1.getSort() == null) return 1;
            if (d2.getSort() == null) return -1;
            
            int sortCompare = d1.getSort().compareTo(d2.getSort());
            return sortCompare != 0 ? sortCompare : d1.getId().compareTo(d2.getId());
        });
        
        // 递归对子部门排序
        for (Department dept : departments) {
            if (dept.getChildren() != null && !dept.getChildren().isEmpty()) {
                sortDepartmentTree(dept.getChildren());
            }
        }
    }

    @Override
    public Department getDepartmentById(Integer id) {
        Department department = departmentMapper.selectById(id);
        if (department == null) {
            throw new BusinessException("部门不存在");
        }
        
        // 统计部门下的学员数量
        Integer studentCount = departmentMapper.countStudentsByDepartment(id);
        department.setStudentCount(studentCount);
        
        // 设置createTime字段
        if (department.getCreatedAt() != null) {
            department.setCreateTime(department.getCreatedAt().toString());
        }
        
        return department;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDepartment(Department department) {
        // 验证父部门是否存在
        if (department.getParentId() != null) {
            Department parent = departmentMapper.selectById(department.getParentId());
            if (parent == null) {
                throw new BusinessException("父部门不存在");
            }
        }
        
        // 设置默认值
        if (department.getSort() == null) {
            department.setSort(0);
        }
        if (department.getStatus() == null) {
            department.setStatus(1);
        }
        
        return departmentMapper.insert(department) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDepartment(Department department) {
        // 验证部门是否存在
        Department existingDepartment = departmentMapper.selectById(department.getId());
        if (existingDepartment == null) {
            throw new BusinessException("部门不存在");
        }
        
        // 验证父部门是否存在
        if (department.getParentId() != null) {
            // 不能将部门的父部门设置为自己
            if (department.getId().equals(department.getParentId())) {
                throw new BusinessException("不能将部门的父部门设置为自己");
            }
            
            Department parent = departmentMapper.selectById(department.getParentId());
            if (parent == null) {
                throw new BusinessException("父部门不存在");
            }
            
            // 不能将部门的父部门设置为自己的子部门
            List<Integer> childrenIds = departmentMapper.selectChildDepartmentIds(department.getId());
            if (childrenIds.contains(department.getParentId())) {
                throw new BusinessException("不能将部门的父部门设置为其子部门");
            }
        }
        
        return departmentMapper.updateById(department) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDepartment(Integer id) {
        // 验证部门是否存在
        Department department = departmentMapper.selectById(id);
        if (department == null) {
            throw new BusinessException("部门不存在");
        }
        
        // 检查是否有子部门
        LambdaQueryWrapper<Department> departmentWrapper = new LambdaQueryWrapper<>();
        departmentWrapper.eq(Department::getParentId, id);
        Long childCount = departmentMapper.selectCount(departmentWrapper);
        if (childCount > 0) {
            throw new BusinessException("存在子部门，无法删除");
        }
        
        // 检查是否有学员
        LambdaQueryWrapper<Student> studentWrapper = new LambdaQueryWrapper<>();
        studentWrapper.eq(Student::getDepartmentId, id);
        Long studentCount = studentMapper.selectCount(studentWrapper);
        if (studentCount > 0) {
            throw new BusinessException("部门下存在学员，无法删除");
        }
        
        return departmentMapper.deleteById(id) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDepartmentSort(List<Map<String, Object>> sortData) {
        if (sortData == null || sortData.isEmpty()) {
            return true;
        }
        
        boolean success = true;
        for (Map<String, Object> item : sortData) {
            Integer id = (Integer) item.get("id");
            Integer parentId = (Integer) item.get("parentId");
            Integer sort = (Integer) item.get("sort");
            
            if (id == null) {
                continue;
            }
            
            Department department = departmentMapper.selectById(id);
            if (department == null) {
                throw new BusinessException("部门不存在，ID: " + id);
            }
            
            // 设置更新字段
            Department updateDept = new Department();
            updateDept.setId(id);
            
            // 如果父级部门ID有变化，则更新
            if (parentId != null) {
                // 验证父部门是否存在（除非parentId为null表示顶级部门）
                if (parentId > 0) {
                    Department parent = departmentMapper.selectById(parentId);
                    if (parent == null) {
                        throw new BusinessException("父部门不存在，ID: " + parentId);
                    }
                    
                    // 不能将部门的父部门设置为自己
                    if (id.equals(parentId)) {
                        throw new BusinessException("不能将部门的父部门设置为自己");
                    }
                    
                    // 不能将部门的父部门设置为自己的子部门
                    List<Integer> childrenIds = departmentMapper.selectChildDepartmentIds(id);
                    if (childrenIds.contains(parentId)) {
                        throw new BusinessException("不能将部门的父部门设置为其子部门");
                    }
                }
                
                updateDept.setParentId(parentId);
            }
            
            // 更新排序
            if (sort != null) {
                updateDept.setSort(sort);
            }
            
            // 更新部门
            if (departmentMapper.updateById(updateDept) <= 0) {
                success = false;
            }
        }
        
        return success;
    }

    @Override
    public List<Integer> getDepartmentAndChildrenIds(Integer departmentId) {
        if (departmentId == null) {
            return new ArrayList<>();
        }
        
        // 创建结果列表，首先添加当前部门ID
        List<Integer> result = new ArrayList<>();
        result.add(departmentId);
        
        // 获取所有子部门ID
        List<Integer> childrenIds = departmentMapper.selectChildDepartmentIds(departmentId);
        
        // 将子部门ID添加到结果列表
        result.addAll(childrenIds);
        
        return result;
    }

    @Override
    public Map<Integer, String> getDepartmentMapByIds(List<Integer> departmentIds) {
        if (departmentIds == null || departmentIds.isEmpty()) {
            return new HashMap<>();
        }
        
        // 查询部门信息
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Department::getId, departmentIds);
        List<Department> departments = departmentMapper.selectList(queryWrapper);
        
        // 构建ID到名称的映射
        return departments.stream()
            .collect(Collectors.toMap(Department::getId, Department::getName, (k1, k2) -> k1));
    }
} 