package com.cy.education.service.exam;

import com.cy.education.model.params.ExamQueryParams;
import com.cy.education.model.vo.ExamVO;
import com.cy.education.model.vo.PageResponse;

public interface ExamService {
    /**
     * 获取考试列表（不含试卷题目/答案，含当前用户作答记录）
     */
    PageResponse<ExamVO> listExams(ExamQueryParams params);

    /**
     * 获取考试详情（不含试卷正确答案，含当前用户作答记录）
     */
    ExamVO getExamDetail(Integer examId);

    /**
     * 获取考试完整详情（含所有信息，包括题目、正确答案、用户作答记录），用于考试作答和判题
     */
    ExamVO getExamFullDetail(Integer examId);

    /**
     * 更新考试状态
     */
    boolean updateExamStatus(Integer id, Integer status);
}
