<template>
  <el-dialog v-model="visible" title="考试记录" width="900px" destroy-on-close @close="handleClose">
    <div class="dialog-header">
      <span>学员：{{ student?.name }}</span>
      <el-select 
        v-model="statusFilter" 
        placeholder="状态筛选" 
        style="width: 120px; margin-left: 20px;" 
        @change="loadData"
      >
        <el-option label="全部" value="" />
        <el-option label="已完成" value="completed" />
        <el-option label="进行中" value="in_progress" />
        <el-option label="未开始" value="not_started" />
      </el-select>
    </div>
    
    <el-table :data="records" v-loading="loading" max-height="400">
      <el-table-column prop="examName" label="考试名称" min-width="150" />
      <el-table-column prop="courseName" label="所属课程" min-width="120" />
      <el-table-column prop="score" label="得分" width="80">
        <template #default="{ row }">
          <span v-if="row.score !== null" :class="row.score >= row.passScore ? 'text-success' : 'text-danger'">
            {{ row.score }}分
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalScore" label="总分" width="80" />
      <el-table-column prop="passScore" label="及格分" width="80" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="开始时间" width="150" />
      <el-table-column prop="endTime" label="结束时间" width="150" />
      <el-table-column label="操作" width="100">
        <template #default="{ row }">
          <el-button text type="primary" @click="viewDetail(row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="dialog-pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadData"
        @current-change="loadData"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { getExamRecordList, type ExamRecord, type ExamRecordQueryParams } from '@/api/exam'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  student: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const records = ref<ExamRecord[]>([])
const statusFilter = ref('')

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 监听props变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.student) {
    loadData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadData = async () => {
  if (!props.student) return

  loading.value = true
  try {
    const params: ExamRecordQueryParams = {
      page: pagination.page,
      limit: pagination.limit,
      userId: props.student.id.toString()
    }

    if (statusFilter.value) {
      params.status = statusFilter.value
    }

    const response = await getExamRecordList(params)

    // 转换数据格式以匹配表格显示
    const transformedRecords = response.list.map(record => ({
      ...record,
      examName: record.examTitle,
      courseName: record.examTitle, // 如果没有课程信息，使用考试标题
      passScore: 60 // 默认及格分，实际应该从考试信息中获取
    }))

    records.value = transformedRecords
    pagination.total = response.total
  } catch (error) {
    console.error('加载考试记录失败:', error)
    ElMessage.error('加载考试记录失败')
  } finally {
    loading.value = false
  }
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'completed': 'success',
    'in_progress': 'warning',
    'not_started': 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'completed': '已完成',
    'in_progress': '进行中',
    'not_started': '未开始'
  }
  return textMap[status] || status
}

const viewDetail = (row: any) => {
  // TODO: 实现查看考试详情
  ElMessage.info(`查看考试详情：${row.examName}`)
}

const handleClose = () => {
  visible.value = false
  records.value = []
  statusFilter.value = ''
  pagination.page = 1
}
</script>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  font-weight: 600;
}

.dialog-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.text-success {
  color: #67c23a;
  font-weight: 600;
}

.text-danger {
  color: #f56c6c;
  font-weight: 600;
}
</style>
