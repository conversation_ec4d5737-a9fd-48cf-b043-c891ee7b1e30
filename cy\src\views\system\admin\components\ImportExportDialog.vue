<template>
  <div>
    <!-- 导入对话框 -->
    <el-dialog v-model="importVisible" title="批量导入管理员" width="600px" destroy-on-close>
      <div class="import-steps">
        <div class="step">
          <div class="step-title">第一步：下载模板</div>
          <div class="step-content">
            <el-button type="primary" :icon="Download" @click="downloadTemplate">
              下载Excel模板
            </el-button>
            <div class="step-tip">请按照模板格式填写管理员信息</div>
          </div>
        </div>
        
        <div class="step">
          <div class="step-title">第二步：上传文件</div>
          <div class="step-content">
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              drag
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              accept=".xlsx,.xls"
              :limit="1"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">
                将Excel文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件，且不超过5MB
                </div>
              </template>
            </el-upload>
          </div>
        </div>
        
        <div v-if="previewData.length > 0" class="step">
          <div class="step-title">第三步：数据预览</div>
          <div class="step-content">
            <div class="preview-info">
              <span>共 {{ previewData.length }} 条数据</span>
              <span v-if="validationErrors.length > 0" class="error-count">
                {{ validationErrors.length }} 个错误
              </span>
            </div>
            
            <el-table :data="previewData.slice(0, 5)" border size="small" max-height="200">
              <el-table-column prop="username" label="用户名" width="120" />
              <el-table-column prop="realName" label="真实姓名" width="120" />
              <el-table-column prop="email" label="邮箱" width="180" />
              <el-table-column prop="phone" label="手机号" width="120" />
              <el-table-column prop="role" label="角色" width="100" />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.status === '启用' ? 'success' : 'danger'">
                    {{ row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
            
            <div v-if="previewData.length > 5" class="more-tip">
              还有 {{ previewData.length - 5 }} 条数据...
            </div>
            
            <div v-if="validationErrors.length > 0" class="validation-errors">
              <div class="error-title">数据验证错误：</div>
              <ul>
                <li v-for="error in validationErrors.slice(0, 10)" :key="error" class="error-item">
                  {{ error }}
                </li>
              </ul>
              <div v-if="validationErrors.length > 10" class="more-errors">
                还有 {{ validationErrors.length - 10 }} 个错误...
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="importVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="submitImport" 
          :loading="importing"
          :disabled="previewData.length === 0 || validationErrors.length > 0"
        >
          确认导入
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 导出对话框 -->
    <el-dialog v-model="exportVisible" title="导出管理员" width="500px">
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="导出范围">
          <el-radio-group v-model="exportForm.scope">
            <el-radio label="all">全部管理员</el-radio>
            <el-radio label="filtered">当前筛选结果</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="导出字段">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox label="username">用户名</el-checkbox>
            <el-checkbox label="realName">真实姓名</el-checkbox>
            <el-checkbox label="email">邮箱</el-checkbox>
            <el-checkbox label="phone">手机号</el-checkbox>
            <el-checkbox label="role">角色</el-checkbox>
            <el-checkbox label="status">状态</el-checkbox>
            <el-checkbox label="createTime">创建时间</el-checkbox>
            <el-checkbox label="lastLoginTime">最后登录</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="文件格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="xlsx">Excel (.xlsx)</el-radio>
            <el-radio label="csv">CSV (.csv)</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="exportVisible = false">取消</el-button>
        <el-button type="primary" @click="submitExport" :loading="exporting">
          确认导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import { ExcelUtils } from '@/utils/excel'

interface Props {
  currentFilters?: any
}

interface Emits {
  (e: 'import-success'): void
  (e: 'export-success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 导入相关
const importVisible = ref(false)
const importing = ref(false)
const uploadRef = ref()
const previewData = ref<any[]>([])
const validationErrors = ref<string[]>([])

// 导出相关
const exportVisible = ref(false)
const exporting = ref(false)
const exportForm = reactive({
  scope: 'all',
  fields: ['username', 'realName', 'email', 'phone', 'role', 'status', 'createTime'],
  format: 'xlsx'
})

// 管理员模板配置
const adminHeaders = [
  { key: 'username', title: '用户名', width: 15, required: true },
  { key: 'realName', title: '真实姓名', width: 15, required: true },
  { key: 'email', title: '邮箱', width: 25, required: true },
  { key: 'phone', title: '手机号', width: 15, required: true },
  { key: 'role', title: '角色', width: 15, required: true },
  { key: 'status', title: '状态', width: 10, required: true }
]

// 示例数据
const sampleData = [
  {
    username: 'admin001',
    realName: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    role: '系统管理员',
    status: '启用'
  },
  {
    username: 'admin002',
    realName: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    role: '部门管理员',
    status: '启用'
  }
]

// 打开导入对话框
const openImport = () => {
  importVisible.value = true
  resetImportData()
}

// 打开导出对话框
const openExport = () => {
  exportVisible.value = true
}

// 重置导入数据
const resetImportData = () => {
  previewData.value = []
  validationErrors.value = []
  uploadRef.value?.clearFiles()
}

// 下载模板
const downloadTemplate = () => {
  ExcelUtils.createTemplate(
    adminHeaders,
    sampleData,
    '管理员导入模板.xlsx',
    '管理员模板'
  )
}

// 文件变化处理
const handleFileChange = async (file: any) => {
  try {
    const rawData = await ExcelUtils.readExcelFile(file.raw)
    
    // 验证数据
    const validation = ExcelUtils.validateExcelData(rawData, [
      ...adminHeaders,
      {
        key: 'email',
        title: '邮箱',
        required: true,
        validator: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
      },
      {
        key: 'phone',
        title: '手机号',
        required: true,
        validator: (value: string) => /^1[3-9]\d{9}$/.test(value)
      }
    ])
    
    previewData.value = validation.validData
    validationErrors.value = validation.errors
    
    if (validation.valid) {
      ElMessage.success(`数据验证通过，共 ${validation.validData.length} 条记录`)
    } else {
      ElMessage.warning(`数据验证失败，发现 ${validation.errors.length} 个错误`)
    }
  } catch (error) {
    console.error('文件解析失败:', error)
    ElMessage.error('文件解析失败，请检查文件格式')
  }
}

// 文件移除处理
const handleFileRemove = () => {
  resetImportData()
}

// 提交导入
const submitImport = async () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('请先上传文件')
    return
  }
  
  if (validationErrors.value.length > 0) {
    ElMessage.error('请先修复数据错误')
    return
  }
  
  importing.value = true
  try {
    // 这里调用实际的导入API
    // await importAdmins(previewData.value)
    
    // 模拟导入
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`成功导入 ${previewData.value.length} 条管理员记录`)
    importVisible.value = false
    emit('import-success')
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

// 提交导出
const submitExport = async () => {
  if (exportForm.fields.length === 0) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }
  
  exporting.value = true
  try {
    // 这里调用实际的导出API获取数据
    // const data = await exportAdmins(exportForm)
    
    // 模拟数据
    const mockData = [
      {
        username: 'admin001',
        realName: '张三',
        email: '<EMAIL>',
        phone: '13800138001',
        role: '系统管理员',
        status: '启用',
        createTime: '2023-01-01 10:00:00',
        lastLoginTime: '2023-12-01 09:30:00'
      }
    ]
    
    // 过滤字段
    const filteredHeaders = adminHeaders.filter(h => exportForm.fields.includes(h.key))
    const filteredData = mockData.map(item => {
      const filtered: any = {}
      exportForm.fields.forEach(field => {
        filtered[field] = item[field as keyof typeof item]
      })
      return filtered
    })
    
    // 导出
    const filename = `管理员列表_${new Date().toISOString().slice(0, 10)}.${exportForm.format}`
    ExcelUtils.exportToExcel(filteredData, filteredHeaders, filename, '管理员列表')
    
    exportVisible.value = false
    emit('export-success')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 暴露方法
defineExpose({
  openImport,
  openExport
})
</script>

<style scoped>
.import-steps {
  margin: 20px 0;
}

.step {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.step:last-child {
  border-bottom: none;
}

.step-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #303133;
}

.step-content {
  margin-left: 16px;
}

.step-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.preview-info {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-count {
  color: #f56c6c;
  font-weight: 600;
}

.more-tip {
  margin-top: 8px;
  text-align: center;
  color: #909399;
  font-size: 12px;
}

.validation-errors {
  margin-top: 16px;
  padding: 12px;
  background-color: #fef0f0;
  border-radius: 4px;
  border: 1px solid #fbc4c4;
}

.error-title {
  font-weight: 600;
  color: #f56c6c;
  margin-bottom: 8px;
}

.error-item {
  color: #f56c6c;
  font-size: 12px;
  margin-bottom: 4px;
}

.more-errors {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 8px;
}
</style>
