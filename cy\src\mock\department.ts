import { mockResponse, generateId, randomDate } from './utils'
import type { Department } from '@/api/department'

// 生成模拟部门树数据
export const departmentData: Department[] = [
  {
    id: 1,
    name: '成远矿业',
    parentId: null,
    leader: '张总',
    sort: 1,
    status: 1,
    createTime: '2023-01-01 10:00:00',
    remark: '总公司',
    children: [
      {
        id: 2,
        name: '采矿部',
        parentId: 1,
        leader: '李经理',
        sort: 1,
        status: 1,
        createTime: '2023-01-02 10:00:00',
        children: [
          {
            id: 6,
            name: '采矿一队',
            parentId: 2,
            leader: '王队长',
            sort: 1,
            status: 1,
            createTime: '2023-01-03 10:00:00',
            children: []
          },
          {
            id: 7,
            name: '采矿二队',
            parentId: 2,
            leader: '赵队长',
            sort: 2,
            status: 1,
            createTime: '2023-01-03 10:30:00',
            children: []
          }
        ]
      },
      {
        id: 3,
        name: '安全部',
        parentId: 1,
        leader: '张经理',
        sort: 2,
        status: 1,
        createTime: '2023-01-02 11:00:00',
        children: [
          {
            id: 8,
            name: '安全检查组',
            parentId: 3,
            leader: '刘组长',
            sort: 1,
            status: 1,
            createTime: '2023-01-04 09:00:00',
            children: []
          }
        ]
      },
      {
        id: 4,
        name: '机电部',
        parentId: 1,
        leader: '王经理',
        sort: 3,
        status: 1,
        createTime: '2023-01-02 14:00:00',
        children: []
      },
      {
        id: 5,
        name: '人力资源部',
        parentId: 1,
        leader: '刘经理',
        sort: 4,
        status: 1,
        createTime: '2023-01-02 16:00:00',
        children: []
      }
    ]
  }
]

// 缓存部门数据的扁平化列表
let departmentList: Department[] = []

// 将部门树扁平化为列表
function flattenDepartmentTree(departments: Department[], result: Department[] = []) {
  departments.forEach(dept => {
    // 创建没有children属性的新对象
    const { children, ...deptWithoutChildren } = dept
    result.push(deptWithoutChildren)
    
    if (children && children.length > 0) {
      flattenDepartmentTree(children, result)
    }
  })
  
  return result
}

// 初始化扁平化的部门列表
export function initDepartmentList() {
  departmentList = flattenDepartmentTree(departmentData)
  return departmentList
}

// 获取部门树
export function mockGetDepartmentTree() {
  return mockResponse(departmentData)
}

// 获取部门列表（扁平化）
export function mockGetDepartmentList() {
  if (departmentList.length === 0) {
    initDepartmentList()
  }
  return mockResponse(departmentList)
}

// 获取部门详情
export function mockGetDepartmentById(id: number) {
  if (departmentList.length === 0) {
    initDepartmentList()
  }
  
  const department = departmentList.find(dept => dept.id === id)
  if (department) {
    return mockResponse(department)
  }
  
  return mockResponse(null, 404, '部门不存在')
}

// 根据ID查找部门在树中的位置
function findDepartmentInTree(departments: Department[], id: number): { dept: Department | null, parent: Department | null, index: number } {
  for (let i = 0; i < departments.length; i++) {
    if (departments[i].id === id) {
      return { dept: departments[i], parent: null, index: i }
    }
    
    const children = departments[i].children || [];
    for (let j = 0; j < children.length; j++) {
      if (children[j].id === id) {
        return { dept: children[j], parent: departments[i], index: j }
      }
      
      // 递归查找更深层级
      const childrenOfChild = children[j].children || [];
      if (childrenOfChild.length > 0) {
        const result = findDepartmentInTree(childrenOfChild, id)
        if (result.dept) {
          return result
        }
      }
    }
  }
  
  return { dept: null, parent: null, index: -1 }
}

// 生成下一个部门ID
let nextDepartmentId = 10

// 添加部门
export function mockAddDepartment(data: Partial<Department>) {
  const newDepartment: Department = {
    id: nextDepartmentId++,
    name: data.name || '',
    parentId: data.parentId !== undefined ? data.parentId : null,
    leader: data.leader || '',
    sort: data.sort || 999,
    status: data.status || 1,
    createTime: new Date().toLocaleString(),
    remark: data.remark || '',
    children: []
  }
  
  // 添加到树结构
  if (newDepartment.parentId === null) {
    // 添加为根部门
    departmentData.push(newDepartment)
  } else {
    // 递归查找父部门并添加
    const addToParent = (departments: Department[], parentId: number): boolean => {
      for (const dept of departments) {
        if (dept.id === parentId) {
          if (!dept.children) {
            dept.children = []
          }
          dept.children.push(newDepartment)
          return true
        }
        
        if (dept.children && dept.children.length > 0) {
          if (addToParent(dept.children, parentId)) {
            return true
          }
        }
      }
      
      return false
    }
    
    addToParent(departmentData, newDepartment.parentId)
  }
  
  // 更新扁平化列表
  const { children, ...deptWithoutChildren } = newDepartment
  departmentList.push(deptWithoutChildren)
  
  return mockResponse({ id: newDepartment.id })
}

// 更新部门
export function mockUpdateDepartment(data: Partial<Department>) {
  if (!data.id) {
    return mockResponse(null, 400, '部门ID不能为空')
  }
  
  // 更新扁平化列表中的部门
  const index = departmentList.findIndex(dept => dept.id === data.id)
  if (index !== -1) {
    departmentList[index] = { ...departmentList[index], ...data }
  }
  
  // 更新树结构中的部门
  const updateInTree = (departments: Department[]): boolean => {
    for (let i = 0; i < departments.length; i++) {
      if (departments[i].id === data.id) {
        // 保留children属性
        const children = departments[i].children || []
        departments[i] = { ...departments[i], ...data, children }
        return true
      }
      
      const children = departments[i].children || [];
      if (children.length > 0) {
        if (updateInTree(children)) {
          return true
        }
      }
    }
    
    return false
  }
  
  updateInTree(departmentData)
  
  return mockResponse({ success: true })
}

// 删除部门
export function mockDeleteDepartment(id: number) {
  // 从树结构中删除
  const deleteFromTree = (departments: Department[]): boolean => {
    for (let i = 0; i < departments.length; i++) {
      if (departments[i].id === id) {
        // 检查是否有子部门
        const children = departments[i].children || [];
        if (children.length > 0) {
          return false // 有子部门不能删除
        }
        
        departments.splice(i, 1)
        return true
      }
      
      const children = departments[i].children || [];
      if (children.length > 0) {
        const found = children.findIndex(child => child.id === id)
        if (found !== -1) {
          // 检查是否有子部门
          const childChildren = children[found].children || [];
          if (childChildren.length > 0) {
            return false // 有子部门不能删除
          }
          
          children.splice(found, 1)
          return true
        }
        
        if (deleteFromTree(children)) {
          return true
        }
      }
    }
    
    return false
  }
  
  const result = deleteFromTree(departmentData)
  if (!result) {
    return mockResponse(null, 400, '删除失败，该部门下有子部门')
  }
  
  // 从扁平化列表中删除
  const index = departmentList.findIndex(dept => dept.id === id)
  if (index !== -1) {
    departmentList.splice(index, 1)
  }
  
  return mockResponse({ success: true })
}

// 更新部门排序
export function mockUpdateDepartmentSort(data: { id: number, parentId: number | null, sort: number }[]) {
  // 暂时简单实现，仅更新sort值
  data.forEach(item => {
    const dept = departmentList.find(d => d.id === item.id)
    if (dept) {
      dept.sort = item.sort
    }
  })
  
  return mockResponse({ success: true })
}

// 初始化部门列表
initDepartmentList() 