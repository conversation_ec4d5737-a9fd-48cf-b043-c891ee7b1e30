package com.cy.education.model.dto;

import lombok.Data;
import java.util.List;

/**
 * 学员导入结果
 */
@Data
public class StudentImportResult {
    
    /**
     * 导入是否成功
     */
    private boolean success;
    
    /**
     * 总记录数
     */
    private int totalCount;
    
    /**
     * 成功导入数
     */
    private int successCount;
    
    /**
     * 失败记录数
     */
    private int failCount;
    
    /**
     * 错误信息
     */
    private String message;
    
    /**
     * 失败记录详情
     */
    private List<StudentImportData> failedRecords;
    
    /**
     * 错误文件下载URL（如果有失败记录）
     */
    private String errorFileUrl;
    
    /**
     * 导入进度（0-100）
     */
    private int progress = 100;
    
    /**
     * 详细统计信息
     */
    private String details;
}
