# 导入导出模板设计说明

## 模板设计原则

### 1. 用户友好性
- 模板包含示例数据，用户可以直接参考
- 必填字段用 `*` 标识
- 提供详细的字段说明和格式要求

### 2. 数据完整性
- 包含所有必要的业务字段
- 字段验证规则清晰
- 支持关联数据的导入

### 3. 错误处理
- 详细的错误信息反馈
- 行号定位错误位置
- 批量处理结果统计

## 模板详细设计

### 1. 管理员导入模板

#### 字段定义
| 字段名 | 是否必填 | 格式要求 | 示例值 | 说明 |
|--------|----------|----------|--------|------|
| 姓名* | 是 | 2-20个字符 | 张三 | 管理员真实姓名 |
| 用户名* | 是 | 3-20位字母数字下划线 | zhangsan | 登录用户名，不能重复 |
| 密码* | 是 | 6-20位字符 | 123456 | 登录密码 |
| 手机号* | 是 | 11位数字 | 13800138001 | 联系手机号 |
| 邮箱 | 否 | 邮箱格式 | <EMAIL> | 联系邮箱 |
| 部门名称 | 否 | 已存在的部门名称 | 技术部 | 所属部门 |

#### 模板示例
```
姓名*          用户名*      密码*    手机号*        邮箱                    部门名称
张三           zhangsan    123456   13800138001   <EMAIL>    技术部
李四           lisi        123456   13800138002   <EMAIL>        人事部
```

#### 验证规则
- 用户名不能重复
- 手机号格式验证
- 邮箱格式验证（如果填写）
- 部门名称必须存在（如果填写）

### 2. 学员导入模板

#### 字段定义
| 字段名 | 是否必填 | 格式要求 | 示例值 | 说明 |
|--------|----------|----------|--------|------|
| 姓名* | 是 | 2-20个字符 | 王五 | 学员真实姓名 |
| 用户名* | 是 | 3-20位字母数字下划线 | wangwu | 登录用户名，不能重复 |
| 密码* | 是 | 6-20位字符 | 123456 | 登录密码 |
| 手机号* | 是 | 11位数字 | 13800138003 | 联系手机号 |
| 邮箱 | 否 | 邮箱格式 | <EMAIL> | 联系邮箱 |
| 部门名称* | 是 | 已存在的部门名称 | 销售部 | 所属部门 |
| 学号 | 否 | 字母数字 | ST2024001 | 学员编号 |
| 性别 | 否 | 男/女 | 男 | 性别 |
| 入职日期 | 否 | YYYY-MM-DD | 2024-01-01 | 入职日期 |

#### 模板示例
```
姓名*  用户名*  密码*   手机号*      邮箱                部门名称*  学号      性别  入职日期
王五   wangwu  123456  13800138003  <EMAIL>  销售部    ST2024001  男   2024-01-01
赵六   zhaoliu 123456  13800138004  <EMAIL> 市场部    ST2024002  女   2024-01-02
```

### 3. 题目导入模板

#### 字段定义
| 字段名 | 是否必填 | 格式要求 | 示例值 | 说明 |
|--------|----------|----------|--------|------|
| 题库名称* | 是 | 已存在的题库名称 | Java基础 | 题目所属题库 |
| 题目类型* | 是 | single/multiple/judge/fill | single | 题目类型 |
| 题目标题* | 是 | 不超过500字符 | Java是什么类型的语言？ | 题目内容 |
| 选项A | 否 | 不超过200字符 | 编译型语言 | 选择题选项A |
| 选项B | 否 | 不超过200字符 | 解释型语言 | 选择题选项B |
| 选项C | 否 | 不超过200字符 | 混合型语言 | 选择题选项C |
| 选项D | 否 | 不超过200字符 | 脚本语言 | 选择题选项D |
| 正确答案* | 是 | 根据题型而定 | C | 正确答案 |
| 解析 | 否 | 不超过1000字符 | Java既需要编译又需要解释执行 | 答案解析 |
| 难度 | 否 | easy/medium/hard | medium | 题目难度 |
| 分值 | 否 | 正整数 | 5 | 题目分值 |

#### 题目类型说明
- **single**: 单选题，答案为A/B/C/D
- **multiple**: 多选题，答案为ABC/AB/BC等组合
- **judge**: 判断题，答案为true/false
- **fill**: 填空题，答案为文本内容

#### 模板示例
```
题库名称*  题目类型*  题目标题*                选项A      选项B      选项C      选项D    正确答案*  解析                        难度    分值
Java基础   single    Java是什么类型的语言？    编译型语言  解释型语言  混合型语言  脚本语言  C         Java既需要编译又需要解释执行  medium  5
Java基础   judge     Java是面向对象的语言      -          -          -          -        true      Java支持面向对象编程        easy    3
```

## 导入流程设计

### 1. 模板下载
- 用户点击"导入"按钮
- 系统显示导入对话框
- 用户点击"下载模板"获取Excel模板
- 模板包含示例数据和字段说明

### 2. 数据填写
- 用户按照模板格式填写数据
- 必填字段必须填写
- 格式必须符合要求
- 保存为Excel文件

### 3. 文件上传
- 用户选择填写好的Excel文件
- 系统验证文件格式（.xlsx/.xls）
- 系统验证文件大小（最大10MB）
- 开始解析和导入

### 4. 数据验证
- 逐行验证数据格式
- 检查必填字段
- 验证业务规则
- 记录错误信息

### 5. 结果反馈
- 显示导入统计：成功数量、失败数量
- 展示失败记录详情：行号、数据、失败原因
- 用户可以下载错误报告

## 导出功能设计

### 1. 导出配置
- **格式选择**: Excel、CSV、PDF
- **范围选择**: 全部数据、当前页面、已选择数据
- **字段选择**: 用户可以选择需要导出的字段
- **时间范围**: 支持按时间范围筛选

### 2. 试卷导出特殊配置
- **格式选择**: Word文档、PDF文档
- **答案设置**: 是否包含答案
- **解析设置**: 是否包含解析
- **答案位置**: 题目内显示 / 附录中显示
- **页面设置**: 页眉、页脚、页码
- **字体设置**: 字体大小选择

### 3. 导出文件命名规则
- **管理员**: `管理员列表_YYYYMMDD_HHMMSS.xlsx`
- **学员**: `学员列表_YYYYMMDD_HHMMSS.xlsx`
- **题目**: `题目列表_YYYYMMDD_HHMMSS.xlsx`
- **试卷**: `试卷名称_含答案解析.docx`
- **考试记录**: `考试记录_YYYYMMDD_HHMMSS.xlsx`
- **学习记录**: `学习记录_YYYYMMDD_HHMMSS.xlsx`

## 错误处理机制

### 1. 文件级错误
- 文件格式不正确
- 文件大小超限
- 文件损坏无法读取
- 文件为空

### 2. 数据级错误
- 必填字段为空
- 数据格式不正确
- 业务规则验证失败
- 关联数据不存在

### 3. 系统级错误
- 数据库连接失败
- 服务器内存不足
- 网络连接超时
- 权限不足

### 4. 错误信息格式
```json
{
  "successCount": 10,
  "failureCount": 2,
  "failures": [
    {
      "row": 3,
      "data": "张三, zhangsan, 123",
      "reason": "手机号格式不正确"
    },
    {
      "row": 5,
      "data": "李四, lisi, 123456",
      "reason": "用户名已存在"
    }
  ]
}
```

## 性能优化

### 1. 批量处理
- 使用批量插入提高数据库性能
- 分批处理大文件，避免内存溢出
- 异步处理，不阻塞用户界面

### 2. 内存管理
- 流式读取Excel文件
- 及时释放内存资源
- 限制单次处理的数据量

### 3. 用户体验
- 显示处理进度
- 支持取消操作
- 提供详细的状态反馈

## 安全考虑

### 1. 文件安全
- 验证文件类型和扩展名
- 限制文件大小
- 扫描恶意内容

### 2. 数据安全
- 验证用户权限
- 记录操作日志
- 敏感数据加密

### 3. 系统安全
- 防止SQL注入
- 限制并发请求
- 资源使用监控

## 总结

本导入导出功能设计遵循以下原则：
1. **用户友好**: 简单易用的操作界面
2. **数据完整**: 支持完整的业务数据
3. **错误处理**: 详细的错误信息和处理机制
4. **性能优化**: 高效的数据处理能力
5. **安全可靠**: 完善的安全防护措施

通过这套设计，用户可以方便地进行数据的批量导入和导出操作，大大提高了数据管理的效率。
