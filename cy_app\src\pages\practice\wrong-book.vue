<template>
  <view class="wrong-book-container">
    <!-- 头部 -->
    <view class="header">
      <view class="header-content">
        <text class="header-title">错题本</text>
        <text class="header-subtitle">查看和重新练习错题</text>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-container">
      <view class="stat-card">
        <text class="stat-number">{{ totalWrongQuestions }}</text>
        <text class="stat-label">错题总数</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{ solvedQuestions }}</text>
        <text class="stat-label">已解决</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{ wrongRate }}%</text>
        <text class="stat-label">错误率</text>
      </view>
    </view>

    <!-- 题库筛选 -->
    <view class="filter-section">
      <view class="filter-header">
        <text class="filter-title">按题库筛选</text>
      </view>
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-item" 
              :class="{ active: selectedBankId === 0 }"
              @click="selectBank(0)">
          <text class="filter-text">全部</text>
        </view>
        <view class="filter-item" 
              v-for="bank in banks" 
              :key="bank.id"
              :class="{ active: selectedBankId === bank.id }"
              @click="selectBank(bank.id)">
          <text class="filter-text">{{ bank.name }}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 错题列表 -->
    <view class="questions-container">
      <view v-if="filteredQuestions.length === 0" class="empty-state">
        <image class="empty-image" 
               src="/static/images/empty.png" 
               mode="aspectFit">
        </image>
        <text class="empty-text">暂无错题记录</text>
        <text class="empty-hint">完成练习后，错题会自动收录到这里</text>
      </view>

      <view v-else class="questions-list">
        <view class="question-item" 
              v-for="(question, index) in filteredQuestions" 
              :key="question.id"
              @click="showQuestionDetail(question)">
          <view class="question-header">
            <view class="question-index">
              <text class="index-text">{{ index + 1 }}</text>
            </view>
            <view class="question-info">
              <text class="question-bank">{{ question.bankName }}</text>
              <text class="question-type">{{ getQuestionTypeText(question.type) }}</text>
            </view>
            <view class="question-status">
              <text class="status-text" 
                    :class="{ solved: question.isSolved }">
                {{ question.isSolved ? '已解决' : '未解决' }}
              </text>
            </view>
          </view>
          
          <view class="question-content">
            <text class="question-text">{{ question.title }}</text>
          </view>
          
          <view class="question-meta">
            <text class="wrong-count">错误{{ question.wrongCount }}次</text>
            <text class="last-wrong-time">{{ formatTime(question.lastWrongTime) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions" v-if="filteredQuestions.length > 0">
      <button class="action-btn primary" @click="startWrongPractice">
        <text class="btn-text">开始错题练习</text>
      </button>
      <button class="action-btn secondary" @click="clearWrongQuestions">
        <text class="btn-text">清空错题本</text>
      </button>
    </view>

    <!-- 题目详情弹窗 -->
    <view class="question-detail-modal" v-if="showDetailModal" @click="closeQuestionDetail">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">题目详情</text>
          <text class="modal-close" @click="closeQuestionDetail">×</text>
        </view>
        
        <view class="modal-body" v-if="selectedQuestion">
          <view class="question-detail">
            <text class="detail-title">{{ selectedQuestion.title }}</text>
            
            <view class="detail-options" v-if="selectedQuestion.options && selectedQuestion.options.length > 0">
              <view class="option-item" 
                    v-for="option in selectedQuestion.options" 
                    :key="option.id">
                <text class="option-text">{{ option.id }}. {{ option.content }}</text>
              </view>
            </view>
            
            <view class="detail-answers">
              <view class="answer-row">
                <text class="answer-label">正确答案：</text>
                <text class="answer-value correct">{{ selectedQuestion.correctAnswer }}</text>
              </view>
              <view class="answer-row">
                <text class="answer-label">你的答案：</text>
                <text class="answer-value wrong">{{ selectedQuestion.userAnswer }}</text>
              </view>
            </view>
            
            <view class="detail-explanation" v-if="selectedQuestion.explanation">
              <text class="explanation-title">解析：</text>
              <text class="explanation-text">{{ selectedQuestion.explanation }}</text>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="modal-btn" @click="practiceThisQuestion">
            <text class="btn-text">重新练习</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getWrongQuestions, getAvailableBanks, clearWrongQuestions } from '@/api/practice'

export default {
  data() {
    return {
      banks: [],
      questions: [],
      selectedBankId: 0,
      showDetailModal: false,
      selectedQuestion: null,
      totalWrongQuestions: 0,
      solvedQuestions: 0,
      wrongRate: 0,
      loading: false
    }
  },
  
  computed: {
    filteredQuestions() {
      if (this.selectedBankId === 0) {
        return this.questions
      }
      return this.questions.filter(q => q.bankId === this.selectedBankId)
    }
  },
  
  onLoad() {
    this.init()
  },
  
  methods: {
    async init() {
      this.loading = true
      try {
        await Promise.all([
          this.loadBanks(),
          this.loadWrongQuestions()
        ])
      } catch (error) {
        console.error('初始化错题本失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    
    async loadBanks() {
      try {
        const response = await getAvailableBanks()
        this.banks = response || []
      } catch (error) {
        console.error('获取题库列表失败:', error)
      }
    },

    async loadWrongQuestions() {
      try {
        const userId = uni.getStorageSync('userId')
        if (!userId) {
          this.questions = []
          return
        }

        const response = await getWrongQuestions(
          parseInt(userId),
          this.selectedBankId === 0 ? null : this.selectedBankId
        )
        this.questions = response || []
        this.updateStats()
      } catch (error) {
        console.error('获取错题列表失败:', error)
      }
    },
    
    updateStats() {
      this.totalWrongQuestions = this.questions.length
      this.solvedQuestions = this.questions.filter(q => q.isSolved).length
      
      // 计算错误率（这里是示例，实际应从后端获取）
      const totalAnswered = this.questions.reduce((sum, q) => sum + q.wrongCount, 0)
      this.wrongRate = totalAnswered > 0 ? Math.round((this.totalWrongQuestions / totalAnswered) * 100) : 0
    },
    
    selectBank(bankId) {
      this.selectedBankId = bankId
      this.loadWrongQuestions()
    },
    
    showQuestionDetail(question) {
      this.selectedQuestion = question
      this.showDetailModal = true
    },
    
    closeQuestionDetail() {
      this.showDetailModal = false
      this.selectedQuestion = null
    },
    
    practiceThisQuestion() {
      if (!this.selectedQuestion) return
      
      // 跳转到单题练习页面
      uni.navigateTo({
        url: `/pages/practice/single-question?questionId=${this.selectedQuestion.id}`
      })
    },
    
    startWrongPractice() {
      const questions = this.filteredQuestions.filter(q => !q.isSolved)
      if (questions.length === 0) {
        uni.showToast({
          title: '没有需要练习的错题',
          icon: 'none'
        })
        return
      }
      
      // 跳转到错题练习页面
      uni.navigateTo({
        url: '/pages/practice/index?mode=wrong&bankId=' + this.selectedBankId
      })
    },
    
    async clearWrongQuestions() {
      const result = await uni.showModal({
        title: '确认清空',
        content: '确定要清空错题本吗？此操作不可恢复。',
        confirmText: '清空',
        cancelText: '取消'
      })

      if (!result.confirm) return

      try {
        const userId = uni.getStorageSync('userId')
        if (!userId) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          })
          return
        }

        await clearWrongQuestions({
          userId: parseInt(userId),
          bankId: this.selectedBankId === 0 ? null : this.selectedBankId
        })

        uni.showToast({
          title: '清空成功',
          icon: 'success'
        })

        this.loadWrongQuestions()
      } catch (error) {
        console.error('清空错题本失败:', error)
        uni.showToast({
          title: '清空失败',
          icon: 'error'
        })
      }
    },
    
    getQuestionTypeText(type) {
      const typeMap = {
        'single': '单选题',
        'multiple': '多选题',
        'judgment': '判断题',
        'fill': '填空题',
        'essay': '简答题'
      }
      return typeMap[type] || '未知类型'
    },
    
    formatTime(timeStr) {
      if (!timeStr) return ''
      const time = new Date(timeStr)
      const now = new Date()
      const diff = now - time
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < ********) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else {
        return Math.floor(diff / ********) + '天前'
      }
    }
  }
}
</script>

<style scoped>
.wrong-book-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 100px;
}

.header {
  padding: 40px 20px 30px;
  text-align: center;
}

.header-content {
  color: white;
}

.header-title {
  font-size: 28px;
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
}

.header-subtitle {
  font-size: 16px;
  opacity: 0.8;
}

.stats-container {
  display: flex;
  justify-content: space-around;
  margin: 0 20px 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.stat-card {
  text-align: center;
  color: white;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.filter-section {
  margin: 0 20px 20px;
}

.filter-header {
  margin-bottom: 15px;
}

.filter-title {
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 10px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.filter-item.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.filter-text {
  color: white;
  font-size: 14px;
}

.questions-container {
  margin: 0 20px;
}

.empty-state {
  text-align: center;
  padding: 50px 20px;
  color: white;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 18px;
  font-weight: bold;
  display: block;
  margin-bottom: 10px;
}

.empty-hint {
  font-size: 14px;
  opacity: 0.8;
}

.questions-list {
  /* 列表样式 */
}

.question-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.question-index {
  width: 30px;
  height: 30px;
  background: #667eea;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.index-text {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.question-info {
  flex: 1;
}

.question-bank {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 5px;
}

.question-type {
  font-size: 12px;
  color: #999;
}

.question-status {
  /* 状态样式 */
}

.status-text {
  font-size: 12px;
  color: #f56c6c;
  padding: 4px 8px;
  background: #fef0f0;
  border-radius: 10px;
}

.status-text.solved {
  color: #67c23a;
  background: #f0f9ff;
}

.question-content {
  margin-bottom: 15px;
}

.question-text {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

.question-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.wrong-count {
  color: #f56c6c;
}

.last-wrong-time {
  /* 时间样式 */
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  gap: 15px;
}

.action-btn {
  flex: 1;
  height: 45px;
  border-radius: 25px;
  border: none;
  font-size: 16px;
  font-weight: bold;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

.btn-text {
  color: inherit;
}

.question-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 15px;
  margin: 20px;
  max-height: 80vh;
  overflow-y: auto;
  max-width: 500px;
  width: 100%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
}

.modal-close {
  font-size: 24px;
  color: #999;
  cursor: pointer;
}

.modal-body {
  padding: 20px;
}

.question-detail {
  /* 详情样式 */
}

.detail-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  line-height: 1.6;
}

.detail-options {
  margin-bottom: 20px;
}

.option-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.option-text {
  font-size: 14px;
  line-height: 1.5;
}

.detail-answers {
  margin-bottom: 20px;
}

.answer-row {
  display: flex;
  margin-bottom: 10px;
}

.answer-label {
  font-size: 14px;
  color: #666;
  width: 80px;
}

.answer-value {
  font-size: 14px;
  font-weight: bold;
}

.answer-value.correct {
  color: #67c23a;
}

.answer-value.wrong {
  color: #f56c6c;
}

.detail-explanation {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
}

.explanation-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.explanation-text {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eee;
}

.modal-btn {
  width: 100%;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 16px;
  font-weight: bold;
}
</style> 