<template>
  <div class="record-result">
    <div class="page-header">
      <h2>考试成绩详情</h2>
      <el-button @click="handleBack">返回列表</el-button>
    </div>

    <div v-loading="loading" class="result-content">
      <el-row :gutter="20" v-if="!loading && recordInfo.id">
        <el-col :span="16">
          <!-- 成绩卡片 -->
          <el-card class="score-card">
            <template #header>
              <div class="card-header">
                <h3>{{ recordInfo.examName }}</h3>
              </div>
            </template>
            
            <div class="student-info">
              <div class="info-item">
                <span class="info-label">考生：</span>
                <span class="info-value">{{ recordInfo.studentName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">部门：</span>
                <span class="info-value">{{ recordInfo.departmentName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">提交时间：</span>
                <span class="info-value">{{ recordInfo.submitTime }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">答题时长：</span>
                <span class="info-value">{{ formatDuration(recordInfo.duration) }}</span>
              </div>
            </div>
            
            <div class="score-overview">
              <div class="total-score">
                <div class="score-circle" :class="getScoreClass(recordInfo.score, recordInfo.passingScore)">
                  <div class="score-value">{{ recordInfo.score }}</div>
                  <div class="score-total">满分 {{ recordInfo.totalScore }}</div>
                </div>
                <div class="score-result">
                  {{ recordInfo.score >= recordInfo.passingScore ? '及格' : '不及格' }}
                  <div class="passing-score">及格分数：{{ recordInfo.passingScore }}</div>
                </div>
              </div>
              
              <div class="score-breakdown">
                <el-progress 
                  type="dashboard" 
                  :percentage="getScorePercentage(recordInfo.objectiveScore, recordInfo.objectiveTotalScore)" 
                  :color="getProgressColor(recordInfo.objectiveScore, recordInfo.objectiveTotalScore)" 
                  :stroke-width="8"
                >
                  <template #default>
                    <div class="progress-info">
                      <div class="progress-value">{{ recordInfo.objectiveScore }}/{{ recordInfo.objectiveTotalScore }}</div>
                      <div class="progress-label">客观题</div>
                    </div>
                  </template>
                </el-progress>
                
                <el-progress 
                  type="dashboard" 
                  :percentage="getScorePercentage(recordInfo.subjectiveScore, recordInfo.subjectiveTotalScore)" 
                  :color="getProgressColor(recordInfo.subjectiveScore, recordInfo.subjectiveTotalScore)" 
                  :stroke-width="8"
                >
                  <template #default>
                    <div class="progress-info">
                      <div class="progress-value">{{ recordInfo.subjectiveScore }}/{{ recordInfo.subjectiveTotalScore }}</div>
                      <div class="progress-label">主观题</div>
                    </div>
                  </template>
                </el-progress>
              </div>
            </div>
            
            <div class="section-scores">
              <h4>各部分得分</h4>
              <div class="section-list">
                <div v-for="(section, index) in recordInfo.sections" :key="index" class="section-item">
                  <div class="section-name">{{ section.name }}</div>
                  <el-progress 
                    :percentage="getScorePercentage(section.score, section.totalScore)"
                    :color="getProgressColor(section.score, section.totalScore)"
                    :text-inside="true"
                    :stroke-width="18"
                    :format="() => `${section.score}/${section.totalScore}`"
                  />
                </div>
              </div>
            </div>
            
            <div v-if="recordInfo.comment" class="comment-section">
              <h4>阅卷评语</h4>
              <div class="comment-content">{{ recordInfo.comment }}</div>
            </div>
            
            <div class="action-buttons">
              <el-button type="primary" @click="handleViewDetail">查看答题详情</el-button>
              <el-button type="success" @click="handlePrintResult">打印成绩单</el-button>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <!-- 统计分析卡片 -->
          <el-card class="analysis-card">
            <template #header>
              <div class="card-header">
                <h3>成绩分析</h3>
              </div>
            </template>
            
            <div class="rank-info">
              <div class="rank-title">排名情况</div>
              <div class="rank-value">
                <span class="rank-position">{{ recordInfo.rank }}</span>
                <span class="rank-total">/ {{ recordInfo.totalStudents }}</span>
              </div>
              <div class="rank-percentage">超过了 {{ recordInfo.rankPercentage }}% 的考生</div>
            </div>
            
            <div class="chart-container">
              <div ref="correctRateChart" class="chart"></div>
            </div>
            
            <div class="wrong-questions-analysis">
              <h4>错题分析</h4>
              <div v-if="recordInfo.wrongQuestions.length > 0" class="wrong-questions-list">
                <div v-for="(question, index) in recordInfo.wrongQuestions" :key="index" class="wrong-question-item">
                  <div class="wrong-question-header">
                    <span class="question-type">[{{ getQuestionTypeName(question.type) }}]</span>
                    <span class="question-score">{{ question.markingScore }}/{{ question.score }}分</span>
                  </div>
                  <div class="wrong-question-content" v-html="question.content"></div>
                  <div class="wrong-question-info">
                    <div class="wrong-answer">
                      <span>你的答案：</span>
                      <span>{{ formatStudentAnswer(question) }}</span>
                    </div>
                    <div class="correct-answer">
                      <span>正确答案：</span>
                      <span>{{ formatReferenceAnswer(question) }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <el-empty v-else description="没有错题" />
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-empty v-else-if="!loading" description="未找到考试记录" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts/core'
import { PieChart } from 'echarts/charts'
import { TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { getExamRecordDetail } from '@/api/exam'

// 注册必要的echarts组件
echarts.use([PieChart, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer])

const route = useRoute()
const router = useRouter()
const recordId = route.params.id

// 加载状态
const loading = ref(false)

// 图表引用
const correctRateChart = ref(null)
let chart = null

// 考试记录信息
interface Section {
  name: string;
  score: number;
  totalScore: number;
}

interface WrongQuestion {
  type: string;
  content: string;
  options: any[];
  answer: string;
  studentAnswer: string;
  score: number;
  markingScore: number;
}

interface RecordInfo {
  id: string;
  studentId: string;
  studentName: string;
  departmentId: string;
  departmentName: string;
  examId: string;
  examName: string;
  score: number;
  totalScore: number;
  objectiveScore: number;
  objectiveTotalScore: number;
  subjectiveScore: number;
  subjectiveTotalScore: number;
  passingScore: number;
  duration: number;
  submitTime: string;
  comment: string;
  rank: number;
  totalStudents: number;
  rankPercentage: number;
  sections: Section[];
  wrongQuestions: WrongQuestion[];
}

const recordInfo = reactive<RecordInfo>({
  id: '',
  studentId: '',
  studentName: '',
  departmentId: '',
  departmentName: '',
  examId: '',
  examName: '',
  score: 0,
  totalScore: 0,
  objectiveScore: 0,
  objectiveTotalScore: 0,
  subjectiveScore: 0,
  subjectiveTotalScore: 0,
  passingScore: 0,
  duration: 0,
  submitTime: '',
  comment: '',
  rank: 0,
  totalStudents: 0,
  rankPercentage: 0,
  sections: [],
  wrongQuestions: []
})

// 获取考试记录详情
const fetchRecordDetail = async () => {
  loading.value = true
  try {
    // 调用后端API获取考试记录详情
    const response = await getExamRecordDetail(recordId as string)
    console.log('获取考试成绩详情成功:', response)
    
    if (response) {
      // 处理API返回的数据
      const record = response as any
      
      // 基本信息
    Object.assign(recordInfo, {
        id: record.id,
        studentId: record.userId,
        studentName: record.userName,
        departmentId: record.departmentId,
        departmentName: record.departmentName,
        examId: record.examId,
        examName: record.examTitle,
        score: record.score || 0,
        totalScore: record.totalScore || 100,
        passingScore: record.passingScore || 60,
        duration: record.duration || 0,
        submitTime: record.endTime ? formatDateTime(record.endTime) : '',
        comment: '', // 暂无评语字段
        rank: 0, // 暂无排名数据
        totalStudents: 0, // 暂无总人数数据
        rankPercentage: 0 // 暂无排名百分比数据
      })
      
      // 计算客观题和主观题得分
      let objectiveScore = 0
      let objectiveTotalScore = 0
      let subjectiveScore = 0
      let subjectiveTotalScore = 0
      
      // 处理答题记录，计算各部分得分
      if (record.answers && record.answers.length > 0) {
        const sections: { name: string, score: number, totalScore: number }[] = [
          { name: '一、单选题', score: 0, totalScore: 0 },
          { name: '二、多选题', score: 0, totalScore: 0 },
          { name: '三、判断题', score: 0, totalScore: 0 },
          { name: '四、填空题', score: 0, totalScore: 0 },
          { name: '五、简答题', score: 0, totalScore: 0 }
        ]
        
        // 错题列表
        const wrongQuestions: any[] = []
        
        // 处理答题记录
        record.answers.forEach((answer: any) => {
          const questionType = answer.questionType || 'single'
          const score = answer.score || 0
          const totalScore = answer.totalScore || 0
          
          // 更新各部分得分
          let sectionIndex = 0
          switch (mapQuestionType(questionType)) {
            case 'single':
              sectionIndex = 0
              objectiveScore += score
              objectiveTotalScore += totalScore
              break
            case 'multiple':
              sectionIndex = 1
              objectiveScore += score
              objectiveTotalScore += totalScore
              break
            case 'judgment':
              sectionIndex = 2
              objectiveScore += score
              objectiveTotalScore += totalScore
              break
            case 'fill':
              sectionIndex = 3
              subjectiveScore += score
              subjectiveTotalScore += totalScore
              break
            case 'essay':
              sectionIndex = 4
              subjectiveScore += score
              subjectiveTotalScore += totalScore
              break
          }
          
          // 更新部分得分
          sections[sectionIndex].score += score
          sections[sectionIndex].totalScore += totalScore
          
          // 添加错题
          if (!answer.isCorrect) {
            wrongQuestions.push({
              type: questionType,
              content: answer.questionContent || '题目内容',
              options: answer.options ? JSON.parse(answer.options) : [],
              answer: answer.correctAnswer || '',
              studentAnswer: answer.answer || '',
              score: totalScore,
              markingScore: score
            })
          }
        })
        
        // 更新记录信息
        recordInfo.objectiveScore = objectiveScore
        recordInfo.objectiveTotalScore = objectiveTotalScore
        recordInfo.subjectiveScore = subjectiveScore
        recordInfo.subjectiveTotalScore = subjectiveTotalScore
        recordInfo.sections = sections.filter(section => section.totalScore > 0)
        recordInfo.wrongQuestions = wrongQuestions
      }
    
    // 等待DOM渲染完成后初始化图表
    nextTick(() => {
      initChart()
    })
    }
  } catch (error) {
    console.error('获取考试记录详情失败:', error)
    ElMessage.error('获取考试记录详情失败')
  } finally {
    loading.value = false
  }
}

// 将后端题型映射到前端题型
const mapQuestionType = (backendType: string): string => {
  const typeMap: Record<string, string> = {
    'single': 'single',
    'multiple': 'multiple',
    'judge': 'judgment',
    'judgment': 'judgment',
    'fill': 'fill',
    'blank': 'fill',
    'essay': 'essay'
  }
  return typeMap[backendType] || 'single'
}

// 初始化图表
const initChart = () => {
  if (correctRateChart.value) {
    chart = echarts.init(correctRateChart.value)
    
    const correctCount = recordInfo.score / recordInfo.totalScore * 100
    const wrongCount = 100 - correctCount
    
    chart.setOption({
      title: {
        text: '答题正确率',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}%'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: ['正确', '错误']
      },
      series: [
        {
          name: '答题情况',
          type: 'pie',
          radius: '60%',
          center: ['50%', '60%'],
          data: [
            { value: correctCount.toFixed(1), name: '正确', itemStyle: { color: '#67c23a' } },
            { value: wrongCount.toFixed(1), name: '错误', itemStyle: { color: '#f56c6c' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            formatter: '{b}: {c}%'
          }
        }
      ]
    })
  }
}

// 获取题型名称
const getQuestionTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    'single': '单选题',
    'multiple': '多选题',
    'judge': '判断题',
    'blank': '填空题',
    'essay': '简答题'
  }
  return typeMap[type] || '未知题型'
}

// 格式化答题时长
const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}小时${mins}分钟`
  }
}

// 获取分数百分比
const getScorePercentage = (score: number, totalScore: number): number => {
  if (!totalScore) return 0
  return Math.round((score / totalScore) * 100)
}

// 获取进度条颜色
const getProgressColor = (score: number, totalScore: number): string => {
  const percentage = getScorePercentage(score, totalScore)
  if (percentage >= 80) {
    return '#67c23a' // 绿色
  } else if (percentage >= 60) {
    return '#e6a23c' // 黄色
  } else {
    return '#f56c6c' // 红色
  }
}

// 获取分数样式类
const getScoreClass = (score: number, passingScore: number): string => {
  return score >= passingScore ? 'pass' : 'fail'
}

// 格式化考生答案
const formatStudentAnswer = (question: WrongQuestion): string => {
  switch (question.type) {
    case 'single':
      return question.studentAnswer
    case 'multiple':
      return question.studentAnswer
    case 'judge':
      return question.studentAnswer === 'true' ? '正确' : '错误'
    default:
      return question.studentAnswer
  }
}

// 格式化参考答案
const formatReferenceAnswer = (question: WrongQuestion): string => {
  switch (question.type) {
    case 'single':
      return question.answer
    case 'multiple':
      return question.answer
    case 'judge':
      return question.answer === 'true' ? '正确' : '错误'
    default:
      return question.answer
  }
}

// 查看答题详情
const handleViewDetail = () => {
  router.push(`/exam/record/detail/${recordId}`)
}

// 打印成绩单
const handlePrintResult = () => {
  window.print()
}

// 返回列表
const handleBack = () => {
  router.push('/exam/management?tab=records')
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (e) {
    console.error('日期格式化错误:', e)
    return dateTime
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchRecordDetail()
})
</script>

<style scoped>
.record-result {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.result-content {
  margin-bottom: 30px;
}

.card-header h3 {
  margin: 0;
  font-weight: 500;
}

.score-card {
  margin-bottom: 20px;
}

.student-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: bold;
  margin-right: 5px;
  color: #606266;
}

.score-overview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.total-score {
  display: flex;
  align-items: center;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
}

.score-circle.pass {
  border: 5px solid #67c23a;
  color: #67c23a;
}

.score-circle.fail {
  border: 5px solid #f56c6c;
  color: #f56c6c;
}

.score-value {
  font-size: 40px;
  font-weight: bold;
  line-height: 1;
}

.score-total {
  font-size: 14px;
  margin-top: 5px;
}

.score-result {
  font-size: 24px;
  font-weight: bold;
}

.score-result.pass {
  color: #67c23a;
}

.score-result.fail {
  color: #f56c6c;
}

.passing-score {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
  font-weight: normal;
}

.score-breakdown {
  display: flex;
  gap: 30px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-value {
  font-size: 16px;
  font-weight: bold;
}

.progress-label {
  font-size: 14px;
  color: #909399;
}

.section-scores {
  margin-bottom: 30px;
}

.section-scores h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-weight: 500;
}

.section-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.section-item {
  display: flex;
  align-items: center;
}

.section-name {
  width: 150px;
  font-weight: 500;
  margin-right: 15px;
}

.comment-section {
  margin-bottom: 30px;
}

.comment-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-weight: 500;
}

.comment-content {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.analysis-card {
  height: 100%;
}

.rank-info {
  text-align: center;
  margin-bottom: 20px;
}

.rank-title {
  font-size: 16px;
  margin-bottom: 10px;
  color: #606266;
}

.rank-value {
  margin-bottom: 5px;
}

.rank-position {
  font-size: 36px;
  font-weight: bold;
  color: #409eff;
}

.rank-total {
  font-size: 18px;
  color: #909399;
}

.rank-percentage {
  font-size: 14px;
  color: #606266;
}

.chart-container {
  height: 300px;
  margin-bottom: 20px;
}

.chart {
  width: 100%;
  height: 100%;
}

.wrong-questions-analysis h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-weight: 500;
}

.wrong-questions-list {
  max-height: 400px;
  overflow-y: auto;
}

.wrong-question-item {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 10px;
}

.wrong-question-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.question-type {
  color: #409eff;
}

.question-score {
  color: #f56c6c;
  font-weight: bold;
}

.wrong-question-content {
  margin-bottom: 10px;
  line-height: 1.6;
}

.wrong-question-info {
  font-size: 14px;
}

.wrong-answer {
  color: #f56c6c;
  margin-bottom: 5px;
}

.correct-answer {
  color: #67c23a;
}

@media print {
  .page-header, .action-buttons, .analysis-card {
    display: none;
  }
  
  .score-card {
    box-shadow: none !important;
    border: 1px solid #ebeef5;
  }
}
</style> 