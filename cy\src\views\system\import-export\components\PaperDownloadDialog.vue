<template>
  <el-dialog v-model="visible" title="下载试卷" width="600px">
    <el-form :model="downloadForm" label-width="120px">
      <el-form-item label="选择试卷" required>
        <el-select 
          v-model="downloadForm.paperId" 
          placeholder="请选择要下载的试卷"
          style="width: 100%;"
          @change="handlePaperChange"
        >
          <el-option
            v-for="paper in paperList"
            :key="paper.id"
            :label="paper.title"
            :value="paper.id"
          >
            <div class="paper-option">
              <div class="paper-title">{{ paper.title }}</div>
              <div class="paper-info">
                总分：{{ paper.totalScore }}分 | 时长：{{ paper.duration }}分钟
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="试卷选项">
        <el-checkbox-group v-model="downloadForm.options">
          <el-checkbox label="includeAnswer">包含答案</el-checkbox>
          <el-checkbox label="includeAnalysis">包含解析</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="答案位置" v-if="downloadForm.options.includes('includeAnswer')">
        <el-radio-group v-model="downloadForm.answerPosition">
          <el-radio label="inline">题目内显示</el-radio>
          <el-radio label="appendix">附录显示</el-radio>
        </el-radio-group>
        <div class="form-tip">
          题目内显示：答案直接显示在每道题目下方<br>
          附录显示：答案统一显示在试卷末尾
        </div>
      </el-form-item>
      
      <el-form-item label="文档格式">
        <el-radio-group v-model="downloadForm.format">
          <el-radio label="docx">Word文档 (.docx)</el-radio>
          <el-radio label="pdf" disabled>PDF文档 (.pdf) - 即将支持</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="预览信息" v-if="selectedPaper">
        <div class="paper-preview">
          <div class="preview-item">
            <span class="preview-label">试卷名称：</span>
            <span>{{ selectedPaper.title }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">题目数量：</span>
            <span>{{ selectedPaper.questions?.length || 0 }} 道</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">总分：</span>
            <span>{{ selectedPaper.totalScore }} 分</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">考试时长：</span>
            <span>{{ selectedPaper.duration }} 分钟</span>
          </div>
          <div class="preview-item" v-if="selectedPaper.description">
            <span class="preview-label">试卷说明：</span>
            <span>{{ selectedPaper.description }}</span>
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleDownload" 
        :loading="downloading"
        :disabled="!downloadForm.paperId"
      >
        下载试卷
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { ExamPaperGenerator, type ExamPaper, type ExamPaperOptions } from '@/utils/examPaper'

interface Props {
  paperList: any[]
}

interface Emits {
  (e: 'download-success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const downloading = ref(false)

const downloadForm = reactive({
  paperId: null as number | null,
  options: ['includeAnswer'],
  answerPosition: 'appendix' as 'inline' | 'appendix',
  format: 'docx'
})

// 选中的试卷
const selectedPaper = computed(() => {
  if (!downloadForm.paperId) return null
  return props.paperList.find(p => p.id === downloadForm.paperId)
})

// 打开对话框
const open = () => {
  visible.value = true
  resetForm()
}

// 重置表单
const resetForm = () => {
  downloadForm.paperId = null
  downloadForm.options = ['includeAnswer']
  downloadForm.answerPosition = 'appendix'
  downloadForm.format = 'docx'
}

// 试卷变化处理
const handlePaperChange = async (paperId: number) => {
  if (!paperId) return
  
  try {
    // 这里可以加载试卷详细信息，包括题目
    // const paperDetail = await getPaperDetail(paperId)
    // 暂时使用模拟数据
  } catch (error) {
    console.error('加载试卷详情失败:', error)
    ElMessage.error('加载试卷详情失败')
  }
}

// 处理下载
const handleDownload = async () => {
  if (!downloadForm.paperId) {
    ElMessage.warning('请先选择试卷')
    return
  }
  
  downloading.value = true
  try {
    // 构建试卷数据
    const paper: ExamPaper = {
      id: selectedPaper.value!.id,
      title: selectedPaper.value!.title,
      description: selectedPaper.value!.description,
      duration: selectedPaper.value!.duration,
      totalScore: selectedPaper.value!.totalScore,
      questions: [
        // 模拟题目数据
        {
          id: 1,
          type: 'single',
          content: '以下哪个是JavaScript的数据类型？',
          options: ['String', 'Integer', 'Float', 'Character'],
          answer: 'A',
          analysis: 'JavaScript的基本数据类型包括String、Number、Boolean等',
          score: 2
        },
        {
          id: 2,
          type: 'multiple',
          content: '以下哪些是前端框架？',
          options: ['Vue.js', 'React', 'Angular', 'Spring'],
          answer: 'ABC',
          analysis: 'Vue.js、React、Angular都是前端框架，Spring是后端框架',
          score: 3
        },
        {
          id: 3,
          type: 'judge',
          content: 'HTML是一种编程语言',
          answer: '错误',
          analysis: 'HTML是标记语言，不是编程语言',
          score: 1
        },
        {
          id: 4,
          type: 'fill',
          content: 'JavaScript中声明变量使用关键字______。',
          answer: 'var/let/const',
          analysis: 'JavaScript中可以使用var、let或const关键字声明变量',
          score: 2
        },
        {
          id: 5,
          type: 'essay',
          content: '请简述Vue.js的生命周期钩子函数。',
          answer: 'Vue.js的生命周期包括created、mounted、updated、destroyed等钩子函数...',
          analysis: '生命周期钩子函数允许开发者在组件的不同阶段执行特定的代码',
          score: 5
        }
      ],
      createTime: new Date().toISOString()
    }
    
    // 构建下载选项
    const options: ExamPaperOptions = {
      includeAnswer: downloadForm.options.includes('includeAnswer'),
      includeAnalysis: downloadForm.options.includes('includeAnalysis'),
      answerPosition: downloadForm.answerPosition
    }
    
    // 生成并下载试卷
    await ExamPaperGenerator.generatePaper(paper, options)
    
    visible.value = false
    emit('download-success')
    ElMessage.success('试卷下载成功')
  } catch (error) {
    console.error('下载试卷失败:', error)
    ElMessage.error('下载试卷失败')
  } finally {
    downloading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.paper-option {
  width: 100%;
}

.paper-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.paper-info {
  font-size: 12px;
  color: #909399;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.paper-preview {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.preview-item {
  display: flex;
  margin-bottom: 8px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  padding: 12px 20px;
}
</style>
