<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.ExamAnswerMapper">

    <!-- 根据考试记录ID查询答题记录列表 -->
    <select id="selectByRecordId" resultType="com.cy.education.model.entity.ExamAnswer">
        SELECT *
        FROM exam_answer
        WHERE record_id = #{recordId}
        ORDER BY id ASC
    </select>

    <!-- 批量插入答题记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO exam_answer (
            record_id,
            question_id,
            answer,
            is_correct,
            score,
            comment,
            created_at,
            updated_at
        )
        VALUES
        <foreach collection="answers" item="item" separator=",">
            (
                #{item.recordId},
                #{item.questionId},
                #{item.answer},
                #{item.isCorrect},
                #{item.score},
                #{item.comment},
                #{item.createdAt},
                #{item.updatedAt}
            )
        </foreach>
    </insert>
</mapper>
