-- PK功能相关数据表

-- PK房间表
CREATE TABLE `pk_room` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '房间ID',
  `room_code` varchar(20) NOT NULL COMMENT '房间码',
  `bank_id` int NOT NULL COMMENT '题库ID',
  `question_count` int NOT NULL DEFAULT 10 COMMENT '题目数量',
  `time_limit` int NOT NULL DEFAULT 300 COMMENT '时间限制(秒)',
  `status` varchar(20) NOT NULL DEFAULT 'waiting' COMMENT '房间状态: waiting-等待, ready-准备中, playing-游戏中, finished-已结束',
  `creator_id` int NOT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `started_at` timestamp NULL COMMENT '开始时间',
  `finished_at` timestamp NULL COMMENT '结束时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_code` (`room_code`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_bank_id` (`bank_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PK房间表';

-- PK参与者表
CREATE TABLE `pk_participant` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '参与者ID',
  `room_id` bigint NOT NULL COMMENT '房间ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `position` int NOT NULL COMMENT '位置: 1-左侧, 2-右侧',
  `score` int NOT NULL DEFAULT 0 COMMENT '得分',
  `correct_count` int NOT NULL DEFAULT 0 COMMENT '正确数',
  `wrong_count` int NOT NULL DEFAULT 0 COMMENT '错误数',
  `answer_time` int NOT NULL DEFAULT 0 COMMENT '总答题时间(毫秒)',
  `is_ready` tinyint NOT NULL DEFAULT 0 COMMENT '是否准备: 0-未准备, 1-已准备',
  `is_finished` tinyint NOT NULL DEFAULT 0 COMMENT '是否完成: 0-未完成, 1-已完成',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `finished_at` timestamp NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_user` (`room_id`, `user_id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PK参与者表';

-- PK题目表
CREATE TABLE `pk_question` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `room_id` bigint NOT NULL COMMENT '房间ID',
  `question_id` int NOT NULL COMMENT '题目ID',
  `question_order` int NOT NULL COMMENT '题目顺序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_question_order` (`room_id`, `question_order`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PK题目表';

-- PK答题记录表
CREATE TABLE `pk_answer` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `room_id` bigint NOT NULL COMMENT '房间ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `question_id` int NOT NULL COMMENT '题目ID',
  `question_order` int NOT NULL COMMENT '题目顺序',
  `user_answer` text COMMENT '用户答案',
  `is_correct` tinyint NOT NULL DEFAULT 0 COMMENT '是否正确: 0-错误, 1-正确',
  `answer_time` int NOT NULL DEFAULT 0 COMMENT '答题时间(毫秒)',
  `score` int NOT NULL DEFAULT 0 COMMENT '得分',
  `answered_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_user_question` (`room_id`, `user_id`, `question_id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PK答题记录表';

-- PK匹配队列表
CREATE TABLE `pk_match_queue` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `bank_id` int NOT NULL COMMENT '题库ID',
  `question_count` int NOT NULL DEFAULT 10 COMMENT '题目数量',
  `time_limit` int NOT NULL DEFAULT 300 COMMENT '时间限制(秒)',
  `status` varchar(20) NOT NULL DEFAULT 'waiting' COMMENT '状态: waiting-等待匹配, matched-已匹配, cancelled-已取消',
  `matched_room_id` bigint NULL COMMENT '匹配到的房间ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `matched_at` timestamp NULL COMMENT '匹配时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_waiting` (`user_id`, `status`),
  KEY `idx_bank_id` (`bank_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PK匹配队列表';

-- 添加外键约束
ALTER TABLE `pk_room` ADD CONSTRAINT `fk_pk_room_creator` FOREIGN KEY (`creator_id`) REFERENCES `students` (`id`);
ALTER TABLE `pk_room` ADD CONSTRAINT `fk_pk_room_bank` FOREIGN KEY (`bank_id`) REFERENCES `exam_bank` (`id`);
ALTER TABLE `pk_participant` ADD CONSTRAINT `fk_pk_participant_room` FOREIGN KEY (`room_id`) REFERENCES `pk_room` (`id`);
ALTER TABLE `pk_participant` ADD CONSTRAINT `fk_pk_participant_user` FOREIGN KEY (`user_id`) REFERENCES `students` (`id`);
ALTER TABLE `pk_question` ADD CONSTRAINT `fk_pk_question_room` FOREIGN KEY (`room_id`) REFERENCES `pk_room` (`id`);
ALTER TABLE `pk_question` ADD CONSTRAINT `fk_pk_question_question` FOREIGN KEY (`question_id`) REFERENCES `exam_question` (`id`);
ALTER TABLE `pk_answer` ADD CONSTRAINT `fk_pk_answer_room` FOREIGN KEY (`room_id`) REFERENCES `pk_room` (`id`);
ALTER TABLE `pk_answer` ADD CONSTRAINT `fk_pk_answer_user` FOREIGN KEY (`user_id`) REFERENCES `students` (`id`);
ALTER TABLE `pk_answer` ADD CONSTRAINT `fk_pk_answer_question` FOREIGN KEY (`question_id`) REFERENCES `exam_question` (`id`);
ALTER TABLE `pk_match_queue` ADD CONSTRAINT `fk_pk_match_queue_user` FOREIGN KEY (`user_id`) REFERENCES `students` (`id`);
ALTER TABLE `pk_match_queue` ADD CONSTRAINT `fk_pk_match_queue_bank` FOREIGN KEY (`bank_id`) REFERENCES `exam_bank` (`id`);
