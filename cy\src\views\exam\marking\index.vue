<template>
  <div class="exam-marking">
    <div class="page-header">
      <h2>在线阅卷</h2>
      <el-button @click="goBack">返回</el-button>
    </div>

    <el-card class="exam-info-card" v-if="examRecord">
      <div class="exam-info">
        <h3>{{ examRecord.examTitle }}</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <span class="label">考生姓名：</span>
              <span class="value">{{ examRecord.userName }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">所属部门：</span>
              <span class="value">{{ examRecord.departmentName }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">考试时长：</span>
              <span class="value">{{ formatDuration(examRecord.duration) }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">当前得分：</span>
              <span class="value">{{ examRecord.score }}/{{ examRecord.totalScore }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <el-card class="questions-card" v-loading="loading">
      <div class="questions-content">
        <div v-for="(question, index) in questions" :key="question.id" class="question-item">
          <div class="question-header">
            <span class="question-number">第{{ index + 1 }}题</span>
            <el-tag :type="getQuestionTypeTag(question.type)">{{ getQuestionTypeText(question.type) }}</el-tag>
            <span class="question-score">{{ question.score }}分</span>
          </div>
          
          <div class="question-content">
            <div class="question-title">{{ question.title }}</div>
            
            <!-- 选择题选项 -->
            <div v-if="question.options" class="question-options">
              <div v-for="(option, optionIndex) in question.options" :key="optionIndex" class="option-item">
                {{ option }}
              </div>
            </div>
            
            <!-- 学生答案 -->
            <div class="student-answer">
              <div class="answer-label">学生答案：</div>
              <div class="answer-content">
                <span v-if="question.type === 'essay'" class="essay-answer">
                  {{ getStudentAnswer(question.id) }}
                </span>
                <span v-else class="choice-answer">
                  {{ formatStudentAnswer(question.type, getStudentAnswer(question.id)) }}
                </span>
              </div>
            </div>
            
            <!-- 正确答案 -->
            <div class="correct-answer">
              <div class="answer-label">正确答案：</div>
              <div class="answer-content">
                {{ formatCorrectAnswer(question.type, question.correctAnswer) }}
              </div>
            </div>
            
            <!-- 主观题阅卷 -->
            <div v-if="question.type === 'essay'" class="marking-section">
              <div class="marking-form">
                <el-form :inline="true">
                  <el-form-item label="得分">
                    <el-input-number
                      v-model="markingScores[question.id]"
                      :min="0"
                      :max="question.score"
                      :step="0.5"
                      style="width: 120px"
                    />
                  </el-form-item>
                  <el-form-item label="评语">
                    <el-input
                      v-model="markingComments[question.id]"
                      placeholder="请输入评语"
                      style="width: 300px"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" size="small" @click="markQuestion(question.id)">
                      确定打分
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            
            <!-- 客观题自动判分结果 -->
            <div v-else class="auto-marking">
              <el-tag :type="isAnswerCorrect(question.id) ? 'success' : 'danger'">
                {{ isAnswerCorrect(question.id) ? '正确' : '错误' }}
              </el-tag>
              <span class="score-info">
                得分：{{ getQuestionScore(question.id) }}/{{ question.score }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <div class="footer-actions">
      <el-button @click="goBack">返回</el-button>
      <el-button type="primary" @click="submitMarking">提交阅卷</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const examRecord = ref<any>(null)
const questions = ref<any[]>([])
const markingScores = reactive<{ [key: string]: number }>({})
const markingComments = reactive<{ [key: string]: string }>({})

// 获取考试记录详情
const fetchExamRecord = async () => {
  loading.value = true
  try {
    const recordId = route.params.id as string
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    examRecord.value = {
      id: recordId,
      examTitle: '安全生产知识考试',
      userName: '张三',
      departmentName: '生产部',
      duration: 120,
      score: 75,
      totalScore: 100,
      answers: []
    }
    
    questions.value = [
      {
        id: '1',
        title: '以下哪项是安全生产的基本方针？',
        type: 'single',
        options: ['A. 安全第一，预防为主', 'B. 生产第一，安全第二', 'C. 效率第一，安全第二', 'D. 成本第一，安全第二'],
        correctAnswer: 'A',
        score: 5
      },
      {
        id: '2',
        title: '请论述安全生产的重要性及其在企业管理中的作用。',
        type: 'essay',
        correctAnswer: '安全生产是企业管理的重要组成部分...',
        score: 20
      }
    ]
    
    // 初始化打分数据
    questions.value.forEach(q => {
      if (q.type === 'essay') {
        markingScores[q.id] = 0
        markingComments[q.id] = ''
      }
    })
    
  } catch (error) {
    console.error('获取考试记录失败:', error)
    ElMessage.error('获取考试记录失败')
  } finally {
    loading.value = false
  }
}

// 格式化考试时长
const formatDuration = (minutes: number) => {
  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours}小时${remainingMinutes}分钟`
  }
}

// 获取题目类型标签
const getQuestionTypeTag = (type: string) => {
  const typeMap: { [key: string]: string } = {
    'single': 'primary',
    'multiple': 'success',
    'judge': 'warning',
    'fill': 'info',
    'essay': 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  const typeMap: { [key: string]: string } = {
    'single': '单选题',
    'multiple': '多选题',
    'judge': '判断题',
    'fill': '填空题',
    'essay': '主观题'
  }
  return typeMap[type] || '未知类型'
}

// 获取学生答案
const getStudentAnswer = (questionId: string) => {
  // 模拟学生答案
  const answers: { [key: string]: string } = {
    '1': 'A',
    '2': '安全生产是企业发展的基础，它不仅关系到员工的生命安全，也影响企业的可持续发展...'
  }
  return answers[questionId] || '未作答'
}

// 格式化学生答案显示
const formatStudentAnswer = (type: string, answer: string) => {
  if (type === 'judge') {
    return answer === 'true' ? '正确' : '错误'
  }
  return answer
}

// 格式化正确答案显示
const formatCorrectAnswer = (type: string, answer: any) => {
  if (type === 'judge') {
    return answer === 'true' ? '正确' : '错误'
  }
  if (Array.isArray(answer)) {
    return answer.join(', ')
  }
  return answer
}

// 判断答案是否正确
const isAnswerCorrect = (questionId: string) => {
  // 模拟判断逻辑
  return questionId === '1'
}

// 获取题目得分
const getQuestionScore = (questionId: string) => {
  // 模拟得分
  return questionId === '1' ? 5 : 0
}

// 主观题打分
const markQuestion = (questionId: string) => {
  const score = markingScores[questionId]
  const comment = markingComments[questionId]
  
  if (score === undefined || score < 0) {
    ElMessage.error('请输入有效的分数')
    return
  }
  
  ElMessage.success('打分成功')
}

// 提交阅卷
const submitMarking = () => {
  ElMessageBox.confirm(
    '确定要提交阅卷结果吗？提交后将无法修改。',
    '提交确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('阅卷结果已提交')
    goBack()
  })
}

// 返回
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchExamRecord()
})
</script>

<style scoped>
.exam-marking {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.exam-info-card {
  margin-bottom: 20px;
}

.exam-info h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 500;
}

.info-item {
  margin-bottom: 10px;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
}

.info-item .value {
  color: #303133;
}

.questions-card {
  margin-bottom: 20px;
}

.question-item {
  border-bottom: 1px solid #ebeef5;
  padding: 20px 0;
}

.question-item:last-child {
  border-bottom: none;
}

.question-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.question-number {
  font-weight: bold;
  font-size: 16px;
}

.question-score {
  color: #409eff;
  font-weight: 500;
}

.question-title {
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 15px;
  color: #303133;
}

.question-options {
  margin-bottom: 15px;
}

.option-item {
  padding: 5px 0;
  color: #606266;
}

.student-answer,
.correct-answer {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.student-answer {
  border-left: 3px solid #409eff;
}

.correct-answer {
  border-left: 3px solid #67c23a;
}

.answer-label {
  font-weight: 500;
  margin-bottom: 5px;
  color: #303133;
}

.answer-content {
  color: #606266;
}

.essay-answer {
  display: block;
  white-space: pre-wrap;
  line-height: 1.6;
}

.marking-section {
  padding: 15px;
  background-color: #fdf6ec;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

.auto-marking {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: #f0f9ff;
  border-radius: 4px;
}

.score-info {
  font-weight: 500;
  color: #303133;
}

.footer-actions {
  text-align: center;
  padding: 20px 0;
}

.footer-actions .el-button {
  margin: 0 10px;
}
</style> 