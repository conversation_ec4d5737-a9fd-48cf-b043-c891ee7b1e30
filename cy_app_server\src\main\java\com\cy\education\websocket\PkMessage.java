package com.cy.education.websocket;

import lombok.Data;

/**
 * PK WebSocket消息
 */
@Data
public class PkMessage {
    
    /**
     * 消息类型
     */
    private String type;
    
    /**
     * 房间ID
     */
    private Long roomId;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 消息数据
     */
    private Object data;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    public PkMessage() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public PkMessage(String type, Long roomId, Integer userId, Object data) {
        this.type = type;
        this.roomId = roomId;
        this.userId = userId;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * 消息类型常量
     */
    public static class Type {
        // 匹配相关
        public static final String MATCH_START = "match_start";           // 开始匹配
        public static final String MATCH_SUCCESS = "match_success";       // 匹配成功
        public static final String MATCH_CANCEL = "match_cancel";         // 取消匹配
        
        // 房间相关
        public static final String ROOM_JOIN = "room_join";               // 加入房间
        public static final String ROOM_LEAVE = "room_leave";             // 离开房间
        public static final String ROOM_READY = "room_ready";             // 准备
        public static final String ROOM_START = "room_start";             // 开始游戏
        public static final String ROOM_UPDATE = "room_update";           // 房间状态更新
        
        // 游戏相关
        public static final String GAME_START = "game_start";             // 开始游戏
        public static final String GAME_QUESTION = "game_question";       // 题目
        public static final String GAME_ANSWER = "game_answer";           // 答题
        public static final String GAME_PROGRESS = "game_progress";       // 进度更新
        public static final String GAME_FINISH = "game_finish";           // 游戏结束
        public static final String GAME_RESULT = "game_result";           // 游戏结果
        
        // 系统相关
        public static final String ERROR = "error";                       // 错误消息
        public static final String HEARTBEAT = "heartbeat";               // 心跳
    }
}
