package com.cy.education.service.points;

import com.cy.education.model.entity.points.PointsRule;

/**
 * 积分规则服务接口
 */
public interface PointsRuleService {

    /**
     * 根据ID查询规则
     *
     * @param id 规则ID
     * @return 规则对象
     */
    PointsRule getById(Integer id);

    /**
     * 根据规则编码查询规则
     *
     * @param code 规则编码
     * @return 规则对象
     */
    PointsRule getByCode(String code);

    /**
     * 增加规则使用次数
     *
     * @param id 规则ID
     * @return 是否成功
     */
    boolean incrementUsedCount(Integer id);
}
