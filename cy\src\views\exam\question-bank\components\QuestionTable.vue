<template>
  <el-card shadow="never">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" size="small">
        <el-form-item label="题目类型">
          <el-select v-model="queryParams.type" placeholder="全部类型" clearable style="width: 150px;">
            <el-option v-for="item in questionTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input v-model="queryParams.keyword" placeholder="题目内容" style="width: 200px;" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="questionList"
      border
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="type" label="题目类型" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getTypeTagType(row.type)">
            {{ getQuestionTypeName(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="题目内容" min-width="300" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-html="formatContent(row.content)"></div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
      <el-table-column label="操作" width="180" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link :icon="View" @click="$emit('preview-question', row)">
            预览
          </el-button>
          <el-button type="primary" link :icon="Edit" @click="$emit('edit-question', row)">
            编辑
          </el-button>
          <el-button type="danger" link :icon="Delete" @click="$emit('delete-question', row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.limit"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :disabled="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  Search,
  Refresh,
  View,
  Edit,
  Delete
} from '@element-plus/icons-vue'

interface QuestionType {
  value: string
  label: string
}

// Props declaration
const props = defineProps({
  questionList: {
    type: Array,
    required: true
  },
  questionTypes: {
    type: Array as () => QuestionType[],
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  }
})

// Emits declaration
const emit = defineEmits([
  'search', 
  'reset', 
  'selection-change', 
  'preview-question', 
  'edit-question', 
  'delete-question',
  'size-change',
  'current-change'
])

// Query parameters
const queryParams = reactive({
  type: '',
  keyword: '',
  page: 1,
  limit: 10
})

// Get question type name
const getQuestionTypeName = (type: string) => {
  const found = props.questionTypes.find((item: QuestionType) => item.value === type)
  return found ? found.label : '未知类型'
}

// Get question type tag style
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'single': 'primary',
    'multiple': 'success',
    'judgment': 'info',
    'fill': 'warning',
    'essay': 'danger'
  }
  return typeMap[type] || 'default'
}

// Format question content
const formatContent = (content: string) => {
  // 添加空值检查
  if (!content) {
    return ''
  }
  // Handle special markers, such as underscores in fill-in-the-blank questions
  return content.replace(/【】/g, '<span style="display:inline-block;min-width:80px;border-bottom:1px solid #000;"></span>')
}

// Search button click handler
const handleSearch = () => {
  emit('search', { ...queryParams })
}

// Reset search handler
const resetSearch = () => {
  // Reset form
  queryParams.type = ''
  queryParams.keyword = ''
  queryParams.page = 1
  
  // Emit reset event
  emit('reset')
}

// Table selection change handler
const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection)
}

// Pagination size change handler
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  queryParams.page = 1
  emit('size-change', val)
}

// Pagination page change handler
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  emit('current-change', val)
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}
</style> 