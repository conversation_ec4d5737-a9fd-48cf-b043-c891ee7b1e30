<template>
  <div class="video-upload">
    <el-upload
      ref="uploadRef"
      action="#"
      :before-upload="beforeUpload"
      :show-file-list="false"
      accept="video/*"
      class="upload-demo"
    >
      <div v-if="videoUrl" class="video-preview">
        <video :src="videoUrl" controls style="width: 100%; max-width: 400px; height: 225px;" />
        <div class="video-overlay">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">点击更换视频</div>
        </div>
      </div>
      <div v-else class="upload-placeholder">
        <el-icon class="upload-icon"><VideoPlay /></el-icon>
        <div class="upload-text">点击上传视频</div>
        <div class="upload-tip">{{ tip }}</div>
      </div>
    </el-upload>
    
    <div v-if="uploading" class="upload-progress">
      <el-progress :percentage="uploadProgress" />
      <div class="progress-text">上传中... {{ uploadProgress }}%</div>
    </div>
    
    <div v-if="videoUrl" class="video-actions">
      <el-button size="small" @click="previewVideo">
        <el-icon><View /></el-icon>
        预览
      </el-button>
      <el-button size="small" @click="copyUrl">
        <el-icon><CopyDocument /></el-icon>
        复制链接
      </el-button>
      <el-button size="small" type="danger" @click="clearVideo">
        <el-icon><Delete /></el-icon>
        删除
      </el-button>
    </div>
    
    <!-- 视频预览对话框 -->
    <el-dialog v-model="previewVisible" title="视频预览" width="80%">
      <div class="preview-container">
        <video :src="videoUrl" controls style="width: 100%; max-height: 500px;" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, VideoPlay, View, CopyDocument, Delete } from '@element-plus/icons-vue'
import { getVideoSignature, uploadToOss } from '@/api/oss'

interface Props {
  modelValue?: string
  maxSize?: number // MB
  tip?: string // 提示文字
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  maxSize: 500,
  tip: '支持 MP4、AVI、MOV、WMV、FLV、WebM 格式，建议大小不超过 500MB'
})

const emit = defineEmits<Emits>()

const uploadRef = ref()
const uploading = ref(false)
const uploadProgress = ref(0)
const previewVisible = ref(false)

const videoUrl = computed({
  get: () => props.modelValue || '',
  set: (value: string) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

const beforeUpload = async (file: File) => {
  // 检查文件类型
  const isVideo = file.type.startsWith('video/')
  if (!isVideo) {
    ElMessage.error('只能上传视频文件!')
    return false
  }

  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`视频大小不能超过 ${props.maxSize}MB!`)
    return false
  }

  uploading.value = true
  uploadProgress.value = 0

  try {
    // 获取OSS上传签名
    const signature = await getVideoSignature()
    
    // 直接上传到OSS
    const result = await uploadToOss(file, signature, (percent) => {
      uploadProgress.value = percent
    })
    
    videoUrl.value = result.url
    uploading.value = false
    uploadProgress.value = 100
    ElMessage.success('上传成功')
    
    return false // 阻止element-plus的默认上传行为
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
    uploading.value = false
    uploadProgress.value = 0
    return false
  }
}

const previewVideo = () => {
  previewVisible.value = true
}

const copyUrl = () => {
  navigator.clipboard.writeText(videoUrl.value).then(() => {
    ElMessage.success('链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

const clearVideo = () => {
  videoUrl.value = ''
  uploadProgress.value = 0
}
</script>

<style scoped>
.video-upload {
  width: 100%;
}

.upload-demo :deep(.el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.upload-demo :deep(.el-upload:hover) {
  border-color: #409eff;
}

.video-preview {
  position: relative;
  width: 400px;
  height: 225px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  color: white;
}

.video-preview:hover .video-overlay {
  opacity: 1;
}

.upload-placeholder {
  width: 400px;
  height: 225px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.upload-tip {
  font-size: 12px;
  color: #c0c4cc;
  text-align: center;
  padding: 0 20px;
}

.upload-progress {
  margin-top: 15px;
  width: 400px;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.video-actions {
  margin-top: 15px;
  display: flex;
  gap: 8px;
}

.preview-container {
  text-align: center;
}
</style>
