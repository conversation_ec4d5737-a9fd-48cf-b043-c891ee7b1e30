# 程远教育培训学员端App需求说明书

## 1. 项目概述

### 1.1 项目背景
程远教育培训学员端App是基于已有管理端系统开发的移动端学习应用，为企业员工提供便捷的在线学习、考试、交流平台。该App与后端管理系统数据互通，实现统一的培训管理体系。

### 1.2 产品定位
- **目标用户**: 企业内部员工/学员
- **使用场景**: 移动端随时随地学习、考试、交流
- **核心价值**: 提供便捷高效的移动学习体验

### 1.3 技术架构
- **前端框架**: React Native / Flutter
- **状态管理**: Redux/MobX 或 Provider
- **网络请求**: axios/dio
- **UI组件库**: Ant Design Mobile / Material UI
- **本地存储**: AsyncStorage / SharedPreferences
- **媒体播放**: react-native-video / video_player

## 2. 底部导航栏设计

### 2.1 底部Tab结构
```
┌─────────┬─────────┬─────────┬─────────┬─────────┐
│  首页   │  学习   │  考试   │  论坛   │  我的   │
│  Home   │ Study   │  Exam   │ Forum   │  Mine   │
│   🏠    │   📚    │   📝    │   💬    │   👤    │
└─────────┴─────────┴─────────┴─────────┴─────────┘
```

### 2.2 各Tab功能说明
- **首页**: 平台公告、轮播图、快捷入口、学习进度概览
- **学习**: 课程列表、学习记录、学习统计
- **考试**: 考试列表、考试记录、成绩查询
- **论坛**: 帖子浏览、发帖回帖、交流互动
- **我的**: 个人信息、积分管理、设置等

## 3. 首页模块

### 3.1 页面布局
```
┌─────────────────────────────────────┐
│  顶部状态栏 + 导航栏                 │
├─────────────────────────────────────┤
│  轮播图区域                         │
├─────────────────────────────────────┤
│  快捷功能区域                       │
├─────────────────────────────────────┤
│  学习统计卡片                       │
├─────────────────────────────────────┤
│  最新公告                           │
├─────────────────────────────────────┤
│  推荐课程                           │
├─────────────────────────────────────┤
│  积分动态                           │
└─────────────────────────────────────┘
```

### 3.2 功能详细说明

#### 3.2.1 顶部导航栏
- **左侧**: 用户头像 + 欢迎语
- **中间**: "程远教育" logo/标题
- **右侧**: 消息通知图标（红点提示）

#### 3.2.2 轮播图区域
- **数据源**: 管理端内容管理 -> 轮播图
- **功能**: 展示重要通知、活动、课程推广
- **交互**: 点击跳转到对应详情页面
- **样式**: 圆角设计，支持自动轮播和手动滑动

#### 3.2.3 快捷功能区域
```
┌─────────┬─────────┬─────────┬─────────┐
│  我的   │  课程   │  考试   │  积分   │
│  学习   │  中心   │  中心   │  商城   │
│   📖    │   🎓    │   📝    │   🎁    │
└─────────┴─────────┴─────────┴─────────┘
```

#### 3.2.4 学习统计卡片
- **学习时长**: 今日/本周/本月学习时长
- **学习进度**: 当前正在学习的课程进度
- **完成课程**: 已完成课程数量
- **积分余额**: 当前积分余额

#### 3.2.5 最新公告
- **显示数量**: 最新3条公告
- **展示内容**: 公告标题、发布时间
- **交互**: 点击查看详情，支持"查看更多"

#### 3.2.6 推荐课程
- **展示形式**: 横向滑动卡片
- **显示内容**: 课程封面、标题、学习人数、评分
- **推荐逻辑**: 基于用户部门、学习历史推荐

#### 3.2.7 积分动态
- **展示内容**: 最近积分获得/消费记录
- **显示数量**: 最新5条记录
- **交互**: 点击跳转到积分记录页面

## 4. 学习模块

### 4.1 课程列表页面

#### 4.1.1 页面布局
```
┌─────────────────────────────────────┐
│  搜索框 + 筛选按钮                   │
├─────────────────────────────────────┤
│  分类标签栏                         │
├─────────────────────────────────────┤
│  课程卡片列表                       │
│  ┌─────────────────────────────────┐ │
│  │ 课程封面 │ 课程标题              │ │
│  │         │ 课程描述              │ │
│  │         │ 进度条 50%            │ │
│  │         │ 📚 10课时 👥 156人    │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 4.1.2 功能特性
- **搜索功能**: 支持课程名称搜索
- **分类筛选**: 按部门、状态、难度筛选
- **排序方式**: 最新、最热、进度
- **学习状态**: 未开始、学习中、已完成
- **下拉刷新**: 支持下拉刷新获取最新数据
- **上拉加载**: 分页加载更多课程

### 4.2 课程详情页面

#### 4.2.1 页面结构
```
┌─────────────────────────────────────┐
│  课程封面 + 基本信息                 │
├─────────────────────────────────────┤
│  课程统计（学习人数、时长、评分）     │
├─────────────────────────────────────┤
│  课程描述                           │
├─────────────────────────────────────┤
│  课程目录（章节 + 课时）             │
├─────────────────────────────────────┤
│  底部操作栏（开始学习/继续学习）     │
└─────────────────────────────────────┘
```

#### 4.2.2 课程目录展示
```
📚 第一章：基础知识
  └ 📄 1.1 概述 (已完成) ✅
  └ 🎥 1.2 视频教程 (学习中) ⏳
  └ 📄 1.3 练习题 (未开始) ⭕

📚 第二章：进阶内容
  └ 🎥 2.1 实操演示 (未开始) ⭕
  └ 📄 2.2 案例分析 (未开始) ⭕
```

### 4.3 学习播放页面

#### 4.3.1 视频播放界面
- **播放器控件**: 播放/暂停、进度条、音量、全屏
- **播放速度**: 支持0.5x、1x、1.25x、1.5x、2x速度
- **字幕支持**: 可开启/关闭字幕
- **章节导航**: 快速跳转到其他章节
- **学习笔记**: 在线记录学习笔记
- **进度同步**: 自动记录学习进度，支持断点续播

#### 4.3.2 文档阅读界面
- **文档展示**: 支持PDF、Word、PPT在线预览
- **阅读进度**: 记录阅读页数和时间
- **书签功能**: 添加重要内容书签
- **搜索功能**: 文档内容搜索
- **字体调节**: 字体大小、亮度调节

### 4.4 学习记录页面

#### 4.4.1 学习统计
- **学习时长**: 今日、本周、本月学习时长统计
- **学习趋势**: 7天/30天学习时长趋势图
- **完成情况**: 课程完成率、课时完成数
- **学习排名**: 部门内学习排名

#### 4.4.2 学习历史
- **最近学习**: 最近学习的课程和资源
- **学习轨迹**: 按时间线展示学习活动
- **笔记记录**: 学习过程中的笔记汇总

## 5. 考试模块

### 5.1 考试列表页面

#### 5.1.1 页面布局
```
┌─────────────────────────────────────┐
│  状态筛选标签                       │
│  [全部][未开始][进行中][已结束]     │
├─────────────────────────────────────┤
│  考试卡片列表                       │
│  ┌─────────────────────────────────┐ │
│  │ 📝 期末考试                    │ │
│  │ 时间：2024-06-01 10:00-12:00   │ │
│  │ 时长：120分钟 | 满分：100分     │ │
│  │ 状态：进行中 🟢                │ │
│  │           [立即参加] [查看详情] │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 5.1.2 考试状态说明
- **未开始**: 显示开始时间，倒计时提醒
- **进行中**: 显示剩余时间，"立即参加"按钮
- **已结束**: 显示成绩，"查看成绩"按钮
- **已过期**: 灰色显示，无法参加

### 5.2 考试详情页面

#### 5.2.1 考试信息
- **考试标题**: 考试名称
- **考试说明**: 考试规则和注意事项
- **时间安排**: 开始时间、结束时间、考试时长
- **分数设置**: 总分、及格分数
- **参考要求**: 参考部门、参考条件
- **考试统计**: 参与人数、完成人数、通过率

#### 5.2.2 操作按钮
- **开始考试**: 进入考试答题界面
- **继续考试**: 继续未完成的考试
- **查看成绩**: 查看已完成考试的成绩

### 5.3 考试答题页面

#### 5.3.1 答题界面
```
┌─────────────────────────────────────┐
│  考试信息栏                         │
│  期末考试 | 剩余时间: 01:45:23      │
├─────────────────────────────────────┤
│  题目导航                           │
│  [1]✅ [2]✅ [3]❌ [4]⭕ [5]⭕      │
├─────────────────────────────────────┤
│  题目内容区域                       │
│  3. 下列哪个选项是正确的？           │
│     A. 选项A                       │
│     B. 选项B ✓                     │
│     C. 选项C                       │
│     D. 选项D                       │
├─────────────────────────────────────┤
│  操作按钮                           │
│  [上一题] [下一题] [标记] [提交]     │
└─────────────────────────────────────┘
```

#### 5.3.2 答题功能
- **题目类型**: 支持单选、多选、判断、填空、简答题
- **题目导航**: 快速跳转到任意题目
- **答案保存**: 自动保存答案，防止数据丢失
- **标记功能**: 标记疑难题目，后续回顾
- **时间提醒**: 考试时间不足提醒
- **防作弊**: 离开页面警告，禁止复制粘贴

### 5.4 考试成绩页面

#### 5.4.1 成绩展示
```
┌─────────────────────────────────────┐
│            考试成绩                 │
│         ━━━━━━━━━━━                  │
│            85分                     │
│          (及格线: 60分)              │
├─────────────────────────────────────┤
│  详细统计                           │
│  总题数: 50题                       │
│  正确: 42题                         │
│  错误: 8题                          │
│  正确率: 84%                        │
├─────────────────────────────────────┤
│  题型分析                           │
│  单选题: 20/25 (80%)                │
│  多选题: 15/15 (100%)               │
│  判断题: 7/10 (70%)                 │
├─────────────────────────────────────┤
│  [查看解析] [重新考试] [分享成绩]   │
└─────────────────────────────────────┘
```

#### 5.4.2 答题解析
- **题目回顾**: 展示所有题目和用户答案
- **正确答案**: 显示标准答案
- **答案解析**: 详细的解题思路和说明
- **错题收藏**: 将错题加入错题本

### 5.5 考试记录页面

#### 5.5.1 记录列表
- **考试历史**: 按时间倒序显示所有考试记录
- **成绩统计**: 平均分、最高分、最低分
- **通过率**: 考试通过情况统计
- **排名信息**: 部门内排名和班级排名

## 6. 论坛模块

### 6.1 论坛首页

#### 6.1.1 页面布局
```
┌─────────────────────────────────────┐
│  搜索框 + 发帖按钮                   │
├─────────────────────────────────────┤
│  分类导航栏                         │
│  [全部][学习交流][问题求助][经验分享] │
├─────────────────────────────────────┤
│  帖子列表                           │
│  ┌─────────────────────────────────┐ │
│  │ 👤 张三 | 学习交流 | 2小时前     │ │
│  │ 如何提高学习效率？               │ │
│  │ 最近在学习新课程，想请教...      │ │
│  │ 💬 12 👍 5 👁 156               │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 6.1.2 功能特性
- **分类浏览**: 按类别筛选帖子
- **搜索功能**: 标题和内容搜索
- **排序方式**: 最新、最热、精华
- **帖子状态**: 置顶、精华、热门标识
- **实时更新**: 下拉刷新获取最新帖子

### 6.2 帖子详情页面

#### 6.2.1 页面结构
```
┌─────────────────────────────────────┐
│  帖子作者信息                       │
│  👤 张三 | VIP学员 | 积分: 1200     │
├─────────────────────────────────────┤
│  帖子标题                           │
│  如何提高学习效率？                 │
├─────────────────────────────────────┤
│  帖子内容                           │
│  最近在学习新课程，遇到一些问题...   │
│  [图片] [视频]                      │
├─────────────────────────────────────┤
│  互动统计                           │
│  👁 156次浏览 💬 12条回复 👍 5个赞  │
├─────────────────────────────────────┤
│  评论列表                           │
│  ┌─────────────────────────────────┐ │
│  │ 👤 李四 | 1小时前               │ │
│  │ 我觉得可以这样...               │ │
│  │ 👍 2 💬 回复                    │ │
│  └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│  底部操作栏                         │
│  [👍 点赞] [💬 评论] [📤 分享] [⭐ 收藏] │
└─────────────────────────────────────┘
```

#### 6.2.2 交互功能
- **点赞功能**: 对帖子和评论点赞/取消点赞
- **评论回复**: 支持多级评论回复
- **图片预览**: 点击图片放大预览
- **内容分享**: 分享到其他平台
- **举报功能**: 举报不当内容

### 6.3 发帖页面

#### 6.3.1 编辑界面
```
┌─────────────────────────────────────┐
│  选择分类                           │
│  [学习交流 ▼]                       │
├─────────────────────────────────────┤
│  帖子标题                           │
│  [ 请输入帖子标题 ]                 │
├─────────────────────────────────────┤
│  帖子内容                           │
│  [ 请输入帖子内容... ]              │
│  │                                 │
│  │                                 │
├─────────────────────────────────────┤
│  附件上传                           │
│  [📷 图片] [📹 视频] [📄 文件]      │
├─────────────────────────────────────┤
│  [取消] [保存草稿] [发布]           │
└─────────────────────────────────────┘
```

#### 6.3.2 发帖功能
- **富文本编辑**: 支持文字格式化
- **多媒体上传**: 图片、视频、文件上传
- **分类选择**: 选择帖子分类
- **草稿保存**: 自动保存草稿
- **标签添加**: 添加相关标签

### 6.4 个人动态页面

#### 6.4.1 我的帖子
- **帖子管理**: 我发布的所有帖子
- **状态筛选**: 已发布、待审核、已删除
- **数据统计**: 总帖数、总浏览量、总点赞数
- **编辑删除**: 对自己的帖子进行编辑删除

#### 6.4.2 我的评论
- **评论记录**: 我发表的所有评论
- **评论统计**: 评论数量、获赞数量
- **快速跳转**: 点击跳转到对应帖子

#### 6.4.3 我的收藏
- **收藏管理**: 我收藏的帖子列表
- **分类整理**: 按分类整理收藏内容
- **取消收藏**: 移除不需要的收藏

## 7. 我的模块

### 7.1 个人中心首页

#### 7.1.1 页面布局
```
┌─────────────────────────────────────┐
│  个人信息卡片                       │
│  ┌─────────────────────────────────┐ │
│  │ 👤 头像  张三 | 技术部          │ │
│  │         学员ID: 2024001         │ │
│  │         💰 积分: 1250           │ │
│  └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│  学习统计卡片                       │
│  ┌─────────────────────────────────┐ │
│  │ 📚 12门    ⏱ 48h     🏆 8张    │ │
│  │ 学习课程   学习时长    获得证书   │ │
│  └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│  功能菜单                           │
│  📚 学习记录      💰 积分管理       │
│  📝 考试记录      🛒 积分商城       │
│  💬 我的帖子      🔖 我的收藏       │
│  ⚙️ 设置          📞 客服           │
└─────────────────────────────────────┘
```

### 7.2 个人信息管理

#### 7.2.1 基本信息
- **头像上传**: 拍照或从相册选择头像
- **姓名**: 显示真实姓名（不可修改）
- **工号**: 显示员工工号（不可修改）
- **部门**: 显示所属部门（不可修改）
- **手机号**: 可修改绑定手机号
- **邮箱**: 可修改绑定邮箱

#### 7.2.2 密码管理
- **修改密码**: 输入原密码和新密码
- **找回密码**: 通过手机或邮箱找回
- **安全验证**: 支持手机验证码验证

### 7.3 积分管理

#### 7.3.1 积分概览
```
┌─────────────────────────────────────┐
│  当前积分余额                       │
│         1,250 分                    │
├─────────────────────────────────────┤
│  本月获得: +280分 | 本月消费: -150分│
├─────────────────────────────────────┤
│  积分明细                           │
│  ┌─────────────────────────────────┐ │
│  │ +20  完成课程学习  2024-06-01   │ │
│  │ -50  兑换礼品     2024-05-30   │ │
│  │ +10  每日签到     2024-05-30   │ │
│  └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│  [积分规则] [兑换记录] [积分商城]   │
└─────────────────────────────────────┘
```

#### 7.3.2 积分获取规则
- **每日签到**: 每天签到获得10积分
- **完成课程**: 完成课程章节获得积分
- **考试通过**: 考试及格获得积分奖励
- **论坛活动**: 发帖、回帖获得积分
- **推荐好友**: 推荐新用户注册获得积分

#### 7.3.3 积分商城
- **商品分类**: 实物礼品、虚拟奖品、优惠券
- **兑换流程**: 选择商品 → 确认兑换 → 填写地址 → 提交订单
- **兑换记录**: 查看历史兑换记录和物流状态

### 7.4 学习档案

#### 7.4.1 学习统计
- **学习总时长**: 累计学习时间统计
- **完成课程数**: 已完成的课程数量
- **证书获得**: 获得的结业证书
- **学习排名**: 部门和全公司排名

#### 7.4.2 学习轨迹
- **学习日历**: 按日期显示学习活动
- **学习曲线**: 学习时长趋势图表
- **知识地图**: 已掌握的知识点分布

### 7.5 考试档案

#### 7.5.1 考试成绩
- **成绩单**: 所有考试成绩列表
- **统计分析**: 平均分、最高分、通过率
- **成绩趋势**: 成绩变化趋势图
- **错题集**: 历次考试错题汇总

#### 7.5.2 证书管理
- **电子证书**: 考试合格证书展示
- **证书下载**: 支持证书PDF下载
- **证书验证**: 证书真伪验证功能

### 7.6 设置中心

#### 7.6.1 通用设置
- **消息推送**: 学习提醒、考试通知开关
- **自动播放**: 视频自动播放设置
- **下载质量**: 离线下载视频质量选择
- **流量保护**: 非WiFi环境播放提醒

#### 7.6.2 隐私设置
- **学习记录**: 学习记录对他人可见性
- **排名显示**: 是否参与排名展示
- **个人资料**: 个人信息对他人可见程度

#### 7.6.3 账号安全
- **登录记录**: 查看登录历史记录
- **设备管理**: 管理已登录设备
- **账号注销**: 申请注销账号功能

## 8. 特色功能设计

### 8.1 离线学习
- **课程下载**: WiFi环境下载课程到本地
- **离线播放**: 无网络环境下观看已下载内容
- **进度同步**: 网络恢复后自动同步学习进度
- **存储管理**: 查看和清理本地存储空间

### 8.2 学习提醒
- **智能提醒**: 根据学习习惯智能推送提醒
- **课程提醒**: 新课程上线通知
- **考试提醒**: 考试开始前定时提醒
- **打卡提醒**: 每日学习打卡提醒

### 8.3 社交互动
- **学习群组**: 同部门学员学习交流群
- **学习PK**: 与同事进行学习时长PK
- **学习分享**: 分享学习成果到朋友圈
- **互助问答**: 学员间互助解答问题

### 8.4 AI学习助手
- **智能推荐**: 基于学习行为推荐相关课程
- **学习路径**: AI生成个性化学习路径
- **答疑助手**: AI自动回答常见问题
- **学习报告**: AI生成个人学习分析报告

## 9. 数据统计与分析

### 9.1 学习分析
- **学习行为**: 学习时间分布、频次统计
- **知识掌握**: 各知识点掌握程度分析
- **学习效果**: 学习时长与考试成绩关联分析
- **改进建议**: 基于数据提供学习改进建议

### 9.2 考试分析
- **知识点分析**: 各知识点答题准确率
- **能力评估**: 综合能力水平评估
- **错误分析**: 常见错误类型分析
- **提升方案**: 针对性的能力提升方案

## 10. 技术实现要点

### 10.1 性能优化
- **图片懒加载**: 列表图片懒加载优化
- **视频预加载**: 智能预加载下一节视频
- **缓存策略**: 合理的数据缓存机制
- **网络优化**: 请求合并和数据压缩

### 10.2 用户体验
- **响应式设计**: 适配不同屏幕尺寸
- **流畅动画**: 页面切换和交互动画
- **手势操作**: 支持常见手势操作
- **无障碍设计**: 支持无障碍访问功能

### 10.3 数据安全
- **数据加密**: 敏感数据传输加密
- **身份验证**: JWT Token安全验证
- **权限控制**: 细粒度权限控制
- **隐私保护**: 用户隐私数据保护

## 11. 开发计划

### 11.1 开发阶段
- **第一阶段**: 基础框架搭建和用户认证
- **第二阶段**: 学习模块和考试模块开发
- **第三阶段**: 论坛模块和个人中心开发
- **第四阶段**: 特色功能和性能优化
- **第五阶段**: 测试、部署和上线

### 11.2 测试计划
- **功能测试**: 各模块功能完整性测试
- **性能测试**: App性能和响应速度测试
- **兼容性测试**: 不同设备和系统版本测试
- **用户测试**: 真实用户使用体验测试

这份需求说明书涵盖了学员端App的完整功能设计，与现有管理端系统形成完整的教育培训生态系统。通过移动端便捷的学习体验，能够大大提升企业培训的效果和员工的学习积极性。 