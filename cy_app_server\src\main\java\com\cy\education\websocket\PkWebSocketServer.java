package com.cy.education.websocket;

import com.cy.education.service.PkService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * PK WebSocket服务器
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/pk/{userId}")
public class PkWebSocketServer {
    
    /**
     * 静态变量，用来记录当前在线连接数
     */
    private static int onlineCount = 0;
    
    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的WebSocket对象
     */
    private static CopyOnWriteArraySet<PkWebSocketServer> webSocketSet = new CopyOnWriteArraySet<>();
    
    /**
     * 用户ID与WebSocket的映射
     */
    private static ConcurrentHashMap<Integer, PkWebSocketServer> userWebSocketMap = new ConcurrentHashMap<>();
    
    /**
     * 房间ID与用户ID集合的映射
     */
    private static ConcurrentHashMap<Long, CopyOnWriteArraySet<Integer>> roomUserMap = new ConcurrentHashMap<>();
    
    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    
    /**
     * 接收userId
     */
    private Integer userId;
    
    /**
     * 当前用户所在的房间ID
     */
    private Long currentRoomId;
    
    private static PkService pkService;
    private static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 由于WebSocket是多例的，Spring无法直接注入，需要通过静态方法获取
     */
    public static void setPkService(PkService pkService) {
        PkWebSocketServer.pkService = pkService;
    }

    public static PkService getPkService() {
        return pkService;
    }
    
    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") Integer userId) {
        this.session = session;
        this.userId = userId;
        webSocketSet.add(this);
        userWebSocketMap.put(userId, this);
        addOnlineCount();
        
        log.info("用户{}连接PK WebSocket，当前在线人数为：{}", userId, getOnlineCount());
        
        try {
            sendMessage(new PkMessage(PkMessage.Type.HEARTBEAT, null, userId, "连接成功"));
        } catch (IOException e) {
            log.error("用户{}连接异常", userId, e);
        }
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        webSocketSet.remove(this);
        userWebSocketMap.remove(userId);
        
        // 从房间中移除用户
        if (currentRoomId != null) {
            removeUserFromRoom(currentRoomId, userId);
        }
        
        subOnlineCount();
        log.info("用户{}断开PK WebSocket连接，当前在线人数为：{}", userId, getOnlineCount());
    }
    
    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("用户{}发送消息：{}", userId, message);
        
        try {
            PkMessage pkMessage = objectMapper.readValue(message, PkMessage.class);
            handleMessage(pkMessage);
        } catch (Exception e) {
            log.error("处理用户{}消息异常", userId, e);
            try {
                sendError("消息格式错误");
            } catch (IOException ioException) {
                log.error("发送错误消息失败", ioException);
            }
        }
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("用户{}发生错误", userId, error);
    }
    
    /**
     * 处理消息
     */
    private void handleMessage(PkMessage message) throws IOException {
        String type = message.getType();
        
        switch (type) {
            case PkMessage.Type.MATCH_START:
                handleMatchStart(message);
                break;
            case PkMessage.Type.MATCH_CANCEL:
                handleMatchCancel(message);
                break;
            case PkMessage.Type.ROOM_JOIN:
                handleRoomJoin(message);
                break;
            case PkMessage.Type.ROOM_LEAVE:
                handleRoomLeave(message);
                break;
            case PkMessage.Type.ROOM_READY:
                handleRoomReady(message);
                break;
            case PkMessage.Type.GAME_ANSWER:
                handleGameAnswer(message);
                break;
            case PkMessage.Type.HEARTBEAT:
                handleHeartbeat(message);
                break;
            default:
                sendError("未知消息类型：" + type);
        }
    }
    
    /**
     * 处理开始匹配
     */
    private void handleMatchStart(PkMessage message) throws IOException {
        try {
            if (pkService != null && message.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) message.getData();
                Integer bankId = (Integer) data.get("bankId");
                Integer questionCount = (Integer) data.get("questionCount");
                Integer timeLimit = (Integer) data.get("timeLimit");

                Map<String, Object> result = pkService.startMatch(userId, bankId, questionCount, timeLimit);

                if ((Boolean) result.get("success")) {
                    if (result.containsKey("roomId")) {
                        // 匹配成功，发送成功消息
                        sendMessage(new PkMessage(PkMessage.Type.MATCH_SUCCESS,
                            (Long) result.get("roomId"), userId, result));
                    } else {
                        // 进入匹配队列
                        sendMessage(new PkMessage(PkMessage.Type.MATCH_START, null, userId,
                            "开始匹配，请等待对手"));
                    }
                } else {
                    sendError((String) result.get("message"));
                }
            }
        } catch (Exception e) {
            log.error("处理开始匹配失败", e);
            sendError("匹配失败：" + e.getMessage());
        }
    }
    
    /**
     * 处理取消匹配
     */
    private void handleMatchCancel(PkMessage message) throws IOException {
        try {
            if (pkService != null) {
                Map<String, Object> result = pkService.cancelMatch(userId);
                sendMessage(new PkMessage(PkMessage.Type.MATCH_CANCEL, null, userId, result));
            }
        } catch (Exception e) {
            log.error("处理取消匹配失败", e);
            sendError("取消匹配失败：" + e.getMessage());
        }
    }
    
    /**
     * 处理加入房间
     */
    private void handleRoomJoin(PkMessage message) throws IOException {
        Long roomId = message.getRoomId();
        addUserToRoom(roomId, userId);
        this.currentRoomId = roomId;
        
        // 通知房间内其他用户
        broadcastToRoom(roomId, new PkMessage(PkMessage.Type.ROOM_UPDATE, roomId, userId, "用户加入房间"));
    }
    
    /**
     * 处理离开房间
     */
    private void handleRoomLeave(PkMessage message) throws IOException {
        Long roomId = message.getRoomId();
        removeUserFromRoom(roomId, userId);
        this.currentRoomId = null;
        
        // 通知房间内其他用户
        broadcastToRoom(roomId, new PkMessage(PkMessage.Type.ROOM_UPDATE, roomId, userId, "用户离开房间"));
    }
    
    /**
     * 处理准备
     */
    private void handleRoomReady(PkMessage message) throws IOException {
        try {
            if (pkService != null) {
                Long roomId = message.getRoomId();
                Map<String, Object> result = pkService.readyGame(roomId, userId);

                // 通知房间内所有用户
                broadcastToRoom(roomId, new PkMessage(PkMessage.Type.ROOM_UPDATE, roomId, userId, result));

                // 如果所有人都准备好了，开始游戏
                if ((Boolean) result.getOrDefault("allReady", false)) {
                    pkService.startGame(roomId);
                }
            }
        } catch (Exception e) {
            log.error("处理准备失败", e);
            sendError("准备失败：" + e.getMessage());
        }
    }
    
    /**
     * 处理答题
     */
    private void handleGameAnswer(PkMessage message) throws IOException {
        try {
            if (pkService != null && message.getData() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) message.getData();
                Long roomId = message.getRoomId();
                Integer questionId = (Integer) data.get("questionId");
                Integer questionOrder = (Integer) data.get("questionOrder");
                String userAnswer = (String) data.get("userAnswer");
                Integer answerTime = (Integer) data.get("answerTime");

                Map<String, Object> result = pkService.submitAnswer(roomId, userId, questionId,
                    questionOrder, userAnswer, answerTime);

                // 通知房间内所有用户进度更新
                broadcastToRoom(roomId, new PkMessage(PkMessage.Type.GAME_PROGRESS, roomId, userId, result));
            }
        } catch (Exception e) {
            log.error("处理答题失败", e);
            sendError("答题失败：" + e.getMessage());
        }
    }
    
    /**
     * 处理心跳
     */
    private void handleHeartbeat(PkMessage message) throws IOException {
        sendMessage(new PkMessage(PkMessage.Type.HEARTBEAT, null, userId, "pong"));
    }
    
    /**
     * 发送消息
     */
    public void sendMessage(PkMessage message) throws IOException {
        this.session.getBasicRemote().sendText(objectMapper.writeValueAsString(message));
    }
    
    /**
     * 发送错误消息
     */
    public void sendError(String error) throws IOException {
        sendMessage(new PkMessage(PkMessage.Type.ERROR, null, userId, error));
    }
    
    /**
     * 向指定用户发送消息
     */
    public static void sendMessageToUser(Integer userId, PkMessage message) {
        PkWebSocketServer webSocket = userWebSocketMap.get(userId);
        if (webSocket != null) {
            try {
                webSocket.sendMessage(message);
            } catch (IOException e) {
                log.error("向用户{}发送消息失败", userId, e);
            }
        }
    }
    
    /**
     * 向房间内所有用户广播消息
     */
    public static void broadcastToRoom(Long roomId, PkMessage message) {
        CopyOnWriteArraySet<Integer> userIds = roomUserMap.get(roomId);
        if (userIds != null) {
            for (Integer userId : userIds) {
                sendMessageToUser(userId, message);
            }
        }
    }
    
    /**
     * 将用户添加到房间
     */
    private void addUserToRoom(Long roomId, Integer userId) {
        roomUserMap.computeIfAbsent(roomId, k -> new CopyOnWriteArraySet<>()).add(userId);
    }
    
    /**
     * 从房间中移除用户
     */
    private void removeUserFromRoom(Long roomId, Integer userId) {
        CopyOnWriteArraySet<Integer> userIds = roomUserMap.get(roomId);
        if (userIds != null) {
            userIds.remove(userId);
            if (userIds.isEmpty()) {
                roomUserMap.remove(roomId);
            }
        }
    }
    
    public static synchronized int getOnlineCount() {
        return onlineCount;
    }
    
    public static synchronized void addOnlineCount() {
        PkWebSocketServer.onlineCount++;
    }
    
    public static synchronized void subOnlineCount() {
        PkWebSocketServer.onlineCount--;
    }
}
