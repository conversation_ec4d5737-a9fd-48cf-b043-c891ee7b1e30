<template>
  <div class="news-table">

    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="newsList"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="封面图" width="120" align="center">
        <template #default="{ row }">
          <el-image
            style="width: 80px; height: 45px"
            :src="row.coverUrl"
            :preview-src-list="[row.coverUrl]"
            fit="cover"
            v-if="row.coverUrl"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <span v-else>无封面</span>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" min-width="180" show-overflow-tooltip />
      <el-table-column prop="publishTime" label="发布时间" width="160" align="center" />
      <el-table-column prop="viewCount" label="阅读量" width="100" align="center" />
      <el-table-column label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status ? 'success' : 'info'">
            {{ row.status ? '已发布' : '草稿' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否置顶" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.isTop ? 'danger' : 'info'">
            {{ row.isTop ? '置顶' : '普通' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link :icon="Edit" @click="$emit('edit', row)">
            编辑
          </el-button>
          <el-button type="primary" link :icon="View" @click="$emit('preview', row)">
            预览
          </el-button>
          <el-button
            type="primary"
            link
            :icon="row.status ? Hide : View"
            @click="$emit('toggle-status', row)"
          >
            {{ row.status ? '下架' : '发布' }}
          </el-button>
          <el-button
            type="warning"
            link
            :icon="row.isTop ? Bottom : Top"
            @click="$emit('toggle-top', row)"
          >
            {{ row.isTop ? '取消置顶' : '置顶' }}
          </el-button>
          <el-button type="danger" link :icon="Delete" @click="$emit('delete', row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="$emit('size-change', $event)"
        @current-change="$emit('current-change', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus, Edit, Delete, View, Refresh, Hide, Picture, Top, Bottom } from '@element-plus/icons-vue'

interface NewsItem {
  id: number
  title: string
  coverUrl?: string
  content: string
  publishTime: string
  viewCount: number
  status: number
  isTop: boolean
  createTime: string
}

defineProps({
  newsList: {
    type: Array as () => NewsItem[],
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits([
  'add', 
  'edit', 
  'delete', 
  'preview',
  'toggle-status',
  'toggle-top',
  'selection-change',
  'refresh',
  'size-change',
  'current-change'
])

const currentPage = ref(1)
const pageSize = ref(10)
const tableRef = ref()

const handleSelectionChange = (selection: NewsItem[]) => {
  emit('selection-change', selection)
}
</script>

<style scoped>
.news-table {
  margin-top: 20px;
}

.table-toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style> 