package com.cy.education.service;

import com.cy.education.model.entity.ExamBank;
import com.cy.education.model.params.ExamBankParams;
import com.cy.education.model.params.ExamBankQueryParams;
import com.cy.education.model.vo.ExamBankVO;
import com.cy.education.model.vo.PageResponse;

/**
 * 题库服务接口
 */
public interface ExamBankService {

    /**
     * 分页查询题库列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResponse<ExamBankVO> listBanks(ExamBankQueryParams params);

    /**
     * 获取题库详情
     *
     * @param id 题库ID
     * @return 题库详情
     */
    ExamBankVO getBankDetail(Integer id);

    /**
     * 创建题库
     *
     * @param params 题库参数
     * @param createdBy 创建人ID
     * @return 题库ID
     */
    Integer createBank(ExamBankParams params, String createdBy);

    /**
     * 更新题库
     *
     * @param id 题库ID
     * @param params 题库参数
     * @return 是否成功
     */
    boolean updateBank(Integer id, ExamBankParams params);

    /**
     * 删除题库
     *
     * @param id 题库ID
     * @return 是否成功
     */
    boolean deleteBank(Integer id);
} 