package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.Student;
import com.cy.education.model.params.ChangePasswordParams;
import com.cy.education.model.params.LoginParams;
import com.cy.education.model.vo.LoginResponseVO;
import com.cy.education.repository.StudentMapper;
import com.cy.education.service.AuthService;
import com.cy.education.utils.FileUploadUtil;
import com.cy.education.utils.JwtTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证服务实现
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private StudentMapper studentMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private FileUploadUtil fileUploadUtil;

    @Override
    public LoginResponseVO login(LoginParams loginParams) {
        // 1. 查询用户
        Student student = studentMapper.selectOne(
                new LambdaQueryWrapper<Student>()
                        .eq(Student::getUsername, loginParams.getUsername()));

        if (student == null) {
            throw new BadCredentialsException("用户名或密码不正确");
        }

        // 2. 校验密码
        if (!passwordEncoder.matches(loginParams.getPassword(), student.getPassword())) {
            throw new BadCredentialsException("用户名或密码不正确");
        }

        // 3. 检查账号状态
        if (student.getStatus() != 1) {
            throw new BusinessException("账号已被禁用，请联系学生");
        }

        // 5. 生成Token，存储Integer类型的ID
        Map<String, Object> claims = new HashMap<>();
        claims.put("id", student.getId());
        claims.put("type", "student");
        claims.put("realName", student.getName()); // 添加真实姓名到token中
        String token = jwtTokenUtil.generateToken(student.getUsername(), claims);

        // 6. 更新登录时间
        student.setLastLoginTime(LocalDateTime.now());
        studentMapper.updateById(student);

        // 7. 构建返回对象
        return LoginResponseVO.builder()
                .token(token)
                .user(LoginResponseVO.UserInfoVO.builder()
                        .id(student.getId())
                        .username(student.getUsername())
                        .name(student.getName())
                        .avatar(student.getAvatar())
                        .build())
                .build();
    }

    @Override
    public boolean logout() {
        // 基于JWT的无状态认证，服务端不需要处理登出逻辑
        // 客户端只需要删除本地存储的token即可
        return true;
    }

    @Override
    public boolean changePassword(ChangePasswordParams params) {
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BadCredentialsException("用户未登录");
        }

        String username = authentication.getName();

        // 查询学生信息
        Student student = studentMapper.selectOne(
                new LambdaQueryWrapper<Student>()
                        .eq(Student::getUsername, username));

        if (student == null) {
            throw new BadCredentialsException("用户不存在");
        }

        // 验证当前密码
        if (!passwordEncoder.matches(params.getOldPassword(), student.getPassword())) {
            throw new BusinessException("当前密码不正确");
        }

        // 检查新密码是否与当前密码相同
        if (passwordEncoder.matches(params.getNewPassword(), student.getPassword())) {
            throw new BusinessException("新密码不能与当前密码相同");
        }

        // 更新密码
        Student updateStudent = new Student();
        updateStudent.setId(student.getId());
        updateStudent.setPassword(passwordEncoder.encode(params.getNewPassword()));
        updateStudent.setUpdatedAt(LocalDateTime.now());

        int result = studentMapper.updateById(updateStudent);

        log.info("修改密码: username={}, result={}", username, result);

        return result > 0;
    }

    @Override
    public String uploadAvatar(MultipartFile file) {
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BadCredentialsException("用户未登录");
        }

        String username = authentication.getName();

        // 上传文件
        String avatarUrl = fileUploadUtil.uploadAvatar(file);

        // 更新用户头像
        Student student = studentMapper.selectOne(
                new LambdaQueryWrapper<Student>()
                        .eq(Student::getUsername, username));

        if (student != null) {
            Student updateStudent = new Student();
            updateStudent.setId(student.getId());
            updateStudent.setAvatar(avatarUrl);
            updateStudent.setUpdatedAt(LocalDateTime.now());

            studentMapper.updateById(updateStudent);
        }

        return avatarUrl;
    }
}
