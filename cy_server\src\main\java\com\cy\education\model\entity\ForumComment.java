package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 论坛评论实体类
 */
@Data
@TableName("forum_comments")
public class ForumComment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评论ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 帖子ID
     */
    private Integer postId;

    /**
     * 父评论ID
     */
    private Integer parentId;

    /**
     * 作者ID
     */
    private Integer authorId;

    /**
     * 状态：0待审核，1已通过，2已拒绝，3已删除
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 作者名称（非数据库字段）
     */
    @TableField(exist = false)
    private String author;

    /**
     * 作者头像（非数据库字段）
     */
    @TableField(exist = false)
    private String authorAvatar;

    /**
     * 帖子标题（非数据库字段）
     */
    @TableField(exist = false)
    private String postTitle;

    /**
     * 子评论列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<ForumComment> children;
} 