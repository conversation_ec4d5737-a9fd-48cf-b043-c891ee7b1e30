<template>
  <div class="paper-management">
    <div class="page-header">
      <h2>试卷管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAddPaper">新增试卷</el-button>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索试卷名称"
          clearable
          style="width: 220px; margin-left: 16px"
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="paperList"
        border
        style="width: 100%"
      >
        <el-table-column prop="title" label="试卷名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="questionCount" label="题目数量" width="100" align="center" />
        <el-table-column prop="totalScore" label="总分" width="100" align="center" />
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isPublished ? 'success' : 'info'">
              {{ row.isPublished ? '已发布' : '未发布' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" align="center" />
        <el-table-column prop="usageCount" label="使用次数" width="100" align="center" />
        <el-table-column label="操作" width="340" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="handlePreview(row)">预览</el-button>
            <el-button type="primary" link @click="handleEdit(row)" :disabled="row.isPublished">编辑</el-button>
            <el-button
              :type="row.isPublished ? 'warning' : 'success'"
              link
              @click="handleTogglePublish(row)"
            >
              {{ row.isPublished ? '取消发布' : '发布' }}
            </el-button>
            <el-button type="primary" link @click="handleStatistics(row)">统计</el-button>
            <el-button type="success" link @click="handleExportPaper(row)">导出Word</el-button>
            <el-button type="primary" link @click="handleCopy(row)">复制</el-button>
            <el-button type="danger" link @click="handleDelete(row)" :disabled="row.isPublished">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 试卷统计弹窗 -->
    <el-dialog
      v-model="statisticsVisible"
      title="试卷统计"
      width="800px"
      destroy-on-close
    >
      <div class="statistics-container">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="试卷名称">{{ currentPaper.title }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentPaper.createTime }}</el-descriptions-item>
          <el-descriptions-item label="题目总数">{{ currentPaper.questionCount }}</el-descriptions-item>
          <el-descriptions-item label="试卷总分">{{ currentPaper.totalScore }}</el-descriptions-item>
          <el-descriptions-item label="使用次数">{{ currentPaper.usageCount }}</el-descriptions-item>
          <el-descriptions-item label="及格率">{{ statisticsData.passRate }}%</el-descriptions-item>
        </el-descriptions>

        <div class="chart-wrapper">
          <div class="chart-item">
            <h3>题型分布</h3>
            <div ref="questionTypeChartRef" class="chart"></div>
          </div>
          <div class="chart-item">
            <h3>分值分布</h3>
            <div ref="difficultyChartRef" class="chart"></div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 试卷预览弹窗 -->
    <el-dialog
      v-model="previewVisible"
      title="试卷预览"
      width="800px"
      destroy-on-close
    >
      <div class="preview-container">
        <div class="paper-header">
          <h1>{{ currentPaper.title }}</h1>
          <div class="paper-info">
            <span>总分：{{ currentPaper.totalScore }}分</span>
            <span>考试时长：{{ currentPaper.duration }}分钟</span>
          </div>
        </div>

        <div class="question-sections" v-if="previewData.questions && previewData.questions.length > 0">
          <div v-for="(question, questionIndex) in previewData.questions" :key="questionIndex" class="question-item">
            <div class="question-header">
              <span class="question-index">{{ questionIndex + 1 }}.</span>
              <span class="question-type">[{{ getQuestionTypeName(question.type) }}]</span>
              <span class="question-score">{{ question.score }}分</span>
            </div>
            <div class="question-content" v-html="question.content"></div>
            
            <!-- 选择题选项 -->
            <div v-if="['single', 'multiple'].includes(question.type)" class="question-options">
              <div v-for="(option, optionIndex) in question.options" :key="optionIndex" class="option-item">
                <span class="option-label">{{ String.fromCharCode(65 + optionIndex) }}.</span>
                <span class="option-content" v-html="option.content"></span>
              </div>
            </div>

            <!-- 判断题选项 -->
            <div v-if="question.type === 'judge'" class="question-options">
              <div class="option-item">
                <span class="option-label">A.</span>
                <span class="option-content">正确</span>
              </div>
              <div class="option-item">
                <span class="option-label">B.</span>
                <span class="option-content">错误</span>
              </div>
            </div>

            <!-- 答案和解析 -->
            <div class="question-answer">
              <div class="answer-label">【参考答案】</div>
              <div class="answer-content" v-html="formatAnswer(question)"></div>
            </div>
            <div class="question-analysis" v-if="question.analysis">
              <div class="analysis-label">【解析】</div>
              <div class="analysis-content" v-html="question.analysis"></div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 试卷导出对话框 -->
    <ExamPaperExportDialog
      v-model="exportDialogVisible"
      :paper-info="{
        id: currentExportPaper?.id || 0,
        title: currentExportPaper?.title || '',
        questionCount: currentExportPaper?.questionCount || 0,
        totalScore: currentExportPaper?.totalScore || 0
      }"
      @success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getPaperList, getPaperDetail, createPaper, updatePaper, deletePaper, publishPaper, type Paper } from '@/api/exam'
import ExamPaperExportDialog from '@/components/ImportExport/ExamPaperExportDialog.vue'
import * as echarts from 'echarts/core'
import { PieChart } from 'echarts/charts'
import { TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 定义问题类型
type QuestionType = 'single' | 'multiple' | 'judgment' | 'judge' | 'fill' | 'essay';

// 定义预览数据类型
interface PreviewQuestion {
  type: QuestionType;
  content: string;
  score: number;
  options?: any;
  answer: string | string[];
  analysis?: string;
}

// 注册必要的echarts组件
echarts.use([PieChart, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer])

const router = useRouter()

// 列表数据和分页
const loading = ref(false)
const paperList = ref<Paper[]>([])
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 导出相关
const exportDialogVisible = ref(false)
const currentExportPaper = ref<Paper | null>(null)

// 弹窗控制
const statisticsVisible = ref(false)
const previewVisible = ref(false)

// 当前操作的试卷
const currentPaper = ref<{
  id: string;
  title: string;
  questionCount: number;
  totalScore: number;
  createTime: string;
  usageCount: number;
  duration: number;
  isPublished: boolean;
}>({
  id: '',
  title: '',
  questionCount: 0,
  totalScore: 0,
  createTime: '',
  usageCount: 0,
  duration: 0,
  isPublished: false
})

// 统计数据
const statisticsData = reactive({
  passRate: 0,
  questionTypeData: [] as { value: number; name: string }[],
  scoreData: [] as { value: number; name: string }[]
})

// 预览数据
const previewData = ref<{
  questions: PreviewQuestion[];
}>({
  questions: []
})

// 图表引用
const questionTypeChartRef = ref(null)
const difficultyChartRef = ref(null)
let questionTypeChart = null
let difficultyChart = null

// 获取试卷列表
const fetchPaperList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      keyword: searchKeyword.value || undefined,
      sortBy: 'updatedAt',
      sortOrder: 'desc' as 'desc' | 'asc'
    }
    
    const res = await getPaperList(params)
    paperList.value = res.list.map(paper => ({
      ...paper,
      questionCount: paper.questionCount || 0, // 使用后端返回的题目数量
      createTime: formatDateTime(paper.createdAt),
      usageCount: Math.floor(Math.random() * 50), // 模拟使用次数
      isPublished: paper.isPublished
    }))
    
    total.value = res.total
  } catch (error) {
    console.error('获取试卷列表失败:', error)
    ElMessage.error('获取试卷列表失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchPaperList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchPaperList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchPaperList()
}

// 新增试卷
const handleAddPaper = () => {
  router.push('/exam/paper/edit/0')
}

// 编辑试卷
const handleEdit = (row: Paper) => {
  router.push(`/exam/paper/edit/${row.id}`)
}

// 导出试卷
const handleExportPaper = (row: Paper) => {
  currentExportPaper.value = row
  exportDialogVisible.value = true
}

// 导出成功回调
const handleExportSuccess = () => {
  ElMessage.success('试卷导出成功')
}

// 删除试卷
const handleDelete = (row: Paper) => {
  ElMessageBox.confirm(
    `确定要删除试卷"${row.title}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deletePaper(row.id)
      const index = paperList.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        paperList.value.splice(index, 1)
        total.value--
      }
      ElMessage.success('删除成功')
    } catch (error) {
      console.error('删除试卷失败:', error)
      ElMessage.error('删除试卷失败')
    }
  }).catch(() => {})
}

// 复制试卷
const handleCopy = (row: Paper) => {
  ElMessageBox.confirm(
    `确定要复制试卷"${row.title}"吗？`,
    '复制确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(async () => {
    try {
      // 获取试卷详情
      const paperDetail = await getPaperDetail(row.id)
      
      // 创建新试卷，复制原试卷内容
      const newPaper = {
        title: `${paperDetail.title} (副本)`,
        description: paperDetail.description,
        totalScore: paperDetail.totalScore,
        passingScore: paperDetail.passingScore,
        duration: paperDetail.duration,
        isPublished: false // 默认未发布
      }
      
      // 创建新试卷
      const result = await createPaper(newPaper)
      
      // 刷新试卷列表
      fetchPaperList()
      
      ElMessage.success('复制成功')
    } catch (error) {
      console.error('复制试卷失败:', error)
      ElMessage.error('复制试卷失败')
    }
  }).catch(() => {})
}

// 统计试卷
const handleStatistics = async (row: Paper) => {
  // 使用类型断言转换currentPaper值的类型
  currentPaper.value = {
    id: row.id,
    title: row.title,
    questionCount: row.questions?.length || 0,
    totalScore: row.totalScore,
    createTime: formatDateTime(row.createdAt),
    usageCount: 0, // 使用次数目前没有后端数据
    duration: row.duration,
    isPublished: row.isPublished
  };
  
  statisticsVisible.value = true
  
  try {
    // 获取试卷详情
    const paperDetail = await getPaperDetail(row.id)
  
    // 计算通过率（使用模拟数据，实际项目中可以从后端获取）
  statisticsData.passRate = 75
    
    // 计算题型分布
    const questionTypes: Record<string, { name: string; count: number }> = {
      'single': { name: '单选题', count: 0 },
      'multiple': { name: '多选题', count: 0 },
      'judgment': { name: '判断题', count: 0 },
      'fill': { name: '填空题', count: 0 },
      'essay': { name: '简答题', count: 0 }
    }
    
    // 计算分值分布
    const scoreMap: Record<number, number> = {}
    
    // 统计题型和分值
    if (paperDetail.questions && paperDetail.questions.length > 0) {
      paperDetail.questions.forEach(item => {
        // 题型统计
        const questionType = item.question.type
        if (questionTypes[questionType]) {
          questionTypes[questionType].count++
        }
        
        // 分值统计
        const score = item.score
        if (score) {
          scoreMap[score] = (scoreMap[score] || 0) + 1
        }
      })
    }
    
    // 转换为图表数据格式
    statisticsData.questionTypeData = Object.values(questionTypes)
      .filter(item => item.count > 0)
      .map(item => ({
        value: item.count,
        name: item.name
      }))
      
    // 转换分值数据为图表格式
    statisticsData.scoreData = Object.entries(scoreMap)
      .map(([score, count]) => ({
        value: count,
        name: `${score}分`
      }))
      .sort((a, b) => parseInt(a.name) - parseInt(b.name))
  
  // 等待DOM渲染完成后初始化图表
  nextTick(() => {
    initCharts()
  })
  } catch (error) {
    console.error('获取试卷统计数据失败:', error)
    ElMessage.error('获取试卷统计数据失败')
  }
}

// 初始化统计图表
const initCharts = () => {
  // 题型分布图表
  if (questionTypeChartRef.value) {
    questionTypeChart = echarts.init(questionTypeChartRef.value)
    questionTypeChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        data: statisticsData.questionTypeData.map(item => item.name)
      },
      series: [
        {
          name: '题型分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: statisticsData.questionTypeData
        }
      ]
    })
  }
  
  // 分值分布图表（原难度分布）
  if (difficultyChartRef.value) {
    difficultyChart = echarts.init(difficultyChartRef.value)
    difficultyChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        data: statisticsData.scoreData.map(item => item.name)
      },
      series: [
        {
          name: '分值分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: statisticsData.scoreData
        }
      ]
    })
  }
}

// 预览试卷
const handlePreview = async (row: Paper) => {
  // 使用类型断言转换currentPaper值的类型
  currentPaper.value = {
    id: row.id,
    title: row.title,
    questionCount: row.questions?.length || 0,
    totalScore: row.totalScore,
    createTime: formatDateTime(row.createdAt),
    usageCount: 0, // 使用次数目前没有后端数据
    duration: row.duration,
    isPublished: row.isPublished
  };
  
  previewVisible.value = true
  
  try {
    // 获取试卷详情
    const paperDetail = await getPaperDetail(row.id)
  
    // 设置预览数据
  previewData.value = {
      questions: paperDetail.questions.map(item => ({
        type: item.question.type as QuestionType,
        content: item.question.title,
        score: item.score,
        options: item.question.type === 'single' || item.question.type === 'multiple' 
          ? JSON.parse(item.question.options.toString())
          : null,
        answer: item.question.correctAnswer,
        analysis: item.question.explanation
      }))
    }
  } catch (error) {
    console.error('获取试卷预览数据失败:', error)
    ElMessage.error('获取试卷预览数据失败')
    previewVisible.value = false
  }
}

// 获取题型名称
const getQuestionTypeName = (type: QuestionType): string => {
  const typeMap: Record<QuestionType, string> = {
    'single': '单选题',
    'multiple': '多选题',
    'judgment': '判断题',
    'judge': '判断题',
    'fill': '填空题',
    'essay': '简答题'
  }
  return typeMap[type] || '未知题型'
}

// 格式化答案显示
const formatAnswer = (question: PreviewQuestion): string => {
  switch (question.type) {
    case 'single':
    case 'multiple':
      if (typeof question.answer === 'string') {
      return question.answer.split('').map(letter => {
        const index = letter.charCodeAt(0) - 65
          return `${letter}. ${question.options?.[index]?.content || ''}`
      }).join('<br>')
      }
      return '';
    case 'judgment':
    case 'judge':
      return question.answer === 'true' ? '正确' : '错误'
    default:
      return typeof question.answer === 'string' ? question.answer : question.answer.join(', ')
  }
}

// 发布/取消发布试卷
const handleTogglePublish = async (row: Paper) => {
  try {
    await publishPaper(row.id, !row.isPublished)
    const index = paperList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      paperList.value[index].isPublished = !row.isPublished
    }
    ElMessage.success(row.isPublished ? '取消发布成功' : '发布成功')
  } catch (error) {
    console.error('发布/取消发布试卷失败:', error)
    ElMessage.error('发布/取消发布试卷失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchPaperList()
})
</script>

<style scoped>
.paper-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 统计弹窗样式 */
.statistics-container {
  padding: 10px;
}

.chart-wrapper {
  display: flex;
  margin-top: 20px;
}

.chart-item {
  flex: 1;
  text-align: center;
}

.chart-item h3 {
  margin-bottom: 10px;
  font-weight: 500;
}

.chart {
  height: 300px;
}

/* 预览弹窗样式 */
.preview-container {
  padding: 10px;
  max-height: 70vh;
  overflow-y: auto;
}

.paper-header {
  text-align: center;
  margin-bottom: 30px;
}

.paper-header h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

.paper-info {
  display: flex;
  justify-content: center;
  gap: 20px;
  color: #666;
}

.question-sections {
  margin-bottom: 30px;
}

.question-item {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #eee;
}

.question-header {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.question-index {
  font-weight: bold;
  margin-right: 5px;
}

.question-type {
  color: #409eff;
  margin-right: 10px;
  font-size: 14px;
}

.question-score {
  color: #ff9900;
  font-size: 14px;
}

.question-content {
  margin-bottom: 10px;
  line-height: 1.6;
}

.question-options {
  margin-left: 20px;
  margin-bottom: 15px;
}

.option-item {
  margin-bottom: 8px;
  display: flex;
}

.option-label {
  margin-right: 8px;
  font-weight: 500;
}

.question-answer, .question-analysis {
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

.answer-label, .analysis-label {
  color: #409eff;
  font-weight: 500;
  margin-bottom: 5px;
}

.answer-content, .analysis-content {
  color: #333;
  line-height: 1.6;
}
</style> 