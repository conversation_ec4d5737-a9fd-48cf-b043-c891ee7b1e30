package com.cy.education.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.PointsProduct;
import com.cy.education.model.params.PointsProductQueryParam;
import com.cy.education.model.vo.ApiResponse;

import java.util.Map;

/**
 * 积分商品服务接口
 */
public interface PointsProductService {

    /**
     * 分页查询积分商品
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<PointsProduct> page(PointsProductQueryParam param);
    
    /**
     * 根据ID查询商品
     *
     * @param id 商品ID
     * @return 商品对象
     */
    PointsProduct getById(Integer id);
    
    /**
     * 创建商品
     *
     * @param product 商品对象
     * @return 创建结果
     */
    ApiResponse<Map<String, Object>> create(PointsProduct product);

    /**
     * 更新商品
     *
     * @param id 商品ID
     * @param product 商品对象
     * @return 更新结果
     */
    ApiResponse<Map<String, Object>> update(Integer id, PointsProduct product);

    /**
     * 删除商品
     *
     * @param id 商品ID
     * @return 删除结果
     */
    ApiResponse<Map<String, Object>> delete(Integer id);

    /**
     * 更新商品状态
     *
     * @param id 商品ID
     * @param status 状态
     * @return 操作结果
     */
    ApiResponse<Map<String, Object>> updateStatus(Integer id, String status);

    /**
     * 补充商品库存
     *
     * @param id 商品ID
     * @param count 补充数量
     * @return 操作结果
     */
    ApiResponse<Map<String, Object>> restock(Integer id, Integer count);
    
    /**
     * 减少商品库存
     *
     * @param id 商品ID
     * @param count 减少数量
     * @return 是否成功
     */
    boolean decreaseStock(Integer id, Integer count);
    
    /**
     * 增加商品库存
     *
     * @param id 商品ID
     * @param count 增加数量
     * @return 是否成功
     */
    boolean increaseStock(Integer id, Integer count);
    
    /**
     * 获取商品统计数据
     *
     * @return 统计数据
     */
    Map<String, Object> getStatistics();
} 