package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.mapper.PkRoomMapper;
import com.cy.education.mapper.PkParticipantMapper;
import com.cy.education.mapper.PkAnswerMapper;
import com.cy.education.mapper.PkQuestionMapper;
import com.cy.education.repository.ExamQuestionMapper;
import com.cy.education.model.entity.*;
import com.cy.education.model.entity.exam.ExamQuestion;
import com.cy.education.service.PkService;
import com.cy.education.websocket.PkWebSocketServer;
import com.cy.education.websocket.PkMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * PK服务实现
 */
@Slf4j
@Service
public class PkServiceImpl implements PkService {
    
    @Autowired
    private PkRoomMapper pkRoomMapper;
    
    @Autowired
    private PkParticipantMapper pkParticipantMapper;
    
    @Autowired
    private PkAnswerMapper pkAnswerMapper;
    
    @Autowired
    private PkQuestionMapper pkQuestionMapper;
    
    @Autowired
    private ExamQuestionMapper examQuestionMapper;
    
    /**
     * 匹配队列：题库ID -> 等待匹配的用户列表
     */
    private static final ConcurrentHashMap<Integer, List<Integer>> matchQueue = new ConcurrentHashMap<>();
    
    /**
     * 定时任务执行器
     */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    @Override
    public Map<String, Object> startMatch(Integer userId, Integer bankId, Integer questionCount, Integer timeLimit) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查用户是否已在匹配队列中
            for (List<Integer> queue : matchQueue.values()) {
                if (queue.contains(userId)) {
                    result.put("success", false);
                    result.put("message", "您已在匹配队列中");
                    return result;
                }
            }
            
            // 获取该题库的匹配队列
            List<Integer> queue = matchQueue.computeIfAbsent(bankId, k -> new ArrayList<>());
            
            synchronized (queue) {
                // 如果队列中有其他用户，直接匹配
                if (!queue.isEmpty()) {
                    Integer opponentId = queue.remove(0);
                    
                    // 创建房间并开始游戏
                    PkRoom room = createRoom(userId, bankId, questionCount, timeLimit);
                    
                    // 添加参与者
                    addParticipant(room.getId(), userId, 1);
                    addParticipant(room.getId(), opponentId, 2);
                    
                    // 生成题目
                    generateQuestions(room.getId(), bankId, questionCount);
                    
                    // 通知双方匹配成功
                    Map<String, Object> matchData = new HashMap<>();
                    matchData.put("roomId", room.getId());
                    matchData.put("roomCode", room.getRoomCode());
                    matchData.put("opponent", getOpponentInfo(opponentId));
                    
                    PkWebSocketServer.sendMessageToUser(userId, 
                        new PkMessage(PkMessage.Type.MATCH_SUCCESS, room.getId(), userId, matchData));
                    
                    matchData.put("opponent", getOpponentInfo(userId));
                    PkWebSocketServer.sendMessageToUser(opponentId, 
                        new PkMessage(PkMessage.Type.MATCH_SUCCESS, room.getId(), opponentId, matchData));
                    
                    result.put("success", true);
                    result.put("roomId", room.getId());
                    result.put("message", "匹配成功");
                } else {
                    // 加入匹配队列
                    queue.add(userId);
                    
                    // 设置匹配超时（30秒）
                    scheduler.schedule(() -> {
                        synchronized (queue) {
                            if (queue.remove(userId)) {
                                PkWebSocketServer.sendMessageToUser(userId, 
                                    new PkMessage(PkMessage.Type.MATCH_CANCEL, null, userId, "匹配超时"));
                            }
                        }
                    }, 30, TimeUnit.SECONDS);
                    
                    result.put("success", true);
                    result.put("message", "开始匹配，请等待对手");
                }
            }
        } catch (Exception e) {
            log.error("开始匹配失败", e);
            result.put("success", false);
            result.put("message", "匹配失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> cancelMatch(Integer userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean removed = false;
            for (List<Integer> queue : matchQueue.values()) {
                synchronized (queue) {
                    if (queue.remove(userId)) {
                        removed = true;
                        break;
                    }
                }
            }
            
            result.put("success", removed);
            result.put("message", removed ? "取消匹配成功" : "您不在匹配队列中");
        } catch (Exception e) {
            log.error("取消匹配失败", e);
            result.put("success", false);
            result.put("message", "取消匹配失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public PkRoom createRoom(Integer creatorId, Integer bankId, Integer questionCount, Integer timeLimit) {
        PkRoom room = new PkRoom();
        room.setRoomCode(generateRoomCode());
        room.setBankId(bankId);
        room.setQuestionCount(questionCount);
        room.setTimeLimit(timeLimit);
        room.setStatus("waiting");
        room.setCreatorId(creatorId);
        room.setCreatedAt(LocalDateTime.now());
        
        pkRoomMapper.insert(room);
        return room;
    }
    
    @Override
    public Map<String, Object> joinRoom(String roomCode, Integer userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查找房间
            QueryWrapper<PkRoom> roomQuery = new QueryWrapper<>();
            roomQuery.eq("room_code", roomCode);
            PkRoom room = pkRoomMapper.selectOne(roomQuery);
            
            if (room == null) {
                result.put("success", false);
                result.put("message", "房间不存在");
                return result;
            }
            
            if (!"waiting".equals(room.getStatus())) {
                result.put("success", false);
                result.put("message", "房间已开始或已结束");
                return result;
            }
            
            // 检查房间人数
            QueryWrapper<PkParticipant> participantQuery = new QueryWrapper<>();
            participantQuery.eq("room_id", room.getId());
            List<PkParticipant> participants = pkParticipantMapper.selectList(participantQuery);
            
            if (participants.size() >= 2) {
                result.put("success", false);
                result.put("message", "房间已满");
                return result;
            }
            
            // 检查用户是否已在房间中
            boolean userInRoom = participants.stream().anyMatch(p -> p.getUserId().equals(userId));
            if (userInRoom) {
                result.put("success", false);
                result.put("message", "您已在房间中");
                return result;
            }
            
            // 添加参与者
            int position = participants.isEmpty() ? 1 : 2;
            addParticipant(room.getId(), userId, position);
            
            // 如果房间满了，生成题目
            if (participants.size() == 1) {
                generateQuestions(room.getId(), room.getBankId(), room.getQuestionCount());
            }
            
            result.put("success", true);
            result.put("room", getRoomInfo(room.getId()));
            result.put("message", "加入房间成功");
            
        } catch (Exception e) {
            log.error("加入房间失败", e);
            result.put("success", false);
            result.put("message", "加入房间失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> leaveRoom(Long roomId, Integer userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 删除参与者
            QueryWrapper<PkParticipant> query = new QueryWrapper<>();
            query.eq("room_id", roomId).eq("user_id", userId);
            pkParticipantMapper.delete(query);
            
            // 检查房间是否还有其他参与者
            QueryWrapper<PkParticipant> participantQuery = new QueryWrapper<>();
            participantQuery.eq("room_id", roomId);
            List<PkParticipant> participants = pkParticipantMapper.selectList(participantQuery);
            
            if (participants.isEmpty()) {
                // 删除房间
                pkRoomMapper.deleteById(roomId);
            }
            
            result.put("success", true);
            result.put("message", "离开房间成功");
            
        } catch (Exception e) {
            log.error("离开房间失败", e);
            result.put("success", false);
            result.put("message", "离开房间失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> readyGame(Long roomId, Integer userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 更新参与者准备状态
            QueryWrapper<PkParticipant> query = new QueryWrapper<>();
            query.eq("room_id", roomId).eq("user_id", userId);
            PkParticipant participant = pkParticipantMapper.selectOne(query);
            
            if (participant != null) {
                participant.setIsReady(true);
                pkParticipantMapper.updateById(participant);
                
                // 检查是否所有人都准备好了
                QueryWrapper<PkParticipant> allQuery = new QueryWrapper<>();
                allQuery.eq("room_id", roomId);
                List<PkParticipant> allParticipants = pkParticipantMapper.selectList(allQuery);
                
                boolean allReady = allParticipants.size() == 2 && 
                                 allParticipants.stream().allMatch(PkParticipant::getIsReady);
                
                if (allReady) {
                    // 开始游戏
                    startGame(roomId);
                }
                
                result.put("success", true);
                result.put("allReady", allReady);
                result.put("message", "准备成功");
            } else {
                result.put("success", false);
                result.put("message", "参与者不存在");
            }
            
        } catch (Exception e) {
            log.error("准备游戏失败", e);
            result.put("success", false);
            result.put("message", "准备失败：" + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public Map<String, Object> startGame(Long roomId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 更新房间状态
            PkRoom room = pkRoomMapper.selectById(roomId);
            room.setStatus("playing");
            room.setStartedAt(LocalDateTime.now());
            pkRoomMapper.updateById(room);
            
            // 获取第一题
            QueryWrapper<PkQuestion> questionQuery = new QueryWrapper<>();
            questionQuery.eq("room_id", roomId).eq("question_order", 1);
            PkQuestion pkQuestion = pkQuestionMapper.selectOne(questionQuery);
            
            if (pkQuestion != null) {
                ExamQuestion question = examQuestionMapper.selectById(pkQuestion.getQuestionId());
                
                Map<String, Object> gameData = new HashMap<>();
                gameData.put("question", question);
                gameData.put("questionOrder", 1);
                gameData.put("totalQuestions", room.getQuestionCount());
                gameData.put("timeLimit", room.getTimeLimit());
                
                // 通知所有参与者开始游戏
                PkWebSocketServer.broadcastToRoom(roomId, 
                    new PkMessage(PkMessage.Type.GAME_START, roomId, null, gameData));
                
                result.put("success", true);
                result.put("gameData", gameData);
            } else {
                result.put("success", false);
                result.put("message", "题目不存在");
            }
            
        } catch (Exception e) {
            log.error("开始游戏失败", e);
            result.put("success", false);
            result.put("message", "开始游戏失败：" + e.getMessage());
        }
        
        return result;
    }
    
    // 其他辅助方法...
    
    /**
     * 生成房间码
     */
    private String generateRoomCode() {
        return String.valueOf(100000 + new Random().nextInt(900000));
    }
    
    /**
     * 添加参与者
     */
    private void addParticipant(Long roomId, Integer userId, Integer position) {
        PkParticipant participant = new PkParticipant();
        participant.setRoomId(roomId);
        participant.setUserId(userId);
        participant.setPosition(position);
        participant.setScore(0);
        participant.setCorrectCount(0);
        participant.setWrongCount(0);
        participant.setAnswerTime(0);
        participant.setIsReady(false);
        participant.setIsFinished(false);
        participant.setJoinedAt(LocalDateTime.now());
        
        pkParticipantMapper.insert(participant);
    }
    
    /**
     * 生成题目
     */
    private void generateQuestions(Long roomId, Integer bankId, Integer questionCount) {
        // 随机获取题目
        QueryWrapper<ExamQuestion> query = new QueryWrapper<>();
        query.eq("bank_id", bankId).eq("deleted", 0);
        query.last("ORDER BY RAND() LIMIT " + questionCount);

        List<ExamQuestion> questions = examQuestionMapper.selectList(query);

        for (int i = 0; i < questions.size(); i++) {
            PkQuestion pkQuestion = new PkQuestion();
            pkQuestion.setRoomId(roomId);
            pkQuestion.setQuestionId(questions.get(i).getId());
            pkQuestion.setQuestionOrder(i + 1);
            pkQuestion.setCreatedAt(LocalDateTime.now());

            pkQuestionMapper.insert(pkQuestion);
        }
    }
    
    /**
     * 获取对手信息
     */
    private Map<String, Object> getOpponentInfo(Integer userId) {
        // 这里应该查询用户信息，简化处理
        Map<String, Object> info = new HashMap<>();
        info.put("userId", userId);
        info.put("name", "用户" + userId);
        return info;
    }
    
    @Override
    @Transactional
    public Map<String, Object> submitAnswer(Long roomId, Integer userId, Integer questionId,
                                          Integer questionOrder, String userAnswer, Integer answerTime) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取正确答案
            ExamQuestion question = examQuestionMapper.selectById(questionId);
            boolean isCorrect = question.getCorrectAnswer().equals(userAnswer);

            // 计算得分
            int score = isCorrect ? 10 : 0;

            // 保存答题记录
            PkAnswer answer = new PkAnswer();
            answer.setRoomId(roomId);
            answer.setUserId(userId);
            answer.setQuestionId(questionId);
            answer.setQuestionOrder(questionOrder);
            answer.setUserAnswer(userAnswer);
            answer.setIsCorrect(isCorrect);
            answer.setAnswerTime(answerTime);
            answer.setScore(score);
            answer.setAnsweredAt(LocalDateTime.now());

            pkAnswerMapper.insert(answer);

            // 更新参与者统计
            updateParticipantStats(roomId, userId, isCorrect, score, answerTime);

            // 检查是否完成所有题目
            PkRoom room = pkRoomMapper.selectById(roomId);
            QueryWrapper<PkAnswer> answerQuery = new QueryWrapper<>();
            answerQuery.eq("room_id", roomId).eq("user_id", userId);
            long answeredCountLong = pkAnswerMapper.selectCount(answerQuery);
            int answeredCount = (int) answeredCountLong;

            boolean isFinished = answeredCount >= room.getQuestionCount();

            if (isFinished) {
                // 标记用户完成
                QueryWrapper<PkParticipant> participantQuery = new QueryWrapper<>();
                participantQuery.eq("room_id", roomId).eq("user_id", userId);
                PkParticipant participant = pkParticipantMapper.selectOne(participantQuery);
                participant.setIsFinished(true);
                participant.setFinishedAt(LocalDateTime.now());
                pkParticipantMapper.updateById(participant);

                // 检查是否所有人都完成了
                QueryWrapper<PkParticipant> allQuery = new QueryWrapper<>();
                allQuery.eq("room_id", roomId);
                List<PkParticipant> allParticipants = pkParticipantMapper.selectList(allQuery);

                boolean allFinished = allParticipants.stream().allMatch(PkParticipant::getIsFinished);

                if (allFinished) {
                    // 结束游戏
                    finishGame(roomId);
                }
            }

            // 准备下一题
            Map<String, Object> progressData = new HashMap<>();
            progressData.put("userId", userId);
            progressData.put("questionOrder", questionOrder);
            progressData.put("isCorrect", isCorrect);
            progressData.put("score", score);
            progressData.put("isFinished", isFinished);

            if (!isFinished && questionOrder < room.getQuestionCount()) {
                // 获取下一题
                QueryWrapper<PkQuestion> nextQuestionQuery = new QueryWrapper<>();
                nextQuestionQuery.eq("room_id", roomId).eq("question_order", questionOrder + 1);
                PkQuestion nextPkQuestion = pkQuestionMapper.selectOne(nextQuestionQuery);

                if (nextPkQuestion != null) {
                    ExamQuestion nextQuestion = examQuestionMapper.selectById(nextPkQuestion.getQuestionId());
                    progressData.put("nextQuestion", nextQuestion);
                    progressData.put("nextQuestionOrder", questionOrder + 1);
                }
            }

            // 通知房间内所有用户进度更新
            PkWebSocketServer.broadcastToRoom(roomId,
                new PkMessage(PkMessage.Type.GAME_PROGRESS, roomId, userId, progressData));

            result.put("success", true);
            result.put("isCorrect", isCorrect);
            result.put("score", score);
            result.put("isFinished", isFinished);

        } catch (Exception e) {
            log.error("提交答案失败", e);
            result.put("success", false);
            result.put("message", "提交答案失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getRoomInfo(Long roomId) {
        Map<String, Object> result = new HashMap<>();

        try {
            PkRoom room = pkRoomMapper.selectById(roomId);
            if (room == null) {
                result.put("success", false);
                result.put("message", "房间不存在");
                return result;
            }

            // 获取参与者信息
            QueryWrapper<PkParticipant> participantQuery = new QueryWrapper<>();
            participantQuery.eq("room_id", roomId);
            List<PkParticipant> participants = pkParticipantMapper.selectList(participantQuery);

            result.put("success", true);
            result.put("room", room);
            result.put("participants", participants);

        } catch (Exception e) {
            log.error("获取房间信息失败", e);
            result.put("success", false);
            result.put("message", "获取房间信息失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getGameResult(Long roomId) {
        Map<String, Object> result = new HashMap<>();

        try {
            PkRoom room = pkRoomMapper.selectById(roomId);
            QueryWrapper<PkParticipant> participantQuery = new QueryWrapper<>();
            participantQuery.eq("room_id", roomId);
            List<PkParticipant> participants = pkParticipantMapper.selectList(participantQuery);

            // 确定胜负
            PkParticipant winner = null;
            if (participants.size() == 2) {
                PkParticipant p1 = participants.get(0);
                PkParticipant p2 = participants.get(1);

                if (p1.getScore() > p2.getScore()) {
                    winner = p1;
                } else if (p2.getScore() > p1.getScore()) {
                    winner = p2;
                } else {
                    // 分数相同，比较时间
                    if (p1.getAnswerTime() < p2.getAnswerTime()) {
                        winner = p1;
                    } else if (p2.getAnswerTime() < p1.getAnswerTime()) {
                        winner = p2;
                    }
                    // 否则平局
                }
            }

            result.put("success", true);
            result.put("room", room);
            result.put("participants", participants);
            result.put("winner", winner);

        } catch (Exception e) {
            log.error("获取游戏结果失败", e);
            result.put("success", false);
            result.put("message", "获取游戏结果失败：" + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getUserPkHistory(Integer userId, Integer page, Integer size) {
        Map<String, Object> result = new HashMap<>();

        try {
            Page<PkParticipant> pageParam = new Page<>(page, size);
            QueryWrapper<PkParticipant> query = new QueryWrapper<>();
            query.eq("user_id", userId);
            query.orderByDesc("joined_at");

            Page<PkParticipant> pageResult = pkParticipantMapper.selectPage(pageParam, query);

            result.put("success", true);
            result.put("list", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("page", page);
            result.put("size", size);

        } catch (Exception e) {
            log.error("获取PK历史失败", e);
            result.put("success", false);
            result.put("message", "获取PK历史失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 更新参与者统计
     */
    private void updateParticipantStats(Long roomId, Integer userId, boolean isCorrect, int score, int answerTime) {
        QueryWrapper<PkParticipant> query = new QueryWrapper<>();
        query.eq("room_id", roomId).eq("user_id", userId);
        PkParticipant participant = pkParticipantMapper.selectOne(query);

        if (participant != null) {
            participant.setScore(participant.getScore() + score);
            participant.setAnswerTime(participant.getAnswerTime() + answerTime);

            if (isCorrect) {
                participant.setCorrectCount(participant.getCorrectCount() + 1);
            } else {
                participant.setWrongCount(participant.getWrongCount() + 1);
            }

            pkParticipantMapper.updateById(participant);
        }
    }

    /**
     * 结束游戏
     */
    private void finishGame(Long roomId) {
        // 更新房间状态
        PkRoom room = pkRoomMapper.selectById(roomId);
        room.setStatus("finished");
        room.setFinishedAt(LocalDateTime.now());
        pkRoomMapper.updateById(room);

        // 获取游戏结果
        Map<String, Object> gameResult = getGameResult(roomId);

        // 通知所有参与者游戏结束
        PkWebSocketServer.broadcastToRoom(roomId,
            new PkMessage(PkMessage.Type.GAME_FINISH, roomId, null, gameResult));
    }
}
