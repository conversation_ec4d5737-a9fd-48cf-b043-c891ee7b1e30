<template>
  <view class="chart-container">
    <view class="chart-header">
      <text class="chart-title">最近7天考试趋势</text>
    </view>
    <view class="chart-content">
      <canvas
          id="timeTrendChart"
          canvas-id="timeTrendChart"
          class="chart-canvas"
          @touchend="touchEnd"
          @touchmove="touchMove"
          @touchstart="touchStart"
      ></canvas>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {ref, onMounted, watch} from 'vue'
import uCharts from '@qiun/ucharts'
import {lineChartConfig} from './chartConfig'

interface TimeTrendData {
  dailyCompletions: Record<string, number>
  dailyPasses: Record<string, number>
}

interface Props {
  data: TimeTrendData
}

const props = defineProps<Props>()

const chartInstance = ref<any>(null)

// 图表配置
const chartConfig = {
  type: 'line',
  context: null as any,
  width: 350,
  height: 200,
  categories: [],
  series: [
    {
      name: '完成人数',
      data: [],
      color: '#667eea'
    },
    {
      name: '通过人数',
      data: [],
      color: '#10b981'
    }
  ],
  ...lineChartConfig
}

// 更新图表数据
const updateChartData = () => {
  if (!props.data) return

  // 生成最近7天的日期
  const dates = []
  const completions = []
  const passes = []

  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    const dateStr = date.toISOString().split('T')[0]
    dates.push(`${date.getMonth() + 1}/${date.getDate()}`)

    completions.push(props.data.dailyCompletions[dateStr] || 0)
    passes.push(props.data.dailyPasses[dateStr] || 0)
  }

  chartConfig.categories = dates
  chartConfig.series[0].data = completions
  chartConfig.series[1].data = passes

  // 计算Y轴数据
  const maxValue = Math.max(...completions, ...passes)
  const yAxisData = []
  for (let i = 0; i <= 5; i++) {
    yAxisData.push(Math.round(maxValue * i / 5))
  }
  chartConfig.yAxis.data = yAxisData

  if (chartInstance.value) {
    chartInstance.value.updateData({
      categories: chartConfig.categories,
      series: chartConfig.series,
      yAxis: chartConfig.yAxis
    })
  }
}

// 初始化图表
const initChart = () => {
  const query = uni.createSelectorQuery()
  query.select('#timeTrendChart')
      .fields({node: true, size: true})
      .exec((res) => {
        if (res[0]) {
          const canvas = res[0].node
          const ctx = canvas.getContext('2d')

          chartConfig.context = ctx
          chartConfig.width = res[0].width
          chartConfig.height = res[0].height

          chartInstance.value = new uCharts(chartConfig)
          updateChartData()
        }
      })
}

// 触摸事件处理
const touchStart = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.touchLegend(e)
    chartInstance.value.showToolTip(e)
  }
}

const touchMove = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.scroll(e)
  }
}

const touchEnd = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.touchLegend(e)
  }
}

// 监听数据变化
watch(() => props.data, updateChartData, {deep: true})

onMounted(() => {
  setTimeout(() => {
    initChart()
  }, 100)
})
</script>

<style lang="scss" scoped>
.chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.chart-content {
  display: flex;
  justify-content: center;
}

.chart-canvas {
  width: 350px;
  height: 200px;
}
</style>
