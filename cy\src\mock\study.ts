import { mockResponse, paginateData } from './utils'
import type { StudyRecordVO, StudyRecordQueryParams, DepartmentStudyStatistics } from '@/api/study'

// 模拟学习记录数据
const studyRecords: StudyRecordVO[] = [
  {
    id: 1,
    userId: 1,
    courseId: 1,
    lessonId: 1,
    resourceId: 1,
    resourceType: 'video',
    progress: 75,
    duration: 1800,
    completed: 0,
    lastPosition: 1350,
    lastStudyTime: '2025-06-27 10:30:00',
    createdAt: '2025-06-27 09:00:00',
    updatedAt: '2025-06-27 10:30:00',
    studentName: '张三',
    departmentName: '技术部',
    courseName: 'Vue.js 实战教程',
    lessonName: '第一章：Vue基础'
  },
  {
    id: 2,
    userId: 1,
    courseId: 1,
    lessonId: 2,
    resourceId: 2,
    resourceType: 'article',
    progress: 100,
    duration: 600,
    completed: 1,
    lastPosition: 0,
    lastStudyTime: '2025-06-27 11:15:00',
    createdAt: '2025-06-27 11:00:00',
    updatedAt: '2025-06-27 11:15:00',
    studentName: '张三',
    departmentName: '技术部',
    courseName: 'Vue.js 实战教程',
    lessonName: '第二章：组件开发'
  },
  {
    id: 3,
    userId: 2,
    courseId: 1,
    lessonId: 1,
    resourceId: 1,
    resourceType: 'video',
    progress: 100,
    duration: 2400,
    completed: 1,
    lastPosition: 2400,
    lastStudyTime: '2025-06-27 09:45:00',
    createdAt: '2025-06-27 09:00:00',
    updatedAt: '2025-06-27 09:45:00',
    studentName: '李四',
    departmentName: '产品部',
    courseName: 'Vue.js 实战教程',
    lessonName: '第一章：Vue基础'
  },
  {
    id: 4,
    userId: 2,
    courseId: 2,
    lessonId: 3,
    resourceId: 3,
    resourceType: 'video',
    progress: 45,
    duration: 900,
    completed: 0,
    lastPosition: 405,
    lastStudyTime: '2025-06-27 14:20:00',
    createdAt: '2025-06-27 14:00:00',
    updatedAt: '2025-06-27 14:20:00',
    studentName: '李四',
    departmentName: '产品部',
    courseName: 'React 开发指南',
    lessonName: '第一章：React入门'
  },
  {
    id: 5,
    userId: 3,
    courseId: 2,
    lessonId: 3,
    resourceId: 3,
    resourceType: 'video',
    progress: 30,
    duration: 540,
    completed: 0,
    lastPosition: 270,
    lastStudyTime: '2025-06-27 16:10:00',
    createdAt: '2025-06-27 16:00:00',
    updatedAt: '2025-06-27 16:10:00',
    studentName: '王五',
    departmentName: '设计部',
    courseName: 'React 开发指南',
    lessonName: '第一章：React入门'
  },
  {
    id: 6,
    userId: 3,
    courseId: 3,
    lessonId: 4,
    resourceId: 4,
    resourceType: 'file',
    progress: 100,
    duration: 300,
    completed: 1,
    lastPosition: 0,
    lastStudyTime: '2025-06-27 13:25:00',
    createdAt: '2025-06-27 13:00:00',
    updatedAt: '2025-06-27 13:25:00',
    studentName: '王五',
    departmentName: '设计部',
    courseName: 'UI设计基础',
    lessonName: '第一章：设计原理'
  }
]

// 模拟部门统计数据
const departmentStats: DepartmentStudyStatistics[] = [
  {
    departmentId: 1,
    departmentName: '技术部',
    totalStudents: 15,
    activeStudents: 12,
    totalStudyTime: 45600, // 12.67小时
    averageStudyTime: 3040, // 50.67分钟
    completionRate: 78,
    topCourses: [
      { courseId: 1, courseName: 'Vue.js 实战教程', studyCount: 8 },
      { courseId: 2, courseName: 'React 开发指南', studyCount: 6 },
      { courseId: 4, courseName: 'Node.js 后端开发', studyCount: 5 }
    ]
  },
  {
    departmentId: 2,
    departmentName: '产品部',
    totalStudents: 8,
    activeStudents: 6,
    totalStudyTime: 28800, // 8小时
    averageStudyTime: 3600, // 60分钟
    completionRate: 65,
    topCourses: [
      { courseId: 5, courseName: '产品设计思维', studyCount: 6 },
      { courseId: 1, courseName: 'Vue.js 实战教程', studyCount: 4 },
      { courseId: 6, courseName: '用户体验设计', studyCount: 3 }
    ]
  },
  {
    departmentId: 3,
    departmentName: '设计部',
    totalStudents: 6,
    activeStudents: 5,
    totalStudyTime: 21600, // 6小时
    averageStudyTime: 3600, // 60分钟
    completionRate: 82,
    topCourses: [
      { courseId: 3, courseName: 'UI设计基础', studyCount: 5 },
      { courseId: 6, courseName: '用户体验设计', studyCount: 4 },
      { courseId: 7, courseName: 'Figma设计工具', studyCount: 3 }
    ]
  },
  {
    departmentId: 4,
    departmentName: '运营部',
    totalStudents: 10,
    activeStudents: 7,
    totalStudyTime: 32400, // 9小时
    averageStudyTime: 3240, // 54分钟
    completionRate: 70,
    topCourses: [
      { courseId: 8, courseName: '数字营销策略', studyCount: 7 },
      { courseId: 9, courseName: '内容运营实战', studyCount: 5 },
      { courseId: 10, courseName: '数据分析基础', studyCount: 4 }
    ]
  }
]

// 模拟活跃学员数据
const activeStudents = [
  {
    userId: 1,
    userName: '张三',
    departmentName: '技术部',
    totalStudyTime: 7200, // 2小时
    lastStudyTime: '2025-06-27 11:15:00'
  },
  {
    userId: 2,
    userName: '李四',
    departmentName: '产品部',
    totalStudyTime: 6300, // 1.75小时
    lastStudyTime: '2025-06-27 14:20:00'
  },
  {
    userId: 3,
    userName: '王五',
    departmentName: '设计部',
    totalStudyTime: 5400, // 1.5小时
    lastStudyTime: '2025-06-27 16:10:00'
  },
  {
    userId: 4,
    userName: '赵六',
    departmentName: '技术部',
    totalStudyTime: 4800, // 1.33小时
    lastStudyTime: '2025-06-27 15:30:00'
  },
  {
    userId: 5,
    userName: '钱七',
    departmentName: '运营部',
    totalStudyTime: 4200, // 1.17小时
    lastStudyTime: '2025-06-27 12:45:00'
  }
]

// 获取学习记录列表
export function mockGetStudyRecordList(url: string) {
  const urlObj = new URL(url, 'http://localhost')
  const params = Object.fromEntries(urlObj.searchParams.entries())
  
  const page = parseInt(params.page) || 1
  const size = parseInt(params.size) || 20
  const keyword = params.keyword || ''
  const departmentId = params.departmentId ? parseInt(params.departmentId) : null
  const courseId = params.courseId ? parseInt(params.courseId) : null
  const completed = params.completed !== undefined ? parseInt(params.completed) : null
  
  // 过滤数据
  let filteredRecords = studyRecords.filter(record => {
    if (keyword && !record.studentName.includes(keyword)) return false
    if (departmentId && record.departmentName !== departmentStats.find(d => d.departmentId === departmentId)?.departmentName) return false
    if (courseId && record.courseId !== courseId) return false
    if (completed !== null && record.completed !== completed) return false
    return true
  })
  
  // 分页
  const { list, total } = paginateData(filteredRecords, page, size)
  
  return mockResponse({ list, total })
}

// 获取部门统计
export function mockGetDepartmentStatistics() {
  return mockResponse(departmentStats)
}

// 获取活跃学员
export function mockGetActiveStudents(url: string) {
  const urlObj = new URL(url, 'http://localhost')
  const params = Object.fromEntries(urlObj.searchParams.entries())
  const limit = parseInt(params.limit) || 10
  
  return mockResponse(activeStudents.slice(0, limit))
}

// 获取用户学习记录
export function mockGetUserStudyRecords(url: string) {
  const pathParts = url.split('/')
  const userId = parseInt(pathParts[pathParts.length - 1])
  
  const userRecords = studyRecords.filter(record => record.userId === userId)
  return mockResponse(userRecords)
}

// 获取课程学习记录
export function mockGetCourseStudyRecords(url: string) {
  const pathParts = url.split('/')
  const courseId = parseInt(pathParts[pathParts.length - 1])
  
  const courseRecords = studyRecords.filter(record => record.courseId === courseId)
  return mockResponse(courseRecords)
}

// 删除学习记录
export function mockDeleteStudyRecord(url: string) {
  const pathParts = url.split('/')
  const id = parseInt(pathParts[pathParts.length - 1])
  
  const index = studyRecords.findIndex(record => record.id === id)
  if (index > -1) {
    studyRecords.splice(index, 1)
    return mockResponse(true)
  }
  
  return mockResponse(false, 404, '记录不存在')
}

// 导出学习记录
export function mockExportStudyRecords() {
  // 模拟导出，返回一个简单的CSV内容
  const csvContent = 'data:text/csv;charset=utf-8,学员姓名,部门,课程名称,学习进度,学习时长,完成状态\n' +
    studyRecords.map(record => 
      `${record.studentName},${record.departmentName},${record.courseName},${record.progress}%,${record.duration}秒,${record.completed ? '已完成' : '学习中'}`
    ).join('\n')
  
  // 创建Blob对象
  const blob = new Blob([csvContent], { type: 'text/csv' })
  return Promise.resolve(blob)
}
