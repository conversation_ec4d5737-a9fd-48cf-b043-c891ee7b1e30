<template>
  <view class="room-page">
    <!-- 导航栏 -->
    <up-navbar title="PK房间" :border="false" :background="navbarBg">
      <template #left>
        <up-icon name="arrow-left" size="20" color="#fff" @click="goBack"></up-icon>
      </template>
    </up-navbar>

    <!-- 房间信息 -->
    <view class="room-info">
      <view class="room-header">
        <text class="room-code">房间号：{{ roomInfo.roomCode }}</text>
        <up-button text="分享" size="mini" @click="shareRoom"></up-button>
      </view>
      <view class="room-details">
        <view class="detail-item">
          <text class="detail-label">题库：</text>
          <text class="detail-value">{{ roomInfo.bankName }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">题数：</text>
          <text class="detail-value">{{ roomInfo.questionCount }}题</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">时限：</text>
          <text class="detail-value">{{ Math.floor(roomInfo.timeLimit / 60) }}分钟</text>
        </view>
      </view>
    </view>

    <!-- 玩家列表 -->
    <view class="players-section">
      <view class="section-title">玩家列表 ({{ participants.length }}/2)</view>
      <view class="players-list">
        <view class="player-item" v-for="player in participants" :key="player.userId">
          <view class="player-info">
            <view class="player-avatar">
              <up-icon name="account" size="24" :color="player.userId === userId ? '#667eea' : '#f59e0b'"></up-icon>
            </view>
            <view class="player-details">
              <text class="player-name">{{ player.name || `玩家${player.userId}` }}</text>
              <text class="player-status" :class="{ 'ready': player.isReady }">
                {{ player.isReady ? '已准备' : '未准备' }}
              </text>
            </view>
          </view>
          <view class="player-position">
            <text class="position-text">{{ player.position === 1 ? '左侧' : '右侧' }}</text>
          </view>
        </view>
        
        <!-- 空位 -->
        <view class="player-item empty" v-if="participants.length < 2">
          <view class="player-info">
            <view class="player-avatar empty">
              <up-icon name="plus" size="24" color="#c7c7cc"></up-icon>
            </view>
            <view class="player-details">
              <text class="player-name">等待玩家加入...</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作区域 -->
    <view class="actions-section">
      <up-button 
        v-if="!isReady && participants.length === 2" 
        type="primary" 
        text="准备" 
        @click="readyGame"
        size="large"
      ></up-button>
      <up-button 
        v-if="isReady" 
        text="取消准备" 
        @click="cancelReady"
        size="large"
      ></up-button>
      <up-button 
        text="离开房间" 
        @click="leaveRoom"
        size="large"
      ></up-button>
    </view>

    <!-- 等待开始 -->
    <view class="waiting-start" v-if="allReady">
      <up-loading-icon mode="circle" size="48"></up-loading-icon>
      <text class="waiting-text">所有玩家已准备，即将开始游戏...</text>
    </view>
  </view>
</template>

<script>
import { getRoomInfo, readyGame, leaveRoom } from '@/api/pk'

export default {
  data() {
    return {
      navbarBg: {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      },
      roomId: null,
      userId: null,
      roomInfo: {},
      participants: [],
      websocket: null
    }
  },
  
  computed: {
    isReady() {
      const myParticipant = this.participants.find(p => p.userId === this.userId)
      return myParticipant?.isReady || false
    },
    
    allReady() {
      return this.participants.length === 2 && this.participants.every(p => p.isReady)
    }
  },
  
  onLoad(options) {
    this.roomId = options.roomId
    this.userId = uni.getStorageSync('userInfo')?.id
    this.loadRoomInfo()
    this.initWebSocket()
  },
  
  onUnload() {
    this.closeWebSocket()
  },
  
  methods: {
    goBack() {
      this.leaveRoom()
    },
    
    // 加载房间信息
    async loadRoomInfo() {
      try {
        const res = await getRoomInfo(this.roomId)
        if (res.success) {
          this.roomInfo = res.room
          this.participants = res.participants || []
        }
      } catch (error) {
        console.error('加载房间信息失败:', error)
        uni.showToast({ title: '加载房间信息失败', icon: 'none' })
      }
    },
    
    // 准备游戏
    async readyGame() {
      try {
        const res = await readyGame(this.roomId, this.userId)
        if (res.success) {
          // 通过WebSocket通知其他玩家
          this.sendWebSocketMessage({
            type: 'room_ready',
            roomId: this.roomId
          })
        }
      } catch (error) {
        console.error('准备失败:', error)
        uni.showToast({ title: '准备失败', icon: 'none' })
      }
    },
    
    // 取消准备
    cancelReady() {
      // TODO: 实现取消准备逻辑
      uni.showToast({ title: '功能开发中', icon: 'none' })
    },
    
    // 离开房间
    async leaveRoom() {
      try {
        await leaveRoom(this.roomId, this.userId)
        this.sendWebSocketMessage({
          type: 'room_leave',
          roomId: this.roomId
        })
        uni.navigateBack()
      } catch (error) {
        console.error('离开房间失败:', error)
        uni.navigateBack() // 即使失败也返回
      }
    },
    
    // 分享房间
    shareRoom() {
      uni.setClipboardData({
        data: this.roomInfo.roomCode,
        success: () => {
          uni.showToast({ title: '房间号已复制', icon: 'success' })
        }
      })
    },
    
    // WebSocket相关方法
    initWebSocket() {
      if (!this.userId) return
      
      // 根据环境配置WebSocket地址
      const baseUrl = process.env.NODE_ENV === 'development' ? 'localhost:8080' : 'your-server-domain.com'
      const wsUrl = `ws://${baseUrl}/websocket/pk/${this.userId}`
      this.websocket = uni.connectSocket({
        url: wsUrl,
        success: () => {
          console.log('WebSocket连接成功')
          // 加入房间
          this.sendWebSocketMessage({
            type: 'room_join',
            roomId: this.roomId
          })
        }
      })
      
      this.websocket.onMessage((res) => {
        try {
          const message = JSON.parse(res.data)
          this.handleWebSocketMessage(message)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      })
    },
    
    closeWebSocket() {
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
    },
    
    sendWebSocketMessage(message) {
      if (this.websocket) {
        message.userId = this.userId
        message.timestamp = Date.now()
        this.websocket.send({
          data: JSON.stringify(message)
        })
      }
    },
    
    handleWebSocketMessage(message) {
      console.log('收到WebSocket消息:', message)
      
      switch (message.type) {
        case 'room_update':
          this.loadRoomInfo()
          break
        case 'game_start':
          this.onGameStart(message.data)
          break
        case 'error':
          uni.showToast({ title: message.data, icon: 'none' })
          break
      }
    },
    
    onGameStart(data) {
      uni.showToast({ title: '游戏开始！', icon: 'success' })
      
      // 跳转到对战页面
      setTimeout(() => {
        uni.redirectTo({
          url: `/pages/pk/battle?roomId=${this.roomId}`
        })
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.room-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

// 房间信息
.room-info {
  background: rgba(255, 255, 255, 0.95);
  margin: 20px;
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.room-code {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.room-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 14px;
  color: #718096;
  width: 60px;
}

.detail-value {
  font-size: 14px;
  color: #2d3748;
  font-weight: 500;
}

// 玩家列表
.players-section {
  background: rgba(255, 255, 255, 0.95);
  margin: 0 20px 20px;
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.players-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.player-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
  border: 2px solid #e2e8f0;

  &.empty {
    border-style: dashed;
    opacity: 0.6;
  }
}

.player-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.player-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;

  &.empty {
    background: #f7fafc;
    border: 2px dashed #e2e8f0;
  }
}

.player-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.player-name {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.player-status {
  font-size: 12px;
  color: #f56565;

  &.ready {
    color: #48bb78;
  }
}

.position-text {
  font-size: 12px;
  color: #718096;
  background: #e2e8f0;
  padding: 4px 8px;
  border-radius: 8px;
}

// 操作区域
.actions-section {
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// 等待开始
.waiting-start {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  z-index: 9999;
}

.waiting-text {
  font-size: 16px;
  color: #fff;
  text-align: center;
}
</style>
