<template>
  <view class="page-container">
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">新闻动态</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <view class="content-container with-navbar">
      <view v-if="loading" class="loading-more">
        <up-loading-icon color="#667eea"></up-loading-icon>
        <text>加载中...</text>
      </view>

      <up-empty
          v-if="newsList.length === 0 && !loading"
          mode="list"
          text="空空如也"
          textColor="#909399"
          textSize="14"
      >
      </up-empty>

      <view v-else class="news-list">
        <view
            v-for="(news, index) in newsList"
            :key="index"
            class="news-card"
            @tap="goToNewsDetail(news)"
        >
          <view class="card-left">
            <up-image
                class="news-thumbnail"
                :src="news.coverUrl"
                mode="aspectFill"
                width="80"
                height="60"
                radius="8"
            />
          </view>

          <view class="card-right">
            <text class="news-title">{{ news.title }}</text>
            <view class="news-meta">
              <text class="news-date">{{ formatTime(news.publishTime) }}</text>
              <view class="meta-right">
                <view class="meta-item">
                  <text class="icon">👁️</text>
                  <text class="text">{{ news.viewCount }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <uni-load-more :status="loadMoreStatus"/>
        <!-- 加载更多 -->
        <view class="load-more" v-if="newsList.length > 0">
          <view v-if="loadMoreStatus === 'loading'" class="loading-text">
            <text>加载中...</text>
          </view>
          <view v-else-if="loadMoreStatus === 'noMore'" class="nomore-text">
            <text>没有更多了</text>
          </view>
          <view v-else class="loadmore-text">
            <text>加载更多</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {getNewsList} from '@/api/content'
import {formatTime} from "@/utils/timeUtil"
import {onPullDownRefresh, onReachBottom} from "@dcloudio/uni-app";

const loading = ref(true)
const newsList = ref([])
const loadMoreStatus = ref('more')
const pageNum = ref(1)
const pageSize = ref(4)
const total = ref(0)
const isLoadingMore = ref(false)
const hasMore = ref(true)

const navigateBack = () => {
  uni.navigateBack()
}

// 下拉刷新
onPullDownRefresh(() => {
  pageNum.value = 1
  newsList.value = []
  hasMore.value = true
  fetchNews()
})

// 上拉加载更多
onReachBottom(() => {
  if (!hasMore.value || isLoadingMore.value) return
  pageNum.value += 1
  fetchNews(true)
})

const fetchNews = async (isLoadMore = false) => {
  if (isLoadMore) {
    isLoadingMore.value = true
    loadMoreStatus.value = 'loading'
  } else {
    loading.value = true
    pageNum.value = 1
    newsList.value = []
    hasMore.value = true
  }
  try {
    const res = await getNewsList({pageNum: pageNum.value, pageSize: pageSize.value})
    total.value = res.total || 0
    if (isLoadMore) {
      newsList.value = newsList.value.concat(res.list)
    } else {
      newsList.value = res.list
    }
    if (newsList.value.length >= total.value) {
      hasMore.value = false
      loadMoreStatus.value = 'noMore'
    } else {
      hasMore.value = true
      loadMoreStatus.value = 'more'
    }
  } finally {
    loading.value = false
    isLoadingMore.value = false
    uni.stopPullDownRefresh();
  }
}

const goToNewsDetail = (news) => {
  uni.navigateTo({url: `/pages/home/<USER>
}

onMounted(() => {
  fetchNews()
})
</script>

<style lang="scss" scoped>
@import '@/styles/pages/home/<USER>';
</style>
