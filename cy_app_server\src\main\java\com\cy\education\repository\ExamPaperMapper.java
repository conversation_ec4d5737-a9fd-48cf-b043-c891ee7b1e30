package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.exam.ExamPaper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 试卷Mapper接口
 */
@Mapper
public interface ExamPaperMapper extends BaseMapper<ExamPaper> {

    /**
     * 分页查询试卷列表
     *
     * @param page        分页参数
     * @param keyword     关键字
     * @param isPublished 是否已发布
     * @param sortBy      排序字段
     * @param sortOrder   排序方式
     * @return 分页结果
     */
    IPage<ExamPaper> selectPaperPage(
            Page<ExamPaper> page,
            @Param("keyword") String keyword,
            @Param("isPublished") Boolean isPublished,
            @Param("sortBy") String sortBy,
            @Param("sortOrder") String sortOrder);
}
