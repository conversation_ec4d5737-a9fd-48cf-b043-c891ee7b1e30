package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BadRequestException;
import com.cy.education.exception.NotFoundException;
import com.cy.education.model.entity.ExamBank;
import com.cy.education.model.entity.ExamQuestion;
import com.cy.education.model.params.ExamBankParams;
import com.cy.education.model.params.ExamBankQueryParams;
import com.cy.education.model.vo.ExamBankVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.ExamBankMapper;
import com.cy.education.repository.ExamQuestionMapper;
import com.cy.education.service.ExamBankService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 题库服务实现类
 */
@Service
@RequiredArgsConstructor
public class ExamBankServiceImpl implements ExamBankService {

    private final ExamBankMapper examBankMapper;
    private final ExamQuestionMapper examQuestionMapper;

    @Override
    public PageResponse<ExamBankVO> listBanks(ExamBankQueryParams params) {
        // 构建查询条件
        LambdaQueryWrapper<ExamBank> queryWrapper = new LambdaQueryWrapper<>();
        
        // 关键词查询
        if (StringUtils.hasText(params.getKeyword())) {
            queryWrapper.like(ExamBank::getName, params.getKeyword())
                    .or()
                    .like(ExamBank::getDescription, params.getKeyword());
        }
        
        // 适用范围查询
        if (StringUtils.hasText(params.getScope())) {
            queryWrapper.like(ExamBank::getScope, params.getScope());
        }
        
        // 排序
        if (StringUtils.hasText(params.getSortBy())) {
            boolean isAsc = "asc".equalsIgnoreCase(params.getSortOrder());
            String sortBy = params.getSortBy();
            
            switch (sortBy) {
                case "name":
                    queryWrapper.orderBy(true, isAsc, ExamBank::getName);
                    break;
                case "createdAt":
                    queryWrapper.orderBy(true, isAsc, ExamBank::getCreatedAt);
                    break;
                default:
                    queryWrapper.orderByDesc(ExamBank::getCreatedAt);
                    break;
            }
        } else {
            // 默认按创建时间降序
            queryWrapper.orderByDesc(ExamBank::getCreatedAt);
        }
        
        // 分页查询
        Page<ExamBank> page = new Page<>(params.getPage(), params.getSize());
        IPage<ExamBank> resultPage = examBankMapper.selectPage(page, queryWrapper);
        
        // 转换为VO对象
        List<ExamBankVO> voList = resultPage.getRecords().stream().map(bank -> {
            ExamBankVO vo = new ExamBankVO();
            BeanUtils.copyProperties(bank, vo);
            // 获取题库中的题目数量
            Integer questionCount = examBankMapper.getQuestionCount(bank.getId());
            vo.setQuestionCount(questionCount != null ? questionCount : 0);
            return vo;
        }).collect(Collectors.toList());
        
        return PageResponse.of(voList, resultPage.getTotal(), params.getPage(), params.getSize());
    }

    @Override
    public ExamBankVO getBankDetail(Integer id) {
        ExamBank bank = examBankMapper.selectById(id);
        if (bank == null) {
            throw new NotFoundException("题库不存在");
        }
        
        ExamBankVO vo = new ExamBankVO();
        BeanUtils.copyProperties(bank, vo);
        
        // 获取题库中的题目数量
        Integer questionCount = examBankMapper.getQuestionCount(id);
        vo.setQuestionCount(questionCount != null ? questionCount : 0);
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createBank(ExamBankParams params, String createdBy) {
        // 检查题库名称是否已存在
        LambdaQueryWrapper<ExamBank> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamBank::getName, params.getName());
        if (examBankMapper.selectCount(queryWrapper) > 0) {
            throw new BadRequestException("题库名称已存在");
        }
        
        ExamBank bank = new ExamBank();
        BeanUtils.copyProperties(params, bank);
        bank.setCreatedBy(createdBy);
        bank.setCreatedAt(LocalDateTime.now());
        bank.setUpdatedAt(LocalDateTime.now());
        
        examBankMapper.insert(bank);
        return bank.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBank(Integer id, ExamBankParams params) {
        ExamBank bank = examBankMapper.selectById(id);
        if (bank == null) {
            throw new NotFoundException("题库不存在");
        }
        
        // 检查题库名称是否已存在(排除自身)
        if (!bank.getName().equals(params.getName())) {
            LambdaQueryWrapper<ExamBank> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExamBank::getName, params.getName());
            if (examBankMapper.selectCount(queryWrapper) > 0) {
                throw new BadRequestException("题库名称已存在");
            }
        }
        
        BeanUtils.copyProperties(params, bank);
        bank.setUpdatedAt(LocalDateTime.now());
        
        return examBankMapper.updateById(bank) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBank(Integer id) {
        ExamBank bank = examBankMapper.selectById(id);
        if (bank == null) {
            throw new NotFoundException("题库不存在");
        }
        
        // 检查题库中是否还有题目
        LambdaQueryWrapper<ExamQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamQuestion::getBankId, id);
        if (examQuestionMapper.selectCount(queryWrapper) > 0) {
            throw new BadRequestException("题库中还有题目，不能删除");
        }
        
        return examBankMapper.deleteById(id) > 0;
    }
} 