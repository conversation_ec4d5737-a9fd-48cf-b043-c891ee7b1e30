.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f6fa 0%, #ffffff 100%);
  display: flex;
  flex-direction: column;
}

.timer-display {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.timer-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timer-text {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  font-family: 'Courier New', monospace;
}

// 进度指示器
.progress-section {
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #f0f2f5;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.progress-label {
  font-size: 14px;
  color: #8e8e93;
}

.progress-bar {
  height: 6px;
  background: #f0f2f5;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

// 题目导航
.question-nav {
  background: #fff;
  border-bottom: 1px solid #f0f2f5;
  padding: 12px 0;
}

.nav-scroll {
  width: 100%;
}

.nav-items {
  display: flex;
  gap: 8px;
  padding: 0 20px;
}

.nav-item {
  position: relative;
  min-width: 40px;
  height: 40px;
  border-radius: 20px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.nav-item:active {
  transform: scale(0.95);
}

.nav-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.nav-item.answered {
  background: rgba(67, 233, 123, 0.1);
  border-color: #43e97b;
}

.nav-item.marked {
  background: rgba(250, 112, 154, 0.1);
  border-color: #fa709a;
}

.nav-number {
  font-size: 14px;
  font-weight: 600;
  color: #1a1d2e;
}

.nav-item.active .nav-number {
  color: #fff;
}

.answer-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  border-radius: 8px;
  background: #43e97b;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mark-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background: #fa709a;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 内容区域
.content-container {
  flex: 1;
  padding: 20px;
  padding-bottom: 100px; // 为底部操作栏留出空间
  overflow-y: auto;
}

.question-card {
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.question-header {
  margin-bottom: 20px;
}

.question-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.question-info {
  flex: 1;
}

.question-number {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 8px;
}

.question-type {
  display: flex;
  align-items: center;
  gap: 12px;
}

.type-badge {
  padding: 4px 12px;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.question-score {
  font-size: 14px;
  color: #fa709a;
  font-weight: 600;
}

.question-actions {
  display: flex;
  align-items: center;
}

.mark-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.mark-btn:active {
  background: #f0f4ff;
}

.mark-btn.marked {
  background: rgba(250, 112, 154, 0.1);
  border-color: #fa709a;
}

.mark-text {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
}

.mark-btn.marked .mark-text {
  color: #fa709a;
}

.question-body {
  line-height: 1.6;
}

.question-text {
  font-size: 16px;
  color: #1a1d2e;
  line-height: 1.6;
  margin-bottom: 20px;
}

.question-images {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.image-wrapper {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-wrapper:active {
  transform: scale(0.98);
}

.question-image {
  width: 100%;
  border-radius: 8px;
}

// 选择题选项
.options-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 5px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.option-item:active {
  background: #f0f4ff;
}

.option-item.selected {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.option-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.option-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #e0e6ff;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.option-circle.selected {
  border-color: #667eea;
  background: #667eea;
}

.option-check {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-key {
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
  min-width: 20px;
}

.option-text {
  flex: 1;
  font-size: 14px;
  color: #1a1d2e;
  line-height: 1.5;
}

// 判断题选项
.judge-options {
  display: flex;
  gap: 16px;
  margin-top: 5px;
}

.judge-option {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.judge-option:active {
  background: #f0f4ff;
}

.judge-option.selected {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.judge-indicator {
  flex-shrink: 0;
}

.judge-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #e0e6ff;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.judge-circle.selected {
  border-color: #667eea;
  background: #667eea;
}

.judge-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  justify-content: center;
}

.judge-text {
  font-size: 14px;
  font-weight: 500;
  color: #1a1d2e;
}

// 填空题和简答题
.fill-container,
.essay-container {
  margin-top: 16px;
}

.input-wrapper {
  position: relative;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: #fff;
}

.fill-textarea,
.essay-textarea {
  width: 100%;
  min-height: 80px;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #1a1d2e;
  line-height: 1.5;
  resize: none;
}

.essay-textarea {
  min-height: 120px;
}

.fill-textarea::placeholder,
.essay-textarea::placeholder {
  color: #8e8e93;
}

.word-count {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  justify-content: flex-end;
}

.count-text {
  font-size: 12px;
  color: #8e8e93;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #f0f2f5;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 999;
}

.navigation-buttons {
  display: flex;
  gap: 12px;
  flex: 1;
}

.nav-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.nav-btn:active {
  background: #f0f4ff;
}

.nav-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-text {
  font-size: 14px;
  color: #8e8e93;
  font-weight: 500;
}

.submit-button {
  height: 44px;
  padding: 0 20px;
  border-radius: 22px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.submit-button:active {
  transform: scale(0.98);
}

.submit-text {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
}

// 交卷确认弹窗
.submit-dialog {
  width: 340px;
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

.header-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(250, 112, 154, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
  flex: 1;
  text-align: center;
}

.close-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: #f5f6fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #e0e6ff;
}

.dialog-content {
  padding: 20px;
}

.submit-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8faff;
  border-radius: 8px;
}

.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.stat-label {
  font-size: 12px;
  color: #8e8e93;
}

.warning-section {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fff8f0;
  border-radius: 8px;
  border-left: 4px solid #fa8c16;
}

.warning-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(250, 140, 22, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f2f5;
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.action-btn.primary:active {
  transform: scale(0.98);
}

.action-btn.secondary {
  background: #f8faff;
  color: #8e8e93;
  border: 1px solid #e0e6ff;
}

.action-btn.secondary:active {
  background: #f0f4ff;
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
}

// 时间到弹窗
.time-up-dialog {
  width: 280px;
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
}

.time-up-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 24px;
  text-align: center;
}

.time-up-icon {
  width: 64px;
  height: 64px;
  border-radius: 32px;
  background: rgba(245, 108, 108, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.time-up-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 8px;
}

.time-up-message {
  font-size: 14px;
  color: #8e8e93;
  line-height: 1.5;
  margin-bottom: 16px;
}

.countdown-display {
  padding: 8px 16px;
  background: #fff8f0;
  border-radius: 16px;
  border: 1px solid #fa8c16;
}

.countdown-text {
  font-size: 14px;
  color: #fa8c16;
  font-weight: 600;
}
