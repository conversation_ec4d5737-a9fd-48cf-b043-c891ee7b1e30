import { get, post } from '@/utils/request'

/**
 * 开始匹配
 * @param {Object} params 匹配参数
 * @param {number} params.userId 用户ID
 * @param {number} params.bankId 题库ID
 * @param {number} params.questionCount 题目数量
 * @param {number} params.timeLimit 时间限制
 */
export function startMatch(params) {
  return post('/api/pk/match/start', params)
}

/**
 * 取消匹配
 * @param {number} userId 用户ID
 */
export function cancelMatch(userId) {
  return post('/api/pk/match/cancel', { userId })
}

/**
 * 加入房间
 * @param {string} roomCode 房间码
 * @param {number} userId 用户ID
 */
export function joinRoom(roomCode, userId) {
  return post('/api/pk/room/join', { roomCode, userId })
}

/**
 * 离开房间
 * @param {number} roomId 房间ID
 * @param {number} userId 用户ID
 */
export function leaveRoom(roomId, userId) {
  return post('/api/pk/room/leave', { roomId, userId })
}

/**
 * 准备游戏
 * @param {number} roomId 房间ID
 * @param {number} userId 用户ID
 */
export function readyGame(roomId, userId) {
  return post('/api/pk/room/ready', { roomId, userId })
}

/**
 * 提交答案
 * @param {Object} params 答题参数
 * @param {number} params.roomId 房间ID
 * @param {number} params.userId 用户ID
 * @param {number} params.questionId 题目ID
 * @param {number} params.questionOrder 题目顺序
 * @param {string} params.userAnswer 用户答案
 * @param {number} params.answerTime 答题时间
 */
export function submitAnswer(params) {
  return post('/api/pk/game/answer', params)
}

/**
 * 获取房间信息
 * @param {number} roomId 房间ID
 */
export function getRoomInfo(roomId) {
  return get(`/api/pk/room/${roomId}`)
}

/**
 * 获取游戏结果
 * @param {number} roomId 房间ID
 */
export function getGameResult(roomId) {
  return get(`/api/pk/game/result/${roomId}`)
}

/**
 * 获取用户PK历史
 * @param {number} userId 用户ID
 * @param {number} page 页码
 * @param {number} size 每页大小
 */
export function getUserPkHistory(userId, page = 1, size = 10) {
  return get(`/api/pk/history/${userId}`, { page, size })
}
