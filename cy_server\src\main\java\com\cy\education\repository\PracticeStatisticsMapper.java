package com.cy.education.repository;

import com.cy.education.model.params.PracticeStatisticsQueryParams;
import com.cy.education.model.vo.PracticeStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 练习统计Mapper接口
 */
@Mapper
public interface PracticeStatisticsMapper {
    
    /**
     * 获取学员练习统计列表
     * @param params 查询参数
     * @return 练习统计列表
     */
    List<PracticeStatisticsVO> selectPracticeStatistics(PracticeStatisticsQueryParams params);
    
    /**
     * 获取学员练习统计详情
     * @param userId 学员ID
     * @param bankId 题库ID
     * @return 练习统计详情
     */
    Map<String, Object> selectPracticeStatisticsDetail(@Param("userId") Integer userId, @Param("bankId") Integer bankId);
    
    /**
     * 获取题库练习统计概览
     * @param bankId 题库ID
     * @return 题库练习统计概览
     */
    Map<String, Object> selectBankPracticeOverview(@Param("bankId") Integer bankId);
    
    /**
     * 获取全部练习统计概览
     * @return 全部练习统计概览
     */
    Map<String, Object> selectAllPracticeOverview();
    
    /**
     * 获取学员练习记录
     * @param userId 学员ID
     * @param bankId 题库ID
     * @return 练习记录列表
     */
    List<Map<String, Object>> selectStudentPracticeRecords(@Param("userId") Integer userId, @Param("bankId") Integer bankId);
} 