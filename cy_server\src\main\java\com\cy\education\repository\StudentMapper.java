package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.Student;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 学员Mapper接口
 */
@Repository
public interface StudentMapper extends BaseMapper<Student> {
    
    /**
     * 分页查询学员列表，并关联部门信息
     *
     * @param page 分页对象
     * @param keyword 关键词（用户名、真实姓名、手机号）
     * @param departmentIds 部门ID列表
     * @param status 状态
     * @return 分页结果
     */
    IPage<Student> selectStudentPage(Page<Student> page, 
                                   @Param("keyword") String keyword, 
                                   @Param("departmentIds") List<Integer> departmentIds, 
                                   @Param("status") Integer status);
    
    /**
     * 根据ID查询学员详情，并关联部门信息
     *
     * @param id 学员ID
     * @return 学员详情
     */
    Student selectStudentById(@Param("id") Integer id);
} 