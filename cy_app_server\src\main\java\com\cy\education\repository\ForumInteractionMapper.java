package com.cy.education.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.forum.ForumPost;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 论坛用户交互Mapper接口
 */
@Repository
public interface ForumInteractionMapper {

    // =========================== 帖子点赞相关 ===========================

    /**
     * 插入帖子点赞记录
     */
    @Insert("INSERT INTO forum_post_likes (post_id, user_id, created_at) VALUES (#{postId}, #{userId}, NOW())")
    int insertPostLike(@Param("postId") Integer postId, @Param("userId") Integer userId);

    /**
     * 删除帖子点赞记录
     */
    @Delete("DELETE FROM forum_post_likes WHERE post_id = #{postId} AND user_id = #{userId}")
    int deletePostLike(@Param("postId") Integer postId, @Param("userId") Integer userId);

    /**
     * 检查用户是否点赞了帖子
     */
    @Select("SELECT COUNT(*) FROM forum_post_likes WHERE post_id = #{postId} AND user_id = #{userId}")
    int checkPostLike(@Param("postId") Integer postId, @Param("userId") Integer userId);

    /**
     * 增加帖子点赞数
     */
    @Update("UPDATE forum_posts SET like_count = like_count + 1 WHERE id = #{postId}")
    int incrementPostLikeCount(@Param("postId") Integer postId);

    /**
     * 减少帖子点赞数
     */
    @Update("UPDATE forum_posts SET like_count = GREATEST(like_count - 1, 0) WHERE id = #{postId}")
    int decrementPostLikeCount(@Param("postId") Integer postId);

    // =========================== 帖子收藏相关 ===========================

    /**
     * 插入帖子收藏记录
     */
    @Insert("INSERT INTO forum_post_collects (post_id, user_id, created_at) VALUES (#{postId}, #{userId}, NOW())")
    int insertPostCollect(@Param("postId") Integer postId, @Param("userId") Integer userId);

    /**
     * 删除帖子收藏记录
     */
    @Delete("DELETE FROM forum_post_collects WHERE post_id = #{postId} AND user_id = #{userId}")
    int deletePostCollect(@Param("postId") Integer postId, @Param("userId") Integer userId);

    /**
     * 检查用户是否收藏了帖子
     */
    @Select("SELECT COUNT(*) FROM forum_post_collects WHERE post_id = #{postId} AND user_id = #{userId}")
    int checkPostCollect(@Param("postId") Integer postId, @Param("userId") Integer userId);

    /**
     * 增加帖子收藏数
     */
    @Update("UPDATE forum_posts SET collect_count = collect_count + 1 WHERE id = #{postId}")
    int incrementPostCollectCount(@Param("postId") Integer postId);

    /**
     * 减少帖子收藏数
     */
    @Update("UPDATE forum_posts SET collect_count = GREATEST(collect_count - 1, 0) WHERE id = #{postId}")
    int decrementPostCollectCount(@Param("postId") Integer postId);

    // =========================== 用户关注相关 ===========================

    /**
     * 插入用户关注记录
     */
    @Insert("INSERT INTO forum_user_follows (follower_id, following_id, created_at) VALUES (#{userId}, #{targetUserId}, NOW())")
    int insertUserFollow(@Param("targetUserId") Integer targetUserId, @Param("userId") Integer userId);

    /**
     * 删除用户关注记录
     */
    @Delete("DELETE FROM forum_user_follows WHERE follower_id = #{userId} AND following_id = #{targetUserId}")
    int deleteUserFollow(@Param("targetUserId") Integer targetUserId, @Param("userId") Integer userId);

    /**
     * 检查用户是否关注了目标用户
     */
    @Select("SELECT COUNT(*) FROM forum_user_follows WHERE follower_id = #{userId} AND following_id = #{targetUserId}")
    int checkUserFollow(@Param("targetUserId") Integer targetUserId, @Param("userId") Integer userId);

    // =========================== 评论点赞相关 ===========================

    /**
     * 插入评论点赞记录
     */
    @Insert("INSERT INTO forum_comment_likes (comment_id, user_id, created_at) VALUES (#{commentId}, #{userId}, NOW())")
    int insertCommentLike(@Param("commentId") Integer commentId, @Param("userId") Integer userId);

    /**
     * 删除评论点赞记录
     */
    @Delete("DELETE FROM forum_comment_likes WHERE comment_id = #{commentId} AND user_id = #{userId}")
    int deleteCommentLike(@Param("commentId") Integer commentId, @Param("userId") Integer userId);

    /**
     * 检查用户是否点赞了评论
     */
    @Select("SELECT COUNT(*) FROM forum_comment_likes WHERE comment_id = #{commentId} AND user_id = #{userId}")
    int checkCommentLike(@Param("commentId") Integer commentId, @Param("userId") Integer userId);

    /**
     * 增加评论点赞数
     */
    @Update("UPDATE forum_comments SET like_count = like_count + 1 WHERE id = #{commentId}")
    int incrementCommentLikeCount(@Param("commentId") Integer commentId);

    /**
     * 减少评论点赞数
     */
    @Update("UPDATE forum_comments SET like_count = GREATEST(like_count - 1, 0) WHERE id = #{commentId}")
    int decrementCommentLikeCount(@Param("commentId") Integer commentId);

    // =========================== 用户收藏帖子列表相关 ===========================

    /**
     * 获取用户收藏的帖子列表（分页）
     */
    IPage<ForumPost> getCollectedPostsPage(@Param("userId") Integer userId, @Param("param") com.cy.education.model.params.ForumPostQueryParam param);
}
