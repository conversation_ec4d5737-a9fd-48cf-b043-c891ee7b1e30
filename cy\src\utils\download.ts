/**
 * 文件下载工具类
 */

/**
 * 下载Blob文件
 * @param blob Blob对象
 * @param filename 文件名
 */
export function downloadBlob(blob: Blob, filename: string) {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  // 释放URL对象
  window.URL.revokeObjectURL(url)
}

/**
 * 下载文本内容为文件
 * @param content 文本内容
 * @param filename 文件名
 * @param mimeType MIME类型
 */
export function downloadText(content: string, filename: string, mimeType = 'text/plain') {
  const blob = new Blob([content], { type: mimeType })
  downloadBlob(blob, filename)
}

/**
 * 下载JSON数据为文件
 * @param data JSON数据
 * @param filename 文件名
 */
export function downloadJson(data: any, filename: string) {
  const content = JSON.stringify(data, null, 2)
  downloadText(content, filename, 'application/json')
}

/**
 * 从URL下载文件
 * @param url 文件URL
 * @param filename 文件名（可选）
 */
export function downloadFromUrl(url: string, filename?: string) {
  const link = document.createElement('a')
  link.href = url
  if (filename) {
    link.download = filename
  }
  link.style.display = 'none'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 生成带时间戳的文件名
 * @param baseName 基础文件名
 * @param extension 文件扩展名
 * @returns 带时间戳的文件名
 */
export function generateTimestampFilename(baseName: string, extension: string): string {
  const timestamp = new Date().toISOString()
    .slice(0, 19)
    .replace(/[-:]/g, '')
    .replace('T', '_')
  return `${baseName}_${timestamp}.${extension}`
}

/**
 * 处理API响应的文件下载
 * @param response 响应对象
 * @param defaultFilename 默认文件名
 */
export function handleFileDownloadResponse(response: any, defaultFilename: string) {
  try {
    // 尝试从响应头获取文件名
    let filename = defaultFilename
    const contentDisposition = response.headers?.['content-disposition']
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '')
        // 解码URL编码的文件名
        try {
          filename = decodeURIComponent(filename)
        } catch (e) {
          // 如果解码失败，使用原文件名
        }
      }
    }
    
    // 创建Blob并下载
    const blob = new Blob([response.data || response], {
      type: response.headers?.['content-type'] || 'application/octet-stream'
    })
    
    downloadBlob(blob, filename)
  } catch (error) {
    console.error('文件下载失败:', error)
    throw new Error('文件下载失败')
  }
}
