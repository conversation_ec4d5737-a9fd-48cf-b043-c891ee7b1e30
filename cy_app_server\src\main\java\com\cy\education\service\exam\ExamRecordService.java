package com.cy.education.service.exam;

import com.cy.education.model.entity.exam.ExamRecord;
import com.cy.education.model.params.ExamRecordQueryParams;
import com.cy.education.model.vo.ExamRecordVO;
import com.cy.education.model.vo.PageResponse;

import java.util.Map;

/**
 * 考试记录服务接口
 */
public interface ExamRecordService {

    /**
     * 分页查询考试记录列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResponse<ExamRecordVO> listExamRecords(ExamRecordQueryParams params);

    /**
     * 获取考试记录详情
     *
     * @param id 记录ID
     * @return 考试记录详情
     */
    ExamRecordVO getExamRecordDetail(Integer id);

    /**
     * 开始考试
     *
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 考试记录ID
     */
    Integer startExam(Integer examId, Integer userId);

    /**
     * 提交考试
     *
     * @param recordId 记录ID
     * @param answers  答案列表
     * @return 考试成绩
     */
    ExamRecord submitExam(Integer recordId, String answers);

    /**
     * 获取考试统计数据
     *
     * @param examId 考试ID，如果为null则统计所有考试
     * @return 统计数据
     */
    Map<String, Object> getExamStats(Integer examId);

    /**
     * 获取当前用户的考试统计
     */
    Map<String, Object> getUserExamStats();

    /**
     * 获取考试结果详情
     */
    Map<String, Object> getExamResult(Integer recordId);


    /**
     * 根据考试ID和用户ID查询考试记录
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 考试记录VO
     */
    ExamRecordVO selectByExamIdAndUserId(Integer examId, Integer userId);
}
