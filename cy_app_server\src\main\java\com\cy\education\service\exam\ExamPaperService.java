package com.cy.education.service.exam;

import com.cy.education.model.vo.ExamPaperVO;

/**
 * 试卷服务接口
 */
public interface ExamPaperService {

    /**
     * 获取试卷详情（包含答案）
     * @param id 试卷ID
     * @return 试卷详情
     */
    ExamPaperVO getPaperDetailWithAnswer(Integer id);

    /**
     * 获取试卷详情（不包含答案）
     * @param id 试卷ID
     * @return 试卷详情
     */
    ExamPaperVO getPaperDetailWithoutAnswer(Integer id);

}
