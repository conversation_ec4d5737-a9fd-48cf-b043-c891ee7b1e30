package com.cy.education.service;

import com.cy.education.model.params.ChangePasswordParams;
import com.cy.education.model.params.LoginParams;
import com.cy.education.model.vo.LoginResponseVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 学生登录
     *
     * @param loginParams 登录参数
     * @return 登录响应
     */
    LoginResponseVO login(LoginParams loginParams);

    /**
     * 学生登出
     *
     * @return 是否成功
     */
    boolean logout();


    /**
     * 修改密码
     *
     * @param params 修改密码参数
     * @return 是否成功
     */
    boolean changePassword(ChangePasswordParams params);

    /**
     * 上传头像
     *
     * @param file 头像文件
     * @return 头像URL
     */
    String uploadAvatar(MultipartFile file);
}
