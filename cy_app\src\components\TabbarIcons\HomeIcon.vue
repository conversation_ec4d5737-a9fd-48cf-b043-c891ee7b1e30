<template>
	<svg :width="size" :height="size" viewBox="0 0 24 24" :fill="active ? '#fff' : '#8E8E93'">
		<path v-if="active" d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
		<path v-else d="M12 5.69l5 4.5V18h-2v-6H9v6H7v-7.81l5-4.5M12 3L2 12h3v8h6v-6h2v6h6v-8h3L12 3z"/>
	</svg>
</template>

<script setup lang="ts">
interface Props {
	size?: string | number
	color?: string
	active?: boolean
}

withDefaults(defineProps<Props>(), {
	size: 24,
	color: 'currentColor',
	active: false
})
</script>