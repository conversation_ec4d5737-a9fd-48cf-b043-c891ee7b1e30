<template>
  <div class="forum-posts">
    <div class="page-container">
      <div class="page-toolbar">
        <div class="toolbar-left">
          <h3>帖子/评论管理</h3>
        </div>
        <div class="toolbar-right">
          <el-select v-model="searchForm.status" placeholder="审核状态" style="width: 120px; margin-right: 12px;">
            <el-option label="全部" value="" />
            <el-option 
              v-for="option in postStatusOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索标题或作者"
            style="width: 250px; margin-right: 12px;"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" :icon="View" @click="handleBatchApprove">批量审核</el-button>
          <el-button type="danger" :icon="Delete" @click="handleBatchDelete">批量删除</el-button>
        </div>
      </div>

      <!-- Tab切换 -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="帖子管理" name="posts">
          <el-table
            :data="postsList"
            v-loading="loading"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="title" label="标题" min-width="200">
              <template #default="{ row }">
                <div style="display: flex; align-items: center;">
                  <el-tag v-if="row.isTop" type="danger" size="small" style="margin-right: 8px;">置顶</el-tag>
                  <el-tag v-if="row.isEssence" type="warning" size="small" style="margin-right: 8px;">精华</el-tag>
                  <span>{{ row.title }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="author" label="作者" width="100">
              <template #default="{ row }">
                <div style="display: flex; align-items: center;">
                  <el-avatar :size="24" :src="row.authorAvatar" style="margin-right: 8px;" />
                  <span>{{ row.author }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="category" label="板块" width="100" />
            <el-table-column prop="viewCount" label="浏览量" width="80" />
            <el-table-column prop="replyCount" label="回复数" width="80" />
            <el-table-column prop="createTime" label="发布时间" width="150" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="{ row }">
                <el-button text type="primary" @click="handleView(row)">查看</el-button>
                <el-button 
                  v-if="row.status === 0" 
                  text 
                  type="success" 
                  @click="handleApprove(row)"
                >
                  通过
                </el-button>
                <el-button 
                  v-if="row.status === 0" 
                  text 
                  type="warning" 
                  @click="handleReject(row)"
                >
                  拒绝
                </el-button>
                <el-dropdown>
                  <el-button text>
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleSetTop(row)">
                        {{ row.isTop ? '取消置顶' : '设为置顶' }}
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleSetEssence(row)">
                        {{ row.isEssence ? '取消精华' : '设为精华' }}
                      </el-dropdown-item>
                      <el-dropdown-item divided @click="handleDelete(row)">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="评论管理" name="comments">
          <el-table
            :data="commentsList"
            v-loading="loading"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="content" label="评论内容" min-width="300">
              <template #default="{ row }">
                <div class="comment-content">
                  {{ row.content }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="postTitle" label="所属帖子" min-width="200" />
            <el-table-column prop="author" label="评论者" width="120">
              <template #default="{ row }">
                <div style="display: flex; align-items: center;">
                  <el-avatar :size="24" :src="row.authorAvatar" style="margin-right: 8px;" />
                  <span>{{ row.author }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="评论时间" width="150" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button text type="primary" @click="handleView(row)">查看</el-button>
                <el-button 
                  v-if="row.status === 0" 
                  text 
                  type="success" 
                  @click="handleApprove(row)"
                >
                  通过
                </el-button>
                <el-button 
                  v-if="row.status === 0" 
                  text 
                  type="warning" 
                  @click="handleReject(row)"
                >
                  拒绝
                </el-button>
                <el-button text type="danger" @click="handleDelete(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 查看详情弹窗 -->
    <el-dialog
      v-model="viewDialogVisible"
      :title="activeTab === 'posts' ? '帖子详情' : '评论详情'"
      width="800px"
      destroy-on-close
    >
      <div v-if="currentItem" class="content-detail">
        <div v-if="activeTab === 'posts'" class="post-detail">
          <h3>{{ (currentItem as Post).title }}</h3>
          <div class="post-meta">
            <span>作者：{{ currentItem.author }}</span>
            <span style="margin-left: 16px;">板块：{{ (currentItem as Post).category }}</span>
            <span style="margin-left: 16px;">发布时间：{{ currentItem.createTime }}</span>
          </div>
          <div class="post-content" v-html="(currentItem as Post).content || '这是帖子的详细内容...'"></div>
          
          <!-- 帖子评论 -->
          <div class="post-comments">
            <h4>帖子评论 ({{ (currentItem as Post).replyCount }})</h4>
            <CommentTree :post-id="(currentItem as Post).id" />
          </div>
        </div>
        <div v-else class="comment-detail">
          <div class="comment-meta">
            <span>评论者：{{ currentItem.author }}</span>
            <span style="margin-left: 16px;">所属帖子：{{ (currentItem as Comment).postTitle }}</span>
            <span style="margin-left: 16px;">评论时间：{{ currentItem.createTime }}</span>
          </div>
          <div class="comment-content">{{ currentItem.content }}</div>
        </div>
      </div>
      <template #footer>
        <el-button @click="viewDialogVisible = false">关闭</el-button>
        <el-button 
          v-if="currentItem?.status === 0" 
          type="success" 
          @click="handleApprove(currentItem)"
        >
          审核通过
        </el-button>
        <el-button 
          v-if="currentItem?.status === 0" 
          type="warning" 
          @click="handleReject(currentItem)"
        >
          审核拒绝
        </el-button>
      </template>
    </el-dialog>

    <!-- 拒绝理由弹窗 -->
    <el-dialog v-model="rejectDialogVisible" title="审核拒绝" width="500px">
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝理由">
          <el-select v-model="rejectForm.reason" placeholder="请选择拒绝理由" style="width: 100%;">
            <el-option label="内容违规" value="违规内容" />
            <el-option label="广告信息" value="广告信息" />
            <el-option label="恶意灌水" value="恶意灌水" />
            <el-option label="重复发布" value="重复发布" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="详细说明">
          <el-input 
            v-model="rejectForm.detail" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入详细说明（选填）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="rejectDialogVisible = false">取消</el-button>
        <el-button type="warning" @click="handleRejectConfirm">确认拒绝</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  Search,
  View,
  Delete,
  ArrowDown
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getPostList,
  getPostById,
  getCommentList,
  getCommentById,
  getPostComments,
  reviewPost,
  reviewComment,
  deletePost,
  deleteComment,
  setPostTop,
  setPostEssence,
  batchReviewPosts,
  batchDeletePosts,
  batchReviewComments,
  batchDeleteComments,
  type Post,
  type Comment,
  type PostQueryParams,
  type CommentQueryParams
} from '@/api/forum'
import CommentTree from './components/CommentTree.vue'
import { 
  getPostStatusInfo, 
  getCommentStatusInfo,
  postStatusOptions,
  commentStatusOptions,
  postStatusMap,
  commentStatusMap 
} from '@/utils/forumStatusHelper'

const loading = ref(false)
const activeTab = ref('posts')
const viewDialogVisible = ref(false)
const rejectDialogVisible = ref(false)
const currentItem = ref<Post | Comment | null>(null)
const selectedItems = ref<(Post | Comment)[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 拒绝表单
const rejectForm = reactive({
  reason: '',
  detail: ''
})

// 数据列表
const postsList = ref<Post[]>([])
const commentsList = ref<Comment[]>([])

// 获取状态类型
const getStatusType = (status: number | string) => {
  if (activeTab.value === 'posts') {
    return getPostStatusInfo(status).type;
  } else {
    return getCommentStatusInfo(status).type;
  }
}

// 获取状态文本
const getStatusText = (status: number | string) => {
  if (activeTab.value === 'posts') {
    return getPostStatusInfo(status).text;
  } else {
    return getCommentStatusInfo(status).text;
  }
}

// Tab切换
const handleTabChange = (tab: string) => {
  activeTab.value = tab
  pagination.page = 1
  loadData()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    if (activeTab.value === 'posts') {
      // 加载帖子列表
      const params: PostQueryParams = {
        keyword: searchForm.keyword,
        status: searchForm.status,
        page: pagination.page,
        size: pagination.size
      }
      const res = await getPostList(params)
      postsList.value = res.list
      pagination.total = res.total
    } else {
      // 加载评论列表
      const params: CommentQueryParams = {
        keyword: searchForm.keyword,
        status: searchForm.status,
        page: pagination.page,
        size: pagination.size
      }
      const res = await getCommentList(params)
      commentsList.value = res.list
      pagination.total = res.total
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleView = async (row: Post | Comment) => {
  loading.value = true
  try {
    if (activeTab.value === 'posts') {
      // 查看帖子详情
      const post = await getPostById((row as Post).id)
      currentItem.value = post
    } else {
      // 查看评论详情
      const comment = await getCommentById((row as Comment).id)
      currentItem.value = comment
    }
    viewDialogVisible.value = true
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 审核通过
const handleApprove = async (row: Post | Comment) => {
  loading.value = true
  try {
    if (activeTab.value === 'posts') {
      // 审核帖子，使用数字状态1（已通过）
      await reviewPost((row as Post).id, 1)
      ElMessage.success('帖子审核通过')
      
      // 更新状态
      if (row) {
        row.status = 1;
      }
    } else {
      // 审核评论，使用数字状态1（已通过）
      await reviewComment((row as Comment).id, 1)
      ElMessage.success('评论审核通过')
      
      // 更新状态
      if (row) {
        row.status = 1;
      }
    }
    
    if (viewDialogVisible.value) {
      viewDialogVisible.value = false
    }
    
    // 刷新数据
    loadData()
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败')
  } finally {
    loading.value = false
  }
}

// 审核拒绝
const handleReject = (row: Post | Comment) => {
  currentItem.value = row
  rejectDialogVisible.value = true
}

// 确认拒绝
const handleRejectConfirm = async () => {
  if (!rejectForm.reason) {
    ElMessage.warning('请选择拒绝理由')
    return
  }
  
  loading.value = true
  try {
    const reason = rejectForm.detail ? `${rejectForm.reason}: ${rejectForm.detail}` : rejectForm.reason
    
    if (activeTab.value === 'posts' && currentItem.value) {
      // 拒绝帖子，使用数字状态2（已拒绝）
      await reviewPost((currentItem.value as Post).id, 2, reason)
      ElMessage.success('帖子已拒绝')
      
      // 更新状态
      if (currentItem.value) {
        currentItem.value.status = 2;
      }
    } else if (currentItem.value) {
      // 拒绝评论，使用数字状态2（已拒绝）
      await reviewComment((currentItem.value as Comment).id, 2, reason)
      ElMessage.success('评论已拒绝')
      
      // 更新状态
      if (currentItem.value) {
        currentItem.value.status = 2;
      }
    }
    
    rejectDialogVisible.value = false
    if (viewDialogVisible.value) {
      viewDialogVisible.value = false
    }
    
    // 重置表单
    Object.assign(rejectForm, { reason: '', detail: '' })
    
    // 刷新数据
    loadData()
  } catch (error) {
    console.error('拒绝失败:', error)
    ElMessage.error('拒绝失败')
  } finally {
    loading.value = false
  }
}

// 删除
const handleDelete = (row: Post | Comment) => {
  ElMessageBox.confirm(
    `确定要删除这个${activeTab.value === 'posts' ? '帖子' : '评论'}吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value = true
    try {
      if (activeTab.value === 'posts') {
        // 删除帖子
        await deletePost((row as Post).id)
        ElMessage.success('帖子删除成功')
      } else {
        // 删除评论
        await deleteComment((row as Comment).id)
        ElMessage.success('评论删除成功')
      }
      
      // 刷新数据
      loadData()
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {})
}

// 设置置顶
const handleSetTop = async (row: Post) => {
  loading.value = true
  try {
    const isTop = !row.isTop
    await setPostTop(row.id, isTop)
    
    // 更新状态
    row.isTop = isTop
    
    ElMessage.success(isTop ? '设置置顶成功' : '取消置顶成功')
  } catch (error) {
    console.error('设置置顶失败:', error)
    ElMessage.error('设置置顶失败')
  } finally {
    loading.value = false
  }
}

// 设置精华
const handleSetEssence = async (row: Post) => {
  loading.value = true
  try {
    const isEssence = !row.isEssence
    await setPostEssence(row.id, isEssence)
    
    // 更新状态
    row.isEssence = isEssence
    
    ElMessage.success(isEssence ? '设置精华成功' : '取消精华成功')
  } catch (error) {
    console.error('设置精华失败:', error)
    ElMessage.error('设置精华失败')
  } finally {
    loading.value = false
  }
}

// 批量审核
const handleBatchApprove = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要审核的项目')
    return
  }
  
  loading.value = true
  try {
    const ids = selectedItems.value.map(item => item.id)
    
    if (activeTab.value === 'posts') {
      // 批量审核帖子，使用数字状态1（已通过）
      await batchReviewPosts(ids, 1)
    } else {
      // 批量审核评论，使用数字状态1（已通过）
      await batchReviewComments(ids, 1)
    }
    
    ElMessage.success(`批量审核通过${selectedItems.value.length}项`)
    
    // 刷新数据
    loadData()
  } catch (error) {
    console.error('批量审核失败:', error)
    ElMessage.error('批量审核失败')
  } finally {
    loading.value = false
  }
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要删除的项目')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的${selectedItems.value.length}项吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value = true
    try {
      const ids = selectedItems.value.map(item => item.id)
      
      if (activeTab.value === 'posts') {
        // 批量删除帖子
        await batchDeletePosts(ids)
      } else {
        // 批量删除评论
        await batchDeleteComments(ids)
      }
      
      ElMessage.success(`批量删除${selectedItems.value.length}项成功`)
      
      // 刷新数据
      loadData()
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {})
}

// 表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedItems.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  loadData()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadData()
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.comment-content {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-detail {
  padding: 16px 0;
}

.post-detail h3 {
  margin-bottom: 16px;
  color: #262626;
}

.post-meta {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  color: #666;
  font-size: 14px;
}

.post-content {
  line-height: 1.6;
  color: #262626;
  min-height: 200px;
}

.comment-meta {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  color: #666;
  font-size: 14px;
}

.comment-detail .comment-content {
  max-width: none;
  white-space: normal;
  line-height: 1.6;
  color: #262626;
  background: #f9f9f9;
  padding: 16px;
  border-radius: 6px;
}

.post-comments {
  margin-top: 20px;
}

.post-comments h4 {
  margin-bottom: 16px;
  color: #262626;
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}
</style>