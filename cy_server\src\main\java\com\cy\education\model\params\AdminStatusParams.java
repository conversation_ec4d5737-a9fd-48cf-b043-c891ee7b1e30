package com.cy.education.model.params;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新管理员状态参数
 */
@Data
public class AdminStatusParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 管理员ID
     */
    @NotNull(message = "管理员ID不能为空")
    private Integer id;

    /**
     * 状态：0禁用，1启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
} 