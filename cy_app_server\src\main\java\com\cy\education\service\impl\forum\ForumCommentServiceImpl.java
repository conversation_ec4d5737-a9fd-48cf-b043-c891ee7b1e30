package com.cy.education.service.impl.forum;

import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.forum.ForumComment;
import com.cy.education.model.entity.forum.ForumPost;
import com.cy.education.repository.ForumCommentMapper;
import com.cy.education.repository.ForumPostMapper;
import com.cy.education.service.forum.ForumCommentService;
import com.cy.education.utils.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 论坛评论服务实现
 */
@Service
public class ForumCommentServiceImpl implements ForumCommentService {

    @Autowired
    private ForumCommentMapper forumCommentMapper;

    @Autowired
    private ForumPostMapper forumPostMapper;

    // 状态常量
    private static final int STATUS_PENDING = 0;
    private static final int STATUS_APPROVED = 1;
    private static final int STATUS_REJECTED = 2;
    private static final int STATUS_DELETED = 3;

    @Override
    public List<ForumComment> getPostComments(Integer postId, String sortBy) {

        // 查询帖子的顶级评论，按指定方式排序
        List<ForumComment> topComments = forumCommentMapper.selectTopCommentsByPostIdWithSort(postId, sortBy,
                SecurityUtil.getCurrentUserId());

        // 查询所有子评论并按照父评论ID分组
        List<ForumComment> allChildComments = new ArrayList<>();
        for (ForumComment topComment : topComments) {
            allChildComments.addAll(getChildComments(topComment.getId()));
        }

        Map<Integer, List<ForumComment>> childrenMap = allChildComments.stream()
                .collect(Collectors.groupingBy(comment -> comment.getParentId() != null ? comment.getParentId() : -1));

        // 构建树形结构
        buildCommentTree(topComments, childrenMap);

        return topComments;
    }

    /**
     * 递归获取所有子评论
     */
    private List<ForumComment> getChildComments(Integer parentId) {
        // 获取当前用户ID
        Integer currentUserId = SecurityUtil.getCurrentUserId();

        List<ForumComment> children = forumCommentMapper.selectChildCommentsByParentId(parentId, currentUserId);
        List<ForumComment> allChildren = new ArrayList<>(children);

        for (ForumComment child : children) {
            allChildren.addAll(getChildComments(child.getId()));
        }

        return allChildren;
    }

    /**
     * 构建评论树形结构
     */
    private void buildCommentTree(List<ForumComment> comments, Map<Integer, List<ForumComment>> childrenMap) {
        for (ForumComment comment : comments) {
            List<ForumComment> children = childrenMap.get(comment.getId());
            if (children != null && !children.isEmpty()) {
                comment.setChildren(children);
                buildCommentTree(children, childrenMap);
            }
        }
    }

    @Override
    public ForumComment getCommentById(Integer id) {
        ForumComment comment = forumCommentMapper.selectCommentWithDetailsById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        return comment;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ForumComment createComment(Integer postId, String content, Integer authorId, Integer parentId,
            Integer replyToId) {
        // 检查帖子是否存在
        ForumPost post = forumPostMapper.selectById(postId);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }
        // 检查帖子状态
        if (post.getStatus() != STATUS_APPROVED) {
            throw new BusinessException("帖子不可评论");
        }

        // 如果有父评论，检查父评论是否存在
        if (parentId != null) {
            ForumComment parentComment = forumCommentMapper.selectById(parentId);
            if (parentComment == null) {
                throw new BusinessException("父评论不存在");
            }
            if (!parentComment.getPostId().equals(postId)) {
                throw new BusinessException("父评论不属于该帖子");
            }
        }

        // 创建评论对象
        ForumComment comment = new ForumComment();
        comment.setPostId(postId);
        comment.setContent(content);
        comment.setAuthorId(authorId);
        comment.setParentId(parentId);
        comment.setStatus(STATUS_APPROVED);

        // 插入评论
        forumCommentMapper.insert(comment);

        // 更新帖子回复数
        forumPostMapper.updateReplyCount(postId);

        // 返回完整的评论信息
        return getCommentById(comment.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCommentByUser(Integer commentId, Integer userId) {
        // 检查评论是否存在
        ForumComment comment = forumCommentMapper.selectById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }

        // 检查是否是评论作者
        if (!comment.getAuthorId().equals(userId)) {
            throw new BusinessException("只能删除自己的评论");
        }

        // 假删除评论
        boolean result = forumCommentMapper.softDeleteComment(commentId) > 0;

        // 更新帖子回复数
        if (result) {
            forumPostMapper.updateReplyCount(comment.getPostId());
        }

        return result;
    }
}
