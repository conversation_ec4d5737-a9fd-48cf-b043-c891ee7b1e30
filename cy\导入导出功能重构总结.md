# 导入导出功能重构总结

## 重构原则

根据用户要求，将所有导入导出功能集成到对应的模块控制器中，而不是创建单独的 `ImportExportController`。

## 已完成的模块

### 1. 管理员模块 (AdminController)

**控制器**: `AdminController`
**服务**: `AdminService` & `AdminServiceImpl`

**接口**:
- `GET /admin/import/template` - 下载管理员导入模板
- `POST /admin/import` - 批量导入管理员
- `POST /admin/export` - 导出管理员列表

**功能特性**:
- ✅ 模板生成 (Excel格式，包含示例数据)
- ✅ 数据导入 (支持Excel解析，数据验证，错误报告)
- ✅ 数据导出 (支持自定义字段选择，多种导出范围)
- ✅ 完整的错误处理和事务管理

### 2. 题目模块 (ExamController)

**控制器**: `ExamController`
**服务**: `ExamQuestionService` & `ExamQuestionServiceImpl`

**接口**:
- `GET /exam/question/import/template` - 下载题目导入模板
- `POST /exam/question/import` - 批量导入题目
- `POST /exam/question/export` - 导出题目列表

**功能特性**:
- ✅ 模板生成 (包含题库、题目类型、选项等字段)
- ✅ 数据导入 (支持题库关联，选项处理)
- ✅ 数据导出 (支持题库名称关联查询)
- ✅ 字段映射和数据验证

### 3. 学员模块 (StudentController)

**状态**: 已存在完整实现
**接口**:
- `GET /student/export` - 导出学员列表
- `POST /student/import` - 批量导入学员

**说明**: 学员模块的导入导出功能已经在原有的 `StudentController` 中实现，无需重复开发。

## 进行中的模块

### 4. 试卷模块 (ExamController)

**控制器**: `ExamController` (已添加接口)
**服务**: `ExamPaperService` (接口已定义，实现待完成)

**接口**:
- `POST /exam/paper/{paperId}/export` - 导出单个试卷
- `POST /exam/paper/batch-export` - 批量导出试卷

**待实现功能**:
- 试卷导出 (Word/PDF格式，包含题目和答案)
- 支持导出选项配置 (是否包含答案、解析等)
- 批量导出多个试卷

## 待实现的模块

### 5. 考试记录模块 (ExamRecordController)

**计划接口**:
- `POST /exam-record/export` - 导出考试记录
- `POST /exam/{examId}/statistics/export` - 导出考试统计

**功能需求**:
- 考试记录导出 (Excel格式)
- 考试统计报告导出
- 支持时间范围筛选

### 6. 学习记录模块

**计划接口**:
- `POST /learning-record/export` - 导出学习记录
- `POST /learning-statistics/export` - 导出学习统计

**功能需求**:
- 学习记录导出
- 学习进度统计导出
- 支持用户和时间筛选

## 技术实现

### 导入功能特性

1. **模板生成**
   - 使用 Apache POI 生成 Excel 模板
   - 包含示例数据和字段说明
   - 自动调整列宽

2. **数据导入**
   - Excel 文件解析
   - 数据验证和业务规则检查
   - 批量插入和事务管理
   - 详细的错误报告

3. **错误处理**
   - 行级错误定位
   - 详细错误信息
   - 成功/失败统计

### 导出功能特性

1. **自定义导出**
   - 支持字段选择
   - 支持导出范围 (全部/当前页/选中项)
   - 支持多种格式 (Excel/CSV/PDF)

2. **查询条件**
   - 支持关键词搜索
   - 支持条件筛选
   - 支持排序

3. **文件生成**
   - 动态文件名 (包含时间戳)
   - 正确的响应头设置
   - 流式输出

## 代码结构

### 控制器层
```java
@ApiOperation("下载导入模板")
@GetMapping("/import/template")
public ResponseEntity<Resource> downloadTemplate()

@ApiOperation("批量导入")
@PostMapping("/import")
public ApiResponse<Map<String, Object>> importData(@RequestParam("file") MultipartFile file)

@ApiOperation("导出数据")
@PostMapping("/export")
public void exportData(@RequestBody Map<String, Object> params, HttpServletResponse response)
```

### 服务层
```java
// 模板生成
Resource generateImportTemplate() throws IOException;

// 数据导入
Map<String, Object> importData(MultipartFile file);

// 数据导出
void exportData(Map<String, Object> params, HttpServletResponse response) throws IOException;
```

### 工具方法
```java
// 单元格值读取
private String getCellStringValue(Cell cell)

// 数据验证
private void validateData(Map<String, String> data, int rowNum)
```

## 前端API更新

### 已更新的API文件

1. **importExport.ts** - 删除重复的学员接口，保留其他模块接口
2. **student.ts** - 保持原有的学员导入导出接口

### API调用示例
```typescript
// 管理员导入导出
import { downloadAdminTemplate, importAdmins, exportAdmins } from '@/api/importExport'

// 题目导入导出
import { downloadQuestionTemplate, importQuestions, exportQuestions } from '@/api/importExport'

// 学员导入导出 (使用原有接口)
import { exportStudentList, importStudents } from '@/api/student'
```

## 配置要求

### 导出参数配置
```typescript
interface ExportParams {
  format: 'xlsx' | 'csv' | 'pdf'           // 导出格式
  range: 'all' | 'current' | 'selected'    // 导出范围
  fields?: string[]                         // 导出字段
  selectedIds?: number[]                    // 选中的ID列表
  // 查询条件
  keyword?: string
  startTime?: string
  endTime?: string
  [key: string]: any
}
```

### 导入结果格式
```typescript
interface ImportResult {
  successCount: number                      // 成功数量
  failureCount: number                      // 失败数量
  failures?: Array<{                        // 失败详情
    row: number
    data: string
    reason: string
  }>
  message?: string
}
```

## 下一步计划

1. **完成试卷导出功能**
   - 实现 ExamPaperServiceImpl 中的导出方法
   - 支持 Word/PDF 格式导出
   - 添加导出选项配置

2. **添加考试记录导出**
   - 在 ExamRecordController 中添加导出接口
   - 实现考试记录和统计导出

3. **添加学习记录导出**
   - 创建学习记录相关的导出功能
   - 支持学习进度统计

4. **前端组件优化**
   - 更新通用导入导出组件
   - 适配各模块的特定需求

5. **测试和优化**
   - 完整的功能测试
   - 性能优化
   - 错误处理完善

## 总结

通过将导入导出功能集成到各自的模块控制器中，实现了：

- ✅ 更好的模块化设计
- ✅ 清晰的功能分工
- ✅ 避免了接口路径冲突
- ✅ 符合RESTful设计原则
- ✅ 便于维护和扩展

目前管理员和题目模块的导入导出功能已完全实现，学员模块使用原有实现，其他模块正在逐步完善中。
