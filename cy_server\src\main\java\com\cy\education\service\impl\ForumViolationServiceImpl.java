package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.ForumViolation;
import com.cy.education.model.vo.ForumViolationQueryParam;
import com.cy.education.repository.ForumCommentMapper;
import com.cy.education.repository.ForumPostMapper;
import com.cy.education.repository.ForumViolationMapper;
import com.cy.education.service.ForumCommentService;
import com.cy.education.service.ForumPostService;
import com.cy.education.service.ForumViolationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 论坛违规举报服务实现
 */
@Service
public class ForumViolationServiceImpl implements ForumViolationService {

    @Autowired
    private ForumViolationMapper forumViolationMapper;
    
    @Autowired
    private ForumPostService forumPostService;
    
    @Autowired
    private ForumCommentService forumCommentService;
    
    @Autowired
    private ForumPostMapper forumPostMapper;
    
    @Autowired
    private ForumCommentMapper forumCommentMapper;
    
    // 状态常量
    private static final int STATUS_PENDING = 0;     // 待处理
    private static final int STATUS_PROCESSED = 1;   // 已处理
    private static final int STATUS_IGNORED = 2;     // 已忽略
    
    // 处理类型常量
    private static final int PROCESS_TYPE_NONE = 0;  // 无操作
    private static final int PROCESS_TYPE_DELETE = 1; // 删除
    private static final int PROCESS_TYPE_WARNING = 2; // 警告

    @Override
    public IPage<ForumViolation> getViolationPage(ForumViolationQueryParam param) {
        // 创建分页对象
        Page<ForumViolation> page = new Page<>(param.getPage(), param.getSize());
        
        // 调用Mapper查询
        return forumViolationMapper.selectViolationPage(page, param.getContentType(), param.getStatus());
    }

    @Override
    public ForumViolation getViolationById(Integer id) {
        // 使用专门的方法查询违规举报详情
        ForumViolation violation = forumViolationMapper.selectViolationWithDetailsById(id);
        
        if (violation == null) {
            throw new BusinessException("违规举报不存在");
        }
        
        return violation;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processViolation(Integer id, Integer status, String result, Integer operatorId) {
        // 检查状态是否合法
        if (!isValidStatus(status)) {
            throw new BusinessException("无效的状态值，应为1(已处理)或2(已忽略)");
        }
        
        // 检查违规举报是否存在
        ForumViolation violation = forumViolationMapper.selectById(id);
        if (violation == null) {
            throw new BusinessException("违规举报不存在");
        }
        
        // 检查举报是否已经处理
        if (violation.getStatus() != STATUS_PENDING) {
            throw new BusinessException("该举报已经处理，不能重复处理");
        }
        
        // 确定处理类型
        int processType = PROCESS_TYPE_NONE;
        
        // 如果是处理状态且处理结果包含"删除"关键词，则执行假删除操作
        if (status == STATUS_PROCESSED && result != null && result.contains("删除")) {
            processType = PROCESS_TYPE_DELETE;
            
            // 根据举报类型删除对应内容
            if ("post".equals(violation.getContentType())) {
                // 假删除帖子（更新状态为已删除）
                forumPostMapper.softDeletePost(violation.getContentId());
            } else if ("comment".equals(violation.getContentType())) {
                // 假删除评论（更新状态为已删除）
                forumCommentMapper.softDeleteComment(violation.getContentId());
            }
        } else if (status == STATUS_PROCESSED && result != null && result.contains("警告")) {
            processType = PROCESS_TYPE_WARNING;
        }
        
        // 更新举报状态和处理类型
        return forumViolationMapper.updateStatus(id, status, processType, result, operatorId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchProcessViolations(List<Integer> ids, Integer status, String result, Integer operatorId) {
        // 检查状态是否合法
        if (!isValidStatus(status)) {
            throw new BusinessException("无效的状态值，应为1(已处理)或2(已忽略)");
        }
        
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // 确定处理类型
        int processType = PROCESS_TYPE_NONE;
        
        // 如果是处理状态且处理结果包含"删除"关键词，则设置处理类型为删除
        if (status == STATUS_PROCESSED && result != null && result.contains("删除")) {
            processType = PROCESS_TYPE_DELETE;
        } else if (status == STATUS_PROCESSED && result != null && result.contains("警告")) {
            processType = PROCESS_TYPE_WARNING;
        }
        
        // 遍历批量更新
        boolean success = true;
        for (Integer id : ids) {
            try {
                // 获取违规举报
                ForumViolation violation = forumViolationMapper.selectById(id);
                if (violation == null || violation.getStatus() != STATUS_PENDING) {
                    continue; // 跳过不存在或已处理的举报
                }
                
                // 如果处理类型是删除，则执行假删除操作
                if (processType == PROCESS_TYPE_DELETE) {
                    // 根据举报类型删除对应内容
                    if ("post".equals(violation.getContentType())) {
                        // 假删除帖子（更新状态为已删除）
                        forumPostMapper.softDeletePost(violation.getContentId());
                    } else if ("comment".equals(violation.getContentType())) {
                        // 假删除评论（更新状态为已删除）
                        forumCommentMapper.softDeleteComment(violation.getContentId());
                    }
                }
                
                // 更新举报状态和处理类型
                if (forumViolationMapper.updateStatus(id, status, processType, result, operatorId) <= 0) {
                    success = false;
                }
            } catch (Exception e) {
                success = false;
            }
        }
        
        return success;
    }
    
    /**
     * 检查状态是否合法
     * 
     * @param status 状态
     * @return 是否合法
     */
    private boolean isValidStatus(Integer status) {
        return status == STATUS_PROCESSED || status == STATUS_IGNORED;
    }
} 