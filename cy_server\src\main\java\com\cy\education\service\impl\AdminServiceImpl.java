package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.Admin;
import com.cy.education.model.params.*;
import com.cy.education.model.vo.AdminVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.AdminMapper;
import com.cy.education.repository.AdminPermissionMapper;
import com.cy.education.service.AdminService;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员服务实现类
 * 
 * 注意：本类处理前端与后端属性命名差异
 * - 前端使用realName属性
 * - 后端实体类使用name属性
 * - VO使用realName属性
 * 
 * 参数类通过添加getName()方法兼容这种差异
 */
@Service
@RequiredArgsConstructor
public class AdminServiceImpl implements AdminService {

    private final AdminMapper adminMapper;
    private final AdminPermissionMapper adminPermissionMapper;
    private final PasswordEncoder passwordEncoder;

    @Override
    public PageResponse<AdminVO> getAdminList(AdminQueryParams params) {
        Page<Admin> page = new Page<>(params.getPage(), params.getSize());
        IPage<Admin> result = adminMapper.selectAdminPage(page, params.getKeyword(), 
                params.getDepartmentId(), params.getStatus());
        
        List<AdminVO> adminVOList = result.getRecords().stream().map(this::convertToVO).collect(Collectors.toList());
        
        return PageResponse.of(adminVOList, result.getTotal(), params.getPage(), params.getSize());
    }

    @Override
    public AdminVO getAdminById(Integer id) {
        Admin admin = adminMapper.selectAdminById(id);
        if (admin == null) {
            throw new RuntimeException("管理员不存在");
        }
        
        AdminVO adminVO = convertToVO(admin);
        // 获取权限列表
        List<Integer> permissions = adminPermissionMapper.selectPermissionIdsByAdminId(id);
        adminVO.setPermissions(permissions);
        
        return adminVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addAdmin(AdminAddParams params) {
        // 检查用户名是否已存在
        if (isUsernameExists(params.getUsername(), null)) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 创建管理员对象（ID由数据库自动生成）
        Admin admin = new Admin();
        admin.setUsername(params.getUsername());
        admin.setPassword(passwordEncoder.encode(params.getPassword()));
        admin.setName(params.getName());
        admin.setPhone(params.getPhone());
        admin.setEmail(params.getEmail());
        admin.setDepartmentId(params.getDepartmentId());
        admin.setStatus(params.getStatus());
        admin.setRemark(params.getRemark());
        admin.setCreatedAt(LocalDateTime.now());
        admin.setUpdatedAt(LocalDateTime.now());
        
        // 插入数据库
        int result = adminMapper.insert(admin);
        if (result <= 0) {
            throw new RuntimeException("新增管理员失败");
        }
        
        return admin.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAdmin(AdminUpdateParams params) {
        // 检查管理员是否存在
        Admin existingAdmin = adminMapper.selectById(params.getId());
        if (existingAdmin == null) {
            throw new RuntimeException("管理员不存在");
        }
        
        // 检查用户名是否已存在（排除当前管理员）
        if (isUsernameExists(params.getUsername(), params.getId())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 更新管理员信息
        Admin admin = new Admin();
        admin.setId(params.getId());
        admin.setUsername(params.getUsername());
        admin.setName(params.getName());
        admin.setPhone(params.getPhone());
        admin.setEmail(params.getEmail());
        admin.setDepartmentId(params.getDepartmentId());
        admin.setStatus(params.getStatus());
        admin.setRemark(params.getRemark());
        admin.setUpdatedAt(LocalDateTime.now());
        
        return adminMapper.updateById(admin) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAdmin(Integer id) {
        // 检查管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new RuntimeException("管理员不存在");
        }
        
        // 检查是否为超级管理员
        if ("admin".equals(admin.getUsername())) {
            throw new RuntimeException("不能删除超级管理员");
        }
        
        // 删除管理员
        int result = adminMapper.deleteById(id);
        if (result > 0) {
            // 删除权限关联
            adminPermissionMapper.deleteByAdminId(id);
        }
        
        return result > 0;
    }

    @Override
    public boolean updateAdminStatus(AdminStatusParams params) {
        Admin admin = new Admin();
        admin.setId(params.getId());
        admin.setStatus(params.getStatus());
        admin.setUpdatedAt(LocalDateTime.now());
        
        return adminMapper.updateById(admin) > 0;
    }

    @Override
    public String resetAdminPassword(Integer id) {
        // 检查管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new RuntimeException("管理员不存在");
        }
        
        // 生成新密码（6位随机数字）
        String newPassword = String.valueOf((int)((Math.random() * 900000) + 100000));
        
        // 更新密码
        Admin updateAdmin = new Admin();
        updateAdmin.setId(id);
        updateAdmin.setPassword(passwordEncoder.encode(newPassword));
        updateAdmin.setUpdatedAt(LocalDateTime.now());
        
        int result = adminMapper.updateById(updateAdmin);
        if (result <= 0) {
            throw new RuntimeException("重置密码失败");
        }
        
        return newPassword;
    }

    @Override
    public List<Integer> getAdminPermissions(Integer id) {
        return adminPermissionMapper.selectPermissionIdsByAdminId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setAdminPermissions(AdminPermissionParams params) {
        // 检查管理员是否存在
        Admin admin = adminMapper.selectById(params.getId());
        if (admin == null) {
            throw new RuntimeException("管理员不存在");
        }
        
        // 删除原有权限
        adminPermissionMapper.deleteByAdminId(params.getId());
        
        // 添加新权限
        if (params.getPermissions() != null && !params.getPermissions().isEmpty()) {
            adminPermissionMapper.batchInsert(params.getId(), params.getPermissions());
        }
        
        return true;
    }

    @Override
    public boolean isUsernameExists(String username, Integer excludeId) {
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Admin::getUsername, username);
        
        if (excludeId != null) {
            queryWrapper.ne(Admin::getId, excludeId);
        }
        
        return adminMapper.selectCount(queryWrapper) > 0;
    }

    // ==================== 导入导出功能 ====================

    @Override
    public Resource generateImportTemplate() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("管理员导入模板");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"姓名*", "用户名*", "密码*", "手机号*", "邮箱", "部门名称"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 创建示例数据行
        Row dataRow1 = sheet.createRow(1);
        dataRow1.createCell(0).setCellValue("张三");
        dataRow1.createCell(1).setCellValue("zhangsan");
        dataRow1.createCell(2).setCellValue("123456");
        dataRow1.createCell(3).setCellValue("13800138001");
        dataRow1.createCell(4).setCellValue("<EMAIL>");
        dataRow1.createCell(5).setCellValue("技术部");

        Row dataRow2 = sheet.createRow(2);
        dataRow2.createCell(0).setCellValue("李四");
        dataRow2.createCell(1).setCellValue("lisi");
        dataRow2.createCell(2).setCellValue("123456");
        dataRow2.createCell(3).setCellValue("13800138002");
        dataRow2.createCell(4).setCellValue("<EMAIL>");
        dataRow2.createCell(5).setCellValue("人事部");

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new ByteArrayResource(outputStream.toByteArray());
    }

    @Override
    @Transactional
    public Map<String, Object> importAdmins(MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        List<Map<String, String>> dataList = new ArrayList<>();

        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            // 跳过标题行，从第二行开始读取数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                Map<String, String> data = new HashMap<>();
                data.put("realName", getCellStringValue(row.getCell(0)));
                data.put("username", getCellStringValue(row.getCell(1)));
                data.put("password", getCellStringValue(row.getCell(2)));
                data.put("phone", getCellStringValue(row.getCell(3)));
                data.put("email", getCellStringValue(row.getCell(4)));
                data.put("departmentName", getCellStringValue(row.getCell(5)));

                dataList.add(data);
            }
        } catch (IOException e) {
            throw new BusinessException("文件读取失败: " + e.getMessage());
        }

        int successCount = 0;
        int failureCount = 0;
        List<Map<String, Object>> failures = new ArrayList<>();

        for (int i = 0; i < dataList.size(); i++) {
            Map<String, String> data = dataList.get(i);
            try {
                // 验证数据
                validateAdminData(data, i + 2); // Excel行号从2开始

                // 检查用户名是否已存在
                if (isUsernameExists(data.get("username"), null)) {
                    throw new RuntimeException("用户名已存在");
                }

                // 创建管理员
                Admin admin = new Admin();
                admin.setName(data.get("realName"));
                admin.setUsername(data.get("username"));
                admin.setPassword(passwordEncoder.encode(data.get("password")));
                admin.setPhone(data.get("phone"));
                admin.setEmail(data.get("email"));
                admin.setDepartmentName(data.get("departmentName"));
                admin.setStatus(1);
                admin.setCreatedAt(LocalDateTime.now());
                admin.setUpdatedAt(LocalDateTime.now());

                adminMapper.insert(admin);
                successCount++;

            } catch (Exception e) {
                failureCount++;
                Map<String, Object> failure = new HashMap<>();
                failure.put("row", i + 2);
                failure.put("data", data.toString());
                failure.put("reason", e.getMessage());
                failures.add(failure);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);
        result.put("failures", failures);

        return result;
    }

    @Override
    public void exportAdmins(Map<String, Object> params, HttpServletResponse response) throws IOException {
        // 构建查询条件
        String keyword = (String) params.get("keyword");
        Integer departmentId = (Integer) params.get("departmentId");
        Integer status = (Integer) params.get("status");
        String format = (String) params.getOrDefault("format", "xlsx");
        List<String> fields = (List<String>) params.get("fields");
        String range = (String) params.getOrDefault("range", "all");
        List<Integer> selectedIds = (List<Integer>) params.get("selectedIds");

        // 查询管理员数据
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like(Admin::getName, keyword)
                .or().like(Admin::getUsername, keyword)
                .or().like(Admin::getPhone, keyword));
        }
        if (departmentId != null) {
            queryWrapper.eq(Admin::getDepartmentId, departmentId);
        }
        if (status != null) {
            queryWrapper.eq(Admin::getStatus, status);
        }
        if ("selected".equals(range) && selectedIds != null && !selectedIds.isEmpty()) {
            queryWrapper.in(Admin::getId, selectedIds);
        }

        List<Admin> admins = adminMapper.selectList(queryWrapper);

        // 设置响应头
        String fileName = URLEncoder.encode("管理员列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + "." + format, StandardCharsets.UTF_8.toString());
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        // 使用Apache POI写入Excel
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("管理员列表");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] allHeaders = {"姓名", "用户名", "手机号", "邮箱", "部门", "状态", "最后登录时间", "创建时间"};
            String[] selectedHeaders = fields != null && !fields.isEmpty() ?
                fields.toArray(new String[0]) : allHeaders;

            for (int i = 0; i < selectedHeaders.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(selectedHeaders[i]);
            }

            // 写入数据
            for (int i = 0; i < admins.size(); i++) {
                Admin admin = admins.get(i);
                Row row = sheet.createRow(i + 1);

                int colIndex = 0;
                for (String header : selectedHeaders) {
                    Cell cell = row.createCell(colIndex++);
                    switch (header) {
                        case "姓名":
                        case "realName":
                            cell.setCellValue(admin.getName());
                            break;
                        case "用户名":
                        case "username":
                            cell.setCellValue(admin.getUsername());
                            break;
                        case "手机号":
                        case "phone":
                            cell.setCellValue(admin.getPhone());
                            break;
                        case "邮箱":
                        case "email":
                            cell.setCellValue(admin.getEmail());
                            break;
                        case "部门":
                        case "department":
                        case "departmentName":
                            cell.setCellValue(admin.getDepartmentName());
                            break;
                        case "状态":
                        case "status":
                            cell.setCellValue(admin.getStatus() == 1 ? "启用" : "禁用");
                            break;
                        case "最后登录时间":
                        case "lastLoginTime":
                            cell.setCellValue(admin.getLastLoginTime() != null ? admin.getLastLoginTime().toString() : "");
                            break;
                        case "创建时间":
                        case "createTime":
                        case "createdAt":
                            cell.setCellValue(admin.getCreatedAt() != null ? admin.getCreatedAt().toString() : "");
                            break;
                    }
                }
            }

            // 自动调整列宽
            for (int i = 0; i < selectedHeaders.length; i++) {
                sheet.autoSizeColumn(i);
            }

            workbook.write(response.getOutputStream());
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 验证管理员数据
     */
    private void validateAdminData(Map<String, String> data, int rowNum) {
        if (!StringUtils.hasText(data.get("realName"))) {
            throw new RuntimeException("第" + rowNum + "行：姓名不能为空");
        }
        if (!StringUtils.hasText(data.get("username"))) {
            throw new RuntimeException("第" + rowNum + "行：用户名不能为空");
        }
        if (!StringUtils.hasText(data.get("password"))) {
            throw new RuntimeException("第" + rowNum + "行：密码不能为空");
        }
        if (!StringUtils.hasText(data.get("phone"))) {
            throw new RuntimeException("第" + rowNum + "行：手机号不能为空");
        }
        // 可以添加更多验证逻辑
    }

    /**
     * 将Admin实体转换为AdminVO
     */
    private AdminVO convertToVO(Admin admin) {
        AdminVO adminVO = new AdminVO();
        BeanUtils.copyProperties(admin, adminVO);

        // 设置realName字段（实体中是name字段）
        adminVO.setRealName(admin.getName());
        // 设置createTime字段（实体中是createdAt字段）
        adminVO.setCreateTime(admin.getCreatedAt());
        // 设置department字段（实体中是departmentName字段）
        adminVO.setDepartment(admin.getDepartmentName());

        return adminVO;
    }
}