<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.ExamExamDepartmentMapper">

    <!-- 批量插入考试部门关联 -->
    <insert id="batchInsert">
        INSERT INTO exam_exam_department (exam_id, department_id, created_at)
        VALUES
        <foreach collection="departmentIds" item="departmentId" separator=",">
            (#{examId}, #{departmentId}, NOW())
        </foreach>
    </insert>

    <!-- 根据考试ID删除所有部门关联 -->
    <delete id="deleteByExamId">
        DELETE FROM exam_exam_department
        WHERE exam_id = #{examId}
    </delete>
</mapper> 