package com.cy.education.service.study;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cy.education.model.entity.study.StudyRecord;
import com.cy.education.model.params.PageParams;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.model.vo.StudyStatisticsVO;
import com.cy.education.model.vo.CourseRecordVO;
import com.cy.education.model.vo.CourseRecordDetailVO;

import java.util.Map;

/**
 * 学习记录服务接口
 */
public interface StudyRecordService extends IService<StudyRecord> {

    /**
     * 获取课程学习记录分页列表
     */
    PageResponse<CourseRecordVO> getCourseRecordList(Integer userId, PageParams params);

    /**
     * 获取课程详情学习记录（章节）
     */
    CourseRecordDetailVO getCourseRecordDetail(Integer userId, Integer courseId);

    /**
     * 创建或更新学习记录
     *
     * @param studyRecord 学习记录
     * @return 保存后的学习记录
     */
    StudyRecord saveStudyRecord(StudyRecord studyRecord);

    /**
     * 记录资源访问日志
     *
     * @param accessLog 访问日志信息
     * @return 是否记录成功
     */
    boolean logResourceAccess(Map<String, Object> accessLog);

    /**
     * 获取学员的学习统计数据
     *
     * @param studentId 学员ID
     * @param courseId  课程ID（可选）
     * @return 学习统计数据
     */
    StudyStatisticsVO getStudyStatistics(Integer studentId, Integer courseId);

}
