package com.cy.education.config;

import com.cy.education.service.PkService;
import com.cy.education.websocket.PkWebSocketServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * WebSocket服务依赖注入配置
 */
@Configuration
public class WebSocketServiceConfig {
    
    @Autowired
    private PkService pkService;
    
    /**
     * 在Spring容器初始化完成后，将服务注入到WebSocket服务器中
     */
    @PostConstruct
    public void init() {
        PkWebSocketServer.setPkService(pkService);
    }
}
