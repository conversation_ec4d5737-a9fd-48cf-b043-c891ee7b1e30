package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 题目实体类
 */
@Data
@TableName("exam_question")
public class ExamQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属题库ID
     */
    private Integer bankId;

    /**
     * 题目标题
     */
    private String title;

    /**
     * 题目类型(single-单选题,multiple-多选题,judgment-判断题,fill-填空题,essay-简答题)
     */
    private String type;

    /**
     * 选项JSON(单选、多选题使用)
     */
    private String options;

    /**
     * 正确答案
     */
    private String correctAnswer;

    /**
     * 题目解析
     */
    private String explanation;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人ID
     */
    private String createdBy;

    /**
     * 是否删除(0-未删除，1-已删除)
     */
    @TableLogic
    private Integer deleted;
} 