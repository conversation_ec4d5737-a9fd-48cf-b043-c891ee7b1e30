<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.ExamPaperMapper">
    <!-- 分页查询试卷列表 -->
    <select id="selectPaperPage" resultType="com.cy.education.model.entity.exam.ExamPaper">
        SELECT *
        FROM exam_paper
        <where>
            deleted = 0
            <if test="keyword != null and keyword != ''">
                AND (title LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="isPublished != null">
                AND is_published = #{isPublished}
            </if>
        </where>
        <if test="sortBy != null and sortBy != ''">
            ORDER BY
            <choose>
                <when test="sortBy == 'createdAt'">
                    created_at
                </when>
                <when test="sortBy == 'updatedAt'">
                    updated_at
                </when>
                <when test="sortBy == 'title'">
                    title
                </when>
                <when test="sortBy == 'duration'">
                    duration
                </when>
                <when test="sortBy == 'totalScore'">
                    total_score
                </when>
                <otherwise>
                    created_at
                </otherwise>
            </choose>
            <if test="sortOrder != null and sortOrder == 'asc'">
                ASC
            </if>
            <if test="sortOrder == null or sortOrder != 'asc'">
                DESC
            </if>
        </if>
        <if test="sortBy == null or sortBy == ''">
            ORDER BY created_at DESC
        </if>
    </select>
</mapper>
