package com.cy.education.model.vo;

import com.cy.education.model.entity.exam.ExamExam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 考试VO类
 */
@Data
@ApiModel("考试信息")
public class ExamVO extends ExamExam {

    /**
     * 试卷信息
     */
    @ApiModelProperty("试卷信息")
    private ExamPaperVO paper;

    /**
     * 当前用户答题记录
     */
    @ApiModelProperty("当前用户答题记录")
    private ExamRecordVO currentUserRecord;

    /**
     * 参考部门ID列表
     */
    @ApiModelProperty("参考部门ID列表")
    private List<Integer> departmentIds;

    /**
     * 参考部门名称列表
     */
    @ApiModelProperty("参考部门名称列表")
    private List<String> departments;

}
