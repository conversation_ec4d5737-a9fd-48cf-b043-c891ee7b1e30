import request from '@/utils/request'

// 获取练习统计列表
export function listPracticeStatistics(params: any) {
  return request({
    url: '/practice/statistics/list',
    method: 'get',
    params
  })
}

// 导出练习统计数据
export function exportPracticeStatistics(params: any) {
  return request({
    url: '/practice/statistics/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出练习详情数据
export function exportPracticeDetail(params: any) {
  return request({
    url: '/practice/statistics/export-detail',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 