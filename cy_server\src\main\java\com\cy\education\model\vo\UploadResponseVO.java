package com.cy.education.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 文件上传响应
 */
@ApiModel("文件上传响应")
public class UploadResponseVO {
    
    @ApiModelProperty("文件URL")
    private String avatarUrl;
    
    @ApiModelProperty("文件名")
    private String fileName;
    
    @ApiModelProperty("文件大小")
    private Long fileSize;
    
    public UploadResponseVO() {}
    
    public UploadResponseVO(String avatarUrl, String fileName, Long fileSize) {
        this.avatarUrl = avatarUrl;
        this.fileName = fileName;
        this.fileSize = fileSize;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public Long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
} 