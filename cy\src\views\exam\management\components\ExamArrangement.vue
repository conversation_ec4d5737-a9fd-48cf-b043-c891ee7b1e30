<template>
  <div class="exam-arrangement">
    <div class="header-actions">
      <el-button type="primary" @click="handleAddExam">新增考试</el-button>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索考试名称"
        clearable
        style="width: 220px; margin-left: 16px"
        @clear="handleSearch"
        @keyup.enter="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <el-table
      v-loading="loading"
      :data="examList"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="name" label="考试名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="paperName" label="试卷" min-width="150" show-overflow-tooltip />
      <el-table-column prop="departmentName" label="参考部门" min-width="120" show-overflow-tooltip />
      <el-table-column prop="startTime" label="开始时间" width="160" align="center" />
      <el-table-column prop="endTime" label="结束时间" width="160" align="center" />
      <el-table-column prop="duration" label="考试时长" width="100" align="center">
        <template #default="{ row }">
          {{ row.duration }}分钟
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="240" align="center">
        <template #default="{ row }">
          <el-button 
            v-if="row.status === 'pending'" 
            type="success" 
            link 
            @click="handleStartExam(row)"
          >
            开始考试
          </el-button>
          <el-button 
            v-if="row.status === 'in_progress'" 
            type="warning" 
            link 
            @click="handleEndExam(row)"
          >
            结束考试
          </el-button>
          <el-button 
            v-if="row.status !== 'completed'" 
            type="primary" 
            link 
            @click="handleEditExam(row)"
          >
            编辑
          </el-button>
          <el-button type="primary" link @click="handleViewRecords(row)">
            查看记录
          </el-button>
          <el-button 
            v-if="row.status === 'pending'" 
            type="danger" 
            link 
            @click="handleDeleteExam(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 考试表单弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑考试' : '新增考试'"
      width="600px"
      destroy-on-close
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="考试名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入考试名称" />
        </el-form-item>
        <el-form-item label="试卷" prop="paperId">
          <el-select v-model="form.paperId" placeholder="请选择试卷" style="width: 100%">
            <el-option
              v-for="item in paperOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="参考部门" prop="departmentIds">
          <el-select
            v-model="form.departmentIds"
            placeholder="请选择参考部门"
            multiple
            style="width: 100%"
          >
            <el-option
              v-for="item in departmentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            placeholder="选择开始时间"
            style="width: 100%"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="datetime"
            placeholder="选择结束时间"
            style="width: 100%"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="考试时长" prop="duration">
          <el-input-number
            v-model="form.duration"
            :min="1"
            :max="240"
            :step="1"
            style="width: 180px"
          />
          <span style="margin-left: 10px">分钟</span>
        </el-form-item>
        <el-form-item label="及格分数" prop="passingScore">
          <el-input-number
            v-model="form.passingScore"
            :min="0"
            :max="100"
            :step="1"
            style="width: 180px"
          />
          <span style="margin-left: 10px">分</span>
        </el-form-item>
        <el-form-item label="考试说明" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            rows="3"
            placeholder="请输入考试说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getExamList, getPaperList, createExam, updateExam, deleteExam, publishExam, updateExamStatus, type Exam } from '@/api/exam'
import { getDepartmentTree, getDepartmentAndChildrenIds, type Department } from '@/api/department'

const router = useRouter()

// 定义组件事件
const emit = defineEmits<{
  'view-records': [examId: string]
}>()

// 表格数据和分页
const loading = ref(false)
const examList = ref<any[]>([])
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 弹窗相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref<any>(null)

// 表单数据
const form = reactive({
  id: '',
  name: '',
  paperId: '',
  departmentIds: [] as string[],
  startTime: '',
  endTime: '',
  duration: 120,
  passingScore: 60,
  description: ''
})

// 验证规则
const rules = {
  name: [{ required: true, message: '请输入考试名称', trigger: 'blur' }],
  paperId: [{ required: true, message: '请选择试卷', trigger: 'change' }],
  departmentIds: [{ required: true, message: '请选择参考部门', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  duration: [{ required: true, message: '请输入考试时长', trigger: 'blur' }],
  passingScore: [{ required: true, message: '请输入及格分数', trigger: 'blur' }]
}

// 选项数据
const paperOptions = ref<{ value: string, label: string }[]>([])
const departmentOptions = ref<{ value: string, label: string }[]>([])
const departmentMap = ref<Map<string, Department>>(new Map())

// 初始化试卷选项
const initPaperOptions = async () => {
  try {
    const res = await getPaperList({
      page: 1,
      limit: 100,
      isPublished: true // 只显示已发布的试卷
    })
    paperOptions.value = res.list.map(paper => ({
      value: paper.id,
      label: paper.title
    }))
  } catch (error) {
    console.error('获取试卷列表失败:', error)
  }
}

// 初始化部门选项
const initDepartmentOptions = async () => {
  try {
    const res = await getDepartmentTree()
    departmentOptions.value = convertDepartmentTreeToOptions(res)
    
    // 创建部门ID到部门对象的映射，方便后续使用
    const flattenDepartments = (departments: Department[]): void => {
      departments.forEach(dept => {
        departmentMap.value.set(dept.id.toString(), dept)
        if (dept.children && dept.children.length > 0) {
          flattenDepartments(dept.children)
        }
      })
    }
    
    flattenDepartments(res)
  } catch (error) {
    console.error('获取部门树失败:', error)
    ElMessage.error('获取部门树失败')
  }
}

// 将部门树转换为扁平的选项数组
const convertDepartmentTreeToOptions = (departments: Department[], prefix = ''): { value: string, label: string }[] => {
  let options: { value: string, label: string }[] = []
  
  departments.forEach(dept => {
    const label = prefix ? `${prefix} / ${dept.name}` : dept.name
    options.push({ value: dept.id.toString(), label })
    
    if (dept.children && dept.children.length > 0) {
      options = options.concat(convertDepartmentTreeToOptions(dept.children, label))
    }
  })
  
  return options
}

// 获取考试列表
const fetchExamList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      keyword: searchKeyword.value || undefined,
      sortBy: 'updatedAt',
      sortOrder: 'desc' as 'desc' | 'asc'
    }
    
    const res = await getExamList(params)
    examList.value = res.list.map(exam => ({
      id: exam.id,
      name: exam.title,
      paperId: exam.paperId,
      paperName: exam.paper?.title || '未知试卷',
      departmentIds: exam.departmentIds,
      departmentName: exam.departments?.join(', ') || '未设置',
      startTime: formatDateTime(exam.startTime),
      endTime: formatDateTime(exam.endTime),
      duration: exam.duration,
      passingScore: exam.passingScore,
      status: getExamStatus(exam),
      description: exam.description,
      participantCount: exam.participantCount,
      completedCount: exam.completedCount,
      passedCount: exam.passedCount,
      isPublished: exam.isPublished
    }))
    
    total.value = res.total
  } catch (error) {
    console.error('获取考试列表失败:', error)
    ElMessage.error('获取考试列表失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取考试状态
const getExamStatus = (exam: Exam) => {
  const now = new Date()
  const startTime = new Date(exam.startTime)
  const endTime = new Date(exam.endTime)
  
  if (!exam.isPublished) {
    return 'draft'
  } else if (now < startTime) {
    return 'pending'
  } else if (now >= startTime && now <= endTime) {
    return 'in_progress'
  } else {
    return 'completed'
  }
}

// 获取状态显示文本
const getStatusText = (status: string) => {
  const statusMap: { [key: string]: string } = {
    'draft': '草稿',
    'pending': '未开始',
    'in_progress': '进行中',
    'completed': '已结束'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  const typeMap: { [key: string]: string } = {
    'draft': 'info',
    'pending': 'warning',
    'in_progress': 'success',
    'completed': 'default'
  }
  return typeMap[status] || 'info'
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchExamList()
}

// 分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchExamList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchExamList()
}

// 新增考试
const handleAddExam = () => {
  isEdit.value = false
  Object.keys(form).forEach((key: string) => {
    const formKey = key as keyof typeof form
    if (formKey === 'duration') {
      form[formKey] = 60
    } else if (formKey === 'passingScore') {
      form[formKey] = 60
    } else if (formKey === 'departmentIds') {
      form[formKey] = []
    } else {
      (form as any)[formKey] = ''
    }
  })
  dialogVisible.value = true
}

// 编辑考试
const handleEditExam = (row: any) => {
  isEdit.value = true
  
  // 处理departmentIds，确保在form中显示为字符串数组
  const deptIds = Array.isArray(row.departmentIds) 
    ? row.departmentIds.map((id: number | string) => id.toString()) 
    : (row.departmentIds ? [row.departmentIds.toString()] : []);
  
  Object.assign(form, {
    id: row.id,
    name: row.name,
    paperId: row.paperId,
    departmentIds: deptIds,
    startTime: row.startTime,
    endTime: row.endTime,
    duration: row.duration,
    passingScore: row.passingScore,
    description: row.description
  });
  
  dialogVisible.value = true;
}

// 提交表单
const handleSubmitForm = () => {
  if (formRef.value) {
    formRef.value.validate(async (valid: boolean) => {
      if (valid) {
        // 检查结束时间是否大于开始时间
        const startTime = new Date(form.startTime).getTime()
        const endTime = new Date(form.endTime).getTime()
        
        if (endTime <= startTime) {
          ElMessage.error('结束时间必须大于开始时间')
          return
        }
        
        try {
          // 处理部门ID列表，包含子部门
          const allDepartmentIds: string[] = []
          const promises = form.departmentIds.map(async (deptId) => {
            try {
              // 获取部门及其子部门ID
              const res = await getDepartmentAndChildrenIds(parseInt(deptId))
              // 将ID转为字符串并添加到列表
              res.forEach(id => {
                if (!allDepartmentIds.includes(id.toString())) {
                  allDepartmentIds.push(id.toString())
                }
              })
            } catch (error) {
              console.error(`获取部门${deptId}的子部门失败:`, error)
            }
          })
          
          // 等待所有部门处理完成
          await Promise.all(promises)
          
          // 准备提交的数据
          const examData = {
            title: form.name,
            description: form.description,
            paperId: form.paperId,
            departmentIds: allDepartmentIds.length > 0 ? allDepartmentIds : form.departmentIds,
            startTime: form.startTime,
            endTime: form.endTime,
            duration: form.duration,
            passingScore: form.passingScore,
            maxAttempts: 1 // 默认最大考试次数为1
          }
          
          if (isEdit.value) {
            // 更新考试
            await updateExam(form.id, examData)
            ElMessage.success('考试更新成功')
          } else {
            // 创建考试
            await createExam(examData)
            ElMessage.success('考试创建成功')
          }
          
          dialogVisible.value = false
          fetchExamList() // 刷新列表
        } catch (error) {
          console.error('保存考试失败:', error)
          ElMessage.error('保存考试失败，请重试')
        }
      }
    })
  }
}

// 开始考试
const handleStartExam = (row: any) => {
  ElMessageBox.confirm(
    `确定要开始考试"${row.name}"吗？开始后考生可以进入考试`,
    '开始确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(async () => {
    try {
      // 更新考试状态为进行中(2)
      await updateExamStatus(row.id, 2)
      ElMessage.success('考试已开始')
      fetchExamList() // 刷新列表
    } catch (error) {
      console.error('开始考试失败:', error)
      ElMessage.error('开始考试失败，请重试')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 结束考试
const handleEndExam = (row: any) => {
  ElMessageBox.confirm(
    `确定要结束考试"${row.name}"吗？结束后考生将无法继续考试`,
    '结束确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 更新考试状态为已结束(3)
      await updateExamStatus(row.id, 3)
      ElMessage.success('考试已结束')
      fetchExamList() // 刷新列表
    } catch (error) {
      console.error('结束考试失败:', error)
      ElMessage.error('结束考试失败，请重试')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 删除考试
const handleDeleteExam = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除考试"${row.name}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteExam(row.id)
      ElMessage.success('删除成功')
      fetchExamList() // 刷新列表
    } catch (error) {
      console.error('删除考试失败:', error)
      ElMessage.error('删除考试失败，请重试')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 查看考试记录
const handleViewRecords = (row: any) => {
  // 发射事件给父组件处理tab切换
  emit('view-records', row.id)
}

// 页面加载时获取数据
onMounted(() => {
  fetchExamList()
  initPaperOptions()
  initDepartmentOptions()
})
</script>

<style scoped>
.exam-arrangement {
  width: 100%;
}

.header-actions {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 