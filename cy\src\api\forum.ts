import { get, post, put, del } from '@/utils/request'

// 响应格式
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

/**
 * 论坛帖子接口数据类型
 */
export interface Post {
  id: number
  title: string
  content: string
  author: string
  authorId: number
  authorAvatar: string
  category: string
  categoryId: number
  viewCount: number
  replyCount: number // 后端统计：回复数量
  createTime: string
  status: number // 0待审核, 1已通过, 2已拒绝, 3已删除
  isTop: boolean
  isEssence: boolean
}

/**
 * 论坛评论接口数据类型
 */
export interface Comment {
  id: number
  content: string
  postId: number
  postTitle: string
  parentId?: number | null
  author: string
  authorId: number
  authorAvatar: string
  createTime: string
  status: number // 0待审核, 1已通过, 2已拒绝, 3已删除
  children?: Comment[]
}

/**
 * 论坛分类接口数据类型
 */
export interface Category {
  id: number
  name: string
  parentId?: number | null
  description: string
  icon?: string
  sort: number
  status: number
  postCount: number // 后端统计：帖子数量
  createTime: string
  isParent?: boolean
  children?: Category[]
}

/**
 * 违规内容接口数据类型
 */
export interface ViolationContent {
  id: number
  contentType: 'post' | 'comment'
  contentId: number
  content: string
  reporter: string
  reporterId: number
  reportReason: string
  reportTime: string
  status: number // 0待处理, 1已处理, 2已忽略
  processType?: number // 0无操作, 1删除, 2警告
  processResult?: string
  processTime?: string
  operator?: string
}

/**
 * 帖子查询参数
 */
export interface PostQueryParams {
  keyword?: string
  status?: number | string
  categoryId?: number
  authorId?: number
  page?: number
  size?: number
}

/**
 * 评论查询参数
 */
export interface CommentQueryParams {
  keyword?: string
  status?: number | string
  postId?: number
  page?: number
  size?: number
}

/**
 * 违规内容查询参数
 */
export interface ViolationQueryParams {
  contentType?: 'post' | 'comment'
  status?: number | string
  page?: number
  size?: number
}

/**
 * 获取帖子列表
 * @param params 查询参数
 */
export function getPostList(params: PostQueryParams) {
  return get<ApiResponse<{
    list: Post[]
    total: number
  }>>('/forum/post/list', params)
}

/**
 * 获取帖子详情
 * @param id 帖子ID
 */
export function getPostById(id: number) {
  return get<ApiResponse<Post>>(`/forum/post/${id}`)
}

/**
 * 审核帖子
 * @param id 帖子ID
 * @param status 审核状态 1:已通过, 2:已拒绝
 * @param reason 拒绝理由（如果状态为rejected）
 */
export function reviewPost(id: number, status: number, reason?: string) {
  return put<ApiResponse<{ success: boolean }>>(`/forum/post/${id}/review`, { status, reason })
}

/**
 * 设置帖子置顶状态
 * @param id 帖子ID
 * @param isTop 是否置顶
 */
export function setPostTop(id: number, isTop: boolean) {
  return put<ApiResponse<{ success: boolean }>>(`/forum/post/${id}/top`, { isTop })
}

/**
 * 设置帖子精华状态
 * @param id 帖子ID
 * @param isEssence 是否精华
 */
export function setPostEssence(id: number, isEssence: boolean) {
  return put<ApiResponse<{ success: boolean }>>(`/forum/post/${id}/essence`, { isEssence })
}

/**
 * 删除帖子
 * @param id 帖子ID
 */
export function deletePost(id: number) {
  return del<ApiResponse<{ success: boolean }>>(`/forum/post/${id}`)
}

/**
 * 批量审核帖子
 * @param ids 帖子ID数组
 * @param status 审核状态 1:已通过, 2:已拒绝
 */
export function batchReviewPosts(ids: number[], status: number, reason?: string) {
  return post<ApiResponse<{ success: boolean }>>('/forum/post/batch-review', { ids, status, reason })
}

/**
 * 批量删除帖子
 * @param ids 帖子ID数组
 */
export function batchDeletePosts(ids: number[]) {
  return post<ApiResponse<{ success: boolean }>>('/forum/post/batch-delete', { ids })
}

/**
 * 获取评论列表
 * @param params 查询参数
 */
export function getCommentList(params: CommentQueryParams) {
  return get<ApiResponse<{
    list: Comment[]
    total: number
  }>>('/forum/comment/list', params)
}

/**
 * 获取帖子的评论列表（树形结构）
 * @param postId 帖子ID
 */
export function getPostComments(postId: number) {
  return get<ApiResponse<Comment[]>>(`/forum/post/${postId}/comments`)
}

/**
 * 获取评论详情
 * @param id 评论ID
 */
export function getCommentById(id: number) {
  return get<ApiResponse<Comment>>(`/forum/comment/${id}`)
}

/**
 * 审核评论
 * @param id 评论ID
 * @param status 审核状态 1:已通过, 2:已拒绝
 * @param reason 拒绝理由（如果状态为rejected）
 */
export function reviewComment(id: number, status: number, reason?: string) {
  return put<ApiResponse<{ success: boolean }>>(`/forum/comment/${id}/review`, { status, reason })
}

/**
 * 删除评论
 * @param id 评论ID
 */
export function deleteComment(id: number) {
  return del<ApiResponse<{ success: boolean }>>(`/forum/comment/${id}`)
}

/**
 * 批量审核评论
 * @param ids 评论ID数组
 * @param status 审核状态 1:已通过, 2:已拒绝
 */
export function batchReviewComments(ids: number[], status: number, reason?: string) {
  return post<ApiResponse<{ success: boolean }>>('/forum/comment/batch-review', { ids, status, reason })
}

/**
 * 批量删除评论
 * @param ids 评论ID数组
 */
export function batchDeleteComments(ids: number[]) {
  return post<ApiResponse<{ success: boolean }>>('/forum/comment/batch-delete', { ids })
}

/**
 * 获取分类列表
 */
export function getCategoryList(params?: any) {
  return get<ApiResponse<Category[]>>('/forum/category/list', params)
}

/**
 * 获取分类详情
 * @param id 分类ID
 */
export function getCategoryById(id: number) {
  return get<ApiResponse<Category>>(`/forum/category/${id}`)
}

/**
 * 添加分类
 * @param data 分类数据
 */
export function addCategory(data: Partial<Category>) {
  return post<ApiResponse<{ id: number }>>('/forum/category/add', data)
}

/**
 * 更新分类
 * @param data 分类数据
 */
export function updateCategory(data: Partial<Category>) {
  return put<ApiResponse<{ success: boolean }>>('/forum/category/update', data)
}

/**
 * 删除分类
 * @param id 分类ID
 */
export function deleteCategory(id: number) {
  return del<ApiResponse<{ success: boolean }>>(`/forum/category/${id}`)
}

/**
 * 更新分类状态
 * @param id 分类ID
 * @param status 状态 0:禁用 1:启用
 */
export function updateCategoryStatus(id: number, param: { status: number }) {
  return put<ApiResponse<{ success: boolean }>>(`/forum/category/${id}/status`, param)
}

/**
 * 更新分类排序
 * @param data 排序数据 {id: number, sort: number}[]
 */
export function updateCategorySort(data: { id: number, sort: number }[]) {
  return put<ApiResponse<{ success: boolean }>>('/forum/category/sort', data)
}

/**
 * 获取父分类列表（用于下拉选择）
 */
export function getParentCategories() {
  return get<ApiResponse<Category[]>>('/forum/category/parents')
}

/**
 * 获取违规内容列表
 * @param params 查询参数
 */
export function getViolationList(params: ViolationQueryParams) {
  return get<ApiResponse<{
    list: ViolationContent[]
    total: number
  }>>('/forum/violation/list', params)
}

/**
 * 获取违规内容详情
 * @param id 违规内容ID
 */
export function getViolationById(id: number) {
  return get<ApiResponse<ViolationContent>>(`/forum/violation/${id}`)
}

/**
 * 处理违规内容
 * @param id 违规内容ID
 * @param status 1:已处理, 2:已忽略
 * @param result 处理结果
 */
export function processViolation(id: number, status: number, result?: string) {
  return put<ApiResponse<{ success: boolean }>>(`/forum/violation/${id}/process`, { status, result })
}

/**
 * 批量处理违规内容
 * @param ids 违规内容ID数组
 * @param status 1:已处理, 2:已忽略
 * @param result 处理结果
 */
export function batchProcessViolations(ids: number[], status: number, result?: string) {
  return post<ApiResponse<{ success: boolean }>>('/forum/violation/batch-process', { ids, status, result })
} 