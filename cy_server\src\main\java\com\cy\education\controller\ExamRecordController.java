package com.cy.education.controller;

import com.cy.education.model.params.ExamRecordQueryParams;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.ExamRecordVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.security.JwtUserDetails;
import com.cy.education.service.ExamRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 考试记录控制器
 */
@Api(tags = "考试记录接口")
@RestController
@RequestMapping("/exam-record")
@RequiredArgsConstructor
public class ExamRecordController {

    private final ExamRecordService examRecordService;

    @ApiOperation("获取考试记录列表")
    @GetMapping("/list")
    public ApiResponse<PageResponse<ExamRecordVO>> listExamRecords(
            ExamRecordQueryParams params,
            @RequestParam(required = false) String status) {

        // 处理前端传来的字符串状态参数
        if (status != null && !status.isEmpty()) {
            switch (status) {
                case "not_started":
                    params.setStatus(0);
                    break;
                case "in_progress":
                    params.setStatus(1);
                    break;
                case "completed":
                    params.setStatus(2);
                    break;
                case "timeout":
                    params.setStatus(3);
                    break;
            }
        }

        return ApiResponse.success(examRecordService.listExamRecords(params));
    }

    @ApiOperation("获取考试记录详情")
    @GetMapping("/detail/{id}")
    public ApiResponse<ExamRecordVO> getExamRecordDetail(@PathVariable Integer id) {
        return ApiResponse.success(examRecordService.getExamRecordDetail(id));
    }

    @ApiOperation("开始考试")
    @PostMapping("/start/{examId}")
    public ApiResponse<Map<String, Object>> startExam(
            @PathVariable Integer examId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        Integer recordId = examRecordService.startExam(examId, Integer.valueOf(userDetails.getUsername()));
        Map<String, Object> result = new HashMap<>();
        result.put("recordId", recordId);
        return ApiResponse.success(result, "考试开始成功");
    }

    @ApiOperation("提交考试")
    @PostMapping("/submit/{recordId}")
    public ApiResponse<Map<String, Object>> submitExam(
            @PathVariable Integer recordId,
            @RequestBody Map<String, String> params) {
        String answers = params.get("answers");
        ExamRecordVO record = examRecordService.submitExam(recordId, answers);
        
        Map<String, Object> result = new HashMap<>();
        result.put("score", record.getScore());
        result.put("isPassed", record.getPassed());
        return ApiResponse.success(result, "考试提交成功");
    }

    @ApiOperation("获取考试统计数据")
    @GetMapping("/statistics")
    public ApiResponse<Object> getExamStatistics(@RequestParam(required = false) Integer examId) {
        return ApiResponse.success(examRecordService.getExamStatistics(examId));
    }

    @ApiOperation("导出考试成绩单")
    @PostMapping("/export")
    public void exportExamRecords(
            @RequestBody Map<String, Object> params,
            HttpServletResponse response) throws IOException {
        examRecordService.exportExamRecords(params, response);
    }
}