<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.ExamAnswerMapper">

    <!-- 根据记录ID查询答题详情列表 -->
    <select id="selectByRecordId" resultType="com.cy.education.model.vo.ExamAnswerVO">
        SELECT
            ea.*,
            q.title as questionTitle,
            q.type as questionType,
            q.options,
            q.correct_answer as correctAnswer,
            q.explanation
        FROM exam_answer ea
        LEFT JOIN exam_question q ON ea.question_id = q.id
        WHERE ea.record_id = #{recordId}
        ORDER BY ea.id ASC
    </select>

    <!-- 批量插入答题记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO exam_answer (
        record_id,
        question_id,
        answer,
        is_correct,
        score
        )
        VALUES
        <foreach collection="answers" item="item" separator=",">
            (
            #{item.recordId},
            #{item.questionId},
            #{item.answer},
            #{item.isCorrect},
            #{item.score}
            )
        </foreach>
    </insert>
</mapper>
