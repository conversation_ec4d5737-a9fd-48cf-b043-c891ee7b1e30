<template>
  <el-dialog v-model="visible" title="导出学员列表" width="600px" :close-on-click-modal="false">
    <el-form :model="exportForm" label-width="100px">
      <!-- 导出范围 -->
      <el-form-item label="导出范围">
        <el-radio-group v-model="exportForm.range">
          <el-radio label="all">全部学员</el-radio>
          <el-radio label="selected" :disabled="!selectedStudents || selectedStudents.length === 0">
            选中学员 ({{ selectedStudents?.length || 0 }}条)
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 导出字段 -->
      <el-form-item label="导出字段">
        <el-checkbox-group v-model="exportForm.fields">
          <el-checkbox label="姓名">姓名</el-checkbox>
          <el-checkbox label="用户名">用户名</el-checkbox>
          <el-checkbox label="手机号">手机号</el-checkbox>
          <el-checkbox label="邮箱">邮箱</el-checkbox>
          <el-checkbox label="状态">状态</el-checkbox>
          <el-checkbox label="创建时间">创建时间</el-checkbox>
          <el-checkbox label="备注">备注</el-checkbox>
        </el-checkbox-group>
        <div style="margin-top: 10px;">
          <el-button size="small" @click="selectAllFields">全选</el-button>
          <el-button size="small" @click="clearAllFields">清空</el-button>
        </div>
      </el-form-item>

      <!-- 文件格式 -->
      <el-form-item label="文件格式">
        <el-radio-group v-model="exportForm.format">
          <el-radio label="xlsx">Excel格式 (.xlsx)</el-radio>
          <el-radio label="csv">CSV格式 (.csv)</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 搜索条件 -->
      <el-form-item label="搜索条件" v-if="searchParams">
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="关键词">
            {{ searchParams.keyword || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ searchParams.departmentName || '全部' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            {{ getStatusText(searchParams.status) }}
          </el-descriptions-item>
          <el-descriptions-item label="预计记录">
            {{ totalCount || '未知' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleExport"
          :loading="exporting"
          :disabled="exportForm.fields.length === 0"
        >
          开始导出
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { exportStudentsV2 } from '@/api/student'

interface Props {
  modelValue: boolean
  selectedStudents?: any[]
  searchParams?: any
  totalCount?: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 导出表单
const exportForm = ref({
  range: 'all',
  fields: ['姓名', '用户名', '手机号', '邮箱', '状态'],
  format: 'xlsx'
})

const exporting = ref(false)

// 重置表单
const resetForm = () => {
  exportForm.value = {
    range: 'all',
    fields: ['姓名', '用户名', '手机号', '邮箱', '状态'],
    format: 'xlsx'
  }
}

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})

// 全选字段
const selectAllFields = () => {
  exportForm.value.fields = ['姓名', '用户名', '手机号', '邮箱', '状态', '创建时间', '备注']
}

// 清空字段
const clearAllFields = () => {
  exportForm.value.fields = []
}

// 获取状态文本
const getStatusText = (status: any) => {
  if (status === undefined || status === null || status === '') return '全部'
  return status === 1 ? '正常' : '禁用'
}

// 开始导出
const handleExport = async () => {
  if (exportForm.value.fields.length === 0) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }

  try {
    exporting.value = true

    // 构建导出参数
    const params = {
      ...props.searchParams,
      fields: exportForm.value.fields,
      range: exportForm.value.range,
      format: exportForm.value.format
    }

    // 如果是选中导出，添加选中的ID
    if (exportForm.value.range === 'selected' && props.selectedStudents) {
      params.selectedIds = props.selectedStudents.map(item => item.id)
    }

    await exportStudentsV2(params)
    
    ElMessage.success('导出成功，请查看浏览器下载文件夹')
    emit('success')
    visible.value = false
    
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + (error.message || error))
  } finally {
    exporting.value = false
  }
}

// 取消
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}
</style>
