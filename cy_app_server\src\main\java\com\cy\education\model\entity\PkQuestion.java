package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PK题目实体
 */
@Data
@TableName("pk_question")
public class PkQuestion {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 房间ID
     */
    private Long roomId;
    
    /**
     * 题目ID
     */
    private Integer questionId;
    
    /**
     * 题目顺序
     */
    private Integer questionOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
