<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="navbar-left">
          <view class="back-btn" @click="goBack">
            <up-icon color="#fff" name="arrow-left" size="10"></up-icon>
          </view>
          <text class="navbar-title">课程详情</text>
        </view>
        <view class="navbar-right">
          <view class="back-btn" @click="refreshCourseData">
            <up-icon color="#fff" name="reload" size="12"></up-icon>
          </view>
          <view class="back-btn" @click="shareCourse">
            <up-icon color="#fff" name="share" size="12"></up-icon>
          </view>
          <view class="back-btn" @click="toggleFavorite">
            <up-icon :name="isFavorite ? 'heart-fill' : 'heart'" color="#fff" size="12"></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 课程封面区域 -->
    <view class="course-header">
      <view class="cover-container">
        <up-image
            :src="course.coverImageUrl"
            width="100%"
            height="200"
            mode="aspectFill"
            radius="0"
            class="cover-image"
        ></up-image>
        <view class="play-overlay" @click="startLearning">
          <view class="play-btn">
            <up-icon name="play-circle-fill" color="#fff" size="48"></up-icon>
          </view>
        </view>
        <view class="course-info-overlay">
          <text class="course-title">{{ course.name }}</text>
          <view class="progress-section">
            <view class="progress-bar">
              <view :style="{ width: courseProgress + '%' }" class="progress-fill"></view>
            </view>
            <text class="progress-text">学习进度 {{ courseProgress }}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 标签栏 -->
    <view class="tabs-section">
      <view class="tab-container">
        <view
            v-for="(tab, index) in tabList"
            :key="index"
            class="tab-item"
            :class="{ active: currentTab === index }"
            @click="switchTab(index)"
        >
          <text class="tab-text">{{ tab.name }}</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 课程介绍 -->
      <view v-if="currentTab === 0" class="intro-content">
        <view class="info-card">
          <view class="card-header">
            <view class="header-icon">
              <up-icon name="info-circle" color="#667eea" size="20"></up-icon>
            </view>
            <text class="card-title">课程简介</text>
          </view>
          <text class="description-text">{{ course.description }}</text>
        </view>
        <view class="info-card">
          <view class="card-header">
            <view class="header-icon">
              <up-icon color="#43e97b" name="order" size="20"></up-icon>
            </view>
            <text class="card-title">课程统计</text>
          </view>
          <view class="stats-grid">
            <view class="stat-item">
              <text class="stat-value">{{ course.chapters.length }}</text>
              <text class="stat-label">章节数</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ getTotalLessons() }}</text>
              <text class="stat-label">课时数</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ getCompletedLessons() }}</text>
              <text class="stat-label">已完成</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ formatDuration(course.totalStudyDuration) }}</text>
              <text class="stat-label">学习时长</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 课程目录 -->
      <view v-if="currentTab === 1" class="catalog-content">
        <template v-if="course.chapters && course.chapters.length > 0">
          <view
              v-for="(chapter, chapterIndex) in course.chapters"
              :key="chapter.id"
              class="chapter-card"
          >
            <view class="chapter-header" @click="toggleChapter(chapter.id)">
              <view class="chapter-info">
                <text class="chapter-title">{{ chapterIndex + 1 }}. {{ chapter.title }}</text>
                <view class="chapter-meta">
                  <text class="lesson-count">{{ chapter.lessons.length }}个课时</text>
                  <text class="chapter-progress">· 进度 {{ getChapterProgress(chapter) }}%</text>
                </view>
              </view>
              <view class="chapter-toggle">
                <up-icon
                    :name="isChapterOpen(chapter.id) ? 'arrow-up' : 'arrow-down'"
                    color="#8e8e93"
                    size="16"
                ></up-icon>
              </view>
            </view>

            <view v-if="isChapterOpen(chapter.id)" class="lessons-list">
              <view
                  v-for="(lesson, lessonIndex) in chapter.lessons"
                  :key="lesson.id"
                  :class="{ completed: lesson.completed, current: lesson.current }"
                  class="lesson-item"
                  @click="playLesson(lesson)"
              >
                <view class="lesson-icon">
                  <up-icon
                      :color="lesson.completed ? '#43e97b' : lesson.current ? '#667eea' : '#8e8e93'"
                      :name="getLessonIcon(lesson.type)"
                      size="20"
                  ></up-icon>
                </view>

                <view class="lesson-content">
                  <text class="lesson-title">{{ lessonIndex + 1 }}. {{ lesson.title }}</text>
                  <view class="lesson-meta">
                    <text class="lesson-duration">{{ formatDuration(lesson.studyDuration) }}</text>
                    <text v-if="lesson.progress > 0" class="lesson-progress">· {{ lesson.progress }}%</text>
                  </view>
                  <!-- 课时进度条 -->
                  <view v-if="lesson.progress > 0" class="lesson-progress-bar">
                    <view :style="{ width: lesson.progress + '%' }" class="lesson-progress-fill"></view>
                  </view>
                </view>

                <view class="lesson-status">
                  <view v-if="lesson.completed" class="status-badge completed">
                    <up-icon color="#43e97b" name="checkmark" size="14"></up-icon>
                  </view>
                  <view v-else-if="lesson.current" class="status-badge current">
                    <up-icon color="#667eea" name="play-right" size="14"></up-icon>
                  </view>
                  <view v-else-if="lesson.progress > 0" class="status-badge in-progress">
                    <up-icon color="#ff9500" name="clock" size="14"></up-icon>
                  </view>
                  <view v-else class="status-badge">
                    <up-icon color="#8e8e93" name="lock" size="14"></up-icon>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
        <template v-else>
          <view style="text-align:center;color:#8e8e93;padding:40px 0;">没有内容</view>
        </template>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-btn primary" @click="startLearning">
        <up-icon name="play-circle" color="#fff" size="20"></up-icon>
        <text class="btn-text" style="color: #fff1f0">{{ courseProgress > 0 ? '继续学习' : '开始学习' }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import {getCourseById} from '@/api/course'
import {getResourceById} from '@/api/resource'

export default {
  data() {
    return {
      tabList: [{name: '介绍'}, {name: '目录'}],
      currentTab: 1,
      openedChapters: [],
      isFavorite: false,
      course: {
        id: '',
        name: '',
        description: '',
        coverImageUrl: '',
        progress: 0,
        chapters: [],
        totalStudyDuration: 0
      },
      studyRecord: null
    }
  },
  computed: {
    courseProgress() {
      // 基于课时学习记录计算课程总体进度
      if (!this.course.chapters || this.course.chapters.length === 0) return 0;

      let totalLessons = 0;
      let totalProgress = 0;

      this.course.chapters.forEach(chapter => {
        chapter.lessons.forEach(lesson => {
          totalLessons++;
          totalProgress += lesson.progress || 0;
        });
      });

      return totalLessons > 0 ? Math.round(totalProgress / totalLessons) : 0;
    }
  },
  async onLoad(options) {
    if (!options.id) return;
    await this.loadCourseData(Number(options.id));
  },

  async onShow() {
    // 页面显示时刷新学习进度
    if (this.course.id) {
      await this.refreshStudyProgress();
    }
  },

  methods: {
    async loadCourseData(courseId) {
      try {
        const res = await getCourseById(courseId);

        // 保存学习记录
        this.studyRecord = res.currentUserStudyRecord;

        let chapters = [];
        let resourceIdMap = {};
        let totalStudyDuration = 0;

        if (res.structure) {
          try {
            const struct = JSON.parse(res.structure);
            chapters = struct.filter(item => item.type === 'chapter').map(chapter => ({
              id: chapter.id,
              title: chapter.label,
              lessons: (chapter.children || []).filter(l => l.type === 'lesson').map(lesson => {
                resourceIdMap[lesson.resourceId] = null;
                return {
                  id: lesson.id,
                  title: lesson.label,
                  resourceId: lesson.resourceId
                }
              }),
            }));

            // 批量获取资源详情
            const resourceIds = Object.keys(resourceIdMap);
            const resourceDetails = await Promise.all(resourceIds.map(id => getResourceById(id)));
            resourceDetails.forEach(resource => {
              if (resource && resource.id) resourceIdMap[resource.id] = resource;
            });

            // 合并资源信息和学习记录到lessons
            chapters.forEach(chapter => {
              chapter.lessons.forEach(lesson => {
                const resource = resourceIdMap[lesson.resourceId];
                if (resource) {
                  lesson.type = resource.type;
                  lesson.content = resource.content;
                  lesson.tags = resource.tags;
                }
                // 合并学习记录
                if (this.studyRecord && this.studyRecord.structure) {
                  const chapterRecord = this.studyRecord.structure.find(c => c.id === chapter.id);
                  if (chapterRecord) {
                    const lessonRecord = chapterRecord.children.find(l => l.id === lesson.id);
                    if (lessonRecord) {
                      lesson.progress = lessonRecord.progress || 0;
                      lesson.completed = lessonRecord.progress === 100;
                      lesson.current = lessonRecord.progress > 0 && lessonRecord.progress < 100;
                      lesson.studyDuration = lessonRecord.duration || 0;
                      lesson.lastPosition = lessonRecord.lastPosition || 0; // 添加lastPosition
                      totalStudyDuration += lesson.studyDuration || 0;
                    }
                  }
                }
              });
            });

            this.course = {
              ...res,
              chapters,
              progress: this.courseProgress,
              totalStudyDuration
            };
            this.openedChapters = [this.course.chapters[0].id]
          } catch (e) {
            console.error('解析课程结构失败:', e);
            chapters = [];
          }
        }
      } catch (e) {
        console.error('课程加载失败:', e);
        uni.showToast({title: '课程加载失败', icon: 'none'});
      }
    },

    async refreshCourseData() {
      await this.loadCourseData(this.course.id);
      uni.showToast({title: '课程已刷新', icon: 'success'});
    },

    async refreshStudyProgress() {
      try {
        // 重新获取课程详情和学习进度
        const res = await getCourseById(this.course.id);
        this.studyRecord = res.currentUserStudyRecord;

        // 更新学习记录到章节数据
        if (this.studyRecord && this.studyRecord.structure) {
          this.course.chapters.forEach(chapter => {
            const chapterRecord = this.studyRecord.structure.find(c => c.id === chapter.id);
            if (chapterRecord) {
              chapter.lessons.forEach(lesson => {
                const lessonRecord = chapterRecord.children.find(l => l.id === lesson.id);
                if (lessonRecord) {
                  lesson.progress = lessonRecord.progress || 0;
                  lesson.completed = lessonRecord.progress === 100;
                  lesson.current = lessonRecord.progress > 0 && lessonRecord.progress < 100;
                  lesson.studyDuration = lessonRecord.duration || 0;
                  lesson.lastPosition = lessonRecord.lastPosition || 0;
                }
              });
            }
          });

          // 重新计算总学习时长
          let totalStudyDuration = 0;
          this.course.chapters.forEach(chapter => {
            chapter.lessons.forEach(lesson => {
              totalStudyDuration += lesson.studyDuration || 0;
            });
          });
          this.course.totalStudyDuration = totalStudyDuration;
        }
      } catch (e) {
        console.error('刷新学习进度失败:', e);
      }
    },

    goBack() {
      uni.navigateBack();
    },

    switchTab(index) {
      this.currentTab = index;
    },

    shareCourse() {
      uni.showActionSheet({
        itemList: ['分享给朋友',],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              uni.showToast({title: '分享给朋友', icon: 'success'});
              break;
          }
        }
      });
    },

    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      uni.showToast({
        title: this.isFavorite ? '已收藏' : '已取消收藏',
        icon: 'success'
      });
    },

    toggleChapter(chapterId) {
      const index = this.openedChapters.indexOf(chapterId);
      if (index > -1) {
        this.openedChapters.splice(index, 1);
      } else {
        this.openedChapters.push(chapterId);
      }
    },

    isChapterOpen(chapterId) {
      return this.openedChapters.includes(chapterId);
    },

    getLessonIcon(type) {
      switch (type) {
        case 'video':
          return 'play-circle-fill';
        case 'article':
          return 'file-text-fill';
        case 'file':
          return 'file-text';
        case 'quiz':
          return 'help-circle-fill';
        default:
          return 'arrow-right';
      }
    },

    playLesson(lesson) {
      // 构建包含进度信息的lesson数据
      const lessonData = {
        ...lesson,
        progress: lesson.progress || 0,
        lastPosition: lesson.lastPosition || 0
      };

      const lessonDataStr = encodeURIComponent(JSON.stringify(lessonData));

      if (lesson.type === 'video') {
        uni.navigateTo({url: `/pages/study/video-player?courseId=${this.course.id}&lesson=${lessonDataStr}`});
      } else if (lesson.type === 'article') {
        uni.navigateTo({url: `/pages/study/document-reader?courseId=${this.course.id}&lesson=${lessonDataStr}`});
      } else {
        uni.showToast({title: '暂不支持该类型课时', icon: 'none'});
      }
    },

    startLearning() {
      for (let chapter of this.course.chapters) {
        for (let lesson of chapter.lessons) {
          if (lesson.progress < 100) {
            this.playLesson(lesson);
            return;
          }
        }
      }
      uni.showToast({title: '课程已全部完成', icon: 'success'});
    },

    getTotalLessons() {
      return this.course.chapters.reduce((total, chapter) => total + chapter.lessons.length, 0);
    },

    getCompletedLessons() {
      let completed = 0;
      this.course.chapters.forEach(chapter => {
        chapter.lessons.forEach(lesson => {
          if (lesson.progress === 100) completed++;
        });
      });
      return completed;
    },

    getChapterProgress(chapter) {
      if (!chapter.lessons || chapter.lessons.length === 0) return 0;

      let totalProgress = 0;
      chapter.lessons.forEach(lesson => {
        totalProgress += lesson.progress || 0;
      });

      return Math.round(totalProgress / chapter.lessons.length);
    },

    formatDuration(seconds) {
      if (!seconds) return '0秒';
      if (seconds < 60) {
        return `${seconds}秒`;
      } else if (seconds < 3600) {
        return `${Math.floor(seconds / 60)}分钟`;
      } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小时${minutes}分钟`;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/study/course-detail.scss";
</style>
