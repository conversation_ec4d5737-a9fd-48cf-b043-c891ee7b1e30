import { 
  generateId, 
  randomDate, 
  randomItem, 
  randomInt, 
  mockResponse,
  paginateData,
  getQueryParams
} from './utils'
import type { 
  PointsRecord, 
  PointsStatistics, 
  PointsAdjustment,
  PointsRule,
  PointsRuleStatistics
} from '@/api/points'
import { users } from './user'

// 积分变动类型
const pointsTypes = ['course_complete', 'sign_in', 'exchange', 'admin_adjust', 'other']

// 相关类型
const relatedTypes = ['course', 'product', 'other']

// 操作员
const operators = ['系统', '管理员', '教师']

// 生成模拟积分记录
function generatePointsRecords(count: number = 200): PointsRecord[] {
  const records: PointsRecord[] = []
  
  for (let i = 0; i < count; i++) {
    const userId = randomItem(users).id
    const userName = randomItem(users).name
    const type = randomItem(pointsTypes) as 'course_complete' | 'sign_in' | 'exchange' | 'admin_adjust' | 'other'
    
    // 根据类型决定积分变动值
    let points: number
    switch (type) {
      case 'course_complete':
        points = randomInt(5, 20)
        break
      case 'sign_in':
        points = randomInt(1, 5)
        break
      case 'exchange':
        points = -randomInt(10, 100)
        break
      case 'admin_adjust':
        points = Math.random() > 0.5 ? randomInt(5, 50) : -randomInt(5, 50)
        break
      default:
        points = Math.random() > 0.5 ? randomInt(1, 10) : -randomInt(1, 10)
    }
    
    const record: PointsRecord = {
      id: generateId(),
      userId,
      userName,
      points,
      balance: 0, // 后面计算
      type,
      description: getDescriptionByType(type, points),
      relatedId: type !== 'admin_adjust' && type !== 'sign_in' ? generateId() : undefined,
      relatedType: type !== 'admin_adjust' && type !== 'sign_in' ? randomItem(relatedTypes) as 'course' | 'product' | 'other' : undefined,
      operator: type === 'admin_adjust' ? randomItem(operators) : undefined,
      createdAt: randomDate()
    }
    
    records.push(record)
  }
  
  // 按日期排序
  records.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
  
  // 计算每条记录的积分余额
  const userBalances = new Map<string, number>()
  
  records.forEach(record => {
    const currentBalance = userBalances.get(record.userId) || 0
    const newBalance = currentBalance + record.points
    userBalances.set(record.userId, newBalance)
    record.balance = newBalance
  })
  
  return records
}

// 根据类型生成描述
function getDescriptionByType(type: string, points: number): string {
  switch (type) {
    case 'course_complete':
      return `完成课程学习，获得${points}积分奖励`
    case 'sign_in':
      return `每日签到，获得${points}积分奖励`
    case 'exchange':
      return `兑换商品，消耗${Math.abs(points)}积分`
    case 'admin_adjust':
      return points > 0 
        ? `管理员调整，增加${points}积分` 
        : `管理员调整，减少${Math.abs(points)}积分`
    default:
      return points > 0 
        ? `其他操作，增加${points}积分` 
        : `其他操作，减少${Math.abs(points)}积分`
  }
}

// 生成积分统计数据
function generatePointsStatistics(records: PointsRecord[]): PointsStatistics {
  // 用户总数
  const uniqueUsers = new Set(records.map(record => record.userId))
  const totalUsers = uniqueUsers.size
  
  // 积分总数
  const userBalances = new Map<string, number>()
  records.forEach(record => {
    userBalances.set(record.userId, record.balance)
  })
  
  const totalPoints = Array.from(userBalances.values()).reduce((sum, balance) => sum + balance, 0)
  
  // 平均积分
  const averagePoints = Math.round(totalPoints / totalUsers)
  
  // 按月统计积分变动
  const monthlyData = new Map<string, { earned: number; consumed: number }>()
  
  records.forEach(record => {
    const date = new Date(record.createdAt)
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    
    const monthData = monthlyData.get(monthKey) || { earned: 0, consumed: 0 }
    
    if (record.points > 0) {
      monthData.earned += record.points
    } else {
      monthData.consumed += Math.abs(record.points)
    }
    
    monthlyData.set(monthKey, monthData)
  })
  
  const monthlyPointsChange = Array.from(monthlyData.entries())
    .map(([month, data]) => ({
      month,
      earned: data.earned,
      consumed: data.consumed
    }))
    .sort((a, b) => a.month.localeCompare(b.month))
  
  // 积分分布
  const ranges = ['0-100', '101-500', '501-1000', '1001-5000', '5000+']
  const pointsDistribution = ranges.map(range => {
    let count = 0
    
    switch (range) {
      case '0-100':
        count = Array.from(userBalances.values()).filter(balance => balance >= 0 && balance <= 100).length
        break
      case '101-500':
        count = Array.from(userBalances.values()).filter(balance => balance > 100 && balance <= 500).length
        break
      case '501-1000':
        count = Array.from(userBalances.values()).filter(balance => balance > 500 && balance <= 1000).length
        break
      case '1001-5000':
        count = Array.from(userBalances.values()).filter(balance => balance > 1000 && balance <= 5000).length
        break
      case '5000+':
        count = Array.from(userBalances.values()).filter(balance => balance > 5000).length
        break
    }
    
    return { range, count }
  })
  
  // 积分排行
  const topUsers = Array.from(userBalances.entries())
    .map(([userId, points]) => {
      const user = records.find(record => record.userId === userId)
      return {
        userId,
        userName: user ? user.userName : '',
        points
      }
    })
    .sort((a, b) => b.points - a.points)
    .slice(0, 10)
  
  return {
    totalUsers,
    totalPoints,
    averagePoints,
    monthlyPointsChange,
    pointsDistribution,
    topUsers
  }
}

// 积分记录数据集
export const pointsRecords = generatePointsRecords(200)

// 模拟获取积分记录列表接口
export function mockGetPointsRecordList(url: string) {
  const params = getQueryParams(url)
  let filteredRecords = [...pointsRecords]
  
  // 用户ID筛选
  if (params.userId) {
    filteredRecords = filteredRecords.filter(record => record.userId === params.userId)
  }
  
  // 用户名筛选
  if (params.userName) {
    const userName = params.userName.toLowerCase()
    filteredRecords = filteredRecords.filter(record => record.userName.toLowerCase().includes(userName))
  }
  
  // 类型筛选
  if (params.type) {
    filteredRecords = filteredRecords.filter(record => record.type === params.type)
  }
  
  // 日期筛选
  if (params.startDate) {
    const startDate = new Date(params.startDate)
    filteredRecords = filteredRecords.filter(record => new Date(record.createdAt) >= startDate)
  }
  
  if (params.endDate) {
    const endDate = new Date(params.endDate)
    filteredRecords = filteredRecords.filter(record => new Date(record.createdAt) <= endDate)
  }
  
  // 排序
  if (params.sortBy) {
    const sortBy = params.sortBy as keyof PointsRecord
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1
    
    filteredRecords.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]
      if (aValue == null || bValue == null) return 0
      if (aValue < bValue) return -1 * sortOrder
      if (aValue > bValue) return 1 * sortOrder
      return 0
    })
  } else {
    // 默认按创建时间倒序
    filteredRecords.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }
  
  // 分页
  const page = parseInt(params.page || '1')
  const limit = parseInt(params.limit || '10')
  const paginatedData = paginateData(filteredRecords, page, limit)
  
  return mockResponse(paginatedData)
}

// 模拟获取用户积分余额接口
export function mockGetUserPointsBalance(url: string) {
  const userId = url.split('/').pop() || ''
  const latestRecord = [...pointsRecords]
    .filter(record => record.userId === userId)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]
  
  if (!latestRecord) {
    return mockResponse({ userId, balance: 0 })
  }
  
  return mockResponse({ userId, balance: latestRecord.balance })
}

// 模拟积分调整接口
export function mockAdjustUserPoints(data: PointsAdjustment) {
  const { userId, points, description, operator } = data
  
  // 查找用户最新积分余额
  const latestRecord = [...pointsRecords]
    .filter(record => record.userId === userId)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]
  
  const currentBalance = latestRecord ? latestRecord.balance : 0
  const newBalance = currentBalance + points
  
  // 创建新记录
  const newRecord: PointsRecord = {
    id: generateId(),
    userId,
    userName: latestRecord?.userName || '未知用户',
    points,
    balance: newBalance,
    type: 'admin_adjust',
    description: description || (points > 0 ? `管理员调整，增加${points}积分` : `管理员调整，减少${Math.abs(points)}积分`),
    operator,
    createdAt: new Date().toISOString()
  }
  
  pointsRecords.push(newRecord)
  
  return mockResponse({ success: true, newBalance })
}

// 模拟批量调整积分接口
export function mockBatchAdjustPoints(data: PointsAdjustment[]) {
  let failedCount = 0
  
  data.forEach(adjustment => {
    try {
      mockAdjustUserPoints(adjustment)
    } catch (error) {
      failedCount++
    }
  })
  
  return mockResponse({ success: true, failedCount })
}

// 模拟获取积分统计数据接口
export function mockGetPointsStatistics() {
  return mockResponse(generatePointsStatistics(pointsRecords))
}

// 模拟获取用户积分趋势接口
export function mockGetUserPointsTrend(url: string, params: { period: string }) {
  const userId = url.split('/').pop() || ''
  const period = params.period || 'month'
  
  // 根据周期筛选数据
  const now = new Date()
  let startDate: Date
  
  switch (period) {
    case 'week':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7)
      break
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
      break
    case 'year':
      startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
      break
    default: // all
      startDate = new Date(2020, 0, 1)
  }
  
  // 获取用户记录
  const userRecords = pointsRecords
    .filter(record => record.userId === userId && new Date(record.createdAt) >= startDate)
    .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
  
  // 生成趋势数据
  const data = userRecords.map(record => ({
    date: record.createdAt.split('T')[0],
    points: record.points,
    balance: record.balance
  }))
  
  return mockResponse({
    userId,
    userName: userRecords[0]?.userName || '未知用户',
    data
  })
}

// 模拟导出积分记录接口
export function mockExportPointsRecords() {
  // 实际应用中应返回文件流，这里简化处理
  return mockResponse(new Blob())
}

/**
 * ========== 积分规则相关模拟接口 ==========
 */

// 生成积分规则模拟数据
function generatePointsRules(): PointsRule[] {
  const rules: PointsRule[] = [
    {
      id: '1',
      category: 'login',
      name: '每日首次登录',
      code: 'daily_login',
      description: '每日首次登录系统奖励积分',
      points: 5,
      dailyLimit: 5,
      timesLimit: 1,
      usedCount: 1250,
      status: 1,
      createTime: '2023-01-01 10:00:00'
    },
    {
      id: '2',
      category: 'learning',
      name: '完成课程学习',
      code: 'complete_course',
      description: '完成一门课程的学习',
      points: 10,
      dailyLimit: 30,
      timesLimit: 3,
      usedCount: 865,
      status: 1,
      createTime: '2023-01-02 10:00:00'
    },
    {
      id: '3',
      category: 'learning',
      name: '观看视频',
      code: 'watch_video',
      description: '观看学习视频',
      points: 2,
      dailyLimit: 10,
      timesLimit: 5,
      usedCount: 2145,
      status: 1,
      createTime: '2023-01-02 11:00:00'
    },
    {
      id: '4',
      category: 'forum',
      name: '发表帖子',
      code: 'post_topic',
      description: '在论坛发表帖子',
      points: 3,
      dailyLimit: 9,
      timesLimit: 3,
      usedCount: 756,
      status: 1,
      createTime: '2023-01-03 09:00:00'
    },
    {
      id: '5',
      category: 'forum',
      name: '回复帖子',
      code: 'reply_topic',
      description: '回复论坛帖子',
      points: 1,
      dailyLimit: 10,
      timesLimit: 10,
      usedCount: 1985,
      status: 1,
      createTime: '2023-01-03 09:30:00'
    },
    {
      id: '6',
      category: 'exam',
      name: '考试通过',
      code: 'exam_pass',
      description: '考试成绩达到80分以上',
      points: 15,
      dailyLimit: -1,
      timesLimit: -1,
      usedCount: 428,
      status: 1,
      createTime: '2023-01-04 14:00:00'
    },
    {
      id: '7',
      category: 'exam',
      name: '考试优秀',
      code: 'exam_excellent',
      description: '考试成绩达到95分以上',
      points: 25,
      dailyLimit: -1,
      timesLimit: -1,
      usedCount: 156,
      status: 1,
      createTime: '2023-01-04 14:30:00'
    },
    {
      id: '8',
      category: 'other',
      name: '违规扣分',
      code: 'violation_penalty',
      description: '违反平台规定扣除积分',
      points: -10,
      dailyLimit: -1,
      timesLimit: -1,
      usedCount: 23,
      status: 1,
      createTime: '2023-01-05 16:00:00'
    },
    {
      id: '9',
      category: 'learning',
      name: '学习时长奖励',
      code: 'study_duration',
      description: '每学习30分钟奖励积分',
      points: 1,
      dailyLimit: 20,
      timesLimit: -1,
      usedCount: 3450,
      status: 1,
      createTime: '2023-01-06 08:00:00'
    },
    {
      id: '10',
      category: 'other',
      name: '签到奖励',
      code: 'daily_checkin',
      description: '每日签到奖励',
      points: 2,
      dailyLimit: 2,
      timesLimit: 1,
      usedCount: 890,
      status: 0,
      createTime: '2023-01-07 10:00:00'
    }
  ]

  return rules
}

// 生成积分规则统计数据
function generatePointsRuleStatistics(rules: PointsRule[]): PointsRuleStatistics {
  const enabledRules = rules.filter(rule => rule.status === 1)
  const totalUsed = rules.reduce((sum, rule) => sum + rule.usedCount, 0)
  const positivePoints = rules.filter(rule => rule.points > 0).reduce((sum, rule) => sum + rule.points, 0)
  const negativePoints = rules.filter(rule => rule.points < 0).reduce((sum, rule) => sum + Math.abs(rule.points), 0)

  // 分类分布
  const categoryDistribution = [
    { category: 'login', count: rules.filter(rule => rule.category === 'login').length },
    { category: 'learning', count: rules.filter(rule => rule.category === 'learning').length },
    { category: 'forum', count: rules.filter(rule => rule.category === 'forum').length },
    { category: 'exam', count: rules.filter(rule => rule.category === 'exam').length },
    { category: 'other', count: rules.filter(rule => rule.category === 'other').length }
  ].filter(item => item.count > 0)

  // 使用频率排行
  const usageRanking = rules
    .sort((a, b) => b.usedCount - a.usedCount)
    .slice(0, 10)
    .map(rule => ({
      id: rule.id,
      name: rule.name,
      category: rule.category,
      usedCount: rule.usedCount,
      points: rule.points
    }))

  return {
    totalRules: rules.length,
    enabledRules: enabledRules.length,
    totalUsed,
    positivePoints,
    negativePoints,
    categoryDistribution,
    usageRanking
  }
}

// 积分规则数据集
export const pointsRules = generatePointsRules()

// 模拟获取积分规则列表接口
export function mockGetPointsRuleList(url: string) {
  const params = getQueryParams(url)
  let filteredRules = [...pointsRules]

  // 分类筛选
  if (params.category) {
    filteredRules = filteredRules.filter(rule => rule.category === params.category)
  }

  // 状态筛选
  if (params.status !== undefined && params.status !== '') {
    const status = parseInt(params.status)
    filteredRules = filteredRules.filter(rule => rule.status === status)
  }

  // 关键词筛选
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredRules = filteredRules.filter(rule => 
      rule.name.toLowerCase().includes(keyword) || 
      rule.description.toLowerCase().includes(keyword) ||
      rule.code.toLowerCase().includes(keyword)
    )
  }

  // 排序
  if (params.sortBy) {
    const sortBy = params.sortBy as keyof PointsRule
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1
    
    filteredRules.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]
      if (aValue == null || bValue == null) return 0
      if (aValue < bValue) return -1 * sortOrder
      if (aValue > bValue) return 1 * sortOrder
      return 0
    })
  } else {
    // 默认按创建时间倒序
    filteredRules.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
  }

  // 分页
  const page = parseInt(params.page || '1')
  const limit = parseInt(params.limit || '10')
  const paginatedData = paginateData(filteredRules, page, limit)

  return mockResponse(paginatedData)
}

// 模拟获取积分规则详情接口
export function mockGetPointsRuleDetail(url: string) {
  const ruleId = url.split('/').pop() || ''
  const rule = pointsRules.find(r => r.id === ruleId)
  
  if (!rule) {
    return mockResponse(null, 404, '规则不存在')
  }
  
  return mockResponse(rule)
}

// 模拟创建积分规则接口
export function mockCreatePointsRule(data: any) {
  const newRule: PointsRule = {
    id: generateId(),
    ...data,
    usedCount: 0,
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  }
  
  pointsRules.push(newRule)
  
  return mockResponse({ id: newRule.id, success: true })
}

// 模拟更新积分规则接口
export function mockUpdatePointsRule(url: string, data: any) {
  const ruleId = url.split('/').pop() || ''
  const ruleIndex = pointsRules.findIndex(r => r.id === ruleId)
  
  if (ruleIndex === -1) {
    return mockResponse(null, 404, '规则不存在')
  }
  
  pointsRules[ruleIndex] = {
    ...pointsRules[ruleIndex],
    ...data,
    updateTime: new Date().toISOString()
  }
  
  return mockResponse({ success: true })
}

// 模拟删除积分规则接口
export function mockDeletePointsRule(url: string) {
  const ruleId = url.split('/').pop() || ''
  const ruleIndex = pointsRules.findIndex(r => r.id === ruleId)
  
  if (ruleIndex === -1) {
    return mockResponse(null, 404, '规则不存在')
  }
  
  pointsRules.splice(ruleIndex, 1)
  
  return mockResponse({ success: true })
}

// 模拟批量删除积分规则接口
export function mockBatchDeletePointsRules(data: { ruleIds: string[] }) {
  let failedCount = 0
  
  data.ruleIds.forEach(ruleId => {
    const ruleIndex = pointsRules.findIndex(r => r.id === ruleId)
    if (ruleIndex !== -1) {
      pointsRules.splice(ruleIndex, 1)
    } else {
      failedCount++
    }
  })
  
  return mockResponse({ success: true, failedCount })
}

// 模拟切换积分规则状态接口
export function mockTogglePointsRuleStatus(url: string, data: { status: 0 | 1 }) {
  const urlParts = url.split('/')
  const ruleId = urlParts[urlParts.length - 2] // 从 /points/rules/{id}/status 中提取ID
  const ruleIndex = pointsRules.findIndex(r => r.id === ruleId)
  
  if (ruleIndex === -1) {
    return mockResponse(null, 404, '规则不存在')
  }
  
  pointsRules[ruleIndex].status = data.status
  pointsRules[ruleIndex].updateTime = new Date().toISOString()
  
  return mockResponse({ success: true })
}

// 模拟获取积分规则统计数据接口
export function mockGetPointsRuleStatistics() {
  return mockResponse(generatePointsRuleStatistics(pointsRules))
}

// 模拟导出积分规则接口
export function mockExportPointsRules() {
  // 实际应用中应返回文件流，这里简化处理
  return mockResponse(new Blob())
} 