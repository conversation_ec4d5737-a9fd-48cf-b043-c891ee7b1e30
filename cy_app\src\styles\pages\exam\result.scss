.exam-result {
  background: var(--bg-secondary);
  min-height: 100vh;
}

.score-section {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  padding: var(--spacing-xl) var(--spacing-md);
  color: #FFFFFF;
}

.score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.score-circle {
  position: relative;
  width: 240rpx;
  height: 240rpx;
}

.score-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.score-number {
  display: block;
  font-size: 120rpx;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 1;
}

.score-unit {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
}

.score-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.score-progress {
  width: 240rpx;
  height: 240rpx;
  border-radius: 50%;
  border: 12rpx solid rgba(255, 255, 255, 0.2);
  border-top-color: #FFFFFF;
  transform: rotate(-90deg);
  transition: stroke-dashoffset 1s ease;
}

.score-info {
  text-align: center;
}

.score-status {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);

  &.passed {
    color: #FFFFFF;
  }

  &.failed {
    color: #FFD700;
  }
}

.score-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
}

.stat-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.stat-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: var(--spacing-sm);
}

.stat-value {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.analysis-section,
.knowledge-section,
.mistakes-section,
.suggestions-section {
  padding: var(--spacing-md);
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.section-header {
  margin-bottom: var(--spacing-md);
}

.mistake-count {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.type-item,
.knowledge-item {
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
}

.type-header,
.knowledge-header {
  margin-bottom: var(--spacing-sm);
}

.type-name,
.knowledge-name {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.type-score {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-color);
}

.knowledge-level {
  padding: 6rpx 12rpx;
  border-radius: var(--radius-sm);
  font-size: 24rpx;

  &.excellent {
    background: rgba(52, 199, 89, 0.1);
    color: var(--success-color);
  }

  &.good {
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-color);
  }

  &.average {
    background: rgba(255, 149, 0, 0.1);
    color: var(--warning-color);
  }

  &.poor {
    background: rgba(255, 59, 48, 0.1);
    color: var(--error-color);
  }
}

.type-progress,
.knowledge-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 6rpx;
  transition: width 0.6s ease;

  &.excellent {
    background: var(--success-color);
  }

  &.good {
    background: var(--primary-color);
  }

  &.average {
    background: var(--warning-color);
  }

  &.poor {
    background: var(--error-color);
  }
}

.progress-percent {
  font-size: 24rpx;
  font-weight: 600;
  color: var(--primary-color);
  min-width: 60rpx;
  text-align: right;
}

.type-details,
.detail-text {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.mistake-item {
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  border-left: 6rpx solid var(--error-color);
}

.mistake-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.mistake-number {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-color);
}

.mistake-type {
  padding: 4rpx 8rpx;
  background: rgba(0, 122, 255, 0.1);
  color: var(--primary-color);
  font-size: 20rpx;
  border-radius: var(--radius-sm);
}

.mistake-question {
  font-size: 30rpx;
  line-height: 1.5;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.mistake-answers {
  margin-bottom: var(--spacing-sm);
}

.answer-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.answer-label {
  font-size: 26rpx;
  color: var(--text-secondary);
  min-width: 120rpx;
}

.answer-value {
  flex: 1;
  font-size: 26rpx;

  &.wrong {
    color: var(--error-color);
  }

  &.correct {
    color: var(--success-color);
  }
}

.mistake-explanation {
  padding: var(--spacing-sm);
  background: rgba(0, 122, 255, 0.05);
  border-radius: var(--radius-sm);
}

.explanation-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.explanation-text {
  font-size: 26rpx;
  line-height: 1.5;
  color: var(--text-primary);
}

.no-mistakes {
  text-align: center;
  padding: var(--spacing-xl) 0;
}

.no-mistakes-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: var(--spacing-md);
}

.no-mistakes-text {
  font-size: 32rpx;
  color: var(--text-secondary);
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
}

.suggestion-icon {
  font-size: 32rpx;
  margin-top: 4rpx;
}

.suggestion-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: var(--text-primary);
}

.result-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}
