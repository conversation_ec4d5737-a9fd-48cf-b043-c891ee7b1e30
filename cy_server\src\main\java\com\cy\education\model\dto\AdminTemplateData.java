package com.cy.education.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 管理员导入模板数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminTemplateData {

    @ExcelProperty("姓名*")
    private String realName;

    @ExcelProperty("用户名*")
    private String username;

    @ExcelProperty("密码*")
    private String password;

    @ExcelProperty("手机号*")
    private String phone;

    @ExcelProperty("邮箱")
    private String email;

    @ExcelProperty("部门名称")
    private String departmentName;

    @Override
    public String toString() {
        return String.format("姓名:%s, 用户名:%s, 手机号:%s", realName, username, phone);
    }
}
