import { 
  generateId, 
  randomDate, 
  randomItem, 
  randomItems,
  randomInt, 
  mockResponse,
  paginateData,
  getQueryParams
} from './utils'
import type { 
  Product,
  ProductCategory,
  ProductStatistics
} from '@/api/product'

// 商品分类
const categoryNames = [
  '学习用品', '安全装备', '纪念品', '电子产品', '生活用品', '书籍', '其他'
]

// 商品标签
const productTags = [
  '热门', '推荐', '限量', '新品', '特价', '精选', '实用', '高档', '礼品'
]

// 商品状态
const productStatus = ['on_shelf', 'off_shelf', 'out_of_stock']

// 生成模拟商品分类
function generateProductCategories(): ProductCategory[] {
  return categoryNames.map((name, index) => ({
    id: generateId(),
    name,
    description: `${name}类商品，包含各种${name}相关物品`,
    count: 0 // 后面计算
  }))
}

// 生成模拟商品数据
function generateProducts(count: number = 50, categories: ProductCategory[]): Product[] {
  const products: Product[] = []
  
  for (let i = 0; i < count; i++) {
    const category = randomItem(categories)
    const points = randomInt(2, 20) * 100
    const stock = Math.random() > 0.1 ? randomInt(5, 100) : 0
    const status: 'on_shelf' | 'off_shelf' | 'out_of_stock' = 
      stock === 0 ? 'out_of_stock' : (Math.random() > 0.2 ? 'on_shelf' : 'off_shelf')
    
    const product: Product = {
      id: generateId(),
      name: `${category.name} ${i + 1}`,
      description: `这是一个${category.name}商品，可以使用${points}积分兑换。`,
      images: [
        `https://picsum.photos/300/300?random=${i}`,
        `https://picsum.photos/300/300?random=${i + 100}`
      ],
      points,
      category: category.name,
      stock,
      limitPerUser: Math.random() > 0.5 ? randomInt(1, 5) : 0,
      exchangeCount: randomInt(0, 200),
      status,
      tags: randomItems(productTags, randomInt(1, 4)),
      createdAt: randomDate(new Date(2022, 0, 1), new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 0, 1))
    }
    
    // 添加促销时间
    if (Math.random() > 0.7) {
      const now = new Date()
      product.startTime = randomDate(new Date(now.getFullYear(), now.getMonth() - 1, 1))
      product.endTime = randomDate(new Date(now.getFullYear(), now.getMonth() + 2, 1))
    }
    
    products.push(product)
  }
  
  return products
}

// 更新分类商品数量
function updateCategoryCounts(products: Product[], categories: ProductCategory[]): void {
  // 重置计数
  categories.forEach(category => {
    category.count = 0
  })
  
  // 统计每个分类的商品数量
  products.forEach(product => {
    const category = categories.find(c => c.name === product.category)
    if (category) {
      category.count += 1
    }
  })
}

// 生成商品统计数据
function generateProductStatistics(products: Product[]): ProductStatistics {
  const totalProducts = products.length
  const activeProducts = products.filter(product => product.status === 'on_shelf').length
  const totalExchanges = products.reduce((total, product) => total + product.exchangeCount, 0)
  const outOfStockCount = products.filter(product => product.status === 'out_of_stock').length
  
  // 分类分布
  const categoryMap = new Map<string, number>()
  products.forEach(product => {
    const count = categoryMap.get(product.category) || 0
    categoryMap.set(product.category, count + 1)
  })
  
  const categoryDistribution = Array.from(categoryMap.entries()).map(([category, count]) => ({
    category,
    count
  }))
  
  // 热门商品
  const popularProducts = [...products]
    .sort((a, b) => b.exchangeCount - a.exchangeCount)
    .slice(0, 5)
    .map(product => ({
      id: product.id,
      name: product.name,
      exchangeCount: product.exchangeCount
    }))
  
  return {
    totalProducts,
    activeProducts,
    totalExchanges,
    outOfStockCount,
    categoryDistribution,
    popularProducts
  }
}

// 商品数据集
export const productCategories = generateProductCategories()
export const products = generateProducts(50, productCategories)

// 更新分类计数
updateCategoryCounts(products, productCategories)

// 模拟获取商品列表接口
export function mockGetProductList(url: string) {
  const params = getQueryParams(url)
  let filteredProducts = [...products]
  
  // 关键字搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredProducts = filteredProducts.filter(
      product => product.name.toLowerCase().includes(keyword) ||
        product.description.toLowerCase().includes(keyword)
    )
  }
  
  // 分类筛选
  if (params.category) {
    filteredProducts = filteredProducts.filter(product => product.category === params.category)
  }
  
  // 状态筛选
  if (params.status) {
    filteredProducts = filteredProducts.filter(product => product.status === params.status)
  }
  
  // 积分范围筛选
  if (params.minPoints) {
    const minPoints = parseInt(params.minPoints)
    filteredProducts = filteredProducts.filter(product => product.points >= minPoints)
  }
  
  if (params.maxPoints) {
    const maxPoints = parseInt(params.maxPoints)
    filteredProducts = filteredProducts.filter(product => product.points <= maxPoints)
  }
  
  // 排序
  if (params.sortBy) {
    const sortBy = params.sortBy as keyof Product
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1
    
    filteredProducts.sort((a, b) => {
      if (a[sortBy] < b[sortBy]) return -1 * sortOrder
      if (a[sortBy] > b[sortBy]) return 1 * sortOrder
      return 0
    })
  }
  
  // 分页
  const page = parseInt(params.page || '1')
  const limit = parseInt(params.limit || '10')
  const paginatedData = paginateData(filteredProducts, page, limit)
  
  return mockResponse(paginatedData)
}

// 模拟获取商品详情接口
export function mockGetProductDetail(url: string) {
  const productId = url.split('/').pop() || ''
  const product = products.find(p => p.id === productId)
  
  if (!product) {
    return mockResponse(null, 404, '商品不存在')
  }
  
  return mockResponse(product)
}

// 模拟创建商品接口
export function mockCreateProduct(data: Partial<Product>) {
  const newProduct: Product = {
    id: generateId(),
    name: data.name || '新商品',
    description: data.description || '商品描述',
    images: data.images || ['https://picsum.photos/300/300'],
    points: data.points || 100,
    category: data.category || productCategories[0].name,
    stock: data.stock || 10,
    limitPerUser: data.limitPerUser || 0,
    exchangeCount: 0,
    status: data.status || 'off_shelf',
    tags: data.tags || [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  // 设置可选字段
  if (data.startTime) newProduct.startTime = data.startTime
  if (data.endTime) newProduct.endTime = data.endTime
  
  products.push(newProduct)
  
  // 更新分类计数
  updateCategoryCounts(products, productCategories)
  
  return mockResponse({ id: newProduct.id })
}

// 模拟更新商品信息接口
export function mockUpdateProduct(url: string, data: Partial<Product>) {
  const productId = url.split('/').pop() || ''
  const productIndex = products.findIndex(p => p.id === productId)
  
  if (productIndex === -1) {
    return mockResponse(null, 404, '商品不存在')
  }
  
  // 更新商品
  products[productIndex] = {
    ...products[productIndex],
    ...data,
    updatedAt: new Date().toISOString()
  }
  
  // 如果分类发生变化，需要更新分类计数
  if (data.category) {
    updateCategoryCounts(products, productCategories)
  }
  
  return mockResponse({ success: true })
}

// 模拟删除商品接口
export function mockDeleteProduct(url: string) {
  const productId = url.split('/').pop() || ''
  const productIndex = products.findIndex(p => p.id === productId)
  
  if (productIndex === -1) {
    return mockResponse(null, 404, '商品不存在')
  }
  
  // 删除商品
  products.splice(productIndex, 1)
  
  // 更新分类计数
  updateCategoryCounts(products, productCategories)
  
  return mockResponse({ success: true })
}

// 模拟更改商品状态接口
export function mockChangeProductStatus(url: string, data: { status: 'on_shelf' | 'off_shelf' }) {
  const productId = url.split('/').pop() || ''
  const productIndex = products.findIndex(p => p.id === productId)
  
  if (productIndex === -1) {
    return mockResponse(null, 404, '商品不存在')
  }
  
  // 更新状态
  products[productIndex].status = data.status
  products[productIndex].updatedAt = new Date().toISOString()
  
  return mockResponse({ success: true })
}

// 模拟更新商品库存接口
export function mockUpdateProductStock(url: string, data: { stock: number }) {
  const productId = url.split('/').pop() || ''
  const productIndex = products.findIndex(p => p.id === productId)
  
  if (productIndex === -1) {
    return mockResponse(null, 404, '商品不存在')
  }
  
  // 更新库存
  products[productIndex].stock = data.stock
  products[productIndex].updatedAt = new Date().toISOString()
  
  // 根据库存自动更新状态
  if (data.stock === 0) {
    products[productIndex].status = 'out_of_stock'
  } else if (products[productIndex].status === 'out_of_stock') {
    products[productIndex].status = 'on_shelf'
  }
  
  return mockResponse({ success: true })
}

// 模拟获取商品分类列表接口
export function mockGetProductCategories() {
  return mockResponse(productCategories)
}

// 模拟创建商品分类接口
export function mockCreateProductCategory(data: { name: string; description?: string }) {
  // 检查分类名称是否已存在
  if (productCategories.some(category => category.name === data.name)) {
    return mockResponse(null, 400, '分类名称已存在')
  }
  
  const newCategory: ProductCategory = {
    id: generateId(),
    name: data.name,
    description: data.description || `${data.name}类商品`,
    count: 0
  }
  
  productCategories.push(newCategory)
  
  return mockResponse({ id: newCategory.id })
}

// 模拟更新商品分类接口
export function mockUpdateProductCategory(url: string, data: { name: string; description?: string }) {
  const categoryId = url.split('/').pop() || ''
  const categoryIndex = productCategories.findIndex(c => c.id === categoryId)
  
  if (categoryIndex === -1) {
    return mockResponse(null, 404, '分类不存在')
  }
  
  // 检查新名称是否与其他分类重名
  if (data.name !== productCategories[categoryIndex].name &&
      productCategories.some(category => category.name === data.name)) {
    return mockResponse(null, 400, '分类名称已存在')
  }
  
  const oldName = productCategories[categoryIndex].name
  
  // 更新分类
  productCategories[categoryIndex] = {
    ...productCategories[categoryIndex],
    name: data.name,
    description: data.description || productCategories[categoryIndex].description
  }
  
  // 如果分类名称发生变化，需要更新商品的分类字段
  if (oldName !== data.name) {
    products.forEach(product => {
      if (product.category === oldName) {
        product.category = data.name
      }
    })
  }
  
  return mockResponse({ success: true })
}

// 模拟删除商品分类接口
export function mockDeleteProductCategory(url: string) {
  const categoryId = url.split('/').pop() || ''
  const categoryIndex = productCategories.findIndex(c => c.id === categoryId)
  
  if (categoryIndex === -1) {
    return mockResponse(null, 404, '分类不存在')
  }
  
  const categoryName = productCategories[categoryIndex].name
  
  // 检查是否有商品使用此分类
  if (products.some(product => product.category === categoryName)) {
    return mockResponse(null, 400, '分类下有商品，无法删除')
  }
  
  // 删除分类
  productCategories.splice(categoryIndex, 1)
  
  return mockResponse({ success: true })
}

// 模拟批量更新商品状态接口
export function mockBatchUpdateProductStatus(data: { ids: string[]; status: 'on_shelf' | 'off_shelf' }) {
  const { ids, status } = data
  let failedCount = 0
  
  ids.forEach(id => {
    const productIndex = products.findIndex(p => p.id === id)
    if (productIndex === -1) {
      failedCount++
      return
    }
    
    // 更新状态
    products[productIndex].status = status
    products[productIndex].updatedAt = new Date().toISOString()
  })
  
  return mockResponse({ success: true, failedCount })
}

// 模拟获取商品统计数据接口
export function mockGetProductStatistics() {
  return mockResponse(generateProductStatistics(products))
}