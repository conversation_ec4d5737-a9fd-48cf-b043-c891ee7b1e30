package com.cy.education.model.params;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 设置管理员权限参数
 */
@Data
public class AdminPermissionParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 管理员ID
     */
    @NotNull(message = "管理员ID不能为空")
    private Integer id;

    /**
     * 权限ID列表
     */
    @NotNull(message = "权限列表不能为空")
    private List<Integer> permissions;
} 