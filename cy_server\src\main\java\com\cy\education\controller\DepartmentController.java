package com.cy.education.controller;

import com.cy.education.model.entity.Department;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.service.DepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 部门管理控制器
 */
@Api(tags = "部门管理接口")
@RestController
@RequestMapping("/department")
@PreAuthorize("hasAuthority('system:manage')")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 获取部门树形结构
     */
    @ApiOperation("获取部门树形结构")
    @GetMapping("/tree")
    public ApiResponse<List<Department>> getDepartmentTree() {
        try {
            List<Department> departments = departmentService.getDepartmentTree();
            return ApiResponse.success(departments);
        } catch (Exception e) {
            return ApiResponse.error("获取部门树失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门列表（扁平结构）
     */
    @ApiOperation("获取部门列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "父部门ID，不传则获取顶级部门", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "includeChildren", value = "是否包含子部门", dataType = "Boolean", paramType = "query", defaultValue = "false")
    })
    @GetMapping("/list")
    public ApiResponse<List<Department>> list(
            @RequestParam(required = false) Integer parentId,
            @RequestParam(defaultValue = "false") boolean includeChildren) {
        try {
            List<Department> departments = departmentService.listDepartments(parentId, includeChildren);
            return ApiResponse.success(departments);
        } catch (Exception e) {
            return ApiResponse.error("获取部门列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取部门详情
     */
    @ApiOperation("根据ID获取部门详情")
    @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "Integer", paramType = "path")
    @GetMapping("/{id}")
    public ApiResponse<Department> getById(@PathVariable Integer id) {
        try {
            Department department = departmentService.getDepartmentById(id);
            return ApiResponse.success(department);
        } catch (Exception e) {
            return ApiResponse.error("获取部门详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建部门
     */
    @ApiOperation("创建部门")
    @PostMapping("/add")
    public ApiResponse<Map<String, Object>> create(@Validated @RequestBody Department department) {
        try {
            boolean result = departmentService.createDepartment(department);
            Map<String, Object> data = new HashMap<>();
            data.put("id", department.getId());
            data.put("success", result);
            return ApiResponse.success(data, "创建部门成功");
        } catch (Exception e) {
            return ApiResponse.error("创建部门失败: " + e.getMessage());
        }
    }

    /**
     * 更新部门
     */
    @ApiOperation("更新部门")
    @PostMapping("/update")
    public ApiResponse<Map<String, Boolean>> update(@Validated @RequestBody Department department) {
        try {
            boolean result = departmentService.updateDepartment(department);
            Map<String, Boolean> data = new HashMap<>();
            data.put("success", result);
            return ApiResponse.success(data, "更新部门成功");
        } catch (Exception e) {
            return ApiResponse.error("更新部门失败: " + e.getMessage());
        }
    }

    /**
     * 删除部门
     */
    @ApiOperation("删除部门")
    @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "Integer", paramType = "path")
    @DeleteMapping("/{id}")
    public ApiResponse<Map<String, Boolean>> delete(@PathVariable Integer id) {
        try {
            boolean result = departmentService.deleteDepartment(id);
            Map<String, Boolean> data = new HashMap<>();
            data.put("success", result);
            return ApiResponse.success(data, "删除部门成功");
        } catch (Exception e) {
            return ApiResponse.error("删除部门失败: " + e.getMessage());
        }
    }

    /**
     * 更新部门排序
     */
    @ApiOperation("更新部门排序")
    @PostMapping("/sort")
    public ApiResponse<Map<String, Boolean>> updateSort(@RequestBody List<Map<String, Object>> sortData) {
        try {
            boolean result = departmentService.updateDepartmentSort(sortData);
            Map<String, Boolean> data = new HashMap<>();
            data.put("success", result);
            return ApiResponse.success(data, "更新部门排序成功");
        } catch (Exception e) {
            return ApiResponse.error("更新部门排序失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门及其所有子部门的ID列表
     */
    @ApiOperation("获取部门及其所有子部门的ID列表")
    @ApiImplicitParam(name = "id", value = "部门ID", required = true, dataType = "Integer", paramType = "path")
    @GetMapping("/children/{id}")
    public ApiResponse<List<Integer>> getDepartmentAndChildrenIds(@PathVariable Integer id) {
        try {
            List<Integer> idList = departmentService.getDepartmentAndChildrenIds(id);
            return ApiResponse.success(idList);
        } catch (Exception e) {
            return ApiResponse.error("获取部门及子部门ID列表失败: " + e.getMessage());
        }
    }
} 