import { mockResponse, generateId, randomDate } from './utils'
import type { Admin } from '@/api/admin'
import { departmentData, initDepartmentList } from './department'

// 权限定义
export interface Permission {
  id: number
  name: string
  code: string
  description?: string
  children?: Permission[]
}

// 权限树数据
export const permissionTree: Permission[] = [
  {
    id: 1,
    name: '系统管理',
    code: 'system',
    description: '系统管理相关权限',
    children: [
      { id: 11, name: '管理员管理', code: 'system:admin', description: '管理员的增删改查权限' },
      { id: 12, name: '部门管理', code: 'system:department', description: '部门的增删改查权限' },
      { id: 13, name: '角色管理', code: 'system:role', description: '角色的增删改查权限' }
    ]
  },
  {
    id: 2,
    name: '学员管理',
    code: 'student',
    description: '学员管理相关权限',
    children: [
      { id: 21, name: '学员信息', code: 'student:info', description: '查看学员信息' },
      { id: 22, name: '学员录入', code: 'student:add', description: '添加新学员' },
      { id: 23, name: '学员编辑', code: 'student:edit', description: '编辑学员信息' }
    ]
  },
  {
    id: 3,
    name: '学习资源',
    code: 'resource',
    description: '学习资源相关权限',
    children: [
      { id: 31, name: '资源管理', code: 'resource:manage', description: '管理学习资源' },
      { id: 32, name: '资源上传', code: 'resource:upload', description: '上传学习资源' },
      { id: 33, name: '资源审核', code: 'resource:review', description: '审核学习资源' }
    ]
  },
  {
    id: 4,
    name: '论坛管理',
    code: 'forum',
    description: '论坛管理相关权限',
    children: [
      { id: 41, name: '帖子管理', code: 'forum:post', description: '管理论坛帖子' },
      { id: 42, name: '评论管理', code: 'forum:comment', description: '管理帖子评论' },
      { id: 43, name: '版块管理', code: 'forum:category', description: '管理论坛版块' }
    ]
  }
]

// 扁平化权限列表，用于快速查找
export const permissionList: Permission[] = []

// 将权限树扁平化
function flattenPermissionTree(permissions: Permission[], result: Permission[] = []) {
  permissions.forEach(perm => {
    const { children, ...permWithoutChildren } = perm
    result.push(permWithoutChildren)
    
    if (children && children.length > 0) {
      flattenPermissionTree(children, result)
    }
  })
  
  return result
}

// 初始化权限列表
function initPermissionList() {
  return flattenPermissionTree(permissionTree, permissionList)
}

// 确保部门列表已初始化
initDepartmentList()

// 生成管理员数据
let adminList: Admin[] = [
  {
    id: 1,
    realName: '超级管理员',
    username: 'admin',
    departmentId: 1,
    department: '成远矿业',
    phone: '13800138000',
    email: '<EMAIL>',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    lastLoginTime: '2024-01-10 16:30:25',
    createTime: '2023-01-01 10:00:00',
    status: 1,
    permissions: [11, 12, 13, 21, 22, 23, 31, 32, 33, 41, 42, 43],
    remark: '系统超级管理员'
  },
  {
    id: 2,
    realName: '张管理员',
    username: 'zhangadmin',
    departmentId: 2,
    department: '采矿部',
    phone: '13812345678',
    email: '<EMAIL>',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    lastLoginTime: '2024-01-10 14:20:12',
    createTime: '2023-03-15 09:30:00',
    status: 1,
    permissions: [21, 22, 31, 41],
    remark: '采矿部管理员'
  },
  {
    id: 3,
    realName: '李管理员',
    username: 'liadmin',
    departmentId: 3,
    department: '安全部',
    phone: '13887654321',
    email: '<EMAIL>',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    lastLoginTime: '2024-01-09 11:45:30',
    createTime: '2023-06-20 14:15:00',
    status: 0,
    permissions: [21, 31],
    remark: '安全部管理员'
  }
]

// 生成下一个管理员ID
let nextAdminId = 4

// 获取管理员列表（带分页）
export function mockGetAdminList(params: {
  keyword?: string
  departmentId?: number
  status?: number
  page: number
  size: number
}) {
  let result = [...adminList]
  
  // 关键词过滤
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    result = result.filter(
      admin => admin.realName.toLowerCase().includes(keyword) || 
               admin.username.toLowerCase().includes(keyword)
    )
  }
  
  // 部门过滤
  if (params.departmentId !== undefined) {
    result = result.filter(admin => admin.departmentId === params.departmentId)
  }
  
  // 状态过滤
  if (params.status !== undefined) {
    result = result.filter(admin => admin.status === params.status)
  }
  
  // 计算总数
  const total = result.length
  
  // 分页
  const start = (params.page - 1) * params.size
  const end = start + params.size
  result = result.slice(start, end)
  
  return mockResponse({
    list: result,
    total
  })
}

// 获取管理员详情
export function mockGetAdminById(id: number) {
  const admin = adminList.find(a => a.id === id)
  if (admin) {
    return mockResponse(admin)
  }
  
  return mockResponse(null, 404, '管理员不存在')
}

// 添加管理员
export function mockAddAdmin(data: Partial<Admin> & { password: string }) {
  // 检查用户名是否已存在
  const exists = adminList.some(admin => admin.username === data.username)
  if (exists) {
    return mockResponse(null, 400, '用户名已存在')
  }
  
  // 查找部门名称
  const findDepartmentById = (id: number) => {
    const dept = initDepartmentList().find(d => d.id === id)
    return dept ? dept.name : '未知部门'
  }
  
  const newAdmin: Admin = {
    id: nextAdminId++,
    realName: data.realName || '',
    username: data.username || '',
    departmentId: data.departmentId || 1,
    department: data.departmentId ? findDepartmentById(data.departmentId) : '未知部门',
    phone: data.phone || '',
    email: data.email || '',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    lastLoginTime: '',
    createTime: new Date().toLocaleString(),
    status: data.status !== undefined ? data.status : 1,
    permissions: data.permissions || [],
    remark: data.remark || ''
  }
  
  adminList.push(newAdmin)
  
  return mockResponse({ id: newAdmin.id })
}

// 更新管理员
export function mockUpdateAdmin(data: Partial<Admin>) {
  if (!data.id) {
    return mockResponse(null, 400, '管理员ID不能为空')
  }
  
  const index = adminList.findIndex(admin => admin.id === data.id)
  if (index === -1) {
    return mockResponse(null, 404, '管理员不存在')
  }
  
  // 如果更新了用户名，检查是否已存在
  if (data.username && data.username !== adminList[index].username) {
    const exists = adminList.some(admin => admin.username === data.username)
    if (exists) {
      return mockResponse(null, 400, '用户名已存在')
    }
  }
  
  // 如果更新了部门，更新部门名称
  if (data.departmentId && data.departmentId !== adminList[index].departmentId) {
    const findDepartmentById = (id: number) => {
      const dept = initDepartmentList().find(d => d.id === id)
      return dept ? dept.name : '未知部门'
    }
    
    data.department = findDepartmentById(data.departmentId)
  }
  
  adminList[index] = { ...adminList[index], ...data }
  
  return mockResponse({ success: true })
}

// 删除管理员
export function mockDeleteAdmin(id: number) {
  const index = adminList.findIndex(admin => admin.id === id)
  if (index === -1) {
    return mockResponse(null, 404, '管理员不存在')
  }
  
  // 超级管理员不能删除
  if (adminList[index].username === 'admin') {
    return mockResponse(null, 403, '不能删除超级管理员')
  }
  
  adminList.splice(index, 1)
  
  return mockResponse({ success: true })
}

// 更新管理员状态
export function mockUpdateAdminStatus(id: number, status: number) {
  const index = adminList.findIndex(admin => admin.id === id)
  if (index === -1) {
    return mockResponse(null, 404, '管理员不存在')
  }
  
  adminList[index].status = status
  
  return mockResponse({ success: true })
}

// 重置管理员密码
export function mockResetAdminPassword(id: number) {
  const index = adminList.findIndex(admin => admin.id === id)
  if (index === -1) {
    return mockResponse(null, 404, '管理员不存在')
  }
  
  // 模拟新密码
  const newPassword = '123456'
  
  return mockResponse({ password: newPassword })
}

// 获取管理员权限
export function mockGetAdminPermissions(id: number) {
  const admin = adminList.find(a => a.id === id)
  if (!admin) {
    return mockResponse(null, 404, '管理员不存在')
  }
  
  return mockResponse({ permissions: admin.permissions || [] })
}

// 设置管理员权限
export function mockSetAdminPermissions(id: number, permissions: number[]) {
  const index = adminList.findIndex(admin => admin.id === id)
  if (index === -1) {
    return mockResponse(null, 404, '管理员不存在')
  }
  
  adminList[index].permissions = permissions
  
  return mockResponse({ success: true })
}

// 导出管理员列表
export function mockExportAdminList(params: {
  keyword?: string
  departmentId?: number
  status?: number
}) {
  // 在实际情况下，这里应该返回一个Blob对象
  // 但在模拟环境中，我们只返回一个成功信息
  return mockResponse({ success: true })
}

// 初始化权限列表
initPermissionList() 