<template>
  <view class="page-container">

    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">用户资料</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-area" scroll-y>
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <up-loading-icon color="#667eea" size="24"></up-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 用户信息卡片 -->
      <view v-else class="user-card">
        <!-- 用户基本信息 -->
        <view class="user-header">
          <up-image
              :src="userInfo.avatar"
              class="user-avatar"
              height="60"
              shape="circle"
              width="60"
          ></up-image>
          <view class="user-infos">
            <text class="user-name">{{ userInfo.name }}</text>
            <text class="user-desc">{{ userInfo.departmentName }} | {{ userInfo.jobTitle }}</text>
          </view>
          <view v-if="userInfo.id !== loginUserId" :class="{ following: userInfo.isFollowing }" class="follow-btn"
                @click="toggleFollow">
            <up-icon
                :color="userInfo.isFollowing ? '#ffffff' : '#667eea'"
                :name="userInfo.isFollowing ? 'checkmark' : 'plus'"
                size="10"
            ></up-icon>
            <text class="follow-text">{{ userInfo.isFollowing ? '已关注' : '关注' }}</text>
          </view>
        </view>

        <!-- 用户统计 -->
        <view class="user-stats">
          <view class="stat-item" @click="switchTab('posts')">
            <text class="stat-number">{{ userInfo.postCount || 0 }}</text>
            <text class="stat-label">帖子</text>
          </view>
          <view class="stat-item" @click="switchTab('followers')">
            <text class="stat-number">{{ userInfo.followerCount || 0 }}</text>
            <text class="stat-label">粉丝</text>
          </view>
          <view class="stat-item" @click="switchTab('following')">
            <text class="stat-number">{{ userInfo.followingCount || 0 }}</text>
            <text class="stat-label">关注</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userInfo.likeCount || 0 }}</text>
            <text class="stat-label">获赞</text>
          </view>
        </view>
      </view>

      <!-- 标签页 -->
      <view class="tab-section">
        <up-tabs
            :current="currentTab"
            :list="tabList"
            activeStyle="color: #667eea; font-weight: 600;"
            inactiveStyle="color: #909399;"
            lineColor="#667eea"
            lineHeight="4"
            lineWidth="30"
            @change="onTabChange"
        ></up-tabs>
      </view>

      <!-- 内容区域 -->
      <view class="tab-content">
        <!-- 帖子列表 -->
        <view v-if="currentTab === 0" class="posts-tab">
          <view v-if="postList.length === 0" class="empty-state">
            <up-empty
                mode="list"
                text="暂无帖子"
                textColor="#909399"
                textSize="14"
            ></up-empty>
          </view>
          <view v-else class="post-list">
            <view
                v-for="post in postList"
                :key="post.id"
                class="post-item"
                @click="goToPostDetail(post)"
            >
              <view class="post-header">
                <text class="post-title">{{ post.title }}</text>
                <text class="post-time">{{ formatTime(post.createdAt) }}</text>
              </view>
              <text class="post-excerpt">{{ post.content }}</text>
              <view class="post-stats">
                <view class="stat-item">
                  <up-icon color="#909399" name="eye" size="14"></up-icon>
                  <text class="stat-text">{{ post.viewCount }}</text>
                </view>
                <view class="stat-item">
                  <up-icon color="#909399" name="chat" size="14"></up-icon>
                  <text class="stat-text">{{ post.replyCount }}</text>
                </view>
                <view class="stat-item">
                  <up-icon color="#909399" name="thumb-up" size="14"></up-icon>
                  <text class="stat-text">{{ post.likeCount }}</text>
                </view>
              </view>
            </view>

            <!-- 加载更多 -->
            <view v-if="postList.length > 0" class="load-more">
              <view v-if="postLoading" class="loading-text">
                <up-loading-icon color="#667eea" size="16"></up-loading-icon>
                <text>加载中...</text>
              </view>
              <view v-else-if="!postHasMore" class="nomore-text">
                <text>没有更多了</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 粉丝列表 -->
        <view v-else-if="currentTab === 1" class="followers-tab">
          <view v-if="followerList.length === 0" class="empty-state">
            <up-empty
                mode="list"
                text="暂无粉丝"
                textColor="#909399"
                textSize="14"
            ></up-empty>
          </view>
          <view v-else class="follower-list">
            <view
                v-for="follower in followerList"
                :key="follower.id"
                class="follower-item"
                @click="goToUserProfile(follower.id)"
            >
              <up-image
                  :src="follower.avatar"
                  class="follower-avatar"
                  height="48"
                  shape="circle"
                  width="48"
              ></up-image>
              <view class="follower-info">
                <text class="follower-name">{{ follower.name }}</text>
                <text class="follower-desc">{{ follower.departmentName }} | {{ follower.jobTitle }}</text>
              </view>
            </view>

            <!-- 加载更多 -->
            <view v-if="followerList.length > 0" class="load-more">
              <view v-if="followerLoading" class="loading-text">
                <up-loading-icon color="#667eea" size="16"></up-loading-icon>
                <text>加载中...</text>
              </view>
              <view v-else-if="!followerHasMore" class="nomore-text">
                <text>没有更多了</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 关注列表 -->
        <view v-else-if="currentTab === 2" class="following-tab">
          <view v-if="followingList.length === 0" class="empty-state">
            <up-empty
                mode="list"
                text="暂无关注"
                textColor="#909399"
                textSize="14"
            ></up-empty>
          </view>
          <view v-else class="following-list">
            <view
                v-for="following in followingList"
                :key="following.id"
                class="following-item"
                @click="goToUserProfile(following.id)"
            >
              <up-image
                  :src="following.avatar"
                  class="following-avatar"
                  height="48"
                  shape="circle"
                  width="48"
              ></up-image>
              <view class="following-info">
                <text class="following-name">{{ following.name }}</text>
                <text class="follower-desc">{{ following.departmentName }} | {{ following.jobTitle }}</text>
              </view>
            </view>

            <!-- 加载更多 -->
            <view v-if="followingList.length > 0" class="load-more">
              <view v-if="followingLoading" class="loading-text">
                <up-loading-icon color="#667eea" size="16"></up-loading-icon>
                <text>加载中...</text>
              </view>
              <view v-else-if="!followingHasMore" class="nomore-text">
                <text>没有更多了</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import {getUserProfile, getUserFollowers, getUserFollowing} from '@/api/user'
import {followUser, unfollowUser, getPostListByUserId} from '@/api/forum'
import {formatTime} from '@/utils/timeUtil'

export default {
  data() {
    return {
      // 用户ID
      userId: null,
      // 当前用户信息
      loginUserId: null,

      // 页面状态
      loading: true,
      currentTab: 0,

      // 标签页配置
      tabList: [
        {name: '帖子', id: 'posts'},
        {name: '粉丝', id: 'followers'},
        {name: '关注', id: 'following'}
      ],

      // 用户信息
      userInfo: {
        id: 0,
        name: '',
        avatar: '',
        title: '',
        description: '',
        postCount: 0,
        followerCount: 0,
        followingCount: 0,
        likeCount: 0,
        isFollowing: false
      },

      // 帖子列表
      postList: [],
      postPageNum: 1,
      postPageSize: 10,
      postHasMore: true,
      postLoading: false,

      // 粉丝列表
      followerList: [],
      followerPageNum: 1,
      followerPageSize: 10,
      followerHasMore: true,
      followerLoading: false,

      // 关注列表
      followingList: [],
      followingPageNum: 1,
      followingPageSize: 10,
      followingHasMore: true,
      followingLoading: false
    }
  },

  onLoad(options) {
    // 获取用户ID
    if (options.userId || options.id) {
      this.userId = parseInt(options.userId || options.id);
    }
    // 获取当前用户信息
    this.loginUserId = uni.getStorageSync('userInfo').id;
    // 加载用户信息
    this.loadUserProfile();
    // 加载帖子列表
    this.loadUserPosts();
  },

  // 上拉加载更多
  onReachBottom() {
    switch (this.currentTab) {
      case 0: // 帖子
        this.loadUserPosts(true);
        break;
      case 1: // 粉丝
        this.loadUserFollowers(true);
        break;
      case 2: // 关注
        this.loadUserFollowing(true);
        break;
    }
  },

  methods: {
    // 页面导航
    goBack() {
      uni.navigateBack();
    },

    // 加载用户资料
    async loadUserProfile() {
      try {
        this.loading = true;
        this.userInfo = await getUserProfile(this.userId);
      } catch (error) {
        uni.showToast({
          title: '加载用户资料失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载用户帖子
    async loadUserPosts(isLoadMore = false) {
      if (isLoadMore) {
        if (!this.postHasMore || this.postLoading) return;
        this.postPageNum += 1;
        this.postLoading = true;
      } else {
        this.postPageNum = 1;
        this.postList = [];
        this.postHasMore = true;
      }

      try {
        const response = await getPostListByUserId(this.userId, {
          pageNum: this.postPageNum,
          pageSize: this.postPageSize
        });

        if (isLoadMore) {
          this.postList = this.postList.concat(response.list || []);
        } else {
          this.postList = response.list || [];
        }

        this.postHasMore = this.postList.length < response.total;
      } catch (error) {
        if (isLoadMore) {
          this.postPageNum -= 1; // 回退页码
        }
      } finally {
        uni.stopPullDownRefresh();
        this.postLoading = false;
      }
    },

    // 加载粉丝列表
    async loadUserFollowers(isLoadMore = false) {
      if (isLoadMore) {
        if (!this.followerHasMore || this.followerLoading) return;
        this.followerPageNum += 1;
        this.followerLoading = true;
      } else {
        this.followerPageNum = 1;
        this.followerList = [];
        this.followerHasMore = true;
      }

      try {
        const response = await getUserFollowers(this.userId, {
          pageNum: this.followerPageNum,
          pageSize: this.followerPageSize
        });

        if (isLoadMore) {
          this.followerList = this.followerList.concat(response.list || []);
        } else {
          this.followerList = response.list || [];
        }

        this.followerHasMore = this.followerList.length < response.total;
      } catch (error) {
        if (isLoadMore) {
          this.followerPageNum -= 1; // 回退页码
        }
      } finally {
        uni.stopPullDownRefresh();
        this.followerLoading = false;
      }
    },

    // 加载关注列表
    async loadUserFollowing(isLoadMore = false) {
      if (isLoadMore) {
        if (!this.followingHasMore || this.followingLoading) return;
        this.followingPageNum += 1;
        this.followingLoading = true;
      } else {
        this.followingPageNum = 1;
        this.followingList = [];
        this.followingHasMore = true;
      }

      try {
        const response = await getUserFollowing(this.userId, {
          pageNum: this.followingPageNum,
          pageSize: this.followingPageSize
        });

        if (isLoadMore) {
          this.followingList = this.followingList.concat(response.list || []);
        } else {
          this.followingList = response.list || [];
        }

        this.followingHasMore = this.followingList.length < response.total;
      } catch (error) {
        if (isLoadMore) {
          this.followingPageNum -= 1; // 回退页码
        }
      } finally {
        uni.stopPullDownRefresh();
        this.followingLoading = false;
      }
    },

    // 切换关注状态
    async toggleFollow() {
      try {
        if (this.userInfo.isFollowing) {
          await unfollowUser(this.userId);
          this.userInfo.followerCount -= 1;
        } else {
          await followUser(this.userId);
          this.userInfo.followerCount += 1;
        }
        this.userInfo.isFollowing = !this.userInfo.isFollowing;
        uni.showToast({
          title: this.userInfo.isFollowing ? '关注成功' : '取消关注',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 标签页切换
    onTabChange(event) {
      this.currentTab = event.index;
      this.switchTab(this.tabList[event.index].id);
    },

    // 切换标签页内容
    switchTab(tabId) {
      switch (tabId) {
        case 'posts':
          if (this.postList.length === 0) {
            this.loadUserPosts();
          }
          break;
        case 'followers':
          if (this.followerList.length === 0) {
            this.loadUserFollowers();
          }
          break;
        case 'following':
          if (this.followingList.length === 0) {
            this.loadUserFollowing();
          }
          break;
      }
    },

    // 跳转到帖子详情
    goToPostDetail(post) {
      uni.navigateTo({
        url: `/pages/forum/detail?id=${post.id}`
      });
    },

    // 跳转到用户资料
    goToUserProfile(userId) {
      uni.navigateTo({
        url: `/pages/profile/user-profile?userId=${userId}`
      });
    },

    // 时间格式化
    formatTime(timeStr) {
      return formatTime(timeStr);
    }
  }
}
</script>

<style lang="scss" scoped>
.more-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-area {
  flex: 1;
  padding: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.loading-text {
  margin-top: 12px;
  color: #909399;
  font-size: 14px;
}

.user-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.user-avatar {
  margin-right: 16px;
}

.user-infos {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.user-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.follow-btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border: 1px solid #667eea;
  border-radius: 20px;
  background: #fff;
  margin-left: 12px;

  &.following {
    background: #43e97b;
    border-color: #43e97b;
  }
}

.follow-text {
  font-size: 14px;
  color: #667eea;
  margin-left: 4px;

  .following & {
    color: #fff;
  }
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.tab-section {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  background: #fff;
  border-radius: 12px;
  min-height: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state {
  padding: 60px 20px;
}

.post-list {
  padding: 16px;
}

.post-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.post-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.post-time {
  font-size: 12px;
  color: #909399;
}

.post-excerpt {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.post-stats {
  display: flex;
  align-items: center;
}

.post-stats .stat-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  flex: none;
}

.stat-text {
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.follower-list,
.following-list {
  padding: 16px;
}

.follower-item,
.following-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.follower-avatar,
.following-avatar {
  margin-right: 12px;
}

.follower-info,
.following-info {
  display: flex;
  flex-direction: column;
}

.follower-name,
.following-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.follower-desc,
.following-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}
</style>
