<template>
  <el-config-provider namespace="el" :locale="zhCn">
    <router-view />
  </el-config-provider>
</template>

<script setup lang="ts">
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { provide, ref } from 'vue'
import { getDepartmentTree } from '@/api/department'
import type { Department } from '@/api/department'

// 部门树缓存
const departmentTreeCache = ref<Department[]>([])
const departmentLoading = ref(false)

// 获取并缓存部门树数据
const refreshDepartmentCache = async () => {
  departmentLoading.value = true
  try {
    const deptTree = await getDepartmentTree()
    departmentTreeCache.value = deptTree
  } catch (error) {
    console.error('加载部门数据失败:', error)
  } finally {
    departmentLoading.value = false
  }
}

// 初始加载部门数据
refreshDepartmentCache()

// 提供部门数据给子组件使用
provide('departmentCache', {
  departmentTree: departmentTreeCache,
  loading: departmentLoading,
  refresh: refreshDepartmentCache
})
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}
</style>