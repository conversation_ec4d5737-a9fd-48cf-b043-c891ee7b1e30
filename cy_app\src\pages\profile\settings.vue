<template>
  <view class="settings-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">系统设置</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <!-- 通知设置 -->
      <view class="settings-section">
        <view class="section-title">通知设置</view>
        <view class="settings-list">
          <view class="setting-item">
            <view class="setting-left">
              <up-icon color="#667eea" name="bell" size="20"></up-icon>
              <text class="setting-name">推送通知</text>
            </view>
            <view class="setting-right">
              <up-switch
                  v-model="settings.pushNotification"
                  activeColor="#667eea"
                  @change="handlePushNotificationChange"
              ></up-switch>
            </view>
          </view>
          <view class="setting-item">
            <view class="setting-left">
              <up-icon color="#f5576c" name="chat" size="20"></up-icon>
              <text class="setting-name">消息提醒</text>
            </view>
            <view class="setting-right">
              <up-switch
                  v-model="settings.messageNotification"
                  activeColor="#667eea"
                  @change="handleMessageNotificationChange"
              ></up-switch>
            </view>
          </view>
          <view class="setting-item">
            <view class="setting-left">
              <up-icon color="#4facfe" name="clock" size="20"></up-icon>
              <text class="setting-name">学习提醒</text>
            </view>
            <view class="setting-right">
              <up-switch
                  v-model="settings.studyReminder"
                  activeColor="#667eea"
                  @change="handleStudyReminderChange"
              ></up-switch>
            </view>
          </view>
        </view>
      </view>

      <!-- 隐私设置 -->
      <view class="settings-section">
        <view class="section-title">隐私设置</view>
        <view class="settings-list">
          <view class="setting-item" @click="handlePrivacyClick">
            <view class="setting-left">
              <up-icon color="#43e97b" name="lock" size="20"></up-icon>
              <text class="setting-name">隐私政策</text>
            </view>
            <view class="setting-right">
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
          <view class="setting-item" @click="handleDataUsageClick">
            <view class="setting-left">
              <up-icon color="#fa709a" name="order" size="20"></up-icon>
              <text class="setting-name">数据使用</text>
            </view>
            <view class="setting-right">
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
          <view class="setting-item">
            <view class="setting-left">
              <up-icon color="#a8edea" name="eye" size="20"></up-icon>
              <text class="setting-name">学习记录可见</text>
            </view>
            <view class="setting-right">
              <up-switch
                  v-model="settings.studyRecordVisible"
                  activeColor="#667eea"
                  @change="handleStudyRecordVisibleChange"
              ></up-switch>
            </view>
          </view>
        </view>
      </view>

      <!-- 账户安全 -->
      <view class="settings-section">
        <view class="section-title">账户安全</view>
        <view class="settings-list">
          <view class="setting-item" @click="handleChangePassword">
            <view class="setting-left">
              <up-icon color="#667eea" name="edit-pen" size="20"></up-icon>
              <text class="setting-name">修改密码</text>
            </view>
            <view class="setting-right">
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
          <view class="setting-item" @click="handleBindPhone">
            <view class="setting-left">
              <up-icon color="#f5576c" name="phone" size="20"></up-icon>
              <text class="setting-name">绑定手机</text>
            </view>
            <view class="setting-right">
              <text class="setting-value">{{ userInfo.phone || '未绑定' }}</text>
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
          <view class="setting-item" @click="handleBindEmail">
            <view class="setting-left">
              <up-icon color="#4facfe" name="email" size="20"></up-icon>
              <text class="setting-name">绑定邮箱</text>
            </view>
            <view class="setting-right">
              <text class="setting-value">{{ userInfo.email || '未绑定' }}</text>
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="settings-section">
        <view class="section-title">应用设置</view>
        <view class="settings-list">
          <view class="setting-item" @click="handleClearCache">
            <view class="setting-left">
              <up-icon color="#ff6b6b" name="trash" size="20"></up-icon>
              <text class="setting-name">清除缓存</text>
            </view>
            <view class="setting-right">
              <text class="setting-value">{{ cacheSize }}</text>
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
          <view class="setting-item" @click="handleCheckUpdate">
            <view class="setting-left">
              <up-icon color="#43e97b" name="download" size="20"></up-icon>
              <text class="setting-name">检查更新</text>
            </view>
            <view class="setting-right">
              <text class="setting-value">v{{ appVersion }}</text>
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
          <view class="setting-item" @click="handleFeedback">
            <view class="setting-left">
              <up-icon color="#fa709a" name="more-dot-fill" size="20"></up-icon>
              <text class="setting-name">意见反馈</text>
            </view>
            <view class="setting-right">
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 其他设置 -->
      <view class="settings-section">
        <view class="section-title">其他</view>
        <view class="settings-list">
          <view class="setting-item" @click="handleAbout">
            <view class="setting-left">
              <up-icon color="#a8edea" name="info-circle" size="20"></up-icon>
              <text class="setting-name">关于我们</text>
            </view>
            <view class="setting-right">
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
          <view class="setting-item" @click="handleHelp">
            <view class="setting-left">
              <up-icon color="#667eea" name="question-circle" size="20"></up-icon>
              <text class="setting-name">帮助中心</text>
            </view>
            <view class="setting-right">
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="logout-section">
        <up-button
            customStyle="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); border: none; border-radius: 12px;"
            text="退出登录"
            type="error"
            @click="handleLogout"
        ></up-button>
      </view>
    </view>
  </view>
</template>

<script>
import {getUserInfo} from '@/api/user'

export default {
  data() {
    return {
      userInfo: {
        id: '',
        name: '',
        phone: '',
        email: '',
        avatar: ''
      },
      settings: {
        pushNotification: true,
        messageNotification: true,
        studyReminder: true,
        studyRecordVisible: true
      },
      cacheSize: '0MB',
      appVersion: '1.0.0'
    }
  },

  onLoad() {
    this.loadSettings()
    this.calculateCacheSize()
  },

  onShow() {
    this.loadUserInfo()
  },

  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        this.userInfo = await getUserInfo()
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    // 加载设置
    loadSettings() {
      try {
        const savedSettings = uni.getStorageSync('app_settings')
        if (savedSettings) {
          this.settings = {...this.settings, ...savedSettings}
        }
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    },

    // 保存设置
    saveSettings() {
      try {
        uni.setStorageSync('app_settings', this.settings)
      } catch (error) {
        console.error('保存设置失败:', error)
      }
    },

    // 计算缓存大小
    calculateCacheSize() {
      try {
        const cacheInfo = uni.getStorageInfoSync()
        const sizeInKB = cacheInfo.currentSize
        const sizeInMB = (sizeInKB / 1024).toFixed(1)
        this.cacheSize = `${sizeInMB}MB`
      } catch (error) {
        console.error('计算缓存大小失败:', error)
        this.cacheSize = '0MB'
      }
    },

    // 推送通知开关
    handlePushNotificationChange(value) {
      this.settings.pushNotification = value
      this.saveSettings()
      uni.showToast({
        title: value ? '已开启推送通知' : '已关闭推送通知',
        icon: 'none'
      })
    },

    // 消息提醒开关
    handleMessageNotificationChange(value) {
      this.settings.messageNotification = value
      this.saveSettings()
      uni.showToast({
        title: value ? '已开启消息提醒' : '已关闭消息提醒',
        icon: 'none'
      })
    },

    // 学习提醒开关
    handleStudyReminderChange(value) {
      this.settings.studyReminder = value
      this.saveSettings()
      uni.showToast({
        title: value ? '已开启学习提醒' : '已关闭学习提醒',
        icon: 'none'
      })
    },

    // 学习记录可见开关
    handleStudyRecordVisibleChange(value) {
      this.settings.studyRecordVisible = value
      this.saveSettings()
      uni.showToast({
        title: value ? '学习记录已公开' : '学习记录已隐藏',
        icon: 'none'
      })
    },

    // 隐私政策
    handlePrivacyClick() {
      uni.navigateTo({
        url: '/pages/profile/privacy'
      })
    },

    // 数据使用
    handleDataUsageClick() {
      uni.navigateTo({
        url: '/pages/profile/data-usage'
      })
    },

    // 修改密码
    handleChangePassword() {
      uni.navigateTo({
        url: '/pages/profile/change-password'
      })
    },

    // 绑定手机
    handleBindPhone() {
      uni.navigateTo({
        url: '/pages/profile/bind-phone'
      })
    },

    // 绑定邮箱
    handleBindEmail() {
      uni.navigateTo({
        url: '/pages/profile/bind-email'
      })
    },

    // 清除缓存
    handleClearCache() {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除所有缓存数据吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              uni.clearStorageSync()
              this.calculateCacheSize()
              uni.showToast({
                title: '缓存已清除',
                icon: 'success'
              })
            } catch (error) {
              uni.showToast({
                title: '清除缓存失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 检查更新
    handleCheckUpdate() {
      uni.showLoading({
        title: '检查中...'
      })

      // 模拟检查更新
      setTimeout(() => {
        uni.hideLoading()
        uni.showModal({
          title: '检查更新',
          content: '当前已是最新版本',
          showCancel: false
        })
      }, 1500)
    },

    // 意见反馈
    handleFeedback() {
      uni.navigateTo({
        url: '/pages/profile/feedback'
      })
    },

    // 关于我们
    handleAbout() {
      uni.navigateTo({
        url: '/pages/profile/about'
      })
    },

    // 帮助中心
    handleHelp() {
      uni.navigateTo({
        url: '/pages/profile/help'
      })
    },

    // 退出登录
    handleLogout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除用户数据
            uni.clearStorageSync()
            uni.reLaunch({
              url: '/pages/login/index'
            })
          }
        }
      })
    },

    // 返回上一页
    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-container {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.content-container {
  padding: 20px;
  padding-bottom: 120px;
}

.settings-section {
  background: #fff;
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-left: 4px;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: #f8f9fa;
}

.setting-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.setting-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.setting-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-value {
  font-size: 14px;
  color: #666;
  margin-right: 4px;
}

.logout-section {
  margin-top: 32px;
  padding: 0 20px;
}
</style>
