.pk-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
  }
}

.page-content {
  padding: 20px;
  padding-top: 20px;
  position: relative;
  z-index: 1;
}

// 模式选择
.mode-section {
  margin-bottom: 32px;
  animation: slideInUp 0.6s ease-out;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 16px;
}

.mode-cards {
  display: flex;
  gap: 16px;
}

.mode-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
  }

  &:active {
    transform: scale(0.96);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  &:hover::before {
    left: 100%;
  }
}

.mode-icon {
  width: 72px;
  height: 72px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 18px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  }

  .up-icon {
    position: relative;
    z-index: 1;
  }
}

.mode-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.mode-title {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 6px;
  letter-spacing: -0.02em;
}

.mode-desc {
  font-size: 13px;
  color: #64748b;
  line-height: 1.4;
  font-weight: 500;
}

// 历史记录
.history-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-top: 8px;
  animation: slideInUp 0.8s ease-out 0.2s both;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  text {
    font-size: 18px;
    font-weight: 700;
    color: #1a202c;
    letter-spacing: -0.02em;
  }
}

.more-text {
  font-size: 14px;
  color: #667eea;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;

  &:active {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(0.95);
  }
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.history-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.history-result {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;

  &.win {
    background: #c6f6d5;
    color: #22543d;
  }

  &.lose {
    background: #fed7d7;
    color: #742a2a;
  }

  &.draw {
    background: #feebc8;
    color: #744210;
  }
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.history-bank {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.history-time {
  font-size: 12px;
  color: #718096;
}

.history-score {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  gap: 12px;
}

.empty-text {
  font-size: 14px;
  color: #a0aec0;
}

// 对话框样式
.match-dialog,
.room-dialog,
.join-room-dialog,
.matching-dialog {
  background: #fff;
  border-radius: 20px;
  padding: 28px 24px 24px;
  min-width: 320px;
  max-width: 90vw;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    margin-top: 8px;
  }
}

.dialog-title {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  text-align: center;
  margin-bottom: 24px;
  letter-spacing: -0.02em;
}

.form-item {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 15px;
  color: #374151;
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    background: #f1f5f9;
    border-color: #667eea;
    transform: scale(0.98);
  }

  .up-icon {
    transition: transform 0.3s ease;
  }
}

.dialog-content {
  margin-bottom: 20px;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

// 表单项
.form-item {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 8px;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  color: #2d3748;
}

// 房间操作
.room-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// 匹配中
.matching-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px 0;
}

.matching-text {
  font-size: 16px;
  font-weight: 500;
  color: #2d3748;
}

.matching-tip {
  font-size: 14px;
  color: #718096;
}

// 动画关键帧
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
