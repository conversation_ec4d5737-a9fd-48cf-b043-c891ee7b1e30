.pk-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.page-content {
  padding: 20px;
  padding-top: 20px;
}

// 模式选择
.mode-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 16px;
}

.mode-cards {
  display: flex;
  gap: 16px;
}

.mode-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.mode-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.mode-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mode-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.mode-desc {
  font-size: 12px;
  color: #718096;
}

// 历史记录
.history-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  color: #1a202c;
}

.more-text {
  font-size: 14px;
  color: #667eea;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.history-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.history-result {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;

  &.win {
    background: #c6f6d5;
    color: #22543d;
  }

  &.lose {
    background: #fed7d7;
    color: #742a2a;
  }

  &.draw {
    background: #feebc8;
    color: #744210;
  }
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.history-bank {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.history-time {
  font-size: 12px;
  color: #718096;
}

.history-score {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  gap: 12px;
}

.empty-text {
  font-size: 14px;
  color: #a0aec0;
}

// 对话框样式
.match-dialog,
.room-dialog,
.join-room-dialog,
.matching-dialog {
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  min-width: 300px;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  text-align: center;
  margin-bottom: 20px;
}

.dialog-content {
  margin-bottom: 20px;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

// 表单项
.form-item {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 8px;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  color: #2d3748;
}

// 房间操作
.room-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// 匹配中
.matching-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px 0;
}

.matching-text {
  font-size: 16px;
  font-weight: 500;
  color: #2d3748;
}

.matching-tip {
  font-size: 14px;
  color: #718096;
}
