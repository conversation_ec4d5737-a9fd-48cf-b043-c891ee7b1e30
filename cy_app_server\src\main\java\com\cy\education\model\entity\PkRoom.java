package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PK房间实体
 */
@Data
@TableName("pk_room")
public class PkRoom {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 房间码
     */
    private String roomCode;
    
    /**
     * 题库ID
     */
    private Integer bankId;
    
    /**
     * 题目数量
     */
    private Integer questionCount;
    
    /**
     * 时间限制(秒)
     */
    private Integer timeLimit;
    
    /**
     * 房间状态: waiting-等待, ready-准备中, playing-游戏中, finished-已结束
     */
    private String status;
    
    /**
     * 创建者ID
     */
    private Integer creatorId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 开始时间
     */
    private LocalDateTime startedAt;
    
    /**
     * 结束时间
     */
    private LocalDateTime finishedAt;
}
