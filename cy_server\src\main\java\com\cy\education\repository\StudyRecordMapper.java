package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.StudyRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 学习记录Mapper接口
 */
@Mapper
public interface StudyRecordMapper extends BaseMapper<StudyRecord> {
    
    /**
     * 分页查询学习记录，包含学员姓名、部门名称、课程名称等信息
     * @param page 分页参数
     * @param studentId 学员ID
     * @param courseId 课程ID
     * @param departmentId 部门ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT r.*, s.name as student_name, d.name as department_name, c.name as course_name " +
            "FROM study_records r " +
            "LEFT JOIN students s ON r.user_id = s.id " +
            "LEFT JOIN departments d ON s.department_id = d.id " +
            "LEFT JOIN courses c ON r.course_id = c.id " +
            "<where> " +
            "  <if test='studentId != null'> " +
            "    AND r.user_id = #{studentId} " +
            "  </if> " +
            "  <if test='courseId != null'> " +
            "    AND r.course_id = #{courseId} " +
            "  </if> " +
            "  <if test='departmentId != null'> " +
            "    AND s.department_id = #{departmentId} " +
            "  </if> " +
            "  <if test='startTime != null'> " +
            "    AND r.created_at &gt;= #{startTime} " +
            "  </if> " +
            "  <if test='endTime != null'> " +
            "    AND r.created_at &lt;= #{endTime} " +
            "  </if> " +
            "</where> " +
            "ORDER BY r.updated_at DESC" +
            "</script>")
    IPage<StudyRecord> selectPageWithDetails(Page<StudyRecord> page, 
                                           @Param("studentId") Integer studentId,
                                           @Param("courseId") Integer courseId,
                                           @Param("departmentId") Integer departmentId,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询学员的课程学习记录
     * @param studentId 学员ID
     * @param courseId 课程ID
     * @return 学习记录
     */
    @Select("SELECT * FROM study_records WHERE user_id = #{studentId} AND course_id = #{courseId} " +
            "ORDER BY updated_at DESC LIMIT 1")
    StudyRecord selectByStudentAndCourse(@Param("studentId") Integer studentId, @Param("courseId") Integer courseId);

    /**
     * 查询学员的课时学习记录
     * @param studentId 学员ID
     * @param lessonId 课时ID
     * @return 学习记录
     */
    @Select("SELECT * FROM study_records WHERE user_id = #{studentId} AND lesson_id = #{lessonId} " +
            "ORDER BY updated_at DESC LIMIT 1")
    StudyRecord selectByStudentAndLesson(@Param("studentId") Integer studentId, @Param("lessonId") Integer lessonId);
    
    /**
     * 统计部门学习情况
     * @return 部门学习统计数据
     */
    @Select("SELECT d.id as departmentId, d.name as departmentName, " +
            "COUNT(DISTINCT r.user_id) as totalStudents, " +
            "COUNT(DISTINCT CASE WHEN r.last_study_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN r.user_id END) as activeStudents, " +
            "IFNULL(SUM(r.duration), 0) as totalStudyTime, " +
            "IFNULL(AVG(r.duration), 0) as averageStudyTime, " +
            "IFNULL(AVG(r.progress), 0) as completionRate " +
            "FROM departments d " +
            "LEFT JOIN students s ON s.department_id = d.id " +
            "LEFT JOIN study_records r ON r.user_id = s.id " +
            "GROUP BY d.id, d.name " +
            "ORDER BY totalStudyTime DESC")
    List<Map<String, Object>> getDepartmentStatistics();

    /**
     * 获取活跃学员排行
     * @param limit 限制数量
     * @return 活跃学员列表
     */
    @Select("SELECT s.id as userId, s.name as userName, d.name as departmentName, " +
            "IFNULL(SUM(r.duration), 0) as totalStudyTime, " +
            "MAX(r.last_study_time) as lastStudyTime " +
            "FROM students s " +
            "JOIN departments d ON s.department_id = d.id " +
            "LEFT JOIN study_records r ON s.id = r.user_id " +
            "GROUP BY s.id, s.name, d.name " +
            "ORDER BY totalStudyTime DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getActiveStudents(@Param("limit") Integer limit);
    
    /**
     * 查询用户的学习记录
     * @param userId 用户ID
     * @param courseId 课程ID（可选）
     * @return 学习记录列表
     */
    @Select("<script>" +
            "SELECT sr.*, c.name as course_name " +
            "FROM study_records sr " +
            "LEFT JOIN courses c ON sr.course_id = c.id " +
            "WHERE sr.user_id = #{userId} " +
            "<if test='courseId != null'> " +
            "  AND sr.course_id = #{courseId} " +
            "</if> " +
            "ORDER BY sr.last_study_time DESC" +
            "</script>")
    List<Map<String, Object>> selectUserStudyRecords(@Param("userId") Integer userId, @Param("courseId") Integer courseId);

    /**
     * 查询课程的学习记录
     * @param courseId 课程ID
     * @return 学习记录列表
     */
    @Select("SELECT sr.*, s.name as user_name " +
            "FROM study_records sr " +
            "LEFT JOIN students s ON sr.user_id = s.id " +
            "WHERE sr.course_id = #{courseId} " +
            "ORDER BY sr.last_study_time DESC")
    List<Map<String, Object>> selectCourseStudyRecords(@Param("courseId") Integer courseId);

    /**
     * 查询课时的学习记录
     * @param lessonId 课时ID
     * @return 学习记录列表
     */
    @Select("SELECT sr.*, s.name as user_name, c.name as course_name " +
            "FROM study_records sr " +
            "LEFT JOIN students s ON sr.user_id = s.id " +
            "LEFT JOIN courses c ON sr.course_id = c.id " +
            "WHERE sr.lesson_id = #{lessonId} " +
            "ORDER BY sr.last_study_time DESC")
    List<Map<String, Object>> selectLessonStudyRecords(@Param("lessonId") Integer lessonId);
    
    /**
     * 查询用户课程的学习进度
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 学习进度信息
     */
    @Select("SELECT " +
            "  IFNULL(AVG(sr.progress), 0) AS progress, " +
            "  IFNULL(MAX(sr.completed), 0) AS completed, " +
            "  IFNULL(SUM(sr.duration), 0) AS duration, " +
            "  MAX(sr.last_study_time) AS lastStudyTime " +
            "FROM study_records sr " +
            "WHERE sr.user_id = #{userId} AND sr.course_id = #{courseId}")
    Map<String, Object> selectUserCourseProgress(@Param("userId") Integer userId, @Param("courseId") Integer courseId);
    
    /**
     * 更新学习进度
     * @param id 记录ID
     * @param progress 进度
     * @param completed 是否完成
     * @return 影响行数
     */
    @Update("UPDATE study_records SET progress = #{progress}, completed = #{completed}, " +
            "last_study_time = NOW(), updated_at = NOW() " +
            "WHERE id = #{id}")
    int updateProgress(@Param("id") Integer id, @Param("progress") Integer progress, @Param("completed") Integer completed);
} 