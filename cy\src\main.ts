import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './styles/index.css'
// 引入wangeditor样式
import '@wangeditor/editor/dist/css/style.css'

// 导入模拟数据 (仅在开发环境且启用模拟时启用)
if (import.meta.env.DEV && import.meta.env.VITE_ENABLE_MOCK === 'true') {
  // 使用新的XHR模拟器
  import('./mock/xhr')
  console.log('已启用XHR模拟数据')
} else {
  console.log('XHR模拟数据已禁用')
}

// 启用调试模式
console.log('API基础URL:', import.meta.env.VITE_API_BASE_URL || '/api')

const app = createApp(App)
const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
app.use(ElementPlus)

app.mount('#app') 