package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.News;
import com.cy.education.model.vo.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.NewsMapper;
import com.cy.education.service.NewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 新闻服务实现
 */
@Service
public class NewsServiceImpl implements NewsService {

    @Autowired
    private NewsMapper newsMapper;

    @Override
    public PageResponse<News> listNews(ContentQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<News> queryWrapper = new LambdaQueryWrapper<>();
        
        // 状态条件
        if (param.getStatus() != null) {
            queryWrapper.eq(News::getStatus, param.getStatus());
        }
        
        // 置顶条件
        if (param.getIsTop() != null) {
            queryWrapper.eq(News::getTop, param.getIsTop());
        }
        
        // 关键词搜索（标题和内容）
        if (!StringUtils.isEmpty(param.getKeyword())) {
            queryWrapper.and(wrapper -> 
                wrapper.like(News::getTitle, param.getKeyword())
                        .or()
                        .like(News::getContent, param.getKeyword())
            );
        }
        
        // 排序（置顶优先，然后是发布时间）
        queryWrapper.orderByDesc(News::getTop, News::getPublishTime);
        
        // 分页查询
        Page<News> page = new Page<>(param.getPageNum(), param.getPageSize());
        Page<News> resultPage = newsMapper.selectPage(page, queryWrapper);
        
        // 转换为自定义分页响应对象
        return PageResponse.of(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    }

    @Override
    public News getNewsById(Integer id) {
        News news = newsMapper.selectById(id);
        if (news == null) {
            throw new BusinessException("新闻不存在");
        }
        return news;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addNews(News news) {
        // 设置默认值
        if (news.getStatus() == null) {
            news.setStatus(1); // 默认发布状态
        }
        if (news.getTop() == null) {
            news.setTop(false); // 默认不置顶
        }
        if (news.getViewCount() == null) {
            news.setViewCount(0); // 初始浏览量为0
        }
        
        // 设置发布时间
        if (news.getPublishTime() == null) {
            news.setPublishTime(LocalDateTime.now());
        }
        
        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        news.setCreatedAt(now);
        news.setUpdatedAt(now);
        
        int result = newsMapper.insert(news);
        if (result <= 0) {
            throw new BusinessException("添加新闻失败");
        }
        
        return news.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNews(News news) {
        // 检查新闻是否存在
        News existingNews = newsMapper.selectById(news.getId());
        if (existingNews == null) {
            throw new BusinessException("新闻不存在");
        }
        
        // 设置更新时间
        news.setUpdatedAt(LocalDateTime.now());
        
        return newsMapper.updateById(news) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNews(Integer id) {
        // 检查新闻是否存在
        News news = newsMapper.selectById(id);
        if (news == null) {
            throw new BusinessException("新闻不存在");
        }
        
        return newsMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNewsStatus(Integer id, Integer status) {
        // 检查新闻是否存在
        News news = newsMapper.selectById(id);
        if (news == null) {
            throw new BusinessException("新闻不存在");
        }
        
        // 检查状态是否合法
        if (status != 0 && status != 1) {
            throw new BusinessException("状态值不合法，应为0或1");
        }
        
        // 创建更新对象
        News updateNews = new News();
        updateNews.setId(id);
        updateNews.setStatus(status);
        updateNews.setUpdatedAt(LocalDateTime.now());
        
        return newsMapper.updateById(updateNews) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNewsTopStatus(Integer id, Boolean isTop) {
        // 检查新闻是否存在
        News news = newsMapper.selectById(id);
        if (news == null) {
            throw new BusinessException("新闻不存在");
        }
        
        // 创建更新对象
        News updateNews = new News();
        updateNews.setId(id);
        updateNews.setTop(isTop);
        updateNews.setUpdatedAt(LocalDateTime.now());
        
        return newsMapper.updateById(updateNews) > 0;
    }
} 