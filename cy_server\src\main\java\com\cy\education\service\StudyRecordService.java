package com.cy.education.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cy.education.model.entity.StudyRecord;
import com.cy.education.model.params.StudyRecordQueryParams;
import com.cy.education.model.vo.StudyRecordVO;
import com.cy.education.model.vo.StudyStatisticsVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 学习记录服务接口
 */
public interface StudyRecordService extends IService<StudyRecord> {
    
    /**
     * 分页查询学习记录列表
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<StudyRecordVO> getStudyRecordList(StudyRecordQueryParams params);
    
    /**
     * 获取用户的学习记录
     * @param userId 用户ID
     * @param courseId 课程ID（可选）
     * @return 学习记录列表
     */
    List<StudyRecordVO> getUserStudyRecords(Integer userId, Integer courseId);
    
    /**
     * 获取课程的学习记录
     * @param courseId 课程ID
     * @return 学习记录列表
     */
    List<StudyRecordVO> getCourseStudyRecords(Integer courseId);
    
    /**
     * 获取课时的学习记录
     * @param lessonId 课时ID
     * @return 学习记录列表
     */
    List<StudyRecordVO> getLessonStudyRecords(Integer lessonId);
    
    /**
     * 创建或更新学习记录
     * @param studyRecord 学习记录
     * @return 保存后的学习记录
     */
    StudyRecord saveStudyRecord(StudyRecord studyRecord);
    
    /**
     * 获取学习记录详情
     * @param id 学习记录ID
     * @return 学习记录详情
     */
    StudyRecordVO getStudyRecordById(Integer id);
    
    /**
     * 删除学习记录
     * @param id 学习记录ID
     * @return 是否删除成功
     */
    boolean deleteStudyRecord(Integer id);
    
    /**
     * 获取用户课程学习进度
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 学习进度信息
     */
    Map<String, Object> getUserCourseProgress(Integer userId, Integer courseId);
    
    /**
     * 记录资源访问日志
     * @param accessLog 访问日志信息
     * @return 是否记录成功
     */
    boolean logResourceAccess(Map<String, Object> accessLog);
    
    /**
     * 获取学员的课程学习记录
     * @param studentId 学员ID
     * @param courseId 课程ID
     * @return 学习记录
     */
    StudyRecordVO getStudentCourseRecord(Integer studentId, Integer courseId);
    
    /**
     * 获取学员的课时学习记录
     * @param studentId 学员ID
     * @param lessonId 课时ID
     * @return 学习记录
     */
    StudyRecordVO getStudentLessonRecord(Integer studentId, Integer lessonId);
    
    /**
     * 获取学员的学习统计数据
     * @param studentId 学员ID
     * @param courseId 课程ID（可选）
     * @return 学习统计数据
     */
    StudyStatisticsVO getStudyStatistics(Integer studentId, Integer courseId);
    
    /**
     * 获取部门学习情况统计
     * @return 部门学习统计数据
     */
    List<Map<String, Object>> getDepartmentStatistics();
    
    /**
     * 获取活跃学员排行
     * @param limit 限制数量
     * @return 活跃学员列表
     */
    List<Map<String, Object>> getActiveStudents(Integer limit);

    /**
     * 导出学习记录
     * @param params 查询参数
     * @return Excel文件字节数组
     */
    byte[] exportStudyRecords(StudyRecordQueryParams params);

    /**
     * 导出学习记录（新版本）
     * @param params 导出参数
     * @param response HTTP响应
     */
    void exportStudyRecords(Map<String, Object> params, HttpServletResponse response) throws IOException;

    /**
     * 获取部门学习统计数据（用于分析页面）
     * @return 部门统计数据列表
     */
    List<Map<String, Object>> getDepartmentStudyStats();
}