package com.cy.education.service.impl;

import com.cy.education.model.params.PracticeStatisticsQueryParams;
import com.cy.education.model.vo.PracticeStatisticsVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.PracticeStatisticsMapper;
import com.cy.education.service.PracticeStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 练习统计Service实现类
 */
@Service
public class PracticeStatisticsServiceImpl implements PracticeStatisticsService {
    
    @Autowired
    private PracticeStatisticsMapper practiceStatisticsMapper;
    
    @Override
    public PageResponse<PracticeStatisticsVO> getPracticeStatistics(PracticeStatisticsQueryParams params) {
        List<PracticeStatisticsVO> list = practiceStatisticsMapper.selectPracticeStatistics(params);

        // 获取总数（这里简化处理，实际应该有单独的count查询）
        long total = list.size();

        return PageResponse.of(list, total, params.getPage(), params.getSize());
    }
    
    @Override
    public Map<String, Object> getPracticeStatisticsDetail(Integer userId, Integer bankId) {
        return practiceStatisticsMapper.selectPracticeStatisticsDetail(userId, bankId);
    }
    
    @Override
    public Map<String, Object> getBankPracticeOverview(Integer bankId) {
        return practiceStatisticsMapper.selectBankPracticeOverview(bankId);
    }
    
    @Override
    public Map<String, Object> getAllPracticeOverview() {
        return practiceStatisticsMapper.selectAllPracticeOverview();
    }
    
    @Override
    public List<Map<String, Object>> getStudentPracticeRecords(Integer userId, Integer bankId) {
        return practiceStatisticsMapper.selectStudentPracticeRecords(userId, bankId);
    }

    @Override
    public void exportPracticeStatistics(PracticeStatisticsQueryParams params) {
        // TODO: 实现导出功能
        throw new UnsupportedOperationException("导出功能待实现");
    }

    @Override
    public Map<String, Object> getPracticeStatisticsChartData(PracticeStatisticsQueryParams params) {
        // TODO: 实现图表数据功能
        Map<String, Object> result = new HashMap<>();
        result.put("chartData", "图表数据待实现");
        return result;
    }
}