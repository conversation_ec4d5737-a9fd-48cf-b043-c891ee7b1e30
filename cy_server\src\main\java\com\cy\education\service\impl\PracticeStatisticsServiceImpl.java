package com.cy.education.service.impl;

import com.cy.education.model.params.PracticeStatisticsQueryParams;
import com.cy.education.model.vo.PracticeStatisticsVO;
import com.cy.education.repository.PracticeStatisticsMapper;
import com.cy.education.service.PracticeStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 练习统计Service实现类
 */
@Service
public class PracticeStatisticsServiceImpl implements PracticeStatisticsService {
    
    @Autowired
    private PracticeStatisticsMapper practiceStatisticsMapper;
    
    @Override
    public List<PracticeStatisticsVO> getPracticeStatistics(PracticeStatisticsQueryParams params) {
        return practiceStatisticsMapper.selectPracticeStatistics(params);
    }
    
    @Override
    public Map<String, Object> getPracticeStatisticsDetail(Integer userId, Integer bankId) {
        return practiceStatisticsMapper.selectPracticeStatisticsDetail(userId, bankId);
    }
    
    @Override
    public Map<String, Object> getBankPracticeOverview(Integer bankId) {
        return practiceStatisticsMapper.selectBankPracticeOverview(bankId);
    }
    
    @Override
    public Map<String, Object> getAllPracticeOverview() {
        return practiceStatisticsMapper.selectAllPracticeOverview();
    }
    
    @Override
    public List<Map<String, Object>> getStudentPracticeRecords(Integer userId, Integer bankId) {
        return practiceStatisticsMapper.selectStudentPracticeRecords(userId, bankId);
    }
} 