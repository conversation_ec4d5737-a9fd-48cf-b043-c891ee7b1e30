package com.cy.education.service.impl;

import com.alibaba.excel.EasyExcel;
import com.cy.education.model.params.PracticeStatisticsQueryParams;
import com.cy.education.model.vo.PracticeStatisticsVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.PracticeStatisticsMapper;
import com.cy.education.service.PracticeStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 练习统计Service实现类
 */
@Service
public class PracticeStatisticsServiceImpl implements PracticeStatisticsService {
    
    @Autowired
    private PracticeStatisticsMapper practiceStatisticsMapper;
    
    @Override
    public PageResponse<PracticeStatisticsVO> getPracticeStatistics(PracticeStatisticsQueryParams params) {
        List<PracticeStatisticsVO> list = practiceStatisticsMapper.selectPracticeStatistics(params);

        // 获取总数（这里简化处理，实际应该有单独的count查询）
        long total = list.size();

        return PageResponse.of(list, total, params.getPage(), params.getSize());
    }
    
    @Override
    public Map<String, Object> getPracticeStatisticsDetail(Integer userId, Integer bankId) {
        return practiceStatisticsMapper.selectPracticeStatisticsDetail(userId, bankId);
    }
    
    @Override
    public Map<String, Object> getBankPracticeOverview(Integer bankId) {
        return practiceStatisticsMapper.selectBankPracticeOverview(bankId);
    }
    
    @Override
    public Map<String, Object> getAllPracticeOverview() {
        return practiceStatisticsMapper.selectAllPracticeOverview();
    }
    
    @Override
    public List<Map<String, Object>> getStudentPracticeRecords(Integer userId, Integer bankId) {
        return practiceStatisticsMapper.selectStudentPracticeRecords(userId, bankId);
    }

    @Override
    public void exportPracticeStatistics(PracticeStatisticsQueryParams params) {
        try {
            // 获取HttpServletResponse
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                throw new RuntimeException("无法获取HttpServletResponse");
            }
            HttpServletResponse response = attributes.getResponse();
            if (response == null) {
                throw new RuntimeException("HttpServletResponse为空");
            }

            // 查询所有数据（不分页）
            params.setPage(null);
            params.setSize(null);
            List<PracticeStatisticsVO> allData = practiceStatisticsMapper.selectPracticeStatistics(params);

            // 准备导出数据
            List<List<Object>> exportData = new ArrayList<>();
            for (PracticeStatisticsVO vo : allData) {
                List<Object> row = new ArrayList<>();
                row.add(vo.getStudentName() != null ? vo.getStudentName() : "");
                row.add(vo.getStudentNumber() != null ? vo.getStudentNumber() : "");
                row.add(vo.getDepartmentName() != null ? vo.getDepartmentName() : "");
                row.add(vo.getBankName() != null ? vo.getBankName() : "");
                row.add(vo.getTotalQuestions() != null ? vo.getTotalQuestions() : 0);
                row.add(vo.getAnsweredQuestions() != null ? vo.getAnsweredQuestions() : 0);
                row.add(vo.getCorrectCount() != null ? vo.getCorrectCount() : 0);
                row.add(vo.getWrongCount() != null ? vo.getWrongCount() : 0);
                row.add(vo.getAccuracyRate() != null ? String.format("%.1f%%", vo.getAccuracyRate().multiply(BigDecimal.valueOf(100)).doubleValue()) : "0.0%");
                row.add(vo.getProgressRate() != null ? String.format("%.1f%%", vo.getProgressRate()) : "0.0%");
                row.add(vo.getPracticeCount() != null ? vo.getPracticeCount() : 0);
                row.add(vo.getTotalPracticeMinutes() != null ? vo.getTotalPracticeMinutes() : 0);
                row.add(vo.getAvgPracticeMinutes() != null ? String.format("%.1f", vo.getAvgPracticeMinutes()) : "0.0");
                row.add(vo.getWrongQuestionCount() != null ? vo.getWrongQuestionCount() : 0);
                row.add(vo.getLastPracticeTime() != null ? vo.getLastPracticeTime().toString() : "");
                exportData.add(row);
            }

            // 创建表头
            List<List<String>> headers = createExportHeaders();

            // 设置响应头
            String fileName = URLEncoder.encode("练习统计_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx", StandardCharsets.UTF_8.toString());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream())
                    .head(headers)
                    .sheet("练习统计")
                    .doWrite(exportData);

        } catch (Exception e) {
            throw new RuntimeException("导出练习统计失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建导出表头
     */
    private List<List<String>> createExportHeaders() {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Arrays.asList("学员姓名"));
        headers.add(Arrays.asList("工号"));
        headers.add(Arrays.asList("部门"));
        headers.add(Arrays.asList("题库"));
        headers.add(Arrays.asList("总题数"));
        headers.add(Arrays.asList("已答题数"));
        headers.add(Arrays.asList("正确数"));
        headers.add(Arrays.asList("错误数"));
        headers.add(Arrays.asList("正确率"));
        headers.add(Arrays.asList("练习进度"));
        headers.add(Arrays.asList("练习次数"));
        headers.add(Arrays.asList("总时长(分钟)"));
        headers.add(Arrays.asList("平均时长(分钟)"));
        headers.add(Arrays.asList("错题数"));
        headers.add(Arrays.asList("最后练习时间"));
        return headers;
    }

    @Override
    public Map<String, Object> getPracticeStatisticsChartData(PracticeStatisticsQueryParams params) {
        // TODO: 实现图表数据功能
        Map<String, Object> result = new HashMap<>();
        result.put("chartData", "图表数据待实现");
        return result;
    }
}