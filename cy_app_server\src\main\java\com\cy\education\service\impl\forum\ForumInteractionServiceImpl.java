package com.cy.education.service.impl.forum;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.forum.ForumPost;
import com.cy.education.repository.ForumInteractionMapper;
import com.cy.education.service.forum.ForumInteractionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 论坛用户交互服务实现
 */
@Service
public class ForumInteractionServiceImpl implements ForumInteractionService {

    @Autowired
    private ForumInteractionMapper forumInteractionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean likePost(Integer postId, Integer userId) {
        // 检查是否已经点赞
        if (isPostLiked(postId, userId)) {
            throw new BusinessException("您已经点赞过这个帖子了");
        }

        // 插入点赞记录
        boolean result = forumInteractionMapper.insertPostLike(postId, userId) > 0;

        // 更新帖子点赞数
        if (result) {
            forumInteractionMapper.incrementPostLikeCount(postId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unlikePost(Integer postId, Integer userId) {
        // 检查是否已经点赞
        if (!isPostLiked(postId, userId)) {
            throw new BusinessException("您还没有点赞这个帖子");
        }

        // 删除点赞记录
        boolean result = forumInteractionMapper.deletePostLike(postId, userId) > 0;

        // 更新帖子点赞数
        if (result) {
            forumInteractionMapper.decrementPostLikeCount(postId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean collectPost(Integer postId, Integer userId) {
        // 检查是否已经收藏
        if (isPostCollected(postId, userId)) {
            throw new BusinessException("您已经收藏过这个帖子了");
        }
        // 插入收藏记录
        boolean result = forumInteractionMapper.insertPostCollect(postId, userId) > 0;
        // 更新帖子收藏数
        if (result) {
            forumInteractionMapper.incrementPostCollectCount(postId);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean uncollectPost(Integer postId, Integer userId) {
        // 检查是否已经收藏
        if (!isPostCollected(postId, userId)) {
            throw new BusinessException("您还没有收藏这个帖子");
        }

        // 删除收藏记录
        boolean result = forumInteractionMapper.deletePostCollect(postId, userId) > 0;

        // 更新帖子收藏数
        if (result) {
            forumInteractionMapper.decrementPostCollectCount(postId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean followUser(Integer targetUserId, Integer userId) {
        // 不能关注自己
        if (targetUserId.equals(userId)) {
            throw new BusinessException("不能关注自己");
        }

        // 检查是否已经关注
        if (isUserFollowed(targetUserId, userId)) {
            throw new BusinessException("您已经关注过这个用户了");
        }

        // 插入关注记录
        return forumInteractionMapper.insertUserFollow(targetUserId, userId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unfollowUser(Integer targetUserId, Integer userId) {
        // 检查是否已经关注
        if (!isUserFollowed(targetUserId, userId)) {
            throw new BusinessException("您还没有关注这个用户");
        }

        // 删除关注记录
        return forumInteractionMapper.deleteUserFollow(targetUserId, userId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean likeComment(Integer commentId, Integer userId) {
        // 检查是否已经点赞
        if (isCommentLiked(commentId, userId)) {
            throw new BusinessException("您已经点赞过这个评论了");
        }

        // 插入点赞记录
        boolean result = forumInteractionMapper.insertCommentLike(commentId, userId) > 0;

        // 更新评论点赞数
        if (result) {
            forumInteractionMapper.incrementCommentLikeCount(commentId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unlikeComment(Integer commentId, Integer userId) {
        // 检查是否已经点赞
        if (!isCommentLiked(commentId, userId)) {
            throw new BusinessException("您还没有点赞这个评论");
        }

        // 删除点赞记录
        boolean result = forumInteractionMapper.deleteCommentLike(commentId, userId) > 0;

        // 更新评论点赞数
        if (result) {
            forumInteractionMapper.decrementCommentLikeCount(commentId);
        }

        return result;
    }

    @Override
    public boolean isPostLiked(Integer postId, Integer userId) {
        return forumInteractionMapper.checkPostLike(postId, userId) > 0;
    }

    @Override
    public boolean isPostCollected(Integer postId, Integer userId) {
        return forumInteractionMapper.checkPostCollect(postId, userId) > 0;
    }

    @Override
    public boolean isUserFollowed(Integer targetUserId, Integer userId) {
        return forumInteractionMapper.checkUserFollow(targetUserId, userId) > 0;
    }

    @Override
    public boolean isCommentLiked(Integer commentId, Integer userId) {
        return forumInteractionMapper.checkCommentLike(commentId, userId) > 0;
    }

}
