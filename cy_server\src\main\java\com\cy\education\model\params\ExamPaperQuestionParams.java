package com.cy.education.model.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 试卷题目参数类
 */
@Data
@ApiModel(description = "试卷题目参数")
public class ExamPaperQuestionParams {

    @ApiModelProperty(value = "题目ID", required = true)
    @NotNull(message = "题目ID不能为空")
    private Integer questionId;

    @ApiModelProperty(value = "分值", required = true)
    @NotNull(message = "分值不能为空")
    @Min(value = 1, message = "分值必须大于0")
    private Integer score;

    @ApiModelProperty(value = "排序号")
    private Integer questionOrder;
} 