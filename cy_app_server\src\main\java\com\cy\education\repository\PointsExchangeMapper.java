package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.points.PointsExchange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 积分兑换记录数据访问层
 */
@Mapper
public interface PointsExchangeMapper extends BaseMapper<PointsExchange> {

    /**
     * 分页查询兑换记录（带用户名、部门名和商品名）
     *
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param productId 商品ID（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT e.*, s.name as user_name, d.name as department_name, p.name as product_name, p.image as goods_image " +
            "FROM points_exchange e " +
            "LEFT JOIN students s ON e.user_id = s.id " +
            "LEFT JOIN departments d ON s.department_id = d.id " +
            "LEFT JOIN points_product p ON e.product_id = p.id " +
            "WHERE 1=1 " +
            "<if test='userId != null'>AND e.user_id = #{userId} </if>" +
            "<if test='productId != null'>AND e.product_id = #{productId} </if>" +
            "<if test='status != null and status != \"\"'>AND e.status = #{status} </if>" +
            "ORDER BY e.exchange_time DESC" +
            "</script>")
    IPage<PointsExchange> pageWithDetails(Page<PointsExchange> page,
                                        @Param("userId") Integer userId,
                                        @Param("productId") Integer productId,
                                        @Param("status") String status);

    /**
     * 获取用户兑换某商品的次数
     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 兑换次数
     */
    @Select("SELECT COUNT(*) FROM points_exchange WHERE user_id = #{userId} AND product_id = #{productId}")
    int countUserProductExchanges(@Param("userId") Integer userId, @Param("productId") Integer productId);
}
