package com.cy.education.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 题库视图对象
 */
@Data
public class ExamBankVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题库ID
     */
    private Integer id;

    /**
     * 题库名称
     */
    private String name;

    /**
     * 题库描述
     */
    private String description;

    /**
     * 适用范围
     */
    private String scope;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人ID
     */
    private String createdBy;
} 