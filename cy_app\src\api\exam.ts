import {get, post} from '@/utils/request'

// =========================== 通用接口 ===========================
export interface PageQuery {
    pageNum?: number;
    pageSize?: number;
    status?: number;
    keyword?: string;
}

export interface PageResponse<T> {
    list: T[];
    total: number;
    pageNum: number;
    pageSize: number;
}

// =========================== 考试相关接口 ===========================

// 考试查询参数
export interface ExamQueryParams extends PageQuery {
    status?: number; // 考试状态筛选
    startTime?: string;
    endTime?: string;
    keyword?: string;
}

// 考试统计信息
export interface ExamStats {
    // 基础统计信息
    examTitle?: string;
    examDuration?: number;
    passingScore?: number;
    maxAttempts?: number;

    // 参与情况统计
    totalParticipants: number;
    completedCount: number;
    ongoingCount: number;

    // 分数统计
    averageScore: number;
    passRate: number;
    highestScore: number;
    lowestScore: number;
    medianScore: number;

    // 部门统计
    departmentStats?: Array<{
        departmentId: number;
        departmentName: string;
        participantCount: number;
        averageScore: number;
        passRate: number;
        highestScore: number;
        lowestScore: number;
        averageDuration: number;
    }>;

    // 分数分布统计
    scoreDistribution?: {
        excellent: number; // 优秀(90-100)
        good: number;      // 良好(80-89)
        moderate: number;  // 中等(70-79)
        pass: number;      // 及格(60-69)
        fail: number;      // 不及格(0-59)
    };

    // 完成时间分布统计
    durationDistribution?: {
        veryFast: number;  // 非常快(小于总时长的25%)
        fast: number;      // 较快(总时长的25%-50%)
        normal: number;    // 一般(总时长的50%-75%)
        slow: number;      // 较慢(总时长的75%-100%)
        verySlow: number;  // 非常慢(超过总时长)
    };

    // 考试次数统计
    attemptStats?: {
        averageAttempts: number;
        maxAttempts: number;
        minAttempts: number;
        totalUsers: number;
    };

    // 时间趋势统计
    timeTrendStats?: {
        dailyCompletions: Record<string, number>;
        dailyPasses: Record<string, number>;
    };
}

// 答题VO
export interface ExamAnswerVO {
    id: number;
    recordId: number;
    questionId: number;
    questionContent: string;
    questionType: string;
    options: string;
    correctAnswer: string;
    explanation: string;
    totalScore: number;
    answer: string;
    isCorrect: boolean;
    score: number;
    comment: string;
    createdAt: string;
    updatedAt: string;
}

// 考试记录VO
export interface ExamRecordVO {
    id: number;
    examId: number;
    examTitle: string;
    userId: number;
    departmentId: number;
    score: number;
    totalScore: number;
    isPassed: boolean;
    startTime: string;
    endTime: string;
    duration: number;
    status: number; // (0-未开始,1-进行中,2-已完成,3-超时)
    createdAt: string;
    updatedAt: string;
}

// 试卷题目VO
export interface ExamPaperQuestionVO {
    id: number;
    paperId: number;
    questionId: number;
    question: ExamQuestionVO;
    score: number;
    questionOrder: number;
}

// 题目VO
export interface ExamQuestionVO {
    id: number;
    type: string; //single-单选题,multiple-多选题,judgment-判断题,fill-填空题,essay-简答题
    title: string;
    options?: string[];
    correctAnswer: string;
    explanation?: string;
}

// 试卷VO
export interface ExamPaperVO {
    id: number;
    title: string;
    description: string;
    totalScore: number;
    passingScore: number;
    duration: number;
    isPublished: boolean;
    questions: ExamPaperQuestionVO[];
    questionCount: number;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
}

// 考试VO
export interface ExamVO {
    id: number;
    title: string;
    description: string;
    paperId: number;
    paper: ExamPaperVO;
    startTime: string;
    endTime: string;
    duration: number;
    passingScore: number;
    maxAttempts: number;
    isPublished: boolean;
    status: number; // 0-草稿,1-未开始,2-进行中,3-已结束
    departmentIds: number[];
    departments: string[];
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    currentUserRecord?: ExamRecordVO;
}

export interface QuestionDetail {
    questionId: number
    questionNumber: number
    type: string
    typeName: string
    question: string
    options: { key: string, text: string }[]
    userAnswer: string | null
    correctAnswer: string
    isCorrect: boolean | null
    explanation?: string
}

export interface ExamResult {
    totalCount: number
    correctCount: number
    wrongCount: number
    unansweredCount: number
    usedTime: number
    questions: QuestionDetail[]
}

// =========================== API 函数 ===========================

// 考试相关API
export const getExamList = (params: ExamQueryParams) =>
    get<PageResponse<ExamVO>>('/exam/list', params)

// 不包含问题的答案
export const getExamDetail = (id: number) =>
    get<ExamVO>(`/exam/detail/${id}`)

// 包含问题的答案
export const getExamFullDetail = (id: number) =>
    get<ExamVO>(`/exam/full-detail/${id}`)

export const getUserExamStats = () =>
    get<ExamStats>(`/exam-record/user/stats`,)

export const getExamStats = (examId: number) =>
    get<ExamStats>(`/exam-record/stats`, {examId})

export const updateExamStatus = (id: number, status: number) =>
    post(`/exam/update-status/${id}`, {status})

// 考试记录相关API
export const getExamRecords = (params: PageQuery) =>
    get<PageResponse<ExamRecordVO>>('/exam-record/list', params)

// 开始考试
export const startExam = (examId: number) =>
    post(`/exam-record/start/${examId}`);

// 提交考试，参数为recordId和answers数组
export const submitExam = (recordId: number, answers: any) =>
    post(`/exam-record/submit/${recordId}`, {answers: JSON.stringify(answers)});

export const getExamResult = (recordId: number) =>
    get<any>(`/exam-record/result/${recordId}`)
