package com.cy.education.service;

import com.cy.education.model.params.ExamParams;
import com.cy.education.model.params.ExamQueryParams;
import com.cy.education.model.vo.ExamVO;
import com.cy.education.model.vo.PageResponse;

/**
 * 考试服务接口
 */
public interface ExamService {

    /**
     * 分页查询考试列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResponse<ExamVO> listExams(ExamQueryParams params);

    /**
     * 获取考试详情
     *
     * @param id 考试ID
     * @return 考试详情
     */
    ExamVO getExamDetail(Integer id);

    /**
     * 创建考试
     *
     * @param params 考试参数
     * @param createdBy 创建人ID
     * @return 考试ID
     */
    Integer createExam(ExamParams params, String createdBy);

    /**
     * 更新考试
     *
     * @param id 考试ID
     * @param params 考试参数
     * @return 是否成功
     */
    boolean updateExam(Integer id, ExamParams params);

    /**
     * 删除考试
     *
     * @param id 考试ID
     * @return 是否成功
     */
    boolean deleteExam(Integer id);

    /**
     * 发布/取消发布考试
     *
     * @param id 考试ID
     * @param isPublished 是否发布
     * @return 是否成功
     */
    boolean publishExam(Integer id, Boolean isPublished);

    /**
     * 更新考试状态
     *
     * @param id 考试ID
     * @param status 状态(0-草稿,1-未开始,2-进行中,3-已结束)
     * @return 是否成功
     */
    boolean updateExamStatus(Integer id, Integer status);
} 