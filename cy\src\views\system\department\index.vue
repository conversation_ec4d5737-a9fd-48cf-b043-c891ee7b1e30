<template>
  <div class="system-department">
    <div class="page-container">
      <div class="page-toolbar">
        <div class="toolbar-left">
          <h3>部门管理</h3>
        </div>
        <div class="toolbar-right">
          <el-button type="primary" :icon="Plus" @click="handleAddRootDept">新增部门</el-button>
          <el-button :icon="Refresh" @click="refreshDepartments">刷新</el-button>
        </div>
      </div>

      <div class="department-content">
        <!-- 左侧部门树 -->
        <dept-tree
          :department-tree="departmentTree"
          :tree-props="treeProps"
          @node-click="handleNodeClick"
          @node-drop="handleDrop"
          @add-child="handleAddChild"
          @edit="handleEdit"
          @delete="handleDelete"
          v-loading="loading"
        />

        <!-- 右侧部门详情 -->
        <dept-detail
          :department="currentDepartment"
          :department-tree="departmentTree"
          @edit="handleEdit"
        />
      </div>
    </div>

    <!-- 新增/编辑部门弹窗 -->
    <dept-form-dialog
      v-model="dialogVisible"
      :is-edit="isEdit"
      :department-tree="departmentTree"
      :department-data="deptForm"
      :tree-props="treeProps"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, inject } from 'vue'
import {
  Plus,
  Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DeptTree from './components/DeptTree.vue'
import DeptDetail from './components/DeptDetail.vue'
import DeptFormDialog from './components/DeptFormDialog.vue'
import { 
  getDepartmentTree, getDepartmentById, addDepartment, 
  updateDepartment, deleteDepartment, updateDepartmentSort,
  type Department 
} from '@/api/department'

// 使用provide/inject共享部门数据，以便在多个组件中复用
const deptCache = inject<{
  departmentTree: Department[],
  loading: boolean,
  refresh: () => Promise<void>
}>('departmentCache')

const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentDepartment = ref<Department | null>(null)

// 树形配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 部门表单
const deptForm = reactive<Partial<Department>>({
  id: undefined,
  name: '',
  parentId: null,
  leader: '',
  sort: 0,
  status: 1,
  remark: ''
})

// 部门树数据
const departmentTree = ref<Department[]>([])

let nextDeptId = 10 // 模拟自增ID，实际应该由后端生成

// 处理节点点击
const handleNodeClick = (data: Department) => {
  currentDepartment.value = data
}

// 处理拖拽排序
const handleDrop = async (draggingNode: any, dropNode: any, dropType: string) => {
  try {
    // 构造排序数据
    const sortData = [{
      id: draggingNode.data.id,
      parentId: dropType === 'inner' ? dropNode.data.id : dropNode.data.parentId,
      sort: draggingNode.data.sort
    }]
    
    await updateDepartmentSort(sortData)
    ElMessage.success(`${draggingNode.label} 已移动到 ${dropType === 'inner' ? dropNode.label + '内部' : dropNode.label + (dropType === 'before' ? '之前' : '之后')}`)
  } catch (error) {
    console.error('更新部门排序失败:', error)
    ElMessage.error('更新部门排序失败')
    // 重新加载以恢复原始状态
    await loadDepartmentTree()
  }
}

// 加载部门树
const loadDepartmentTree = async () => {
  loading.value = true
  try {
    // 如果有缓存数据，直接使用
    if (deptCache && deptCache.departmentTree.length > 0) {
      departmentTree.value = deptCache.departmentTree
    } else {
      const res = await getDepartmentTree()
      departmentTree.value = res
    }
  } catch (error) {
    console.error('加载部门树失败:', error)
    ElMessage.error('加载部门树失败')
  } finally {
    loading.value = false
  }
}

// 新增根部门
const handleAddRootDept = () => {
  isEdit.value = false
  Object.assign(deptForm, {
    id: undefined,
    name: '',
    parentId: null,
    leader: '',
    sort: 999,
    status: 1,
    remark: ''
  })
  dialogVisible.value = true
}

// 新增子部门
const handleAddChild = (data: Department) => {
  isEdit.value = false
  Object.assign(deptForm, {
    id: undefined,
    name: '',
    parentId: data.id,
    leader: '',
    sort: 999,
    status: 1,
    remark: ''
  })
  dialogVisible.value = true
}

// 编辑部门
const handleEdit = async (data: Department) => {
  isEdit.value = true
  
  try {
    // 获取最新的部门数据
    const departmentDetail = await getDepartmentById(data.id)
    Object.assign(deptForm, departmentDetail)
  } catch (error) {
    console.error('获取部门详情失败:', error)
    ElMessage.error('获取部门详情失败')
    // 使用传入的数据作为备选
    Object.assign(deptForm, {
      id: data.id,
      name: data.name,
      parentId: data.parentId,
      leader: data.leader || '',
      sort: data.sort,
      status: data.status,
      remark: data.remark || ''
    })
  }
  
  dialogVisible.value = true
}

// 删除部门
const handleDelete = async (data: Department, node: any) => {
  // 检查是否有子部门
  if (data.children && data.children.length > 0) {
    ElMessage.warning('该部门下有子部门，不能直接删除')
    return
  }
  
  ElMessageBox.confirm('确定要删除该部门吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteDepartment(data.id)
      
      // 如果是当前选中的部门，清除选中状态
      if (currentDepartment.value?.id === data.id) {
        currentDepartment.value = null
      }
      
      ElMessage.success('删除成功')
      
      // 重新加载部门树
      await loadDepartmentTree()
      
      // 如果有缓存更新函数，调用它以更新全局缓存
      if (deptCache && typeof deptCache.refresh === 'function') {
        await deptCache.refresh()
      }
    } catch (error) {
      console.error('删除部门失败:', error)
      ElMessage.error('删除部门失败')
    }
  })
}

// 提交表单
const handleSubmit = async (formData: Partial<Department>) => {
  try {
    if (isEdit.value) {
      // 编辑部门
      await updateDepartment(formData)
      ElMessage.success('编辑成功')
    } else {
      // 新增部门
      await addDepartment(formData)
      ElMessage.success('新增成功')
    }
    
    // 重新加载部门树
    await loadDepartmentTree()
    
    // 如果有缓存更新函数，调用它以更新全局缓存
    if (deptCache && typeof deptCache.refresh === 'function') {
      await deptCache.refresh()
    }
  } catch (error) {
    console.error('保存部门失败:', error)
    ElMessage.error('保存部门失败')
  }
}

// 刷新部门
const refreshDepartments = async () => {
  await loadDepartmentTree()
  
  // 如果有缓存更新函数，调用它以更新全局缓存
  if (deptCache && typeof deptCache.refresh === 'function') {
    await deptCache.refresh()
  }
  
  ElMessage.success('部门数据已刷新')
}

onMounted(async () => {
  await loadDepartmentTree()
})
</script>

<style scoped>
.department-content {
  display: flex;
  gap: 24px;
  margin-top: 20px;
}

.system-department {
  padding: 20px;
}

.page-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style> 