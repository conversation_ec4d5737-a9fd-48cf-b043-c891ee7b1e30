package com.cy.education.service.impl.home;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.home.News;
import com.cy.education.model.params.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.NewsMapper;
import com.cy.education.service.home.NewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class NewsServiceImpl implements NewsService {

    @Autowired
    private NewsMapper newsMapper;

    @Override
    public PageResponse<News> listNews(ContentQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<News> queryWrapper = new LambdaQueryWrapper<>();

        // 默认只查询已发布的新闻
        queryWrapper.eq(News::getStatus, 1);

        // 置顶条件
        if (param.getIsTop() != null) {
            queryWrapper.eq(News::getTop, param.getIsTop());
        }

        // 关键词搜索（标题和内容）
        if (!StringUtils.isEmpty(param.getKeyword())) {
            queryWrapper.and(wrapper ->
                    wrapper.like(News::getTitle, param.getKeyword())
                            .or()
                            .like(News::getContent, param.getKeyword())
            );
        }

        // 排序（置顶优先，然后是发布时间）
        queryWrapper.orderByDesc(News::getTop, News::getPublishTime);

        // 分页查询
        Page<News> page = new Page<>(param.getPageNum(), param.getPageSize());
        Page<News> resultPage = newsMapper.selectPage(page, queryWrapper);

        // 转换为自定义分页响应对象
        return PageResponse.of(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    }

    @Override
    public News getNewsById(Integer id) {
        News news = newsMapper.selectById(id);
        if (news == null) {
            throw new BusinessException("新闻不存在");
        }
        return news;
    }

    @Override
    public boolean incrementViewCount(Integer id) {
        return newsMapper.incrementViewCount(id) > 0;
    }
}
