package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.ExamRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 考试记录Mapper接口
 */
@Mapper
public interface ExamRecordMapper extends BaseMapper<ExamRecord> {

    /**
     * 分页查询考试记录列表
     *
     * @param page 分页参数
     * @param examId 考试ID
     * @param departmentId 部门ID
     * @param keyword 关键字(用户名)
     * @param status 状态
     * @param isPassed 是否通过
     * @param sortBy 排序字段
     * @param sortOrder 排序方式
     * @return 分页结果
     */
    IPage<ExamRecord> selectExamRecordPage(
            Page<ExamRecord> page,
            @Param("examId") Integer examId,
            @Param("departmentId") Integer departmentId,
            @Param("keyword") String keyword,
            @Param("status") Integer status,
            @Param("isPassed") Boolean isPassed,
            @Param("sortBy") String sortBy,
            @Param("sortOrder") String sortOrder);
} 