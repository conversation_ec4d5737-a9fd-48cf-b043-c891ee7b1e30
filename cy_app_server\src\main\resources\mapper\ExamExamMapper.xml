<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.ExamExamMapper">
    <!-- 分页查询考试列表 -->
    <select id="selectExamPage" resultType="com.cy.education.model.entity.exam.ExamExam">
        SELECT e.*
        FROM exam_exam e
        <if test="departmentId != null">
            INNER JOIN exam_exam_department ed ON e.id = ed.exam_id AND ed.department_id = #{departmentId}
        </if>
        <where>
            e.deleted = 0
            <if test="keyword != null and keyword != ''">
                AND (e.title LIKE CONCAT('%', #{keyword}, '%') OR e.description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="paperId != null">
                AND e.paper_id = #{paperId}
            </if>
            AND e.status != 0
            <if test="status != null">
                AND e.status = #{status}
            </if>
            AND e.is_published = 1
        </where>
        ORDER BY e.created_at DESC
    </select>

    <select id="countExamsByDepartmentId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM exam_exam e
        <if test="departmentId != null">
            INNER JOIN exam_exam_department ed ON e.id = ed.exam_id AND ed.department_id = #{departmentId}
        </if>
        <where>
            e.deleted = 0
            AND e.status != 0
            AND e.is_published = 1
        </where>
    </select>
</mapper>
