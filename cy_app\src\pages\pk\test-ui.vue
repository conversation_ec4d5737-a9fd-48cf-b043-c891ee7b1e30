<template>
  <view class="test-page">
    <up-navbar title="PK UI测试" :border="false">
      <template #left>
        <up-icon name="arrow-left" size="20" @click="goBack"></up-icon>
      </template>
    </up-navbar>

    <view class="test-content">
      <!-- 选择器测试 -->
      <view class="test-section">
        <text class="section-title">选择器测试</text>
        
        <view class="form-item">
          <text class="form-label">题库选择</text>
          <up-picker 
            :show="showBankPicker" 
            :columns="bankColumns" 
            @confirm="onBankConfirm"
            @cancel="showBankPicker = false"
            @close="showBankPicker = false">
          </up-picker>
          <view class="picker-display" @click="showBankPicker = true">
            {{ selectedBank || '请选择题库' }}
            <up-icon name="arrow-down" size="16" color="#c7c7cc"></up-icon>
          </view>
        </view>

        <view class="form-item">
          <text class="form-label">题目数量</text>
          <up-picker 
            :show="showCountPicker" 
            :columns="countColumns" 
            @confirm="onCountConfirm"
            @cancel="showCountPicker = false"
            @close="showCountPicker = false">
          </up-picker>
          <view class="picker-display" @click="showCountPicker = true">
            {{ selectedCount }}题
            <up-icon name="arrow-down" size="16" color="#c7c7cc"></up-icon>
          </view>
        </view>
      </view>

      <!-- 弹窗测试 -->
      <view class="test-section">
        <text class="section-title">弹窗测试</text>
        
        <up-button text="测试快速匹配弹窗" @click="showTestDialog = true" type="primary"></up-button>
        
        <up-popup :show="showTestDialog" mode="center" border-radius="16">
          <view class="match-dialog">
            <view class="dialog-title">测试弹窗</view>
            <view class="dialog-content">
              <view class="form-item">
                <text class="form-label">测试选择器</text>
                <up-picker 
                  :show="showTestPicker" 
                  :columns="testColumns" 
                  @confirm="onTestConfirm"
                  @cancel="showTestPicker = false"
                  @close="showTestPicker = false">
                </up-picker>
                <view class="picker-display" @click="showTestPicker = true">
                  {{ testValue || '请选择' }}
                  <up-icon name="arrow-down" size="16" color="#c7c7cc"></up-icon>
                </view>
              </view>
            </view>
            <view class="dialog-actions">
              <up-button text="取消" @click="showTestDialog = false"></up-button>
              <up-button type="primary" text="确定" @click="showTestDialog = false"></up-button>
            </view>
          </view>
        </up-popup>
      </view>

      <!-- 样式测试 -->
      <view class="test-section">
        <text class="section-title">样式测试</text>
        
        <view class="mode-cards">
          <view class="mode-card">
            <view class="mode-icon">
              <up-icon name="search" size="32" color="#fff"></up-icon>
            </view>
            <view class="mode-content">
              <text class="mode-title">快速匹配</text>
              <text class="mode-desc">系统自动匹配对手</text>
            </view>
          </view>
          <view class="mode-card">
            <view class="mode-icon">
              <up-icon name="home" size="32" color="#fff"></up-icon>
            </view>
            <view class="mode-content">
              <text class="mode-title">房间对战</text>
              <text class="mode-desc">创建或加入房间</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 测试结果 -->
      <view class="test-section">
        <text class="section-title">测试结果</text>
        <view class="result-box">
          <text class="result-text">选择的题库: {{ selectedBank }}</text>
          <text class="result-text">选择的数量: {{ selectedCount }}</text>
          <text class="result-text">测试值: {{ testValue }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 选择器状态
      showBankPicker: false,
      showCountPicker: false,
      showTestPicker: false,
      showTestDialog: false,
      
      // 选择的值
      selectedBank: '',
      selectedCount: 10,
      testValue: '',
      
      // 选择器数据
      bankColumns: [
        [
          { text: '数学题库', value: 1 },
          { text: '语文题库', value: 2 },
          { text: '英语题库', value: 3 },
          { text: '物理题库', value: 4 },
          { text: '化学题库', value: 5 }
        ]
      ],
      countColumns: [
        [
          { text: '5题', value: 5 },
          { text: '10题', value: 10 },
          { text: '15题', value: 15 },
          { text: '20题', value: 20 }
        ]
      ],
      testColumns: [
        [
          { text: '选项1', value: 'option1' },
          { text: '选项2', value: 'option2' },
          { text: '选项3', value: 'option3' }
        ]
      ]
    }
  },
  
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    onBankConfirm(e) {
      const index = e.value[0]
      this.selectedBank = this.bankColumns[0][index].text
      this.showBankPicker = false
    },
    
    onCountConfirm(e) {
      const index = e.value[0]
      this.selectedCount = this.countColumns[0][index].value
      this.showCountPicker = false
    },
    
    onTestConfirm(e) {
      const index = e.value[0]
      this.testValue = this.testColumns[0][index].text
      this.showTestPicker = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/pages/pk/index.scss';

.test-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.test-content {
  padding: 20px;
}

.test-section {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.result-box {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.result-text {
  display: block;
  font-size: 14px;
  color: #4a5568;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
