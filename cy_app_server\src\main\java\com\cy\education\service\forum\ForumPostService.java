package com.cy.education.service.forum;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.forum.ForumPost;
import com.cy.education.model.params.ForumPostQueryParam;

import java.util.List;

/**
 * 论坛帖子服务接口
 */
public interface ForumPostService {

    /**
     * 获取帖子列表（分页）
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<ForumPost> getPostPage(Integer loginUserId, Integer userId, ForumPostQueryParam param);

    /**
     * 获取用户收藏的帖子列表（分页）
     *
     * @param userId 用户ID
     * @param param  查询参数
     * @return 分页结果
     */
    IPage<ForumPost> getCollectedPostsPage(Integer userId, ForumPostQueryParam param);

    /**
     * 获取帖子详情
     *
     * @param id 帖子ID
     * @return 帖子信息
     */
    ForumPost getPostById(Integer id);

    /**
     * 增加帖子浏览量
     *
     * @param id 帖子ID
     * @return 是否成功
     */
    boolean incrementViewCount(Integer id);

    /**
     * 删除帖子（假删除）
     *
     * @param id 帖子ID
     * @return 是否成功
     */
    boolean deletePost(Integer id);

    /**
     * 创建帖子
     *
     * @param title      帖子标题
     * @param content    帖子内容
     * @param categoryId 分类ID
     * @param images     图片列表
     * @param authorId   作者ID
     * @return 创建的帖子信息
     */
    ForumPost createPost(String title, String content, Integer categoryId, List<String> images, Integer authorId);

}
