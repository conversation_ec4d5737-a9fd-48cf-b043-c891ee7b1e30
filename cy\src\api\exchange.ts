import { get, post, put } from '@/utils/request'

/**
 * 兑换记录接口数据类型
 */
export interface Exchange {
  id: string
  userId: string
  userName: string
  productId: string
  productName: string
  productImage: string
  points: number
  quantity: number
  totalPoints: number
  status: 'pending' | 'approved' | 'shipped' | 'delivered' | 'cancelled' | 'rejected'
  address: {
    name: string
    phone: string
    province: string
    city: string
    district: string
    address: string
    postalCode?: string
  }
  expressInfo?: {
    company: string
    trackingNumber: string
    shippedAt: string
  }
  remark?: string
  createdAt: string
  updatedAt: string
}

export interface ExchangeStatistics {
  totalExchanges: number
  pendingExchanges: number
  totalPointsUsed: number
  statusDistribution: {
    status: string
    count: number
  }[]
  productDistribution: {
    productId: string
    productName: string
    count: number
  }[]
  monthlyExchanges: {
    month: string
    count: number
    points: number
  }[]
}

export interface ExchangeQueryParams {
  page?: number
  limit?: number
  userId?: string
  userName?: string
  productId?: string
  productName?: string
  status?: string
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

/**
 * 获取兑换记录列表
 * @param params 查询参数
 */
export function getExchangeList(params?: ExchangeQueryParams) {
  return get<{ list: Exchange[]; total: number }>('/points/exchanges', params)
}

/**
 * 获取兑换记录详情
 * @param id 兑换记录ID
 */
export function getExchangeDetail(id: string) {
  return get<Exchange>(`/points/exchanges/${id}`)
}

/**
 * 审核兑换申请
 * @param id 兑换记录ID
 * @param approved 是否通过
 * @param remark 备注信息
 */
export function reviewExchange(id: string, approved: boolean, remark?: string) {
  return post<{ success: boolean }>(`/points/exchanges/${id}/status`, {
    status: approved ? 'approved' : 'rejected',
    remark
  })
}

/**
 * 更新物流信息
 * @param id 兑换记录ID
 * @param expressInfo 物流信息
 */
export function updateExchangeExpress(
  id: string,
  expressInfo: {
    company: string
    trackingNumber: string
  }
) {
  return put<{ success: boolean }>(`/points/exchanges/${id}/ship`, expressInfo)
}

/**
 * 确认收货
 * @param id 兑换记录ID
 */
export function confirmExchangeDelivery(id: string) {
  return post<{ success: boolean }>(`/points/exchanges/${id}/status`, { 
    status: 'delivered' 
  })
}

/**
 * 取消兑换
 * @param id 兑换记录ID
 * @param reason 取消原因
 */
export function cancelExchange(id: string, reason: string) {
  return post<{ success: boolean }>(`/points/exchanges/${id}/status`, { 
    status: 'cancelled', 
    remark: reason 
  })
}

/**
 * 批量更新兑换状态
 * @param ids 兑换记录ID数组
 * @param status 状态
 * @param remark 备注信息
 */
export function batchUpdateExchangeStatus(
  ids: string[],
  status: 'approved' | 'shipped' | 'rejected' | 'cancelled',
  remark?: string
) {
  // 如果是发货状态，需要调用批量发货接口
  if (status === 'shipped') {
    return post<{ success: boolean; failedCount: number }>('/points/exchanges/batch-ship', {
      shipments: ids.map(id => ({ id })),
      company: remark || '', // 这里把remark用作物流公司名称
      remark: ''
    });
  }
  
  // 其他状态调用批量状态更新接口
  return post<{ success: boolean; failedCount: number }>('/points/exchanges/batch-status', {
    ids,
    status,
    remark
  });
}

/**
 * 获取兑换统计数据
 */
export function getExchangeStatistics() {
  return get<ExchangeStatistics>('/points/exchanges/statistics')
}

/**
 * 导出兑换记录
 * @param params 查询参数
 */
export function exportExchanges(params?: ExchangeQueryParams) {
  return get<Blob>('/points/exchanges/export', params, {
    responseType: 'blob',
    showLoading: true
  })
}
