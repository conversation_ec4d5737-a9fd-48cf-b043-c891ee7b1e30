package com.cy.education.model.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("points_record")
public class PointsRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 学员ID
     */
    private Integer userId;

    /**
     * 积分变动：正数表示增加，负数表示减少
     */
    private Integer points;

    /**
     * 变动后余额
     */
    private Integer balance;

    /**
     * 积分类型：course_complete(课程完成)、sign_in(签到)、exchange(兑换)、
     * admin_adjust(管理员调整)、other(其他)
     */
    private String type;

    /**
     * 描述
     */
    private String description;

    /**
     * 关联ID：可能关联到课程、商品等
     */
    private Integer relatedId;

    /**
     * 关联类型：course(课程)、product(商品)、other(其他)
     */
    private String relatedType;

    /**
     * 操作员：可能是管理员操作的，也可能是系统自动的
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 用户名(非数据库字段)
     */
    @TableField(exist = false)
    private String userName;
}
