import { get } from '@/utils/request'

export interface GoodsItem {
  id: string
  name: string
  description: string
  image: string
  points: number
  stock: number
  category: string
}

export interface CategoryItem {
  id: string
  name: string
}

export interface GoodsListResponse {
  list: GoodsItem[]
  total: number
}

export const getGoodsList = (params: { pageNum: number; pageSize: number; category?: string }) =>
  get<GoodsListResponse>('/product/list', params)