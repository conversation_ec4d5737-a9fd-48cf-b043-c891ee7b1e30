package com.cy.education.model.entity.exam;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 试卷实体类
 */
@Data
@TableName("exam_paper")
public class ExamPaper implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 试卷ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 试卷标题
     */
    private String title;

    /**
     * 试卷描述
     */
    private String description;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 及格分数
     */
    private Integer passingScore;

    /**
     * 考试时长(分钟)
     */
    private Integer duration;

    /**
     * 是否发布(0-未发布,1-已发布)
     */
    private Boolean isPublished;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人ID
     */
    private String createdBy;

    /**
     * 是否删除(0-未删除，1-已删除)
     */
    @TableLogic
    private Integer deleted;
}
