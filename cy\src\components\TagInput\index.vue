<template>
  <div class="tag-input">
    <div class="tag-container">
      <!-- 显示已有标签 -->
      <el-tag
        v-for="(tag, index) in tags"
        :key="index"
        closable
        @close="removeTag(index)"
        class="tag-item"
      >
        {{ tag }}
      </el-tag>
      
      <!-- 输入框 -->
      <el-input
        v-if="inputVisible"
        ref="inputRef"
        v-model="inputValue"
        size="small"
        class="tag-input-field"
        @keyup.enter="handleInputConfirm"
        @blur="handleInputConfirm"
        placeholder="输入标签后按回车"
      />
      
      <!-- 添加按钮 -->
      <el-button
        v-else
        size="small"
        type="primary"
        plain
        @click="showInput"
        class="add-tag-btn"
      >
        <el-icon><Plus /></el-icon>
        添加标签
      </el-button>
    </div>
    
    <!-- 标签建议 -->
    <div v-if="suggestions.length > 0 && inputVisible" class="tag-suggestions">
      <div class="suggestion-title">建议标签：</div>
      <el-tag
        v-for="suggestion in suggestions"
        :key="suggestion"
        size="small"
        type="info"
        class="suggestion-tag"
        @click="addSuggestion(suggestion)"
      >
        {{ suggestion }}
      </el-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

interface Props {
  modelValue?: string[]
  suggestions?: string[]
  maxTags?: number
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', value: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  suggestions: () => [],
  maxTags: 10
})

const emit = defineEmits<Emits>()

const inputRef = ref()
const inputVisible = ref(false)
const inputValue = ref('')

const tags = computed({
  get: () => props.modelValue || [],
  set: (value: string[]) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

// 过滤掉已存在的建议标签
const suggestions = computed(() => {
  return props.suggestions.filter(suggestion => 
    !tags.value.includes(suggestion) && 
    suggestion.toLowerCase().includes(inputValue.value.toLowerCase())
  )
})

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  const value = inputValue.value.trim()
  if (value && !tags.value.includes(value)) {
    if (tags.value.length < props.maxTags) {
      tags.value = [...tags.value, value]
    } else {
      ElMessage.warning(`最多只能添加${props.maxTags}个标签`)
    }
  }
  inputVisible.value = false
  inputValue.value = ''
}

const removeTag = (index: number) => {
  const newTags = [...tags.value]
  newTags.splice(index, 1)
  tags.value = newTags
}

const addSuggestion = (suggestion: string) => {
  if (!tags.value.includes(suggestion)) {
    if (tags.value.length < props.maxTags) {
      tags.value = [...tags.value, suggestion]
    } else {
      ElMessage.warning(`最多只能添加${props.maxTags}个标签`)
    }
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 监听输入值变化，实时过滤建议
watch(inputValue, () => {
  // 这里可以添加防抖逻辑
})
</script>

<style scoped>
.tag-input {
  width: 100%;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  min-height: 32px;
  padding: 4px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
}

.tag-container:focus-within {
  border-color: #409eff;
}

.tag-item {
  margin: 0;
}

.tag-input-field {
  width: 120px;
  margin: 0;
}

.tag-input-field :deep(.el-input__wrapper) {
  box-shadow: none;
  padding: 0 4px;
}

.add-tag-btn {
  height: 24px;
  padding: 0 8px;
  font-size: 12px;
}

.tag-suggestions {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.suggestion-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.suggestion-tag {
  margin-right: 4px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion-tag:hover {
  background-color: #409eff;
  color: white;
}
</style>
