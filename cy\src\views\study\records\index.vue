<template>
  <div class="study-records-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-left">
        <h2>学习记录管理</h2>
        <p class="page-description">查看和管理学员的学习记录，分析学习数据和趋势</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出记录
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ statistics.totalStudents }}</div>
                <div class="stats-label">总学员数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon active">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ statistics.activeStudents }}</div>
                <div class="stats-label">活跃学员</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon time">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ formatDuration(statistics.totalStudyTime) }}</div>
                <div class="stats-label">总学习时长</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon rate">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ statistics.completionRate }}%</div>
                <div class="stats-label">完成率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选表单 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" ref="filterFormRef" inline>
        <el-form-item label="学员姓名">
          <el-input
            v-model="filterForm.keyword"
            placeholder="请输入学员姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="部门">
          <el-select
            v-model="filterForm.departmentId"
            placeholder="请选择部门"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dept in departmentList"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="课程">
          <el-select
            v-model="filterForm.courseId"
            placeholder="请选择课程"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="course in courseList"
              :key="course.id"
              :label="course.name"
              :value="course.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="完成状态">
          <el-select
            v-model="filterForm.completed"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="未完成" :value="0" />
            <el-option label="已完成" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="学习时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 学习记录表格 -->
    <el-card class="table-card">
      <el-table
        :data="recordsList"
        v-loading="loading"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="studentName" label="学员姓名" width="120" />
        <el-table-column prop="departmentName" label="部门" width="120" />
        <el-table-column prop="courseName" label="课程名称" min-width="200" />
        <el-table-column prop="lessonName" label="课时名称" min-width="180" />
        <el-table-column prop="progress" label="学习进度" width="100" align="center">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress"
              :stroke-width="8"
              :show-text="false"
              :color="getProgressColor(row.progress)"
            />
            <span class="progress-text">{{ row.progress }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="学习时长" width="100" align="center">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        <el-table-column prop="completed" label="完成状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.completed ? 'success' : 'warning'">
              {{ row.completed ? '已完成' : '学习中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastStudyTime" label="最后学习时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.lastStudyTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="viewDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button type="danger" link @click="deleteRecord(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 学习记录详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="学习记录详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="学员姓名">{{ currentRecord.studentName }}</el-descriptions-item>
          <el-descriptions-item label="所属部门">{{ currentRecord.departmentName }}</el-descriptions-item>
          <el-descriptions-item label="课程名称">{{ currentRecord.courseName }}</el-descriptions-item>
          <el-descriptions-item label="课时名称">{{ currentRecord.lessonName || '整体课程' }}</el-descriptions-item>
          <el-descriptions-item label="学习进度">
            <el-progress :percentage="currentRecord.progress" />
          </el-descriptions-item>
          <el-descriptions-item label="学习时长">{{ formatDuration(currentRecord.duration) }}</el-descriptions-item>
          <el-descriptions-item label="完成状态">
            <el-tag :type="currentRecord.completed ? 'success' : 'warning'">
              {{ currentRecord.completed ? '已完成' : '学习中' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后学习位置">{{ currentRecord.lastPosition || 0 }}秒</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentRecord.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="最后学习时间">{{ formatDateTime(currentRecord.lastStudyTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 导出对话框 -->
    <ExportDialog
      v-model="exportDialogVisible"
      title="导出学习记录"
      export-url="/api/study/records/export"
      :support-csv="true"
      :support-range="true"
      :support-time-range="true"
      :fields="[
        { key: 'studentName', label: '学员姓名', required: true },
        { key: 'studentNumber', label: '学员编号' },
        { key: 'departmentName', label: '部门' },
        { key: 'courseName', label: '课程名称', required: true },
        { key: 'lessonName', label: '课时名称' },
        { key: 'progress', label: '学习进度', required: true },
        { key: 'duration', label: '学习时长' },
        { key: 'completed', label: '完成状态' },
        { key: 'lastStudyTime', label: '最后学习时间' },
        { key: 'createdAt', label: '创建时间' }
      ]"
      :default-params="{
        keyword: filterForm.keyword,
        departmentId: filterForm.departmentId,
        courseId: filterForm.courseId,
        completed: filterForm.completed,
        startTime: filterForm.startTime,
        endTime: filterForm.endTime
      }"
      description="可选择导出全部记录、当前页面记录，支持按时间范围和完成状态筛选"
      @success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  RefreshLeft,
  Download,
  Refresh,
  User,
  UserFilled,
  Clock,
  TrendCharts,
  View,
  Delete
} from '@element-plus/icons-vue'
import {
  getStudyRecordList,
  deleteStudyRecord,
  exportStudyRecords,
  getDepartmentStatistics,
  getActiveStudents,
  type StudyRecordVO,
  type StudyRecordQueryParams,
  type DepartmentStudyStatistics
} from '@/api/study'
import { getDepartmentList, type Department } from '@/api/department'
import { getCourseList, type Course } from '@/api/course'
import ExportDialog from '@/components/ImportExport/ExportDialog.vue'

// 表格数据和分页
const loading = ref(false)
const exportLoading = ref(false)
const recordsList = ref<StudyRecordVO[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 筛选表单
const filterFormRef = ref()
const filterForm = reactive<StudyRecordQueryParams>({
  keyword: '',
  departmentId: undefined,
  courseId: undefined,
  completed: undefined,
  startTime: '',
  endTime: ''
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 部门和课程列表
const departmentList = ref<Department[]>([])
const courseList = ref<Course[]>([])

// 统计数据
const statistics = ref({
  totalStudents: 0,
  activeStudents: 0,
  totalStudyTime: 0,
  completionRate: 0
})

// 详情弹窗
const detailDialogVisible = ref(false)
const currentRecord = ref<StudyRecordVO | null>(null)

// 导出相关
const exportDialogVisible = ref(false)

// 获取学习记录列表
const fetchRecordsList = async () => {
  loading.value = true
  try {
    const params: StudyRecordQueryParams = {
      page: currentPage.value,
      size: pageSize.value,
      ...filterForm
    }

    const data = await getStudyRecordList(params)
    recordsList.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取学习记录失败:', error)
    ElMessage.error('获取学习记录失败')
  } finally {
    loading.value = false
  }
}

// 获取部门列表
const fetchDepartmentList = async () => {
  try {
    const data = await getDepartmentList({ page: 1, size: 1000 })
    departmentList.value = data.list
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 获取课程列表
const fetchCourseList = async () => {
  try {
    const data = await getCourseList({ page: 1, size: 1000 })
    courseList.value = data.list
  } catch (error) {
    console.error('获取课程列表失败:', error)
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const [deptStats, activeStudents] = await Promise.all([
      getDepartmentStatistics(),
      getActiveStudents(100)
    ])

    // 计算总体统计
    statistics.value = {
      totalStudents: deptStats.reduce((sum, dept) => sum + dept.totalStudents, 0),
      activeStudents: deptStats.reduce((sum, dept) => sum + dept.activeStudents, 0),
      totalStudyTime: deptStats.reduce((sum, dept) => sum + dept.totalStudyTime, 0),
      completionRate: Math.round(
        deptStats.reduce((sum, dept) => sum + dept.completionRate, 0) / deptStats.length
      )
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchRecordsList()
}

// 重置筛选
const handleReset = () => {
  filterFormRef.value?.resetFields()
  dateRange.value = null
  filterForm.startTime = ''
  filterForm.endTime = ''
  currentPage.value = 1
  fetchRecordsList()
}

// 日期范围变化
const handleDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    filterForm.startTime = dates[0]
    filterForm.endTime = dates[1]
  } else {
    filterForm.startTime = ''
    filterForm.endTime = ''
  }
}

// 分页变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchRecordsList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchRecordsList()
}

// 排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  // 这里可以添加排序逻辑
  console.log('排序:', prop, order)
}

// 查看详情
const viewDetails = (record: StudyRecordVO) => {
  currentRecord.value = record
  detailDialogVisible.value = true
}

// 删除记录
const deleteRecord = async (record: StudyRecordVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除学员"${record.studentName}"的学习记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteStudyRecord(record.id!)
    ElMessage.success('删除成功')
    fetchRecordsList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除学习记录失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 导出记录
const handleExport = () => {
  exportDialogVisible.value = true
}

// 导出成功回调
const handleExportSuccess = () => {
  ElMessage.success('学习记录导出成功')
}

// 刷新数据
const refreshData = () => {
  fetchRecordsList()
  fetchStatistics()
}

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds) return '0分钟'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 初始化数据
onMounted(() => {
  fetchRecordsList()
  fetchDepartmentList()
  fetchCourseList()
  fetchStatistics()
})
</script>

<style scoped>
.study-records-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-cards {
  margin-bottom: 20px;
}

/* .stats-card {
  height: 100px;
} */

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.time {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.progress-text {
  margin-left: 8px;
  font-size: 12px;
  color: #606266;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.record-detail {
  padding: 20px 0;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-progress-bar__outer) {
  background-color: #f0f2f5;
}

:deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-card__body) {
  padding: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .stats-cards .el-col {
    margin-bottom: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
