<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.ExamPaperQuestionMapper">
    <!-- 查询试卷题目关联列表（包含题目详情） -->
    <resultMap id="PaperQuestionWithDetailMap" type="com.cy.education.model.vo.ExamPaperQuestionVO">
        <id property="id" column="id"/>
        <result property="paperId" column="paper_id"/>
        <result property="questionId" column="question_id"/>
        <result property="score" column="score"/>
        <result property="questionOrder" column="question_order"/>
        <association property="question" javaType="com.cy.education.model.vo.ExamQuestionVO">
            <id property="id" column="q_id"/>
            <result property="bankId" column="q_bank_id"/>
            <result property="bankName" column="bank_name"/>
            <result property="title" column="q_title"/>
            <result property="type" column="q_type"/>
            <result property="options" column="q_options"/>
            <result property="correctAnswer" column="q_correct_answer"/>
            <result property="explanation" column="q_explanation"/>
            <result property="createdAt" column="q_created_at"/>
            <result property="updatedAt" column="q_updated_at"/>
            <result property="createdBy" column="q_created_by"/>
        </association>
    </resultMap>

    <select id="selectQuestionsByPaperId" resultMap="PaperQuestionWithDetailMap">
        SELECT
        pq.id, pq.paper_id, pq.question_id, pq.score, pq.question_order,
        q.id AS q_id, q.bank_id AS q_bank_id, b.name AS bank_name,
        q.title AS q_title, q.type AS q_type, q.options AS q_options,
        q.correct_answer AS q_correct_answer, q.explanation AS q_explanation,
        q.created_at AS q_created_at, q.updated_at AS q_updated_at,
        q.created_by AS q_created_by
        FROM exam_paper_question pq
        LEFT JOIN exam_question q ON pq.question_id = q.id
        LEFT JOIN exam_bank b ON q.bank_id = b.id
        WHERE pq.paper_id = #{paperId} AND q.deleted = 0
        ORDER BY pq.question_order ASC, pq.id ASC
    </select>

    <!-- 批量插入试卷题目关联 -->
    <insert id="batchInsert">
        INSERT INTO exam_paper_question (paper_id, question_id, score, question_order, created_at)
        VALUES
        <foreach collection="questions" item="item" separator=",">
            (#{item.paperId}, #{item.questionId}, #{item.score}, #{item.questionOrder}, #{item.createdAt})
        </foreach>
    </insert>

    <!-- 删除试卷题目关联 -->
    <delete id="deleteByPaperIdAndQuestionId">
        DELETE FROM exam_paper_question
        WHERE paper_id = #{paperId} AND question_id = #{questionId}
    </delete>

    <!-- 删除试卷所有题目关联 -->
    <delete id="deleteByPaperId">
        DELETE FROM exam_paper_question
        WHERE paper_id = #{paperId}
    </delete>
</mapper>
