<template>
  <el-dialog
    :title="`批量上传${resourceType === 'file' ? '文件' : '视频'}`"
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    width="70%"
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
  >
    <el-upload
      drag
      multiple
      action="#"
      :http-request="() => {}"
      :before-upload="handleBeforeUpload"
      class="upload-area"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    </el-upload>

    <el-table :data="uploadList" style="width: 100%; margin-top: 20px;">
      <el-table-column label="文件名" prop="file.name">
        <template #default="{ row }">
          <span>{{ row.file.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="资源名称" prop="name">
        <template #default="{row}">
          <el-input v-model="row.name" placeholder="请输入资源名称"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="标签" prop="tags" width="250">
        <template #default="{row}">
           <el-tag
              v-for="tag in row.tags"
              :key="tag"
              class="mx-1"
              closable
              :disable-transitions="false"
              @close="handleTagClose(row, tag)"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="row.inputVisible"
              :ref="el => tagInputRefs[row.uid] = el"
              v-model="row.inputValue"
              class="ml-1 w-20"
              size="small"
              @keyup.enter="handleInputConfirm(row)"
              @blur="handleInputConfirm(row)"
            />
            <el-button v-else class="button-new-tag ml-1" size="small" @click="showTagInput(row)">
              + New Tag
            </el-button>
        </template>
      </el-table-column>
      <el-table-column label="进度" prop="progress">
          <template #default="{row}">
              <el-progress :percentage="row.progress" :status="row.status" />
          </template>
      </el-table-column>
      <el-table-column label="操作" width="80">
          <template #default="{$index}">
              <el-button type="danger" :icon="Delete" circle @click="handleRemove($index)"></el-button>
          </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取 消</el-button>
        <el-button type="primary" @click="handleStartUpload" :loading="isUploading">开始上传</el-button>
        <el-button type="success" @click="handleSaveToLibrary" :disabled="!isAllUploaded" :loading="isSaving">保存到资源库</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, reactive, nextTick } from 'vue';
import { ElMessage, ElInput } from 'element-plus';
import type { UploadRawFile } from 'element-plus';
import { UploadFilled, Delete } from '@element-plus/icons-vue';
import { getOssSignature, uploadToOss } from '@/api/oss';
import { createResourcesBatch, Resource } from '@/api/resource';

type ResourceType = 'file' | 'video';

interface UploadItem {
  uid: number;
  file: File;
  name: string;
  tags: string[];
  progress: number;
  status: 'ready' | 'uploading' | 'success' | 'exception';
  url?: string;
  inputVisible?: boolean;
  inputValue?: string;
}

const props = defineProps<{
  visible: boolean;
  resourceType: ResourceType;
}>();
const emit = defineEmits(['update:visible', 'upload-success']);

const uploadList = ref<UploadItem[]>([]);
const isUploading = ref(false);
const isSaving = ref(false);
const tagInputRefs = reactive<Record<number, any>>({});


const isAllUploaded = computed(() =>
  uploadList.value.length > 0 && uploadList.value.every(item => item.status === 'success')
);

const handleDialogClosed = () => {
    uploadList.value = [];
    isUploading.value = false;
    isSaving.value = false;
}

const handleBeforeUpload = (rawFile: UploadRawFile) => {
  uploadList.value.push({
    uid: rawFile.uid,
    file: rawFile,
    name: rawFile.name.substring(0, rawFile.name.lastIndexOf('.')),
    tags: [],
    progress: 0,
    status: 'ready',
    inputVisible: false,
    inputValue: ''
  });
  return false;
};

const handleRemove = (index: number) => {
    uploadList.value.splice(index, 1);
}

const handleStartUpload = async () => {
  if (uploadList.value.length === 0) {
    ElMessage.warning('请先选择文件');
    return;
  }
  isUploading.value = true;
  
  const uploadPromises = uploadList.value.map(async (item) => {
    try {
        const signature: any = await getOssSignature();
        console.log('Got signature for', item.file.name, signature);
        item.status = 'uploading';
        
        // Handle both wrapped and unwrapped signature responses
        const ossSignatureData = signature.data ? signature.data : signature;

        const { url } = await uploadToOss(item.file, ossSignatureData, (percent) => {
            item.progress = percent;
        });
        item.url = url;
        item.status = 'success';
    } catch (e) {
        item.status = 'exception';
        console.error('Upload failed for', item.file.name, e);
    }
  });

  await Promise.all(uploadPromises);
  isUploading.value = false;
  if (uploadList.value.every(item => item.status === 'success' || item.status === 'exception')) {
      if(uploadList.value.some(item => item.status === 'exception')) {
          ElMessage.error('部分文件上传失败，请检查控制台');
      } else {
          ElMessage.success('所有上传任务已完成');
      }
  }
};

const handleSaveToLibrary = async () => {
    isSaving.value = true;
    const resourcesToSave: Partial<Resource>[] = uploadList.value
    .filter(item => item.status === 'success')
    .map(item => ({
        name: item.name,
        type: props.resourceType,
        content: item.url!,
        tags: JSON.stringify(item.tags),
    }));
    
    if (resourcesToSave.length === 0) {
        ElMessage.warning('没有成功上传的文件可保存');
        isSaving.value = false;
        return;
    }
    
    try {
        await createResourcesBatch(resourcesToSave as Resource[]);
        ElMessage.success('已成功保存到资源库');
        emit('upload-success');
        emit('update:visible', false);
    } catch (error) {
        ElMessage.error('保存到资源库失败');
    } finally {
        isSaving.value = false;
    }
};

// --- Tag Editing Logic ---
const handleTagClose = (row: UploadItem, tag: string) => {
  row.tags.splice(row.tags.indexOf(tag), 1)
}

const showTagInput = (row: UploadItem) => {
  row.inputVisible = true
  nextTick(() => {
    tagInputRefs[row.uid]?.input.focus()
  })
}

const handleInputConfirm = (row: UploadItem) => {
  if (row.inputValue) {
    row.tags.push(row.inputValue)
  }
  row.inputVisible = false
  row.inputValue = ''
}
// --- End Tag Editing Logic ---
</script>

<style scoped>
.upload-area {
  width: 100%;
}
.mx-1 {
  margin: 0 4px 4px 0;
}
.w-20 {
    width: 80px;
}
.button-new-tag {
    margin-left: 4px;
}
</style> 