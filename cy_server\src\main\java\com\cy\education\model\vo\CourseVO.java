package com.cy.education.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程VO
 */
@Data
public class CourseVO {
    
    /**
     * 课程ID
     */
    private Integer id;
    
    /**
     * 课程名称
     */
    private String title;
    
    /**
     * 封面图片路径
     */
    private String cover;
    
    /**
     * 课程描述
     */
    private String description;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 课程级别：beginner, intermediate, advanced
     */
    private String level;
    
    /**
     * 课程总时长(分钟)
     */
    private Integer duration;
    
    /**
     * 学习人数
     */
    private Integer studentsCount;
    
    /**
     * 是否发布：0草稿，1已发布
     */
    private Integer isPublished;
    
    /**
     * 是否允许快进：0不允许，1允许
     */
    private Integer allowFastForward;
    
    /**
     * 可见部门ID列表
     */
    private List<Integer> visibleDepartments;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 