package com.cy.education.service.impl.exam;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BadRequestException;
import com.cy.education.exception.NotFoundException;
import com.cy.education.model.entity.Student;
import com.cy.education.model.entity.exam.ExamExam;
import com.cy.education.model.params.ExamQueryParams;
import com.cy.education.model.vo.ExamPaperVO;
import com.cy.education.model.vo.ExamVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.model.vo.ExamRecordVO;
import com.cy.education.model.vo.ExamAnswerVO;
import com.cy.education.repository.ExamExamMapper;
import com.cy.education.service.StudentService;
import com.cy.education.service.exam.ExamAnswerService;
import com.cy.education.service.exam.ExamPaperService;
import com.cy.education.service.exam.ExamRecordService;
import com.cy.education.service.exam.ExamService;
import com.cy.education.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 考试服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExamServiceImpl implements ExamService {

    private final ExamExamMapper examExamMapper;
    private final ExamPaperService examPaperService;
    private final StudentService studentService;
    private final ExamRecordService examRecordService;
    private final ExamAnswerService examAnswerService;

    @Override
    public PageResponse<ExamVO> listExams(ExamQueryParams params) {
        Page<ExamExam> page = new Page<>(params.getPageNum(), params.getPageSize());
        Integer userId = SecurityUtil.getCurrentUserId();
        Student student = studentService.getStudentById(userId);
        IPage<ExamExam> examPage = examExamMapper.selectExamPage(
                page,
                params.getKeyword(),
                params.getPaperId(),
                student.getDepartmentId(),
                params.getStatus(),
                params.getIsPublished(),
                params.getSortBy(),
                params.getSortOrder()
        );
        List<ExamVO> voList = examPage.getRecords().stream().map(this::convert2VOWithPaper).collect(Collectors.toList());
        return PageResponse.of(voList, examPage.getTotal(), params.getPageNum(), params.getPageSize());
    }

    /**
     * 获取考试详情（不含试卷正确答案，含当前用户作答记录）
     */
    @Override
    public ExamVO getExamDetail(Integer examId) {
        ExamExam exam = examExamMapper.selectById(examId);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        return convert2VOWithPaper(exam);
    }

    /**
     * 获取考试完整详情（含所有信息，包括题目、正确答案、用户作答记录），用于考试作答和判题
     */
    @Override
    public ExamVO getExamFullDetail(Integer examId) {
        ExamExam exam = examExamMapper.selectById(examId);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        return convert2VOWithAnswer(exam);
    }

    /**
     * 转换为ExamVO（不含正确答案）
     */
    private ExamVO convert2VOWithPaper(ExamExam exam) {
        ExamVO vo = new ExamVO();
        BeanUtils.copyProperties(exam, vo);
        // 查询试卷信息（不含正确答案）
        ExamPaperVO paper = examPaperService.getPaperDetailWithoutAnswer(exam.getPaperId());
        vo.setPaper(paper);
        // 填充当前用户的作答信息和答案详情
        ExamRecordVO record = examRecordService.selectByExamIdAndUserId(exam.getId(), SecurityUtil.getCurrentUserId());
        vo.setCurrentUserRecord(record);
        return vo;
    }

    /**
     * 转换为ExamVO（含正确答案）
     */
    private ExamVO convert2VOWithAnswer(ExamExam exam) {
        ExamVO vo = new ExamVO();
        BeanUtils.copyProperties(exam, vo);
        // 查询试卷信息（含正确答案）
        ExamPaperVO paper = examPaperService.getPaperDetailWithAnswer(exam.getPaperId());
        vo.setPaper(paper);
        // 查询部门ID和名称
//        List<Integer> departmentIds = examExamDepartmentMapper.selectDepartmentIdsByExamId(exam.getId());
//        vo.setDepartmentIds(departmentIds);
//        if (departmentIds != null && !departmentIds.isEmpty()) {
//            Map<Integer, String> departmentMap = departmentService.getDepartmentMapByIds(departmentIds);
//            List<String> departments = departmentIds.stream()
//                    .map(departmentMap::get)
//                    .filter(StringUtils::hasText)
//                    .collect(Collectors.toList());
//            vo.setDepartments(departments);
//        }
        // 填充当前用户的作答信息和答案详情
        ExamRecordVO record = examRecordService.selectByExamIdAndUserId(exam.getId(), SecurityUtil.getCurrentUserId());
        if (record != null) {
            List<ExamAnswerVO> answers = examAnswerService.selectByRecordId(record.getId());
            record.setAnswers(answers);
            vo.setCurrentUserRecord(record);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExamStatus(Integer id, Integer status) {
        ExamExam exam = examExamMapper.selectById(id);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        // 如果状态没有变化，则直接返回成功
        if (exam.getStatus().equals(status)) {
            return true;
        }
        // 状态只能是0-草稿,1-未开始,2-进行中,3-已结束
        if (status < 0 || status > 3) {
            throw new BadRequestException("无效的考试状态");
        }
        // 更新考试状态
        exam.setStatus(status);
        return examExamMapper.updateById(exam) > 0;
    }
}
