package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分商品实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("points_product")
public class PointsProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品分类：virtual(虚拟物品)、physical(实物)、service(服务)、other(其他)
     */
    private String category;

    /**
     * 所需积分
     */
    private Integer points;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 库存预警阈值
     */
    private Integer lowStockThreshold;

    /**
     * 商品图片URL
     */
    private String image;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 兑换次数
     */
    private Integer exchangeCount;

    /**
     * 状态：active(上架)、inactive(下架)
     */
    private String status;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
