import { get, post } from '@/utils/request'

// 特殊的API调用函数，处理非标准响应格式
function practiceRequest<T>(url: string, options: any = {}): Promise<T> {
  return new Promise((resolve, reject) => {
    const BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'
    const token = uni.getStorageSync('token') || localStorage.getItem('token') || ''
    
    uni.request({
      url: BASE_URL + url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        ...(options.header || {}),
        Authorization: token ? `Bearer ${token}` : ''
      },
      success(res) {
        console.log('Practice API Response:', res)
        const data = res.data as any
        
        // 如果是数组类型，直接返回
        if (Array.isArray(data)) {
          resolve(data as T)
          return
        }
        
        // 如果是标准格式
        if (data && (data.code === 200 || data.code === 0)) {
          resolve(data.data as T)
          return
        }
        
        // 如果是对象但没有code字段，直接返回
        if (typeof data === 'object' && data !== null) {
          resolve(data as T)
          return
        }
        
        // 其他情况作为错误处理
        uni.showToast({
          title: data.message || '请求失败',
          icon: 'none'
        })
        reject(data)
      },
      fail(err) {
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

/**
 * 获取允许练习的题库列表
 */
export const getAvailableBanks = () =>
  practiceRequest('/api/practice/banks')

/**
 * 获取用户的练习统计信息
 */
export const getUserPracticeStats = (userId: number) =>
  practiceRequest(`/api/practice/stats/${userId}`)

/**
 * 获取用户在指定题库的练习统计信息
 */
export const getUserPracticeStatsByBank = (userId: number, bankId: number) =>
  practiceRequest(`/api/practice/stats/${userId}/${bankId}`)

/**
 * 开始练习
 */
export const startPractice = (data: {
  userId: number
  bankId?: number
  type: string
}) =>
  practiceRequest('/api/practice/start', { method: 'POST', data })

/**
 * 获取下一道题
 */
export const getNextQuestion = (recordId: number) =>
  practiceRequest(`/api/practice/next/${recordId}`)

/**
 * 提交答案
 */
export const submitAnswer = (data: {
  recordId: number
  questionId: number
  userAnswer: string
}) =>
  practiceRequest('/api/practice/submit', { method: 'POST', data })

/**
 * 完成练习
 */
export const completePractice = (recordId: number) =>
  practiceRequest(`/api/practice/complete/${recordId}`, { method: 'POST' })

/**
 * 获取错题本
 */
export const getWrongQuestions = (userId: number, bankId: number | null) => {
  return request({
    url: '/practice/wrong-questions',
    method: 'get',
    params: { userId, bankId }
  })
}

/**
 * 获取用户的练习记录
 */
export const getUserPracticeRecords = (userId: number) =>
  practiceRequest(`/api/practice/records/${userId}`)

/**
 * 获取练习记录详情
 */
export const getPracticeRecordDetail = (recordId: number) =>
  practiceRequest(`/api/practice/record/${recordId}`)

/**
 * 清空错题本
 */
export const clearWrongQuestions = (data: {
  userId: number
  bankId?: number
}) =>
  practiceRequest('/api/practice/clear-wrong', { method: 'POST', data }) 