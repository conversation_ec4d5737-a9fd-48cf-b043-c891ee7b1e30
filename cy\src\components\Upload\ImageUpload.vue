<template>
  <div class="image-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :show-file-list="false"
      accept="image/*"
      class="upload-demo"
    >
      <div v-if="imageUrl" class="image-preview">
        <img :src="imageUrl" alt="预览图" />
        <div class="image-overlay">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">点击更换图片</div>
        </div>
      </div>
      <div v-else class="upload-placeholder">
        <el-icon class="upload-icon"><Plus /></el-icon>
        <div class="upload-text">点击上传图片</div>
        <div class="upload-tip">{{ tip }}</div>
      </div>
    </el-upload>
    
    <div v-if="uploading" class="upload-progress">
      <el-progress :percentage="uploadProgress" />
      <div class="progress-text">上传中...</div>
    </div>
    
    <div v-if="imageUrl" class="image-actions">
      <el-button size="small" @click="previewImage">
        <el-icon><View /></el-icon>
        预览
      </el-button>
      <el-button size="small" @click="copyUrl">
        <el-icon><CopyDocument /></el-icon>
        复制链接
      </el-button>
      <el-button size="small" type="danger" @click="clearImage">
        <el-icon><Delete /></el-icon>
        删除
      </el-button>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="60%">
      <div class="preview-container">
        <img :src="imageUrl" alt="预览图" style="max-width: 100%; max-height: 500px;" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, View, CopyDocument, Delete } from '@element-plus/icons-vue'
import { getImageSignature, uploadToOss } from '@/api/oss'

interface Props {
  modelValue?: string
  maxSize?: number // MB
  width?: number // 建议宽度
  height?: number // 建议高度
  tip?: string // 提示文字
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  maxSize: 10,
  tip: '支持 JPG、PNG、GIF 格式，建议大小不超过 10MB'
})

const emit = defineEmits<Emits>()

const uploadRef = ref()
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadUrl = ref('')
const uploadHeaders = ref({})
const uploadData = ref({})
const previewVisible = ref(false)

const imageUrl = computed({
  get: () => props.modelValue || '',
  set: (value: string) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

const beforeUpload = async (file: File) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`图片大小不能超过 ${props.maxSize}MB!`)
    return false
  }

  uploading.value = true
  uploadProgress.value = 0

  try {
    // 获取OSS上传签名
    const signature = await getImageSignature()

    // 直接上传到OSS
    const result = await uploadToOss(file, signature, (percent) => {
      uploadProgress.value = percent
    })

    imageUrl.value = result.url
    uploading.value = false
    uploadProgress.value = 100
    ElMessage.success('上传成功')

    return false // 阻止element-plus的默认上传行为
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
    uploading.value = false
    uploadProgress.value = 0
    return false
  }
}

// 这些方法不再需要，因为我们直接在beforeUpload中处理上传

const previewImage = () => {
  previewVisible.value = true
}

const copyUrl = () => {
  navigator.clipboard.writeText(imageUrl.value).then(() => {
    ElMessage.success('链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

const clearImage = () => {
  imageUrl.value = ''
  uploadProgress.value = 0
}
</script>

<style scoped>
.image-upload {
  width: 100%;
}

.upload-demo :deep(.el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.upload-demo :deep(.el-upload:hover) {
  border-color: #409eff;
}

.image-preview {
  position: relative;
  width: 178px;
  height: 178px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  color: white;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.upload-placeholder {
  width: 178px;
  height: 178px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
  margin-bottom: 4px;
}

.upload-tip {
  font-size: 12px;
  color: #c0c4cc;
  text-align: center;
  padding: 0 10px;
}

.upload-progress {
  margin-top: 10px;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.image-actions {
  margin-top: 10px;
  display: flex;
  gap: 8px;
}

.preview-container {
  text-align: center;
}
</style>
