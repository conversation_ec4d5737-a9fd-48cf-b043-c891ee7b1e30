# PK功能启动指南

## 问题诊断

根据日志显示的问题：

### 1. 历史记录API问题
```
加载历史记录失败: {total: 0, size: 5, success: true, page: 1, list: Array(0)}
```
**分析**: API返回成功，但list为空数组，说明数据库中没有PK历史记录数据。

### 2. WebSocket连接问题
```
WebSocket connection to 'ws://localhost:8090/websocket/pk/1' failed
```
**分析**: WebSocket连接失败，可能原因：
- 后端服务未启动
- WebSocket配置问题
- 端口不正确

## 解决步骤

### 步骤1: 确保后端服务启动

1. **启动后端服务**
```bash
cd cy_app_server
mvn spring-boot:run
```

2. **检查服务是否启动成功**
访问: `http://localhost:8090/api/pk/test/health`

期望响应:
```json
{
  "status": "ok",
  "timestamp": **********,
  "service": "PK Service"
}
```

### 步骤2: 检查数据库表

1. **确保PK表已创建**
```sql
-- 检查表是否存在
SHOW TABLES LIKE 'pk_%';

-- 应该看到以下表：
-- pk_room
-- pk_participant
-- pk_question
-- pk_answer
-- pk_match_queue
```

2. **如果表不存在，执行创建脚本**
```sql
source cy_app_server/src/main/resources/db/pk_tables.sql
```

### 步骤3: 测试API接口

1. **测试Mapper注入**
```bash
curl http://localhost:8090/api/pk/mapper-test/injection
```

2. **测试PK表**
```bash
curl http://localhost:8090/api/pk/mapper-test/tables
```

3. **测试PK服务**
```bash
curl http://localhost:8090/api/pk/test/service
```

### 步骤4: 测试WebSocket

1. **检查WebSocket配置**
确保以下配置正确：
- `@ServerEndpoint("/websocket/pk/{userId}")`
- `ServerEndpointExporter` Bean已注册
- `spring-boot-starter-websocket` 依赖已添加

2. **使用前端调试页面测试**
- 进入 `/pages/pk/debug`
- 点击"测试后端健康状态"
- 点击"重新连接WebSocket"
- 查看连接日志

### 步骤5: 前端调试

1. **检查用户ID**
确保用户已登录，`uni.getStorageSync('userInfo')` 返回有效数据

2. **使用调试页面**
- 访问 `/pages/pk/debug`
- 检查所有测试项目
- 查看详细的错误日志

## 常见问题解决

### 问题1: 后端服务启动失败

**可能原因**:
- 端口被占用
- 数据库连接失败
- 依赖缺失

**解决方案**:
```bash
# 检查端口占用
netstat -ano | findstr :8090

# 检查数据库连接
# 确保数据库服务运行正常

# 重新安装依赖
mvn clean install
```

### 问题2: WebSocket连接失败

**可能原因**:
- 后端WebSocket服务未启动
- 防火墙阻止连接
- 端口配置错误

**解决方案**:
1. 确保后端服务正常运行
2. 检查防火墙设置
3. 验证WebSocket端点配置

### 问题3: 数据库表不存在

**错误信息**: `Table 'database.pk_room' doesn't exist`

**解决方案**:
```sql
-- 执行表创建脚本
source cy_app_server/src/main/resources/db/pk_tables.sql

-- 或手动创建表
CREATE TABLE pk_room (...);
-- 其他表...
```

### 问题4: Mapper Bean注入失败

**错误信息**: `Field pkRoomMapper required a bean of type 'PkRoomMapper' that could not be found`

**解决方案**:
1. 确保@MapperScan配置正确
2. 检查Mapper接口包路径
3. 重启应用

## 验证清单

### 后端验证
- [ ] 服务启动成功 (端口8090)
- [ ] 健康检查接口可访问
- [ ] 数据库表已创建
- [ ] Mapper注入成功
- [ ] WebSocket端点可用

### 前端验证
- [ ] 用户登录状态正常
- [ ] API接口调用成功
- [ ] WebSocket连接成功
- [ ] 页面布局正常显示

### 功能验证
- [ ] 题库列表加载成功
- [ ] 历史记录显示正常（即使为空）
- [ ] WebSocket心跳正常
- [ ] 错误处理正常工作

## 调试工具

### 1. 后端调试接口
- `/api/pk/test/health` - 健康检查
- `/api/pk/test/service` - 服务测试
- `/api/pk/mapper-test/injection` - Mapper注入测试
- `/api/pk/mapper-test/tables` - 数据库表测试

### 2. 前端调试页面
- `/pages/pk/debug` - 完整的调试工具
- 实时日志监控
- API测试功能
- WebSocket连接测试

### 3. 浏览器开发者工具
- Network标签：查看API请求
- Console标签：查看JavaScript日志
- WebSocket标签：监控WebSocket连接

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：

1. 后端启动日志
2. 前端调试页面的测试结果
3. 浏览器开发者工具的错误信息
4. 数据库连接状态

这将帮助快速定位和解决问题。
