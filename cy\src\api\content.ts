import { get, post, put, del } from '@/utils/request'

// 轮播图相关接口
export interface CarouselItem {
  id: number
  title: string
  imageUrl: string
  link: string
  sort: number
  status: number
  createTime: string
  updateTime?: string
}

export interface CarouselQueryParams {
  pageNum?: number
  pageSize?: number
  status?: number
  keyword?: string
}

// 轮播图列表
export const getCarouselList = (params: CarouselQueryParams) => {
  return get<{
    list: CarouselItem[]
    total: number
  }>('/content/carousel/list', params)
}

// 轮播图详情
export const getCarouselById = (id: number) => {
  return get<CarouselItem>(`/content/carousel/${id}`)
}

// 新增轮播图
export const addCarousel = (data: Partial<CarouselItem>) => {
  return post<{ id: number }>('/content/carousel', data)
}

// 编辑轮播图
export const updateCarousel = (data: Partial<CarouselItem>) => {
  return put<{ success: boolean }>(`/content/carousel/${data.id}`, data)
}

// 删除轮播图
export const deleteCarousel = (id: number) => {
  return del<{ success: boolean }>(`/content/carousel/${id}`)
}

// 切换轮播图状态
export const updateCarouselStatus = (id: number, status: number) => {
  return put<{ success: boolean }>(`/content/carousel/${id}/status`, { status })
}

// 新闻相关接口
export interface NewsItem {
  id: number
  title: string
  coverUrl?: string
  content: string
  publishTime: string
  viewCount: number
  status: number
  isTop: boolean
  createTime: string
  updateTime?: string
}

export interface NewsQueryParams {
  pageNum?: number
  pageSize?: number
  status?: number
  isTop?: boolean
  keyword?: string
}

// 新闻列表
export const getNewsList = (params: NewsQueryParams) => {
  return get<{
    list: NewsItem[]
    total: number
  }>('/content/news/list', params)
}

// 新闻详情
export const getNewsById = (id: number) => {
  return get<NewsItem>(`/content/news/${id}`)
}

// 新增新闻
export const addNews = (data: Partial<NewsItem>) => {
  return post<{ id: number }>('/content/news', data)
}

// 编辑新闻
export const updateNews = (data: Partial<NewsItem>) => {
  return put<{ success: boolean }>(`/content/news/${data.id}`, data)
}

// 删除新闻
export const deleteNews = (id: number) => {
  return del<{ success: boolean }>(`/content/news/${id}`)
}

// 切换新闻状态
export const updateNewsStatus = (id: number, status: number) => {
  return put<{ success: boolean }>(`/content/news/${id}/status`, { status })
}

// 切换新闻置顶状态
export const updateNewsTop = (id: number, isTop: boolean) => {
  return put<{ success: boolean }>(`/content/news/${id}/top`, { isTop })
}

// 公告相关接口
export interface NoticeItem {
  id: number
  title: string
  content: string
  publishTime: string
  importance: number
  status: number
  isTop: boolean
  createTime: string
  updateTime?: string
}

export interface NoticeQueryParams {
  pageNum?: number
  pageSize?: number
  status?: number
  isTop?: boolean
  importance?: number
  keyword?: string
}

// 公告列表
export const getNoticeList = (params: NoticeQueryParams) => {
  return get<{
    list: NoticeItem[]
    total: number
  }>('/content/notice/list', params)
}

// 公告详情
export const getNoticeById = (id: number) => {
  return get<NoticeItem>(`/content/notice/${id}`)
}

// 新增公告
export const addNotice = (data: Partial<NoticeItem>) => {
  return post<{ id: number }>('/content/notice', data)
}

// 编辑公告
export const updateNotice = (data: Partial<NoticeItem>) => {
  return put<{ success: boolean }>(`/content/notice/${data.id}`, data)
}

// 删除公告
export const deleteNotice = (id: number) => {
  return del<{ success: boolean }>(`/content/notice/${id}`)
}

// 切换公告状态
export const updateNoticeStatus = (id: number, status: number) => {
  return put<{ success: boolean }>(`/content/notice/${id}/status`, { status })
}

// 切换公告置顶状态
export const updateNoticeTop = (id: number, isTop: boolean) => {
  return put<{ success: boolean }>(`/content/notice/${id}/top`, { isTop })
} 