package com.cy.education.controller;

import com.cy.education.model.entity.PracticeRecord;
import com.cy.education.model.entity.PracticeStats;
import com.cy.education.model.entity.exam.ExamBank;
import com.cy.education.model.entity.exam.ExamQuestion;
import com.cy.education.service.PracticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 练习功能控制器
 */
@RestController
@RequestMapping("/practice")
@Api(tags = "练习功能")
public class PracticeController {
    
    @Autowired
    private PracticeService practiceService;
    
    /**
     * 获取允许练习的题库列表
     */
    @GetMapping("/banks")
    @ApiOperation("获取允许练习的题库列表")
    public ResponseEntity<List<ExamBank>> getAvailableBanks() {
        List<ExamBank> banks = practiceService.getAvailableBanks();
        return ResponseEntity.ok(banks);
    }
    
    /**
     * 获取用户的练习统计信息
     */
    @GetMapping("/stats/{userId}")
    @ApiOperation("获取用户的练习统计信息")
    public ResponseEntity<List<PracticeStats>> getUserPracticeStats(
            @ApiParam("用户ID") @PathVariable Integer userId) {
        List<PracticeStats> stats = practiceService.getUserPracticeStats(userId);
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 获取用户在指定题库的练习统计信息
     */
    @GetMapping("/stats/{userId}/{bankId}")
    @ApiOperation("获取用户在指定题库的练习统计信息")
    public ResponseEntity<PracticeStats> getUserPracticeStatsByBank(
            @ApiParam("用户ID") @PathVariable Integer userId,
            @ApiParam("题库ID") @PathVariable Integer bankId) {
        PracticeStats stats = practiceService.getUserPracticeStatsByBank(userId, bankId);
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 开始练习
     */
    @PostMapping("/start")
    @ApiOperation("开始练习")
    public ResponseEntity<Map<String, Object>> startPractice(
            @RequestBody Map<String, Object> request) {
        Integer userId = (Integer) request.get("userId");
        Integer bankId = (Integer) request.get("bankId");
        String type = (String) request.get("type");
        
        Integer recordId = practiceService.startPractice(userId, bankId, type);
        
        Map<String, Object> response = new HashMap<>();
        response.put("recordId", recordId);
        response.put("success", true);
        response.put("message", "练习已开始");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取下一道题
     */
    @GetMapping("/next/{recordId}")
    @ApiOperation("获取下一道题")
    public ResponseEntity<Map<String, Object>> getNextQuestion(
            @ApiParam("练习记录ID") @PathVariable Integer recordId) {
        Map<String, Object> result = practiceService.getNextQuestionWithProgress(recordId);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 提交答案
     */
    @PostMapping("/submit")
    @ApiOperation("提交答案")
    public ResponseEntity<Map<String, Object>> submitAnswer(
            @RequestBody Map<String, Object> request) {
        Integer recordId = (Integer) request.get("recordId");
        Integer questionId = (Integer) request.get("questionId");
        String userAnswer = (String) request.get("userAnswer");
        
        Map<String, Object> result = practiceService.submitAnswer(recordId, questionId, userAnswer);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 完成练习
     */
    @PostMapping("/complete/{recordId}")
    @ApiOperation("完成练习")
    public ResponseEntity<Map<String, Object>> completePractice(
            @ApiParam("练习记录ID") @PathVariable Integer recordId) {
        Map<String, Object> result = practiceService.completePractice(recordId);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取错题本
     */
    @GetMapping("/wrong-questions")
    @ApiOperation("获取错题本")
    public ResponseEntity<List<ExamQuestion>> getWrongQuestions(
            @ApiParam("用户ID") @RequestParam Integer userId,
            @ApiParam("题库ID") @RequestParam(required = false) Integer bankId) {
        List<ExamQuestion> questions = practiceService.getWrongQuestions(userId, bankId);
        return ResponseEntity.ok(questions);
    }
    
    /**
     * 获取用户的练习记录
     */
    @GetMapping("/records/{userId}")
    @ApiOperation("获取用户的练习记录")
    public ResponseEntity<List<PracticeRecord>> getUserPracticeRecords(
            @ApiParam("用户ID") @PathVariable Integer userId) {
        List<PracticeRecord> records = practiceService.getUserPracticeRecords(userId);
        return ResponseEntity.ok(records);
    }
    
    /**
     * 获取练习记录详情
     */
    @GetMapping("/record/{recordId}")
    @ApiOperation("获取练习记录详情")
    public ResponseEntity<Map<String, Object>> getPracticeRecordDetail(
            @ApiParam("练习记录ID") @PathVariable Integer recordId) {
        Map<String, Object> result = practiceService.getPracticeRecordDetail(recordId);
        return ResponseEntity.ok(result);
    }

    /**
     * 清空错题本
     */
    @PostMapping("/clear-wrong")
    @ApiOperation("清空错题本")
    public ResponseEntity<Map<String, Object>> clearWrongQuestions(
            @RequestBody Map<String, Object> request) {
        Integer userId = (Integer) request.get("userId");
        Integer bankId = (Integer) request.get("bankId");

        Map<String, Object> result = practiceService.clearWrongQuestions(userId, bankId);
        return ResponseEntity.ok(result);
    }
}