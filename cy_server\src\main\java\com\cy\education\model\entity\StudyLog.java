package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 学习日志实体类
 */
@Data
@TableName("study_logs")
public class StudyLog {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 学习记录ID
     */
    private Integer recordId;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 课程ID
     */
    private Integer courseId;
    
    /**
     * 课时ID
     */
    private Integer lessonId;
    
    /**
     * 资源ID
     */
    private Integer resourceId;
    
    /**
     * 资源类型：video, file, article
     * 注意：此字段不在数据库中
     */
    @TableField(exist = false)
    private String resourceType;
    
    /**
     * 开始位置（秒）
     */
    private Integer startPosition;
    
    /**
     * 结束位置（秒）
     */
    private Integer endPosition;
    
    /**
     * 学习时长（秒）
     */
    private Integer duration;
    
    /**
     * 学习进度（百分比）
     */
    private Integer progress;
    
    /**
     * 是否完成：0未完成，1已完成
     */
    private Integer completed;
    
    /**
     * 学习时间
     */
    private LocalDateTime studyTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 