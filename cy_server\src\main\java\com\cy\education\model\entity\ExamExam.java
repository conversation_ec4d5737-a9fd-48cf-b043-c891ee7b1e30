package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试实体类
 */
@Data
@TableName("exam_exam")
public class ExamExam {
    
    /**
     * 考试ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 考试标题
     */
    private String title;
    
    /**
     * 考试描述
     */
    private String description;
    
    /**
     * 试卷ID
     */
    private Integer paperId;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 考试时长(分钟)
     */
    private Integer duration;
    
    /**
     * 及格分数
     */
    private Integer passingScore;
    
    /**
     * 最大考试次数
     */
    private Integer maxAttempts;
    
    /**
     * 是否发布(0-未发布,1-已发布)
     */
    private Boolean isPublished;
    
    /**
     * 状态(0-草稿,1-未开始,2-进行中,3-已结束)
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建人ID
     */
    private String createdBy;
    
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;
} 