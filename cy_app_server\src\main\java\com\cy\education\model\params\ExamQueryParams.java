package com.cy.education.model.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考试查询参数类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("考试查询参数")
public class ExamQueryParams extends PageParams {

    /**
     * 关键字(标题、描述)
     */
    @ApiModelProperty("关键字(标题、描述)")
    private String keyword;

    /**
     * 试卷ID
     */
    @ApiModelProperty("试卷ID")
    private Integer paperId;

    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    private Integer departmentId;

    /**
     * 状态(0-草稿,1-未开始,2-进行中,3-已结束)
     */
    @ApiModelProperty("状态(0-草稿,1-未开始,2-进行中,3-已结束)")
    private Integer status;

    /**
     * 是否已发布
     */
    @ApiModelProperty("是否已发布")
    private Boolean isPublished;

    /**
     * 排序字段
     */
    @ApiModelProperty("排序字段")
    private String sortBy;

    /**
     * 排序方式(asc升序,desc降序)
     */
    @ApiModelProperty("排序方式(asc升序,desc降序)")
    private String sortOrder;
}
