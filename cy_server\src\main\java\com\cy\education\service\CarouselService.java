package com.cy.education.service;

import com.cy.education.model.entity.Carousel;
import com.cy.education.model.vo.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;

/**
 * 轮播图服务接口
 */
public interface CarouselService {
    
    /**
     * 分页查询轮播图列表
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    PageResponse<Carousel> listCarousels(ContentQueryParam param);
    
    /**
     * 获取轮播图详情
     * 
     * @param id 轮播图ID
     * @return 轮播图信息
     */
    Carousel getCarouselById(Integer id);
    
    /**
     * 新增轮播图
     * 
     * @param carousel 轮播图信息
     * @return 是否成功
     */
    Integer addCarousel(Carousel carousel);
    
    /**
     * 更新轮播图
     * 
     * @param carousel 轮播图信息
     * @return 是否成功
     */
    boolean updateCarousel(Carousel carousel);
    
    /**
     * 删除轮播图
     * 
     * @param id 轮播图ID
     * @return 是否成功
     */
    boolean deleteCarousel(Integer id);
    
    /**
     * 更新轮播图状态
     * 
     * @param id 轮播图ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateCarouselStatus(Integer id, Integer status);
} 