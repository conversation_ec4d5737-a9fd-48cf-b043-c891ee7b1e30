package com.cy.education.model.entity.study;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 学习记录实体类
 */
@Data
@TableName("study_records")
public class StudyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 课程ID
     */
    private Integer courseId;

    /**
     * 课时ID
     */
    private Integer lessonId;

    /**
     * 资源ID
     */
    private Integer resourceId;

    /**
     * 资源类型：video, file, article
     */
    private String resourceType;

    /**
     * 学习进度（百分比）
     */
    private Integer progress;

    /**
     * 学习时长（秒）
     */
    private Integer duration;

    /**
     * 是否完成：0未完成，1已完成
     */
    private Integer completed;

    /**
     * 最后学习位置（秒）
     */
    private Integer lastPosition;

    /**
     * 最后学习时间
     */
    private LocalDateTime lastStudyTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 学员姓名（非数据库字段）
     */
    @TableField(exist = false)
    private String studentName;

    /**
     * 部门名称（非数据库字段）
     */
    @TableField(exist = false)
    private String departmentName;

    /**
     * 课程名称（非数据库字段）
     */
    @TableField(exist = false)
    private String courseName;

    /**
     * 课时名称（非数据库字段）
     */
    @TableField(exist = false)
    private String lessonName;
}
