package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.Student;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.StudentMapper;
import com.cy.education.service.StudentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学员服务实现
 */
@Service
@Slf4j
public class StudentServiceImpl implements StudentService {

    @Autowired
    private StudentMapper studentMapper;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public Student getStudentById(Integer id) {
        Student student = studentMapper.selectStudentById(id);
        if (student == null) {
            throw new BusinessException("学员不存在");
        }

        // 处理学员字段，填充前端所需字段
        processStudentFields(student);
        return student;
    }

    /**
     * 处理学员字段，填充前端所需字段
     */
    private void processStudentFields(Student student) {
        // 设置注册时间
        if (student.getCreatedAt() != null) {
            student.setRegisterTime(student.getCreatedAt().format(DATE_TIME_FORMATTER));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStudent(Student student) {
        // 1. 检查学员是否存在
        Student existingStudent = studentMapper.selectById(student.getId());
        if (existingStudent == null) {
            throw new BusinessException("学员不存在");
        }

        // 2. 检查手机号是否已被其他学员使用
        if (!StringUtils.isEmpty(student.getPhone()) && !student.getPhone().equals(existingStudent.getPhone())) {
            LambdaQueryWrapper<Student> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Student::getPhone, student.getPhone())
                    .ne(Student::getId, student.getId());
            if (studentMapper.selectCount(queryWrapper) > 0) {
                throw new BusinessException("手机号已被使用");
            }
        }

        return studentMapper.updateById(student) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStudentPoints(Integer userId, Integer points) {
        if (userId == null || points == null) {
            log.error("更新学生积分参数无效: userId={}, points={}", userId, points);
            return false;
        }

        try {
            // 1. 检查学员是否存在
            Student existingStudent = studentMapper.selectById(userId);
            if (existingStudent == null) {
                log.error("更新学生积分失败: 学员不存在，userId={}", userId);
                return false;
            }

            // 2. 更新积分
            Student student = new Student();
            student.setId(userId);
            student.setPoints(points);
            student.setUpdatedAt(LocalDateTime.now());

            // 3. 更新数据库
            int result = studentMapper.updateById(student);
            if (result <= 0) {
                log.error("更新学生积分失败: 数据库更新返回0行，userId={}, points={}", userId, points);
                return false;
            }

            log.info("更新学生积分成功: userId={}, name={}, 新积分={}", userId, existingStudent.getName(), points);
            return true;
        } catch (Exception e) {
            log.error("更新学生积分异常: userId={}, points={}", userId, points, e);
            throw new RuntimeException("更新学生积分失败", e);
        }
    }

    // =========================== 论坛相关用户功能 ===========================

    @Override
    public Map<String, Object> getUserProfile(Integer userId, Integer currentUserId) {
        try {
            // 1. 获取用户基本信息
            Student student = studentMapper.selectStudentById(userId);
            if (student == null) {
                throw new BusinessException("用户不存在");
            }

            // 2. 构建用户资料
            Map<String, Object> profile = new HashMap<>();
            profile.put("id", student.getId());
            profile.put("name", student.getName());
            profile.put("avatar", student.getAvatar());
            profile.put("jobTitle", student.getJobTitle());
            profile.put("employeeId", student.getEmployeeId());
            profile.put("departmentName", student.getDepartmentName());

            // 3. 获取统计数据
            profile.put("postCount", studentMapper.getUserPostCount(userId));
            profile.put("followerCount", studentMapper.getUserFollowerCount(userId));
            profile.put("followingCount", studentMapper.getUserFollowingCount(userId));
            profile.put("likeCount", studentMapper.getUserLikeCount(userId));

            // 4. 检查关注状态
            if (currentUserId != null && !currentUserId.equals(userId)) {
                int followCount = studentMapper.checkUserFollow(currentUserId, userId);
                profile.put("isFollowing", followCount > 0);
            } else {
                profile.put("isFollowing", false);
            }
            return profile;
        } catch (Exception e) {
            log.error("获取用户资料失败: userId={}, currentUserId={}", userId, currentUserId, e);
            throw new BusinessException("获取用户资料失败: " + e.getMessage());
        }
    }

    @Override
    public PageResponse<Map<String, Object>> getUserFollowers(Integer userId, Integer pageNum, Integer pageSize) {
        try {
            Page<Map<String, Object>> page = new Page<>(pageNum, pageSize);
            IPage<Map<String, Object>> resultPage = studentMapper.getUserFollowers(page, userId);
            return PageResponse.of(
                    resultPage.getRecords(),
                    resultPage.getTotal(),
                    resultPage.getCurrent(),
                    resultPage.getSize()
            );
        } catch (Exception e) {
            log.error("获取用户粉丝失败: userId={}, pageNum={}, pageSize={}", userId, pageNum, pageSize, e);
            throw new BusinessException("获取用户粉丝失败: " + e.getMessage());
        }
    }

    @Override
    public PageResponse<Map<String, Object>> getUserFollowing(Integer userId, Integer pageNum, Integer pageSize) {
        try {
            Page<Map<String, Object>> page = new Page<>(pageNum, pageSize);
            IPage<Map<String, Object>> resultPage = studentMapper.getUserFollowing(page, userId);
            return PageResponse.of(
                    resultPage.getRecords(),
                    resultPage.getTotal(),
                    resultPage.getCurrent(),
                    resultPage.getSize()
            );
        } catch (Exception e) {
            log.error("获取用户关注列表失败: userId={}, pageNum={}, pageSize={}", userId, pageNum, pageSize, e);
            throw new BusinessException("获取用户关注列表失败: " + e.getMessage());
        }
    }
}
