package com.cy.education.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 课程预览VO
 */
@Data
public class CoursePreviewVO {
    
    /**
     * 课程ID
     */
    private Integer id;
    
    /**
     * 课程名称
     */
    private String title;
    
    /**
     * 封面图片路径
     */
    private String cover;
    
    /**
     * 课程描述
     */
    private String description;
    
    /**
     * 课程级别：beginner, intermediate, advanced
     */
    private String level;
    
    /**
     * 课程总时长(分钟)
     */
    private Integer duration;
    
    /**
     * 是否允许快进：0不允许，1允许
     */
    private Integer allowFastForward;
    
    /**
     * 课程章节列表
     */
    private List<CoursePreviewSectionVO> sections;
    
    /**
     * 课程章节预览VO
     */
    @Data
    public static class CoursePreviewSectionVO {
        
        /**
         * 章节ID
         */
        private Integer id;
        
        /**
         * 章节标题
         */
        private String title;
        
        /**
         * 章节时长(分钟)
         */
        private Integer duration;
        
        /**
         * 课时列表
         */
        private List<CoursePreviewLessonVO> lessons;
    }
    
    /**
     * 课程课时预览VO
     */
    @Data
    public static class CoursePreviewLessonVO {
        
        /**
         * 课时ID
         */
        private Integer id;
        
        /**
         * 课时标题
         */
        private String title;
        
        /**
         * 课时类型：video, document, quiz
         */
        private String type;
        
        /**
         * 课时时长(分钟)
         */
        private Integer duration;
        
        /**
         * 是否可预览（免费试看）
         */
        private Boolean previewable;
    }
} 