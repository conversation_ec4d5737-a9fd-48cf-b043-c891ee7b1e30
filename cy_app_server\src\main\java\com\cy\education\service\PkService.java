package com.cy.education.service;

import com.cy.education.model.entity.PkRoom;
import com.cy.education.model.entity.PkParticipant;
import com.cy.education.model.entity.PkAnswer;

import java.util.List;
import java.util.Map;

/**
 * PK服务接口
 */
public interface PkService {
    
    /**
     * 开始匹配
     * @param userId 用户ID
     * @param bankId 题库ID
     * @param questionCount 题目数量
     * @param timeLimit 时间限制
     * @return 匹配结果
     */
    Map<String, Object> startMatch(Integer userId, Integer bankId, Integer questionCount, Integer timeLimit);
    
    /**
     * 取消匹配
     * @param userId 用户ID
     * @return 取消结果
     */
    Map<String, Object> cancelMatch(Integer userId);
    
    /**
     * 创建房间
     * @param creatorId 创建者ID
     * @param bankId 题库ID
     * @param questionCount 题目数量
     * @param timeLimit 时间限制
     * @return 房间信息
     */
    PkRoom createRoom(Integer creatorId, Integer bankId, Integer questionCount, Integer timeLimit);
    
    /**
     * 加入房间
     * @param roomCode 房间码
     * @param userId 用户ID
     * @return 房间信息
     */
    Map<String, Object> joinRoom(String roomCode, Integer userId);
    
    /**
     * 离开房间
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Map<String, Object> leaveRoom(Long roomId, Integer userId);
    
    /**
     * 准备游戏
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Map<String, Object> readyGame(Long roomId, Integer userId);
    
    /**
     * 开始游戏
     * @param roomId 房间ID
     * @return 游戏信息
     */
    Map<String, Object> startGame(Long roomId);
    
    /**
     * 提交答案
     * @param roomId 房间ID
     * @param userId 用户ID
     * @param questionId 题目ID
     * @param questionOrder 题目顺序
     * @param userAnswer 用户答案
     * @param answerTime 答题时间
     * @return 答题结果
     */
    Map<String, Object> submitAnswer(Long roomId, Integer userId, Integer questionId, 
                                   Integer questionOrder, String userAnswer, Integer answerTime);
    
    /**
     * 获取房间信息
     * @param roomId 房间ID
     * @return 房间信息
     */
    Map<String, Object> getRoomInfo(Long roomId);
    
    /**
     * 获取游戏结果
     * @param roomId 房间ID
     * @return 游戏结果
     */
    Map<String, Object> getGameResult(Long roomId);
    
    /**
     * 获取用户PK历史
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return PK历史
     */
    Map<String, Object> getUserPkHistory(Integer userId, Integer page, Integer size);
}
