package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.forum.ForumComment;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 论坛评论Mapper接口
 */
@Repository
public interface ForumCommentMapper extends BaseMapper<ForumComment> {

    /**
     * 查询评论列表（带作者和帖子信息）
     *
     * @param page    分页对象
     * @param postId  帖子ID
     * @param keyword 搜索关键词
     * @param status  状态
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT c.*, s.name as author, s.avatar as author_avatar, p.title as post_title " +
            "FROM forum_comments c " +
            "LEFT JOIN students s ON c.author_id = s.id " +
            "LEFT JOIN forum_posts p ON c.post_id = p.id " +
            "<where> " +
            "<if test='postId != null'> AND c.post_id = #{postId} </if> " +
            "<if test='keyword != null and keyword != \"\"'> AND c.content LIKE CONCAT('%', #{keyword}, '%') </if> " +
            "<if test='status != null'> AND c.status = #{status} </if> " +
            "AND c.status != 3 " + // 不显示已删除的评论
            "</where> " +
            "ORDER BY c.created_at DESC" +
            "</script>")
    IPage<ForumComment> selectCommentPage(Page<ForumComment> page,
            @Param("postId") Integer postId,
            @Param("keyword") String keyword,
            @Param("status") Integer status);

    /**
     * 查询帖子的顶级评论（带作者信息）
     *
     * @param postId 帖子ID
     * @return 评论列表
     */
    @Select("SELECT c.*, s.name as author, s.avatar as author_avatar " +
            "FROM forum_comments c " +
            "LEFT JOIN students s ON c.author_id = s.id " +
            "WHERE c.post_id = #{postId} AND c.parent_id IS NULL AND c.status = 1 " + // 状态1表示已通过
            "ORDER BY c.created_at DESC")
    List<ForumComment> selectTopCommentsByPostId(@Param("postId") Integer postId);

    /**
     * 查询帖子的顶级评论（带作者信息，支持排序，包含用户交互状态）
     *
     * @param postId 帖子ID
     * @param sortBy 排序方式：latest-最新，hot-最热
     * @param userId 当前用户ID
     * @return 评论列表
     */
    @Select("<script>" +
            "SELECT c.*, s.name as author, s.avatar as author_avatar, " +
            "CASE WHEN cl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked " +
            "FROM forum_comments c " +
            "LEFT JOIN students s ON c.author_id = s.id " +
            "LEFT JOIN forum_comment_likes cl ON c.id = cl.comment_id AND cl.user_id = #{userId} " +
            "WHERE c.post_id = #{postId} AND c.parent_id IS NULL AND c.status = 1 " + // 状态1表示已通过
            "<choose> " +
            "<when test='sortBy == \"latest\"'> " +
            "ORDER BY c.created_at DESC " +
            "</when> " +
            "<when test='sortBy == \"hot\"'> " +
            "ORDER BY c.like_count DESC, c.created_at DESC " +
            "</when> " +
            "<otherwise> " +
            "ORDER BY c.created_at DESC " +
            "</otherwise> " +
            "</choose> " +
            "</script>")
    List<ForumComment> selectTopCommentsByPostIdWithSort(@Param("postId") Integer postId,
            @Param("sortBy") String sortBy, @Param("userId") Integer userId);

    /**
     * 查询评论的子评论（带作者信息，包含用户交互状态）
     *
     * @param parentId 父评论ID
     * @param userId   当前用户ID
     * @return 子评论列表
     */
    @Select("SELECT c.*, s.name as author, s.avatar as author_avatar, " +
            "CASE WHEN cl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked " +
            "FROM forum_comments c " +
            "LEFT JOIN students s ON c.author_id = s.id " +
            "LEFT JOIN forum_comment_likes cl ON c.id = cl.comment_id AND cl.user_id = #{userId} " +
            "WHERE c.parent_id = #{parentId} AND c.status = 1 " + // 状态1表示已通过
            "ORDER BY c.created_at ASC")
    List<ForumComment> selectChildCommentsByParentId(@Param("parentId") Integer parentId,
            @Param("userId") Integer userId);

    /**
     * 更新评论审核状态
     *
     * @param commentId 评论ID
     * @param status    状态
     * @return 影响行数
     */
    @Update("UPDATE forum_comments SET status = #{status} WHERE id = #{commentId}")
    int updateStatus(@Param("commentId") Integer commentId, @Param("status") Integer status);

    /**
     * 假删除评论（更新状态为已删除）
     *
     * @param commentId 评论ID
     * @return 影响行数
     */
    @Update("UPDATE forum_comments SET status = 3 WHERE id = #{commentId}")
    int softDeleteComment(@Param("commentId") Integer commentId);

    /**
     * 通过ID获取评论信息，包括已删除的评论
     *
     * @param commentId 评论ID
     * @return 评论信息
     */
    @Select("SELECT c.*, s.name as author, s.avatar as author_avatar, p.title as post_title " +
            "FROM forum_comments c " +
            "LEFT JOIN students s ON c.author_id = s.id " +
            "LEFT JOIN forum_posts p ON c.post_id = p.id " +
            "WHERE c.id = #{commentId}")
    ForumComment selectCommentWithDetailsById(@Param("commentId") Integer commentId);

    /**
     * 统计帖子下的评论数量
     *
     * @param postId 帖子ID
     * @return 评论数量
     */
    @Select("SELECT COUNT(*) FROM forum_comments WHERE post_id = #{postId} AND status = 1")
    // 状态1表示已通过
    int countCommentsByPostId(@Param("postId") Integer postId);
}
