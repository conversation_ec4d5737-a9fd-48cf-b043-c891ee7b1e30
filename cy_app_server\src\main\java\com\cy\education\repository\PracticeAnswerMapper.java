package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.PracticeAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 练习答案Mapper接口
 */
@Mapper
public interface PracticeAnswerMapper extends BaseMapper<PracticeAnswer> {
    
    /**
     * 根据练习记录ID查询答案
     * @param recordId 练习记录ID
     * @return 答案列表
     */
    List<PracticeAnswer> selectByRecordId(@Param("recordId") Integer recordId);
    
    /**
     * 根据用户ID和题目ID查询错题记录
     * @param userId 用户ID
     * @param questionId 题目ID
     * @return 错题记录
     */
    List<PracticeAnswer> selectWrongAnswersByUserIdAndQuestionId(@Param("userId") Integer userId, @Param("questionId") Integer questionId);
    
    /**
     * 根据用户ID查询错题记录
     * @param userId 用户ID
     * @return 错题记录列表
     */
    List<PracticeAnswer> selectWrongAnswersByUserId(@Param("userId") Integer userId);
    
    /**
     * 根据用户ID和题库ID查询错题记录
     * @param userId 用户ID
     * @param bankId 题库ID
     * @return 错题记录列表
     */
    List<PracticeAnswer> selectWrongAnswersByUserIdAndBankId(@Param("userId") Integer userId, @Param("bankId") Integer bankId);
} 