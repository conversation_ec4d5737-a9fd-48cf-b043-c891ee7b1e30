<template>
  <view class="profile-container">
    <!-- 顶部渐变个人信息卡 -->
    <view class="header-section">
      <!-- 个人信息卡片 -->
      <view class="profile-card">
        <view class="profile-info" @click="viewProfile">
          <up-image
              :src="userInfo.avatar"
              width="70"
              height="70"
              shape="circle"
              class="avatar"
          ></up-image>
          <view class="user-details">
            <text class="username">{{ userInfo.name }}</text>
            <text class="job-info">{{ userInfo.departmentName }} | {{ userInfo.jobTitle }}</text>
            <text class="employee-id">工号：{{ userInfo.employeeId || 'N/A' }}</text>
          </view>
        </view>
        <view class="points-section">
          <text class="points-label">我的积分</text>
          <text class="points-value">{{ userInfo.points || 0 }}</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <!-- 学习统计数据 -->
      <!--      <view class="stats-section">-->
      <!--        <view class="stats-grid">-->
      <!--          <view class="stat-card">-->
      <!--            <view class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">-->
      <!--              <up-icon name="checkmark-circle" color="#fff" size="20"></up-icon>-->
      <!--            </view>-->
      <!--            <text class="stat-number">{{ learningStats.completedCourses }}</text>-->
      <!--            <text class="stat-label">完成课程</text>-->
      <!--          </view>-->
      <!--          <view class="stat-card">-->
      <!--            <view class="stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">-->
      <!--              <up-icon name="clock" color="#fff" size="20"></up-icon>-->
      <!--            </view>-->
      <!--            <text class="stat-number">{{ learningStats.totalHours }}h</text>-->
      <!--            <text class="stat-label">学习时长</text>-->
      <!--          </view>-->
      <!--          <view class="stat-card">-->
      <!--            <view class="stat-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">-->
      <!--              <up-icon name="integral" color="#fff" size="20"></up-icon>-->
      <!--            </view>-->
      <!--            <text class="stat-number">{{ learningStats.ranking }}</text>-->
      <!--            <text class="stat-label">部门排名</text>-->
      <!--          </view>-->
      <!--        </view>-->
      <!--      </view>-->

      <!-- 快捷功能 -->
      <view class="function-section">
        <view class="section-title">我的功能</view>
        <view class="function-grid">
          <view
              v-for="item in functionList"
              :key="item.name"
              class="function-item"
              @click="handleFunctionClick(item)"
          >
            <view class="function-icon" :style="{ background: item.gradient }">
              <up-icon :name="item.icon" color="#fff" size="20"></up-icon>
              <up-badge
                  v-if="item.badge"
                  :value="item.badge"
                  :offset="[-2, -2]"
                  :absolute="true"
              ></up-badge>
            </view>
            <text class="function-name">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <!-- 系统功能 -->
      <view class="system-section">
        <view class="system-list">
          <view
              v-for="item in systemList"
              :key="item.name"
              class="system-item"
              @click="handleFunctionClick(item)"
          >
            <view class="system-left">
              <up-icon :name="item.icon" :color="item.color" size="20"></up-icon>
              <text class="system-name">{{ item.name }}</text>
            </view>
            <view class="system-right">
              <text v-if="item.value" class="system-value">{{ item.value }}</text>
              <up-icon name="arrow-right" color="#ccc" size="14"></up-icon>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <custom-tabbar :current="4"></custom-tabbar>
  </view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar/index.vue'
import {getUserInfo} from '@/api/user'

export default {
  components: {
    CustomTabbar
  },
  data() {
    return {
      userInfo: {
        id: 0,
        name: '',
        jobTitle: '',
        employeeId: '',
        avatar: '',
        department_id: 0,
        points: 0,
        departmentName: ''
      },
      learningStats: {
        completedCourses: 5,
        totalHours: 23,
        certificates: 2,
        ranking: 8
      },
      functionList: [
        {
          name: "学习记录",
          icon: "play-circle",
          gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          path: "/pages/study/records"
        },
        {
          name: "考试记录",
          icon: "file-text",
          gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
          path: "/pages/exam/records"
        },
        {
          name: "我的帖子",
          icon: "chat",
          gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
          path: "/pages/profile/my-posts"
        },
        {
          name: "我的收藏",
          icon: "bookmark",
          gradient: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
          path: "/pages/profile/my-collections"
        },
        {
          name: "积分商城",
          icon: "gift",
          gradient: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
          path: "/pages/points/mall"
        }
      ],
      systemList: [
        {name: "个人信息", icon: "account", color: "#667eea", path: "/pages/profile/edit"},
        {name: "积分明细", icon: "integral", color: "#f5ce43", path: "/pages/points/records"},
        {name: "兑换明细", icon: "gift", color: "#f5576c", path: "/pages/points/exchange-records"}, // todo 学习统计页面
        {name: "学习统计", icon: "hourglass", color: "#4facfe", path: "/pages/study/records"},
        {name: "系统设置", icon: "setting", color: "#43e97b", path: "/pages/profile/settings"}
      ]
    }
  },

  onShow() {
    this.loadUserProfile();
  },

  methods: {
    async loadUserProfile() {
      this.userInfo = await getUserInfo();
    },

    handleFunctionClick(item) {
      if (item.path) {
        uni.navigateTo({
          url: item.path
        });
      }
    },
    // 查看用户资料
    viewProfile() {
      const userId = this.userInfo.id || 0;
      uni.navigateTo({
        url: `/pages/profile/user-profile?userId=${userId}`
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/profile/index.scss";
</style>
