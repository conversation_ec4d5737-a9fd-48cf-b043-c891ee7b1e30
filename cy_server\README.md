# 程远教育培训管理系统 - 后端服务

## 项目简介

程远教育培训管理系统是一个企业内部的培训管理平台，帮助企业管理培训课程、考试、积分、论坛等功能。本项目是系统的后端服务部分，基于Spring Boot框架开发。

## 技术栈

- Spring Boot 2.7.14
- Spring Security
- MyBatis Plus
- Redis
- MySQL
- JWT
- Knife4j (Swagger文档)

## 系统要求

- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis

## 配置说明

数据库和Redis配置位于`src/main/resources/application.yml`文件中：

```yaml
spring:
  datasource:
    url: *******************************************************************************************************************
    username: test
    password: seBRPGJp5ti2xyic
  redis:
    host: **********
    port: 6379
    password: NEUneu123
```

## 构建与运行

### 使用Maven

```bash
# 编译
mvn clean compile

# 打包
mvn clean package

# 运行
java -jar target/education-0.0.1-SNAPSHOT.jar
```

### 使用IDE

直接运行`com.cy.education.EducationApplication`类的main方法。

## 数据库初始化

项目首次运行前，需要初始化数据库。使用以下步骤：

1. 确保MySQL服务已启动，并且可以连接到远程数据库（**********:3306）
2. 使用`src/main/resources/db/init.sql`脚本初始化数据库：

```bash
# 方法1：使用MySQL命令行客户端
mysql -h ********** -u test -p < src/main/resources/db/init.sql
# 输入密码: seBRPGJp5ti2xyic

# 方法2：使用MySQL Workbench或其他GUI工具导入并执行脚本
```

初始化后，系统会创建一个管理员账号：
- 用户名: admin
- 密码: 123456

## API文档

系统启动后，可通过以下地址访问API文档：

```
http://localhost:8080/api/doc.html
```

## 项目结构

```
src/main/java/com/cy/education/
├── config        // 配置类
├── controller    // 控制器
├── exception     // 异常处理
├── model         // 数据模型
│   ├── entity    // 实体类
│   ├── params    // 请求参数
│   └── vo        // 视图对象
├── repository    // 数据访问层
├── security      // 安全相关
├── service       // 业务逻辑层
└── utils         // 工具类
```

## 功能模块

- 用户认证与权限管理
- 学员管理
- 部门管理
- 课程管理
- 考试管理
- 积分管理
- 论坛管理
- 内容管理
- 文件资源管理
- 统计分析 