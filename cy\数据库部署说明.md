# 程远教育培训管理系统 - 数据库部署说明

## 环境要求

### 数据库要求
- **MySQL**: 8.0 或更高版本
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB

### 系统要求
- **操作系统**: Linux/Windows/macOS
- **内存**: 至少 4GB RAM
- **磁盘空间**: 至少 20GB 可用空间

## 快速部署

### 1. 创建数据库
```bash
# 连接MySQL服务器
mysql -u root -p

# 或者使用数据库管理工具如Navicat、phpMyAdmin等
```

### 2. 执行初始化脚本
```sql
-- 直接执行初始化脚本
source /path/to/数据库初始化脚本.sql;

-- 或者复制粘贴脚本内容执行
```

### 3. 验证安装
```sql
-- 检查数据库
USE education_training;

-- 检查表数量
SELECT COUNT(*) as table_count FROM information_schema.tables 
WHERE table_schema = 'education_training';

-- 检查初始数据
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as role_count FROM roles;
SELECT COUNT(*) as department_count FROM departments;
```

## 默认账号信息

### 管理员账号
- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: 超级管理员
- **权限**: 所有模块的完整权限

### 普通用户账号
- **用户名**: `user`  
- **密码**: `user123`
- **角色**: 系统管理员
- **权限**: 除用户管理外的大部分权限

### 教师账号
- **用户名**: `teacher1`
- **密码**: `teacher123`
- **角色**: 教师
- **权限**: 课程管理、考试参与等

### 学员账号
- **用户名**: `student1`
- **密码**: `student123`
- **角色**: 学员
- **权限**: 课程学习、考试参与等

## 数据库结构概览

### 核心表统计
- **用户权限模块**: 4个表 (删除了角色相关表)
- **课程管理模块**: 4个表  
- **考试管理模块**: 7个表 (新增题库表)
- **积分管理模块**: 4个表
- **论坛管理模块**: 4个表
- **内容管理模块**: 3个表
- **文件资源模块**: 1个表

**总计**: 27个主要业务表

**重要**: 数据库设计遵循无冗余原则，所有统计数据通过SQL查询实时计算

**企业内部培训特化**: 删除了价格、评分、证书等不必要功能，专注核心培训需求

### 主要外键关系
```
departments (部门) ← users (用户) ← user_permissions → permissions (权限)
users ← courses (课程) ← course_sections ← course_lessons
users + courses → study_records (学习记录)
users ← question_banks (题库) ← questions (题目) → paper_questions ← papers (试卷) ← exams (考试)
users + exams → exam_records ← exam_answers
users ← points_records (积分记录)
users ← points_exchanges → points_products (积分商品)
users ← forum_posts → forum_categories
users ← forum_comments → forum_posts
```

### 学习记录设计说明

#### 记录模式
- **每次学习单独记录**: 每次用户学习课程或课时都会产生一条学习记录
- **支持课时级别记录**: 可以记录到具体的课时学习情况
- **无冗余统计**: 不存储总时长、最后学习时间等可计算字段

#### 统计数据获取
```sql
-- 用户课程总学习时长
SELECT SUM(study_time) FROM study_records 
WHERE user_id = ? AND course_id = ?;

-- 用户最后学习时间  
SELECT MAX(created_at) FROM study_records 
WHERE user_id = ? AND course_id = ?;

-- 课程学员数量
SELECT COUNT(DISTINCT user_id) FROM study_records 
WHERE course_id = ?;

-- 考试参与/完成/通过人数
SELECT 
  COUNT(DISTINCT user_id) as participant_count,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
  COUNT(CASE WHEN passed = 1 THEN 1 END) as passed_count
FROM exam_records WHERE exam_id = ?;
```

## 业务数据初始化

### 部门结构
```
总公司
├── 安全生产部
│   ├── 矿山一部  
│   └── 矿山二部
├── 人力资源部
└── 技术部
    └── 设备维护组
```

### 权限体系
- **超级管理员 (admin001)**: 所有权限
- **系统管理员 (user001)**: 除用户管理外的管理权限
- **教师 (teacher001)**: 课程管理、考试参与权限
- **学员 (student001)**: 基础学习和参与权限

**注意**: 系统采用用户直接关联权限的模式，无角色概念

### 题库设置
- 安全知识题库 (全体员工)
- 技术能力题库 (技术部门)
- 管理知识题库 (管理人员)
- 应急处理题库 (全体员工)
- 设备维护题库 (技术部门)

### 论坛分类
- 安全培训 (新手指导、设备安全)
- 技术交流 (操作规程、故障排除) 
- 政策法规
- 经验分享

### 积分规则
- **登录类**: 每日登录 +10分
- **学习类**: 完成课程 +50分，学习时长 +5分/30分钟
- **考试类**: 考试通过 +30分，考试满分 +100分
- **论坛类**: 发布帖子 +15分，回复帖子 +5分
- **其他类**: 每日签到 +5分

### 积分商品
- 安全头盔 (500积分)
- 防护手套 (200积分)
- 培训教材 (300积分)
- U盘 (150积分)
- 保温杯 (180积分)

## 性能优化建议

### 1. 索引优化
```sql
-- 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SET GLOBAL slow_query_log = 'ON';

-- 分析查询性能
EXPLAIN SELECT * FROM users WHERE department_id = 1;
EXPLAIN SELECT * FROM study_records WHERE user_id = 'user001';
```

### 2. 分区建议
```sql
-- 对于大数据量表，建议按时间分区
-- 积分记录表按月分区
ALTER TABLE points_records PARTITION BY RANGE (TO_DAYS(created_at)) (
    PARTITION p202401 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    -- 继续添加分区...
);

-- 考试记录表按年分区
ALTER TABLE exam_records PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    -- 继续添加分区...
);
```

### 3. 缓存策略
```sql
-- 开启查询缓存
SET GLOBAL query_cache_type = ON;
SET GLOBAL query_cache_size = 268435456; -- 256MB

-- 优化InnoDB缓冲池
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
```

## 数据备份策略

### 1. 全量备份
```bash
# 每日全量备份
mysqldump -u root -p education_training > backup_$(date +%Y%m%d).sql

# 压缩备份
mysqldump -u root -p education_training | gzip > backup_$(date +%Y%m%d).sql.gz
```

### 2. 增量备份
```bash
# 开启二进制日志
# 在my.cnf中添加：
# log-bin=mysql-bin
# binlog-format=ROW

# 增量备份脚本
#!/bin/bash
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p --single-transaction --routines --triggers education_training > $BACKUP_DIR/full_backup_$DATE.sql
```

### 3. 数据恢复
```bash
# 从备份恢复
mysql -u root -p education_training < backup_20241201.sql

# 从压缩备份恢复
gunzip < backup_20241201.sql.gz | mysql -u root -p education_training
```

## 监控和维护

### 1. 性能监控
```sql
-- 监控数据库大小
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'education_training'
GROUP BY table_schema;

-- 监控表大小
SELECT 
    table_name as 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'education_training'
ORDER BY (data_length + index_length) DESC;
```

### 2. 定期维护
```sql
-- 优化表
OPTIMIZE TABLE users, courses, exam_records, points_records;

-- 分析表
ANALYZE TABLE users, courses, exam_records, points_records;

-- 检查表
CHECK TABLE users, courses, exam_records, points_records;
```

### 3. 数据清理
```sql
-- 清理过期的考试记录（保留2年）
DELETE FROM exam_records 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR);

-- 清理过期的积分记录（保留3年）  
DELETE FROM points_records 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 YEAR);

-- 清理过期的论坛帖子（软删除，保留5年）
UPDATE forum_posts 
SET status = 'deleted' 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 5 YEAR) 
  AND status != 'deleted';
```

## 安全配置

### 1. 用户权限
```sql
-- 创建应用专用数据库用户
CREATE USER 'edu_app'@'localhost' IDENTIFIED BY 'strong_password_here';

-- 授予必要权限
GRANT SELECT, INSERT, UPDATE, DELETE ON education_training.* TO 'edu_app'@'localhost';

-- 创建只读用户（用于报表查询）
CREATE USER 'edu_readonly'@'localhost' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON education_training.* TO 'edu_readonly'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
```

### 2. 密码策略
```sql
-- 设置密码策略
SET GLOBAL validate_password.policy = STRONG;
SET GLOBAL validate_password.length = 8;
SET GLOBAL validate_password.mixed_case_count = 1;
SET GLOBAL validate_password.number_count = 1;
SET GLOBAL validate_password.special_char_count = 1;
```

### 3. 连接安全
```sql
-- 限制最大连接数
SET GLOBAL max_connections = 200;

-- 设置连接超时
SET GLOBAL wait_timeout = 300;
SET GLOBAL interactive_timeout = 300;
```

## 故障排查

### 1. 常见问题
```sql
-- 检查锁等待
SHOW ENGINE INNODB STATUS;

-- 查看当前连接
SHOW PROCESSLIST;

-- 检查表锁
SHOW OPEN TABLES WHERE In_use > 0;
```

### 2. 性能问题
```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 检查索引使用情况
SELECT 
    table_schema,
    table_name,
    index_name,
    cardinality
FROM information_schema.statistics 
WHERE table_schema = 'education_training'
ORDER BY cardinality DESC;
```

### 3. 空间问题
```sql
-- 检查碎片
SELECT 
    table_name,
    ROUND(data_length/1024/1024) as data_mb,
    ROUND(data_free/1024/1024) as free_mb
FROM information_schema.tables 
WHERE table_schema = 'education_training' 
  AND data_free > 0;
```

## 扩展配置

### 1. 主从复制
```sql
-- 主库配置 (my.cnf)
[mysqld]
server-id = 1
log-bin = mysql-bin
binlog-do-db = education_training

-- 从库配置 (my.cnf) 
[mysqld]
server-id = 2
relay-log = mysql-relay-bin
replicate-do-db = education_training
```

### 2. 读写分离
```yaml
# 应用配置示例
database:
  master:
    host: master-db-host
    database: education_training
    username: edu_app
    password: ${DB_PASSWORD}
  slaves:
    - host: slave1-db-host
      database: education_training
      username: edu_readonly
      password: ${DB_READONLY_PASSWORD}
    - host: slave2-db-host
      database: education_training  
      username: edu_readonly
      password: ${DB_READONLY_PASSWORD}
```

### 3. 集群部署
```yaml
# MySQL集群配置示例
version: '3.8'
services:
  mysql-master:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: education_training
    volumes:
      - mysql-master-data:/var/lib/mysql
      - ./init:/docker-entrypoint-initdb.d
    
  mysql-slave:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    volumes:
      - mysql-slave-data:/var/lib/mysql
    depends_on:
      - mysql-master
```

## 联系支持

如遇到数据库相关问题，请联系技术支持：
- **邮箱**: <EMAIL>
- **文档**: 查看完整的数据库设计方案文档
- **Wiki**: 内部技术文档库

---

**版本**: v1.0  
**更新时间**: 2024年12月  
**维护人员**: 数据库管理团队 