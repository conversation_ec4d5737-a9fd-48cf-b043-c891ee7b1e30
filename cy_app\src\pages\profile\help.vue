<template>
  <view class="help-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">帮助中心</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <!-- 搜索框 -->
      <view class="search-section">
        <up-search
            v-model="searchKeyword"
            :clearabled="true"
            :showAction="false"
            customStyle="background: #f8f9fa; border-radius: 12px;"
            placeholder="搜索问题或关键词"
            @clear="handleClear"
            @search="handleSearch"
        ></up-search>
      </view>

      <!-- 快速入口 -->
      <view class="quick-actions">
        <view class="action-item" @click="handleQuickAction('password')">
          <view class="action-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <up-icon color="#fff" name="lock" size="20"></up-icon>
          </view>
          <text class="action-text">密码问题</text>
        </view>
        <view class="action-item" @click="handleQuickAction('course')">
          <view class="action-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <up-icon color="#fff" name="play-circle" size="20"></up-icon>
          </view>
          <text class="action-text">课程学习</text>
        </view>
        <view class="action-item" @click="handleQuickAction('exam')">
          <view class="action-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <up-icon color="#fff" name="file-text" size="20"></up-icon>
          </view>
          <text class="action-text">考试相关</text>
        </view>
        <view class="action-item" @click="handleQuickAction('points')">
          <view class="action-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <up-icon color="#fff" name="integral" size="20"></up-icon>
          </view>
          <text class="action-text">积分问题</text>
        </view>
      </view>

      <!-- 问题分类 -->
      <view class="help-categories">
        <view
            v-for="category in filteredCategories"
            :key="category.id"
            class="category-section"
        >
          <view class="category-header">
            <up-icon :color="category.color" :name="category.icon" size="18"></up-icon>
            <text class="category-title">{{ category.title }}</text>
            <text class="category-count">({{ category.questions.length }})</text>
          </view>

          <view class="question-list">
            <view
                v-for="question in category.questions"
                :key="question.id"
                class="question-item"
                @click="showQuestionDetail(question)"
            >
              <view class="question-content">
                <text class="question-text">{{ question.question }}</text>
                <text class="question-desc">{{ question.description }}</text>
              </view>
              <up-icon color="#ccc" name="arrow-right" size="14"></up-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 联系客服 -->
      <view class="contact-section">
        <view class="contact-card">
          <view class="contact-header">
            <up-icon color="#667eea" name="kefu-ermai" size="24"></up-icon>
            <text class="contact-title">联系客服</text>
          </view>
          <view class="contact-content">
            <text class="contact-desc">如果以上问题无法解决您的问题，请联系我们的客服团队</text>
            <view class="contact-methods">
              <view class="contact-method" @click="handleContact('online')">
                <up-icon color="#667eea" name="chat" size="20"></up-icon>
                <text class="method-text">在线客服</text>
              </view>
              <view class="contact-method" @click="handleContact('phone')">
                <up-icon color="#43e97b" name="phone" size="20"></up-icon>
                <text class="method-text">电话客服</text>
              </view>
              <view class="contact-method" @click="handleContact('email')">
                <up-icon color="#f5576c" name="email" size="20"></up-icon>
                <text class="method-text">邮件支持</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 问题详情弹窗 -->
    <up-popup v-model="showDetail" :round="12" mode="center" width="90%">
      <view class="detail-popup">
        <view class="detail-header">
          <text class="detail-title">{{ currentQuestion.question }}</text>
          <view class="close-btn" @click="showDetail = false">
            <up-icon color="#999" name="close" size="16"></up-icon>
          </view>
        </view>
        <view class="detail-content">
          <text class="detail-text">{{ currentQuestion.answer }}</text>
          <view v-if="currentQuestion.steps" class="detail-steps">
            <text class="steps-title">操作步骤：</text>
            <view v-for="(step, index) in currentQuestion.steps" :key="index" class="step-item">
              <text class="step-number">{{ index + 1 }}</text>
              <text class="step-text">{{ step }}</text>
            </view>
          </view>
          <view v-if="currentQuestion.tips" class="detail-tips">
            <text class="tips-title">温馨提示：</text>
            <text class="tips-text">{{ currentQuestion.tips }}</text>
          </view>
        </view>
        <view class="detail-footer">
          <up-button
              customStyle="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 8px;"
              text="问题已解决"
              type="primary"
              @click="markAsSolved"
          ></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      showDetail: false,
      currentQuestion: {},
      helpCategories: [
        {
          id: 'account',
          title: '账户管理',
          icon: 'account',
          color: '#667eea',
          questions: [
            {
              id: '1',
              question: '如何修改密码？',
              description: '忘记密码或需要修改密码',
              answer: '您可以通过以下方式修改密码：1. 在个人中心点击"系统设置"；2. 选择"修改密码"；3. 输入当前密码和新密码；4. 点击确认修改。',
              steps: [
                '进入个人中心页面',
                '点击"系统设置"',
                '选择"修改密码"',
                '输入当前密码',
                '输入新密码（6-20位字符，包含数字和字母）',
                '确认新密码',
                '点击"确认修改"'
              ],
              tips: '新密码必须包含数字和字母，长度在6-20位之间。'
            },
            {
              id: '2',
              question: '如何绑定手机号？',
              description: '绑定手机号用于登录和找回密码',
              answer: '绑定手机号可以增强账户安全性，用于登录验证和密码找回。',
              steps: [
                '进入"系统设置"',
                '选择"账户安全"',
                '点击"绑定手机"',
                '输入手机号码',
                '获取并输入验证码',
                '点击"确认绑定"'
              ],
              tips: '一个手机号只能绑定一个账户。'
            },
            {
              id: '3',
              question: '如何绑定邮箱？',
              description: '绑定邮箱用于接收重要通知',
              answer: '绑定邮箱可以接收学习通知、系统公告等重要信息。',
              steps: [
                '进入"系统设置"',
                '选择"账户安全"',
                '点击"绑定邮箱"',
                '输入邮箱地址',
                '获取并输入验证码',
                '点击"确认绑定"'
              ],
              tips: '请确保邮箱地址正确，验证码有效期为5分钟。'
            }
          ]
        },
        {
          id: 'course',
          title: '课程学习',
          icon: 'play-circle',
          color: '#f5576c',
          questions: [
            {
              id: '4',
              question: '如何查看学习进度？',
              description: '了解自己的学习情况',
              answer: '您可以在个人中心查看详细的学习记录和进度统计。',
              steps: [
                '进入个人中心',
                '查看学习统计数据',
                '点击"学习记录"查看详细记录',
                '在"学习统计"页面查看分析报告'
              ],
              tips: '学习进度会实时更新，包括完成课程数、学习时长等。'
            },
            {
              id: '5',
              question: '课程无法播放怎么办？',
              description: '视频播放问题解决方案',
              answer: '如果课程无法正常播放，请尝试以下解决方案。',
              steps: [
                '检查网络连接是否正常',
                '尝试刷新页面',
                '清除浏览器缓存',
                '检查是否在支持的设备上使用',
                '联系客服获取帮助'
              ],
              tips: '建议使用稳定的WiFi网络观看视频课程。'
            },
            {
              id: '6',
              question: '如何下载课程资料？',
              description: '下载学习资料和文档',
              answer: '部分课程提供资料下载功能，您可以在课程详情页面下载相关资料。',
              steps: [
                '进入课程详情页面',
                '查看"课程资料"部分',
                '点击需要下载的文件',
                '选择保存位置',
                '等待下载完成'
              ],
              tips: '下载功能仅在WiFi环境下可用，请注意流量使用。'
            }
          ]
        },
        {
          id: 'exam',
          title: '考试相关',
          icon: 'file-text',
          color: '#4facfe',
          questions: [
            {
              id: '7',
              question: '如何参加在线考试？',
              description: '参加考试的操作流程',
              answer: '在线考试需要按照指定流程进行，确保考试顺利进行。',
              steps: [
                '在考试列表中选择要参加的考试',
                '仔细阅读考试说明和注意事项',
                '点击"开始考试"',
                '按照提示完成身份验证',
                '在规定时间内完成答题',
                '提交试卷'
              ],
              tips: '考试过程中请保持网络稳定，不要刷新页面。'
            },
            {
              id: '8',
              question: '考试中断怎么办？',
              description: '处理考试意外中断',
              answer: '如果考试过程中出现意外中断，系统会自动保存答题进度。',
              steps: [
                '重新登录系统',
                '进入考试页面',
                '系统会提示是否继续考试',
                '选择"继续考试"',
                '从上次中断的地方继续答题'
              ],
              tips: '建议在稳定的网络环境下参加考试，避免意外中断。'
            },
            {
              id: '9',
              question: '如何查看考试成绩？',
              description: '查看考试结果和成绩',
              answer: '考试完成后，您可以在考试记录中查看详细的成绩和解析。',
              steps: [
                '进入个人中心',
                '点击"考试记录"',
                '选择要查看的考试',
                '查看成绩和答题详情',
                '查看正确答案和解析'
              ],
              tips: '成绩通常在考试结束后24小时内公布。'
            }
          ]
        },
        {
          id: 'points',
          title: '积分系统',
          icon: 'integral',
          color: '#43e97b',
          questions: [
            {
              id: '10',
              question: '如何获得积分？',
              description: '积分获取方式说明',
              answer: '您可以通过多种方式获得积分，用于兑换礼品和学习资源。',
              steps: [
                '完成课程学习获得积分',
                '参加考试获得积分',
                '每日签到获得积分',
                '参与论坛讨论获得积分',
                '完成学习任务获得积分'
              ],
              tips: '积分获取规则可能会根据活动进行调整。'
            },
            {
              id: '11',
              question: '积分如何使用？',
              description: '积分兑换和使用',
              answer: '积分可以在积分商城中兑换各种礼品和学习资源。',
              steps: [
                '进入"积分商城"',
                '浏览可兑换的商品',
                '选择想要兑换的商品',
                '确认积分数量',
                '点击"立即兑换"',
                '填写收货信息'
              ],
              tips: '积分兑换后不可退回，请谨慎选择。'
            },
            {
              id: '12',
              question: '积分会过期吗？',
              description: '积分有效期说明',
              answer: '积分有一定的有效期，请在有效期内使用。',
              steps: [
                '查看积分明细',
                '了解积分有效期',
                '在有效期内使用积分',
                '关注积分到期提醒'
              ],
              tips: '建议及时使用积分，避免过期损失。'
            }
          ]
        },
        {
          id: 'technical',
          title: '技术问题',
          icon: 'setting',
          color: '#fa709a',
          questions: [
            {
              id: '13',
              question: '应用闪退怎么办？',
              description: '解决应用崩溃问题',
              answer: '如果应用频繁闪退，可以尝试以下解决方法。',
              steps: [
                '重启应用',
                '清除应用缓存',
                '检查设备存储空间',
                '更新应用到最新版本',
                '重启设备',
                '重新安装应用'
              ],
              tips: '如果问题持续存在，请联系客服获取技术支持。'
            },
            {
              id: '14',
              question: '网络连接失败？',
              description: '网络问题解决方案',
              answer: '网络连接问题可能由多种原因引起，请按步骤排查。',
              steps: [
                '检查设备网络设置',
                '尝试切换WiFi/移动网络',
                '重启路由器',
                '检查应用网络权限',
                '清除应用缓存',
                '联系网络服务商'
              ],
              tips: '建议在网络稳定的环境下使用应用。'
            },
            {
              id: '15',
              question: '如何清除缓存？',
              description: '清理应用缓存数据',
              answer: '定期清理缓存可以提升应用性能，释放存储空间。',
              steps: [
                '进入"系统设置"',
                '选择"应用设置"',
                '点击"清除缓存"',
                '确认清除操作',
                '等待清理完成'
              ],
              tips: '清除缓存不会影响您的学习记录和账户信息。'
            }
          ]
        }
      ]
    }
  },

  computed: {
    filteredCategories() {
      if (!this.searchKeyword) {
        return this.helpCategories
      }

      return this.helpCategories.map(category => ({
        ...category,
        questions: category.questions.filter(question =>
            question.question.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
            question.description.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
            question.answer.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      })).filter(category => category.questions.length > 0)
    }
  },

  methods: {
    // 搜索处理
    handleSearch() {
      // 搜索逻辑已在computed中处理
    },

    // 清除搜索
    handleClear() {
      this.searchKeyword = ''
    },

    // 快速操作
    handleQuickAction(type) {
      const actionMap = {
        password: '1',
        course: '4',
        exam: '7',
        points: '10'
      }

      const questionId = actionMap[type]
      if (questionId) {
        const question = this.findQuestionById(questionId)
        if (question) {
          this.showQuestionDetail(question)
        }
      }
    },

    // 根据ID查找问题
    findQuestionById(id) {
      for (const category of this.helpCategories) {
        const question = category.questions.find(q => q.id === id)
        if (question) {
          return question
        }
      }
      return null
    },

    // 显示问题详情
    showQuestionDetail(question) {
      this.currentQuestion = question
      this.showDetail = true
    },

    // 标记问题已解决
    markAsSolved() {
      this.showDetail = false
      uni.showToast({
        title: '感谢您的反馈',
        icon: 'success'
      })
    },

    // 联系客服
    handleContact(type) {
      const contactMap = {
        online: {
          title: '在线客服',
          content: '工作时间：周一至周五 9:00-18:00\n客服QQ：123456789'
        },
        phone: {
          title: '电话客服',
          content: '客服热线：************\n工作时间：周一至周日 8:00-22:00'
        },
        email: {
          title: '邮件支持',
          content: '客服邮箱：<EMAIL>\n我们会在24小时内回复您的邮件'
        }
      }

      const contact = contactMap[type]
      if (contact) {
        uni.showModal({
          title: contact.title,
          content: contact.content,
          showCancel: false
        })
      }
    },

    // 返回上一页
    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.help-container {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.content-container {
  padding: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 8px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-text {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.help-categories {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.category-section {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.category-count {
  font-size: 14px;
  color: #999;
}

.question-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:active {
    background: #e9ecef;
  }
}

.question-content {
  flex: 1;
  margin-right: 12px;
}

.question-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.question-desc {
  font-size: 12px;
  color: #666;
  display: block;
}

.contact-section {
  margin-bottom: 20px;
}

.contact-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.contact-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.contact-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.contact-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.contact-methods {
  display: flex;
  justify-content: space-around;
  gap: 16px;
}

.contact-method {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  flex: 1;
  transition: background-color 0.2s ease;

  &:active {
    background: #e9ecef;
  }
}

.method-text {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

// 弹窗样式
.detail-popup {
  background: #fff;
  border-radius: 16px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16px;
}

.close-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #f8f9fa;
}

.detail-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.detail-text {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16px;
}

.detail-steps {
  margin-bottom: 16px;
}

.steps-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
}

.step-number {
  width: 20px;
  height: 20px;
  background: #667eea;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.detail-tips {
  background: #fff5f5;
  border-left: 3px solid #ff6b6b;
  padding: 12px;
  border-radius: 0 8px 8px 0;
}

.tips-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.tips-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.detail-footer {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

</style>
