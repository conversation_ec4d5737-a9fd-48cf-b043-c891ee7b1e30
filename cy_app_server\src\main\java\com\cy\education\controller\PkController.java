package com.cy.education.controller;

import com.cy.education.service.PkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * PK控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/pk")
@Api(tags = "PK功能")
public class PkController {
    
    @Autowired
    private PkService pkService;
    
    /**
     * 开始匹配
     */
    @PostMapping("/match/start")
    @ApiOperation("开始匹配")
    public Map<String, Object> startMatch(
            @ApiParam("用户ID") @RequestParam Integer userId,
            @ApiParam("题库ID") @RequestParam Integer bankId,
            @ApiParam("题目数量") @RequestParam(defaultValue = "10") Integer questionCount,
            @ApiParam("时间限制") @RequestParam(defaultValue = "300") Integer timeLimit) {
        return pkService.startMatch(userId, bankId, questionCount, timeLimit);
    }
    
    /**
     * 取消匹配
     */
    @PostMapping("/match/cancel")
    @ApiOperation("取消匹配")
    public Map<String, Object> cancelMatch(@ApiParam("用户ID") @RequestParam Integer userId) {
        return pkService.cancelMatch(userId);
    }
    
    /**
     * 加入房间
     */
    @PostMapping("/room/join")
    @ApiOperation("加入房间")
    public Map<String, Object> joinRoom(
            @ApiParam("房间码") @RequestParam String roomCode,
            @ApiParam("用户ID") @RequestParam Integer userId) {
        return pkService.joinRoom(roomCode, userId);
    }
    
    /**
     * 离开房间
     */
    @PostMapping("/room/leave")
    @ApiOperation("离开房间")
    public Map<String, Object> leaveRoom(
            @ApiParam("房间ID") @RequestParam Long roomId,
            @ApiParam("用户ID") @RequestParam Integer userId) {
        return pkService.leaveRoom(roomId, userId);
    }
    
    /**
     * 准备游戏
     */
    @PostMapping("/room/ready")
    @ApiOperation("准备游戏")
    public Map<String, Object> readyGame(
            @ApiParam("房间ID") @RequestParam Long roomId,
            @ApiParam("用户ID") @RequestParam Integer userId) {
        return pkService.readyGame(roomId, userId);
    }
    
    /**
     * 提交答案
     */
    @PostMapping("/game/answer")
    @ApiOperation("提交答案")
    public Map<String, Object> submitAnswer(
            @ApiParam("房间ID") @RequestParam Long roomId,
            @ApiParam("用户ID") @RequestParam Integer userId,
            @ApiParam("题目ID") @RequestParam Integer questionId,
            @ApiParam("题目顺序") @RequestParam Integer questionOrder,
            @ApiParam("用户答案") @RequestParam String userAnswer,
            @ApiParam("答题时间") @RequestParam Integer answerTime) {
        return pkService.submitAnswer(roomId, userId, questionId, questionOrder, userAnswer, answerTime);
    }
    
    /**
     * 获取房间信息
     */
    @GetMapping("/room/{roomId}")
    @ApiOperation("获取房间信息")
    public Map<String, Object> getRoomInfo(@ApiParam("房间ID") @PathVariable Long roomId) {
        return pkService.getRoomInfo(roomId);
    }
    
    /**
     * 获取游戏结果
     */
    @GetMapping("/game/result/{roomId}")
    @ApiOperation("获取游戏结果")
    public Map<String, Object> getGameResult(@ApiParam("房间ID") @PathVariable Long roomId) {
        return pkService.getGameResult(roomId);
    }
    
    /**
     * 获取用户PK历史
     */
    @GetMapping("/history/{userId}")
    @ApiOperation("获取用户PK历史")
    public Map<String, Object> getUserPkHistory(
            @ApiParam("用户ID") @PathVariable Integer userId,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size) {
        return pkService.getUserPkHistory(userId, page, size);
    }
}
