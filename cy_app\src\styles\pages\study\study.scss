// 学习中心页面样式

// 分类标签栏
.category-tabs {
  background: #fff;
  border-bottom: 1px solid #f0f2f5;
  position: sticky;
  top: 0;
  z-index: 999;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  padding: 16px 20px;
  gap: 16px;
}

.tab-item {
  flex-shrink: 0;
  padding: 8px 20px;
  border-radius: 20px;
  background: #f5f7ff;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.tab-text {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
  white-space: nowrap;
}

.tab-item.active .tab-text {
  color: #fff;
  font-weight: 600;
}

// 页面内容
.page-content {
  padding: 20px;
  padding-bottom: 120px;
}

// 上次学习区域
.recent-study-section {
  margin-bottom: 24px;
}

.recent-study-card {
  background: #fff;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.more-btn {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: #f5f7ff;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.more-btn:active {
  background: #e8f2ff;
  transform: scale(0.95);
}

.more-text {
  font-size: 12px;
  color: #667eea;
  margin-right: 4px;
  font-weight: 500;
}

.recent-course {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  border-radius: 16px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
}

.recent-course:active {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.course-cover {
  position: relative;
  flex-shrink: 0;
}

.cover-image {
  border-radius: 12px;
  overflow: hidden;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.recent-course:active .play-overlay {
  opacity: 1;
}

.play-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.course-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.course-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
  line-height: 1.3;
  margin-bottom: 2px;
}

.course-instructor {
  font-size: 13px;
  color: #8e8e93;
  margin-bottom: 8px;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: #f0f2f5;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #667eea;
  min-width: 36px;
  font-weight: 600;
}

.last-study-time {
  font-size: 12px;
  color: #8e8e93;
}

.continue-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 12px 20px;
  align-self: flex-start;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.continue-btn:active {
  transform: scale(0.95);
}

.continue-btn .btn-text {
  font-size: 13px;
  color: white;
  font-weight: 600;
}

// 课程列表样式
.course-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.view-toggle {
  display: flex;
  background: #f5f7ff;
  border-radius: 12px;
  padding: 4px;
  gap: 4px;
}

.toggle-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

// 网格视图
// 列表视图
.course-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.course-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.course-item:active {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.item-cover {
  margin-right: 16px;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.item-title {
  font-size: 15px;
  font-weight: 600;
  color: #1a1d2e;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-instructor {
  font-size: 13px;
  color: #8e8e93;
}

.item-stats {
  display: flex;
  gap: 16px;
}

.item-action {
  margin-left: 16px;
  flex-shrink: 0;
}

.course-card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.course-card:active {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.course-cover-container {
  position: relative;
  height: 120px;
}

.course-cover {
  // width: 100%;
  height: 100%;
}

.course-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.course-card:active .course-overlay {
  opacity: 1;
}

.course-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: #fff;
}

.course-badge.new {
  background: #10b981;
}

.course-badge.hot {
  background: #f59e0b;
}

.course-info {
  padding: 16px;
}

.course-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1d2e;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-instructor {
  font-size: 12px;
  color: #8e8e93;
  margin-bottom: 12px;
}

.course-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-text {
  font-size: 11px;
  color: #8e8e93;
}

.course-actions {
  margin-top: 8px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.btn-primary:active {
  transform: scale(0.95);
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 1px solid #667eea;
}

.btn-outline:active {
  background: rgba(102, 126, 234, 0.1);
}

.btn-text {
  color: inherit;
  font-size: inherit;
  font-weight: inherit;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}



// 弹窗样式 - uView
.search-dialog {
  padding: 20px;
  max-height: 70vh;
}

.filter-dialog {
  background: var(--bg-primary);
  border-radius: 20px 20px 0 0;
  padding: 8px 20px 20px;
  max-height: 70vh;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  margin-bottom: 12px;
  position: relative;
  text-align: center;
}

.dialog-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.filter-content {
  margin-bottom: 20px;
}

.filter-group {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
  display: block;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  padding-top: 12px;
} 