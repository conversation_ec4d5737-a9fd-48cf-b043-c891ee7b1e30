<template>
  <div class="points-exchange">
    <div class="page-header">
      <h2>兑换记录</h2>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" ref="filterFormRef" :inline="true">
        <el-form-item label="学员姓名" prop="userName">
          <el-input v-model="filterForm.userName" placeholder="请输入学员姓名" clearable />
        </el-form-item>
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="filterForm.productName" placeholder="请输入商品名称" clearable />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已审核" value="approved" />
            <el-option label="已发货" value="shipped" />
            <el-option label="已送达" value="delivered" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="兑换时间" prop="timeRange">
          <el-date-picker
            v-model="filterForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleBatchShip" :disabled="selectedExchanges.length === 0">批量发货</el-button>
      <el-button type="primary" @click="handleExport">导出记录</el-button>
    </div>

    <!-- 兑换记录表格 -->
    <el-table
      v-loading="loading"
      :data="exchangeList"
      border
      style="width: 100%; margin-top: 16px"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" align="center" />
      <el-table-column prop="userName" label="学员姓名" min-width="100" align="center" />
      <el-table-column prop="departmentName" label="所属部门" min-width="120" align="center" />
      <el-table-column prop="productName" label="商品名称" min-width="150" show-overflow-tooltip />
      <el-table-column label="商品图片" width="80" align="center">
        <template #default="{ row }">
          <el-image
            style="width: 40px; height: 40px"
            :src="row.goodsImage"
            :preview-src-list="[row.goodsImage]"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="points" label="所需积分" width="100" align="center" />
      <el-table-column label="收货地址" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          {{ formatAddress(row.address) }}
        </template>
      </el-table-column>
      <el-table-column prop="exchangeTime" label="兑换时间" min-width="160" align="center" />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" align="center" fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="row.status === 'pending'"
            type="primary"
            link
            @click="handleShip(row)"
          >
            发货
          </el-button>
          <el-button
            v-if="row.status === 'pending'"
            type="danger"
            link
            @click="handleCancel(row)"
          >
            取消兑换
          </el-button>
          <el-button
            v-if="row.status === 'shipped'"
            type="primary"
            link
            @click="handleTrack(row)"
          >
            物流查询
          </el-button>
          <el-button type="primary" link @click="handleViewDetail(row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 发货弹窗 -->
    <el-dialog
      v-model="shipDialogVisible"
      title="商品发货"
      width="500px"
      destroy-on-close
    >
      <el-form ref="shipFormRef" :model="shipForm" :rules="shipRules" label-width="100px">
        <div class="ship-info">
          <p><strong>学员：</strong>{{ currentExchange.userName }}</p>
          <p><strong>商品：</strong>{{ currentExchange.productName }}</p>
        </div>
        <el-form-item label="收货地址" prop="address">
          <el-input
            :value="formatAddress(currentExchange.address)"
            type="textarea"
            rows="2"
            readonly
          />
        </el-form-item>
        <el-form-item label="物流公司" prop="company">
          <el-select v-model="shipForm.company" placeholder="请选择物流公司" style="width: 100%">
            <el-option label="顺丰速运" value="SF" />
            <el-option label="中通快递" value="ZTO" />
            <el-option label="圆通速递" value="YTO" />
            <el-option label="韵达快递" value="YD" />
            <el-option label="申通快递" value="STO" />
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号" prop="trackingNumber">
          <el-input v-model="shipForm.trackingNumber" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="shipForm.remark"
            type="textarea"
            rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="shipDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitShip" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 批量发货弹窗 -->
    <el-dialog
      v-model="batchShipDialogVisible"
      title="批量发货"
      width="700px"
      destroy-on-close
    >
      <div class="batch-ship-info">
        <p>已选择 {{ selectedExchanges.length }} 条记录进行批量发货</p>
      </div>
      <el-form ref="batchShipFormRef" :model="batchShipForm" :rules="batchShipRules" label-width="100px">
        <el-form-item label="物流公司" prop="company">
          <el-select v-model="batchShipForm.company" placeholder="请选择物流公司" style="width: 100%">
            <el-option label="顺丰速运" value="SF" />
            <el-option label="中通快递" value="ZTO" />
            <el-option label="圆通速递" value="YTO" />
            <el-option label="韵达快递" value="YD" />
            <el-option label="申通快递" value="STO" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="batchShipForm.remark"
            type="textarea"
            rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <el-table
        :data="selectedExchanges"
        border
        height="250"
        style="width: 100%; margin-top: 20px"
      >
        <el-table-column prop="userName" label="学员姓名" min-width="100" align="center" />
        <el-table-column prop="productName" label="商品名称" min-width="150" show-overflow-tooltip />
        <el-table-column label="收货地址" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatAddress(row.address) }}
          </template>
        </el-table-column>
        <el-table-column label="物流单号" min-width="150">
          <template #default="{ row, $index }">
            <el-input
              v-model="batchTrackingNumbers[$index]"
              placeholder="请输入物流单号"
              size="small"
            />
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="batchShipDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBatchShip" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 物流查询弹窗 -->
    <el-dialog
      v-model="trackDialogVisible"
      title="物流查询"
      width="600px"
      destroy-on-close
    >
      <div class="track-info">
        <p><strong>物流公司：</strong>{{ getLogisticsCompanyName(currentTrack.company) }}</p>
        <p><strong>物流单号：</strong>{{ currentTrack.trackingNumber }}</p>
      </div>
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in trackActivities"
          :key="index"
          :timestamp="activity.time"
          :color="index === 0 ? '#67C23A' : ''"
        >
          {{ activity.content }}
        </el-timeline-item>
      </el-timeline>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="兑换详情"
      width="700px"
      destroy-on-close
    >
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="兑换单号">{{ currentExchange.id }}</el-descriptions-item>
        <el-descriptions-item label="兑换时间">{{ currentExchange.exchangeTime }}</el-descriptions-item>
        <el-descriptions-item label="兑换状态">
          <el-tag :type="getStatusTagType(currentExchange.status)">
            {{ getStatusLabel(currentExchange.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="所需积分">{{ currentExchange.points }}</el-descriptions-item>
      </el-descriptions>

      <el-divider />

      <el-descriptions title="学员信息" :column="2" border>
        <el-descriptions-item label="学员姓名">{{ currentExchange.userName }}</el-descriptions-item>
        <el-descriptions-item label="所属部门">{{ currentExchange.departmentName }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ currentExchange.phone }}</el-descriptions-item>
        <el-descriptions-item label="收货地址" :span="2">{{ formatAddress(currentExchange.address) }}</el-descriptions-item>
      </el-descriptions>

      <el-divider />

      <el-descriptions title="商品信息" :column="2" border>
        <el-descriptions-item label="商品名称">{{ currentExchange.productName }}</el-descriptions-item>
        <el-descriptions-item label="商品分类">{{ currentExchange.goodsCategory }}</el-descriptions-item>
        <el-descriptions-item label="商品图片">
          <el-image
            style="width: 60px; height: 60px"
            :src="currentExchange.goodsImage"
            :preview-src-list="[currentExchange.goodsImage]"
            fit="cover"
          />
        </el-descriptions-item>
        <el-descriptions-item label="商品描述" :span="2">
          {{ currentExchange.goodsDescription }}
        </el-descriptions-item>
      </el-descriptions>

      <el-divider v-if="currentExchange.status === 'shipped' || currentExchange.status === 'delivered'" />

      <template v-if="currentExchange.status === 'shipped' || currentExchange.status === 'delivered'">
        <el-descriptions title="物流信息" :column="2" border>
          <el-descriptions-item label="物流公司">
            {{ getLogisticsCompanyName(currentExchange.logistics?.company) }}
          </el-descriptions-item>
          <el-descriptions-item label="物流单号">{{ currentExchange.logistics?.trackingNumber }}</el-descriptions-item>
          <el-descriptions-item label="发货时间">{{ currentExchange.logistics?.shipTime }}</el-descriptions-item>
          <el-descriptions-item label="发货备注" :span="2">{{ currentExchange.logistics?.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getExchangeList,
  reviewExchange,
  updateExchangeExpress,
  confirmExchangeDelivery,
  cancelExchange,
  batchUpdateExchangeStatus,
  getExchangeStatistics,
  exportExchanges,
  type Exchange,
  type ExchangeQueryParams,
  type ExchangeStatistics
} from '@/api/exchange'

// 表格数据和分页
const loading = ref(false)
const exchangeList = ref<Exchange[]>([])
const selectedExchanges = ref<Exchange[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 筛选表单
const filterFormRef = ref()
const filterForm = reactive<ExchangeQueryParams>({
  userName: '',
  productName: '',
  status: '',
  startDate: '',
  endDate: ''
})

// 发货相关
const shipDialogVisible = ref(false)
const shipFormRef = ref(null)
const submitting = ref(false)
const currentExchange = ref<Exchange>({})
const shipForm = reactive({
  address: '',
  company: '',
  trackingNumber: '',
  remark: ''
})

const shipRules = {
  company: [{ required: true, message: '请选择物流公司', trigger: 'change' }],
  trackingNumber: [{ required: true, message: '请输入物流单号', trigger: 'blur' }]
}

// 批量发货相关
const batchShipDialogVisible = ref(false)
const batchShipFormRef = ref(null)
const batchTrackingNumbers = ref<string[]>([])
const batchShipForm = reactive({
  company: '',
  remark: ''
})

const batchShipRules = {
  company: [{ required: true, message: '请选择物流公司', trigger: 'change' }]
}

// 物流查询相关
const trackDialogVisible = ref(false)
const currentTrack = ref<any>({})
const trackActivities = ref<any[]>([])

// 详情弹窗
const detailDialogVisible = ref(false)

// 获取兑换记录列表
const fetchExchangeList = async () => {
  loading.value = true
  try {
    const params: ExchangeQueryParams = {
      page: currentPage.value,
      limit: pageSize.value,
      ...filterForm
    }
    
    const data = await getExchangeList(params)
    exchangeList.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取兑换记录失败:', error)
    ElMessage.error('获取兑换记录失败')
  } finally {
    loading.value = false
  }
}

// 状态标签
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待审核',
    'approved': '已审核',
    'shipped': '已发货',
    'delivered': '已送达',
    'completed': '已完成',
    'cancelled': '已取消',
    'rejected': '已拒绝'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'approved': 'primary',
    'shipped': 'primary',
    'delivered': 'success',
    'completed': 'success',
    'cancelled': 'danger',
    'rejected': 'danger'
  }
  return typeMap[status] || ''
}

// 物流公司名称
const getLogisticsCompanyName = (code: string) => {
  const companyMap: Record<string, string> = {
    'SF': '顺丰速运',
    'ZTO': '中通快递',
    'YTO': '圆通速递',
    'YD': '韵达快递',
    'STO': '申通快递'
  }
  return companyMap[code] || code
}

// 筛选和重置
const handleFilter = () => {
  currentPage.value = 1
  fetchExchangeList()
}

const resetFilter = () => {
  filterFormRef.value?.resetFields()
  Object.keys(filterForm).forEach(key => {
    filterForm[key as keyof ExchangeQueryParams] = '' as any
  })
  currentPage.value = 1
  fetchExchangeList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  fetchExchangeList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchExchangeList()
}

// 选择记录
const handleSelectionChange = (selection: Exchange[]) => {
  selectedExchanges.value = selection
}

// 发货操作
const handleShip = (row: Exchange) => {
  currentExchange.value = row
  shipForm.address = row.address
  shipForm.company = ''
  shipForm.trackingNumber = ''
  shipForm.remark = ''
  shipDialogVisible.value = true
}

// 提交发货
const submitShip = () => {
  if (!shipFormRef.value) return
  
  shipFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitting.value = true
      
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 更新状态
        const index = exchangeList.value.findIndex(item => item.id === currentExchange.value.id)
        if (index !== -1) {
          exchangeList.value[index].status = 'shipped'
          exchangeList.value[index].logistics = {
            company: shipForm.company,
            trackingNumber: shipForm.trackingNumber,
            shipTime: new Date().toLocaleString(),
            remark: shipForm.remark
          }
        }
        
        ElMessage.success('发货成功')
        shipDialogVisible.value = false
      } catch (error) {
        console.error('发货失败:', error)
        ElMessage.error('发货失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 批量发货
const handleBatchShip = () => {
  if (selectedExchanges.value.length === 0) {
    ElMessage.warning('请先选择待发货的记录')
    return
  }
  
  batchShipForm.company = ''
  batchShipForm.remark = ''
  batchTrackingNumbers.value = selectedExchanges.value.map(() => '')
  batchShipDialogVisible.value = true
}

// 提交批量发货
const submitBatchShip = () => {
  if (!batchShipFormRef.value) return
  
  // 检查是否所有物流单号都已填写
  const hasEmptyTrackingNumber = batchTrackingNumbers.value.some(number => !number)
  if (hasEmptyTrackingNumber) {
    ElMessage.warning('请填写所有记录的物流单号')
    return
  }
  
  batchShipFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitting.value = true
      
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 更新状态
        selectedExchanges.value.forEach((item, index) => {
          const listIndex = exchangeList.value.findIndex(exchange => exchange.id === item.id)
          if (listIndex !== -1) {
            exchangeList.value[listIndex].status = 'shipped'
            exchangeList.value[listIndex].logistics = {
              company: batchShipForm.company,
              trackingNumber: batchTrackingNumbers.value[index],
              shipTime: new Date().toLocaleString(),
              remark: batchShipForm.remark
            }
          }
        })
        
        ElMessage.success(`成功发货 ${selectedExchanges.value.length} 条记录`)
        batchShipDialogVisible.value = false
        // 清空选择
        selectedExchanges.value = []
      } catch (error) {
        console.error('批量发货失败:', error)
        ElMessage.error('批量发货失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 取消兑换
const handleCancel = (row: Exchange) => {
  ElMessageBox.confirm(
    `确定要取消学员 "${row.userName}" 的 "${row.productName}" 兑换记录吗？`,
    '取消确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 更新状态
      const index = exchangeList.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        exchangeList.value[index].status = 'cancelled'
      }
      
      ElMessage.success('兑换已取消')
    } catch (error) {
      console.error('取消失败:', error)
      ElMessage.error('取消失败')
    }
  }).catch(() => {})
}

// 物流查询
const handleTrack = (row: Exchange) => {
  if (!row.logistics) {
    ElMessage.warning('暂无物流信息')
    return
  }
  
  currentTrack.value = row.logistics
  
  // 模拟获取物流轨迹
  trackActivities.value = [
    {
      time: '2023-10-16 14:30:00',
      content: '已签收，签收人：' + row.userName
    },
    {
      time: '2023-10-16 09:15:00',
      content: '【太原市】快递已到达太原市小店区矿业大学营业点，快递员正在派送'
    },
    {
      time: '2023-10-15 20:30:00',
      content: '【太原市】快递已到达太原分拨中心'
    },
    {
      time: row.logistics.shipTime,
      content: '【太原市】商家已发货'
    }
  ]
  
  trackDialogVisible.value = true
}

// 查看详情
const handleViewDetail = (row: Exchange) => {
  currentExchange.value = row
  detailDialogVisible.value = true
}

// 导出记录
const handleExport = () => {
  ElMessage.success('兑换记录导出成功')
}

// 地址格式化
const formatAddress = (address: any) => {
  if (typeof address === 'string') {
    return address
  }
  if (typeof address === 'object' && address) {
    const { province = '', city = '', district = '', address: detailAddress = '' } = address
    return `${province}${city}${district}${detailAddress}`
  }
  return ''
}

// 页面加载时获取数据
onMounted(() => {
  fetchExchangeList()
})
</script>

<style scoped>
.points-exchange {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.filter-card {
  margin-bottom: 20px;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 发货弹窗 */
.ship-info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.ship-info p {
  margin: 5px 0;
}

/* 批量发货弹窗 */
.batch-ship-info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #ecf5ff;
  border-radius: 4px;
  color: #409eff;
}

/* 物流查询弹窗 */
.track-info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.track-info p {
  margin: 5px 0;
}
</style> 