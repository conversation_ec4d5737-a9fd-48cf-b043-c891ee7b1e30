<template>
  <div class="profile-container">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <span>个人中心</span>
        </div>
      </template>

      <div class="profile-content">
        <!-- 用户头像和基本信息 -->
        <div class="profile-header">
          <div class="avatar-section">
            <el-avatar
              :size="120"
              :src="userInfo?.avatar"
              class="user-avatar"
            >
              {{ userInfo?.name?.charAt(0) }}
            </el-avatar>
            <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :before-upload="beforeAvatarUpload"
              :http-request="handleAvatarUpload"
              accept="image/*"
            >
              <el-button type="primary" size="small" class="upload-btn">
                更换头像
              </el-button>
            </el-upload>
          </div>
          <div class="user-info">
            <h2>{{ userInfo?.name }}</h2>
            <p class="username">@{{ userInfo?.username }}</p>
            <div v-if="userInfo?.permissions && userInfo.permissions.length > 0" class="permissions-container">
              <el-tag
                v-for="permission in userInfo.permissions"
                :key="permission"
                type="success"
                size="small"
                class="permission-tag"
              >
                {{ permission }}
              </el-tag>
            </div>
            <div v-else class="no-permissions">
              <el-tag type="info" size="small">暂无权限</el-tag>
            </div>
          </div>
        </div>

        <!-- 个人信息修改 -->
        <div class="profile-form">
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-form
                ref="basicFormRef"
                :model="basicForm"
                :rules="basicRules"
                label-width="100px"
                class="profile-basic-form"
              >
                <el-form-item label="用户名">
                  <el-input
                    v-model="basicForm.username"
                    disabled
                    placeholder="用户名不可修改"
                  />
                </el-form-item>
                <el-form-item label="姓名" prop="name">
                  <el-input
                    v-model="basicForm.name"
                    placeholder="请输入您的姓名"
                  />
                </el-form-item>
                <el-form-item label="权限">
                  <div v-if="userInfo?.permissions && userInfo.permissions.length > 0" class="permissions-list">
                    <el-tag
                      v-for="permission in userInfo.permissions"
                      :key="permission"
                      type="info"
                      size="small"
                      class="permission-item"
                    >
                      {{ permission }}
                    </el-tag>
                  </div>
                  <el-tag v-else type="warning" size="small">暂无权限</el-tag>
                  <span class="role-note">权限由系统管理员分配</span>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="updateBasicInfo"
                    :loading="basicLoading"
                  >
                    保存修改
                  </el-button>
                  <el-button @click="resetBasicForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 密码修改 -->
            <el-tab-pane label="修改密码" name="password">
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
                class="profile-password-form"
              >
                <el-form-item label="当前密码" prop="oldPassword">
                  <el-input
                    v-model="passwordForm.oldPassword"
                    type="password"
                    placeholder="请输入当前密码"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    placeholder="请输入新密码"
                    show-password
                  />
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    placeholder="请再次输入新密码"
                    show-password
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="changePassword"
                    :loading="passwordLoading"
                  >
                    修改密码
                  </el-button>
                  <el-button @click="resetPasswordForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, UploadRawFile } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { updateUserInfo, changePassword as changePasswordApi, uploadAvatar } from '@/api/user'

const userStore = useUserStore()

// 计算属性：用户信息
const userInfo = computed(() => userStore.userInfo)

// 活动标签页
const activeTab = ref('basic')

// 表单引用
const basicFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 基本信息表单
const basicForm = reactive({
  username: '',
  name: ''
})

const basicLoading = ref(false)

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordLoading = ref(false)

// 表单验证规则
const basicRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (userInfo.value) {
    basicForm.username = userInfo.value.username
    basicForm.name = userInfo.value.name
  }
}

// 重置基本信息表单
const resetBasicForm = () => {
  initFormData()
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordFormRef.value?.clearValidate()
}

// 头像上传前的检查
const beforeAvatarUpload = (file: UploadRawFile) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 头像上传处理
const handleAvatarUpload = async (options: any) => {
  try {
    const formData = new FormData()
    formData.append('avatar', options.file)
    
    const response = await uploadAvatar(formData)
    
    if (response.avatarUrl) {
      // 更新用户信息中的头像
      await updateUserInfo({ avatar: response.avatarUrl })
      
      // 强制从服务器获取最新用户信息
      await userStore.getUserInfoAction(true)
      
      ElMessage.success('头像更新成功!')
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败，请稍后重试')
  }
}

// 更新基本信息
const updateBasicInfo = async () => {
  if (!basicFormRef.value) return

  try {
    await basicFormRef.value.validate()
    
    basicLoading.value = true
    
    await updateUserInfo({
      name: basicForm.name
    })
    
    // 强制从服务器获取最新用户信息
    await userStore.getUserInfoAction(true)
    
    ElMessage.success('基本信息更新成功!')
  } catch (error) {
    console.error('更新基本信息失败:', error)
    ElMessage.error('更新失败，请稍后重试')
  } finally {
    basicLoading.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    
    await ElMessageBox.confirm(
      '确定要修改密码吗？修改后需要重新登录。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    passwordLoading.value = true
    
    await changePasswordApi(passwordForm.oldPassword, passwordForm.newPassword)
    
    ElMessage.success('密码修改成功，请重新登录!')
    
    // 清空密码表单
    resetPasswordForm()
    
    // 延迟退出登录
    setTimeout(() => {
      userStore.logout()
    }, 1500)
    
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('修改密码失败:', error)
      ElMessage.error('修改密码失败，请检查当前密码是否正确')
    }
  } finally {
    passwordLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  initFormData()
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.profile-content {
  padding: 20px 0;
}

.profile-header {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  margin-bottom: 30px;
  color: white;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30px;
}

.user-avatar {
  margin-bottom: 15px;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.upload-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.upload-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.user-info h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.username {
  margin: 0 0 15px 0;
  opacity: 0.8;
  font-size: 14px;
}

.user-type-tag {
  margin-right: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.profile-form {
  padding: 0 20px;
}

.profile-basic-form,
.profile-password-form {
  max-width: 500px;
  margin: 20px 0;
}

.role-note {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
}

.el-tabs--border-card {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-button + .el-button {
  margin-left: 10px;
}

.permissions-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 5px;
}

.permission-tag {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.no-permissions {
  margin-top: 5px;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.permission-item {
  margin-right: 0;
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .avatar-section {
    margin-right: 0;
    margin-bottom: 20px;
  }
  
  .profile-basic-form,
  .profile-password-form {
    max-width: 100%;
  }
}
</style> 