package com.example.cy_app_server.controller;

import com.example.cy_app_server.service.PracticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/practice")
public class PracticeController {

    @Autowired
    private PracticeService practiceService;

    @GetMapping("/banks")
    public ResponseEntity<?> getAvailableBanks() {
        return ResponseEntity.ok(practiceService.getAvailableBanks());
    }

    @GetMapping("/stats/{userId}")
    public ResponseEntity<?> getUserPracticeStats(@PathVariable Integer userId) {
        return ResponseEntity.ok(practiceService.getUserPracticeStats(userId));
    }

    @GetMapping("/wrong-questions")
    public ResponseEntity<?> getWrongQuestions(@RequestParam Integer userId, @RequestParam(required = false) Integer bankId) {
        return ResponseEntity.ok(practiceService.getWrongQuestions(userId, bankId));
    }

    @PostMapping("/start")
    public ResponseEntity<?> startPractice(@RequestBody Map<String, Object> payload) {
        Integer userId = (Integer) payload.get("userId");
        Integer bankId = (Integer) payload.get("bankId");
        String type = (String) payload.get("type");
        return ResponseEntity.ok(practiceService.startPractice(userId, bankId, type));
    }

    @PostMapping("/submit")
    public ResponseEntity<?> submitAnswer(@RequestBody Map<String, Object> payload) {
        Integer recordId = (Integer) payload.get("recordId");
        Integer questionId = (Integer) payload.get("questionId");
        String userAnswer = (String) payload.get("userAnswer");
        return ResponseEntity.ok(practiceService.submitAnswer(recordId, questionId, userAnswer));
    }
    
    @GetMapping("/record/{recordId}")
    public ResponseEntity<?> getPracticeRecordDetail(@PathVariable Integer recordId) {
        return ResponseEntity.ok(practiceService.getPracticeRecordDetail(recordId));
    }
}
