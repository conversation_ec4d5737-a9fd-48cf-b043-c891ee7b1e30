package com.cy.education.service.impl;

import com.cy.education.model.vo.CaptchaResponseVO;
import com.cy.education.service.CaptchaService;
import com.cy.education.utils.CaptchaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现类
 */
@Slf4j
@Service
public class CaptchaServiceImpl implements CaptchaService {

    @Autowired
    private CaptchaUtil captchaUtil;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 验证码缓存键前缀
     */
    private static final String CAPTCHA_KEY_PREFIX = "captcha:";

    /**
     * 验证码过期时间（分钟）
     */
    private static final int CAPTCHA_EXPIRE_MINUTES = 5;

    @Override
    public CaptchaResponseVO generateCaptcha() {
        // 生成验证码ID
        String captchaId = UUID.randomUUID().toString();
        
        // 生成验证码文本
        String code = captchaUtil.generateCode();
        
        // 生成验证码图片
        String captchaImage = captchaUtil.generateImage(code);
        
        // 将验证码存储到Redis中
        String key = CAPTCHA_KEY_PREFIX + captchaId;
        redisTemplate.opsForValue().set(key, code, CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        log.info("生成验证码: captchaId={}, code={}", captchaId, code);
        
        return CaptchaResponseVO.builder()
                .captchaId(captchaId)
                .captchaImage(captchaImage)
                .expireTime(System.currentTimeMillis() + CAPTCHA_EXPIRE_MINUTES * 60 * 1000)
                .build();
    }

    @Override
    public boolean verifyCaptcha(String captchaId, String code) {
        if (captchaId == null || code == null) {
            log.warn("验证码验证失败: captchaId或code为空");
            return false;
        }
        
        String key = CAPTCHA_KEY_PREFIX + captchaId;
        String cachedCode = redisTemplate.opsForValue().get(key);
        
        if (cachedCode == null) {
            log.warn("验证码验证失败: 验证码不存在或已过期, captchaId={}", captchaId);
            return false;
        }
        
        boolean isValid = captchaUtil.verify(code, cachedCode);
        
        if (isValid) {
            // 验证成功后立即删除验证码（防止重复使用）
            redisTemplate.delete(key);
            log.info("验证码验证成功: captchaId={}", captchaId);
        } else {
            log.warn("验证码验证失败: 验证码错误, captchaId={}, input={}, expected={}", 
                    captchaId, code, cachedCode);
        }
        
        return isValid;
    }

    @Override
    public void clearCaptcha(String captchaId) {
        if (captchaId != null) {
            String key = CAPTCHA_KEY_PREFIX + captchaId;
            redisTemplate.delete(key);
            log.info("清除验证码: captchaId={}", captchaId);
        }
    }
} 