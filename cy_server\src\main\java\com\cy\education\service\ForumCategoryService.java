package com.cy.education.service;

import com.cy.education.model.entity.ForumCategory;
import com.cy.education.model.vo.ForumCategoryQueryParam;

import java.util.List;

/**
 * 论坛分类服务接口
 */
public interface ForumCategoryService {
    
    /**
     * 获取分类列表（树形结构）
     * 
     * @param param 查询参数
     * @return 分类列表
     */
    List<ForumCategory> getCategoryTree(ForumCategoryQueryParam param);
    
    /**
     * 获取分类详情
     * 
     * @param id 分类ID
     * @return 分类信息
     */
    ForumCategory getCategoryById(Integer id);
    
    /**
     * 新增分类
     * 
     * @param category 分类信息
     * @return 分类ID
     */
    Integer addCategory(ForumCategory category);
    
    /**
     * 更新分类
     * 
     * @param category 分类信息
     * @return 是否成功
     */
    boolean updateCategory(ForumCategory category);
    
    /**
     * 删除分类
     * 
     * @param id 分类ID
     * @return 是否成功
     */
    boolean deleteCategory(Integer id);
    
    /**
     * 更新分类状态
     * 
     * @param id 分类ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateCategoryStatus(Integer id, Integer status);
    
    /**
     * 更新分类排序
     * 
     * @param sortData 排序数据 [{id, sort}]
     * @return 是否成功
     */
    boolean updateCategorySort(List<ForumCategory> sortData);
    
    /**
     * 获取所有父分类（用于下拉选择）
     * 
     * @return 父分类列表
     */
    List<ForumCategory> getParentCategories();
} 