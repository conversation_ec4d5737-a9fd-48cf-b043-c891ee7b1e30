package com.cy.education.service.impl.forum;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.forum.ForumPost;
import com.cy.education.model.params.ForumPostQueryParam;
import com.cy.education.repository.ForumPostMapper;
import com.cy.education.service.forum.ForumPostService;
import com.cy.education.utils.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 论坛帖子服务实现
 */
@Service
public class ForumPostServiceImpl implements ForumPostService {

    @Autowired
    private ForumPostMapper forumPostMapper;

    private static final int STATUS_APPROVED = 1;

    @Override
    public IPage<ForumPost> getPostPage(Integer loginUserId, Integer userId, ForumPostQueryParam param) {
        Page<ForumPost> page = new Page<>(param.getPageNum(), param.getPageSize());
        return forumPostMapper.selectPostPage(page, param.getCategoryId(), param.getKeyword(), STATUS_APPROVED,
                param.getSortBy(), loginUserId, userId);
    }

    @Override
    public IPage<ForumPost> getCollectedPostsPage(Integer userId, com.cy.education.model.params.ForumPostQueryParam param) {
        Page<ForumPost> page = new Page<>(param.getPageNum(), param.getPageSize());
        return forumPostMapper.getCollectedPostsPage(page, param.getCategoryId(), param.getKeyword(), STATUS_APPROVED,
                param.getSortBy(), userId);
    }

    @Override
    public ForumPost getPostById(Integer id) {
        ForumPost post = forumPostMapper.selectPostWithDetailsById(id, SecurityUtil.getCurrentUserId());
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }
        return post;
    }

    @Override
    public boolean incrementViewCount(Integer id) {
        return forumPostMapper.incrementViewCount(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePost(Integer id) {
        // 检查帖子是否存在
        ForumPost post = forumPostMapper.selectById(id);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }
        // 假删除帖子（将状态设为已删除）
        return forumPostMapper.softDeletePost(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ForumPost createPost(String title, String content, Integer categoryId, List<String> images, Integer authorId) {
        // 创建帖子对象
        ForumPost post = new ForumPost();
        post.setTitle(title.trim());
        post.setContent(content.trim());
        post.setCategoryId(categoryId);
        post.setAuthorId(authorId);

        // 处理图片列表
        if (images != null && !images.isEmpty()) {
            String imagesJson = String.join(",", images);
            post.setImages(imagesJson);
        }

        // 插入帖子到数据库
        int result = forumPostMapper.insert(post);
        if (result <= 0) {
            throw new BusinessException("创建帖子失败");
        }

        // 返回创建的帖子信息（包含作者和分类信息）
        return forumPostMapper.selectPostWithDetailsById(post.getId(), authorId);
    }

}
