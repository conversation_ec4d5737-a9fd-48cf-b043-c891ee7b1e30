<template>
  <view class="test-page">
    <up-navbar title="PK功能测试" :border="false">
      <template #left>
        <up-icon name="arrow-left" size="20" @click="goBack"></up-icon>
      </template>
    </up-navbar>

    <view class="test-content">
      <view class="test-section">
        <text class="section-title">API测试</text>
        
        <up-button text="测试获取题库列表" @click="testGetBanks" :loading="loading.banks"></up-button>
        <view class="result-box" v-if="results.banks">
          <text class="result-title">题库列表结果：</text>
          <text class="result-text">{{ JSON.stringify(results.banks, null, 2) }}</text>
        </view>
        
        <up-button text="测试开始匹配" @click="testStartMatch" :loading="loading.match"></up-button>
        <view class="result-box" v-if="results.match">
          <text class="result-title">匹配结果：</text>
          <text class="result-text">{{ JSON.stringify(results.match, null, 2) }}</text>
        </view>
        
        <up-button text="测试WebSocket连接" @click="testWebSocket" :loading="loading.websocket"></up-button>
        <view class="result-box" v-if="results.websocket">
          <text class="result-title">WebSocket结果：</text>
          <text class="result-text">{{ results.websocket }}</text>
        </view>
      </view>
      
      <view class="test-section">
        <text class="section-title">页面跳转测试</text>
        
        <up-button text="跳转到PK主页" @click="goToPkIndex"></up-button>
        <up-button text="跳转到PK历史" @click="goToPkHistory"></up-button>
      </view>
    </view>
  </view>
</template>

<script>
import { getAvailableBanks } from '@/api/practice'
import { startMatch } from '@/api/pk'

export default {
  data() {
    return {
      loading: {
        banks: false,
        match: false,
        websocket: false
      },
      results: {
        banks: null,
        match: null,
        websocket: null
      },
      websocket: null,
      userId: null
    }
  },
  
  onLoad() {
    this.userId = uni.getStorageSync('userInfo')?.id || 1
  },
  
  onUnload() {
    if (this.websocket) {
      this.websocket.close()
    }
  },
  
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    async testGetBanks() {
      this.loading.banks = true
      try {
        const res = await getAvailableBanks()
        this.results.banks = res
        uni.showToast({ title: '获取题库成功', icon: 'success' })
      } catch (error) {
        console.error('获取题库失败:', error)
        this.results.banks = { error: error.message || '获取失败' }
        uni.showToast({ title: '获取题库失败', icon: 'none' })
      } finally {
        this.loading.banks = false
      }
    },
    
    async testStartMatch() {
      this.loading.match = true
      try {
        const params = {
          userId: this.userId,
          bankId: 1,
          questionCount: 5,
          timeLimit: 180
        }
        const res = await startMatch(params)
        this.results.match = res
        uni.showToast({ title: '匹配请求成功', icon: 'success' })
      } catch (error) {
        console.error('开始匹配失败:', error)
        this.results.match = { error: error.message || '匹配失败' }
        uni.showToast({ title: '匹配失败', icon: 'none' })
      } finally {
        this.loading.match = false
      }
    },
    
    testWebSocket() {
      this.loading.websocket = true
      
      try {
        const baseUrl = process.env.NODE_ENV === 'development' ? 'localhost:8080' : 'your-server-domain.com'
        const wsUrl = `ws://${baseUrl}/websocket/pk/${this.userId}`
        
        this.websocket = uni.connectSocket({
          url: wsUrl,
          success: () => {
            this.results.websocket = 'WebSocket连接成功'
            uni.showToast({ title: 'WebSocket连接成功', icon: 'success' })
          },
          fail: (error) => {
            this.results.websocket = `WebSocket连接失败: ${JSON.stringify(error)}`
            uni.showToast({ title: 'WebSocket连接失败', icon: 'none' })
          }
        })
        
        this.websocket.onMessage((res) => {
          console.log('收到WebSocket消息:', res.data)
          this.results.websocket += `\n收到消息: ${res.data}`
        })
        
        this.websocket.onError((error) => {
          console.error('WebSocket错误:', error)
          this.results.websocket += `\nWebSocket错误: ${JSON.stringify(error)}`
        })
        
        this.websocket.onClose(() => {
          console.log('WebSocket连接关闭')
          this.results.websocket += '\nWebSocket连接关闭'
        })
        
        // 发送测试消息
        setTimeout(() => {
          if (this.websocket) {
            this.websocket.send({
              data: JSON.stringify({
                type: 'heartbeat',
                userId: this.userId,
                timestamp: Date.now()
              })
            })
          }
        }, 1000)
        
      } catch (error) {
        console.error('WebSocket测试失败:', error)
        this.results.websocket = `WebSocket测试失败: ${error.message}`
        uni.showToast({ title: 'WebSocket测试失败', icon: 'none' })
      } finally {
        this.loading.websocket = false
      }
    },
    
    goToPkIndex() {
      uni.navigateTo({
        url: '/pages/pk/index'
      })
    },
    
    goToPkHistory() {
      uni.navigateTo({
        url: '/pages/pk/history'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.test-content {
  padding: 20px;
}

.test-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
  display: block;
}

.result-box {
  margin-top: 12px;
  padding: 12px;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.result-title {
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  display: block;
  margin-bottom: 8px;
}

.result-text {
  font-size: 12px;
  color: #2d3748;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
