import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'
import { useUserStore } from '@/store/modules/user'

// Fix for TypeScript import.meta.env error
declare global {
  interface ImportMeta {
    env: Record<string, string>
  }
}

// Add type declaration for Vue component imports
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Define route meta type
interface RouteMeta {
  title?: string
  icon?: string
  hidden?: boolean
}

// Define our extended route type
type AppRouteRecordRaw = RouteRecordRaw & {
  meta?: RouteMeta
  children?: AppRouteRecordRaw[]
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: Layout,
      redirect: '/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/dashboard/index.vue'),
          meta: { title: '看板', icon: 'dashboard' }
        }
      ]
    },
    {
      path: '/system',
      component: Layout,
      redirect: '/system/admin',
      meta: { title: '系统管理', icon: 'setting' },
      children: [
        {
          path: 'admin',
          name: 'Admin',
          component: () => import('@/views/system/admin/index.vue'),
          meta: { title: '管理员管理' }
        },
        {
          path: 'department',
          name: 'Department',
          component: () => import('@/views/system/department/index.vue'),
          meta: { title: '部门管理' }
        }
      ]
    },
    {
      path: '/app-home',
      component: Layout,
      redirect: '/app-home/content',
      meta: { title: 'APP首页管理', icon: 'home' },
      children: [
        {
          path: 'content',
          name: 'AppHomeContent',
          component: () => import('@/views/app-home/content/index.vue'),
          meta: { title: '首页内容管理' }
        }
      ]
    },
    {
      path: '/student',
      component: Layout,
      redirect: '/student/info',
      meta: { title: '学员管理', icon: 'user' },
      children: [
        {
          path: 'info',
          name: 'StudentInfo',
          component: () => import('@/views/student/info/index.vue'),
          meta: { title: '学员信息管理' }
        },
        {
          path: 'detail/:id',
          name: 'StudentDetail',
          component: () => import('@/views/student/detail/index.vue'),
          meta: { title: '学员详情', hidden: true }
        }
      ]
    },
    {
      path: '/forum',
      component: Layout,
      redirect: '/forum/posts',
      meta: { title: '论坛内容管理', icon: 'comment' },
      children: [
        {
          path: 'posts',
          name: 'ForumPosts',
          component: () => import('@/views/forum/posts/index.vue'),
          meta: { title: '帖子/评论管理' }
        },
        {
          path: 'categories',
          name: 'ForumCategories',
          component: () => import('@/views/forum/categories/index.vue'),
          meta: { title: '板块分类管理' }
        },
        {
          path: 'violations',
          name: 'ForumViolations',
          component: () => import('@/views/forum/violations/index.vue'),
          meta: { title: '违规内容处理' }
        }
      ]
    },
    {
      path: '/resource',
      component: Layout,
      redirect: '/resource/index',
      meta: { title: '资源管理', icon: 'folder-opened' },
      children: [
        {
          path: 'index',
          name: 'ResourceManagement',
          component: () => import('@/views/resource/index.vue'),
          meta: { title: '资源库管理' }
        }
      ]
    },
    {
      path: '/course',
      component: Layout,
      redirect: '/course/list',
      meta: { title: '课程管理', icon: 'book' },
      children: [
        {
          path: 'list',
          name: 'CourseList',
          component: () => import('@/views/course/index.vue'),
          meta: { title: '课程列表' }
        },
        {
          path: 'detail/:id',
          name: 'CourseDetail',
          component: () => import('@/views/course/detail/index.vue'),
          meta: { title: '课程详情', hidden: true }
        },
        {
          path: 'preview/:id',
          name: 'CoursePreview',
          component: () => import('@/views/course/preview/index.vue'),
          meta: { title: '课程预览', hidden: true }
        }
      ]
    },
    {
      path: '/exam',
      component: Layout,
      redirect: '/exam/question-bank',
      meta: { title: '试题考试模块', icon: 'edit' },
      children: [
        {
          path: 'question-bank',
          name: 'ExamQuestionBank',
          component: () => import('@/views/exam/question-bank/index.vue'),
          meta: { title: '题库管理' }
        },
        {
          path: 'paper',
          name: 'ExamPaper',
          component: () => import('@/views/exam/paper/index.vue'),
          meta: { title: '试卷管理' }
        },
        {
          path: 'paper/edit/:id',
          name: 'ExamPaperEdit',
          component: () => import('@/views/exam/paper/edit.vue'),
          meta: { title: '编辑试卷', hidden: true }
        },
        {
          path: 'management',
          name: 'ExamManagement',
          component: () => import('@/views/exam/management/index.vue'),
          meta: { title: '考试管理' }
        },
        {
          path: 'marking/:id',
          name: 'ExamMarking',
          component: () => import('@/views/exam/marking/index.vue'),
          meta: { title: '在线阅卷', hidden: true }
        },
        {
          path: 'practice-statistics',
          name: 'PracticeStatistics',
          component: () => import('@/views/exam/practice-statistics/index.vue'),
          meta: { title: '练习统计' }
        },
        {
          path: 'record/detail/:id',
          name: 'ExamRecordDetail',
          component: () => import('@/views/exam/record/detail.vue'),
          meta: { title: '考试记录详情', hidden: true }
        },
        {
          path: 'record/result/:id',
          name: 'ExamRecordResult',
          component: () => import('@/views/exam/record/result.vue'),
          meta: { title: '考试成绩详情', hidden: true }
        }
      ]
    },
    {
      path: '/study',
      component: Layout,
      redirect: '/study/records',
      meta: { title: '学习管理', icon: 'reading' },
      children: [
        {
          path: 'records',
          name: 'StudyRecords',
          component: () => import('@/views/study/records/index.vue'),
          meta: { title: '学习记录' }
        },
        {
          path: 'analytics',
          name: 'StudyAnalytics',
          component: () => import('@/views/study/analytics/index.vue'),
          meta: { title: '学习分析' }
        }
      ]
    },
    {
      path: '/points',
      component: Layout,
      redirect: '/points/rules',
      meta: { title: '积分管理', icon: 'money' },
      children: [
        {
          path: 'rules',
          name: 'PointsRules',
          component: () => import('@/views/points/rules/index.vue'),
          meta: { title: '积分规则' }
        },
        {
          path: 'records',
          name: 'PointsRecords',
          component: () => import('@/views/points/records/index.vue'),
          meta: { title: '积分记录' }
        },
        {
          path: 'goods',
          name: 'PointsGoods',
          component: () => import('@/views/points/goods/index.vue'),
          meta: { title: '商品管理' }
        },
        {
          path: 'exchange',
          name: 'PointsExchange',
          component: () => import('@/views/points/exchange/index.vue'),
          meta: { title: '兑换记录' }
        }
      ]
    },
    {
      path: '/login',
      component: () => import('@/views/login/index.vue'),
      meta: { hidden: true }
    },
    {
      path: '/profile',
      component: Layout,
      redirect: '/profile/index',
      meta: { hidden: true },
      children: [
        {
          path: 'index',
          name: 'Profile',
          component: () => import('@/views/profile/index.vue'),
          meta: { title: '个人中心' }
        }
      ]
    },
    {
      path: '/test',
      component: Layout,
      meta: { title: '测试页面', icon: 'test', hidden: true },
      children: [
        {
          path: 'download',
          name: 'DownloadTest',
          component: () => import('@/views/test/download.vue'),
          meta: { title: '下载测试' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error-page/404.vue'),
      meta: { hidden: true }
    }
  ]
})

router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  if (userStore.token) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      // TODO: Add permission logic here if needed
      next()
    }
  } else {
    if (to.path !== '/login') {
      next(`/login?redirect=${to.path}`)
    } else {
      next()
    }
  }
})

export default router 