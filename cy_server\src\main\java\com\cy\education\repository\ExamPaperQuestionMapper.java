package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.ExamPaperQuestion;
import com.cy.education.model.vo.ExamPaperQuestionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试卷题目关联Mapper接口
 */
@Mapper
public interface ExamPaperQuestionMapper extends BaseMapper<ExamPaperQuestion> {

    /**
     * 根据试卷ID查询试卷题目关联列表（包含题目详情）
     *
     * @param paperId 试卷ID
     * @return 试卷题目列表
     */
    List<ExamPaperQuestionVO> selectQuestionsByPaperId(@Param("paperId") Integer paperId);

    /**
     * 批量插入试卷题目关联
     *
     * @param questions 试卷题目关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("questions") List<ExamPaperQuestion> questions);

    /**
     * 删除试卷题目关联
     *
     * @param paperId 试卷ID
     * @param questionId 题目ID
     * @return 影响行数
     */
    int deleteByPaperIdAndQuestionId(@Param("paperId") Integer paperId, @Param("questionId") Integer questionId);

    /**
     * 删除试卷所有题目关联
     *
     * @param paperId 试卷ID
     * @return 影响行数
     */
    int deleteByPaperId(@Param("paperId") Integer paperId);
} 