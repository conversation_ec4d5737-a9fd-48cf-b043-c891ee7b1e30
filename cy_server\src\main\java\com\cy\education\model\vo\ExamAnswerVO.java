package com.cy.education.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试答题记录VO类
 */
@Data
@ApiModel("考试答题记录信息")
public class ExamAnswerVO {
    
    /**
     * 答题ID
     */
    @ApiModelProperty("答题ID")
    private Integer id;
    
    /**
     * 考试记录ID
     */
    @ApiModelProperty("考试记录ID")
    private Integer recordId;
    
    /**
     * 题目ID
     */
    @ApiModelProperty("题目ID")
    private Integer questionId;
    
    /**
     * 题目内容
     */
    @ApiModelProperty("题目内容")
    private String questionContent;
    
    /**
     * 题目类型
     */
    @ApiModelProperty("题目类型")
    private String questionType;
    
    /**
     * 题目选项
     */
    @ApiModelProperty("题目选项")
    private String options;
    
    /**
     * 正确答案
     */
    @ApiModelProperty("正确答案")
    private String correctAnswer;
    
    /**
     * 题目解析
     */
    @ApiModelProperty("题目解析")
    private String explanation;
    
    /**
     * 题目总分
     */
    @ApiModelProperty("题目总分")
    private Integer totalScore;
    
    /**
     * 用户答案
     */
    @ApiModelProperty("用户答案")
    private String answer;
    
    /**
     * 是否正确
     */
    @ApiModelProperty("是否正确")
    private Boolean isCorrect;
    
    /**
     * 得分
     */
    @ApiModelProperty("得分")
    private Integer score;
    
    /**
     * 评语(主观题使用)
     */
    @ApiModelProperty("评语(主观题使用)")
    private String comment;
    
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;
} 