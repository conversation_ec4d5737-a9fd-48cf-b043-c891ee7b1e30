<template>
  <div class="department-detail">
    <el-card shadow="never" v-if="department">
      <template #header>
        <div class="card-header">
          <span>部门详情</span>
          <el-button text :icon="Edit" @click="$emit('edit', department)">编辑</el-button>
        </div>
      </template>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="部门名称">{{ department.name }}</el-descriptions-item>
        <el-descriptions-item label="上级部门">{{ getParentDeptName(department.parentId) }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ department.leader || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="排序">{{ department.sort }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ department.createTime }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="department.status ? 'success' : 'danger'">
            {{ department.status ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="备注">{{ department.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    <div class="select-dept-tip" v-else>
      <el-empty description="请选择左侧部门查看详情">
        <template #image>
          <el-icon style="font-size: 60px; color: #909399;"><OfficeBuilding /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Edit, OfficeBuilding } from '@element-plus/icons-vue'

interface DepartmentData {
  id: number
  name: string
  parentId: number | null
  leader?: string
  sort: number
  status: number
  createTime: string
  remark?: string
  children?: DepartmentData[]
}

const props = defineProps({
  department: {
    type: Object as () => DepartmentData | null,
    default: null
  },
  departmentTree: {
    type: Array as () => DepartmentData[],
    required: true
  }
})

const emit = defineEmits(['edit'])

// 获取上级部门名称
const getParentDeptName = (parentId: number | null) => {
  if (!parentId) return '无'
  
  // 递归查找部门
  const findDept = (depts: DepartmentData[], id: number): DepartmentData | null => {
    for (const dept of depts) {
      if (dept.id === id) return dept
      if (dept.children?.length) {
        const found = findDept(dept.children, id)
        if (found) return found
      }
    }
    return null
  }
  
  const parent = findDept(props.departmentTree, parentId)
  return parent ? parent.name : '未知部门'
}
</script>

<style scoped>
.department-detail {
  flex: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.select-dept-tip {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}
</style> 