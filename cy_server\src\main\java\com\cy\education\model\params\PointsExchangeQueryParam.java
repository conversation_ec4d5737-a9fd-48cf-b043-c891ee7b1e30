package com.cy.education.model.params;

import lombok.Data;

/**
 * 积分兑换查询参数
 */
@Data
public class PointsExchangeQueryParam {
    
    /**
     * 当前页码
     */
    private Integer page = 1;
    
    /**
     * 每页记录数
     */
    private Integer limit = 10;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 商品ID
     */
    private Integer productId;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 兑换状态
     */
    private String status;
    
    /**
     * 开始日期
     */
    private String startDate;
    
    /**
     * 结束日期
     */
    private String endDate;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向：asc/desc
     */
    private String sortOrder;
} 