package com.cy.education.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 题目导入模板数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionTemplateData {

    @ExcelProperty("题库名称*")
    private String bankName;

    @ExcelProperty("题目类型*")
    private String type;

    @ExcelProperty("题目标题*")
    private String title;

    @ExcelProperty("选项A")
    private String optionA;

    @ExcelProperty("选项B")
    private String optionB;

    @ExcelProperty("选项C")
    private String optionC;

    @ExcelProperty("选项D")
    private String optionD;

    @ExcelProperty("正确答案*")
    private String answer;

    @ExcelProperty("解析")
    private String explanation;

    @ExcelProperty("难度")
    private String difficulty;

    @ExcelProperty("分值")
    private Integer score;

    @Override
    public String toString() {
        return String.format("题库:%s, 类型:%s, 标题:%s", bankName, type, title);
    }
}
