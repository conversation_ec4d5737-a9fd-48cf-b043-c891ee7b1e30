<template>
  <view class="history-page">
    <!-- 导航栏 -->
    <up-navbar title="对战历史" :border="false" :background="navbarBg">
      <template #left>
        <up-icon name="arrow-left" size="20" color="#fff" @click="goBack"></up-icon>
      </template>
    </up-navbar>

    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stat-item">
          <text class="stat-value">{{ stats.totalGames }}</text>
          <text class="stat-label">总场次</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ stats.winGames }}</text>
          <text class="stat-label">胜利</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ stats.loseGames }}</text>
          <text class="stat-label">失败</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{ stats.winRate }}%</text>
          <text class="stat-label">胜率</text>
        </view>
      </view>
    </view>

    <!-- 历史记录列表 -->
    <view class="history-section">
      <view class="section-title">对战记录</view>
      
      <view class="history-list" v-if="historyList.length > 0">
        <view class="history-item" v-for="item in historyList" :key="item.id" @click="viewDetail(item)">
          <view class="history-left">
            <view class="result-badge" :class="getResultClass(item)">
              {{ getResultText(item) }}
            </view>
            <view class="history-info">
              <text class="bank-name">{{ item.bankName }}</text>
              <text class="game-time">{{ formatTime(item.joinedAt) }}</text>
            </view>
          </view>
          
          <view class="history-center">
            <view class="score-display">
              <text class="my-score">{{ item.score }}</text>
              <text class="vs-text">VS</text>
              <text class="opponent-score">{{ item.opponentScore || 0 }}</text>
            </view>
            <view class="game-stats">
              <text class="stat-text">{{ item.correctCount }}/{{ item.totalQuestions }} 正确</text>
            </view>
          </view>
          
          <view class="history-right">
            <up-icon name="arrow-right" size="16" color="#c7c7cc"></up-icon>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <up-icon name="file-text" size="64" color="#c7c7cc"></up-icon>
        <text class="empty-text">暂无对战记录</text>
        <up-button text="去对战" type="primary" @click="goToPk"></up-button>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="hasMore && historyList.length > 0">
      <up-button text="加载更多" :loading="loading" @click="loadMore"></up-button>
    </view>
  </view>
</template>

<script>
import { getUserPkHistory } from '@/api/pk'

export default {
  data() {
    return {
      navbarBg: {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      },
      userId: null,
      historyList: [],
      stats: {
        totalGames: 0,
        winGames: 0,
        loseGames: 0,
        winRate: 0
      },
      page: 1,
      size: 10,
      hasMore: true,
      loading: false
    }
  },
  
  onLoad() {
    this.userId = uni.getStorageSync('userInfo')?.id
    this.loadHistory()
  },
  
  onPullDownRefresh() {
    this.page = 1
    this.hasMore = true
    this.loadHistory(true)
  },
  
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },
  
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    // 加载历史记录
    async loadHistory(refresh = false) {
      if (this.loading) return

      this.loading = true

      try {
        console.log('PK历史页面 - 开始加载历史记录，用户ID:', this.userId, '页码:', this.page)
        const res = await getUserPkHistory(this.userId, this.page, this.size)
        console.log('PK历史页面 - 历史记录响应:', res)

        // 由于request函数已经处理了响应格式，这里直接使用
        if (res && res.success === true) {
          const newList = res.list || []
          console.log('PK历史页面 - 获取到历史记录数量:', newList.length)

          if (refresh) {
            this.historyList = newList
          } else {
            this.historyList = [...this.historyList, ...newList]
          }

          this.hasMore = newList.length === this.size
          this.calculateStats()
        } else {
          // 这种情况不应该发生，因为request函数会处理错误
          console.warn('PK历史页面 - 意外的响应格式:', res)
        }
      } catch (error) {
        console.error('PK历史页面 - 加载历史记录异常:', error)

        // 只在真正的网络错误时显示toast
        if (error && error.message && !error.message.includes('请求失败')) {
          uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' })
        }
      } finally {
        this.loading = false
        if (refresh) {
          uni.stopPullDownRefresh()
        }
      }
    },
    
    // 加载更多
    loadMore() {
      this.page++
      this.loadHistory()
    },
    
    // 计算统计数据
    calculateStats() {
      const total = this.historyList.length
      let wins = 0
      let loses = 0
      
      this.historyList.forEach(item => {
        const result = this.getResultClass(item)
        if (result === 'win') {
          wins++
        } else if (result === 'lose') {
          loses++
        }
      })
      
      this.stats = {
        totalGames: total,
        winGames: wins,
        loseGames: loses,
        winRate: total > 0 ? Math.round((wins / total) * 100) : 0
      }
    },
    
    // 获取结果样式类
    getResultClass(item) {
      // 这里需要根据实际数据结构判断胜负
      // 假设有winner字段或者通过分数比较
      if (item.isWinner) {
        return 'win'
      } else if (item.isWinner === false) {
        return 'lose'
      } else {
        return 'draw'
      }
    },
    
    // 获取结果文本
    getResultText(item) {
      const resultClass = this.getResultClass(item)
      const textMap = {
        'win': '胜利',
        'lose': '失败',
        'draw': '平局'
      }
      return textMap[resultClass] || '未知'
    },
    
    // 格式化时间
    formatTime(time) {
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else if (diff < 604800000) { // 1周内
        return Math.floor(diff / 86400000) + '天前'
      } else {
        return date.toLocaleDateString()
      }
    },
    
    // 查看详情
    viewDetail(item) {
      // TODO: 跳转到对战详情页面
      uni.showToast({ title: '功能开发中', icon: 'none' })
    },
    
    // 去对战
    goToPk() {
      uni.navigateTo({
        url: '/pages/pk/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.history-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

// 统计卡片
.stats-section {
  padding: 20px;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  justify-content: space-around;
  backdrop-filter: blur(10px);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 12px;
  color: #718096;
}

// 历史记录
.history-section {
  background: rgba(255, 255, 255, 0.95);
  margin: 0 20px 20px;
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  min-height: 400px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background: #edf2f7;
  }
}

.history-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.result-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;

  &.win {
    background: #c6f6d5;
    color: #22543d;
  }

  &.lose {
    background: #fed7d7;
    color: #742a2a;
  }

  &.draw {
    background: #feebc8;
    color: #744210;
  }
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.bank-name {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.game-time {
  font-size: 12px;
  color: #718096;
}

.history-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.my-score,
.opponent-score {
  font-size: 18px;
  font-weight: 600;
  color: #667eea;
}

.vs-text {
  font-size: 12px;
  color: #a0aec0;
}

.game-stats {
  font-size: 12px;
  color: #718096;
}

.history-right {
  padding-left: 12px;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

.empty-text {
  font-size: 16px;
  color: #a0aec0;
  margin-bottom: 8px;
}

// 加载更多
.load-more {
  padding: 20px;
  display: flex;
  justify-content: center;
}
</style>
