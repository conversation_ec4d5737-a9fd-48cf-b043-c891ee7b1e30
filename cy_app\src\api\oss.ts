import {get} from '@/utils/request'

/**
 * OSS签名数据类型
 */
export interface OssSignature {
    accessId: string
    host: string
    policy: string
    signature: string
    'x-oss-signature'?: string
    dir: string
    expire: string
    x_oss_date: string
    x_oss_credential: string
    security_token: string
    success_action_status: string
    x_oss_signature_version: string
    bucket?: string
}

/**
 * 获取通用OSS上传签名
 * @param directory 目录
 * @param fileType 文件类型
 */
export function getOssSignature(directory?: string, fileType?: string) {
    return get<OssSignature>('/api/oss/signature', {directory, fileType})
}

/**
 * 获取图片上传签名
 */
export function getImageSignature() {
    return get<OssSignature>('/api/oss/image/signature')
}

/**
 * 获取视频上传签名
 */
export function getVideoSignature() {
    return get<OssSignature>('/api/oss/video/signature')
}

/**
 * 获取文档上传签名
 */
export function getDocumentSignature() {
    return get<OssSignature>('/api/oss/document/signature')
}

/**
 * 构建OSS上传参数
 * @param signature OSS签名信息
 * @param filename 文件名
 */
export function buildOssUploadParams(signature: OssSignature, filename: string) {
    // 生成随机文件名，防止重复
    const suffix = filename.substring(filename.lastIndexOf('.'))
    const randomFileName = Date.now() + '_' + Math.random().toString(36).slice(2, 10) + suffix
    const key = signature.dir + randomFileName

    return {
        key,
        policy: signature.policy,
        OSSAccessKeyId: signature.accessId,
        success_action_status: signature.success_action_status,
        'x-oss-signature': signature.signature || signature['x-oss-signature'],
        'x-oss-security-token': signature.security_token,
        'x-oss-signature-version': signature.x_oss_signature_version,
        'x-oss-date': signature.x_oss_date,
        'x-oss-credential': signature.x_oss_credential,
        bucket: signature.bucket || undefined
    }
}

/**
 * 上传文件到OSS
 * @param file 文件对象
 * @param signature OSS签名信息
 * @param onProgress 上传进度回调
 */
export async function uploadToOss(
    file: File,
    signature: OssSignature,
    onProgress?: (percent: number) => void
): Promise<{ url: string, key: string }> {
    const formData = new FormData()

    // 构建上传参数
    const params = buildOssUploadParams(signature, file.name)

    // 添加所有参数到表单
    Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
            formData.append(key, value)
        }
    })

    // 添加文件类型
    formData.append('Content-Type', file.type)

    // 添加文件必须放在其他参数之后
    formData.append('file', file)

    // 打印FormData内容（调试用）
    console.log('OSS上传参数:')
    for (const pair of formData.entries()) {
        if (pair[0] !== 'file') {
            console.log(pair[0] + ': ' + pair[1])
        } else {
            console.log(pair[0] + ': [文件]')
        }
    }

    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest()

        // 监听上传进度
        if (onProgress) {
            xhr.upload.onprogress = (e) => {
                if (e.lengthComputable) {
                    const percent = Math.round((e.loaded * 100) / e.total)
                    onProgress(percent)
                }
            }
        }

        // 请求完成
        xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                // 上传成功，返回文件URL
                const fileUrl = signature.host + '/' + params.key
                resolve({url: fileUrl, key: params.key})
            } else {
                let errorMessage = `上传失败: ${xhr.status} ${xhr.statusText}`
                // 尝试解析错误响应
                try {
                    const errorResponse = xhr.responseText
                    console.error('OSS错误响应:', errorResponse)
                    errorMessage += ` - ${errorResponse}`
                } catch (e) {
                    // 忽略解析错误
                }
                reject(new Error(errorMessage))
            }
        }

        // 错误处理
        xhr.onerror = () => {
            reject(new Error('网络错误，上传失败'))
        }

        // 发送请求
        xhr.open('POST', signature.host, true)
        xhr.send(formData)
    })
}
