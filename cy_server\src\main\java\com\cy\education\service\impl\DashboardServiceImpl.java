package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cy.education.repository.StudentMapper;
import com.cy.education.repository.CourseRepository;
import com.cy.education.repository.ExamExamMapper;
import com.cy.education.repository.ForumPostMapper;
import com.cy.education.model.entity.Student;
import com.cy.education.model.entity.Course;
import com.cy.education.model.entity.ExamExam;
import com.cy.education.model.entity.ForumPost;
import com.cy.education.service.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Dashboard服务实现
 */
@Service
public class DashboardServiceImpl implements DashboardService {

    @Autowired
    private StudentMapper studentMapper;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private ExamExamMapper examExamMapper;

    @Autowired
    private ForumPostMapper forumPostMapper;

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 统计学员总数
            Long totalStudents = studentMapper.selectCount(new QueryWrapper<Student>()
                    .eq("status", 1)); // 只统计正常状态的学员
            statistics.put("totalStudents", totalStudents);
        } catch (Exception e) {
            statistics.put("totalStudents", 0L);
        }

        try {
            // 统计课程总数
            Long totalCourses = courseRepository.selectCount(new QueryWrapper<Course>()
                    .eq("status", "published")); // 只统计已发布的课程
            statistics.put("totalCourses", totalCourses);
        } catch (Exception e) {
            statistics.put("totalCourses", 0L);
        }

        try {
            // 统计考试总数
            Long totalExams = examExamMapper.selectCount(new QueryWrapper<ExamExam>());
            statistics.put("totalExams", totalExams);
        } catch (Exception e) {
            statistics.put("totalExams", 0L);
        }

        try {
            // 统计帖子总数
            Long totalPosts = forumPostMapper.selectCount(new QueryWrapper<ForumPost>()
                    .eq("status", 1)); // 只统计已发布的帖子
            statistics.put("totalPosts", totalPosts);
        } catch (Exception e) {
            statistics.put("totalPosts", 0L);
        }

        return statistics;
    }

    @Override
    public Map<String, Object> getRecentData() {
        Map<String, Object> recentData = new HashMap<>();

        try {
            // 获取最新学员（最近5个）
            List<Student> recentStudents = studentMapper.selectList(
                    new QueryWrapper<Student>()
                            .eq("status", 1)
                            .orderByDesc("created_at")
                            .last("LIMIT 5")
            );
            recentData.put("recentStudents", recentStudents);
        } catch (Exception e) {
            recentData.put("recentStudents", new java.util.ArrayList<>());
        }

        try {
            // 获取最新课程（最近5个）
            List<Course> recentCourses = courseRepository.selectList(
                    new QueryWrapper<Course>()
                            .orderByDesc("create_time")
                            .last("LIMIT 5")
            );
            recentData.put("recentCourses", recentCourses);
        } catch (Exception e) {
            recentData.put("recentCourses", new java.util.ArrayList<>());
        }

        try {
            // 获取最新考试（最近5个）
            List<ExamExam> recentExams = examExamMapper.selectList(
                    new QueryWrapper<ExamExam>()
                            .orderByDesc("created_at")
                            .last("LIMIT 5")
            );
            recentData.put("recentExams", recentExams);
        } catch (Exception e) {
            recentData.put("recentExams", new java.util.ArrayList<>());
        }

        try {
            // 获取最新帖子（最近5个）
            List<ForumPost> recentPosts = forumPostMapper.selectList(
                    new QueryWrapper<ForumPost>()
                            .orderByDesc("created_at")
                            .last("LIMIT 5")
            );
            recentData.put("recentPosts", recentPosts);
        } catch (Exception e) {
            recentData.put("recentPosts", new java.util.ArrayList<>());
        }

        return recentData;
    }
}
