<template>
  <view class="feedback-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">意见反馈</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <view class="form-section">
        <view class="form-title">您的反馈对我们很重要</view>
        <view class="form-subtitle">请详细描述您遇到的问题或建议</view>

        <!-- 反馈类型 -->
        <view class="form-item">
          <view class="form-label">反馈类型</view>
          <view class="type-selector">
            <view
                v-for="type in feedbackTypes"
                :key="type.value"
                :class="{ active: formData.type === type.value }"
                class="type-item"
                @click="selectType(type.value)"
            >
              <up-icon :color="formData.type === type.value ? '#fff' : '#666'" :name="type.icon" size="16"></up-icon>
              <text class="type-text">{{ type.label }}</text>
            </view>
          </view>
        </view>

        <!-- 反馈内容 -->
        <view class="form-item">
          <view class="form-label">反馈内容</view>
          <up-textarea
              v-model="formData.content"
              :border="false"
              :count="true"
              :maxlength="500"
              customStyle="background: #f8f9fa; border-radius: 12px; padding: 16px; min-height: 120px;"
              placeholder="请详细描述您遇到的问题或建议..."
          ></up-textarea>
        </view>

        <!-- 图片上传 -->
        <view class="form-item">
          <view class="form-label">上传图片（可选）</view>
          <view class="upload-section">
            <view class="image-list">
              <view
                  v-for="(image, index) in formData.images"
                  :key="index"
                  class="image-item"
              >
                <up-image
                    :src="image"
                    height="80"
                    radius="8"
                    width="80"
                ></up-image>
                <view class="delete-btn" @click="removeImage(index)">
                  <up-icon color="#fff" name="close" size="12"></up-icon>
                </view>
              </view>
              <view
                  v-if="formData.images.length < 3"
                  class="upload-btn"
                  @click="handleUploadImage"
              >
                <up-icon color="#666" name="camera" size="24"></up-icon>
                <text class="upload-text">添加图片</text>
              </view>
            </view>
            <text class="upload-tip">最多上传3张图片</text>
          </view>
        </view>

        <!-- 联系方式 -->
        <view class="form-item">
          <view class="form-label">联系方式（可选）</view>
          <up-input
              v-model="formData.contact"
              :border="false"
              customStyle="background: #f8f9fa; border-radius: 12px; padding: 16px;"
              placeholder="请留下您的手机号或邮箱，方便我们回复"
              type="text"
          ></up-input>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
          <up-button
              :disabled="!isFormValid"
              customStyle="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 12px; height: 48px;"
              text="提交反馈"
              type="primary"
              @click="handleSubmit"
          ></up-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        type: 'bug',
        content: '',
        images: [],
        contact: ''
      },
      feedbackTypes: [
        {label: '功能异常', value: 'bug', icon: 'error-circle'},
        {label: '功能建议', value: 'suggestion', icon: 'error-circle'},
        {label: '界面优化', value: 'ui', icon: 'error-circle'},
        {label: '其他问题', value: 'other', icon: 'question-circle'}
      ]
    }
  },

  computed: {
    isFormValid() {
      return this.formData.type && this.formData.content.trim().length > 0
    }
  },

  methods: {
    // 选择反馈类型
    selectType(type) {
      this.formData.type = type
    },

    // 上传图片
    handleUploadImage() {
      uni.chooseImage({
        count: 3 - this.formData.images.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.formData.images.push(...res.tempFilePaths)
        }
      })
    },

    // 删除图片
    removeImage(index) {
      this.formData.images.splice(index, 1)
    },

    // 提交反馈
    async handleSubmit() {
      if (!this.isFormValid) {
        uni.showToast({
          title: '请完善反馈内容',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({
          title: '提交中...'
        })

        // 这里应该调用提交反馈的API
        // 由于没有相关API，这里模拟提交成功
        await this.submitFeedback()

        uni.hideLoading()
        uni.showToast({
          title: '反馈提交成功',
          icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: error.message || '提交失败',
          icon: 'none'
        })
      }
    },

    // 模拟提交反馈
    submitFeedback() {
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log('提交反馈:', this.formData)
          resolve()
        }, 1000)
      })
    },

    // 返回上一页
    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.feedback-container {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.content-container {
  padding: 20px;
}

.form-section {
  background: #fff;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.form-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
}

.form-subtitle {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-bottom: 24px;
}

.form-item {
  margin-bottom: 24px;
}

.form-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.type-selector {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.type-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;

  &.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
  }
}

.type-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;

  .active & {
    color: #fff;
  }
}

.upload-section {
  margin-top: 8px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
}

.delete-btn {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  background: #ff6b6b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.upload-btn {
  width: 80px;
  height: 80px;
  background: #f8f9fa;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    background: #e9ecef;
    border-color: #667eea;
  }
}

.upload-text {
  font-size: 12px;
  color: #666;
}

.upload-tip {
  font-size: 12px;
  color: #999;
}

.submit-section {
  margin-top: 32px;
}
</style>
