// 积分商城列表页样式
.status-bar-placeholder {
  height: var(--status-bar-height, 44px);
}

.points-balance {
  background: linear-gradient(135deg, var(--primary-color), #4dabf7);
  padding: 48rpx 0 18rpx 0;
  text-align: center;
  color: #fff;
  border-radius: 0 0 32rpx 32rpx;
  margin-bottom: 24rpx;
}

.balance-label {
  display: block;
  font-size: 28rpx;
  margin-bottom: var(--spacing-sm);
  opacity: 0.9;
}

.balance-value {
  display: block;
  font-size: 72rpx;
  font-weight: bold;
}

.category-tabs {
  display: flex;
  background: var(--bg-primary);
  padding: 16rpx 0;
  border-bottom: 1px solid var(--border-color);
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: var(--text-secondary);
  border-radius: 32rpx;
  margin: 0 8rpx;
  transition: background 0.2s, color 0.2s;
  &.active {
    background: var(--primary-color);
    color: #fff;
    font-weight: bold;
    box-shadow: 0 2rpx 8rpx rgba(64,158,255,0.08);
  }
}

.goods-list {
  padding: 0 24rpx;
  margin-top: 8rpx;
}

.goods-item {
  display: flex;
  background: var(--bg-primary);
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  transition: box-shadow 0.2s;
  &:active {
    box-shadow: 0 8rpx 32rpx rgba(64,158,255,0.12);
  }
}

.goods-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 20rpx 0 0 20rpx;
  object-fit: cover;
}

.goods-info {
  flex: 1;
  padding: 24rpx 20rpx 20rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.goods-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.goods-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}

.goods-points {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-color);
}

.goods-stock {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.detail-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  animation: fadeIn 0.2s;
}

.dialog-content {
  background: var(--bg-primary);
  border-radius: 28rpx;
  width: 100%;
  max-width: 600rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: popIn 0.2s;
}

.detail-image {
  width: 100%;
  height: 320rpx;
  object-fit: cover;
  border-radius: 28rpx 28rpx 0 0;
}

.detail-info {
  width: 100%;
  padding: 32rpx 28rpx 16rpx 28rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.detail-name {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12rpx;
}

.detail-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 18rpx;
}

.detail-points {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.dialog-actions {
  width: 100%;
  display: flex;
  gap: 20rpx;
  padding: 24rpx 28rpx 32rpx 28rpx;
  border-top: 1px solid var(--border-color);
  background: var(--bg-primary);
  box-sizing: border-box;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  border: none;
  transition: background 0.2s, color 0.2s;
  &.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
  }
  &.btn-primary {
    background: var(--primary-color);
    color: #fff;
    &[disabled] {
      opacity: 0.5;
      background: var(--bg-secondary);
      color: var(--text-tertiary);
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
@keyframes popIn {
  from { transform: scale(0.95); }
  to { transform: scale(1); }
}