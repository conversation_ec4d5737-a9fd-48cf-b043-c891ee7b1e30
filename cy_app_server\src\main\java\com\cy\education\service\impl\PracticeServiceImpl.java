package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cy.education.model.entity.PracticeAnswer;
import com.cy.education.model.entity.PracticeRecord;
import com.cy.education.model.entity.PracticeStats;
import com.cy.education.model.entity.exam.ExamBank;
import com.cy.education.model.entity.exam.ExamQuestion;
import com.cy.education.repository.ExamBankMapper;
import com.cy.education.repository.ExamQuestionMapper;
import com.cy.education.repository.PracticeAnswerMapper;
import com.cy.education.repository.PracticeRecordMapper;
import com.cy.education.repository.PracticeStatsMapper;
import com.cy.education.service.PracticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 练习功能Service实现类
 */
@Service
public class PracticeServiceImpl implements PracticeService {
    
    @Autowired
    private ExamBankMapper examBankMapper;
    
    @Autowired
    private ExamQuestionMapper examQuestionMapper;
    
    @Autowired
    private PracticeRecordMapper practiceRecordMapper;
    
    @Autowired
    private PracticeAnswerMapper practiceAnswerMapper;
    
    @Autowired
    private PracticeStatsMapper practiceStatsMapper;
    
    @Override
    public List<ExamBank> getAvailableBanks() {
        QueryWrapper<ExamBank> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_practice_enabled", true);
        queryWrapper.eq("deleted", 0);
        List<ExamBank> banks = examBankMapper.selectList(queryWrapper);

        // 为每个题库设置题目数量
        for (ExamBank bank : banks) {
            QueryWrapper<ExamQuestion> questionQuery = new QueryWrapper<>();
            questionQuery.eq("bank_id", bank.getId());
            questionQuery.eq("deleted", 0);
            Long questionCount = examQuestionMapper.selectCount(questionQuery);
            bank.setTotalQuestions(questionCount.intValue());
        }

        return banks;
    }
    
    @Override
    public List<PracticeStats> getUserPracticeStats(Integer userId) {
        List<PracticeStats> stats = practiceStatsMapper.selectByUserId(userId);

        // 验证统计数据的一致性
        for (PracticeStats stat : stats) {
            validateAndFixStats(stat);
        }

        return stats;
    }
    
    @Override
    public PracticeStats getUserPracticeStatsByBank(Integer userId, Integer bankId) {
        PracticeStats stats = practiceStatsMapper.selectByUserIdAndBankId(userId, bankId);
        if (stats != null) {
            validateAndFixStats(stats);
        }
        return stats;
    }
    
    @Override
    @Transactional
    public Integer startPractice(Integer userId, Integer bankId, String type) {
        // 获取题目数量
        QueryWrapper<ExamQuestion> questionQuery = new QueryWrapper<>();
        questionQuery.eq("deleted", 0);
        if (bankId != null) {
            questionQuery.eq("bank_id", bankId);
        }
        
        // 如果是错题练习，获取错题
        List<ExamQuestion> questions;
        if ("wrong".equals(type)) {
            questions = getWrongQuestions(userId, bankId);
            System.out.println("错题练习 - 用户ID: " + userId + ", 题库ID: " + bankId + ", 错题数量: " + questions.size());
        } else {
            questions = examQuestionMapper.selectList(questionQuery);
            System.out.println("正常练习 - 题库ID: " + bankId + ", 题目数量: " + questions.size());
        }

        if (questions.isEmpty()) {
            throw new RuntimeException("没有可练习的题目");
        }
        
        // 创建练习记录
        PracticeRecord record = PracticeRecord.builder()
                .userId(userId)
                .bankId(bankId)
                .type(type)
                .totalQuestions(questions.size())
                .answeredQuestions(0)
                .correctCount(0)
                .wrongCount(0)
                .status("ongoing")
                .startTime(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        
        practiceRecordMapper.insert(record);
        return record.getId();
    }
    
    @Override
    public ExamQuestion getNextQuestion(Integer recordId) {
        // 获取练习记录
        PracticeRecord record = practiceRecordMapper.selectById(recordId);
        if (record == null) {
            throw new RuntimeException("练习记录不存在");
        }
        
        // 获取已答题目
        QueryWrapper<PracticeAnswer> answerQuery = new QueryWrapper<>();
        answerQuery.eq("record_id", recordId);
        List<PracticeAnswer> answeredList = practiceAnswerMapper.selectList(answerQuery);
        Set<Integer> answeredQuestionIds = new HashSet<>();
        for (PracticeAnswer answer : answeredList) {
            answeredQuestionIds.add(answer.getQuestionId());
        }
        
        // 获取可用题目
        QueryWrapper<ExamQuestion> questionQuery = new QueryWrapper<>();
        questionQuery.eq("deleted", 0);
        if (record.getBankId() != null) {
            questionQuery.eq("bank_id", record.getBankId());
        }
        
        List<ExamQuestion> allQuestions;
        if ("wrong".equals(record.getType())) {
            allQuestions = getWrongQuestions(record.getUserId(), record.getBankId());
        } else {
            allQuestions = examQuestionMapper.selectList(questionQuery);
        }
        
        // 找到未答题目
        for (ExamQuestion question : allQuestions) {
            if (!answeredQuestionIds.contains(question.getId())) {
                return question;
            }
        }
        
        return null; // 没有更多题目
    }
    
    @Override
    public Map<String, Object> getNextQuestionWithProgress(Integer recordId) {
        // 获取练习记录
        PracticeRecord record = practiceRecordMapper.selectById(recordId);
        if (record == null) {
            throw new RuntimeException("练习记录不存在");
        }
        
        // 获取已答题目
        QueryWrapper<PracticeAnswer> answerQuery = new QueryWrapper<>();
        answerQuery.eq("record_id", recordId);
        List<PracticeAnswer> answeredList = practiceAnswerMapper.selectList(answerQuery);
        Set<Integer> answeredQuestionIds = new HashSet<>();
        for (PracticeAnswer answer : answeredList) {
            answeredQuestionIds.add(answer.getQuestionId());
        }
        
        // 获取可用题目
        QueryWrapper<ExamQuestion> questionQuery = new QueryWrapper<>();
        questionQuery.eq("deleted", 0);
        if (record.getBankId() != null) {
            questionQuery.eq("bank_id", record.getBankId());
        }
        
        List<ExamQuestion> allQuestions;
        if ("wrong".equals(record.getType())) {
            allQuestions = getWrongQuestions(record.getUserId(), record.getBankId());
            System.out.println("getNextQuestionWithProgress - 错题练习，错题数量: " + allQuestions.size());
        } else {
            allQuestions = examQuestionMapper.selectList(questionQuery);
            System.out.println("getNextQuestionWithProgress - 正常练习，题目数量: " + allQuestions.size());
        }
        
        // 找到未答题目
        ExamQuestion nextQuestion = null;
        for (ExamQuestion question : allQuestions) {
            if (!answeredQuestionIds.contains(question.getId())) {
                nextQuestion = question;
                break;
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        if (nextQuestion != null) {
            result.put("question", nextQuestion);
            result.put("hasNext", true);
        } else {
            result.put("hasNext", false);
            result.put("message", "没有更多题目");
        }
        
        // 添加进度信息
        result.put("totalQuestions", allQuestions.size());
        result.put("answeredQuestions", answeredQuestionIds.size());
        result.put("currentQuestionIndex", answeredQuestionIds.size() + 1);
        result.put("progress", allQuestions.size() > 0 ? 
            (double) answeredQuestionIds.size() / allQuestions.size() * 100 : 0);
        
        // 添加统计信息
        result.put("correctCount", record.getCorrectCount());
        result.put("wrongCount", record.getWrongCount());
        
        return result;
    }
    
    @Override
    @Transactional
    public Map<String, Object> submitAnswer(Integer recordId, Integer questionId, String userAnswer) {
        // 获取练习记录
        PracticeRecord record = practiceRecordMapper.selectById(recordId);
        if (record == null) {
            throw new RuntimeException("练习记录不存在");
        }
        
        // 获取题目
        ExamQuestion question = examQuestionMapper.selectById(questionId);
        if (question == null) {
            throw new RuntimeException("题目不存在");
        }
        
        // 判断答案是否正确
        boolean isCorrect = checkAnswer(question, userAnswer);
        
        // 保存答案
        PracticeAnswer answer = PracticeAnswer.builder()
                .recordId(recordId)
                .questionId(questionId)
                .userAnswer(userAnswer)
                .correctAnswer(question.getCorrectAnswer())
                .isCorrect(isCorrect)
                .answerTime(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .build();
        
        practiceAnswerMapper.insert(answer);
        
        // 更新练习记录
        record.setAnsweredQuestions(record.getAnsweredQuestions() + 1);
        if (isCorrect) {
            record.setCorrectCount(record.getCorrectCount() + 1);
        } else {
            record.setWrongCount(record.getWrongCount() + 1);
        }
        record.setUpdatedAt(LocalDateTime.now());
        practiceRecordMapper.updateById(record);
        
        // 更新统计信息
        updatePracticeStats(record.getUserId(), question.getBankId());
        
        Map<String, Object> result = new HashMap<>();
        result.put("isCorrect", isCorrect);
        result.put("correctAnswer", question.getCorrectAnswer());
        result.put("explanation", question.getExplanation());
        result.put("progress", (double) record.getAnsweredQuestions() / record.getTotalQuestions());
        
        return result;
    }
    
    @Override
    @Transactional
    public Map<String, Object> completePractice(Integer recordId) {
        // 获取练习记录
        PracticeRecord record = practiceRecordMapper.selectById(recordId);
        if (record == null) {
            throw new RuntimeException("练习记录不存在");
        }
        
        // 更新练习记录状态
        record.setStatus("completed");
        record.setEndTime(LocalDateTime.now());
        record.setUpdatedAt(LocalDateTime.now());
        
        // 计算得分
        if (record.getTotalQuestions() > 0) {
            int score = (int) ((double) record.getCorrectCount() / record.getTotalQuestions() * 100);
            record.setScore(score);
        }
        
        practiceRecordMapper.updateById(record);
        
        Map<String, Object> result = new HashMap<>();
        result.put("totalQuestions", record.getTotalQuestions());
        result.put("correctCount", record.getCorrectCount());
        result.put("wrongCount", record.getWrongCount());
        result.put("score", record.getScore());
        result.put("accuracy", record.getTotalQuestions() > 0 ? 
                (double) record.getCorrectCount() / record.getTotalQuestions() * 100 : 0);
        
        return result;
    }
    
    @Override
    public List<ExamQuestion> getWrongQuestions(Integer userId, Integer bankId) {
        List<PracticeAnswer> wrongAnswers;
        if (bankId != null) {
            wrongAnswers = practiceAnswerMapper.selectWrongAnswersByUserIdAndBankId(userId, bankId);
            System.out.println("getWrongQuestions - 用户ID: " + userId + ", 题库ID: " + bankId + ", 错题答案记录数: " + wrongAnswers.size());
        } else {
            wrongAnswers = practiceAnswerMapper.selectWrongAnswersByUserId(userId);
            System.out.println("getWrongQuestions - 用户ID: " + userId + ", 全部题库错题答案记录数: " + wrongAnswers.size());
        }

        Set<Integer> questionIds = new HashSet<>();
        for (PracticeAnswer answer : wrongAnswers) {
            questionIds.add(answer.getQuestionId());
        }

        if (questionIds.isEmpty()) {
            System.out.println("getWrongQuestions - 没有错题ID，返回空列表");
            return new ArrayList<>();
        }

        QueryWrapper<ExamQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", questionIds);
        queryWrapper.eq("deleted", 0);

        List<ExamQuestion> questions = examQuestionMapper.selectList(queryWrapper);
        System.out.println("getWrongQuestions - 最终错题数量: " + questions.size());
        return questions;
    }
    
    @Override
    public List<PracticeRecord> getUserPracticeRecords(Integer userId) {
        QueryWrapper<PracticeRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("created_at");
        return practiceRecordMapper.selectList(queryWrapper);
    }
    
    @Override
    public Map<String, Object> getPracticeRecordDetail(Integer recordId) {
        PracticeRecord record = practiceRecordMapper.selectById(recordId);
        if (record == null) {
            throw new RuntimeException("练习记录不存在");
        }
        
        List<PracticeAnswer> answers = practiceAnswerMapper.selectByRecordId(recordId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("record", record);
        result.put("answers", answers);
        
        return result;
    }
    
    /**
     * 检查答案是否正确
     */
    private boolean checkAnswer(ExamQuestion question, String userAnswer) {
        if (userAnswer == null || question.getCorrectAnswer() == null) {
            return false;
        }
        
        // 多选题需要按顺序比较
        if ("multiple".equals(question.getType())) {
            String[] userAnswerArray = userAnswer.split(",");
            String[] correctAnswerArray = question.getCorrectAnswer().split(",");
            
            Arrays.sort(userAnswerArray);
            Arrays.sort(correctAnswerArray);
            
            return Arrays.equals(userAnswerArray, correctAnswerArray);
        } else {
            return userAnswer.trim().equalsIgnoreCase(question.getCorrectAnswer().trim());
        }
    }
    
    /**
     * 更新练习统计信息
     */
    private void updatePracticeStats(Integer userId, Integer bankId) {
        PracticeStats stats = practiceStatsMapper.selectByUserIdAndBankId(userId, bankId);
        
        if (stats == null) {
            // 创建新的统计记录
            QueryWrapper<ExamQuestion> questionQuery = new QueryWrapper<>();
            questionQuery.eq("bank_id", bankId);
            questionQuery.eq("deleted", 0);
            Long totalQuestionsLong = examQuestionMapper.selectCount(questionQuery);
            int totalQuestions = totalQuestionsLong.intValue();
            
            stats = PracticeStats.builder()
                    .userId(userId)
                    .bankId(bankId)
                    .totalQuestions(totalQuestions)
                    .answeredQuestions(0)
                    .correctCount(0)
                    .wrongCount(0)
                    .accuracyRate(BigDecimal.ZERO)
                    .lastPracticeTime(LocalDateTime.now())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            
            practiceStatsMapper.insert(stats);
        } else {
            // 更新统计信息
            stats.setLastPracticeTime(LocalDateTime.now());
            stats.setUpdatedAt(LocalDateTime.now());
            
            // 重新计算统计数据
            // 这里可以根据实际需求实现更复杂的统计逻辑
            
            practiceStatsMapper.updateById(stats);
        }
    }

    @Override
    @Transactional
    public Map<String, Object> clearWrongQuestions(Integer userId, Integer bankId) {
        try {
            if (bankId != null) {
                // 清空指定题库的错题
                practiceAnswerMapper.deleteWrongAnswersByUserIdAndBankId(userId, bankId);
            } else {
                // 清空所有错题
                practiceAnswerMapper.deleteWrongAnswersByUserId(userId);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "错题本已清空");
            return result;
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "清空失败：" + e.getMessage());
            return result;
        }
    }

    /**
     * 验证并修复统计数据的一致性
     */
    private void validateAndFixStats(PracticeStats stats) {
        if (stats == null) return;

        // 确保基本数据不为null
        if (stats.getTotalQuestions() == null) stats.setTotalQuestions(0);
        if (stats.getAnsweredQuestions() == null) stats.setAnsweredQuestions(0);
        if (stats.getCorrectCount() == null) stats.setCorrectCount(0);
        if (stats.getWrongCount() == null) stats.setWrongCount(0);
        if (stats.getAccuracyRate() == null) stats.setAccuracyRate(BigDecimal.ZERO);

        // 验证数据一致性：正确数 + 错误数 应该等于已答题数
        int calculatedAnswered = stats.getCorrectCount() + stats.getWrongCount();
        if (calculatedAnswered != stats.getAnsweredQuestions()) {
            System.out.println("统计数据不一致 - 用户ID: " + stats.getUserId() +
                ", 题库ID: " + stats.getBankId() +
                ", 已答题数: " + stats.getAnsweredQuestions() +
                ", 正确数: " + stats.getCorrectCount() +
                ", 错误数: " + stats.getWrongCount() +
                ", 计算的已答题数: " + calculatedAnswered);

            // 修复：使用正确数+错误数作为已答题数
            stats.setAnsweredQuestions(calculatedAnswered);
        }

        // 重新计算正确率
        if (stats.getAnsweredQuestions() > 0) {
            BigDecimal accuracyRate = new BigDecimal(stats.getCorrectCount())
                .divide(new BigDecimal(stats.getAnsweredQuestions()), 4, RoundingMode.HALF_UP);
            stats.setAccuracyRate(accuracyRate);
        } else {
            stats.setAccuracyRate(BigDecimal.ZERO);
        }

        // 确保已答题数不超过总题数
        if (stats.getAnsweredQuestions() > stats.getTotalQuestions()) {
            System.out.println("已答题数超过总题数 - 用户ID: " + stats.getUserId() +
                ", 题库ID: " + stats.getBankId() +
                ", 总题数: " + stats.getTotalQuestions() +
                ", 已答题数: " + stats.getAnsweredQuestions());
        }
    }


}