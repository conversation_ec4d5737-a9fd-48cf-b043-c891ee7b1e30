// 状态标签栏
.status-tabs {
  background: #fff;
  border-bottom: 1px solid #f0f2f5;
  position: sticky;
  top: 0;
  z-index: 999;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  padding: 16px 20px;
  gap: 16px;
}

.tab-item {
  flex-shrink: 0;
  padding: 8px 20px;
  border-radius: 20px;
  background: #f5f7ff;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.tab-text {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
  white-space: nowrap;
}

.tab-item.active .tab-text {
  color: #fff;
  font-weight: 600;
}

// 页面内容
.page-content {
  padding: 20px;
  padding-bottom: 120px;
}

// 练习入口
.practice-section {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.practice-card {
  background: #fff;
  border-radius: 16px;
  padding: 16px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }
}

.practice-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.practice-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;

  &.wrong {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  }

  &.pk {
    background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  }
}

.practice-content {
  flex: 1;
}

.practice-title {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 4px;
}

.practice-desc {
  font-size: 13px;
  color: #8e8e93;
  font-weight: 500;
}

.practice-right {
  margin-left: 12px;
}

// 统计卡片
.stats-section {
  margin: 16px 0;
}

.stats-card {
  background: #fff;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.stats-header {
  margin-bottom: 20px;
  text-align: center;
}

.stats-title {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #1a1d2e;
  margin-bottom: 4px;
}

.stats-subtitle {
  font-size: 13px;
  color: #8e8e93;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 6px;
  line-height: 1;
}

.stat-number.participated {
  color: #3b82f6;
}

.stat-number.completed {
  color: #10b981;
}

.stat-number.passed {
  color: #f59e0b;
}

.stat-label {
  font-size: 13px;
  color: #8e8e93;
  font-weight: 500;
}

.stats-details {
  border-top: 1px solid #f0f2f5;
  padding-top: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.detail-label {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;

  &.not-participated {
    color: #ef4444;
  }

  &.ongoing {
    color: #f59e0b;
  }

  &.average {
    color: #3b82f6;
  }

  &.pass-rate {
    color: #10b981;
  }
}

// 考试列表
.exam-section {
  margin-bottom: 24px;
}

.exam-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.exam-card {
  position: relative;
  background: #fff;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  margin-bottom: 16px;

  .status-tag {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 4px 14px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 600;
    z-index: 2;

    &.upcoming {
      background: rgba(245, 158, 11, 0.1);
      color: #f59e0b;
    }

    &.ongoing {
      background: rgba(239, 68, 68, 0.1);
      color: #ef4444;
    }

    &.completed {
      background: rgba(16, 185, 129, 0.1);
      color: #10b981;
    }
  }

  .exam-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 8px;
  }

  .exam-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(102, 126, 234, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .exam-title-desc {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .exam-title {
    font-size: 17px;
    font-weight: 700;
    color: #1a1d2e;
    line-height: 1.4;
    margin-bottom: 2px;
    word-break: break-all;
  }

  .exam-desc {
    font-size: 13px;
    color: #8e8e93;
    line-height: 1.5;
    max-height: 2.8em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .exam-info {
    margin-top: 8px;

    .info-row {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 6px;

      .info-text {
        font-size: 13px;
        color: #4a5568;
        font-weight: 500;
      }

      .score.passed {
        color: #10b981;
        font-weight: 600;
      }

      .score.failed {
        color: #ef4444;
        font-weight: 600;
      }
    }
  }

  .exam-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .exam-action {
    flex-shrink: 0;
    margin-left: auto;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

// 加载更多
.load-more {
  text-align: center;
  padding: 20px;
  margin-top: 20px;
}

.loading-text,
.nomore-text,
.loadmore-btn {
  font-size: 14px;
  color: #8e8e93;
  padding: 12px 24px;
  border-radius: 20px;
  background: #f5f7ff;
}

.loadmore-btn {
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
}

.loadmore-btn:active {
  background: #e8f2ff;
  transform: scale(0.95);
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  color: #4a5568;
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-desc {
  font-size: 14px;
  color: #8e8e93;
}

// 操作按钮样式
.action-btn {
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
  min-width: 80px;
  transition: background 0.2s, color 0.2s;
  cursor: pointer;
}

.action-btn.primary {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #667eea;
  border: 1px solid #667eea;
}

.action-btn.disabled {
  background: #f5f5f5;
  color: #bbb;
  border: 1px solid #eee;
  cursor: not-allowed;
}
