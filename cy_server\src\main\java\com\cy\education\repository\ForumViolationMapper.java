package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.ForumViolation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 论坛违规举报Mapper接口
 */
@Repository
public interface ForumViolationMapper extends BaseMapper<ForumViolation> {
    
    /**
     * 查询违规举报列表（带举报人和处理人信息，以及被举报内容）
     * 
     * @param page 分页对象
     * @param contentType 内容类型
     * @param status 状态
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT v.*, s.name as reporter, a.name as operator, " +
            "CASE v.content_type " +
            "WHEN 'post' THEN (SELECT title FROM forum_posts WHERE id = v.content_id) " +
            "WHEN 'comment' THEN (SELECT content FROM forum_comments WHERE id = v.content_id) " +
            "END as content " +
            "FROM forum_violations v " +
            "LEFT JOIN students s ON v.reporter_id = s.id " +
            "LEFT JOIN admins a ON v.operator_id = a.id " +
            "<where> " +
            "<if test='contentType != null and contentType != \"\"'> AND v.content_type = #{contentType} </if> " +
            "<if test='status != null'> AND v.status = #{status} </if> " +
            "</where> " +
            "ORDER BY v.created_at DESC" +
            "</script>")
    IPage<ForumViolation> selectViolationPage(Page<ForumViolation> page, 
                                            @Param("contentType") String contentType,
                                            @Param("status") Integer status);
    
    /**
     * 根据ID查询违规举报详情
     * 
     * @param id 违规举报ID
     * @return 违规举报详情
     */
    @Select("SELECT v.*, s.name as reporter, a.name as operator, " +
            "CASE v.content_type " +
            "WHEN 'post' THEN (SELECT title FROM forum_posts WHERE id = v.content_id) " +
            "WHEN 'comment' THEN (SELECT content FROM forum_comments WHERE id = v.content_id) " +
            "END as content " +
            "FROM forum_violations v " +
            "LEFT JOIN students s ON v.reporter_id = s.id " +
            "LEFT JOIN admins a ON v.operator_id = a.id " +
            "WHERE v.id = #{id}")
    ForumViolation selectViolationWithDetailsById(@Param("id") Integer id);
    
    /**
     * 更新违规举报处理状态
     * 
     * @param violationId 违规举报ID
     * @param status 状态
     * @param processType 处理类型：0无操作，1删除，2警告
     * @param processResult 处理结果
     * @param operatorId 处理人ID
     * @return 影响行数
     */
    @Update("UPDATE forum_violations SET status = #{status}, process_type = #{processType}, " +
            "process_result = #{processResult}, process_time = NOW(), operator_id = #{operatorId} " +
            "WHERE id = #{violationId}")
    int updateStatus(@Param("violationId") Integer violationId, 
                    @Param("status") Integer status,
                    @Param("processType") Integer processType,
                    @Param("processResult") String processResult, 
                    @Param("operatorId") Integer operatorId);
} 