package com.cy.education.model.entity.forum;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 论坛帖子实体类
 */
@Data
@TableName("forum_posts")
public class ForumPost implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 帖子ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 帖子标题
     */
    private String title;

    /**
     * 帖子内容
     */
    private String content;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 作者ID
     */
    private Integer authorId;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 回复数
     */
    private Integer replyCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 状态：0待审核，1已通过，2已拒绝，3已删除
     */
    private Integer status;

    /**
     * 是否置顶
     */
    private Boolean isTop;

    /**
     * 是否精华
     */
    private Boolean isEssence;

    /**
     * 图片列表
     */
    private String images;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 作者名称（非数据库字段）
     */
    @TableField(exist = false)
    private String author;

    /**
     * 作者头像（非数据库字段）
     */
    @TableField(exist = false)
    private String authorAvatar;

    /**
     * 分类名称（非数据库字段）
     */
    @TableField(exist = false)
    private String category;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 分享数
     */
    private Integer shareCount;

    /**
     * 当前用户是否点赞（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean isLiked;

    /**
     * 当前用户是否收藏（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean isCollected;

    /**
     * 当前用户是否已关注作者
     */
    @TableField(exist = false)
    private Boolean isFollowing;
}
