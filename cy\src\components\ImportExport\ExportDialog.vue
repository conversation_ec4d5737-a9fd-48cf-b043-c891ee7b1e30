<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="500px"
    destroy-on-close
    @closed="handleClosed"
  >
    <div class="export-container">
      <el-form :model="exportForm" label-width="100px">
        <!-- 导出格式 -->
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="xlsx">Excel (.xlsx)</el-radio>
            <el-radio label="csv" v-if="supportCsv">CSV (.csv)</el-radio>
            <el-radio label="pdf" v-if="supportPdf">PDF (.pdf)</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 导出范围 -->
        <el-form-item label="导出范围" v-if="supportRange">
          <el-radio-group v-model="exportForm.range">
            <el-radio label="all">全部数据</el-radio>
            <el-radio label="current">当前页面</el-radio>
            <el-radio label="selected" v-if="selectedCount > 0">
              已选择 ({{ selectedCount }} 条)
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 时间范围 -->
        <el-form-item label="时间范围" v-if="supportTimeRange">
          <el-date-picker
            v-model="exportForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <!-- 自定义字段选择 -->
        <el-form-item label="导出字段" v-if="fields && fields.length > 0">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox
              v-for="field in fields"
              :key="field.key"
              :label="field.key"
              :disabled="field.required"
            >
              {{ field.label }}
            </el-checkbox>
          </el-checkbox-group>
          <div class="field-actions">
            <el-button type="text" @click="selectAllFields">全选</el-button>
            <el-button type="text" @click="clearAllFields">清空</el-button>
          </div>
        </el-form-item>

        <!-- 特殊选项 -->
        <el-form-item v-if="specialOptions && specialOptions.length > 0">
          <el-checkbox-group v-model="exportForm.options">
            <el-checkbox
              v-for="option in specialOptions"
              :key="option.key"
              :label="option.key"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 导出说明 -->
        <el-form-item v-if="description">
          <el-alert
            :title="description"
            type="info"
            :closable="false"
            show-icon
          />
        </el-form-item>
      </el-form>

      <!-- 导出进度 -->
      <div v-if="exporting" class="export-progress">
        <el-progress :percentage="exportProgress" />
        <div class="progress-text">正在生成文件...</div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="startExport" 
          :loading="exporting"
          :disabled="!canExport"
        >
          开始导出
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

interface ExportField {
  key: string
  label: string
  required?: boolean
}

interface ExportOption {
  key: string
  label: string
}

interface Props {
  modelValue: boolean
  title: string
  exportUrl: string
  supportCsv?: boolean
  supportPdf?: boolean
  supportRange?: boolean
  supportTimeRange?: boolean
  selectedCount?: number
  fields?: ExportField[]
  specialOptions?: ExportOption[]
  description?: string
  defaultParams?: Record<string, any>
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  supportCsv: false,
  supportPdf: false,
  supportRange: true,
  supportTimeRange: false,
  selectedCount: 0
})

const emit = defineEmits<Emits>()

const exporting = ref(false)
const exportProgress = ref(0)

const exportForm = ref({
  format: 'xlsx',
  range: 'all',
  timeRange: null as [string, string] | null,
  fields: [] as string[],
  options: [] as string[]
})

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const canExport = computed(() => {
  if (props.fields && props.fields.length > 0) {
    return exportForm.value.fields.length > 0
  }
  return true
})

// 初始化字段选择
watch(() => props.fields, (newFields) => {
  if (newFields && newFields.length > 0) {
    exportForm.value.fields = newFields
      .filter(field => field.required)
      .map(field => field.key)
  }
}, { immediate: true })

const selectAllFields = () => {
  if (props.fields) {
    exportForm.value.fields = props.fields.map(field => field.key)
  }
}

const clearAllFields = () => {
  if (props.fields) {
    exportForm.value.fields = props.fields
      .filter(field => field.required)
      .map(field => field.key)
  }
}

const startExport = async () => {
  exporting.value = true
  exportProgress.value = 0

  try {
    // 构建导出参数
    const params = {
      format: exportForm.value.format,
      range: exportForm.value.range,
      fields: exportForm.value.fields,
      options: exportForm.value.options,
      ...props.defaultParams
    }

    if (exportForm.value.timeRange) {
      params.startTime = exportForm.value.timeRange[0]
      params.endTime = exportForm.value.timeRange[1]
    }

    // 发起导出请求
    const response = await fetch(props.exportUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(params)
    })

    if (!response.ok) {
      throw new Error('导出失败')
    }

    // 处理文件下载
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    // 从响应头获取文件名，或使用默认文件名
    const contentDisposition = response.headers.get('Content-Disposition')
    let filename = `${props.title}_${new Date().toISOString().slice(0, 10)}.${exportForm.value.format}`
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }
    
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    exportProgress.value = 100
    ElMessage.success('导出成功')
    emit('success')
    visible.value = false
    
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const handleClosed = () => {
  exporting.value = false
  exportProgress.value = 0
  exportForm.value = {
    format: 'xlsx',
    range: 'all',
    timeRange: null,
    fields: [],
    options: []
  }
  
  // 重新初始化字段选择
  if (props.fields && props.fields.length > 0) {
    exportForm.value.fields = props.fields
      .filter(field => field.required)
      .map(field => field.key)
  }
}
</script>

<style scoped>
.export-container {
  padding: 20px 0;
}

.field-actions {
  margin-top: 8px;
}

.field-actions .el-button {
  padding: 0;
  margin-right: 16px;
}

.export-progress {
  margin-top: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
