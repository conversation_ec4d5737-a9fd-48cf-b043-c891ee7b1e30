package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 练习记录实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("practice_record")
public class PracticeRecord implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 学员ID
     */
    private Integer userId;
    
    /**
     * 题库ID(为空表示全部题库)
     */
    private Integer bankId;
    
    /**
     * 练习类型(normal-正常练习，wrong-错题练习)
     */
    private String type;
    
    /**
     * 总题数
     */
    private Integer totalQuestions;
    
    /**
     * 已答题数
     */
    private Integer answeredQuestions;
    
    /**
     * 正确数
     */
    private Integer correctCount;
    
    /**
     * 错误数
     */
    private Integer wrongCount;
    
    /**
     * 得分
     */
    private Integer score;
    
    /**
     * 状态(ongoing-进行中,completed-已完成,abandoned-已放弃)
     */
    private String status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 练习类型枚举
     */
    public enum PracticeType {
        NORMAL("normal"),
        WRONG("wrong");
        
        private final String value;
        
        PracticeType(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
    }
    
    /**
     * 练习状态枚举
     */
    public enum PracticeStatus {
        ONGOING("ongoing"),
        COMPLETED("completed"),
        ABANDONED("abandoned");
        
        private final String value;
        
        PracticeStatus(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
    }
} 