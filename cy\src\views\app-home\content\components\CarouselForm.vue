<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑轮播图' : '新增轮播图'"
    width="600px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="轮播图片" prop="imageUrl">
        <image-upload
          v-model="form.imageUrl"
          :max-size="10"
          :width="1920"
          :height="1080"
          tip="请上传比例为16:9的轮播图片，推荐尺寸为1920×1080，支持JPG、PNG格式，最大10MB"
        />
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入轮播图标题" />
      </el-form-item>
      <el-form-item label="跳转链接" prop="link">
        <el-input v-model="form.link" placeholder="请输入跳转链接（可选）" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort" :min="0" :max="999" controls-position="right" />
      </el-form-item>
      <el-form-item label="状态">
        <el-switch
          v-model="form.status"
          :active-value="1"
          :inactive-value="0"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import ImageUpload from '@/components/Upload/ImageUpload.vue'

interface CarouselForm {
  id?: number
  imageUrl: string
  title: string
  link: string
  sort: number
  status: number
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  carouselData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<CarouselForm>({
  id: undefined,
  imageUrl: '',
  title: '',
  link: '',
  sort: 0,
  status: 1
})

// 表单验证规则
const rules = {
  imageUrl: [{ required: true, message: '请上传轮播图片', trigger: 'change' }],
  title: [{ required: true, message: '请输入轮播图标题', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
}

// 监听属性变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(() => props.carouselData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(form, {
      id: newVal.id,
      imageUrl: newVal.imageUrl || '',
      title: newVal.title || '',
      link: newVal.link || '',
      sort: newVal.sort || 0,
      status: newVal.status !== undefined ? newVal.status : 1
    })
  }
}, { deep: true, immediate: true })



// 提交表单
const submitForm = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      emit('submit', { ...form })
      visible.value = false
    }
  })
}

// 关闭弹窗重置表单
const handleClosed = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    id: undefined,
    imageUrl: '',
    title: '',
    link: '',
    sort: 0,
    status: 1
  })
}
</script>

<style scoped>
/* 轮播图表单样式 */
</style>