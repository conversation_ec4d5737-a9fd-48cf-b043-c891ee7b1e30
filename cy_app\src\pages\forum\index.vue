<template>
  <view class="page-container">
    <!-- 渐变头部 -->
    <view class="custom-navbar">
      <!-- 导航栏 -->
      <view class="navbar-content">
        <view class="navbar-left">
          <text class="navbar-title">学习论坛</text>
        </view>
        <view class="navbar-right">
          <view class="navbar-search">
            <view class="search-input-wrapper">
              <up-icon color="#8e8e93" name="search" size="16"></up-icon>
              <input
                  v-model="searchKeyword"
                  class="search-input"
                  placeholder="搜索帖子..."
                  @input="onSearchChange"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主内容区域 -->
    <view class="main-content">
      <!-- 分类标签 -->
      <view class="category-section">
        <!-- 一级分类 -->
        <view class="primary-categories">
          <up-tabs
              :current="currentPrimaryIndex"
              :list="primaryCategoryTabs"
              activeStyle="color: #667eea; font-weight: 600;"
              inactiveStyle="color: #909399;"
              lineColor="#667eea"
              lineHeight="4"
              lineWidth="30"
              @change="onPrimaryCategoryChange"
          ></up-tabs>
        </view>

        <!-- 二级分类 -->
        <view v-if="currentSecondaryTabs.length > 0" class="secondary-categories">
          <up-tabs
              :current="currentSecondaryIndex"
              :list="currentSecondaryTabs"
              activeStyle="color: #764ba2; font-weight: 500; font-size: 13px;"
              inactiveStyle="color: #909399; font-size: 13px;"
              lineColor="#764ba2"
              lineHeight="3"
              lineWidth="20"
              @change="onSecondaryCategoryChange"
          ></up-tabs>
        </view>
      </view>

      <!-- 排序和筛选 -->
      <view class="filter-section">
        <view class="sort-buttons">
          <up-button
              v-for="sort in sortOptions"
              :key="sort.value"
              :plain="currentSort !== sort.value"
              :text="sort.label"
              :type="currentSort === sort.value ? 'primary' : 'default'"
              customStyle="margin-right: 8px;"
              size="small"
              @click="switchSort(sort.value)"
          ></up-button>
        </view>
      </view>

      <!-- 帖子列表 -->
      <view class="posts-container">
        <view
            v-for="(post, index) in postList"
            :key="post.id"
            class="post-card"
        >
          <!-- 置顶/精华标识 -->
          <view class="post-badges">
            <up-tag
                :text="getCategoryName(post.categoryId)"
                :type="getCategoryType(post.categoryId)"
                plain
                size="mini"
            ></up-tag>
            <up-tag v-if="post.isTop" text="置顶" type="error" size="mini" plain></up-tag>
            <up-tag v-if="post.isEssence" text="精华" type="warning" size="mini" plain></up-tag>
          </view>

          <!-- 用户信息 -->
          <view class="post-header">
            <up-avatar
                :src="post.authorAvatar"
                size="40"
                @click="goToUserProfile(post)"
            ></up-avatar>
            <view class="user-info">
              <text class="username" @click="goToUserProfile(post)">{{ post.author }}</text>
            </view>
          </view>

          <!-- 帖子内容 -->
          <view class="post-content" @click="goToPostDetail(post)">
            <view class="post-title-row">
              <text class="post-title">{{ post.title }}</text>
              <view class="post-time-wrapper">
                <text class="post-time">{{ this.formatTime(post.createdAt) }}</text>
              </view>
            </view>
            <text class="post-excerpt">{{ post.content }}</text>
          </view>

          <!-- 交互统计 -->
          <view class="post-actions">
            <view class="action-stats">
              <view class="stat-item">
                <up-icon name="chat" color="#909399" size="16"></up-icon>
                <text>{{ post.replyCount }}</text>
              </view>
              <view class="stat-item" @click="toggleLike(post)">
                <up-icon
                    :color="post.isLiked ? '#f56c6c' : '#909399'"
                    name="thumb-up"
                    size="16"
                ></up-icon>
                <text :style="{ color: post.isLiked ? '#f56c6c' : '#909399' }">{{ post.likeCount }}</text>
              </view>
              <view class="stat-item" @click="toggleCollect(post)">
                <up-icon
                    :color="post.isCollected ? '#fa8c16' : '#909399'"
                    name="star"
                    size="16"
                ></up-icon>
                <text :style="{ color: post.isCollected ? '#fa8c16' : '#909399' }">{{ post.collectCount || 0 }}</text>
              </view>
              <view class="stat-item">
                <up-icon name="eye" color="#909399" size="16"></up-icon>
                <text>{{ post.viewCount }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <up-empty
            v-if="postList.length === 0 && !loading"
            mode="list"
            text="暂无相关帖子"
            textColor="#909399"
            textSize="14"
        >
          <template #bottom>
            <up-button type="primary" text="发布第一个帖子" size="small" @click="goToPost"></up-button>
          </template>
        </up-empty>

        <!-- 加载更多 -->
        <view class="load-more" v-if="postList.length > 0">
          <view v-if="loadMoreStatus === 'loading'" class="loading-text">
            <up-loading-icon color="#667eea" size="16"></up-loading-icon>
            <text>加载中...</text>
          </view>
          <view v-else-if="loadMoreStatus === 'noMore'" class="nomore-text">
            <text>没有更多了</text>
          </view>
          <view v-else class="loadmore-text">
            <text>加载更多</text>
          </view>
        </view>
      </view>

      <up-gap height="80"></up-gap>
    </view>

    <!-- 浮动发帖按钮 -->
    <view class="floating-post-btn" @click="goToCreatePost">
      <up-icon name="plus" color="#fff" size="24"></up-icon>
    </view>

    <!-- 底部导航栏 -->
    <custom-tabbar :current="3"></custom-tabbar>
  </view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar/index.vue'
import {getCategoryList, getPostList, likePost, unlikePost, collectPost, uncollectPost} from '@/api/forum'
import {formatTime} from "@/utils/timeUtil"

export default {
  components: {
    CustomTabbar
  },
  data() {
    return {
      searchKeyword: '',
      currentPrimaryIndex: 0,
      currentSecondaryIndex: 0,
      currentSort: 'latest',
      loading: true,
      hasMore: true,
      pageNum: 1,
      pageSize: 3,
      total: 0,
      isLoadingMore: false,
      loadMoreStatus: 'more',

      forumStats: {
        totalPosts: 0,
        todayPosts: 0,
        totalUsers: 0,
        hotDiscussion: 0
      },

      primaryCategoryTabs: [
        {name: '全部', id: 'all'}
      ],

      currentSecondaryTabs: [],

      sortOptions: [
        {value: 'latest', label: '最新'},
        {value: 'hot', label: '最热'},
        {value: 'essence', label: '精华'}
      ],

      postList: [],
      categories: [],
      searchTimer: null
    }
  },

  computed: {},

  onLoad() {
    this.loadCategories();
    this.fetchPosts();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.pageNum = 1;
    this.postList = [];
    this.hasMore = true;
    this.fetchPosts();
  },

  // 上拉加载更多
  onReachBottom() {
    if (!this.hasMore || this.isLoadingMore) return;
    this.pageNum += 1;
    this.fetchPosts(true);
  },

  methods: {
    // 格式化时间的方法
    formatTime(timeStr) {
      return formatTime(timeStr);
    },

    // 加载分类列表
    async loadCategories() {
      try {
        this.loading = true;
        const categories = await getCategoryList();
        this.categories = categories;

        // 构建一级分类标签
        this.primaryCategoryTabs = [
          {name: '全部', id: 'all'},
          ...categories.map(cat => ({
            name: cat.name,
            id: cat.id.toString(),
            hasChildren: cat.children && cat.children.length > 0
          }))
        ];

        // 初始化二级分类
        this.updateSecondaryCategories();
      } catch (error) {
        console.error('加载分类失败:', error);
        uni.showToast({
          title: '加载分类失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 更新二级分类
    updateSecondaryCategories() {
      const currentPrimaryCategory = this.primaryCategoryTabs[this.currentPrimaryIndex];
      if (currentPrimaryCategory && currentPrimaryCategory.id !== 'all') {
        const primaryCategory = this.categories.find(cat => cat.id === parseInt(currentPrimaryCategory.id));
        if (primaryCategory && primaryCategory.children && primaryCategory.children.length > 0) {
          this.currentSecondaryTabs = primaryCategory.children.map(child => ({
            name: child.name,
            id: child.id.toString()
          }));
          this.currentSecondaryIndex = 0;
        } else {
          this.currentSecondaryTabs = [];
          this.currentSecondaryIndex = 0;
        }
      } else {
        this.currentSecondaryTabs = [];
        this.currentSecondaryIndex = 0;
      }
    },

    // 获取分类及其子分类的ID列表
    getCategoryAndChildrenIds(categoryId) {
      const category = this.categories.find(cat => cat.id === categoryId);
      if (!category) return [categoryId];

      const ids = [categoryId];
      if (category.children && category.children.length > 0) {
        category.children.forEach(child => {
          ids.push(child.id);
        });
      }
      return ids;
    },

    // 加载帖子列表
    async fetchPosts(isLoadMore = false) {
      if (isLoadMore) {
        this.isLoadingMore = true;
        this.loadMoreStatus = 'loading';
      } else {
        this.loading = true;
        this.pageNum = 1;
        this.postList = [];
        this.hasMore = true;
      }

      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          keyword: this.searchKeyword || undefined,
          sortBy: this.currentSort
        };

        // 分类筛选逻辑
        const currentPrimaryCategory = this.primaryCategoryTabs[this.currentPrimaryIndex];
        const currentSecondaryCategory = this.currentSecondaryTabs[this.currentSecondaryIndex];

        if (currentPrimaryCategory && currentPrimaryCategory.id !== 'all') {
          if (currentSecondaryCategory) {
            // 如果有二级分类，按二级分类筛选
            params.categoryId = parseInt(currentSecondaryCategory.id);
          } else {
            // 如果只有一级分类，筛选该分类及其子分类的帖子
            const categoryIds = this.getCategoryAndChildrenIds(parseInt(currentPrimaryCategory.id));
            params.categoryIds = categoryIds;
          }
        }

        const response = await getPostList(params);
        this.total = response.total || 0;

        if (isLoadMore) {
          this.postList = this.postList.concat(response.list || []);
        } else {
          this.postList = response.list || [];
        }

        if (this.postList.length >= this.total) {
          this.hasMore = false;
          this.loadMoreStatus = 'noMore';
        } else {
          this.hasMore = true;
          this.loadMoreStatus = 'more';
        }

      } catch (error) {
        console.error('加载帖子列表失败:', error);
        uni.showToast({
          title: '加载帖子列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.isLoadingMore = false;
        uni.stopPullDownRefresh();
      }
    },

    onSearchChange(event) {
      // 在uni-app中，input事件传递的是事件对象，需要从event.detail.value获取值
      this.searchKeyword = event.detail.value;
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      // 防抖处理，500ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.fetchPosts();
      }, 500);
    },

    onPrimaryCategoryChange(event) {
      this.currentPrimaryIndex = event.index;
      this.currentSecondaryIndex = 0;
      // 更新二级分类
      this.updateSecondaryCategories();
      // 切换分类时重新加载数据
      this.fetchPosts();
    },

    onSecondaryCategoryChange(event) {
      this.currentSecondaryIndex = event.index;
      // 切换二级分类时重新加载数据
      this.fetchPosts();
    },

    switchSort(sortValue) {
      this.currentSort = sortValue;
      // 切换排序时重新加载数据
      this.fetchPosts();
    },

    getCategoryName(categoryId) {
      // 先在一级分类中查找
      let category = this.categories.find(cat => cat.id === categoryId);
      if (category) {
        return category.name;
      }

      // 在二级分类中查找
      for (const primaryCat of this.categories) {
        if (primaryCat.children) {
          const childCategory = primaryCat.children.find(child => child.id === categoryId);
          if (childCategory) {
            return childCategory.name;
          }
        }
      }

      return '未知';
    },

    getCategoryType(categoryId) {
      return "primary"
      // 根据分类ID返回不同的类型
      const typeMap = {
        1: 'primary',   // 学习交流
        2: 'warning',   // 问题求助
        3: 'success',   // 经验分享
        4: 'info'       // 讨论建议
      };
      return typeMap[categoryId] || 'default';
    },

    // 点赞/取消点赞
    async toggleLike(post) {
      if (!post) return;
      try {
        if (post.isLiked) {
          await unlikePost(post.id);
          post.likeCount -= 1;
        } else {
          await likePost(post.id);
          post.likeCount += 1;
        }
        post.isLiked = !post.isLiked;
        uni.showToast({
          title: post.isLiked ? '点赞成功' : '取消点赞',
          icon: 'none',
          duration: 1500
        });
      } catch (e) {
        uni.showToast({title: '操作失败', icon: 'none'});
      }
    },
    // 收藏/取消收藏
    async toggleCollect(post) {
      if (!post) return;
      try {
        if (post.isCollected) {
          await uncollectPost(post.id);
        } else {
          await collectPost(post.id);
        }
        post.isCollected = !post.isCollected;
        uni.showToast({
          title: post.isCollected ? '收藏成功' : '取消收藏',
          icon: 'none',
          duration: 1500
        });
      } catch (e) {
        uni.showToast({title: '操作失败', icon: 'none'});
      }
    },

    goToPost() {
      uni.navigateTo({
        url: '/pages/forum/post'
      });
    },

    goToPostDetail(post) {
      uni.navigateTo({
        url: `/pages/forum/detail?id=${post.id}`
      });
    },
    goToUserProfile(post) {
      uni.navigateTo({
        url: `/pages/profile/user-profile?userId=${post.authorId}`
      });
    },
    goToCreatePost() {
      uni.navigateTo({
        url: '/pages/forum/create-post'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/pages/forum/forum.scss';
</style>
