.battle-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

// 状态栏
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.timer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.timer-text {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.progress {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 16px;
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

// 对战区域
.battle-area {
  display: flex;
  align-items: center;
  padding: 20px;
  gap: 20px;
}

.player-area {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(10px);
}

.player-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.player-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.player-name {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 4px;
}

.player-score {
  font-size: 18px;
  font-weight: 600;
  color: #667eea;
}

.player-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-bar {
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.progress-label {
  font-size: 12px;
  color: #718096;
  text-align: center;
}

.vs-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.vs-text {
  font-size: 18px;
  font-weight: 700;
  color: #667eea;
}

// 题目区域
.question-area {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px 16px 0 0;
  padding: 24px;
  margin: 0 20px;
  backdrop-filter: blur(10px);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.question-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.question-type {
  background: #667eea;
  color: #fff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.question-content {
  margin-bottom: 24px;
}

.question-text {
  font-size: 16px;
  line-height: 1.6;
  color: #2d3748;
  margin-bottom: 16px;
}

.question-image {
  width: 100%;
  border-radius: 8px;
}

// 选择题选项
.options-area {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.3s ease;

  &.selected {
    background: #e6f3ff;
    border-color: #667eea;
  }

  &:active {
    transform: scale(0.98);
  }
}

.option-label {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #667eea;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 12px;
  flex-shrink: 0;
}

.option-text {
  flex: 1;
  font-size: 14px;
  color: #2d3748;
  line-height: 1.5;
}

// 判断题
.judge-area {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.judge-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.3s ease;

  &.selected {
    background: #e6f3ff;
    border-color: #667eea;
  }

  &:active {
    transform: scale(0.95);
  }
}

// 填空题
.fill-area {
  margin-bottom: 24px;
}

// 提交区域
.submit-area {
  display: flex;
  justify-content: center;
}

// 等待区域
.waiting-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px 16px 0 0;
  margin: 0 20px;
  backdrop-filter: blur(10px);
}

.waiting-text {
  font-size: 16px;
  color: #718096;
}

// 结果对话框
.result-dialog {
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  min-width: 320px;
}

.result-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.result-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;

  &.win {
    background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  }

  &.lose {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  }
}

.result-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
}

.result-content {
  margin-bottom: 24px;
}

.result-scores {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.score-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-label {
  font-size: 14px;
  color: #718096;
}

.score-value {
  font-size: 20px;
  font-weight: 600;
  color: #667eea;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #718096;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
}

.result-actions {
  display: flex;
  gap: 12px;
}
