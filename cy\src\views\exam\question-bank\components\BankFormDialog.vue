<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="500px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="题库名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入题库名称" />
      </el-form-item>
      <el-form-item label="适用范围" prop="scope">
        <el-input v-model="form.scope" placeholder="请输入适用范围" />
      </el-form-item>
      <el-form-item label="题库描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入题库描述" />
      </el-form-item>
      <el-form-item label="练习功能" prop="isPracticeEnabled">
        <el-switch
          v-model="form.isPracticeEnabled"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="启用"
          inactive-text="禁用"
        />
        <div style="font-size: 12px; color: #909399; margin-top: 4px;">
          开启后，学员可以使用此题库进行练习
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { FormInstance } from 'element-plus'

// Props declaration
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  bankData: {
    type: Object,
    default: () => ({})
  }
})

// Emits declaration
const emit = defineEmits(['update:modelValue', 'submit'])

// Reactive refs
const visible = ref(props.modelValue)
const formRef = ref<FormInstance>()

// Form data initialization
const form = reactive({
  id: undefined,
  name: '',
  description: '',
  scope: '',
  isPracticeEnabled: true
})

// Computed title based on editing state
const title = computed(() => props.isEdit ? '编辑题库' : '新增题库')

// Form validation rules
const rules = {
  name: [{ required: true, message: '请输入题库名称', trigger: 'blur' }],
  scope: [{ required: true, message: '请输入适用范围', trigger: 'blur' }]
}

// Watch for props changes
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => props.bankData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(form, {
      id: newVal.id,
      name: newVal.name || '',
      description: newVal.description || '',
      scope: newVal.scope || '',
      isPracticeEnabled: newVal.isPracticeEnabled !== undefined ? newVal.isPracticeEnabled : true
    })
  }
}, { deep: true })

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// Form submit handler
const submitForm = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      emit('submit', { ...form })
      visible.value = false
    }
  })
}

// Handle dialog closed event
const handleClosed = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    id: undefined,
    name: '',
    description: '',
    scope: '',
    isPracticeEnabled: true
  })
}
</script> 