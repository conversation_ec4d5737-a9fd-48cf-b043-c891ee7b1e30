<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <!-- 返回按钮 -->
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">新闻详情</text>
        <!-- 占位符，保持标题居中 -->
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 内容容器 -->
    <view class="content-container with-navbar">
      <view v-if="loading" class="loading-state">
        <uni-load-more status="loading"/>
      </view>

      <view v-else-if="!newsDetail" class="empty-state">
        <text class="empty-text">新闻不存在或已被删除</text>
      </view>

      <view v-else class="news-detail-content">
        <text class="news-title">{{ newsDetail.title }}</text>

        <view class="meta-info">
          <text class="meta-item">发布于 {{ formatDate(newsDetail.createdAt) }}</text>
          <text class="meta-item">{{ newsDetail.viewCount }} 次阅读</text>
        </view>

        <up-image
            v-if="newsDetail.coverUrl"
            class="cover-image"
            :src="newsDetail.coverUrl"
            radius="8"
            mode="aspectFill"
        />

        <view class="news-body">
          <rich-text :nodes="newsDetail.content"/>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {ref} from 'vue'
import {onLoad} from '@dcloudio/uni-app'
import {getNewsById} from '@/api/content'
import {formatDate} from "@/utils/timeUtil"

const loading = ref(true)
const newsDetail = ref(null)

const navigateBack = () => {
  uni.navigateBack()
}

onLoad(async (options) => {
  const newsId = options.id
  loading.value = true
  try {
    const data = await getNewsById(Number(newsId))
    if (data) {
      newsDetail.value = data
    }
  } finally {
    loading.value = false
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/pages/home/<USER>';
</style>
