package com.cy.education.service;

import com.cy.education.model.params.ExamQuestionParams;
import com.cy.education.model.params.ExamQuestionQueryParams;
import com.cy.education.model.vo.ExamQuestionVO;
import com.cy.education.model.vo.PageResponse;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 题目服务接口
 */
public interface ExamQuestionService {

    /**
     * 分页查询题目列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResponse<ExamQuestionVO> listQuestions(ExamQuestionQueryParams params);

    /**
     * 获取题目详情
     *
     * @param id 题目ID
     * @return 题目详情
     */
    ExamQuestionVO getQuestionDetail(Integer id);

    /**
     * 创建题目
     *
     * @param params 题目参数
     * @param createdBy 创建人ID
     * @return 题目ID
     */
    Integer createQuestion(ExamQuestionParams params, String createdBy);

    /**
     * 更新题目
     *
     * @param id 题目ID
     * @param params 题目参数
     * @return 是否成功
     */
    boolean updateQuestion(Integer id, ExamQuestionParams params);

    /**
     * 删除题目
     *
     * @param id 题目ID
     * @return 是否成功
     */
    boolean deleteQuestion(Integer id);

    /**
     * 批量删除题目
     *
     * @param ids 题目ID列表
     * @return 是否成功
     */
    boolean batchDeleteQuestions(List<Integer> ids);

    // ==================== 导入导出功能 ====================

    /**
     * 生成题目导入模板
     *
     * @return 模板文件资源
     */
    Resource generateImportTemplate() throws IOException;

    /**
     * 批量导入题目
     *
     * @param file Excel文件
     * @return 导入结果
     */
    Map<String, Object> importQuestions(MultipartFile file);

    /**
     * 导出题目列表
     *
     * @param params 导出参数
     * @param response HTTP响应
     */
    void exportQuestions(Map<String, Object> params, HttpServletResponse response) throws IOException;
}