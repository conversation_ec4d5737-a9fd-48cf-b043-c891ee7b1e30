﻿// 文件内容将通过编辑器填充

import { defineStore } from 'pinia'
import { login, logout, getUserInfo, type UserInfo } from '@/api/user'
import router from '@/router'
import { ElMessage } from 'element-plus'

interface UserState {
  token: string
  userInfo: UserInfo | null
  permissions: string[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: localStorage.getItem('token') || '',
    userInfo: null,
    permissions: []
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.permissions.includes('user:manage'),
    hasPermission: (state) => (permission: string) => state.permissions.includes(permission)
  },
  
  actions: {
    // 设置Token
    setToken(token: string) {
      this.token = token
      localStorage.setItem('token', token)
    },
    
    // 清除Token
    clearToken() {
      this.token = ''
      localStorage.removeItem('token')
    },
    
    // 设置用户信息
    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo
      this.permissions = userInfo.permissions || []
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    },
    
    // 清除用户信息
    clearUserInfo() {
      this.userInfo = null
      this.permissions = []
      localStorage.removeItem('userInfo')
    },
    
    // 登录
    async loginAction(username: string, password: string, remember: boolean = false, captchaId?: string, captcha?: string) {
      console.log('UserStore: 开始登录请求', { username, password: '******', remember, captchaId, captcha: captcha ? '****' : undefined })
      
      try {
        // 直接调用登录API
        console.log('UserStore: 调用login API')
        const response = await login({ username, password, remember, captchaId, captcha })
        console.log('UserStore: 登录请求完成，响应数据:', response)
        
        // 检查响应格式，确保包含token和user
        if (!response || typeof response !== 'object') {
          console.error('UserStore: 登录响应格式错误:', response)
          return false
        }
        
        // 处理特殊情况 - 如果response本身就是错误响应
        const anyResponse = response as any
        if (anyResponse.code && anyResponse.code !== 200) {
          console.error('UserStore: 登录响应错误码:', anyResponse.code, anyResponse.message)
          return false
        }
        
        // 从模拟数据中提取token和user
        let token: string | undefined
        let user: UserInfo | undefined
        
        // 处理两种可能的响应格式
        if ('token' in anyResponse && 'user' in anyResponse) {
          // 直接使用标准格式
          token = anyResponse.token
          user = anyResponse.user
        } else if (anyResponse.data && typeof anyResponse.data === 'object' && 
                   'token' in anyResponse.data && 'user' in anyResponse.data) {
          // 嵌套在data字段中
          token = anyResponse.data.token
          user = anyResponse.data.user
        } else {
          console.error('UserStore: 无法从响应中提取token和user:', response)
          return false
        }
        
        if (!token || !user) {
          console.error('UserStore: 登录响应缺少token或user数据')
          return false
        }
        
        console.log('UserStore: 设置用户凭据:', { token, user: { ...user, password: undefined } })
        this.setToken(token)
        this.setUserInfo(user)
        
        // 额外打印当前状态，确认设置成功
        console.log('UserStore: 登录完成，当前状态:', { 
          hasToken: !!this.token, 
          hasUserInfo: !!this.userInfo,
          isLoggedIn: this.isLoggedIn
        })
        
        return true
      } catch (error) {
        console.error('UserStore: 登录失败:', error)
        return false
      }
    },
    
    // 获取用户信息
    async getUserInfoAction(force: boolean = false) {
      try {
        if (!this.token) {
          throw new Error('Token is required')
        }
        
        // 如果不是强制刷新且已经有用户信息，则直接返回
        if (!force && this.userInfo) {
          return this.userInfo
        }
        
        // 如果不是强制刷新，尝试从localStorage获取用户信息
        if (!force) {
          const cachedUserInfo = localStorage.getItem('userInfo')
          if (cachedUserInfo) {
            try {
              const userInfo = JSON.parse(cachedUserInfo) as UserInfo
              this.setUserInfo(userInfo)
              return userInfo
            } catch (e) {
              localStorage.removeItem('userInfo')
            }
          }
        }
        
        // 从服务器获取最新用户信息
        const userInfo = await getUserInfo()
        this.setUserInfo(userInfo)
        return userInfo
      } catch (error) {
        console.error('Get user info failed:', error)
        this.logout()
        return null
      }
    },
    
    // 登出
    async logout() {
      console.log('开始退出登录')
      let logoutSuccess = true

      if (this.token) {
        try {
          console.log('调用登出接口')
          await Promise.race([
            logout(),
            // 设置5秒超时
            new Promise((_, reject) => setTimeout(() => reject(new Error('登出请求超时')), 5000))
          ])
          console.log('登出接口调用成功')
        } catch (error) {
          console.error('登出接口调用失败:', error)
          logoutSuccess = false
          // 虽然接口调用失败，但仍然要清除本地状态
        }
      }
      
      // 无论接口调用是否成功，都清除本地状态
      console.log('清除用户状态')
      this.clearToken()
      this.clearUserInfo()
      
      // 清除其他可能的缓存
      localStorage.removeItem('username')
      localStorage.removeItem('password')
      
      // 重定向到登录页
      console.log('准备跳转到登录页')
      setTimeout(() => {
        router.push('/login')
        console.log('已跳转到登录页')
        
        if (!logoutSuccess) {
          ElMessage.warning('登出接口请求失败，但已成功清除本地登录状态')
        } else {
          ElMessage.success('已成功退出登录')
        }
      }, 100)
    },
    
    // 初始化状态
    initFromStorage() {
      // 从localStorage恢复用户信息
      const userInfoStr = localStorage.getItem('userInfo')
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr) as UserInfo
          this.setUserInfo(userInfo)
        } catch (e) {
          localStorage.removeItem('userInfo')
        }
      }
      
      // 如果有token但没有用户信息，则尝试获取用户信息
      if (this.token && !this.userInfo) {
        this.getUserInfoAction().catch(() => {
          ElMessage.error('获取用户信息失败，请重新登录')
          this.logout()
        })
      }
    }
  }
})
