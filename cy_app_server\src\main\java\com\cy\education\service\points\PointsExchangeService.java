package com.cy.education.service.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.points.PointsExchange;
import com.cy.education.model.params.PointsExchangeQueryParam;
import com.cy.education.model.vo.ApiResponse;

import java.util.Map;

/**
 * 积分兑换服务接口
 */
public interface PointsExchangeService {

    /**
     * 分页查询兑换记录
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<PointsExchange> page(PointsExchangeQueryParam param);

    /**
     * 根据ID查询兑换记录
     *
     * @param id 兑换记录ID
     * @return 兑换记录对象
     */
    PointsExchange getById(Integer id);

    /**
     * 创建兑换记录
     *
     * @param exchange 兑换记录对象
     * @return 创建结果
     */
    ApiResponse<Map<String, Object>> create(PointsExchange exchange);
}
