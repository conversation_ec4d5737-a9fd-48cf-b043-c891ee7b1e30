<template>
	<svg :width="size" :height="size" viewBox="0 0 24 24" :fill="active ? '#fff' : '#8E8E93'">
		<path v-if="active" d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22V22H9Z"/>
		<path v-else d="M4,2A2,2 0 0,0 2,4V16A2,2 0 0,0 4,18H8V21A1,1 0 0,0 9,22H9.5A1,1 0 0,0 10.2,21.71L13.9,18H20A2,2 0 0,0 22,16V4C22,2.89 21.1,2 20,2H4M4,4H20V16H13.08L10,19.08V16H4V4Z"/>
	</svg>
</template>

<script setup lang="ts">
interface Props {
	size?: string | number
	color?: string
	active?: boolean
}

withDefaults(defineProps<Props>(), {
	size: 24,
	color: 'currentColor',
	active: false
})
</script>