package com.cy.education.model.params;

import lombok.Data;

import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 分页查询参数
 */
@Data
public class PageParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 页码，默认1
     */
    @Min(value = 1, message = "页码不能小于1")
    private Long page = 1L;

    /**
     * 每页大小，默认10
     */
    @Min(value = 1, message = "每页大小不能小于1")
    private Long size = 10L;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 排序方向，默认降序
     */
    private String sortOrder = "desc";

    /**
     * 计算当前页的起始索引
     */
    public Long getOffset() {
        return (page - 1) * size;
    }
} 