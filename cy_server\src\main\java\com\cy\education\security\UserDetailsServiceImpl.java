package com.cy.education.security;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cy.education.model.entity.Admin;
import com.cy.education.model.entity.Permission;
import com.cy.education.repository.AdminMapper;
import com.cy.education.repository.PermissionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户认证服务实现
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 查询管理员用户
        Admin admin = adminMapper.selectOne(
                new LambdaQueryWrapper<Admin>()
                        .eq(Admin::getUsername, username)
        );

        if (admin == null) {
            throw new UsernameNotFoundException("用户名不存在");
        }

        // 检查用户状态
        if (admin.getStatus() != 1) {
            throw new UsernameNotFoundException("用户已被禁用");
        }

        // 获取用户权限
        List<Permission> permissionList = permissionMapper.selectPermissionsByAdminId(admin.getId());

        // 转换为Spring Security的权限格式
        List<SimpleGrantedAuthority> authorities = permissionList.stream()
                .map(permission -> new SimpleGrantedAuthority(permission.getCode()))
                .collect(Collectors.toList());

        // 创建自定义的JwtUserDetails
        JwtUserDetails userDetails = new JwtUserDetails(
                admin.getUsername(),
                admin.getPassword(),
                authorities
        );
        
        // 设置额外信息
        userDetails.setUserId(admin.getId().toString());
        userDetails.setRealName(admin.getName());
        
        return userDetails;
    }
} 