package com.cy.education.controller;

import com.cy.education.model.entity.Course;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/courses")
public class CourseController {

    @Autowired
    private CourseService courseService;

    @GetMapping
    public ApiResponse<PageResponse<Course>> getCourses(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name) {
        PageResponse<Course> courseList = courseService.getCourseList(page, size, name);
        return ApiResponse.success(courseList);
    }

    @PostMapping
    public ApiResponse<Course> createCourse(@RequestBody Course course) {
        // In a real app, you'd get the user ID from the security context
        // course.setCreatorId(SecurityUtil.getCurrentUserId());
        course.setCreateTime(new Date());
        course.setUpdateTime(new Date());
        courseService.save(course);
        return ApiResponse.success(course);
    }

    @GetMapping("/{id}")
    public ApiResponse<Course> getCourseDetailsForEdit(@PathVariable Long id) {
        Course course = courseService.getById(id);
        if (course == null) {
            return ApiResponse.error("Course not found");
        }
        return ApiResponse.success(course);
    }
    
    @GetMapping("/{id}/preview")
    public ApiResponse<Course> getCourseDetailsForPreview(@PathVariable Long id) {
        Course course = courseService.getCourseWithSanitizedStructure(id);
        if (course == null) {
            return ApiResponse.error("Course not found");
        }
        // Here you might want to do more sanitization or transformation
        // before sending it to the frontend for preview.
        return ApiResponse.success(course);
    }

    @PutMapping("/{id}")
    public ApiResponse<Course> updateCourse(@PathVariable Long id, @RequestBody Course courseDetails) {
        Course course = courseService.getById(id);
        if (course == null) {
            return ApiResponse.error("Course not found");
        }
        
        // As per requirement, the entire course info is saved at once.
        courseDetails.setId(id); // Ensure the ID is set for the update
        courseDetails.setUpdateTime(new Date());

        courseService.updateById(courseDetails);
        return ApiResponse.success(courseDetails);
    }

    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteCourse(@PathVariable Long id) {
        boolean removed = courseService.removeById(id);
        if (!removed) {
            return ApiResponse.error("Course not found or could not be deleted");
        }
        return ApiResponse.success(null);
    }
} 