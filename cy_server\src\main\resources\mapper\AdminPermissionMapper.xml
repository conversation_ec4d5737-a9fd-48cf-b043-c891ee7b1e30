<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.AdminPermissionMapper">

    <!-- 批量插入管理员权限关联 -->
    <insert id="batchInsert">
        INSERT INTO admin_permissions(admin_id, permission_id, created_at)
        VALUES
        <foreach collection="permissionIds" item="permissionId" separator=",">
            (#{adminId}, #{permissionId}, NOW())
        </foreach>
    </insert>

    <!-- 根据管理员ID删除权限关联 -->
    <delete id="deleteByAdminId">
        DELETE FROM admin_permissions
        WHERE admin_id = #{adminId}
    </delete>

    <!-- 根据管理员ID查询权限ID列表 -->
    <select id="selectPermissionIdsByAdminId" resultType="java.lang.Integer">
        SELECT permission_id
        FROM admin_permissions
        WHERE admin_id = #{adminId}
        ORDER BY permission_id ASC
    </select>

</mapper> 