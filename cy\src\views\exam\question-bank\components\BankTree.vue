<template>
  <div class="bank-sidebar">
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>题库分类</span>
          <el-button text :icon="Plus" @click="$emit('add-bank')">新增题库</el-button>
        </div>
      </template>
      <div class="bank-tree-container">
        <el-tree
          ref="bankTreeRef"
          :data="bankList"
          :props="treeProps"
          node-key="id"
          highlight-current
          default-expand-all
          @node-click="handleBankSelect"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <span>
                <el-icon><DocumentCopy /></el-icon>
                <span style="margin-left: 6px;">{{ node.label }}</span>
                <el-tag size="small" type="info" effect="plain">{{ data.questionCount }}</el-tag>
              </span>
              <div class="tree-node-actions" v-if="!node.disabled">
                <el-button text size="small" @click.stop="$emit('edit-bank', data)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button text size="small" @click.stop="$emit('delete-bank', data)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Plus,
  Edit,
  Delete,
  DocumentCopy
} from '@element-plus/icons-vue'
import type { ElTree } from 'element-plus'

// Props declaration
const props = defineProps({
  bankList: {
    type: Array,
    required: true
  },
  treeProps: {
    type: Object,
    default: () => ({
      children: 'children',
      label: 'name'
    })
  }
})

// Emits declaration
const emit = defineEmits(['add-bank', 'edit-bank', 'delete-bank', 'select-bank'])

const bankTreeRef = ref<InstanceType<typeof ElTree>>()

// Handle bank selection
const handleBankSelect = (data: any) => {
  emit('select-bank', data)
}

// Expose methods to parent component
defineExpose({
  bankTreeRef
})
</script>

<style scoped>
.bank-sidebar {
  width: 280px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tree-node-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.custom-tree-node:hover .tree-node-actions {
  opacity: 1;
}

.bank-tree-container {
  height: calc(100vh - 280px);
  overflow-y: auto;
}
</style> 