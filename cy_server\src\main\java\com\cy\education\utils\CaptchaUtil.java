package com.cy.education.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;

/**
 * 验证码工具类
 */
@Component
@Slf4j
public class CaptchaUtil {

    /**
     * 验证码字符集
     */
    private static final String CHAR_SET = "23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz";
    
    /**
     * 验证码长度
     */
    private static final int CODE_LENGTH = 4;
    
    /**
     * 图片宽度
     */
    private static final int WIDTH = 120;
    
    /**
     * 图片高度
     */
    private static final int HEIGHT = 40;
    
    /**
     * 干扰线数量
     */
    private static final int LINE_COUNT = 5;
    
    /**
     * 噪点数量
     */
    private static final int NOISE_COUNT = 50;
    
    private final Random random = new Random();

    /**
     * 生成验证码
     * @return 验证码文本
     */
    public String generateCode() {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHAR_SET.charAt(random.nextInt(CHAR_SET.length())));
        }
        return code.toString();
    }

    /**
     * 生成验证码图片
     * @param code 验证码文本
     * @return Base64编码的图片
     */
    public String generateImage(String code) {
        // 创建图片
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 设置背景色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, WIDTH, HEIGHT);
        
        // 绘制干扰线
        drawLines(g2d);
        
        // 绘制噪点
        drawNoise(g2d);
        
        // 绘制验证码字符
        drawCode(g2d, code);
        
        g2d.dispose();
        
        // 转换为Base64
        return imageToBase64(image);
    }

    /**
     * 绘制干扰线
     */
    private void drawLines(Graphics2D g2d) {
        for (int i = 0; i < LINE_COUNT; i++) {
            int x1 = random.nextInt(WIDTH);
            int y1 = random.nextInt(HEIGHT);
            int x2 = random.nextInt(WIDTH);
            int y2 = random.nextInt(HEIGHT);
            
            g2d.setColor(getRandomColor(150, 200));
            g2d.setStroke(new BasicStroke(1.5f));
            g2d.drawLine(x1, y1, x2, y2);
        }
    }

    /**
     * 绘制噪点
     */
    private void drawNoise(Graphics2D g2d) {
        for (int i = 0; i < NOISE_COUNT; i++) {
            int x = random.nextInt(WIDTH);
            int y = random.nextInt(HEIGHT);
            
            g2d.setColor(getRandomColor(100, 160));
            g2d.fillOval(x, y, 2, 2);
        }
    }

    /**
     * 绘制验证码字符
     */
    private void drawCode(Graphics2D g2d, String code) {
        int fontHeight = HEIGHT - 4;
        Font font = new Font("Arial", Font.BOLD, fontHeight);
        g2d.setFont(font);
        
        char[] chars = code.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            // 随机颜色
            g2d.setColor(getRandomColor(20, 130));
            
            // 字符位置
            int x = (WIDTH / CODE_LENGTH) * i + 10;
            int y = HEIGHT - 8;
            
            // 随机旋转角度
            AffineTransform originalTransform = g2d.getTransform();
            double angle = (random.nextDouble() - 0.5) * 0.4; // -0.2 到 0.2 弧度
            g2d.rotate(angle, x, y);
            
            // 绘制字符
            g2d.drawString(String.valueOf(chars[i]), x, y);
            
            // 恢复变换
            g2d.setTransform(originalTransform);
        }
    }

    /**
     * 获取随机颜色
     */
    private Color getRandomColor(int min, int max) {
        int r = random.nextInt(max - min) + min;
        int g = random.nextInt(max - min) + min;
        int b = random.nextInt(max - min) + min;
        return new Color(r, g, b);
    }

    /**
     * 将图片转换为Base64
     */
    private String imageToBase64(BufferedImage image) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "PNG", baos);
            byte[] bytes = baos.toByteArray();
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            log.error("图片转换Base64失败", e);
            throw new RuntimeException("生成验证码图片失败", e);
        }
    }

    /**
     * 验证码验证（忽略大小写）
     */
    public boolean verify(String inputCode, String actualCode) {
        if (inputCode == null || actualCode == null) {
            return false;
        }
        return inputCode.equalsIgnoreCase(actualCode);
    }
} 