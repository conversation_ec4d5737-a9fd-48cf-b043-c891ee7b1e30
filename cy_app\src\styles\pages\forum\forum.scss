// 浮动发帖按钮
.floating-post-btn {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  z-index: 999;
  transition: all 0.3s ease;
}

.floating-post-btn:active {
  transform: scale(0.95);
}

.main-content {
  padding: 0 16px;
  margin-top: 10px;
}

.category-section {
  background: #fff;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 0;

  .primary-categories {
    padding: 12px 16px 8px 16px;
    border-bottom: 1px solid #f5f5f5;
  }

  .secondary-categories {
    padding: 8px 16px 12px 16px;
    background: #fafafa;
  }
}

.filter-section {
  background: #fff;
  border-radius: 0 0 16px 16px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  border-top: 1px solid #f5f5f5;

  .sort-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.posts-container {
  .post-card {
    background: #fff;
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    transition: transform 0.2s, box-shadow 0.2s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }

    .post-badges {
      position: absolute;
      top: 12px;
      right: 12px;
      display: flex;
      gap: 4px;
    }

    .post-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .user-info {
        margin-left: 12px;
        flex: 1;

        .username {
          display: block;
          font-size: 15px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .post-meta {
          display: flex;
          align-items: center;
          gap: 8px;

          .post-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }


    }

    .post-content {
      margin-bottom: 16px;

      .post-title-row {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 8px;
        gap: 12px;

        .post-title {
          flex: 1;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          line-height: 1.4;
        }

        .post-time-wrapper {
          flex-shrink: 0;
          display: flex;
          align-items: center;

          .post-time {
            font-size: 12px;
            color: #909399;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 12px;
            white-space: nowrap;
          }
        }
      }

      .post-excerpt {
        display: block;
        font-size: 14px;
        color: #606266;
        line-height: 1.5;
        margin-bottom: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }

      .post-images {
        display: flex;
        gap: 8px;
        align-items: center;

        .more-images {
          width: 60px;
          height: 60px;
          background: #f5f5f5;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .post-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-top: 1px solid #f5f5f5;
      padding-top: 12px;
      border-radius: 0 0 12px 12px;

      .action-stats {
        display: flex;
        gap: 20px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 13px;
          color: #909399;
        }
      }

      .action-buttons {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }
}
