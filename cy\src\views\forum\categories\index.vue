<template>
  <div class="forum-categories">
    <div class="page-container">
      <div class="page-toolbar">
        <div class="toolbar-left">
          <h3>板块分类管理</h3>
        </div>
        <div class="toolbar-right">
          <el-button type="primary" :icon="Plus" @click="handleAdd">新增分类</el-button>
          <el-button :icon="Refresh" @click="refreshData">刷新</el-button>
        </div>
      </div>

      <el-card shadow="never" class="categories-table">
        <el-table
          v-loading="loading"
          :data="categoryList"
          border
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          default-expand-all
          :row-class-name="tableRowClassName"
        >
          <el-table-column label="板块名称" min-width="200">
            <template #default="{ row }">
              <div class="category-name">
                <el-icon v-if="row.icon" :class="row.icon"></el-icon>
                <span v-else class="default-icon">
                  <el-icon><ChatDotRound /></el-icon>
                </span>
                <span class="name">{{ row.name }}</span>
                <el-tag v-if="row.isParent" type="info" size="small">父板块</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />
          <el-table-column prop="postCount" label="帖子数量" width="100" align="center" />
          <el-table-column prop="sort" label="排序" width="80" align="center" />
          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status ? 'success' : 'danger'">
                {{ row.status ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" align="center" />
          <el-table-column label="操作" width="250" align="center" fixed="right">
            <template #default="{ row, $index }">
              <el-button-group v-if="!row.isParent">
                <el-button type="primary" link :icon="Top" @click="handleMoveUp(row, $index)" :disabled="$index === 0">
                  上移
                </el-button>
                <el-button type="primary" link :icon="Bottom" @click="handleMoveDown(row, $index)" :disabled="$index === categoryList.length - 1">
                  下移
                </el-button>
              </el-button-group>
              <el-button v-if="row.isParent" type="success" link :icon="Plus" @click="handleAddSub(row)">
                添加子板块
              </el-button>
              <el-button type="primary" link :icon="Edit" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button 
                type="warning" 
                link 
                :icon="row.status ? Lock : Unlock" 
                @click="handleToggleStatus(row)"
              >
                {{ row.status ? '禁用' : '启用' }}
              </el-button>
              <el-button 
                type="danger" 
                link 
                :icon="Delete" 
                @click="handleDelete(row)"
                :disabled="row.children && row.children.length > 0"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card shadow="never" class="statistics-card" style="margin-top: 20px;">
        <template #header>
          <div class="card-header">
            <span>板块统计</span>
          </div>
        </template>
        <div class="statistics-content">
          <div class="statistic-item">
            <div class="statistic-title">总板块数</div>
            <div class="statistic-value">{{ totalCategories }}</div>
          </div>
          <div class="statistic-item">
            <div class="statistic-title">父板块数</div>
            <div class="statistic-value">{{ parentCategories }}</div>
          </div>
          <div class="statistic-item">
            <div class="statistic-title">子板块数</div>
            <div class="statistic-value">{{ childCategories }}</div>
          </div>
          <div class="statistic-item">
            <div class="statistic-title">总帖子数</div>
            <div class="statistic-value">{{ totalPosts }}</div>
          </div>
          <div class="statistic-item">
            <div class="statistic-title">启用板块</div>
            <div class="statistic-value">{{ enabledCategories }}</div>
          </div>
          <div class="statistic-item">
            <div class="statistic-title">禁用板块</div>
            <div class="statistic-value">{{ disabledCategories }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑板块' : (isAddSub ? '新增子板块' : '新增板块')"
      width="500px"
      destroy-on-close
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="板块名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入板块名称" />
        </el-form-item>
        <el-form-item label="所属板块" prop="parentId" v-if="!isParentCategory">
          <el-select v-model="form.parentId" placeholder="请选择所属板块" style="width: 100%;">
            <el-option 
              v-for="item in parentCategoryOptions" 
              :key="item.id" 
              :label="item.name" 
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="板块图标" prop="icon">
          <el-input v-model="form.icon" placeholder="请输入图标类名">
            <template #prepend>
              <el-icon :class="form.icon || 'ChatDotRound'">
                <component :is="form.icon || 'ChatDotRound'" />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="板块描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入板块描述" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="999" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import {
  Plus,
  Edit,
  Delete,
  Refresh,
  ChatDotRound,
  Lock,
  Unlock,
  Top,
  Bottom
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCategoryList, getCategoryById, addCategory, updateCategory, deleteCategory, updateCategoryStatus, updateCategorySort, getParentCategories } from '@/api/forum'

interface CategoryData {
  id: number
  name: string
  parentId?: number | null
  description: string
  icon?: string
  sort: number
  status: number
  postCount: number
  createTime: string
  isParent?: boolean
  children?: CategoryData[]
}

const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const isAddSub = ref(false)
const isParentCategory = ref(false)
const formRef = ref()

// 表单数据
const form = reactive<Partial<CategoryData>>({
  id: undefined,
  name: '',
  parentId: null,
  description: '',
  icon: '',
  sort: 0,
  status: 1
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入板块名称', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
}

// 分类数据
const categoryList = ref<CategoryData[]>([])

// 父板块选项
const parentCategoryOptions = ref<{id: number, name: string}[]>([])

// 加载父分类选项
const loadParentOptions = async () => {
  try {
    const res = await getParentCategories()
      parentCategoryOptions.value = res.map((item: any) => ({
      id: item.id,
      name: item.name
    }))
    
  } catch (error) {
    console.error('加载父分类选项失败', error)
  }
}

// 统计数据
const totalCategories = computed(() => {
  let count = 0
  categoryList.value.forEach(item => {
    count += 1
    if (item.children) {
      count += item.children.length
    }
  })
  return count
})

const parentCategories = computed(() => {
  return categoryList.value.filter(item => item.isParent).length
})

const childCategories = computed(() => {
  return totalCategories.value - parentCategories.value
})

const totalPosts = computed(() => {
  let count = 0
  categoryList.value.forEach(item => {
    count += item.postCount || 0
    if (item.children) {
      item.children.forEach(child => {
        count += child.postCount || 0
      })
    }
  })
  return count
})

const enabledCategories = computed(() => {
  let count = 0
  categoryList.value.forEach(item => {
    if (item.status === 1) count += 1
    if (item.children) {
      count += item.children.filter(child => child.status === 1).length
    }
  })
  return count
})

const disabledCategories = computed(() => {
  return totalCategories.value - enabledCategories.value
})

// 表格行样式
const tableRowClassName = ({ row }: { row: CategoryData }) => {
  if (row.status === 0) {
    return 'disabled-row'
  }
  return ''
}

// 加载分类数据
const loadCategories = async () => {
  loading.value = true
  try {
    const res = await getCategoryList({})
    categoryList.value = res
  } catch (error) {
    console.error('加载分类失败', error)
    ElMessage.error('加载分类失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadCategories()
    ElMessage.success('数据已刷新')
}

// 新增板块
const handleAdd = async () => {
  isEdit.value = false
  isAddSub.value = false
  isParentCategory.value = true
  
  // 重置表单
  Object.assign(form, {
    id: undefined,
    name: '',
    parentId: null,
    description: '',
    icon: '',
    sort: categoryList.value.length + 1,
    status: 1
  })
  
  await loadParentOptions()
  dialogVisible.value = true
}

// 新增子板块
const handleAddSub = async (row: CategoryData) => {
  isEdit.value = false
  isAddSub.value = true
  isParentCategory.value = false
  
  // 重置表单
  Object.assign(form, {
    id: undefined,
    name: '',
    parentId: row.id,
    description: '',
    icon: '',
    sort: row.children ? row.children.length + 1 : 1,
    status: 1
  })
  
  await loadParentOptions()
  dialogVisible.value = true
}

// 编辑板块
const handleEdit = async (row: CategoryData) => {
  isEdit.value = true
  isAddSub.value = false
  isParentCategory.value = !!row.isParent
  
  try {
    // 获取详细信息
    const res = await getCategoryById(row.id)
      // 填充表单
  Object.assign(form, {
        id: res.id,
        name: res.name,
        parentId: res.parentId,
        description: res.description,
        icon: res.icon,
        sort: res.sort,
        status: res.status
  })
  } catch (error) {
    console.error('获取分类详情失败', error)
    ElMessage.error('获取分类详情失败，请稍后重试')
    return
  }
  
  await loadParentOptions()
  dialogVisible.value = true
}

// 切换状态
const handleToggleStatus = (row: CategoryData) => {
  const statusText = row.status ? '禁用' : '启用'
  ElMessageBox.confirm(`确定要${statusText}该板块吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const newStatus = row.status ? 0 : 1
      const res = await updateCategoryStatus(row.id, { status: newStatus })
      if (res.success) {
        // 更新本地状态
        row.status = newStatus
    
    // 如果是父板块，同时更新所有子板块状态
    if (row.children) {
      row.children.forEach(child => {
            child.status = newStatus
      })
    }
    
    ElMessage.success(`${statusText}成功`)
      } else {
        ElMessage.error(res.message || `${statusText}失败`)
      }
    } catch (error) {
      console.error(`${statusText}失败`, error)
      ElMessage.error(`${statusText}失败，请稍后重试`)
    }
  })
}

// 删除板块
const handleDelete = (row: CategoryData) => {
  // 检查是否有子板块
  if (row.children && row.children.length > 0) {
    ElMessage.warning('该板块下有子板块，不能直接删除')
    return
  }
  
  ElMessageBox.confirm('确定要删除该板块吗？删除后将无法恢复！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteCategory(row.id)
      if (res.success) {
    if (row.isParent) {
      // 删除父板块
      const index = categoryList.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        categoryList.value.splice(index, 1)
      }
    } else {
      // 删除子板块
      categoryList.value.forEach(parent => {
        if (parent.children) {
          const index = parent.children.findIndex(child => child.id === row.id)
          if (index !== -1) {
            parent.children.splice(index, 1)
          }
        }
      })
    }
    
    ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  })
}

// 上移板块
const handleMoveUp = (row: CategoryData, index: number) => {
  if (index === 0) return
  
  // 查找父板块
  const parent = categoryList.value.find(item => item.id === row.parentId)
  if (parent && parent.children) {
    const childIndex = parent.children.findIndex(child => child.id === row.id)
    if (childIndex > 0) {
      // 交换位置
      [parent.children[childIndex - 1], parent.children[childIndex]] = 
        [parent.children[childIndex], parent.children[childIndex - 1]]
      
      // 更新排序号
      const sortData = [
        { id: parent.children[childIndex - 1].id, sort: childIndex },
        { id: parent.children[childIndex].id, sort: childIndex + 1 }
      ]
      
      // 更新后端排序
      updateCategorySort(sortData).then(res => {
        if (res.success) {
          // 更新本地排序号
      parent.children[childIndex - 1].sort = childIndex
      parent.children[childIndex].sort = childIndex + 1
  ElMessage.success('上移成功')
        } else {
          ElMessage.error(res.message || '上移失败')
          // 如果失败，恢复原有顺序
          [parent.children[childIndex], parent.children[childIndex - 1]] = 
            [parent.children[childIndex - 1], parent.children[childIndex]]
        }
      }).catch(error => {
        console.error('上移失败', error)
        ElMessage.error('上移失败，请稍后重试')
        // 如果失败，恢复原有顺序
        [parent.children[childIndex], parent.children[childIndex - 1]] = 
          [parent.children[childIndex - 1], parent.children[childIndex]]
      })
    }
  }
}

// 下移板块
const handleMoveDown = (row: CategoryData, index: number) => {
  // 查找父板块
  const parent = categoryList.value.find(item => item.id === row.parentId)
  if (parent && parent.children) {
    const childIndex = parent.children.findIndex(child => child.id === row.id)
    if (childIndex < parent.children.length - 1) {
      // 交换位置
      [parent.children[childIndex], parent.children[childIndex + 1]] = 
        [parent.children[childIndex + 1], parent.children[childIndex]]
      
      // 更新排序号
      const sortData = [
        { id: parent.children[childIndex].id, sort: childIndex + 1 },
        { id: parent.children[childIndex + 1].id, sort: childIndex + 2 }
      ]
      
      // 更新后端排序
      updateCategorySort(sortData).then(res => {
        if (res.success) {
          // 更新本地排序号
      parent.children[childIndex].sort = childIndex + 1
      parent.children[childIndex + 1].sort = childIndex + 2
  ElMessage.success('下移成功')
        } else {
          ElMessage.error(res.message || '下移失败')
          // 如果失败，恢复原有顺序
          [parent.children[childIndex + 1], parent.children[childIndex]] = 
            [parent.children[childIndex], parent.children[childIndex + 1]]
        }
      }).catch(error => {
        console.error('下移失败', error)
        ElMessage.error('下移失败，请稍后重试')
        // 如果失败，恢复原有顺序
        [parent.children[childIndex + 1], parent.children[childIndex]] = 
          [parent.children[childIndex], parent.children[childIndex + 1]]
      })
    }
  }
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
      if (isEdit.value) {
        // 编辑分类
          const res = await updateCategory(form)
          if (res.success) {
            ElMessage.success('编辑成功')
            // 刷新数据
            loadCategories()
          } else {
            ElMessage.error(res.message || '编辑失败')
          }
      } else {
        // 新增分类
          const res = await addCategory(form)
          if (res.id) {
            ElMessage.success('新增成功')
            // 刷新数据
            loadCategories()
        } else {
            ElMessage.error(res.message || '新增失败')
          }
      }
      
      dialogVisible.value = false
      } catch (error) {
        console.error(isEdit.value ? '编辑失败' : '新增失败', error)
        ElMessage.error((isEdit.value ? '编辑' : '新增') + '失败，请稍后重试')
      }
    }
  })
}

onMounted(() => {
  // 加载分类数据
  loadCategories()
})
</script>

<style scoped>
.statistics-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.statistic-item {
  flex: 1;
  min-width: 120px;
  text-align: center;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.statistic-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.default-icon {
  color: #409eff;
}

.name {
  margin: 0 8px;
}

:deep(.disabled-row) {
  color: #c0c4cc;
  background-color: #f9f9f9;
}

.categories-table {
  margin-top: 20px;
}
</style> 