import { ElMessage } from 'element-plus'
import { getImageSignature, uploadToOss } from '@/api/oss'

/**
 * 配置富文本编辑器的图片上传
 * @returns 编辑器配置对象
 */
export function createEditorUploadConfig() {
  return {
    uploadImage: {
      async customUpload(file: File, insertFn: (url: string, alt?: string, href?: string) => void) {
        try {
          // 检查文件类型
          if (!file.type.startsWith('image/')) {
            ElMessage.error('只能上传图片文件!')
            return
          }

          // 检查文件大小 (10MB)
          const maxSize = 10 * 1024 * 1024
          if (file.size > maxSize) {
            ElMessage.error('图片大小不能超过 10MB!')
            return
          }

          // 获取OSS上传签名
          const signature = await getImageSignature()

          // 直接上传到OSS
          const result = await uploadToOss(file, signature)

          // 插入图片到编辑器
          insertFn(result.url, file.name, result.url)

        } catch (error) {
          console.error('富文本编辑器图片上传失败:', error)
          ElMessage.error('图片上传失败')
        }
      }
    }
  }
}

/**
 * 创建完整的编辑器配置
 * @param placeholder 占位符文本
 * @param maxLength 最大长度
 * @returns 完整的编辑器配置
 */
export function createEditorConfig(placeholder = '请输入内容...', maxLength?: number) {
  const config: any = {
    placeholder,
    MENU_CONF: createEditorUploadConfig()
  }

  if (maxLength) {
    config.maxLength = maxLength
  }

  return config
}

/**
 * 创建工具栏配置
 * @param excludeKeys 要排除的工具栏按钮
 * @returns 工具栏配置
 */
export function createToolbarConfig(excludeKeys?: string[]) {
  const config: any = {}

  if (excludeKeys && excludeKeys.length > 0) {
    config.excludeKeys = excludeKeys
  }

  return config
}
