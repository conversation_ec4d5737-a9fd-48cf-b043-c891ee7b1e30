package com.cy.education.service.impl.exam;

import com.cy.education.model.vo.ExamAnswerVO;
import com.cy.education.repository.ExamAnswerMapper;
import com.cy.education.service.exam.ExamAnswerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 考试答题服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExamAnswerServiceImpl implements ExamAnswerService {

    private final ExamAnswerMapper examAnswerMapper;

    @Override
    public List<ExamAnswerVO> selectByRecordId(Integer recordId) {
        return examAnswerMapper.selectByRecordId(recordId);
    }
} 