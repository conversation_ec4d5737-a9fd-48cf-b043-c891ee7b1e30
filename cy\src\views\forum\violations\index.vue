<template>
  <div class="forum-violations">
    <div class="page-container">
      <div class="page-toolbar">
        <div class="toolbar-left">
          <h3>违规内容处理</h3>
        </div>
        <div class="toolbar-right">
          <el-select v-model="searchForm.contentType" placeholder="内容类型" style="width: 120px; margin-right: 12px;">
            <el-option label="全部" value="" />
            <el-option label="帖子" value="post" />
            <el-option label="评论" value="comment" />
          </el-select>
          <el-select v-model="searchForm.status" placeholder="处理状态" style="width: 120px; margin-right: 12px;">
            <el-option label="全部" value="" />
            <el-option 
              v-for="option in violationStatusOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
          <el-button type="primary" :icon="View" @click="handleBatchProcess">批量处理</el-button>
        </div>
      </div>

      <!-- 违规内容表格 -->
      <el-table
        :data="violationList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="content" label="违规内容" min-width="250">
          <template #default="{ row }">
            <div class="violation-content">
              {{ row.content }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="contentType" label="内容类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.contentType === 'post' ? 'primary' : 'success'">
              {{ row.contentType === 'post' ? '帖子' : '评论' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reporter" label="举报人" width="120" />
        <el-table-column prop="reportReason" label="举报理由" min-width="150" />
        <el-table-column prop="reportTime" label="举报时间" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleView(row)">查看</el-button>
            <el-button 
              v-if="row.status === 0" 
              text 
              type="primary" 
              @click="handleProcess(row)"
            >
              处理
            </el-button>
            <el-button 
              v-if="row.status === 0" 
              text 
              type="info" 
              @click="handleIgnore(row)"
            >
              忽略
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 查看详情弹窗 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="违规内容详情"
      width="800px"
      destroy-on-close
    >
      <div v-if="currentItem" class="violation-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="内容类型">
            <el-tag :type="currentItem.contentType === 'post' ? 'primary' : 'success'">
              {{ currentItem.contentType === 'post' ? '帖子' : '评论' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="违规内容">{{ currentItem.content }}</el-descriptions-item>
          <el-descriptions-item label="举报人">{{ currentItem.reporter }}</el-descriptions-item>
          <el-descriptions-item label="举报理由">{{ currentItem.reportReason }}</el-descriptions-item>
          <el-descriptions-item label="举报时间">{{ currentItem.reportTime }}</el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="getStatusType(currentItem.status)">
              {{ getStatusText(currentItem.status) }}
            </el-tag>
          </el-descriptions-item>
          <template v-if="currentItem.status !== 0">
            <el-descriptions-item label="处理结果">{{ currentItem.processResult }}</el-descriptions-item>
            <el-descriptions-item label="处理时间">{{ currentItem.processTime }}</el-descriptions-item>
            <el-descriptions-item label="处理人">{{ currentItem.operator }}</el-descriptions-item>
          </template>
        </el-descriptions>
        
        <!-- 如果是帖子，显示帖子内容 -->
        <div v-if="currentItem.contentType === 'post' && postContent" class="content-preview">
          <h4>帖子内容预览</h4>
          <div class="post-preview">
            <h3>{{ postContent.title }}</h3>
            <div class="post-meta">
              <span>作者：{{ postContent.author }}</span>
              <span style="margin-left: 16px;">板块：{{ postContent.category }}</span>
              <span style="margin-left: 16px;">发布时间：{{ postContent.createTime }}</span>
            </div>
            <div class="post-content" v-html="postContent.content"></div>
          </div>
        </div>
        
        <!-- 如果是评论，显示评论内容 -->
        <div v-if="currentItem.contentType === 'comment' && commentContent" class="content-preview">
          <h4>评论内容预览</h4>
          <div class="comment-preview">
            <div class="comment-meta">
              <span>所属帖子：{{ commentContent.postTitle }}</span>
              <span style="margin-left: 16px;">评论者：{{ commentContent.author }}</span>
              <span style="margin-left: 16px;">评论时间：{{ commentContent.createTime }}</span>
            </div>
            <div class="comment-content">{{ commentContent.content }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="viewDialogVisible = false">关闭</el-button>
        <el-button 
          v-if="currentItem?.status === 0" 
          type="primary" 
          @click="handleProcess(currentItem)"
        >
          处理
        </el-button>
        <el-button 
          v-if="currentItem?.status === 0" 
          type="info" 
          @click="handleIgnore(currentItem)"
        >
          忽略
        </el-button>
      </template>
    </el-dialog>

    <!-- 处理违规内容弹窗 -->
    <el-dialog
      v-model="processDialogVisible"
      title="处理违规内容"
      width="550px"
      destroy-on-close
    >
      <el-form :model="processForm" label-width="100px">
        <el-form-item label="违规内容">
          <div class="process-content">{{ currentItem?.content }}</div>
        </el-form-item>
        <el-form-item label="内容类型">
          <el-tag :type="currentItem?.contentType === 'post' ? 'primary' : 'success'">
            {{ currentItem?.contentType === 'post' ? '帖子' : '评论' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="举报理由">
          <div>{{ currentItem?.reportReason }}</div>
        </el-form-item>
        <el-form-item label="处理结果">
          <el-select v-model="processForm.result" placeholder="请选择处理结果" style="width: 100%;">
            <el-option 
              v-for="option in processOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleProcessConfirm">确认处理</el-button>
      </template>
    </el-dialog>

    <!-- 批量处理弹窗 -->
    <el-dialog
      v-model="batchProcessDialogVisible"
      title="批量处理违规内容"
      width="500px"
      destroy-on-close
    >
      <div v-if="selectedItems.length === 0" class="batch-empty">
        请先选择要处理的违规内容
      </div>
      <el-form v-else :model="batchProcessForm" label-width="100px">
        <el-form-item label="已选择">
          <div>{{ selectedItems.length }}项违规内容</div>
        </el-form-item>
        <el-form-item label="处理方式">
          <el-radio-group v-model="batchProcessForm.action">
            <el-radio label="process">处理违规</el-radio>
            <el-radio label="ignore">忽略举报</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="batchProcessForm.action === 'process'" label="处理结果">
          <el-select v-model="batchProcessForm.result" placeholder="请选择处理结果" style="width: 100%;">
            <el-option 
              v-for="option in processOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="batchProcessDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchProcessConfirm">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import {
  Search,
  View,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getViolationList,
  getViolationById,
  getPostById,
  getCommentById,
  processViolation,
  batchProcessViolations,
  type ViolationContent,
  type Post,
  type Comment,
  type ViolationQueryParams
} from '@/api/forum'
import { 
  getViolationStatusInfo, 
  getProcessTypeInfo,
  violationStatusOptions,
  processTypeMap
} from '@/utils/forumStatusHelper'

const loading = ref(false)
const viewDialogVisible = ref(false)
const processDialogVisible = ref(false)
const batchProcessDialogVisible = ref(false)
const currentItem = ref<ViolationContent | null>(null)
const selectedItems = ref<ViolationContent[]>([])

// 内容预览
const postContent = ref<Post | null>(null)
const commentContent = ref<Comment | null>(null)

// 搜索表单
const searchForm = reactive({
  contentType: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 处理表单
const processForm = reactive({
  result: ''
})

// 批量处理表单
const batchProcessForm = reactive({
  action: 'process',
  result: ''
})

// 违规内容列表
const violationList = ref<ViolationContent[]>([])

// 处理选项
const processOptions = computed(() => {
  const baseOptions = [
    { label: '内容违规，予以删除', value: '删除内容' },
    { label: '内容不当，禁言用户', value: '禁言用户' }
  ]
  
  // 根据内容类型添加不同选项
  if (currentItem.value?.contentType === 'post') {
    return [
      ...baseOptions,
      { label: '帖子违规，禁止显示', value: '禁止显示帖子' }
    ]
  } else {
    return baseOptions
  }
})

// 获取状态类型
const getStatusType = (status: number | string) => {
  return getViolationStatusInfo(status).type;
}

// 获取状态文本
const getStatusText = (status: number | string) => {
  return getViolationStatusInfo(status).text;
}

// 选择变更
const handleSelectionChange = (selection: ViolationContent[]) => {
  selectedItems.value = selection
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 分页大小变更
const handleSizeChange = (size: number) => {
  pagination.size = size
  loadData()
}

// 页码变更
const handlePageChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params: ViolationQueryParams = {
      contentType: searchForm.contentType as 'post' | 'comment' | undefined,
      status: searchForm.status,
      page: pagination.page,
      size: pagination.size
    }
    const res = await getViolationList(params)
    violationList.value = res.list
    pagination.total = res.total
  } catch (error) {
    console.error('加载违规内容失败:', error)
    ElMessage.error('加载违规内容失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleView = async (row: ViolationContent) => {
  loading.value = true
  try {
    const violation = await getViolationById(row.id)
    currentItem.value = violation
    
    // 加载相关内容
    if (violation.contentType === 'post') {
      const post = await getPostById(violation.contentId)
      postContent.value = post
      commentContent.value = null
    } else {
      const comment = await getCommentById(violation.contentId)
      commentContent.value = comment
      postContent.value = null
    }
    
    viewDialogVisible.value = true
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 处理违规内容
const handleProcess = (row: ViolationContent) => {
  currentItem.value = row
  processForm.result = ''
  processDialogVisible.value = true
}

// 处理违规内容确认
const handleProcessConfirm = async () => {
  if (!processForm.result) {
    ElMessage.warning('请选择处理结果')
    return
  }
  
  loading.value = true
  try {
    if (currentItem.value) {
      // 使用数字状态1（已处理）
      await processViolation(currentItem.value.id, 1, processForm.result)
      ElMessage.success('处理成功')
      
      processDialogVisible.value = false
      if (viewDialogVisible.value) {
        viewDialogVisible.value = false
      }
      
      // 刷新数据
      loadData()
    }
  } catch (error) {
    console.error('处理失败:', error)
    ElMessage.error('处理失败')
  } finally {
    loading.value = false
  }
}

// 忽略举报
const handleIgnore = async (row: ViolationContent) => {
  ElMessageBox.confirm(
    '确定要忽略此举报吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value = true
    try {
      // 使用数字状态2（已忽略）
      await processViolation(row.id, 2, '举报内容不违规')
      ElMessage.success('已忽略该举报')
      
      if (viewDialogVisible.value) {
        viewDialogVisible.value = false
      }
      
      // 刷新数据
      loadData()
    } catch (error) {
      console.error('操作失败:', error)
      ElMessage.error('操作失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {})
}

// 批量处理
const handleBatchProcess = () => {
  batchProcessForm.action = 'process'
  batchProcessForm.result = ''
  batchProcessDialogVisible.value = true
}

// 确认批量处理
const handleBatchProcessConfirm = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要处理的项目')
    return
  }
  
  if (batchProcessForm.action === 'process' && !batchProcessForm.result) {
    ElMessage.warning('请选择处理结果')
    return
  }
  
  loading.value = true
  try {
    const ids = selectedItems.value.map(item => item.id)
    
    if (batchProcessForm.action === 'process') {
      // 使用数字状态1（已处理）
      await batchProcessViolations(ids, 1, batchProcessForm.result)
      ElMessage.success(`批量处理${selectedItems.value.length}项成功`)
    } else {
      // 使用数字状态2（已忽略）
      await batchProcessViolations(ids, 2, '批量忽略违规举报')
      ElMessage.success(`批量忽略${selectedItems.value.length}项成功`)
    }
    
    batchProcessDialogVisible.value = false
    
    // 刷新数据
    loadData()
  } catch (error) {
    console.error('批量处理失败:', error)
    ElMessage.error('批量处理失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.forum-violations {
  padding: 20px;
}

.page-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.page-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.violation-content {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.violation-detail {
  padding: 20px 0;
}

.content-preview {
  margin-top: 24px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.content-preview h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #409eff;
  font-size: 16px;
}

.post-preview h3 {
  margin-top: 0;
  margin-bottom: 12px;
}

.post-meta, .comment-meta {
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.post-content, .comment-content {
  line-height: 1.8;
}

.process-content {
  max-height: 100px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.batch-empty {
  text-align: center;
  padding: 30px 0;
  color: #909399;
}
</style> 