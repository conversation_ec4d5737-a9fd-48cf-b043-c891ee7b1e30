package com.cy.education.service;

import com.cy.education.model.entity.Student;
import com.cy.education.model.vo.PageResponse;

import java.util.List;
import java.util.Map;

/**
 * 学员服务接口
 */
public interface StudentService {

    /**
     * 根据ID查询学员详情
     *
     * @param id 学员ID
     * @return 学员详情
     */
    Student getStudentById(Integer id);

    /**
     * 更新学员信息
     *
     * @param student 学员信息
     * @return 是否成功
     */
    boolean updateStudent(Student student);

    /**
     * 更新学生积分
     *
     * @param userId 用户ID
     * @param points 积分余额
     * @return 是否成功
     */
    boolean updateStudentPoints(Integer userId, Integer points);

    // =========================== 论坛相关用户功能 ===========================

    /**
     * 获取用户资料（用于论坛展示）
     *
     * @param userId        用户ID
     * @param currentUserId 当前用户ID
     * @return 用户资料
     */
    Map<String, Object> getUserProfile(Integer userId, Integer currentUserId);

    /**
     * 获取用户的粉丝列表
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 粉丝列表
     */
    PageResponse<Map<String, Object>> getUserFollowers(Integer userId, Integer pageNum, Integer pageSize);

    /**
     * 获取用户关注的用户列表
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 关注列表
     */
    PageResponse<Map<String, Object>> getUserFollowing(Integer userId, Integer pageNum, Integer pageSize);
}
