package com.cy.education.service.impl.forum;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.forum.ForumCategory;
import com.cy.education.model.params.ForumCategoryQueryParam;
import com.cy.education.repository.ForumCategoryMapper;
import com.cy.education.service.forum.ForumCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 论坛分类服务实现
 */
@Service
public class ForumCategoryServiceImpl implements ForumCategoryService {

    @Autowired
    private ForumCategoryMapper forumCategoryMapper;

    @Override
    public List<ForumCategory> getCategoryTree(ForumCategoryQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<ForumCategory> queryWrapper = new LambdaQueryWrapper<>();

        // 关键词搜索
        if (!StringUtils.isEmpty(param.getKeyword())) {
            queryWrapper.like(ForumCategory::getName, param.getKeyword())
                    .or()
                    .like(ForumCategory::getDescription, param.getKeyword());
        }

        // 状态过滤
        if (param.getStatus() != null) {
            queryWrapper.eq(ForumCategory::getStatus, param.getStatus());
        }

        // 按照排序号和创建时间排序
        queryWrapper.orderByAsc(ForumCategory::getSort).orderByAsc(ForumCategory::getCreatedAt);

        // 查询所有分类
        List<ForumCategory> allCategories = forumCategoryMapper.selectList(queryWrapper);

        // 查询并设置帖子数量
        for (ForumCategory category : allCategories) {
            int postCount = forumCategoryMapper.countPostsByCategoryId(category.getId());
            category.setPostCount(postCount);
        }

        // 如果指定了父分类ID，只返回该父分类下的子分类
        if (param.getParentId() != null) {
            return allCategories.stream()
                    .filter(c -> param.getParentId().equals(c.getParentId()))
                    .collect(Collectors.toList());
        }

        // 构建树形结构
        return buildCategoryTree(allCategories);
    }

    /**
     * 构建分类树形结构
     */
    private List<ForumCategory> buildCategoryTree(List<ForumCategory> allCategories) {
        // 按照父分类ID分组
        Map<Integer, List<ForumCategory>> parentIdMap = allCategories.stream()
                .collect(Collectors.groupingBy(c -> c.getParentId() == null ? -1 : c.getParentId()));

        // 获取顶级分类（无父分类的）
        List<ForumCategory> rootCategories = parentIdMap.getOrDefault(-1, new ArrayList<>());

        // 为每个分类设置子分类
        for (ForumCategory category : allCategories) {
            // 设置是否是父分类
            List<ForumCategory> children = parentIdMap.get(category.getId());
            if (children != null && !children.isEmpty()) {
                category.setIsParent(true);
                category.setChildren(children);

                // 累加子分类的帖子数量
                int totalPostCount = category.getPostCount() == null ? 0 : category.getPostCount();
                for (ForumCategory child : children) {
                    totalPostCount += child.getPostCount() == null ? 0 : child.getPostCount();
                }
                category.setPostCount(totalPostCount);
            } else {
                category.setIsParent(false);
            }
        }

        return rootCategories;
    }

    @Override
    public ForumCategory getCategoryById(Integer id) {
        ForumCategory category = forumCategoryMapper.selectById(id);
        if (category == null) {
            throw new BusinessException("分类不存在");
        }

        // 设置帖子数量
        int postCount = forumCategoryMapper.countPostsByCategoryId(id);
        category.setPostCount(postCount);

        // 检查是否是父分类
        LambdaQueryWrapper<ForumCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForumCategory::getParentId, id);
        List<ForumCategory> children = forumCategoryMapper.selectList(queryWrapper);

        category.setIsParent(!children.isEmpty());
        if (!children.isEmpty()) {
            // 查询子分类的帖子数量
            for (ForumCategory child : children) {
                int childPostCount = forumCategoryMapper.countPostsByCategoryId(child.getId());
                child.setPostCount(childPostCount);
            }
            category.setChildren(children);
        }

        return category;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addCategory(ForumCategory category) {
        // 设置默认值
        if (category.getSort() == null) {
            category.setSort(0);
        }
        if (category.getStatus() == null) {
            category.setStatus(1); // 默认启用
        }

        // 如果是子分类，检查父分类是否存在
        if (category.getParentId() != null) {
            ForumCategory parent = forumCategoryMapper.selectById(category.getParentId());
            if (parent == null) {
                throw new BusinessException("父分类不存在");
            }
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        category.setCreatedAt(now);
        category.setUpdatedAt(now);

        // 保存分类
        forumCategoryMapper.insert(category);

        return category.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(ForumCategory category) {
        // 检查分类是否存在
        ForumCategory existingCategory = forumCategoryMapper.selectById(category.getId());
        if (existingCategory == null) {
            throw new BusinessException("分类不存在");
        }

        // 设置更新时间
        category.setUpdatedAt(LocalDateTime.now());

        // 如果是子分类，检查父分类是否存在
        if (category.getParentId() != null) {
            ForumCategory parent = forumCategoryMapper.selectById(category.getParentId());
            if (parent == null) {
                throw new BusinessException("父分类不存在");
            }

            // 检查是否将分类设置为自己的子分类（循环引用）
            if (category.getId().equals(category.getParentId())) {
                throw new BusinessException("不能将分类设置为自己的子分类");
            }
        }

        return forumCategoryMapper.updateById(category) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(Integer id) {
        // 检查分类是否存在
        ForumCategory category = forumCategoryMapper.selectById(id);
        if (category == null) {
            throw new BusinessException("分类不存在");
        }

        // 检查是否有子分类
        LambdaQueryWrapper<ForumCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForumCategory::getParentId, id);
        long childCount = forumCategoryMapper.selectCount(queryWrapper);
        if (childCount > 0) {
            throw new BusinessException("该分类下有子分类，无法删除");
        }

        // 检查分类下是否有帖子
        int postCount = forumCategoryMapper.countPostsByCategoryId(id);
        if (postCount > 0) {
            throw new BusinessException("该分类下有帖子，无法删除");
        }

        return forumCategoryMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategoryStatus(Integer id, Integer status) {
        // 检查分类是否存在
        ForumCategory category = forumCategoryMapper.selectById(id);
        if (category == null) {
            throw new BusinessException("分类不存在");
        }

        // 检查状态是否合法
        if (status != 0 && status != 1) {
            throw new BusinessException("状态值不合法，应为0或1");
        }

        // 更新状态
        LambdaUpdateWrapper<ForumCategory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ForumCategory::getId, id)
                .set(ForumCategory::getStatus, status)
                .set(ForumCategory::getUpdatedAt, LocalDateTime.now());

        boolean success = forumCategoryMapper.update(null, updateWrapper) > 0;

        // 如果是父分类，同时更新所有子分类的状态
        if (success) {
            LambdaQueryWrapper<ForumCategory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ForumCategory::getParentId, id);
            List<ForumCategory> children = forumCategoryMapper.selectList(queryWrapper);

            if (!children.isEmpty()) {
                LambdaUpdateWrapper<ForumCategory> childUpdateWrapper = new LambdaUpdateWrapper<>();
                childUpdateWrapper.eq(ForumCategory::getParentId, id)
                        .set(ForumCategory::getStatus, status)
                        .set(ForumCategory::getUpdatedAt, LocalDateTime.now());
                forumCategoryMapper.update(null, childUpdateWrapper);
            }
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategorySort(List<ForumCategory> sortData) {
        if (sortData == null || sortData.isEmpty()) {
            return false;
        }

        LocalDateTime now = LocalDateTime.now();

        // 批量更新排序
        for (ForumCategory item : sortData) {
            if (item.getId() == null || item.getSort() == null) {
                continue;
            }

            LambdaUpdateWrapper<ForumCategory> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ForumCategory::getId, item.getId())
                    .set(ForumCategory::getSort, item.getSort())
                    .set(ForumCategory::getUpdatedAt, now);

            forumCategoryMapper.update(null, updateWrapper);
        }

        return true;
    }

    @Override
    public List<ForumCategory> getParentCategories() {
        // 查询所有父分类（parentId为null的）
        LambdaQueryWrapper<ForumCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(ForumCategory::getParentId)
                .eq(ForumCategory::getStatus, 1) // 只查询启用的分类
                .orderByAsc(ForumCategory::getSort);

        List<ForumCategory> parentCategories = forumCategoryMapper.selectList(queryWrapper);

        // 设置标识
        parentCategories.forEach(c -> c.setIsParent(true));

        return parentCategories;
    }
}
