package com.cy.education.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.points.PointsExchange;
import com.cy.education.model.entity.points.PointsRecord;
import com.cy.education.model.params.PointsExchangeQueryParam;
import com.cy.education.model.params.PointsQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.points.PointsExchangeService;
import com.cy.education.service.points.PointsRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 积分管理控制器
 */
@Api(tags = "积分管理")
@RestController
@RequestMapping("/points")
public class PointsController {

    @Autowired
    private PointsRecordService pointsRecordService;

    @Autowired
    private PointsExchangeService pointsExchangeService;

    /**
     * 获取积分记录列表
     */
    @ApiOperation("获取积分记录列表")
    @GetMapping("/records")
    public ApiResponse<PageResponse<PointsRecord>> getRecordList(PointsQueryParam param) {
        IPage<PointsRecord> page = pointsRecordService.page(param);
        return ApiResponse.success(PageResponse.from(page));
    }

    /*
     * 获取用户积分余额
     */
    @ApiOperation("获取用户积分余额")
    @GetMapping("/balance/{userId}")
    public ApiResponse<Integer> getUserBalance(@PathVariable Integer userId) {
        Integer balance = pointsRecordService.getUserBalance(userId);
        return ApiResponse.success(balance);
    }

    /**
     * 获取兑换记录列表
     */
    @ApiOperation("获取兑换记录列表")
    @GetMapping("/exchanges")
    public ApiResponse<PageResponse<PointsExchange>> getExchangeList(PointsExchangeQueryParam param) {
        IPage<PointsExchange> page = pointsExchangeService.page(param);
        return ApiResponse.success(PageResponse.from(page));
    }

    /**
     * 获取兑换记录详情
     */
    @ApiOperation("获取兑换记录详情")
    @GetMapping("/exchanges/{id}")
    public ApiResponse<PointsExchange> getExchangeDetail(@PathVariable Integer id) {
        PointsExchange exchange = pointsExchangeService.getById(id);
        return ApiResponse.success(exchange);
    }

    /**
     * 创建兑换记录
     */
    @ApiOperation("创建兑换记录")
    @PostMapping("/exchanges")
    public ApiResponse<Map<String, Object>> createExchange(@RequestBody @Valid PointsExchange exchange) {
        return pointsExchangeService.create(exchange);
    }
}
