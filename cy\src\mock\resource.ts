import { 
  generateId, 
  randomDate, 
  randomInt, 
  mockResponse,
  paginateData,
  getQueryParams
} from './utils'
import type { 
  FileResource, 
  VideoResource, 
  ArticleResource,
  ResourceTag
} from '@/api/resource'

// 修改generateId的使用方式，返回number类型
function getNextId(collection: any[]): number {
  const maxId = collection.reduce((max, item) => Math.max(max, item.id || 0), 0);
  return maxId + 1;
}

// 生成模拟文件资源数据
function generateFiles(count: number = 20): FileResource[] {
  const files: FileResource[] = []
  
  const fileTypes = [
    { ext: 'pdf', type: 'application/pdf' },
    { ext: 'docx', type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
    { ext: 'xlsx', type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
    { ext: 'pptx', type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' },
    { ext: 'txt', type: 'text/plain' }
  ]
  
  const fileNames = [
    '安全生产操作手册',
    '矿山设备使用指南',
    '员工培训计划表',
    '安全事故案例分析',
    '矿山安全规程',
    '应急救援预案',
    '设备维护保养手册',
    '安全检查记录表',
    '技术培训资料',
    '风险评估报告'
  ]
  
  for (let i = 0; i < count; i++) {
    const fileType = fileTypes[randomInt(0, fileTypes.length - 1)]
    const fileName = fileNames[randomInt(0, fileNames.length - 1)]
    const extension = fileType.ext
    
    const file: FileResource = {
      id: i + 1,
      name: `${fileName}_${randomInt(1, 999)}.${extension}`,
      size: randomInt(100 * 1024, 10 * 1024 * 1024), // 100KB到10MB
      uploadTime: randomDate(new Date(2022, 0, 1)),
      downloadCount: randomInt(0, 500),
      url: `https://example.com/files/${i + 1}.${extension}`,
      type: fileType.type,
      extension
    }
    
    files.push(file)
  }
  
  return files
}

// 生成模拟视频资源数据
function generateVideos(count: number = 20): VideoResource[] {
  const videos: VideoResource[] = []
  
  const videoNames = [
    '安全生产操作规程培训',
    '矿山救援演练实录',
    '设备维护保养指南',
    '安全事故案例分析与预防',
    '矿山设备操作演示',
    '应急救援培训',
    '安全生产法规解读',
    '新员工入职培训',
    '特种设备操作规程',
    '安全生产知识讲座'
  ]
  
  for (let i = 0; i < count; i++) {
    const videoName = videoNames[randomInt(0, videoNames.length - 1)]
    const duration = randomInt(300, 3600) // 5分钟到1小时
    
    const video: VideoResource = {
      id: i + 1,
      name: `${videoName}_${randomInt(1, 999)}.mp4`,
      duration,
      size: duration * randomInt(1000000, 3000000), // 根据时长生成大小
      uploadTime: randomDate(new Date(2022, 0, 1)),
      viewCount: randomInt(0, 1000),
      url: 'https://www.w3schools.com/html/mov_bbb.mp4', // 示例视频URL
      coverUrl: `https://picsum.photos/300/200?random=${i}`
    }
    
    videos.push(video)
  }
  
  return videos
}

// 生成模拟文章资源数据
function generateArticles(count: number = 20): ArticleResource[] {
  const articles: ArticleResource[] = []
  
  const articleTitles = [
    '矿山安全生产十大要点',
    '新型矿山设备使用指南',
    '矿山应急救援案例分析',
    '最新矿山安全法规解读',
    '提高矿山安全生产水平的措施',
    '矿山设备维护保养技巧',
    '矿山安全事故分析与预防',
    '矿山安全管理体系建设',
    '矿山特种作业人员培训要点',
    '矿山安全生产标准化建设'
  ]
  
  const authors = ['张安全', '李工程', '王救援', '赵法规', '钱技术']
  
  const tags = [
    '安全生产',
    '技术培训',
    '设备操作',
    '案例分析',
    '政策法规',
    '矿山救援',
    '应急处理',
    '风险防控',
    '标准化建设',
    '安全管理'
  ]
  
  for (let i = 0; i < count; i++) {
    const title = articleTitles[randomInt(0, articleTitles.length - 1)]
    const author = authors[randomInt(0, authors.length - 1)]
    const tagCount = randomInt(1, 3)
    const articleTags: string[] = []
    
    // 随机选择1-3个标签
    for (let j = 0; j < tagCount; j++) {
      const tag = tags[randomInt(0, tags.length - 1)]
      if (!articleTags.includes(tag)) {
        articleTags.push(tag)
      }
    }
    
    const article: ArticleResource = {
      id: i + 1,
      title: `${title}_${randomInt(1, 999)}`,
      author,
      publishTime: randomDate(new Date(2022, 0, 1)),
      readCount: randomInt(0, 1000),
      tags: articleTags,
      content: `<h2>${title}</h2><p>本文介绍了${title}的相关内容，包括背景、方法、步骤和注意事项等。</p><p>一、背景介绍</p><p>矿山安全生产是企业发展的基础，关系到每位员工的生命安全...</p><p>二、主要内容</p><p>1. 安全生产责任制度的落实</p><p>2. 安全技术措施的实施</p><p>3. 安全教育培训的开展</p><p>三、实施效果</p><p>通过以上措施的实施，有效提高了企业安全生产水平，降低了事故发生率...</p>`
    }
    
    articles.push(article)
  }
  
  return articles
}

// 资源数据集
export const files = generateFiles(20)
export const videos = generateVideos(20)
export const articles = generateArticles(20)

// 添加标签数据集
export const tags: ResourceTag[] = [
  { id: 1, name: '安全生产' },
  { id: 2, name: '技术培训' },
  { id: 3, name: '设备操作' },
  { id: 4, name: '案例分析' },
  { id: 5, name: '政策法规' },
  { id: 6, name: '矿山救援' },
  { id: 7, name: '应急处理' },
  { id: 8, name: '风险防控' },
  { id: 9, name: '标准化建设' },
  { id: 10, name: '安全管理' },
  { id: 11, name: '新员工培训' },
  { id: 12, name: '职业健康' },
  { id: 13, name: '环保知识' },
  { id: 14, name: '机电设备' },
  { id: 15, name: '工艺技术' },
  { id: 16, name: '规章制度' },
  { id: 17, name: '防灾减灾' },
  { id: 18, name: '作业规范' },
  { id: 19, name: '安全隐患' },
  { id: 20, name: '事故调查' }
]

// 文件资源接口
export function mockGetFileList(url: string) {
  const params = getQueryParams(url)
  const filteredFiles = files.filter(file => {
    if (params.keyword && !file.name.toLowerCase().includes(params.keyword.toLowerCase())) {
      return false
    }
    return true
  })
  
  const paginatedData = paginateData(filteredFiles, parseInt(params.page) || 1, parseInt(params.limit) || 10)
  
  return mockResponse({
    list: paginatedData.list,
    total: paginatedData.total
  })
}

export function mockUploadFile(data: FormData) {
  // 获取表单数据
  const name = data.get('name') as string || '';
  const url = data.get('url') as string || '';
  const sizeStr = data.get('size') as string || '0';
  const size = parseInt(sizeStr);
  const extension = data.get('extension') as string || '';
  const tagIdsStr = data.get('tagIds') as string;
  let tagIds: number[] = [];
  
  try {
    if (tagIdsStr) {
      tagIds = JSON.parse(tagIdsStr);
    }
  } catch (e) {
    console.error('解析标签ID失败:', e);
  }
  
  // 创建新文件记录
  const id = getNextId(files);
  const newFile: FileResource = {
    id,
    name,
    size,
    uploadTime: new Date().toLocaleString(),
    downloadCount: 0,
    url,
    type: extension ? `application/${extension}` : 'application/octet-stream',
    extension,
    tags: tagIds.map(tagId => {
      const tag = tags.find(t => t.id === tagId);
      return tag ? tag.name : '';
    }).filter(Boolean)
  };
  
  // 添加到文件列表
  files.unshift(newFile);
  
  return mockResponse({
    id
  });
}

export function mockDeleteFile(url: string) {
  // 从URL中提取ID
  const id = url.split('/').pop()
  
  // 模拟删除操作
  const index = files.findIndex(file => file.id === id)
  if (index !== -1) {
    files.splice(index, 1)
  }
  
  return mockResponse({
    success: true
  })
}

// 视频资源接口
export function mockGetVideoList(url: string) {
  const params = getQueryParams(url)
  const filteredVideos = videos.filter(video => {
    if (params.keyword && !video.name.toLowerCase().includes(params.keyword.toLowerCase())) {
      return false
    }
    return true
  })
  
  const paginatedData = paginateData(filteredVideos, parseInt(params.page) || 1, parseInt(params.limit) || 10)
  
  return mockResponse({
    list: paginatedData.list,
    total: paginatedData.total
  })
}

export function mockUploadVideo(data: FormData) {
  // 获取表单数据
  const name = data.get('name') as string || '';
  const url = data.get('url') as string || '';
  const sizeStr = data.get('size') as string || '0';
  const size = parseInt(sizeStr);
  const durationStr = data.get('duration') as string || '0';
  const duration = parseInt(durationStr);
  const coverUrl = data.get('coverUrl') as string;
  const tagIdsStr = data.get('tagIds') as string;
  let tagIds: number[] = [];
  
  try {
    if (tagIdsStr) {
      tagIds = JSON.parse(tagIdsStr);
    }
  } catch (e) {
    console.error('解析标签ID失败:', e);
  }
  
  // 创建新视频记录
  const id = getNextId(videos);
  const newVideo: VideoResource = {
    id,
    name,
    duration,
    size,
    uploadTime: new Date().toLocaleString(),
    viewCount: 0,
    url,
    coverUrl,
    tags: tagIds.map(tagId => {
      const tag = tags.find(t => t.id === tagId);
      return tag ? tag.name : '';
    }).filter(Boolean)
  };
  
  // 添加到视频列表
  videos.unshift(newVideo);
  
  return mockResponse({
    id
  });
}

export function mockDeleteVideo(url: string) {
  // 从URL中提取ID
  const id = url.split('/').pop()
  
  // 模拟删除操作
  const index = videos.findIndex(video => video.id === id)
  if (index !== -1) {
    videos.splice(index, 1)
  }
  
  return mockResponse({
    success: true
  })
}

// 文章资源接口
export function mockGetArticleList(url: string) {
  const params = getQueryParams(url)
  const filteredArticles = articles.filter(article => {
    if (params.keyword && !article.title.toLowerCase().includes(params.keyword.toLowerCase())) {
      return false
    }
    return true
  })
  
  const paginatedData = paginateData(filteredArticles, parseInt(params.page) || 1, parseInt(params.limit) || 10)
  
  return mockResponse({
    list: paginatedData.list,
    total: paginatedData.total
  })
}

export function mockGetArticleDetail(url: string) {
  // 从URL中提取ID
  const id = url.split('/').pop()
  
  // 查找文章
  const article = articles.find(article => article.id === id)
  
  if (!article) {
    return mockResponse({
      code: 404,
      message: '文章不存在',
      data: null
    })
  }
  
  return mockResponse(article)
}

export function mockCreateArticle(data: Partial<ArticleResource>) {
  // 创建新文章
  const id = generateId()
  const now = new Date().toLocaleString()
  
  const newArticle: ArticleResource = {
    id,
    title: data.title || '',
    author: data.author || '',
    publishTime: now,
    readCount: 0,
    tags: data.tags || [],
    content: data.content || ''
  }
  
  articles.unshift(newArticle)
  
  return mockResponse({
    id
  })
}

export function mockUpdateArticle(url: string, data: Partial<ArticleResource>) {
  // 从URL中提取ID
  const id = url.split('/').pop()
  
  // 查找并更新文章
  const index = articles.findIndex(article => article.id === id)
  
  if (index !== -1) {
    articles[index] = {
      ...articles[index],
      ...data,
      publishTime: articles[index].publishTime // 保持原发布时间不变
    }
  }
  
  return mockResponse({
    success: true
  })
}

export function mockDeleteArticle(url: string) {
  // 从URL中提取ID
  const id = url.split('/').pop()
  
  // 模拟删除操作
  const index = articles.findIndex(article => article.id === id)
  if (index !== -1) {
    articles.splice(index, 1)
  }
  
  return mockResponse({
    success: true
  })
}

// 标签资源接口
export function mockGetResourceTags() {
  return mockResponse(tags)
}

export function mockAddResourceTag(data: { name: string }) {
  // 检查标签是否已存在
  const existingTag = tags.find(tag => tag.name === data.name)
  if (existingTag) {
    return mockResponse(existingTag)
  }
  
  // 创建新标签
  const newTag: ResourceTag = {
    id: getNextId(tags),
    name: data.name
  }
  
  tags.push(newTag)
  return mockResponse(newTag)
}

export function mockGetVideosByTag(url: string) {
  // 从URL中提取标签ID
  const tagId = parseInt(url.split('/').pop() || '0')
  
  // 模拟按标签过滤视频
  const filteredVideos = videos.filter((_, index) => index % (tagId + 1) === 0)
  
  return mockResponse(filteredVideos)
}

export function mockGetFilesByTag(url: string) {
  // 从URL中提取标签ID
  const tagId = parseInt(url.split('/').pop() || '0')
  
  // 模拟按标签过滤文件
  const filteredFiles = files.filter((_, index) => index % (tagId + 1) === 0)
  
  return mockResponse(filteredFiles)
}

export function mockGetArticlesByTag(url: string) {
  // 从URL中提取标签ID
  const tagId = parseInt(url.split('/').pop() || '0')
  
  // 模拟按标签过滤文章
  const filteredArticles = articles.filter((_, index) => index % (tagId + 1) === 0)
  
  return mockResponse(filteredArticles)
}

export function mockSaveFileResource(data: any) {
  // 创建新文件记录
  const id = getNextId(files);
  const now = new Date().toLocaleString();
  
  const newFile: FileResource = {
    id,
    name: data.name || '',
    size: data.size || 0,
    uploadTime: now,
    downloadCount: 0,
    url: data.url || '',
    type: data.type || 'application/octet-stream',
    extension: data.extension || '',
    tags: []
  };
  
  // 如果有标签ID，添加标签名称
  if (data.tagIds && Array.isArray(data.tagIds) && data.tagIds.length > 0) {
    newFile.tags = data.tagIds
      .map((tagId: number) => {
        const tag = tags.find(t => t.id === tagId);
        return tag ? tag.name : '';
      })
      .filter(Boolean);
  }
  
  // 添加到文件列表
  files.unshift(newFile);
  
  return mockResponse({
    id
  });
}

export function mockSaveVideoResource(data: any) {
  // 创建新视频记录
  const id = getNextId(videos);
  const now = new Date().toLocaleString();
  
  const newVideo: VideoResource = {
    id,
    name: data.name || '',
    duration: data.duration || 0,
    size: data.size || 0,
    uploadTime: now,
    viewCount: 0,
    url: data.url || '',
    coverUrl: data.coverUrl,
    tags: []
  };
  
  // 如果有标签ID，添加标签名称
  if (data.tagIds && Array.isArray(data.tagIds) && data.tagIds.length > 0) {
    newVideo.tags = data.tagIds
      .map((tagId: number) => {
        const tag = tags.find(t => t.id === tagId);
        return tag ? tag.name : '';
      })
      .filter(Boolean);
  }
  
  // 添加到视频列表
  videos.unshift(newVideo);
  
  return mockResponse({
    id
  });
} 