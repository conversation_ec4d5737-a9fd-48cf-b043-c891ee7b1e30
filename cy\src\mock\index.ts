import axios, { AxiosRequestConfig, InternalAxiosRequestConfig } from 'axios'
import { delay, getQueryParams, mockResponse } from './utils'
import { setupMock } from './xhr'

// 导入各模块的模拟数据处理函数
import {
  mockLogin,
  mockLogout,
  mockGetUserInfo,
  mockGetVerificationCode,
  mockResetPassword,
  mockUpdateUserInfo,
  mockChangePassword,
  mockUploadAvatar
} from './user'

import {
  mockGetCourseList,
  mockGetCourseDetail,
  mockCreateCourse,
  mockUpdateCourse,
  mockDeleteCourse,
  mockPublishCourse,
  mockGetStudyRecordList,
  mockGetStudyRecordDetail,
  mockGetCourseStatistics,
  mockGetUserStudyStatistics
} from './course'

import {
  mockGetPointsRecordList,
  mockGetUserPointsBalance,
  mockAdjustUserPoints,
  mockBatchAdjustPoints,
  mockGetPointsStatistics,
  mockGetUserPointsTrend,
  mockExportPointsRecords,
  mockGetPointsRuleList,
  mockGetPointsRuleDetail,
  mockCreatePointsRule,
  mockUpdatePointsRule,
  mockDeletePointsRule,
  mockBatchDeletePointsRules,
  mockTogglePointsRuleStatus,
  mockGetPointsRuleStatistics,
  mockExportPointsRules
} from './points'

import {
  mockGetProductList,
  mockGetProductDetail,
  mockCreateProduct,
  mockUpdateProduct,
  mockDeleteProduct,
  mockChangeProductStatus,
  mockUpdateProductStock,
  mockGetProductCategories,
  mockCreateProductCategory,
  mockUpdateProductCategory,
  mockDeleteProductCategory,
  mockBatchUpdateProductStatus,
  mockGetProductStatistics
} from './product'

import {
  mockGetExchangeList,
  mockGetExchangeDetail,
  mockReviewExchange,
  mockUpdateExchangeExpress,
  mockConfirmExchangeDelivery,
  mockCancelExchange,
  mockBatchUpdateExchangeStatus,
  mockGetExchangeStatistics,
  mockExportExchanges
} from './exchange'

import {
  mockGetStudyRecordList as mockGetStudyRecordListNew,
  mockGetDepartmentStatistics,
  mockGetActiveStudents,
  mockGetUserStudyRecords,
  mockGetCourseStudyRecords,
  mockDeleteStudyRecord,
  mockExportStudyRecords
} from './study'

import {
  mockGetFileList,
  mockUploadFile,
  mockDeleteFile,
  mockGetVideoList,
  mockUploadVideo,
  mockDeleteVideo,
  mockGetArticleList,
  mockGetArticleDetail,
  mockCreateArticle,
  mockUpdateArticle,
  mockDeleteArticle,
  mockGetResourceTags,
  mockAddResourceTag,
  mockGetVideosByTag,
  mockGetFilesByTag,
  mockGetArticlesByTag,
  mockSaveFileResource,
  mockSaveVideoResource
} from './resource'

import * as departmentMock from './department'
import * as adminMock from './admin'
import * as studentMock from './student'
import * as forumMock from './forum'
import { carouselMockHandlers, newsMockHandlers, noticeMockHandlers } from './content'

import { 
  mockGetQuestionList, 
  mockGetQuestionDetail, 
  mockCreateQuestion, 
  mockUpdateQuestion, 
  mockDeleteQuestion,
  mockGetPaperList,
  mockGetPaperDetail,
  mockGetExamList,
  mockGetExamRecordList,
  mockGetExamStatistics,
  mockGetBankList,
  mockGetBankDetail,
  mockCreateBank,
  mockUpdateBank,
  mockDeleteBank
} from './exam'

// 模拟接口开关 - 暂时禁用登录和用户信息相关的模拟接口
const enableMock = import.meta.env.VITE_ENABLE_MOCK === 'true' || false
// const enableMock = false
const enableUserMock = false // 禁用用户相关的模拟接口

/**
 * 设置请求拦截器
 */
function setupInterceptor() {
  if (!enableMock) {
    console.log('Mock is disabled')
    return
  }

  console.log('Mock is enabled')

  // 使用响应拦截器替代请求拦截器
  const interceptor = axios.interceptors.response.use(
    // 对正常响应不做处理
    response => response,
    // 处理请求错误或模拟响应
    async (error) => {
      // 如果不是请求错误或已有响应，则直接抛出错误
      if (!error.config || error.response) {
        return Promise.reject(error)
      }

      const { url, method, data } = error.config
      
      // 如果URL不存在，则直接抛出错误
      if (!url) {
        return Promise.reject(error)
      }

      console.log('拦截请求:', method, url)
      
      // 解析请求数据
      let requestData: any = {}
      if (data) {
        try {
          if (typeof data === 'string') {
            requestData = JSON.parse(data)
          } else {
            requestData = data
          }
        } catch (err) {
          console.error('解析请求数据失败:', err)
        }
      }
      
      console.log('请求数据:', requestData)

      try {
        // 延迟响应，模拟网络请求
        await delay(300)

        let mockResult = null

        // 用户接口 - 根据enableUserMock开关决定是否拦截
        if (enableUserMock && url.includes('/user/login') && method === 'post') {
          mockResult = mockLogin(requestData.username, requestData.password)
        } else if (enableUserMock && url.includes('/user/logout') && method === 'post') {
          mockResult = mockLogout()
        } else if (enableUserMock && url.includes('/user/info') && method === 'get') {
          // 从请求头获取token
          const token = error.config.headers?.Authorization?.toString().split(' ')[1] || ''
          mockResult = mockGetUserInfo(token)
        } else if (enableUserMock && url.includes('/user/code') && method === 'get') {
          // 获取URL参数
          const urlObj = new URL(url, 'http://localhost')
          const phone = urlObj.searchParams.get('phone') || ''
          mockResult = mockGetVerificationCode(phone)
        } else if (enableUserMock && url.includes('/user/reset-password') && method === 'post') {
          mockResult = mockResetPassword(requestData)
        } else if (enableUserMock && url.includes('/user/update') && method === 'post') {
          mockResult = mockUpdateUserInfo(requestData)
        } else if (enableUserMock && url.includes('/user/change-password') && method === 'post') {
          mockResult = mockChangePassword(
            requestData.oldPassword || '',
            requestData.newPassword || ''
          )
        } else if (enableUserMock && url.includes('/user/upload-avatar') && method === 'post') {
          mockResult = mockUploadAvatar(requestData)
        }
        
        // 课程接口
        else if (url.includes('/api/course/list') && method === 'get') {
          mockResult = mockGetCourseList(url)
        } else if (url.match(/\/api\/course\/detail\/\w+/) && method === 'get') {
          mockResult = mockGetCourseDetail(url)
        } else if (url.includes('/api/course/create') && method === 'post') {
          mockResult = mockCreateCourse(requestData)
        } else if (url.match(/\/api\/course\/update\/\w+/) && method === 'put') {
          mockResult = mockUpdateCourse(url, requestData)
        } else if (url.match(/\/api\/course\/delete\/\w+/) && method === 'delete') {
          mockResult = mockDeleteCourse(url)
        } else if (url.match(/\/api\/course\/publish\/\w+/) && method === 'post') {
          mockResult = mockPublishCourse(url, { isPublished: !!requestData.isPublished })
        } else if (url.includes('/api/course/study-record/list') && method === 'get') {
          mockResult = mockGetStudyRecordList(url)
        } else if (url.match(/\/api\/course\/study-record\/detail\/\w+/) && method === 'get') {
          mockResult = mockGetStudyRecordDetail(url)
        } else if (url.includes('/api/course/statistics') && method === 'get') {
          mockResult = mockGetCourseStatistics()
        } else if (url.match(/\/api\/course\/study-statistics\/\w+/) && method === 'get') {
          mockResult = mockGetUserStudyStatistics(url)
        }
        
        // 积分接口
        else if (url.includes('/api/points/records') && method === 'get') {
          mockResult = mockGetPointsRecordList(url)
        } else if (url.match(/\/api\/points\/balance\/\w+/) && method === 'get') {
          mockResult = mockGetUserPointsBalance(url)
        } else if (url.includes('/api/points/adjust') && method === 'post') {
          mockResult = mockAdjustUserPoints(requestData)
        } else if (url.includes('/api/points/batch-adjust') && method === 'post') {
          mockResult = mockBatchAdjustPoints(Array.isArray(requestData) ? requestData : [])
        } else if (url.includes('/api/points/statistics') && method === 'get') {
          mockResult = mockGetPointsStatistics()
        } else if (url.match(/\/api\/points\/trend\/\w+/) && method === 'get') {
          const urlObj = new URL(url, 'http://localhost')
          const params = Object.fromEntries(urlObj.searchParams)
          mockResult = mockGetUserPointsTrend(url, { period: params.period || 'month' })
        } else if (url.includes('/api/points/export') && method === 'get') {
          mockResult = mockExportPointsRecords()
        }
        
        // 积分规则接口
        else if (url.includes('/api/points/rules') && url.includes('statistics') && method === 'get') {
          mockResult = mockGetPointsRuleStatistics()
        } else if (url.includes('/api/points/rules') && url.includes('export') && method === 'get') {
          mockResult = mockExportPointsRules()
        } else if (url.match(/\/api\/points\/rules\/\w+\/status/) && method === 'put') {
          mockResult = mockTogglePointsRuleStatus(url, requestData)
        } else if (url.match(/\/api\/points\/rules\/\w+/) && method === 'get') {
          mockResult = mockGetPointsRuleDetail(url)
        } else if (url.match(/\/api\/points\/rules\/\w+/) && method === 'put') {
          mockResult = mockUpdatePointsRule(url, requestData)
        } else if (url.match(/\/api\/points\/rules\/\w+/) && method === 'delete') {
          mockResult = mockDeletePointsRule(url)
        } else if (url.includes('/api/points/rules/batch-delete') && method === 'post') {
          mockResult = mockBatchDeletePointsRules(requestData)
        } else if (url.includes('/api/points/rules') && method === 'get') {
          mockResult = mockGetPointsRuleList(url)
        } else if (url.includes('/api/points/rules') && method === 'post') {
          mockResult = mockCreatePointsRule(requestData)
        }
        
        // 商品接口
        else if (url.includes('/api/product/list') && method === 'get') {
          mockResult = mockGetProductList(url)
        } else if (url.match(/\/api\/product\/detail\/\w+/) && method === 'get') {
          mockResult = mockGetProductDetail(url)
        } else if (url.includes('/api/product/create') && method === 'post') {
          mockResult = mockCreateProduct(requestData)
        } else if (url.match(/\/api\/product\/update\/\w+/) && method === 'put') {
          mockResult = mockUpdateProduct(url, requestData)
        } else if (url.match(/\/api\/product\/delete\/\w+/) && method === 'delete') {
          mockResult = mockDeleteProduct(url)
        } else if (url.match(/\/api\/product\/status\/\w+/) && method === 'post') {
          mockResult = mockChangeProductStatus(url, { 
            status: requestData.status as 'on_shelf' | 'off_shelf' 
          })
        } else if (url.match(/\/api\/product\/stock\/\w+/) && method === 'put') {
          mockResult = mockUpdateProductStock(url, { 
            stock: typeof requestData.stock === 'number' ? requestData.stock : 0 
          })
        } else if (url.includes('/api/product/categories') && method === 'get') {
          mockResult = mockGetProductCategories()
        } else if (url.includes('/api/product/category/create') && method === 'post') {
          mockResult = mockCreateProductCategory(requestData)
        } else if (url.match(/\/api\/product\/category\/update\/\w+/) && method === 'put') {
          mockResult = mockUpdateProductCategory(url, requestData)
        } else if (url.match(/\/api\/product\/category\/delete\/\w+/) && method === 'delete') {
          mockResult = mockDeleteProductCategory(url)
        } else if (url.includes('/api/product/batch-status') && method === 'post') {
          mockResult = mockBatchUpdateProductStatus(requestData)
        } else if (url.includes('/api/product/statistics') && method === 'get') {
          mockResult = mockGetProductStatistics()
        }
        
        // 兑换接口
        else if (url.includes('/api/exchange/list') && method === 'get') {
          mockResult = mockGetExchangeList(url)
        } else if (url.match(/\/api\/exchange\/detail\/\w+/) && method === 'get') {
          mockResult = mockGetExchangeDetail(url)
        } else if (url.match(/\/api\/exchange\/review\/\w+/) && method === 'post') {
          mockResult = mockReviewExchange(url, requestData)
        } else if (url.match(/\/api\/exchange\/express\/\w+/) && method === 'put') {
          mockResult = mockUpdateExchangeExpress(url, requestData)
        } else if (url.match(/\/api\/exchange\/confirm-delivery\/\w+/) && method === 'post') {
          mockResult = mockConfirmExchangeDelivery(url)
        } else if (url.match(/\/api\/exchange\/cancel\/\w+/) && method === 'post') {
          mockResult = mockCancelExchange(url, requestData)
        } else if (url.includes('/api/exchange/batch-status') && method === 'post') {
          mockResult = mockBatchUpdateExchangeStatus(requestData)
        } else if (url.includes('/api/exchange/statistics') && method === 'get') {
          mockResult = mockGetExchangeStatistics()
        } else if (url.includes('/api/exchange/export') && method === 'get') {
          mockResult = mockExportExchanges()
        }
        
        // 资源接口
        else if (url.includes('/api/resource/file/list') && method === 'get') {
          mockResult = mockGetFileList(url)
        } else if (url.includes('/api/resource/file/upload') && method === 'post') {
          mockResult = mockUploadFile(requestData)
        } else if (url.includes('/api/resource/file/save') && method === 'post') {
          mockResult = mockSaveFileResource(requestData)
        } else if (url.match(/\/api\/resource\/file\/delete\/\w+/) && method === 'delete') {
          mockResult = mockDeleteFile(url)
        } else if (url.match(/\/api\/resource\/file\/tag\/\w+/) && method === 'get') {
          mockResult = mockGetFilesByTag(url)
        } else if (url.includes('/api/resource/video/list') && method === 'get') {
          mockResult = mockGetVideoList(url)
        } else if (url.includes('/api/resource/video/upload') && method === 'post') {
          mockResult = mockUploadVideo(requestData)
        } else if (url.includes('/api/resource/video/save') && method === 'post') {
          mockResult = mockSaveVideoResource(requestData)
        } else if (url.match(/\/api\/resource\/video\/delete\/\w+/) && method === 'delete') {
          mockResult = mockDeleteVideo(url)
        } else if (url.includes('/api/resource/article/list') && method === 'get') {
          mockResult = mockGetArticleList(url)
        } else if (url.match(/\/api\/resource\/article\/\w+$/) && method === 'get') {
          mockResult = mockGetArticleDetail(url)
        } else if (url.includes('/api/resource/article/create') && method === 'post') {
          mockResult = mockCreateArticle(requestData)
        } else if (url.match(/\/api\/resource\/article\/update\/\w+/) && method === 'put') {
          mockResult = mockUpdateArticle(url, requestData)
        } else if (url.match(/\/api\/resource\/article\/delete\/\w+/) && method === 'delete') {
          mockResult = mockDeleteArticle(url)
        } else if (url.match(/\/api\/resource\/article\/tag\/\w+/) && method === 'get') {
          mockResult = mockGetArticlesByTag(url)
        }
        
        // 资源接口 - 标签相关
        else if (url.includes('/api/resource/tags') && method === 'get') {
          mockResult = mockGetResourceTags()
        } else if (url.includes('/api/resource/tag/add') && method === 'post') {
          mockResult = mockAddResourceTag(requestData)
        }
        
        // 考试模块接口
        else if (url.includes('/exam/question/list') && method === 'get') {
          mockResult = mockGetQuestionList(url)
        } else if (url.match(/\/exam\/question\/detail\/\w+/) && method === 'get') {
          mockResult = mockGetQuestionDetail(url)
        } else if (url.includes('/exam/question/create') && method === 'post') {
          mockResult = mockCreateQuestion(requestData)
        } else if (url.match(/\/exam\/question\/update\/\w+/) && method === 'put') {
          mockResult = mockUpdateQuestion(url, requestData)
        } else if (url.match(/\/exam\/question\/delete\/\w+/) && method === 'delete') {
          mockResult = mockDeleteQuestion(url)
        } else if (url.includes('/exam/paper/list') && method === 'get') {
          mockResult = mockGetPaperList(url)
        } else if (url.match(/\/exam\/paper\/detail\/\w+/) && method === 'get') {
          mockResult = mockGetPaperDetail(url)
        } else if (url.includes('/exam/list') && method === 'get') {
          mockResult = mockGetExamList(url)
        } else if (url.includes('/exam/record/list') && method === 'get') {
          mockResult = mockGetExamRecordList(url)
        } else if (url.includes('/exam/statistics') && method === 'get') {
          mockResult = mockGetExamStatistics(url)
        }
        
        // 如果路径包含'/api/'，移除这个前缀再次尝试匹配
        if (url.includes('/api/') && !mockResult) {
          const newUrl = url.replace('/api/', '/')
          console.log('尝试匹配不带/api前缀的路径:', newUrl)
          
          if (enableUserMock && newUrl.includes('/user/login') && method === 'post') {
            mockResult = mockLogin(requestData.username, requestData.password)
          }
          // 添加资源接口的匹配
          else if (newUrl.includes('/resource/file/list') && method === 'get') {
            mockResult = mockGetFileList(newUrl)
          } else if (newUrl.includes('/resource/file/upload') && method === 'post') {
            mockResult = mockUploadFile(requestData)
          } else if (newUrl.includes('/resource/file/save') && method === 'post') {
            mockResult = mockSaveFileResource(requestData)
          } else if (newUrl.match(/\/resource\/file\/delete\/\w+/) && method === 'delete') {
            mockResult = mockDeleteFile(newUrl)
          } else if (newUrl.includes('/resource/video/list') && method === 'get') {
            mockResult = mockGetVideoList(newUrl)
          } else if (newUrl.includes('/resource/video/upload') && method === 'post') {
            mockResult = mockUploadVideo(requestData)
          } else if (newUrl.includes('/resource/video/save') && method === 'post') {
            mockResult = mockSaveVideoResource(requestData)
          } else if (newUrl.match(/\/resource\/video\/delete\/\w+/) && method === 'delete') {
            mockResult = mockDeleteVideo(newUrl)
          } else if (newUrl.includes('/resource/article/list') && method === 'get') {
            mockResult = mockGetArticleList(newUrl)
          } else if (newUrl.match(/\/resource\/article\/detail\/\w+/) && method === 'get') {
            mockResult = mockGetArticleDetail(newUrl)
          } else if (newUrl.includes('/resource/article/create') && method === 'post') {
            mockResult = mockCreateArticle(requestData)
          } else if (newUrl.match(/\/resource\/article\/update\/\w+/) && method === 'put') {
            mockResult = mockUpdateArticle(newUrl, requestData)
          } else if (newUrl.match(/\/resource\/article\/delete\/\w+/) && method === 'delete') {
            mockResult = mockDeleteArticle(newUrl)
          }
          // 同理可以添加其他接口的匹配
        }

        // 如果找到匹配的模拟接口
        if (mockResult) {
          console.log('返回模拟数据:', mockResult)
          
          // 构建模拟响应对象
          return Promise.resolve({
            data: mockResult,
            status: 200,
            statusText: 'OK',
            headers: {},
            config: error.config,
          })
        }

        // 如果没有匹配的模拟接口，继续抛出错误
        console.log('未找到匹配的模拟接口, 继续抛出错误')
        return Promise.reject(error)
      } catch (err) {
        console.error('执行模拟接口出错:', err)
        return Promise.reject(error)
      }
    }
  )

  // 清除拦截器的方法
  return () => {
    axios.interceptors.response.eject(interceptor)
  }
}

// 初始化模拟数据
export const interceptorDispose = setupInterceptor()

export default {
  setupInterceptor
}

// 设置XHR模拟
// 创建API接口配置数组
const mockConfigs = [
  // 部门相关接口
  {
    url: '/api/department/tree',
    method: 'GET',
    response: async () => {
      await delay(200)
      return departmentMock.mockGetDepartmentTree()
    }
  },
  {
    url: '/api/department/list',
    method: 'GET',
    response: async () => {
      await delay(200)
      return departmentMock.mockGetDepartmentList()
    }
  },
  {
    url: /\/api\/department\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/department\/(\d+)/)[1])
      return departmentMock.mockGetDepartmentById(id)
    }
  },
  {
    url: '/api/department/add',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return departmentMock.mockAddDepartment(config.data)
    }
  },
  {
    url: '/api/department/update',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return departmentMock.mockUpdateDepartment(config.data)
    }
  },
  {
    url: /\/api\/department\/(\d+)$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(500)
      const id = parseInt(config.url.match(/\/department\/(\d+)/)[1])
      return departmentMock.mockDeleteDepartment(id)
    }
  },
  {
    url: '/api/department/sort',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return departmentMock.mockUpdateDepartmentSort(config.data)
    }
  },

  // 管理员相关接口
  {
    url: '/api/admin/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      return adminMock.mockGetAdminList({
        keyword: params.get('keyword') || undefined,
        departmentId: params.get('departmentId') ? parseInt(params.get('departmentId') as string) : undefined,
        status: params.get('status') ? parseInt(params.get('status') as string) : undefined,
        page: parseInt(params.get('page') || '1'),
        size: parseInt(params.get('size') || '20')
      })
    }
  },
  {
    url: /\/api\/admin\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/admin\/(\d+)/)[1])
      return adminMock.mockGetAdminById(id)
    }
  },
  {
    url: '/api/admin/add',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return adminMock.mockAddAdmin(config.data)
    }
  },
  {
    url: '/api/admin/update',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return adminMock.mockUpdateAdmin(config.data)
    }
  },
  {
    url: /\/api\/admin\/(\d+)$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(500)
      const id = parseInt(config.url.match(/\/admin\/(\d+)/)[1])
      return adminMock.mockDeleteAdmin(id)
    }
  },
  {
    url: '/api/admin/status',
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      return adminMock.mockUpdateAdminStatus(config.data.id, config.data.status)
    }
  },
  {
    url: '/api/admin/reset-password',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return adminMock.mockResetAdminPassword(config.data.id)
    }
  },
  {
    url: /\/api\/admin\/permissions\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/permissions\/(\d+)/)[1])
      return adminMock.mockGetAdminPermissions(id)
    }
  },
  {
    url: '/api/admin/permissions',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return adminMock.mockSetAdminPermissions(config.data.id, config.data.permissions)
    }
  },
  {
    url: '/api/admin/export',
    method: 'GET',
    response: async (config: any) => {
      await delay(1000)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      return adminMock.mockExportAdminList({
        keyword: params.get('keyword') || undefined,
        departmentId: params.get('departmentId') ? parseInt(params.get('departmentId') as string) : undefined,
        status: params.get('status') ? parseInt(params.get('status') as string) : undefined
      })
    }
  },

  // 轮播图相关接口
  {
    url: '/content/carousel/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      const mockHandler = carouselMockHandlers.find(h => h.url === '/content/carousel/list' && h.method === 'GET')
      return mockHandler?.handler({
        pageNum: parseInt(params.get('pageNum') || '1'),
        pageSize: parseInt(params.get('pageSize') || '10'),
        status: params.get('status') ? parseInt(params.get('status') as string) : undefined,
        keyword: params.get('keyword') || undefined
      }, null)
    }
  },
  {
    url: /\/content\/carousel\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/carousel\/(\d+)/)[1])
      const mockHandler = carouselMockHandlers.find(h => h.url === '/content/carousel/:id' && h.method === 'GET')
      return mockHandler?.handler({ id }, null)
    }
  },
  {
    url: '/content/carousel',
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      const mockHandler = carouselMockHandlers.find(h => h.url === '/content/carousel' && h.method === 'POST')
      return mockHandler?.handler(config.data, null)
    }
  },
  {
    url: /\/content\/carousel\/(\d+)$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/carousel\/(\d+)/)[1])
      const mockHandler = carouselMockHandlers.find(h => h.url === '/content/carousel/:id' && h.method === 'PUT')
      return mockHandler?.handler(config.data, { id })
    }
  },
  {
    url: /\/content\/carousel\/(\d+)$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/carousel\/(\d+)/)[1])
      const mockHandler = carouselMockHandlers.find(h => h.url === '/content/carousel/:id' && h.method === 'DELETE')
      return mockHandler?.handler(null, { id })
    }
  },
  {
    url: /\/content\/carousel\/(\d+)\/status$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/carousel\/(\d+)\/status/)[1])
      const mockHandler = carouselMockHandlers.find(h => h.url === '/content/carousel/:id/status' && h.method === 'PUT')
      return mockHandler?.handler(config.data, { id })
    }
  },

  // 新闻相关接口
  {
    url: '/content/news/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      const mockHandler = newsMockHandlers.find(h => h.url === '/content/news/list' && h.method === 'GET')
      return mockHandler?.handler({
        pageNum: parseInt(params.get('pageNum') || '1'),
        pageSize: parseInt(params.get('pageSize') || '10'),
        status: params.get('status') ? parseInt(params.get('status') as string) : undefined,
        isTop: params.get('isTop') ? params.get('isTop') === 'true' : undefined,
        keyword: params.get('keyword') || undefined
      }, null)
    }
  },
  {
    url: /\/content\/news\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/news\/(\d+)/)[1])
      const mockHandler = newsMockHandlers.find(h => h.url === '/content/news/:id' && h.method === 'GET')
      return mockHandler?.handler({ id }, null)
    }
  },
  {
    url: '/content/news',
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      const mockHandler = newsMockHandlers.find(h => h.url === '/content/news' && h.method === 'POST')
      return mockHandler?.handler(config.data, null)
    }
  },
  {
    url: /\/content\/news\/(\d+)$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/news\/(\d+)/)[1])
      const mockHandler = newsMockHandlers.find(h => h.url === '/content/news/:id' && h.method === 'PUT')
      return mockHandler?.handler(config.data, { id })
    }
  },
  {
    url: /\/content\/news\/(\d+)$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/news\/(\d+)/)[1])
      const mockHandler = newsMockHandlers.find(h => h.url === '/content/news/:id' && h.method === 'DELETE')
      return mockHandler?.handler(null, { id })
    }
  },
  {
    url: /\/content\/news\/(\d+)\/status$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/news\/(\d+)\/status/)[1])
      const mockHandler = newsMockHandlers.find(h => h.url === '/content/news/:id/status' && h.method === 'PUT')
      return mockHandler?.handler(config.data, { id })
    }
  },
  {
    url: /\/content\/news\/(\d+)\/top$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/news\/(\d+)\/top/)[1])
      const mockHandler = newsMockHandlers.find(h => h.url === '/content/news/:id/top' && h.method === 'PUT')
      return mockHandler?.handler(config.data, { id })
    }
  },

  // 公告相关接口
  {
    url: '/content/notice/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      const mockHandler = noticeMockHandlers.find(h => h.url === '/content/notice/list' && h.method === 'GET')
      return mockHandler?.handler({
        pageNum: parseInt(params.get('pageNum') || '1'),
        pageSize: parseInt(params.get('pageSize') || '10'),
        status: params.get('status') ? parseInt(params.get('status') as string) : undefined,
        isTop: params.get('isTop') ? params.get('isTop') === 'true' : undefined,
        importance: params.get('importance') ? parseInt(params.get('importance') as string) : undefined,
        keyword: params.get('keyword') || undefined
      }, null)
    }
  },
  {
    url: /\/content\/notice\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/notice\/(\d+)/)[1])
      const mockHandler = noticeMockHandlers.find(h => h.url === '/content/notice/:id' && h.method === 'GET')
      return mockHandler?.handler({ id }, null)
    }
  },
  {
    url: '/content/notice',
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      const mockHandler = noticeMockHandlers.find(h => h.url === '/content/notice' && h.method === 'POST')
      return mockHandler?.handler(config.data, null)
    }
  },
  {
    url: /\/content\/notice\/(\d+)$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/notice\/(\d+)/)[1])
      const mockHandler = noticeMockHandlers.find(h => h.url === '/content/notice/:id' && h.method === 'PUT')
      return mockHandler?.handler(config.data, { id })
    }
  },
  {
    url: /\/content\/notice\/(\d+)$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/notice\/(\d+)/)[1])
      const mockHandler = noticeMockHandlers.find(h => h.url === '/content/notice/:id' && h.method === 'DELETE')
      return mockHandler?.handler(null, { id })
    }
  },
  {
    url: /\/content\/notice\/(\d+)\/status$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/notice\/(\d+)\/status/)[1])
      const mockHandler = noticeMockHandlers.find(h => h.url === '/content/notice/:id/status' && h.method === 'PUT')
      return mockHandler?.handler(config.data, { id })
    }
  },
  {
    url: /\/content\/notice\/(\d+)\/top$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/notice\/(\d+)\/top/)[1])
      const mockHandler = noticeMockHandlers.find(h => h.url === '/content/notice/:id/top' && h.method === 'PUT')
      return mockHandler?.handler(config.data, { id })
    }
  },

  // 学员相关接口
  {
    url: '/api/student/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      return studentMock.mockGetStudentList({
        keyword: params.get('keyword') || undefined,
        departmentId: params.get('departmentId') ? parseInt(params.get('departmentId') as string) : undefined,
        status: params.get('status') || undefined,
        page: parseInt(params.get('page') || '1'),
        size: parseInt(params.get('size') || '20')
      })
    }
  },
  {
    url: /\/api\/student\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/student\/(\d+)/)[1])
      return studentMock.mockGetStudentById(id)
    }
  },
  {
    url: '/api/student/add',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return studentMock.mockAddStudent(config.data)
    }
  },
  {
    url: '/api/student/update',
    method: 'PUT',
    response: async (config: any) => {
      await delay(500)
      return studentMock.mockUpdateStudent(config.data)
    }
  },
  {
    url: /\/api\/student\/(\d+)$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(500)
      const id = parseInt(config.url.match(/\/student\/(\d+)/)[1])
      return studentMock.mockDeleteStudent(id)
    }
  },
  {
    url: /\/api\/student\/(\d+)\/reset-password$/,
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      const id = parseInt(config.url.match(/\/student\/(\d+)\/reset-password/)[1])
      return studentMock.mockResetStudentPassword(id)
    }
  },
  {
    url: /\/api\/student\/(\d+)\/status$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/student\/(\d+)\/status/)[1])
      return studentMock.mockUpdateStudentStatus(id, config.data.status)
    }
  },
  {
    url: '/api/student/export',
    method: 'GET',
    response: async (config: any) => {
      await delay(1000)
      return studentMock.mockExportStudentList()
    }
  },
  {
    url: '/api/student/import',
    method: 'POST',
    response: async (config: any) => {
      await delay(1500)
      return studentMock.mockImportStudents()
    }
  },

  // 论坛帖子相关接口
  {
    url: '/api/forum/post/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      return forumMock.mockGetPostList({
        keyword: params.get('keyword') || undefined,
        status: params.get('status') || undefined,
        categoryId: params.get('categoryId') ? parseInt(params.get('categoryId') as string) : undefined,
        page: parseInt(params.get('page') || '1'),
        size: parseInt(params.get('size') || '10')
      })
    }
  },
  {
    url: /\/api\/forum\/post\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/post\/(\d+)/)[1])
      return forumMock.mockGetPostById(id)
    }
  },
  {
    url: /\/api\/forum\/post\/(\d+)\/review$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/post\/(\d+)\/review/)[1])
      return forumMock.mockReviewPost(id, config.data.status, config.data.reason)
    }
  },
  {
    url: /\/api\/forum\/post\/(\d+)\/top$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/post\/(\d+)\/top/)[1])
      return forumMock.mockSetPostTop(id, config.data.isTop)
    }
  },
  {
    url: /\/api\/forum\/post\/(\d+)\/essence$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/post\/(\d+)\/essence/)[1])
      return forumMock.mockSetPostEssence(id, config.data.isEssence)
    }
  },
  {
    url: /\/api\/forum\/post\/(\d+)$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/post\/(\d+)/)[1])
      return forumMock.mockDeletePost(id)
    }
  },
  {
    url: '/api/forum/post/batch-review',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      // 模拟批量审核，实际只返回成功
      return mockResponse({ success: true })
    }
  },
  {
    url: '/api/forum/post/batch-delete',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      // 模拟批量删除，实际只返回成功
      return mockResponse({ success: true })
    }
  },
  {
    url: /\/api\/forum\/post\/(\d+)\/comments$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/post\/(\d+)\/comments/)[1])
      return forumMock.mockGetPostComments(id)
    }
  },

  // 论坛评论相关接口
  {
    url: '/api/forum/comment/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      return forumMock.mockGetCommentList({
        keyword: params.get('keyword') || undefined,
        status: params.get('status') || undefined,
        postId: params.get('postId') ? parseInt(params.get('postId') as string) : undefined,
        page: parseInt(params.get('page') || '1'),
        size: parseInt(params.get('size') || '10')
      })
    }
  },
  {
    url: /\/api\/forum\/comment\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/comment\/(\d+)/)[1])
      return forumMock.mockGetCommentById(id)
    }
  },
  {
    url: /\/api\/forum\/comment\/(\d+)\/review$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/comment\/(\d+)\/review/)[1])
      return forumMock.mockReviewComment(id, config.data.status, config.data.reason)
    }
  },
  {
    url: /\/api\/forum\/comment\/(\d+)$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/comment\/(\d+)/)[1])
      return forumMock.mockDeleteComment(id)
    }
  },
  {
    url: '/api/forum/comment/batch-review',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      // 模拟批量审核，实际只返回成功
      return mockResponse({ success: true })
    }
  },
  {
    url: '/api/forum/comment/batch-delete',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      // 模拟批量删除，实际只返回成功
      return mockResponse({ success: true })
    }
  },

  // 论坛分类相关接口
  {
    url: '/api/forum/category/list',
    method: 'GET',
    response: async () => {
      await delay(200)
      return forumMock.mockGetCategoryList()
    }
  },

  // 论坛违规内容相关接口
  {
    url: '/api/forum/violation/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      return forumMock.mockGetViolationList({
        contentType: params.get('contentType') as 'post' | 'comment' || undefined,
        status: params.get('status') || undefined,
        page: parseInt(params.get('page') || '1'),
        size: parseInt(params.get('size') || '10')
      })
    }
  },
  {
    url: /\/api\/forum\/violation\/(\d+)$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const id = parseInt(config.url.match(/\/violation\/(\d+)/)[1])
      return forumMock.mockGetViolationById(id)
    }
  },
  {
    url: /\/api\/forum\/violation\/(\d+)\/process$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      const id = parseInt(config.url.match(/\/violation\/(\d+)\/process/)[1])
      return forumMock.mockProcessViolation(id, config.data.status, config.data.result)
    }
  },
  {
    url: '/api/forum/violation/batch-process',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      // 模拟批量处理，实际只返回成功
      return mockResponse({ success: true })
    }
  }
]

// 添加资源相关的模拟接口配置
const resourceMockConfigs = [
  {
    url: '/api/resource/file/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetFileList(config.url)
    }
  },
  {
    url: '/api/resource/file/upload',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockUploadFile(config.data)
    }
  },
  {
    url: '/api/resource/file/save',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockSaveFileResource(config.data)
    }
  },
  {
    url: /\/api\/resource\/file\/delete\/\w+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      return mockDeleteFile(config.url)
    }
  },
  {
    url: '/api/resource/video/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetVideoList(config.url)
    }
  },
  {
    url: '/api/resource/video/upload',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockUploadVideo(config.data)
    }
  },
  {
    url: '/api/resource/video/save',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockSaveVideoResource(config.data)
    }
  },
  {
    url: /\/api\/resource\/video\/delete\/\w+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      return mockDeleteVideo(config.url)
    }
  },
  {
    url: '/api/resource/article/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetArticleList(config.url)
    }
  },
  {
    url: /\/api\/resource\/article\/detail\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetArticleDetail(config.url)
    }
  },
  {
    url: '/api/resource/article/create',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockCreateArticle(config.data)
    }
  },
  {
    url: /\/api\/resource\/article\/update\/\w+$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(500)
      return mockUpdateArticle(config.url, config.data)
    }
  },
  {
    url: /\/api\/resource\/article\/delete\/\w+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      return mockDeleteArticle(config.url)
    }
  },
  {
    url: /\/api\/resource\/file\/tag\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetFilesByTag(config.url)
    }
  },
  {
    url: /\/api\/resource\/video\/tag\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetVideosByTag(config.url)
    }
  },
  {
    url: /\/api\/resource\/article\/tag\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetArticlesByTag(config.url)
    }
  },
  {
    url: '/api/resource/tags',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetResourceTags()
    }
  },
  {
    url: '/api/resource/tag/add',
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      return mockAddResourceTag(config.data)
    }
  }
]

// 添加课程相关的模拟接口配置
const courseMockConfigs = [
  {
    url: '/api/course/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetCourseList(config.url)
    }
  },
  {
    url: /\/api\/course\/detail\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetCourseDetail(config.url)
    }
  },
  {
    url: '/api/course/create',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockCreateCourse(config.data)
    }
  },
  {
    url: /\/api\/course\/update\/\w+$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(500)
      return mockUpdateCourse(config.url, config.data)
    }
  },
  {
    url: /\/api\/course\/delete\/\w+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(500)
      return mockDeleteCourse(config.url)
    }
  },
  {
    url: /\/api\/course\/publish\/\w+$/,
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockPublishCourse(config.url, config.data)
    }
  },
  {
    url: '/api/course/study-record/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetStudyRecordList(config.url)
    }
  },
  {
    url: /\/api\/course\/study-record\/detail\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetStudyRecordDetail(config.url)
    }
  },
  {
    url: '/api/course/statistics',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetCourseStatistics()
    }
  },
  {
    url: /\/api\/course\/study-statistics\/\w+/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetUserStudyStatistics(config.url)
    }
  },
]

// 合并所有模拟接口配置
const allMockConfigs = [...mockConfigs, ...resourceMockConfigs, ...courseMockConfigs]

// 添加考试相关的模拟接口配置
const examMockConfigs = [
  // 题库管理
  {
    url: '/api/exam/banks',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetBankList(config.url)
    }
  },
  {
    url: /\/api\/exam\/banks\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetBankDetail(config.url)
    }
  },
  {
    url: '/api/exam/banks',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockCreateBank(config.data)
    }
  },
  {
    url: /\/api\/exam\/banks\/\w+$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(500)
      return mockUpdateBank(config.url, config.data)
    }
  },
  {
    url: /\/api\/exam\/banks\/\w+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      return mockDeleteBank(config.url)
    }
  },
  // 题目管理
  {
    url: '/api/exam/question/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetQuestionList(config.url)
    }
  },
  {
    url: /\/api\/exam\/question\/detail\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetQuestionDetail(config.url)
    }
  },
  {
    url: '/api/exam/question/create',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockCreateQuestion(config.data)
    }
  },
  {
    url: /\/api\/exam\/question\/update\/\w+$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(500)
      return mockUpdateQuestion(config.url, config.data)
    }
  },
  {
    url: /\/api\/exam\/question\/delete\/\w+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      return mockDeleteQuestion(config.url)
    }
  },
  // 试卷管理
  {
    url: '/api/exam/paper/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetPaperList(config.url)
    }
  },
  {
    url: /\/api\/exam\/paper\/detail\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetPaperDetail(config.url)
    }
  },
  // 考试管理
  {
    url: '/api/exam/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetExamList(config.url)
    }
  },
  // 考试记录
  {
    url: '/api/exam/record/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetExamRecordList(config.url)
    }
  },
  // 考试统计
  {
    url: '/api/exam/statistics',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetExamStatistics(config.url)
    }
  }
]

// 添加积分规则相关的模拟接口配置
const pointsRuleMockConfigs = [
  // 积分规则统计（更具体的路径，放在前面）
  {
    url: '/api/points/rules/statistics',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetPointsRuleStatistics()
    }
  },
  // 积分规则导出
  {
    url: '/api/points/rules/export',
    method: 'GET',
    response: async (config: any) => {
      await delay(300)
      return mockExportPointsRules()
    }
  },
  // 批量删除积分规则
  {
    url: '/api/points/rules/batch-delete',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockBatchDeletePointsRules(config.data)
    }
  },
  // 积分规则列表（通用路径，放在后面）
  {
    url: '/api/points/rules',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetPointsRuleList(config.url)
    }
  },
  // 创建积分规则
  {
    url: '/api/points/rules',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockCreatePointsRule(config.data)
    }
  },
  // 积分规则详情
  {
    url: /\/api\/points\/rules\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetPointsRuleDetail(config.url)
    }
  },
  // 更新积分规则
  {
    url: /\/api\/points\/rules\/\w+$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(500)
      return mockUpdatePointsRule(config.url, config.data)
    }
  },
  // 删除积分规则
  {
    url: /\/api\/points\/rules\/\w+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      return mockDeletePointsRule(config.url)
    }
  },
  // 切换积分规则状态
  {
    url: /\/api\/points\/rules\/\w+\/status$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      return mockTogglePointsRuleStatus(config.url, config.data)
    }
  }
]

// 添加积分记录相关的模拟接口配置
const pointsRecordMockConfigs = [
  // 积分记录列表
  {
    url: '/api/points/records',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetPointsRecordList(config.url)
    }
  },
  // 用户积分余额
  {
    url: /\/api\/points\/balance\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetUserPointsBalance(config.url)
    }
  },
  // 积分调整
  {
    url: '/api/points/adjust',
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      return mockAdjustUserPoints(config.data)
    }
  },
  // 批量积分调整
  {
    url: '/api/points/batch-adjust',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockBatchAdjustPoints(config.data)
    }
  },
  // 积分统计
  {
    url: '/api/points/statistics',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetPointsStatistics()
    }
  },
  // 用户积分趋势
  {
    url: /\/api\/points\/trend\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      const params = new URLSearchParams(config.url.split('?')[1] || '')
      return mockGetUserPointsTrend(config.url, { period: params.get('period') || 'month' })
    }
  },
  // 导出积分记录
  {
    url: '/api/points/export',
    method: 'GET',
    response: async (config: any) => {
      await delay(1000)
      return mockExportPointsRecords()
    }
  }
]

// 添加商品相关的模拟接口配置
const productMockConfigs = [
  // 商品列表
  {
    url: '/api/product/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetProductList(config.url)
    }
  },
  // 商品详情
  {
    url: /\/api\/product\/detail\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetProductDetail(config.url)
    }
  },
  // 创建商品
  {
    url: '/api/product/create',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockCreateProduct(config.data)
    }
  },
  // 更新商品
  {
    url: /\/api\/product\/update\/\w+$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(500)
      return mockUpdateProduct(config.url, config.data)
    }
  },
  // 删除商品
  {
    url: /\/api\/product\/delete\/\w+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      return mockDeleteProduct(config.url)
    }
  },
  // 商品状态变更
  {
    url: /\/api\/product\/status\/\w+$/,
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      return mockChangeProductStatus(config.url, config.data)
    }
  },
  // 更新商品库存
  {
    url: /\/api\/product\/stock\/\w+$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      return mockUpdateProductStock(config.url, config.data)
    }
  },
  // 商品分类列表
  {
    url: '/api/product/categories',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetProductCategories()
    }
  },
  // 创建商品分类
  {
    url: '/api/product/category/create',
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      return mockCreateProductCategory(config.data)
    }
  },
  // 更新商品分类
  {
    url: /\/api\/product\/category\/update\/\w+$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      return mockUpdateProductCategory(config.url, config.data)
    }
  },
  // 删除商品分类
  {
    url: /\/api\/product\/category\/delete\/\w+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      return mockDeleteProductCategory(config.url)
    }
  },
  // 批量更新商品状态
  {
    url: '/api/product/batch-status',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockBatchUpdateProductStatus(config.data)
    }
  },
  // 商品统计
  {
    url: '/api/product/statistics',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetProductStatistics()
    }
  }
]

// 添加兑换相关的模拟接口配置
const exchangeMockConfigs = [
  // 兑换记录列表
  {
    url: '/api/exchange/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetExchangeList(config.url)
    }
  },
  // 兑换记录详情
  {
    url: /\/api\/exchange\/detail\/\w+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetExchangeDetail(config.url)
    }
  },
  // 审核兑换申请
  {
    url: /\/api\/exchange\/review\/\w+$/,
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      return mockReviewExchange(config.url, config.data)
    }
  },
  // 更新物流信息
  {
    url: /\/api\/exchange\/express\/\w+$/,
    method: 'PUT',
    response: async (config: any) => {
      await delay(300)
      return mockUpdateExchangeExpress(config.url, config.data)
    }
  },
  // 确认收货
  {
    url: /\/api\/exchange\/confirm-delivery\/\w+$/,
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      return mockConfirmExchangeDelivery(config.url)
    }
  },
  // 取消兑换
  {
    url: /\/api\/exchange\/cancel\/\w+$/,
    method: 'POST',
    response: async (config: any) => {
      await delay(300)
      return mockCancelExchange(config.url, config.data)
    }
  },
  // 批量更新兑换状态
  {
    url: '/api/exchange/batch-status',
    method: 'POST',
    response: async (config: any) => {
      await delay(500)
      return mockBatchUpdateExchangeStatus(config.data)
    }
  },
  // 兑换统计
  {
    url: '/api/exchange/statistics',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetExchangeStatistics()
    }
  },
  // 导出兑换记录
  {
    url: '/api/exchange/export',
    method: 'GET',
    response: async (config: any) => {
      await delay(1000)
      return mockExportExchanges()
    }
  }
]

// 学习记录相关Mock配置
const studyMockConfigs = [
  // 获取学习记录列表
  {
    url: '/api/study/records/list',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetStudyRecordListNew(config.url)
    }
  },
  // 获取部门统计
  {
    url: '/api/study/records/department-statistics',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetDepartmentStatistics()
    }
  },
  // 获取活跃学员
  {
    url: '/api/study/records/active-students',
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetActiveStudents(config.url)
    }
  },
  // 获取用户学习记录
  {
    url: /\/api\/study\/records\/user\/\d+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetUserStudyRecords(config.url)
    }
  },
  // 获取课程学习记录
  {
    url: /\/api\/study\/records\/course\/\d+$/,
    method: 'GET',
    response: async (config: any) => {
      await delay(200)
      return mockGetCourseStudyRecords(config.url)
    }
  },
  // 删除学习记录
  {
    url: /\/api\/study\/records\/\d+$/,
    method: 'DELETE',
    response: async (config: any) => {
      await delay(300)
      return mockDeleteStudyRecord(config.url)
    }
  },
  // 导出学习记录
  {
    url: '/api/study/records/export',
    method: 'GET',
    response: async (config: any) => {
      await delay(1000)
      return mockExportStudyRecords()
    }
  }
]

// 重新合并所有配置
const finalMockConfigs = [...mockConfigs, ...resourceMockConfigs, ...courseMockConfigs, ...examMockConfigs, ...pointsRuleMockConfigs, ...pointsRecordMockConfigs, ...productMockConfigs, ...exchangeMockConfigs, ...studyMockConfigs]

// 设置XHR模拟
// export const mockDispose = setupMock(finalMockConfigs) 