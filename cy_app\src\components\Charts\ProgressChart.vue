<template>
  <view class="progress-chart-container">
    <view class="progress-header">
      <text class="progress-title">{{ title }}</text>
      <text class="progress-value">{{ value }}%</text>
    </view>
    <view class="progress-bar-container">
      <view class="progress-bar">
        <view
            :style="{
            width: `${value}%`,
            background: `linear-gradient(90deg, ${color} 0%, ${colorLight} 100%)`
          }"
            class="progress-fill"
        ></view>
      </view>
    </view>
    <view class="progress-labels">
      <text class="progress-label">{{ label }}</text>
      <text class="progress-count">{{ count }}人</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {progressColors} from './chartConfig'

interface Props {
  title: string
  value: number
  label: string
  count: number
  color: string
  colorLight: string
}

defineProps<Props>()
</script>

<style lang="scss" scoped>
.progress-chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a1d2e;
}

.progress-value {
  font-size: 16px;
  font-weight: 700;
  color: #667eea;
}

.progress-bar-container {
  margin-bottom: 8px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  font-size: 12px;
  color: #8e8e93;
}

.progress-count {
  font-size: 12px;
  font-weight: 600;
  color: #1a1d2e;
}
</style>
