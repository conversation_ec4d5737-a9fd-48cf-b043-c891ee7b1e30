<template>
	<view id="app">
		<router-view />
	</view>
</template>

<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { checkAuth, redirectToLogin } from '@/utils/auth'

onLaunch(async () => {
	console.log('App Launch')
	// 检查登录状态
	const isLoggedIn = await checkAuth()
	if (!isLoggedIn) {
		// 未登录，跳转到登录页
		redirectToLogin()
	}
})

onShow(() => {
	console.log('App Show')
})

onHide(() => {
	console.log('App Hide')
})
</script>

<style lang="scss">
@import "uview-plus/index.scss";
@import './styles/global.scss';

#app {
	width: 100%;
	height: 100%;
}
</style>
