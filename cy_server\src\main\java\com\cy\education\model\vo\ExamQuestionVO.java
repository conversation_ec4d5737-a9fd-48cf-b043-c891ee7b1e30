package com.cy.education.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 题目视图对象
 */
@Data
public class ExamQuestionVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    private Integer id;

    /**
     * 所属题库ID
     */
    private Integer bankId;

    /**
     * 所属题库名称
     */
    private String bankName;

    /**
     * 题目标题
     */
    private String title;

    /**
     * 题目类型(single-单选题,multiple-多选题,judgment-判断题,fill-填空题,essay-简答题)
     */
    private String type;

    /**
     * 选项(单选、多选题使用)
     */
    private Object options;

    /**
     * 正确答案
     */
    private String correctAnswer;

    /**
     * 题目解析
     */
    private String explanation;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人ID
     */
    private String createdBy;
} 