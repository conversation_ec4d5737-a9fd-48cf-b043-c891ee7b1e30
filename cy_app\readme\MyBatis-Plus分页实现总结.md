# MyBatis-Plus分页实现总结

## 概述

本次优化将论坛模块的用户相关分页功能从手动分页改为使用MyBatis-Plus的标准分页方式，与项目中其他模块（如NewsService）保持一致。

## 核心实现

### 1. 分页插件配置

项目已配置MyBatis-Plus分页插件：

```java
@Configuration
public class MybatisPlusConfig {
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        
        return interceptor;
    }
}
```

### 2. Mapper层实现

#### 2.1 方法签名修改

将返回类型从`List`改为`IPage`，添加分页参数：

```java
// 修改前
List<Map<String, Object>> getUserPosts(@Param("userId") Integer userId);

// 修改后
IPage<Map<String, Object>> getUserPosts(IPage<Map<String, Object>> page, @Param("userId") Integer userId);
```

#### 2.2 SQL查询

移除手动分页参数，让MyBatis-Plus自动处理：

```java
@Select("SELECT p.*, c.name as category_name FROM forum_posts p " +
        "LEFT JOIN forum_categories c ON p.category_id = c.id " +
        "WHERE p.author_id = #{userId} AND p.status != 3 " +
        "ORDER BY p.created_at DESC")
IPage<Map<String, Object>> getUserPosts(IPage<Map<String, Object>> page, @Param("userId") Integer userId);
```

### 3. Service层实现

#### 3.1 标准分页模式

使用与NewsService相同的分页模式：

```java
@Override
public PageResponse<Map<String, Object>> getUserPosts(Integer userId, Integer pageNum, Integer pageSize) {
    try {
        // 1. 创建分页对象
        Page<Map<String, Object>> page = new Page<>(pageNum, pageSize);

        // 2. 使用MyBatis-Plus分页查询
        IPage<Map<String, Object>> resultPage = studentMapper.getUserPosts(page, userId);

        // 3. 转换为自定义分页响应对象
        return PageResponse.of(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    } catch (Exception e) {
        log.error("获取用户帖子失败: userId={}, pageNum={}, pageSize={}", userId, pageNum, pageSize, e);
        throw new BusinessException("获取用户帖子失败: " + e.getMessage());
    }
}
```

### 4. 与NewsService对比

#### 4.1 NewsService实现（标准实体查询）

```java
@Override
public PageResponse<News> listNews(ContentQueryParam param) {
    // 构建查询条件
    LambdaQueryWrapper<News> queryWrapper = new LambdaQueryWrapper<>();
    
    // 分页查询
    Page<News> page = new Page<>(param.getPageNum(), param.getPageSize());
    Page<News> resultPage = newsMapper.selectPage(page, queryWrapper);

    // 转换为自定义分页响应对象
    return PageResponse.of(
            resultPage.getRecords(),
            resultPage.getTotal(),
            resultPage.getCurrent(),
            resultPage.getSize()
    );
}
```

#### 4.2 我们的实现（自定义SQL查询）

```java
@Override
public PageResponse<Map<String, Object>> getUserPosts(Integer userId, Integer pageNum, Integer pageSize) {
    // 创建分页对象
    Page<Map<String, Object>> page = new Page<>(pageNum, pageSize);
    
    // 使用自定义SQL进行分页查询
    IPage<Map<String, Object>> resultPage = studentMapper.getUserPosts(page, userId);
    
    // 转换为分页响应对象
    return PageResponse.of(
            resultPage.getRecords(),
            resultPage.getTotal(),
            resultPage.getCurrent(),
            resultPage.getSize()
    );
}
```

## 关键优势

### 1. 自动分页处理

- MyBatis-Plus分页插件自动在SQL中添加`LIMIT`和`OFFSET`
- 自动计算总数查询
- 无需手动处理分页逻辑

### 2. 性能优化

- 分页插件会优化SQL执行
- 避免全表扫描
- 自动处理不同数据库的分页语法

### 3. 代码一致性

- 与项目中其他模块使用相同的分页模式
- 统一的API接口格式
- 易于维护和扩展

### 4. 错误处理

- 自动处理分页参数验证
- 防止全表更新和删除
- 更好的异常处理

## 实现细节

### 1. 分页参数处理

```java
// 创建分页对象
Page<Map<String, Object>> page = new Page<>(pageNum, pageSize);
```

### 2. 分页查询执行

```java
// MyBatis-Plus自动处理分页SQL
IPage<Map<String, Object>> resultPage = studentMapper.getUserPosts(page, userId);
```

### 3. 结果转换

```java
// 使用PageResponse.of()方法构建统一响应格式
return PageResponse.of(
        resultPage.getRecords(),    // 当前页数据
        resultPage.getTotal(),      // 总记录数
        resultPage.getCurrent(),    // 当前页码
        resultPage.getSize()        // 每页大小
);
```

## 注意事项

1. **分页插件配置**：确保MyBatis-Plus分页插件已正确配置
2. **数据库类型**：分页插件会根据数据库类型自动选择合适的分页语法
3. **复杂查询支持**：即使是多表关联查询，分页插件也能正确处理
4. **性能考虑**：对于大数据量查询，建议添加适当的索引

## 总结

通过使用MyBatis-Plus的标准分页方式，我们实现了：

1. **代码简化**：移除了手动分页逻辑，代码更简洁
2. **性能提升**：利用MyBatis-Plus的分页优化
3. **一致性**：与项目中其他模块保持相同的分页模式
4. **可维护性**：统一的分页处理方式，易于维护和扩展

这种实现方式完全符合MyBatis-Plus的最佳实践，与项目中NewsService等模块保持一致，提高了代码质量和可维护性。 
