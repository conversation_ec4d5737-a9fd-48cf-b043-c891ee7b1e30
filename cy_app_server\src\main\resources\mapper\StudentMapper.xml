<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.StudentMapper">

    <!-- 根据ID查询学员详情，并关联部门信息 -->
    <select id="selectStudentById" resultType="com.cy.education.model.entity.Student">
        SELECT s.*, d.name AS department_name
        FROM students s
        LEFT JOIN departments d ON s.department_id = d.id
        WHERE s.id = #{id}
    </select>

</mapper>
