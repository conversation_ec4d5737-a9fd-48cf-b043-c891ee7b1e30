import { get, post, put, del } from '@/utils/request'

/**
 * 题库接口数据类型
 */
export interface Bank {
  id: string
  name: string
  description: string
  scope: string
  createdAt: string
  createdBy: string
  updatedAt: string
  questionCount?: number
}

/**
 * 题目类型枚举
 */
export type QuestionType = 'single' | 'multiple' | 'judgment' | 'fill' | 'essay'

/**
 * 题目接口数据类型
 */
export interface Question {
  id: string
  bankId: string
  bankName: string
  title: string
  type: QuestionType
  options: string // JSON字符串
  correctAnswer: string
  explanation: string
  createdAt: string
  createdBy: string
  updatedAt: string
}

/**
 * 试卷接口数据类型
 */
export interface Paper {
  id: string
  title: string
  description: string
  totalScore: number
  passingScore: number
  duration: number
  isPublished: boolean
  createdAt: string
  createdBy: string
  updatedAt: string
  questions?: PaperQuestion[]
}

/**
 * 试卷题目关联接口数据类型
 */
export interface PaperQuestion {
  id: string
  paperId: string
  questionId: string
  score: number
  questionOrder: number
  question?: Question
}

/**
 * 考试接口数据类型
 */
export interface Exam {
  id: string
  title: string
  description: string
  paperId: string
  paper?: Paper
  startTime: string
  endTime: string
  duration: number
  passingScore: number
  departmentIds: string[] // 参考部门
  departments?: string[] // 部门名称
  maxAttempts: number // 最大考试次数
  isPublished: boolean
  status: number // 0-草稿,1-未开始,2-进行中,3-已结束
  participantCount: number // 后端统计：参与人数
  completedCount: number // 后端统计：完成人数
  passedCount: number // 后端统计：通过人数
  createdAt: string
  updatedAt: string
  createdBy: string
}

/**
 * 考试记录接口数据类型
 */
export interface ExamRecord {
  id: string
  examId: string
  examTitle: string
  userId: string
  userName: string
  departmentId: string
  departmentName: string
  score: number
  totalScore: number
  passed: boolean
  startTime: string
  endTime?: string
  duration: number // 实际考试时长
  answers: ExamAnswer[]
  status: 'not_started' | 'in_progress' | 'completed' | 'timeout'
  attemptNumber: number // 考试次数
  createdAt: string
  updatedAt: string
}

/**
 * 考试答案接口数据类型
 */
export interface ExamAnswer {
  id: string
  recordId: string
  questionId: string
  answer: string
  isCorrect: boolean
  score: number
  comment?: string
}

/**
 * 查询参数接口
 */
export interface QueryParams {
  page?: number
  limit?: number
  keyword?: string
  bankId?: string // 题库ID筛选
  type?: string
  departmentId?: string
  status?: string
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

/**
 * 考试记录查询参数
 */
export interface ExamRecordQueryParams {
  page?: number
  limit?: number
  userId?: string
  examId?: string
  status?: string
  startDate?: string
  endDate?: string
}

// ==================== 题库相关接口 ====================

/**
 * 获取题库列表
 */
export function getBankList(params: any) {
  return get<{ list: Bank[], total: number }>('/exam/bank/list', params)
}

/**
 * 获取题库详情
 */
export function getBankDetail(id: string) {
  return get<Bank>(`/exam/bank/detail/${id}`)
}

/**
 * 创建题库
 */
export function createBank(data: any) {
  return post<{ id: string }>('/exam/bank/create', data)
}

/**
 * 更新题库
 */
export function updateBank(id: string, data: any) {
  return put<void>(`/exam/bank/update/${id}`, data)
}

/**
 * 删除题库
 */
export function deleteBank(id: string) {
  return del<void>(`/exam/bank/delete/${id}`)
}

// ==================== 题目相关接口 ====================

/**
 * 获取题目列表
 */
export function getQuestionList(params: any) {
  return get<{ list: Question[], total: number }>('/exam/question/list', params)
}

/**
 * 获取题目详情
 */
export function getQuestionDetail(id: string) {
  return get<Question>(`/exam/question/detail/${id}`)
}

/**
 * 创建题目
 */
export function createQuestion(data: any) {
  return post<{ id: string }>('/exam/question/create', data)
}

/**
 * 更新题目
 */
export function updateQuestion(id: string, data: any) {
  return put<void>(`/exam/question/update/${id}`, data)
}

/**
 * 删除题目
 */
export function deleteQuestion(id: string) {
  return del<void>(`/exam/question/delete/${id}`)
}

/**
 * 批量删除题目
 */
export function batchDeleteQuestions(ids: string[]) {
  return post<void>('/exam/question/batch-delete', ids)
}

// ==================== 试卷相关接口 ====================

/**
 * 获取试卷列表
 */
export function getPaperList(params: any) {
  return get<{ list: Paper[], total: number }>('/exam/paper/list', params)
}

/**
 * 获取试卷详情
 */
export function getPaperDetail(id: string) {
  return get<Paper>(`/exam/paper/detail/${id}`)
}

/**
 * 创建试卷
 */
export function createPaper(data: any) {
  return post<{ id: string }>('/exam/paper/create', data)
}

/**
 * 更新试卷
 */
export function updatePaper(id: string, data: any) {
  return put<void>(`/exam/paper/update/${id}`, data)
}

/**
 * 删除试卷
 */
export function deletePaper(id: string) {
  return del<void>(`/exam/paper/delete/${id}`)
}

/**
 * 发布/取消发布试卷
 */
export function publishPaper(id: string, isPublished: boolean) {
  return post<void>(`/exam/paper/publish/${id}`, { isPublished })
}

/**
 * 添加题目到试卷
 */
export function addQuestionsToPaper(paperId: string, questions: PaperQuestion[]) {
  return post<void>(`/exam/paper/add-questions/${paperId}`, { questions })
}

/**
 * 从试卷移除题目
 */
export function removeQuestionFromPaper(paperId: string, questionId: string) {
  return del<void>(`/exam/paper/remove-question/${paperId}/${questionId}`)
}

/**
 * 更新试卷题目列表
 */
export function updatePaperQuestions(paperId: string, questions: PaperQuestion[]) {
  return post<void>(`/exam/paper/update-questions/${paperId}`, { questions })
}

// ==================== 考试相关接口 ====================

/**
 * 获取考试列表
 */
export function getExamList(params: any) {
  return get<{ list: Exam[], total: number }>('/exam/list', params)
}

/**
 * 获取考试详情
 */
export function getExamDetail(id: string) {
  return get<Exam>(`/exam/detail/${id}`)
}

/**
 * 创建考试
 */
export function createExam(data: any) {
  return post<{ id: string }>('/exam/create', data)
}

/**
 * 更新考试
 */
export function updateExam(id: string, data: any) {
  return put<void>(`/exam/update/${id}`, data)
}

/**
 * 删除考试
 */
export function deleteExam(id: string) {
  return del<void>(`/exam/delete/${id}`)
}

/**
 * 发布/取消发布考试
 */
export function publishExam(id: string, isPublished: boolean) {
  return post<void>(`/exam/publish/${id}`, { isPublished })
}

/**
 * 更新考试状态
 */
export function updateExamStatus(id: string, status: number) {
  return post<void>(`/exam/update-status/${id}`, { status })
}

// ==================== 考试记录相关接口 ====================

/**
 * 获取考试记录列表
 */
export function getExamRecordList(params: ExamRecordQueryParams) {
  return get<{ list: ExamRecord[], total: number }>('/exam-record/list', params)
}

/**
 * 获取考试记录详情
 */
export function getExamRecordDetail(id: string) {
  return get<ExamRecord>(`/exam-record/detail/${id}`)
}

/**
 * 开始考试
 */
export function startExam(examId: string) {
  return post<{ recordId: string }>(`/exam-record/start/${examId}`)
}

/**
 * 提交考试
 */
export function submitExam(recordId: string, answers: ExamAnswer[]) {
  return post<{ score: number, isPassed: boolean }>(`/exam-record/submit/${recordId}`, { answers })
}

/**
 * 获取考试统计数据
 */
export function getExamStatistics(examId?: string) {
  return get<{
    totalExams: number
    totalParticipants: number
    averageScore: number
    passRate: number
    departmentStats: { departmentId: string; departmentName: string; participantCount: number; averageScore: number; passRate: number }[]
  }>('/exam-record/statistics', examId ? { examId } : undefined)
}

/**
 * 批阅主观题
 */
export function markSubjectiveQuestion(recordId: string, questionId: string, score: number, comment?: string) {
  return post<{ success: boolean }>('/exam/mark', {
    recordId,
    questionId,
    score,
    comment
  })
} 