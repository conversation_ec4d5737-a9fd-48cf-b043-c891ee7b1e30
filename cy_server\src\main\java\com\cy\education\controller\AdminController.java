package com.cy.education.controller;

import com.cy.education.model.params.*;
import com.cy.education.model.vo.AdminVO;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.AdminService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员管理控制器
 */
@Api(tags = "管理员管理")
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@Validated
public class AdminController {

    private final AdminService adminService;

    /**
     * 分页查询管理员列表
     */
    @ApiOperation("分页查询管理员列表")
    @GetMapping("/list")
    public ApiResponse<PageResponse<AdminVO>> getAdminList(@Valid AdminQueryParams params) {
        try {
            PageResponse<AdminVO> result = adminService.getAdminList(params);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询管理员列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询管理员详情
     */
    @ApiOperation("根据ID查询管理员详情")
    @GetMapping("/{id}")
    public ApiResponse<AdminVO> getAdminById(@PathVariable Integer id) {
        try {
            AdminVO admin = adminService.getAdminById(id);
            return ApiResponse.success(admin);
        } catch (Exception e) {
            return ApiResponse.error("查询管理员详情失败: " + e.getMessage());
        }
    }

    /**
     * 新增管理员
     */
    @ApiOperation("新增管理员")
    @PostMapping("/add")
    public ApiResponse<Map<String, Integer>> addAdmin(@Valid @RequestBody AdminAddParams params) {
        try {
            Integer adminId = adminService.addAdmin(params);
            Map<String, Integer> result = new HashMap<>();
            result.put("id", adminId);
            return ApiResponse.success(result, "新增管理员成功");
        } catch (Exception e) {
            return ApiResponse.error("新增管理员失败: " + e.getMessage());
        }
    }

    /**
     * 更新管理员信息
     */
    @ApiOperation("更新管理员信息")
    @PostMapping("/update")
    public ApiResponse<Map<String, Boolean>> updateAdmin(@Valid @RequestBody AdminUpdateParams params) {
        try {
            boolean success = adminService.updateAdmin(params);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新管理员成功");
        } catch (Exception e) {
            return ApiResponse.error("更新管理员失败: " + e.getMessage());
        }
    }

    /**
     * 删除管理员
     */
    @ApiOperation("删除管理员")
    @DeleteMapping("/{id}")
    public ApiResponse<Map<String, Boolean>> deleteAdmin(@PathVariable Integer id) {
        try {
            boolean success = adminService.deleteAdmin(id);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "删除管理员成功");
        } catch (Exception e) {
            return ApiResponse.error("删除管理员失败: " + e.getMessage());
        }
    }

    /**
     * 更新管理员状态
     */
    @ApiOperation("更新管理员状态")
    @PostMapping("/status")
    public ApiResponse<Map<String, Boolean>> updateAdminStatus(@Valid @RequestBody AdminStatusParams params) {
        try {
            boolean success = adminService.updateAdminStatus(params);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            String statusText = params.getStatus() == 1 ? "启用" : "禁用";
            return ApiResponse.success(result, statusText + "管理员成功");
        } catch (Exception e) {
            return ApiResponse.error("更新管理员状态失败: " + e.getMessage());
        }
    }

    /**
     * 重置管理员密码
     */
    @ApiOperation("重置管理员密码")
    @PostMapping("/reset-password")
    public ApiResponse<Map<String, String>> resetAdminPassword(@RequestBody Map<String, Integer> params) {
        try {
            Integer id = params.get("id");
            if (id == null) {
                return ApiResponse.validateFailed("管理员ID不能为空");
            }
            
            String newPassword = adminService.resetAdminPassword(id);
            Map<String, String> result = new HashMap<>();
            result.put("password", newPassword);
            return ApiResponse.success(result, "重置密码成功");
        } catch (Exception e) {
            return ApiResponse.error("重置密码失败: " + e.getMessage());
        }
    }

    /**
     * 获取管理员权限列表
     */
    @ApiOperation("获取管理员权限列表")
    @GetMapping("/permissions/{id}")
    public ApiResponse<Map<String, List<Integer>>> getAdminPermissions(@PathVariable Integer id) {
        try {
            List<Integer> permissions = adminService.getAdminPermissions(id);
            Map<String, List<Integer>> result = new HashMap<>();
            result.put("permissions", permissions);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("获取管理员权限失败: " + e.getMessage());
        }
    }

    /**
     * 设置管理员权限
     */
    @ApiOperation("设置管理员权限")
    @PostMapping("/permissions")
    public ApiResponse<Map<String, Boolean>> setAdminPermissions(@Valid @RequestBody AdminPermissionParams params) {
        try {
            boolean success = adminService.setAdminPermissions(params);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "设置权限成功");
        } catch (Exception e) {
            return ApiResponse.error("设置权限失败: " + e.getMessage());
        }
    }

    // ==================== 导入导出功能 ====================

    /**
     * 下载管理员导入模板
     */
    @ApiOperation("下载管理员导入模板")
    @GetMapping("/import/template")
    public ResponseEntity<Resource> downloadImportTemplate() throws IOException {
        Resource resource = adminService.generateImportTemplate();
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=admin_template.xlsx")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    /**
     * 批量导入管理员
     */
    @ApiOperation("批量导入管理员")
    @PostMapping("/import")
    public ApiResponse<Map<String, Object>> importAdmins(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> result = adminService.importAdmins(file);
            return ApiResponse.success(result, "管理员导入完成");
        } catch (Exception e) {
            return ApiResponse.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出管理员列表
     */
    @ApiOperation("导出管理员列表")
    @PostMapping("/export")
    public void exportAdmins(@RequestBody Map<String, Object> params, HttpServletResponse response) throws IOException {
        adminService.exportAdmins(params, response);
    }
} 