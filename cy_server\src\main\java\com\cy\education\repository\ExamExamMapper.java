package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.ExamExam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 考试Mapper接口
 */
@Mapper
public interface ExamExamMapper extends BaseMapper<ExamExam> {

    /**
     * 分页查询考试列表
     *
     * @param page 分页参数
     * @param keyword 关键字
     * @param paperId 试卷ID
     * @param departmentId 部门ID
     * @param status 状态
     * @param isPublished 是否已发布
     * @param sortBy 排序字段
     * @param sortOrder 排序方式
     * @return 分页结果
     */
    IPage<ExamExam> selectExamPage(
            Page<ExamExam> page,
            @Param("keyword") String keyword,
            @Param("paperId") Integer paperId,
            @Param("departmentId") Integer departmentId,
            @Param("status") Integer status,
            @Param("isPublished") Boolean isPublished,
            @Param("sortBy") String sortBy,
            @Param("sortOrder") String sortOrder);
} 