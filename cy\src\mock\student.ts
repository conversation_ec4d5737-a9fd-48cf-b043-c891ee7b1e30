import { mockResponse, generateId } from './utils'
import type { Student } from '@/api/student'
import { departmentData } from './department'

// 获取所有部门的扁平列表
const getAllDepartments = () => {
  const result: { id: number, name: string }[] = []
  
  const traverseDepartments = (departments: any[]) => {
    departments.forEach(dept => {
      result.push({ id: dept.id, name: dept.name })
      if (dept.children && dept.children.length) {
        traverseDepartments(dept.children)
      }
    })
  }
  
  traverseDepartments(departmentData)
  return result
}

// 部门列表
const departments = getAllDepartments()

// 生成模拟学员数据
const generateStudents = (count: number): Student[] => {
  const students: Student[] = []
  const statusValues = [0, 1]
  
  for (let i = 1; i <= count; i++) {
    const departmentIndex = Math.floor(Math.random() * departments.length)
    const departmentId = departments[departmentIndex].id
    const departmentName = departments[departmentIndex].name
    
    const registerDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
    const lastLoginDate = new Date(registerDate.getTime() + Math.random() * (Date.now() - registerDate.getTime()))
    
    students.push({
      id: i,
      name: `学员${i}`,
      phone: `1${Math.floor(Math.random() * 9) + 3}${Math.random().toString().substring(2, 11)}`,
      departmentId,
      department: departmentName,
      avatar: `https://via.placeholder.com/50/4e73df/ffffff?text=${encodeURIComponent(`学员${i}`)}`,
      status: statusValues[Math.floor(Math.random() * statusValues.length)],
      registerTime: registerDate.toLocaleString(),
      lastLoginTime: lastLoginDate.toLocaleString(),
      points: Math.floor(Math.random() * 2000),
      entryTime: new Date(registerDate.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      remark: Math.random() > 0.7 ? `备注信息${i}` : ''
    })
  }
  
  return students
}

// 学员数据
let studentData = generateStudents(100)

// 下一个学员ID
let nextStudentId = studentData.length + 1

// 获取学员列表
export function mockGetStudentList(params: {
  keyword?: string
  departmentId?: number
  status?: number
  page?: number
  size?: number
}) {
  let filteredData = [...studentData]
  
  // 关键字搜索（姓名或手机号）
  if (params.keyword) {
    filteredData = filteredData.filter(
      student => student.name.includes(params.keyword!) || student.phone.includes(params.keyword!)
    )
  }
  
  // 部门筛选
  if (params.departmentId) {
    filteredData = filteredData.filter(student => student.departmentId === params.departmentId)
  }
  
  // 状态筛选
  if (params.status !== undefined) {
    filteredData = filteredData.filter(student => student.status === params.status)
  }
  
  // 计算总数
  const total = filteredData.length
  
  // 分页
  const page = params.page || 1
  const size = params.size || 10
  const start = (page - 1) * size
  const end = start + size
  
  filteredData = filteredData.slice(start, end)
  
  return mockResponse({
    list: filteredData,
    total
  })
}

// 获取学员详情
export function mockGetStudentById(id: number) {
  const student = studentData.find(item => item.id === id)
  
  if (student) {
    return mockResponse(student)
  }
  
  return mockResponse(null, 404, '学员不存在')
}

// 添加学员
export function mockAddStudent(data: Partial<Student> & { password: string }) {
  // 从部门ID获取部门名称
  const department = departments.find(item => item.id === data.departmentId)
  
  const newStudent: Student = {
    id: nextStudentId++,
    name: data.name || '',
    phone: data.phone || '',
    departmentId: data.departmentId || 0,
    department: department ? department.name : '',
    avatar: `https://via.placeholder.com/50/4e73df/ffffff?text=${encodeURIComponent(data.name || '')}`,
    status: 1, // 默认启用
    registerTime: new Date().toLocaleString(),
    lastLoginTime: new Date().toLocaleString(),
    points: 0,
    entryTime: data.entryTime,
    remark: data.remark
  }
  
  studentData.unshift(newStudent)
  
  return mockResponse({ id: newStudent.id })
}

// 更新学员
export function mockUpdateStudent(data: Partial<Student>) {
  if (!data.id) {
    return mockResponse(null, 400, '学员ID不能为空')
  }
  
  const index = studentData.findIndex(item => item.id === data.id)
  
  if (index === -1) {
    return mockResponse(null, 404, '学员不存在')
  }
  
  // 如果更新了部门ID，需要更新部门名称
  if (data.departmentId && data.departmentId !== studentData[index].departmentId) {
    const department = departments.find(item => item.id === data.departmentId)
    if (department) {
      data.department = department.name
    }
  }
  
  studentData[index] = { ...studentData[index], ...data }
  
  return mockResponse({ success: true })
}

// 删除学员
export function mockDeleteStudent(id: number) {
  const index = studentData.findIndex(item => item.id === id)
  
  if (index === -1) {
    return mockResponse(null, 404, '学员不存在')
  }
  
  studentData.splice(index, 1)
  
  return mockResponse({ success: true })
}

// 重置学员密码
export function mockResetStudentPassword(id: number) {
  const student = studentData.find(item => item.id === id)
  
  if (!student) {
    return mockResponse(null, 404, '学员不存在')
  }
  
  // 生成随机6位数密码
  const password = Math.floor(100000 + Math.random() * 900000).toString()
  
  return mockResponse({ password })
}

// 更新学员状态
export function mockUpdateStudentStatus(id: number, status: number) {
  const student = studentData.find(item => item.id === id)
  
  if (!student) {
    return mockResponse(null, 404, '学员不存在')
  }
  
  student.status = status
  
  return mockResponse({ success: true })
}

// 导出学员列表
export function mockExportStudentList() {
  return mockResponse({ url: '/static/students_export.xlsx' })
}

// 导入学员
export function mockImportStudents() {
  // 模拟导入成功，实际应处理上传文件
  const count = Math.floor(Math.random() * 10) + 5
  
  // 生成新的学员
  const newStudents = generateStudents(count).map(student => ({
    ...student,
    id: nextStudentId++
  }))
  
  // 添加到列表
  studentData = [...newStudents, ...studentData]
  
  return mockResponse({ success: true, count })
} 