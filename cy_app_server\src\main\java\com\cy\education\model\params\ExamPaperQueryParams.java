package com.cy.education.model.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 试卷查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "试卷查询参数")
public class ExamPaperQueryParams extends PageParams {

    @ApiModelProperty(value = "关键字，用于搜索试卷标题或描述")
    private String keyword;

    @ApiModelProperty(value = "是否已发布")
    private Boolean isPublished;

    @ApiModelProperty(value = "排序字段")
    private String sortBy;

    @ApiModelProperty(value = "排序方式(asc/desc)")
    private String sortOrder;
} 