package com.cy.education.service;

import com.cy.education.model.entity.Notice;
import com.cy.education.model.vo.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;

/**
 * 公告服务接口
 */
public interface NoticeService {
    
    /**
     * 分页查询公告列表
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    PageResponse<Notice> listNotices(ContentQueryParam param);
    
    /**
     * 获取公告详情
     * 
     * @param id 公告ID
     * @return 公告信息
     */
    Notice getNoticeById(Integer id);
    
    /**
     * 新增公告
     * 
     * @param notice 公告信息
     * @return 公告ID
     */
    Integer addNotice(Notice notice);
    
    /**
     * 更新公告
     * 
     * @param notice 公告信息
     * @return 是否成功
     */
    boolean updateNotice(Notice notice);
    
    /**
     * 删除公告
     * 
     * @param id 公告ID
     * @return 是否成功
     */
    boolean deleteNotice(Integer id);
    
    /**
     * 更新公告状态
     * 
     * @param id 公告ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateNoticeStatus(Integer id, Integer status);
    
    /**
     * 更新公告置顶状态
     * 
     * @param id 公告ID
     * @param isTop 是否置顶
     * @return 是否成功
     */
    boolean updateNoticeTopStatus(Integer id, Boolean isTop);
} 