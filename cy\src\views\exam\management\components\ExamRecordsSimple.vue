<template>
  <div class="exam-records">
    <div class="filter-form">
      <el-form :inline="true" :model="filterForm" ref="filterFormRef">
        <el-form-item label="考试名称">
          <el-select v-model="filterForm.examId" placeholder="选择考试" clearable style="width: 200px">
            <el-option
              v-for="item in examOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filterForm.departmentId" placeholder="选择部门" clearable style="width: 200px">
            <el-option
              v-for="item in departmentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">筛选</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        :data="recordsList"
        :loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="studentName" label="学员姓名" width="120" />
        <el-table-column prop="examName" label="考试名称" width="200" />
        <el-table-column prop="score" label="得分" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'completed' ? 'success' : 'warning'">
              {{ row.status === 'completed' ? '已完成' : '进行中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="提交时间" width="180" />
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 表格数据和分页
const loading = ref(false)
const recordsList = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 筛选表单
const filterForm = reactive({
  examId: '',
  departmentId: ''
})

// 选项数据
const examOptions = ref<{ value: string, label: string }[]>([])
const departmentOptions = ref<{ value: string, label: string }[]>([])

// 获取考试记录列表
const fetchRecordsList = async () => {
  loading.value = true
  try {
    // 模拟数据
    const mockData = {
      list: [
        {
          id: 1,
          studentName: '张三',
          examName: '期末考试',
          score: 85,
          status: 'completed',
          submitTime: '2024-01-15 10:30:00'
        },
        {
          id: 2,
          studentName: '李四',
          examName: '期末考试',
          score: 92,
          status: 'completed',
          submitTime: '2024-01-15 11:15:00'
        }
      ],
      total: 2
    }
    
    recordsList.value = mockData.list
    total.value = mockData.total
  } catch (error) {
    console.error('获取考试记录失败:', error)
    ElMessage.error('获取考试记录失败')
  } finally {
    loading.value = false
  }
}

// 筛选
const handleFilter = () => {
  currentPage.value = 1
  fetchRecordsList()
}

// 重置
const handleReset = () => {
  filterForm.examId = ''
  filterForm.departmentId = ''
  currentPage.value = 1
  fetchRecordsList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchRecordsList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchRecordsList()
}

// 初始化选项数据
const initOptions = () => {
  examOptions.value = [
    { value: '1', label: '期末考试' },
    { value: '2', label: '期中考试' }
  ]
  
  departmentOptions.value = [
    { value: '1', label: '技术部' },
    { value: '2', label: '市场部' }
  ]
}

// 页面加载时获取数据
onMounted(() => {
  initOptions()
  fetchRecordsList()
})

// 暴露方法给父组件
defineExpose({
  filterByExam: (examId: string) => {
    filterForm.examId = examId
    handleFilter()
  }
})
</script>

<style scoped>
.exam-records {
  width: 100%;
}

.filter-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
