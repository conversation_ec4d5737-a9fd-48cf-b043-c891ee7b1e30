package com.cy.education.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.ForumCategory;
import com.cy.education.model.entity.ForumComment;
import com.cy.education.model.entity.ForumPost;
import com.cy.education.model.entity.ForumViolation;
import com.cy.education.model.vo.*;
import com.cy.education.service.ForumCategoryService;
import com.cy.education.service.ForumCommentService;
import com.cy.education.service.ForumPostService;
import com.cy.education.service.ForumViolationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 论坛管理控制器
 */
@Api(tags = "论坛管理接口")
@RestController
@RequestMapping("/forum")
@Slf4j
public class ForumController {

    @Autowired
    private ForumCategoryService forumCategoryService;
    
    @Autowired
    private ForumPostService forumPostService;
    
    @Autowired
    private ForumCommentService forumCommentService;
    
    @Autowired
    private ForumViolationService forumViolationService;
    
    //=========================== 论坛分类接口 ===========================
    
    /**
     * 获取分类列表（树形结构）
     */
    @ApiOperation("获取分类列表（树形结构）")
    @GetMapping("/category/list")
    public ApiResponse<List<ForumCategory>> getCategoryList(ForumCategoryQueryParam param) {
        try {
            List<ForumCategory> categoryList = forumCategoryService.getCategoryTree(param);
            return ApiResponse.success(categoryList);
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            return ApiResponse.error("获取分类列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取分类详情
     */
    @ApiOperation("获取分类详情")
    @GetMapping("/category/{id}")
    public ApiResponse<ForumCategory> getCategoryById(@PathVariable Integer id) {
        try {
            ForumCategory category = forumCategoryService.getCategoryById(id);
            return ApiResponse.success(category);
        } catch (Exception e) {
            log.error("获取分类详情失败", e);
            return ApiResponse.error("获取分类详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取父分类列表（用于下拉选择）
     */
    @ApiOperation("获取父分类列表")
    @GetMapping("/category/parents")
    public ApiResponse<List<ForumCategory>> getParentCategories() {
        try {
            List<ForumCategory> parentList = forumCategoryService.getParentCategories();
            return ApiResponse.success(parentList);
        } catch (Exception e) {
            log.error("获取父分类列表失败", e);
            return ApiResponse.error("获取父分类列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增分类
     */
    @ApiOperation("新增分类")
    @PostMapping("/category/add")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Object>> addCategory(@Validated @RequestBody ForumCategory category) {
        try {
            Integer id = forumCategoryService.addCategory(category);
            Map<String, Object> result = new HashMap<>();
            result.put("id", id);
            return ApiResponse.success(result, "添加分类成功");
        } catch (Exception e) {
            log.error("添加分类失败", e);
            return ApiResponse.error("添加分类失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新分类
     */
    @ApiOperation("更新分类")
    @PutMapping("/category/update")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> updateCategory(@Validated @RequestBody ForumCategory category) {
        try {
            boolean success = forumCategoryService.updateCategory(category);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新分类成功");
        } catch (Exception e) {
            log.error("更新分类失败", e);
            return ApiResponse.error("更新分类失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除分类
     */
    @ApiOperation("删除分类")
    @DeleteMapping("/category/{id}")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> deleteCategory(@PathVariable Integer id) {
        try {
            boolean success = forumCategoryService.deleteCategory(id);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "删除分类成功");
        } catch (Exception e) {
            log.error("删除分类失败", e);
            return ApiResponse.error("删除分类失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新分类状态
     */
    @ApiOperation("更新分类状态")
    @PutMapping("/category/{id}/status")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> updateCategoryStatus(@PathVariable Integer id, @RequestBody Map<String, Integer> param) {
        try {
            Integer status = param.get("status");
            if (status == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            boolean success = forumCategoryService.updateCategoryStatus(id, status);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新状态成功");
        } catch (Exception e) {
            log.error("更新分类状态失败", e);
            return ApiResponse.error("更新状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新分类排序
     */
    @ApiOperation("更新分类排序")
    @PutMapping("/category/sort")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> updateCategorySort(@RequestBody List<ForumCategory> sortData) {
        try {
            boolean success = forumCategoryService.updateCategorySort(sortData);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新排序成功");
        } catch (Exception e) {
            log.error("更新分类排序失败", e);
            return ApiResponse.error("更新排序失败: " + e.getMessage());
        }
    }

    //=========================== 论坛帖子接口 ===========================
    
    /**
     * 获取帖子列表
     */
    @ApiOperation("获取帖子列表")
    @GetMapping("/post/list")
    public ApiResponse<Map<String, Object>> getPostList(ForumPostQueryParam param) {
        try {
            IPage<ForumPost> page = forumPostService.getPostPage(param);
            Map<String, Object> result = new HashMap<>();
            result.put("list", page.getRecords());
            result.put("total", page.getTotal());
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取帖子列表失败", e);
            return ApiResponse.error("获取帖子列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取帖子详情
     */
    @ApiOperation("获取帖子详情")
    @GetMapping("/post/{id}")
    public ApiResponse<ForumPost> getPostById(@PathVariable Integer id) {
        try {
            ForumPost post = forumPostService.getPostById(id);
            // 增加浏览量
            forumPostService.incrementViewCount(id);
            return ApiResponse.success(post);
        } catch (Exception e) {
            log.error("获取帖子详情失败", e);
            return ApiResponse.error("获取帖子详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 审核帖子
     */
    @ApiOperation("审核帖子")
    @PutMapping("/post/{id}/review")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> reviewPost(@PathVariable Integer id, @RequestBody Map<String, Object> param) {
        try {
            Object statusObj = param.get("status");
            if (statusObj == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            // 将字符串状态转换为数字状态
            Integer status;
            if (statusObj instanceof String) {
                status = convertPostStatus((String) statusObj);
            } else if (statusObj instanceof Integer) {
                status = (Integer) statusObj;
            } else {
                return ApiResponse.validateFailed("状态格式错误");
            }
            
            boolean success = forumPostService.reviewPost(id, status);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "审核成功");
        } catch (Exception e) {
            log.error("审核帖子失败", e);
            return ApiResponse.error("审核失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置帖子置顶状态
     */
    @ApiOperation("设置帖子置顶状态")
    @PutMapping("/post/{id}/top")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> setPostTop(@PathVariable Integer id, @RequestBody Map<String, Boolean> param) {
        try {
            Boolean isTop = param.get("isTop");
            if (isTop == null) {
                return ApiResponse.validateFailed("置顶状态不能为空");
            }
            
            boolean success = forumPostService.setPostTop(id, isTop);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, isTop ? "置顶成功" : "取消置顶成功");
        } catch (Exception e) {
            log.error("设置帖子置顶状态失败", e);
            return ApiResponse.error("设置置顶状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置帖子精华状态
     */
    @ApiOperation("设置帖子精华状态")
    @PutMapping("/post/{id}/essence")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> setPostEssence(@PathVariable Integer id, @RequestBody Map<String, Boolean> param) {
        try {
            Boolean isEssence = param.get("isEssence");
            if (isEssence == null) {
                return ApiResponse.validateFailed("精华状态不能为空");
            }
            
            boolean success = forumPostService.setPostEssence(id, isEssence);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, isEssence ? "设为精华成功" : "取消精华成功");
        } catch (Exception e) {
            log.error("设置帖子精华状态失败", e);
            return ApiResponse.error("设置精华状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除帖子
     */
    @ApiOperation("删除帖子")
    @DeleteMapping("/post/{id}")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> deletePost(@PathVariable Integer id) {
        try {
            boolean success = forumPostService.deletePost(id);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "删除成功");
        } catch (Exception e) {
            log.error("删除帖子失败", e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量审核帖子
     */
    @ApiOperation("批量审核帖子")
    @PostMapping("/post/batch-review")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> batchReviewPosts(@RequestBody Map<String, Object> param) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> ids = (List<Integer>) param.get("ids");
            Object statusObj = param.get("status");
            
            if (ids == null || ids.isEmpty()) {
                return ApiResponse.validateFailed("帖子ID列表不能为空");
            }
            if (statusObj == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            // 将字符串状态转换为数字状态
            Integer status;
            if (statusObj instanceof String) {
                status = convertPostStatus((String) statusObj);
            } else if (statusObj instanceof Integer) {
                status = (Integer) statusObj;
            } else {
                return ApiResponse.validateFailed("状态格式错误");
            }
            
            boolean success = forumPostService.batchReviewPosts(ids, status);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "批量审核成功");
        } catch (Exception e) {
            log.error("批量审核帖子失败", e);
            return ApiResponse.error("批量审核失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除帖子
     */
    @ApiOperation("批量删除帖子")
    @PostMapping("/post/batch-delete")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> batchDeletePosts(@RequestBody Map<String, List<Integer>> param) {
        try {
            List<Integer> ids = param.get("ids");
            if (ids == null || ids.isEmpty()) {
                return ApiResponse.validateFailed("帖子ID列表不能为空");
            }
            
            boolean success = forumPostService.batchDeletePosts(ids);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "批量删除成功");
        } catch (Exception e) {
            log.error("批量删除帖子失败", e);
            return ApiResponse.error("批量删除失败: " + e.getMessage());
        }
    }
    
    //=========================== 论坛评论接口 ===========================
    
    /**
     * 获取评论列表
     */
    @ApiOperation("获取评论列表")
    @GetMapping("/comment/list")
    public ApiResponse<Map<String, Object>> getCommentList(ForumCommentQueryParam param) {
        try {
            IPage<ForumComment> page = forumCommentService.getCommentPage(param);
            Map<String, Object> result = new HashMap<>();
            result.put("list", page.getRecords());
            result.put("total", page.getTotal());
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取评论列表失败", e);
            return ApiResponse.error("获取评论列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取帖子的评论列表（树形结构）
     */
    @ApiOperation("获取帖子的评论列表")
    @GetMapping("/post/{postId}/comments")
    public ApiResponse<List<ForumComment>> getPostComments(@PathVariable Integer postId) {
        try {
            List<ForumComment> comments = forumCommentService.getPostComments(postId);
            return ApiResponse.success(comments);
        } catch (Exception e) {
            log.error("获取帖子评论列表失败", e);
            return ApiResponse.error("获取帖子评论列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取评论详情
     */
    @ApiOperation("获取评论详情")
    @GetMapping("/comment/{id}")
    public ApiResponse<ForumComment> getCommentById(@PathVariable Integer id) {
        try {
            ForumComment comment = forumCommentService.getCommentById(id);
            return ApiResponse.success(comment);
        } catch (Exception e) {
            log.error("获取评论详情失败", e);
            return ApiResponse.error("获取评论详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 审核评论
     */
    @ApiOperation("审核评论")
    @PutMapping("/comment/{id}/review")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> reviewComment(@PathVariable Integer id, @RequestBody Map<String, Object> param) {
        try {
            Object statusObj = param.get("status");
            if (statusObj == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            // 将字符串状态转换为数字状态
            Integer status;
            if (statusObj instanceof String) {
                status = convertCommentStatus((String) statusObj);
            } else if (statusObj instanceof Integer) {
                status = (Integer) statusObj;
            } else {
                return ApiResponse.validateFailed("状态格式错误");
            }
            
            boolean success = forumCommentService.reviewComment(id, status);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "审核成功");
        } catch (Exception e) {
            log.error("审核评论失败", e);
            return ApiResponse.error("审核失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除评论
     */
    @ApiOperation("删除评论")
    @DeleteMapping("/comment/{id}")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> deleteComment(@PathVariable Integer id) {
        try {
            boolean success = forumCommentService.deleteComment(id);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "删除成功");
        } catch (Exception e) {
            log.error("删除评论失败", e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量审核评论
     */
    @ApiOperation("批量审核评论")
    @PostMapping("/comment/batch-review")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> batchReviewComments(@RequestBody Map<String, Object> param) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> ids = (List<Integer>) param.get("ids");
            Object statusObj = param.get("status");
            
            if (ids == null || ids.isEmpty()) {
                return ApiResponse.validateFailed("评论ID列表不能为空");
            }
            if (statusObj == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            // 将字符串状态转换为数字状态
            Integer status;
            if (statusObj instanceof String) {
                status = convertCommentStatus((String) statusObj);
            } else if (statusObj instanceof Integer) {
                status = (Integer) statusObj;
            } else {
                return ApiResponse.validateFailed("状态格式错误");
            }
            
            boolean success = forumCommentService.batchReviewComments(ids, status);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "批量审核成功");
        } catch (Exception e) {
            log.error("批量审核评论失败", e);
            return ApiResponse.error("批量审核失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除评论
     */
    @ApiOperation("批量删除评论")
    @PostMapping("/comment/batch-delete")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> batchDeleteComments(@RequestBody Map<String, List<Integer>> param) {
        try {
            List<Integer> ids = param.get("ids");
            if (ids == null || ids.isEmpty()) {
                return ApiResponse.validateFailed("评论ID列表不能为空");
            }
            
            boolean success = forumCommentService.batchDeleteComments(ids);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "批量删除成功");
        } catch (Exception e) {
            log.error("批量删除评论失败", e);
            return ApiResponse.error("批量删除失败: " + e.getMessage());
        }
    }
    
    //=========================== 论坛违规举报接口 ===========================
    
    /**
     * 获取违规举报列表
     */
    @ApiOperation("获取违规举报列表")
    @GetMapping("/violation/list")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Object>> getViolationList(ForumViolationQueryParam param) {
        try {
            IPage<ForumViolation> page = forumViolationService.getViolationPage(param);
            Map<String, Object> result = new HashMap<>();
            result.put("list", page.getRecords());
            result.put("total", page.getTotal());
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取违规举报列表失败", e);
            return ApiResponse.error("获取违规举报列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取违规举报详情
     */
    @ApiOperation("获取违规举报详情")
    @GetMapping("/violation/{id}")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<ForumViolation> getViolationById(@PathVariable Integer id) {
        try {
            ForumViolation violation = forumViolationService.getViolationById(id);
            return ApiResponse.success(violation);
        } catch (Exception e) {
            log.error("获取违规举报详情失败", e);
            return ApiResponse.error("获取违规举报详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理违规举报
     */
    @ApiOperation("处理违规举报")
    @PutMapping("/violation/{id}/process")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> processViolation(@PathVariable Integer id, @RequestBody Map<String, Object> param) {
        try {
            Object statusObj = param.get("status");
            String result = (String) param.get("result");
            
            if (statusObj == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            // 将字符串状态转换为数字状态
            Integer status;
            if (statusObj instanceof String) {
                status = convertViolationStatus((String) statusObj);
            } else if (statusObj instanceof Integer) {
                status = (Integer) statusObj;
            } else {
                return ApiResponse.validateFailed("状态格式错误");
            }
            
            // 获取当前操作用户ID
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Integer operatorId = null;
            if (authentication != null && authentication.getPrincipal() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> userInfo = (Map<String, Object>) authentication.getPrincipal();
                operatorId = (Integer) userInfo.get("id");
            }
            
            boolean success = forumViolationService.processViolation(id, status, result, operatorId);
            Map<String, Boolean> response = new HashMap<>();
            response.put("success", success);
            return ApiResponse.success(response, "处理成功");
        } catch (Exception e) {
            log.error("处理违规举报失败", e);
            return ApiResponse.error("处理违规举报失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量处理违规举报
     */
    @ApiOperation("批量处理违规举报")
    @PostMapping("/violation/batch-process")
    @PreAuthorize("hasAuthority('forum:manage')")
    public ApiResponse<Map<String, Boolean>> batchProcessViolations(@RequestBody Map<String, Object> param) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> ids = (List<Integer>) param.get("ids");
            Object statusObj = param.get("status");
            String result = (String) param.get("result");
            
            if (ids == null || ids.isEmpty()) {
                return ApiResponse.validateFailed("违规举报ID列表不能为空");
            }
            if (statusObj == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            // 将字符串状态转换为数字状态
            Integer status;
            if (statusObj instanceof String) {
                status = convertViolationStatus((String) statusObj);
            } else if (statusObj instanceof Integer) {
                status = (Integer) statusObj;
            } else {
                return ApiResponse.validateFailed("状态格式错误");
            }
            
            // 获取当前操作用户ID
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Integer operatorId = null;
            if (authentication != null && authentication.getPrincipal() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> userInfo = (Map<String, Object>) authentication.getPrincipal();
                operatorId = (Integer) userInfo.get("id");
            }
            
            boolean success = forumViolationService.batchProcessViolations(ids, status, result, operatorId);
            Map<String, Boolean> response = new HashMap<>();
            response.put("success", success);
            return ApiResponse.success(response, "批量处理成功");
        } catch (Exception e) {
            log.error("批量处理违规举报失败", e);
            return ApiResponse.error("批量处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 将帖子字符串状态转换为数字状态
     */
    private Integer convertPostStatus(String status) {
        switch (status) {
            case "pending":
                return 0;
            case "approved":
                return 1;
            case "rejected":
                return 2;
            case "deleted":
                return 3;
            default:
                throw new IllegalArgumentException("无效的帖子状态: " + status);
        }
    }
    
    /**
     * 将评论字符串状态转换为数字状态
     */
    private Integer convertCommentStatus(String status) {
        switch (status) {
            case "pending":
                return 0;
            case "approved":
                return 1;
            case "rejected":
                return 2;
            case "deleted":
                return 3;
            default:
                throw new IllegalArgumentException("无效的评论状态: " + status);
        }
    }
    
    /**
     * 将违规举报字符串状态转换为数字状态
     */
    private Integer convertViolationStatus(String status) {
        switch (status) {
            case "pending":
                return 0;
            case "processed":
                return 1;
            case "ignored":
                return 2;
            default:
                throw new IllegalArgumentException("无效的违规举报状态: " + status);
        }
    }
} 