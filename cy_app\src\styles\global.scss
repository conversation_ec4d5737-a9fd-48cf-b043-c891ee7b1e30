@import "uview-plus/theme.scss";

/* 全局CSS变量 */
:root {
  /* 主色调 - 现代紫蓝色系 */
  --primary-color: #667eea;
  --primary-light: #a8b5ff;
  --primary-dark: #4c63d2;
  --secondary-color: #764ba2;

  /* 渐变色系 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-soft: linear-gradient(135deg, #667eea20 0%, #764ba220 100%);
  --gradient-card: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  --gradient-overlay: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.05) 100%);

  /* 背景色系 */
  --bg-primary: #fafbff;
  --bg-secondary: #f5f7ff;
  --bg-card: #ffffff;
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-overlay: rgba(0, 0, 0, 0.02);

  /* 文字色系 */
  --text-primary: #1a1d2e;
  --text-secondary: #4a5568;
  --text-tertiary: #8e8e93;
  --text-light: #a0aec0;
  --text-white: #ffffff;

  /* 边框色系 */
  --border-light: #e2e8f0;
  --border-medium: #cbd5e0;
  --border-primary: var(--primary-color);

  /* 阴影系统 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-card: 0 2px 12px rgba(102, 126, 234, 0.15);
  --shadow-float: 0 8px 24px rgba(102, 126, 234, 0.25);

  /* 圆角系统 */
  --radius-xs: 4px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-full: 9999px;

  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;

  /* 字体系统 */
  --font-xs: 10px;
  --font-sm: 12px;
  --font-md: 14px;
  --font-lg: 16px;
  --font-xl: 18px;
  --font-2xl: 20px;
  --font-3xl: 24px;
  --font-4xl: 32px;

  /* 字重 */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  /* 高度设置 */
  --navbar-height: 44px;
  --tabbar-height: 60px;
  --safe-area-top: env(safe-area-inset-top);
  --safe-area-bottom: env(safe-area-inset-bottom);
}

/* 全局重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body, page {
  font-family: var(--font-family);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  line-height: 1.6;
}

/* ================== 页面容器 ================== */
.page-container {
  min-height: 100vh;
  background: var(--bg-primary);
  padding-bottom: calc(var(--tabbar-height) + var(--safe-area-bottom) + 32px);
  overflow-x: hidden;
  position: relative;
}

/* 统一页面内容区域 */
.page-content {
  padding: 20px;
  padding-bottom: 120px;
  position: relative;
  z-index: 1;
}

.content-container {
  padding: 20px;
  background: var(--bg-primary);
}

// 通用自定义导航栏样式
.custom-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  z-index: 1000;
}
.status-bar-placeholder {
  height: var(--status-bar-height, 40px);
}
.navbar-content {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}
.placeholder {
  width: 36px;
}
.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.back-btn {
  width: 25px;
  height: 25px;
  border-radius: 18px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  .icon {
    font-size: 22px;
    color: white;
    font-weight: bold;
    line-height: 1;
  }
}
.back-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}
.navbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.navbar-action {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: var(--transition-normal);
}

.navbar-action:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-time {
  font-size: 16px;
  color: #ffffff;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

.navbar-search {
  flex: 1;
  max-width: 200px;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 6px 12px;
  height: 32px;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #333;
}

.search-input::placeholder {
  color: #8e8e93;
}

.message-icon {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-circle);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  color: white;
  font-size: 16px;
}

.badge {
  position: absolute;
  top: -2px;
  right: -2px;
  min-width: 16px;
  height: 16px;
  background: var(--error-color);
  border-radius: var(--radius-circle);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  font-weight: var(--font-weight-medium);
  padding: 0 4px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info .avatar {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-circle);
}

.user-info .welcome {
  color: white;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
}

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 120rpx;
  color: var(--text-tertiary);
}

.load-more {
display: flex;
align-items: center;
justify-content: center;
padding: 20px;

.loading-text {
    display: flex;
    align-items: center;
    gap: 8px;

  text {
    font-size: 14px;
    color: #909399;
    }
}

.nomore-text {
    text {
    font-size: 14px;
    color: #c0c4cc;
    }
}

.loadmore-text {
    text {
    font-size: 14px;
    color: #667eea;
    }
}
}

.empty-image {
  width: 180px;
  height: 180px;
  margin-bottom: 24px;
}

.empty-text {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

/* ================== 页面内容区域 ================== */
.page-content {
  flex: 1;
  background: var(--bg-secondary);
  border-radius: 20px 20px 0 0;
  margin-top: -20px;
  padding: 24px 16px 80px;
  position: relative;
  z-index: 1;
  min-height: calc(100vh - 120px);
}

/* ================== 卡片样式 ================== */
.card {
  background: var(--gradient-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  transition: var(--transition-normal);
  margin-bottom: 16px;
}

.card:active {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card.floating {
  box-shadow: var(--shadow-float);
  transform: translateY(-4px);
}

.card-padding {
  padding: 20px;
}

.card-header {
  //padding: var(--space-lg) var(--space-lg) 0;
}

.card-title {
  font-size: var(--font-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.card-subtitle {
  font-size: var(--font-sm);
  color: var(--text-tertiary);
}

.card-content {
  padding: 16px 20px 20px;
}

.card-footer {
  padding: 0 var(--space-lg) var(--space-lg);
  border-top: 1px solid var(--border-light);
  background: var(--bg-overlay);
}

/* ================== 段落和区块 ================== */
.section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 0 4px;
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.section-more {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* ================== 按钮样式 ================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  font-size: var(--font-md);
  font-weight: var(--font-medium);
  text-align: center;
  border: none;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-overlay);
  opacity: 0;
  transition: var(--transition-fast);
}

.btn:active {
  transform: scale(0.98);
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-white);
  box-shadow: var(--shadow-card);
}

.btn-primary:hover {
  box-shadow: var(--shadow-float);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--bg-card);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background: var(--gradient-soft);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:active {
  background: rgba(16, 185, 129, 0.1);
}

.btn-text {
  background: transparent;
  color: var(--primary-color);
  padding: 8px 16px;
}

.btn-ghost {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
  backdrop-filter: blur(10px);
}

.btn-small {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-sm);
  min-height: 36px;
}

.btn-large {
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-lg);
  min-height: 52px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ================== 标签样式 ================== */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-xs);
  font-weight: var(--font-medium);
}

.tag-primary {
  background: var(--gradient-soft);
  color: var(--primary-color);
}

.tag-primary-light {
  background: rgba(16, 185, 129, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.tag-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.tag-error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.tag-info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

/* ================== 列表样式 ================== */
.list {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-light);
  transition: var(--transition-fast);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background: var(--bg-secondary);
}

.item-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  margin-right: var(--space-md);
  background: var(--gradient-soft);
  overflow: hidden;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: var(--font-md);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.item-subtitle {
  font-size: var(--font-sm);
  color: var(--text-tertiary);
}

.item-arrow {
  color: var(--text-tertiary);
  font-size: 16px;
  margin-left: var(--space-sm);
}

/* ================== 输入框样式 ================== */
.input-group {
  margin-bottom: var(--space-md);
}

.input-label {
  display: block;
  font-size: var(--font-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.input-field {
  width: 100%;
  padding: var(--space-md);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--font-md);
  background: var(--bg-card);
  transition: var(--transition-normal);
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: var(--bg-primary);
}

.input-field::placeholder {
  color: var(--text-light);
}

/* ================== 搜索框样式 ================== */
.search-box {
  display: flex;
  align-items: center;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 12px 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color-light);
  gap: 12px;
}

.search-icon {
  color: var(--text-tertiary);
  font-size: 16px;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: var(--font-size-md);
  color: var(--text-primary);
  background: transparent;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.clear-icon {
  color: var(--text-tertiary);
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
}

/* ================== 进度条样式 ================== */
.progress {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

/* ================== 空状态样式 ================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.empty-desc {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* ================== 加载状态 ================== */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--text-secondary);
}

/* ================== 底部占位 ================== */
.tabbar-placeholder {
  height: 80px;
}

.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* ================== 工具类 ================== */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-disabled { color: var(--text-disabled); }
.text-brand { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.font-regular { font-weight: var(--font-weight-regular); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.mb-xs { margin-bottom: var(--space-xs); }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }

.mt-xs { margin-top: var(--space-xs); }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }

.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* ================== 专用页面组件样式 ================== */

/* 论坛页面样式 */
.post-card {
  padding: 20px !important;

  .post-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 12px;
    gap: 12px;
  }

  .user-info {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 12px;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-circle);
  }

  .user-details {
    flex: 1;
  }

  .username {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: 4px;
    display: block;
  }

  .post-meta {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .category-tag {
    font-size: var(--font-size-xs);
    color: var(--primary-color);
    background: rgba(16, 185, 129, 0.1);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
  }

  .post-time {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
  }

  .top-badge {
    padding: 4px 8px;
    background: var(--error-color);
    color: white;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
  }

  .post-content {
    margin-bottom: 12px;
  }

  .post-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: 8px;
    display: block;
    line-height: 1.4;
  }

  .post-excerpt {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .post-stats {
    display: flex;
    gap: 16px;
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

/* 个人中心页面样式 */
.profile-card {
  background: var(--primary-gradient) !important;
  color: white;

  .profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 16px;
  }

  .profile-avatar {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-circle);
    border: 3px solid rgba(255, 255, 255, 0.3);
  }

  .profile-info {
    flex: 1;
  }

  .profile-name {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: 4px;
    display: block;
  }

  .profile-dept {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin-bottom: 2px;
    display: block;
  }

  .profile-id {
    font-size: var(--font-size-xs);
    opacity: 0.7;
    display: block;
  }

  .edit-btn {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);

    text {
      color: white;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
    }
  }

  .points-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
  }

  .points-label {
    font-size: var(--font-size-sm);
    opacity: 0.8;
  }

  .points-value {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
  }
}

.menu-card {
  .menu-list {
    .menu-item {
      display: flex;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid var(--border-color-light);
      gap: 16px;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: var(--bg-secondary);
        margin: 0 -20px;
        padding-left: 20px;
        padding-right: 20px;
      }
    }

    .menup-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;

      &.study {
        background: linear-gradient(135deg, #E8F5E8 0%, #D4F5D4 100%);
      }

      &.exam {
        background: linear-gradient(135deg, #FFF5E8 0%, #FFE8D4 100%);
      }

      &.points {
        background: linear-gradient(135deg, #FFE8F5 0%, #FFD4E8 100%);
      }

      &.forum {
        background: linear-gradient(135deg, #E8F2FF 0%, #D4E8FF 100%);
      }

      &.collection {
        background: linear-gradient(135deg, #F3E8FF 0%, #E8D4FF 100%);
      }

      &.settings {
        background: linear-gradient(135deg, #F0F0F0 0%, #E0E0E0 100%);
      }
    }

    .menu-content {
      flex: 1;
    }

    .menu-title {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
      margin-bottom: 4px;
      display: block;
    }

    .menu-desc {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      display: block;
    }

    .menu-arrow {
      color: var(--text-tertiary);
      font-size: 16px;
    }
  }
}

/* 分类标签样式 */
.category-tabs {
  .tab-list {
    display: flex;
    gap: 12px;
    padding: 0 4px;
    padding-right: 20px;

    .tab-item {
      flex-shrink: 0;
      padding: 8px 16px;
      border-radius: var(--radius-lg);
      background: var(--bg-primary);
      color: var(--text-secondary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      transition: all var(--transition-fast);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-color-light);

      &.active {
        background: var(--primary-gradient);
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

/* 搜索区域样式 */
.search-section {
  display: flex;
  gap: 12px;
  align-items: center;

  .search-box {
    flex: 1;
  }

  .filter-btn {
    padding: 12px;
    min-width: 44px;
    border-radius: var(--radius-md);

    .filter-icon {
      font-size: 16px;
    }
  }
}

/* 轮播图样式 */
.carousel-container {
  margin: var(--space-md) 0;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);

  .carousel-item {
    position: relative;
    height: 200px;
    background: var(--gradient-primary);

    .carousel-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .carousel-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
      padding: var(--space-lg);
      color: var(--text-white);

      .carousel-title {
        font-size: var(--font-lg);
        font-weight: var(--font-semibold);
        margin-bottom: var(--space-xs);
      }

      .carousel-subtitle {
        font-size: var(--font-sm);
        opacity: 0.9;
      }
    }
  }
}

/* 快捷操作样式 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 0 4px;

  .quick-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 8px;
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);

    &:active {
      transform: scale(0.95);
      box-shadow: var(--shadow-md);
    }
  }

  .quick-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-circle);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;

    &.study {
      background: linear-gradient(135deg, #E8F5E8 0%, #D4F5D4 100%);
    }

    &.course {
      background: linear-gradient(135deg, #E8F2FF 0%, #D4E8FF 100%);
    }

    &.exam {
      background: linear-gradient(135deg, #FFF5E8 0%, #FFE8D4 100%);
    }

    &.points {
      background: linear-gradient(135deg, #FFE8F5 0%, #FFD4E8 100%);
    }
  }

  .quick-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    text-align: center;
  }
}

/* 状态样式 */
.status {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
}

.status-dot.online {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-dot.offline {
  background: #6b7280;
}

.status-dot.busy {
  background: #f59e0b;
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, to { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0,-8px,0); }
  70% { transform: translate3d(0,-4px,0); }
  90% { transform: translate3d(0,-2px,0); }
}

.animate-fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.animate-slide-in {
  animation: slideIn var(--transition-normal) ease-out;
}

.animate-bounce {
  animation: bounce 2s ease-in-out infinite;
}

/* 响应式 */
@media (max-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }

  .btn-responsive {
    width: 100%;
  }

  .text-responsive {
    font-size: var(--font-sm);
  }
}
