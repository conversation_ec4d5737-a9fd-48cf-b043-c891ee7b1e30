<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.ExamExamMapper">

    <!-- 分页查询考试列表 -->
    <select id="selectExamPage" resultType="com.cy.education.model.entity.ExamExam">
        SELECT e.*
        FROM exam_exam e
        <if test="departmentId != null">
            INNER JOIN exam_exam_department ed ON e.id = ed.exam_id AND ed.department_id = #{departmentId}
        </if>
        <where>
            e.deleted = 0
            <if test="keyword != null and keyword != ''">
                AND (e.title LIKE CONCAT('%', #{keyword}, '%') OR e.description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="paperId != null">
                AND e.paper_id = #{paperId}
            </if>
            <if test="status != null">
                AND e.status = #{status}
            </if>
            <if test="isPublished != null">
                AND e.is_published = #{isPublished}
            </if>
        </where>
        <if test="sortBy != null and sortBy != ''">
            ORDER BY
            <choose>
                <when test="sortBy == 'createdAt'">e.created_at</when>
                <when test="sortBy == 'updatedAt'">e.updated_at</when>
                <when test="sortBy == 'startTime'">e.start_time</when>
                <when test="sortBy == 'endTime'">e.end_time</when>
                <when test="sortBy == 'title'">e.title</when>
                <otherwise>e.created_at</otherwise>
            </choose>
            <if test="sortOrder != null and sortOrder == 'asc'">ASC</if>
            <if test="sortOrder == null or sortOrder != 'asc'">DESC</if>
        </if>
        <if test="sortBy == null or sortBy == ''">
            ORDER BY e.created_at DESC
        </if>
    </select>
</mapper>
