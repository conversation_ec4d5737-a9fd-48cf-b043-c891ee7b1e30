package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.ExamQuestion;
import com.cy.education.model.vo.ExamQuestionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 题目Mapper接口
 */
@Mapper
public interface ExamQuestionMapper extends BaseMapper<ExamQuestion> {

    /**
     * 分页查询题目列表(包含题库名称)
     *
     * @param page 分页参数
     * @param keyword 关键词
     * @param bankId 题库ID
     * @param type 题目类型
     * @return 分页结果
     */
    @Select({
        "<script>",
        "SELECT q.*, b.name as bank_name FROM exam_question q",
        "LEFT JOIN exam_bank b ON q.bank_id = b.id",
        "WHERE q.deleted = 0 AND b.deleted = 0",
        "<if test='bankId != null'>",
        "  AND q.bank_id = #{bankId}",
        "</if>",
        "<if test='type != null and type != \"\"'>",
        "  AND q.type = #{type}",
        "</if>",
        "<if test='keyword != null and keyword != \"\"'>",
        "  AND (q.title LIKE CONCAT('%', #{keyword}, '%') OR q.explanation LIKE CONCAT('%', #{keyword}, '%'))",
        "</if>",
        "ORDER BY q.created_at DESC",
        "</script>"
    })
    IPage<ExamQuestionVO> pageQuestions(Page<ExamQuestionVO> page, 
                                      @Param("keyword") String keyword,
                                      @Param("bankId") Integer bankId,
                                      @Param("type") String type);

    /**
     * 获取题目详情(包含题库名称)
     *
     * @param id 题目ID
     * @return 题目详情
     */
    @Select("SELECT q.*, b.name as bank_name FROM exam_question q " +
           "LEFT JOIN exam_bank b ON q.bank_id = b.id " +
           "WHERE q.id = #{id} AND q.deleted = 0")
    ExamQuestionVO getQuestionDetail(@Param("id") Integer id);
} 