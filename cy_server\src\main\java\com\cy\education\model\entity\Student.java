package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学员实体类
 */
@Data
@TableName("students")
@Slf4j
public class Student implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学员ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 真实姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 职位
     */
    private String jobTitle;

    /**
     * 工号
     */
    private String employeeId;

    /**
     * 状态：0禁用，1启用
     */
    private Integer status;

    /**
     * 积分余额
     */
    private Integer points;

    /**
     * 入职时间
     */
    private LocalDate entryTime;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 部门名称（非数据库字段）
     */
    @TableField(exist = false)
    private String departmentName;
    
    /**
     * 注册时间（非数据库字段，前端显示用）
     */
    @TableField(exist = false)
    private String registerTime;
    
    /**
     * 获取部门名称，兼容前端department字段
     */
    public String getDepartment() {
        return this.departmentName;
    }
    
    // /**
    //  * 获取状态文本，兼容前端status字段
    //  * 在JSON序列化时，此方法返回值作为status字段
    //  */
    // @JsonProperty("status")
    // public String getStatus() {
    //     // 添加日志输出status值，帮助调试
    //     log.debug("转换status字段 - ID: {}, raw status value: {}", this.id, this.status);
        
    //     // 将任何非0值都视为启用状态
    //     if (this.status == null) {
    //         return "disabled";
    //     }
        
    //     int statusValue = this.status.intValue();
    //     return statusValue != 0 ? "active" : "disabled";
    // }
    
    /**
     * 设置状态值
     * 用于接收前端传入的status字段
     */
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    
    // /**
    //  * 设置状态值（字符串格式）
    //  * 用于接收前端传入的status字段
    //  */
    // @JsonProperty("status")
    // public void setStatus(String status) {
    //     this.status = "active".equals(status) ? 1 : 0;
    // }
} 