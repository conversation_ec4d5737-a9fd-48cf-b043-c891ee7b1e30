<template>
  <div class="practice-statistics-container">
    <div class="search-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="学员姓名" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入学员姓名"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="题库名称" prop="bankName">
          <el-input
            v-model="queryParams.bankName"
            placeholder="请输入题库名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="练习类型" prop="practiceType">
          <el-select v-model="queryParams.practiceType" placeholder="请选择练习类型" clearable>
            <el-option label="全部练习" value="all" />
            <el-option label="错题练习" value="wrong" />
          </el-select>
        </el-form-item>
        <el-form-item label="统计时间" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['exam:practice:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExportDetail"
            v-hasPermi="['exam:practice:export']"
          >导出详情</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ totalUsers }}</div>
              <div class="stat-label">参与练习人数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ totalPractices }}</div>
              <div class="stat-label">总练习次数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ totalQuestions }}</div>
              <div class="stat-label">总练习题数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ avgAccuracy }}%</div>
              <div class="stat-label">平均正确率</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="practiceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学员姓名" align="center" prop="userName" />
      <el-table-column label="题库名称" align="center" prop="bankName" />
      <el-table-column label="练习类型" align="center" prop="practiceType">
        <template #default="scope">
          <el-tag :type="scope.row.practiceType === 'all' ? 'primary' : 'warning'">
            {{ scope.row.practiceType === 'all' ? '全部练习' : '错题练习' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="总题数" align="center" prop="totalQuestions" />
      <el-table-column label="正确题数" align="center" prop="correctQuestions" />
      <el-table-column label="错误题数" align="center" prop="wrongQuestions" />
      <el-table-column label="未答题数" align="center" prop="unansweredQuestions" />
      <el-table-column label="正确率" align="center" prop="accuracy">
        <template #default="scope">
          <span :class="getAccuracyClass(scope.row.accuracy)">
            {{ scope.row.accuracy }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column label="练习时长" align="center" prop="duration">
        <template #default="scope">
          {{ formatDuration(scope.row.duration) }}
        </template>
      </el-table-column>
      <el-table-column label="练习时间" align="center" prop="practiceTime" width="180">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.practiceTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleDetail(scope.row)"
            v-hasPermi="['exam:practice:query']"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 练习详情对话框 -->
    <el-dialog title="练习详情" v-model="detailVisible" width="80%" append-to-body>
      <div class="practice-detail">
        <div class="detail-header">
          <h3>{{ currentDetail.userName }} - {{ currentDetail.bankName }}</h3>
          <div class="detail-stats">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="detail-stat-item">
                  <div class="stat-value">{{ currentDetail.totalQuestions }}</div>
                  <div class="stat-label">总题数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="detail-stat-item">
                  <div class="stat-value">{{ currentDetail.correctQuestions }}</div>
                  <div class="stat-label">正确题数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="detail-stat-item">
                  <div class="stat-value">{{ currentDetail.wrongQuestions }}</div>
                  <div class="stat-label">错误题数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="detail-stat-item">
                  <div class="stat-value">{{ currentDetail.accuracy }}%</div>
                  <div class="stat-label">正确率</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        
        <div class="detail-questions" v-if="currentDetail.questions && currentDetail.questions.length > 0">
          <h4>答题详情</h4>
          <el-table :data="currentDetail.questions" max-height="400">
            <el-table-column label="题目" prop="content" width="300" show-overflow-tooltip />
            <el-table-column label="题目类型" prop="type" width="100" align="center">
              <template #default="scope">
                <el-tag>{{ getQuestionTypeText(scope.row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="正确答案" prop="correctAnswer" width="120" align="center" />
            <el-table-column label="用户答案" prop="userAnswer" width="120" align="center" />
            <el-table-column label="答题结果" prop="isCorrect" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.isCorrect ? 'success' : 'danger'">
                  {{ scope.row.isCorrect ? '正确' : '错误' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { listPracticeStatistics, exportPracticeStatistics, exportPracticeDetail } from '@/api/practice'

// 定义类型接口
interface PracticeRecord {
  id: number
  userName: string
  bankName: string
  practiceType: string
  totalQuestions: number
  correctQuestions: number
  wrongQuestions: number
  unansweredQuestions: number
  accuracy: number
  duration: number
  practiceTime: string
  questions?: QuestionDetail[]
}

interface QuestionDetail {
  id: number
  content: string
  type: string
  correctAnswer: string
  userAnswer: string
  isCorrect: boolean
}

// 响应式数据
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const practiceList = ref<PracticeRecord[]>([])
const detailVisible = ref(false)
const currentDetail = ref<PracticeRecord>({} as PracticeRecord)

// 统计数据
const totalUsers = ref(0)
const totalPractices = ref(0)
const totalQuestions = ref(0)
const avgAccuracy = ref(0)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  userName: '',
  bankName: '',
  practiceType: '',
  dateRange: []
})

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 调用后端API获取练习统计数据
    const response = await listPracticeStatistics(queryParams)
    
    if (response.data) {
      practiceList.value = response.data.rows || []
      total.value = response.data.total || 0
      
      // 更新统计数据
      if (response.data.statistics) {
        totalUsers.value = response.data.statistics.totalUsers || 0
        totalPractices.value = response.data.statistics.totalPractices || 0
        totalQuestions.value = response.data.statistics.totalQuestions || 0
        avgAccuracy.value = response.data.statistics.avgAccuracy || 0
      }
    }
  } catch (error) {
    console.error('获取练习统计数据失败:', error)
    ElMessage.error('获取练习统计数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryParams.userName = ''
  queryParams.bankName = ''
  queryParams.practiceType = ''
  queryParams.dateRange = []
  queryParams.pageNum = 1
  getList()
}

// 多选框选中数据
const handleSelectionChange = (selection: PracticeRecord[]) => {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

// 导出
const handleExport = () => {
  ElMessageBox.confirm('是否确认导出所有练习统计数据项？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await exportPracticeStatistics(queryParams)
      
      // 创建下载链接
      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `练习统计数据_${new Date().toISOString().slice(0, 10)}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  })
}

// 导出详情
const handleExportDetail = () => {
  ElMessageBox.confirm('是否确认导出练习详情数据？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await exportPracticeDetail(queryParams)
      
      // 创建下载链接
      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `练习详情数据_${new Date().toISOString().slice(0, 10)}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  })
}

// 查看详情
const handleDetail = async (row: PracticeRecord) => {
  try {
    // 这里可以添加获取详细答题记录的API调用
    // const response = await getPracticeRecordDetail(row.id)
    
    currentDetail.value = {
      ...row,
      questions: [] // 实际应该从API获取详细的答题记录
    }
    detailVisible.value = true
  } catch (error) {
    console.error('获取练习详情失败:', error)
    ElMessage.error('获取练习详情失败')
  }
}

// 格式化时长
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}时${minutes}分${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 格式化时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 获取正确率样式
const getAccuracyClass = (accuracy: number) => {
  if (accuracy >= 90) return 'text-success'
  if (accuracy >= 70) return 'text-warning'
  return 'text-danger'
}

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'single': '单选题',
    'multiple': '多选题',
    'judge': '判断题',
    'fill': '填空题'
  }
  return typeMap[type] || '未知'
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
.practice-statistics-container {
  padding: 20px;
}

.search-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.statistics-overview {
  margin-bottom: 20px;
}

.stat-card {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.practice-detail {
  padding: 20px;
}

.detail-header {
  margin-bottom: 20px;
}

.detail-header h3 {
  margin-bottom: 16px;
  color: #333;
}

.detail-stats {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
}

.detail-stat-item {
  text-align: center;
  border-right: 1px solid #e4e4e4;
}

.detail-stat-item:last-child {
  border-right: none;
}

.detail-questions {
  margin-top: 20px;
}

.detail-questions h4 {
  margin-bottom: 12px;
  color: #333;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.mb8 {
  margin-bottom: 8px;
}
</style> 