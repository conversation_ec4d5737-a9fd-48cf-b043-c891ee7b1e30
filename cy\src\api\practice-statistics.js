import request from '@/utils/request'

// 获取学员练习统计列表
export function getPracticeStatistics(params) {
  return request({
    url: '/api/admin/practice-statistics/list',
    method: 'get',
    params
  })
}

// 获取学员练习统计详情
export function getPracticeStatisticsDetail(userId, bankId) {
  return request({
    url: `/api/admin/practice-statistics/detail/${userId}/${bankId}`,
    method: 'get'
  })
}

// 获取题库练习统计概览
export function getBankPracticeOverview(bankId) {
  return request({
    url: `/api/admin/practice-statistics/bank-overview/${bankId}`,
    method: 'get'
  })
}

// 获取全部练习统计概览
export function getAllPracticeOverview() {
  return request({
    url: '/api/admin/practice-statistics/overview',
    method: 'get'
  })
}

// 获取学员练习记录
export function getStudentPracticeRecords(userId, bankId = null) {
  return request({
    url: `/api/admin/practice-statistics/records/${userId}`,
    method: 'get',
    params: bankId ? { bankId } : {}
  })
}

// 导出练习统计数据
export function exportPracticeStatistics(params) {
  return request({
    url: '/api/admin/practice-statistics/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 获取练习统计图表数据
export function getPracticeStatisticsChartData(params) {
  return request({
    url: '/api/admin/practice-statistics/chart-data',
    method: 'get',
    params
  })
}
