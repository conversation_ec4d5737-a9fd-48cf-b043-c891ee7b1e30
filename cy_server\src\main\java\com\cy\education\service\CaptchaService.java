package com.cy.education.service;

import com.cy.education.model.vo.CaptchaResponseVO;

/**
 * 验证码服务接口
 */
public interface CaptchaService {
    
    /**
     * 生成验证码
     * @return 验证码响应
     */
    CaptchaResponseVO generateCaptcha();
    
    /**
     * 验证验证码
     * @param captchaId 验证码ID
     * @param code 用户输入的验证码
     * @return 验证结果
     */
    boolean verifyCaptcha(String captchaId, String code);
    
    /**
     * 清除验证码
     * @param captchaId 验证码ID
     */
    void clearCaptcha(String captchaId);
} 