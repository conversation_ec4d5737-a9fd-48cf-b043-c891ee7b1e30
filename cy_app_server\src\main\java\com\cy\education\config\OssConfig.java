package com.cy.education.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云OSS配置类
 */
@Configuration
public class OssConfig {
    /**
     * OSS bucket名称
     */
    @Value("${aliyun.oss.bucket:#{null}}")
    private String bucket;

    /**
     * OSS地域
     */
    @Value("${aliyun.oss.region:#{null}}")
    private String region;

    /**
     * OSS访问域名
     */
    @Value("${aliyun.oss.host:#{null}}")
    private String host;

    /**
     * 上传目录
     */
    @Value("${aliyun.oss.dir:upload}")
    private String uploadDir;

    /**
     * 签名有效期（秒）
     */
    @Value("${aliyun.oss.expireTime:3600}")
    private Long expireTime;

    /**
     * 文件大小限制（字节）
     */
    @Value("${aliyun.oss.maxSize:10485760}")
    private Long maxSize;

    public String getBucket() {
        return bucket;
    }

    public String getRegion() {
        return region;
    }

    public String getHost() {
        return host;
    }

    public String getUploadDir() {
        return uploadDir;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public Long getMaxSize() {
        return maxSize;
    }
} 