package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.Department;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 部门Mapper接口
 */
@Repository
public interface DepartmentMapper extends BaseMapper<Department> {
    
    /**
     * 查询部门列表，根据父部门ID
     *
     * @param parentId 父部门ID，如果为null则查询顶级部门
     * @return 部门列表
     */
    List<Department> selectDepartmentList(@Param("parentId") Integer parentId);
    
    /**
     * 查询部门及其子部门列表
     *
     * @param parentId 父部门ID，如果为null则查询所有部门
     * @return 部门树形列表
     */
    List<Department> selectDepartmentTreeList(@Param("parentId") Integer parentId);
    
    /**
     * 统计部门下的学员数量
     *
     * @param departmentId 部门ID
     * @return 学员数量
     */
    Integer countStudentsByDepartment(@Param("departmentId") Integer departmentId);
    
    /**
     * 查询子部门ID列表
     *
     * @param departmentId 部门ID
     * @return 子部门ID列表
     */
    List<Integer> selectChildDepartmentIds(@Param("departmentId") Integer departmentId);
} 