import { mockResponse, generateId } from './utils'
import type { Post, Comment, Category, ViolationContent } from '@/api/forum'

// 模拟论坛分类数据
export const categoryData: Category[] = [
  {
    id: 1,
    name: '学习交流',
    description: '分享学习心得与经验',
    icon: 'Reading',
    sort: 1,
    status: 1,
    postCount: 246,
    createTime: '2023-01-01 10:00:00',
    isParent: true,
    children: [
      {
        id: 4,
        name: '课程讨论',
        parentId: 1,
        description: '关于各课程的讨论区',
        icon: 'ChatLineRound',
        sort: 1,
        status: 1,
        postCount: 124,
        createTime: '2023-01-02 10:00:00'
      },
      {
        id: 5,
        name: '考试心得',
        parentId: 1,
        description: '分享考试经验和心得',
        icon: 'DocumentChecked',
        sort: 2,
        status: 1,
        postCount: 88,
        createTime: '2023-01-02 11:00:00'
      },
      {
        id: 6,
        name: '学习资料',
        parentId: 1,
        description: '分享各类学习资料',
        icon: 'Folder',
        sort: 3,
        status: 1,
        postCount: 34,
        createTime: '2023-01-02 12:00:00'
      }
    ]
  },
  {
    id: 2,
    name: '技术交流',
    description: '矿山技术交流与讨论',
    icon: 'Tools',
    sort: 2,
    status: 1,
    postCount: 189,
    createTime: '2023-01-01 11:00:00',
    isParent: true,
    children: [
      {
        id: 7,
        name: '采矿技术',
        parentId: 2,
        description: '采矿技术交流讨论',
        icon: 'Cpu',
        sort: 1,
        status: 1,
        postCount: 98,
        createTime: '2023-01-03 10:00:00'
      },
      {
        id: 8,
        name: '安全生产',
        parentId: 2,
        description: '安全生产经验分享',
        icon: 'AlarmClock',
        sort: 2,
        status: 1,
        postCount: 91,
        createTime: '2023-01-03 11:00:00'
      }
    ]
  },
  {
    id: 3,
    name: '休闲娱乐',
    description: '矿山生活分享与交流',
    icon: 'Coffee',
    sort: 3,
    status: 0,
    postCount: 156,
    createTime: '2023-01-01 12:00:00',
    isParent: true,
    children: [
      {
        id: 9,
        name: '生活分享',
        parentId: 3,
        description: '分享矿山生活点滴',
        icon: 'Picture',
        sort: 1,
        status: 0,
        postCount: 83,
        createTime: '2023-01-04 10:00:00'
      },
      {
        id: 10,
        name: '趣味故事',
        parentId: 3,
        description: '矿山有趣的人和事',
        icon: 'ChatDotSquare',
        sort: 2,
        status: 0,
        postCount: 73,
        createTime: '2023-01-04 11:00:00'
      }
    ]
  }
]

// 模拟帖子数据
export const postData: Post[] = [
  {
    id: 1,
    title: '安全生产的重要性讨论',
    content: '<p>安全生产是矿山工作的重中之重，希望大家能够分享一些在安全生产方面的经验和做法。</p><p>我认为首先要做好安全培训工作，确保每位员工都熟悉安全规程和操作流程。其次，定期进行安全检查和演练，及时发现和排除安全隐患。</p><p>大家有什么好的建议吗？</p>',
    author: '张三',
    authorId: 101,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    category: '安全生产',
    categoryId: 8,
    viewCount: 1250,
    replyCount: 28,
    createTime: '2024-01-10 14:30:25',
    status: 'approved',
    isTop: true,
    isEssence: false
  },
  {
    id: 2,
    title: '新型采矿设备使用心得分享',
    content: '<p>最近我们矿区引进了新型的智能采矿设备，使用了一段时间后，感觉效率提高了不少，想和大家分享一下使用心得。</p><p>1. 自动化程度高，减少了人工操作失误</p><p>2. 数据监测精确，可以实时调整作业参数</p><p>3. 安全性能更好，有多重保护机制</p><p>不过在使用过程中也发现了一些问题，比如...</p>',
    author: '李四',
    authorId: 102,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    category: '技术交流',
    categoryId: 7,
    viewCount: 890,
    replyCount: 15,
    createTime: '2024-01-10 10:15:42',
    status: 'pending',
    isTop: false,
    isEssence: true
  },
  {
    id: 3,
    title: '矿山环保措施建议',
    content: '<p>随着环保要求越来越严格，我们需要更加重视矿山的环保工作。以下是一些环保措施建议：</p><p>1. 加强粉尘控制，采用湿式作业</p><p>2. 完善废水处理系统，实现水资源循环利用</p><p>3. 推进绿色矿山建设，加强生态恢复</p><p>希望大家一起讨论如何更好地做好环保工作。</p>',
    author: '王五',
    authorId: 103,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    category: '技术交流',
    categoryId: 7,
    viewCount: 654,
    replyCount: 9,
    createTime: '2024-01-09 16:45:12',
    status: 'rejected',
    isTop: false,
    isEssence: false
  },
  {
    id: 4,
    title: '安全生产月活动总结',
    content: '<p>本月我们开展了一系列安全生产月活动，包括安全知识竞赛、应急演练、隐患排查等，效果很好。</p><p>通过这些活动，提高了大家的安全意识，也发现了一些工作中的安全隐患并及时进行了整改。</p><p>希望各位同事能够将安全生产的理念贯彻到日常工作中。</p>',
    author: '赵六',
    authorId: 104,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    category: '安全生产',
    categoryId: 8,
    viewCount: 765,
    replyCount: 12,
    createTime: '2024-01-08 09:20:15',
    status: 'approved',
    isTop: false,
    isEssence: true
  },
  {
    id: 5,
    title: '矿山工作生活趣事分享',
    content: '<p>在矿山工作这些年，经历了不少有趣的事情，今天和大家分享一下。</p><p>记得有一次夜班作业，突然停电，大家拿着头灯在井下，影子在墙上晃来晃去，场面特别壮观...</p><p>还有一次班组聚餐，老李喝多了，站起来非要唱歌，结果一开口把隔壁桌的人都吓跑了...</p>',
    author: '孙七',
    authorId: 105,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    category: '趣味故事',
    categoryId: 10,
    viewCount: 1025,
    replyCount: 25,
    createTime: '2024-01-07 16:35:40',
    status: 'approved',
    isTop: false,
    isEssence: false
  }
]

// 模拟评论数据（包含树形结构）
export const commentData: Comment[] = [
  {
    id: 1,
    content: '这个观点很有道理，我们部门也在实施类似的安全措施。',
    postId: 1,
    postTitle: '安全生产的重要性讨论',
    author: '赵六',
    authorId: 104,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    createTime: '2024-01-10 15:20:30',
    status: 'approved',
    children: [
      {
        id: 6,
        content: '你们部门是怎么做的？能详细说说吗？',
        postId: 1,
        postTitle: '安全生产的重要性讨论',
        parentId: 1,
        author: '张三',
        authorId: 101,
        authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        createTime: '2024-01-10 15:25:45',
        status: 'approved',
        children: [
          {
            id: 10,
            content: '我们主要是加强了日常检查和培训，每周都有安全专题会议。',
            postId: 1,
            postTitle: '安全生产的重要性讨论',
            parentId: 6,
            author: '赵六',
            authorId: 104,
            authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            createTime: '2024-01-10 15:30:20',
            status: 'approved'
          }
        ]
      }
    ]
  },
  {
    id: 2,
    content: '安全培训很重要，建议每月至少进行一次全员培训。',
    postId: 1,
    postTitle: '安全生产的重要性讨论',
    author: '李四',
    authorId: 102,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    createTime: '2024-01-10 15:40:15',
    status: 'approved',
    children: [
      {
        id: 7,
        content: '赞同这个观点，培训一定要形成制度化、常态化。',
        postId: 1,
        postTitle: '安全生产的重要性讨论',
        parentId: 2,
        author: '王五',
        authorId: 103,
        authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        createTime: '2024-01-10 15:45:30',
        status: 'pending'
      }
    ]
  },
  {
    id: 3,
    content: '支持楼主的想法，希望能够推广到全矿使用。',
    postId: 2,
    postTitle: '新型采矿设备使用心得分享',
    author: '孙七',
    authorId: 105,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    createTime: '2024-01-10 11:30:45',
    status: 'pending'
  },
  {
    id: 4,
    content: '这套设备的成本如何？投入产出比高吗？',
    postId: 2,
    postTitle: '新型采矿设备使用心得分享',
    author: '周八',
    authorId: 106,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    createTime: '2024-01-10 13:15:20',
    status: 'approved',
    children: [
      {
        id: 8,
        content: '初期投入确实较大，但长期来看效益显著，预计两年可以收回成本。',
        postId: 2,
        postTitle: '新型采矿设备使用心得分享',
        parentId: 4,
        author: '李四',
        authorId: 102,
        authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        createTime: '2024-01-10 14:20:10',
        status: 'approved'
      }
    ]
  },
  {
    id: 5,
    content: '你说的第三点很重要，环保必须放在首位。',
    postId: 3,
    postTitle: '矿山环保措施建议',
    author: '吴九',
    authorId: 107,
    authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    createTime: '2024-01-09 17:30:25',
    status: 'rejected',
    children: [
      {
        id: 9,
        content: '我们矿已经开始实施绿色矿山建设，效果不错。',
        postId: 3,
        postTitle: '矿山环保措施建议',
        parentId: 5,
        author: '郑十',
        authorId: 108,
        authorAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        createTime: '2024-01-09 18:10:35',
        status: 'rejected'
      }
    ]
  }
]

// 模拟违规内容数据
export const violationData: ViolationContent[] = [
  {
    id: 1,
    contentType: 'post',
    contentId: 3,
    content: '矿山环保措施建议',
    reporter: '管理员',
    reporterId: 999,
    reportReason: '内容包含虚假信息',
    reportTime: '2024-01-09 17:00:00',
    status: 'pending'
  },
  {
    id: 2,
    contentType: 'comment',
    contentId: 5,
    content: '你说的第三点很重要，环保必须放在首位。',
    reporter: '管理员',
    reporterId: 999,
    reportReason: '评论无意义',
    reportTime: '2024-01-09 18:00:00',
    status: 'processed',
    processResult: '删除评论',
    processTime: '2024-01-09 19:00:00',
    operator: '超级管理员'
  },
  {
    id: 3,
    contentType: 'comment',
    contentId: 9,
    content: '我们矿已经开始实施绿色矿山建设，效果不错。',
    reporter: '李四',
    reporterId: 102,
    reportReason: '评论含广告信息',
    reportTime: '2024-01-09 19:30:00',
    status: 'ignored',
    processResult: '检查后未发现违规',
    processTime: '2024-01-10 09:15:00',
    operator: '管理员A'
  }
]

// 获取扁平化的评论列表（用于评论管理页面）
export const getFlatComments = (): Comment[] => {
  const result: Comment[] = []
  
  const flattenComments = (comments: Comment[]) => {
    comments.forEach(comment => {
      const { children, ...rest } = comment
      result.push(rest)
      
      if (children && children.length > 0) {
        flattenComments(children)
      }
    })
  }
  
  flattenComments(commentData)
  return result
}

// 递归构建评论树
export const buildCommentTree = (comments: Comment[]): Comment[] => {
  const commentMap = new Map<number, Comment>()
  const rootComments: Comment[] = []
  
  // 先构建一个以id为key的Map
  comments.forEach(comment => {
    commentMap.set(comment.id, { ...comment, children: [] })
  })
  
  // 构建树结构
  comments.forEach(comment => {
    const currentComment = commentMap.get(comment.id)
    if (currentComment) {
      if (comment.parentId) {
        const parentComment = commentMap.get(comment.parentId)
        if (parentComment) {
          if (!parentComment.children) {
            parentComment.children = []
          }
          parentComment.children.push(currentComment)
        }
      } else {
        rootComments.push(currentComment)
      }
    }
  })
  
  return rootComments
}

// 获取帖子列表
export function mockGetPostList(params: {
  keyword?: string
  status?: string
  categoryId?: number
  page?: number
  size?: number
}) {
  let filteredPosts = [...postData]
  
  // 关键字过滤（标题或作者）
  if (params.keyword) {
    filteredPosts = filteredPosts.filter(post => 
      post.title.includes(params.keyword!) || 
      post.author.includes(params.keyword!)
    )
  }
  
  // 状态过滤
  if (params.status) {
    filteredPosts = filteredPosts.filter(post => post.status === params.status)
  }
  
  // 分类过滤
  if (params.categoryId) {
    filteredPosts = filteredPosts.filter(post => post.categoryId === params.categoryId)
  }
  
  // 计算总数
  const total = filteredPosts.length
  
  // 分页
  const page = params.page || 1
  const size = params.size || 10
  const start = (page - 1) * size
  const end = start + size
  
  filteredPosts = filteredPosts.slice(start, end)
  
  return mockResponse({
    list: filteredPosts,
    total
  })
}

// 获取帖子详情
export function mockGetPostById(id: number) {
  const post = postData.find(post => post.id === id)
  
  if (post) {
    return mockResponse(post)
  }
  
  return mockResponse(null, 404, '帖子不存在')
}

// 审核帖子
export function mockReviewPost(id: number, status: 'approved' | 'rejected', reason?: string) {
  const post = postData.find(post => post.id === id)
  
  if (!post) {
    return mockResponse(null, 404, '帖子不存在')
  }
  
  post.status = status
  
  return mockResponse({ success: true })
}

// 设置帖子置顶
export function mockSetPostTop(id: number, isTop: boolean) {
  const post = postData.find(post => post.id === id)
  
  if (!post) {
    return mockResponse(null, 404, '帖子不存在')
  }
  
  post.isTop = isTop
  
  return mockResponse({ success: true })
}

// 设置帖子精华
export function mockSetPostEssence(id: number, isEssence: boolean) {
  const post = postData.find(post => post.id === id)
  
  if (!post) {
    return mockResponse(null, 404, '帖子不存在')
  }
  
  post.isEssence = isEssence
  
  return mockResponse({ success: true })
}

// 删除帖子
export function mockDeletePost(id: number) {
  const index = postData.findIndex(post => post.id === id)
  
  if (index === -1) {
    return mockResponse(null, 404, '帖子不存在')
  }
  
  postData.splice(index, 1)
  
  return mockResponse({ success: true })
}

// 获取评论列表
export function mockGetCommentList(params: {
  keyword?: string
  status?: string
  postId?: number
  page?: number
  size?: number
}) {
  // 获取扁平化的评论列表
  let filteredComments = getFlatComments()
  
  // 关键字过滤（内容或作者）
  if (params.keyword) {
    filteredComments = filteredComments.filter(comment => 
      comment.content.includes(params.keyword!) || 
      comment.author.includes(params.keyword!)
    )
  }
  
  // 状态过滤
  if (params.status) {
    filteredComments = filteredComments.filter(comment => comment.status === params.status)
  }
  
  // 帖子ID过滤
  if (params.postId) {
    filteredComments = filteredComments.filter(comment => comment.postId === params.postId)
  }
  
  // 计算总数
  const total = filteredComments.length
  
  // 分页
  const page = params.page || 1
  const size = params.size || 10
  const start = (page - 1) * size
  const end = start + size
  
  filteredComments = filteredComments.slice(start, end)
  
  return mockResponse({
    list: filteredComments,
    total
  })
}

// 获取帖子的评论列表（树形结构）
export function mockGetPostComments(postId: number) {
  // 先过滤出属于该帖子的评论
  const postComments = commentData.filter(comment => comment.postId === postId)
  
  return mockResponse(postComments)
}

// 获取评论详情
export function mockGetCommentById(id: number) {
  // 从扁平化的评论列表中查找
  const comment = getFlatComments().find(comment => comment.id === id)
  
  if (comment) {
    return mockResponse(comment)
  }
  
  return mockResponse(null, 404, '评论不存在')
}

// 审核评论
export function mockReviewComment(id: number, status: 'approved' | 'rejected', reason?: string) {
  // 递归查找评论并更新状态
  const updateCommentStatus = (comments: Comment[]) => {
    for (const comment of comments) {
      if (comment.id === id) {
        comment.status = status
        return true
      }
      
      if (comment.children && comment.children.length > 0) {
        if (updateCommentStatus(comment.children)) {
          return true
        }
      }
    }
    
    return false
  }
  
  if (updateCommentStatus(commentData)) {
    return mockResponse({ success: true })
  }
  
  return mockResponse(null, 404, '评论不存在')
}

// 删除评论
export function mockDeleteComment(id: number) {
  // 递归查找评论并删除
  const deleteCommentById = (comments: Comment[], parentArray?: Comment[]) => {
    for (let i = 0; i < comments.length; i++) {
      if (comments[i].id === id) {
        if (parentArray) {
          parentArray.splice(i, 1)
        } else {
          comments.splice(i, 1)
        }
        return true
      }
      
      if (comments[i].children && comments[i].children.length > 0) {
        const childrenArray = comments[i].children as Comment[];
        if (deleteCommentById(childrenArray, childrenArray)) {
          return true
        }
      }
    }
    
    return false
  }
  
  if (deleteCommentById(commentData)) {
    return mockResponse({ success: true })
  }
  
  return mockResponse(null, 404, '评论不存在')
}

// 获取分类列表
export function mockGetCategoryList() {
  return mockResponse(categoryData)
}

// 获取违规内容列表
export function mockGetViolationList(params: {
  contentType?: 'post' | 'comment'
  status?: string
  page?: number
  size?: number
}) {
  let filteredViolations = [...violationData]
  
  // 内容类型过滤
  if (params.contentType) {
    filteredViolations = filteredViolations.filter(violation => 
      violation.contentType === params.contentType
    )
  }
  
  // 状态过滤
  if (params.status) {
    filteredViolations = filteredViolations.filter(violation => 
      violation.status === params.status
    )
  }
  
  // 计算总数
  const total = filteredViolations.length
  
  // 分页
  const page = params.page || 1
  const size = params.size || 10
  const start = (page - 1) * size
  const end = start + size
  
  filteredViolations = filteredViolations.slice(start, end)
  
  return mockResponse({
    list: filteredViolations,
    total
  })
}

// 获取违规内容详情
export function mockGetViolationById(id: number) {
  const violation = violationData.find(violation => violation.id === id)
  
  if (violation) {
    return mockResponse(violation)
  }
  
  return mockResponse(null, 404, '违规内容不存在')
}

// 处理违规内容
export function mockProcessViolation(id: number, status: 'processed' | 'ignored', result?: string) {
  const violation = violationData.find(violation => violation.id === id)
  
  if (!violation) {
    return mockResponse(null, 404, '违规内容不存在')
  }
  
  violation.status = status
  violation.processResult = result || '已处理'
  violation.processTime = new Date().toLocaleString()
  violation.operator = '当前管理员'
  
  return mockResponse({ success: true })
} 