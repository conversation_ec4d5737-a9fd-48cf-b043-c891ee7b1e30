package com.cy.education.model.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考试记录查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("考试记录查询参数")
public class ExamRecordQueryParams extends PageParams {
    
    /**
     * 考试ID
     */
    @ApiModelProperty("考试ID")
    private Integer examId;
    
    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    private Integer departmentId;
    
    /**
     * 关键字(用户名)
     */
    @ApiModelProperty("关键字(用户名)")
    private String keyword;
    
    /**
     * 状态(0-未开始,1-进行中,2-已完成,3-超时)
     */
    @ApiModelProperty("状态(0-未开始,1-进行中,2-已完成,3-超时)")
    private Integer status;
    
    /**
     * 是否通过
     */
    @ApiModelProperty("是否通过")
    private Boolean isPassed;
    
    /**
     * 排序字段
     */
    @ApiModelProperty("排序字段")
    private String sortBy = "created_at";
    
    /**
     * 排序方式(asc/desc)
     */
    @ApiModelProperty("排序方式(asc/desc)")
    private String sortOrder = "desc";
} 