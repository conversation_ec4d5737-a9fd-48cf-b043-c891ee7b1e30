<template>
  <div class="student-info">
    <div class="page-container">
      <div class="page-toolbar">
        <div class="toolbar-left">
          <h3>学员信息管理</h3>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索学员姓名或手机号"
            style="width: 250px; margin-right: 12px;"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" :icon="Plus" @click="handleAdd">新增学员</el-button>
          <el-button :icon="Upload" @click="handleImport">批量导入</el-button>
          <el-button :icon="Download" @click="handleExport">导出学员</el-button>
        </div>
      </div>

      <div class="student-content">
        <!-- 左侧部门树 -->
        <div class="department-tree">
          <div class="tree-header">
            <h4>部门列表</h4>
            <el-button text :icon="Refresh" @click="refreshDepartments">刷新</el-button>
          </div>
          <el-tree
            :data="departmentTree"
            :props="treeProps"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            @node-click="handleDepartmentClick"
          >
            <template #default="{ node, data }">
              <span class="tree-node">
                <el-icon><OfficeBuilding /></el-icon>
                <span style="margin-left: 6px;">{{ node.label }}</span>
                <!-- <span style="color: #999; margin-left: 8px;">({{ data.count || 0 }})</span> -->
              </span>
            </template>
          </el-tree>
        </div>

        <!-- 右侧学员表格 -->
        <div class="student-table">
          <el-table
            :data="studentList"
            v-loading="loading"
            height="600"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="avatar" label="头像" width="80">
              <template #default="{ row }">
                <el-avatar :size="40" :src="row.avatar" />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" min-width="100" />
            <el-table-column prop="phone" label="手机号" min-width="120" />
            <el-table-column prop="department" label="所属部门" min-width="120" />
            <el-table-column prop="registerTime" label="注册时间" min-width="150" />
            <el-table-column prop="lastLoginTime" label="最后登录" min-width="150" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'" @click="handleStatusChange(row)" style="cursor: pointer">
                  {{ row.status === 1 ? '正常' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="积分" width="80" />
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="{ row }">
                <el-button text type="primary" @click="handleEdit(row)">编辑</el-button>
                <el-button text type="success" @click="handleViewPoints(row)">积分记录</el-button>
                <el-button text type="warning" @click="handleViewPosts(row)">帖子</el-button>
                <el-button text type="info" @click="handleViewStudy(row)">学习记录</el-button>
                <el-dropdown>
                  <el-button text>
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleResetPassword(row)">重置密码</el-dropdown-item>
                      <el-dropdown-item @click="handleViewExam(row)">考试记录</el-dropdown-item>
                      <el-dropdown-item divided @click="handleDelete(row)">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.size"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑学员弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑学员' : '新增学员'"
      width="600px"
      destroy-on-close
    >
      <el-form :model="studentForm" :rules="studentRules" ref="studentFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="studentForm.name" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="studentForm.phone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="!isEdit">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input v-model="studentForm.password" type="password" placeholder="请输入密码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input v-model="studentForm.confirmPassword" type="password" placeholder="请确认密码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属部门" prop="departmentId">
              <el-select v-model="studentForm.departmentId" placeholder="请选择部门" style="width: 100%;">
                <el-option
                  v-for="dept in flatDepartments"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入职时间" prop="entryTime">
              <el-date-picker
                v-model="studentForm.entryTime"
                type="date"
                placeholder="请选择入职时间"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="studentForm.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 使用新的导入组件 -->
    <StudentImportDialog
      v-model="importDialogVisible"
      @success="handleImportSuccess"
    />

    <!-- 使用新的导出组件 -->
    <StudentExportDialog
      v-model="exportDialogVisible"
      :selected-students="selectedStudents"
      :search-params="searchForm"
      :total-count="total"
      @success="handleExportSuccess"
    />

    <!-- 使用组件替换原有的dialog -->
    <PointsRecordDialog v-model="pointsDialogVisible" :student="currentStudent" />
    <PostsRecordDialog v-model="postsDialogVisible" :student="currentStudent" />
    <StudyRecordDialog v-model="studyDialogVisible" :student="currentStudent" />
    <ExamRecordDialog v-model="examDialogVisible" :student="currentStudent" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  Search,
  Plus,
  Upload,
  Download,
  Refresh,
  OfficeBuilding,
  ArrowDown,
  UploadFilled
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getStudentList,
  getStudentById,
  addStudent,
  updateStudent,
  deleteStudent,
  resetStudentPassword,
  updateStudentStatus,
  exportStudentList,
  exportStudentsV2,
  importStudents,
  type Student,
  type StudentQueryParams
} from '@/api/student'
import { getDepartmentTree, type Department } from '@/api/department'
import PointsRecordDialog from '@/components/student/PointsRecordDialog.vue'
import PostsRecordDialog from '@/components/student/PostsRecordDialog.vue'
import StudyRecordDialog from '@/components/student/StudyRecordDialog.vue'
import ExamRecordDialog from '@/components/student/ExamRecordDialog.vue'
import StudentImportDialog from '@/components/student/StudentImportDialog.vue'
import StudentExportDialog from '@/components/student/StudentExportDialog.vue'

const loading = ref(false)
const dialogVisible = ref(false)
const importDialogVisible = ref(false)
const exportDialogVisible = ref(false)
const isEdit = ref(false)
const selectedStudents = ref<Student[]>([])

// 部门树配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 部门树数据
const departmentTree = ref<Department[]>([])

// 学员列表数据
const studentList = ref<Student[]>([])

// 扁平化部门数据（用于下拉选择）
const flatDepartments = ref<{ id: number, name: string }[]>([])

// 搜索表单
const searchForm = reactive<StudentQueryParams>({
  keyword: '',
  departmentId: undefined,
  status: undefined,
  page: 1,
  size: 20
})

// Dialog相关状态
const pointsDialogVisible = ref(false)
const postsDialogVisible = ref(false)
const studyDialogVisible = ref(false)
const examDialogVisible = ref(false)
const currentStudent = ref<Student | null>(null)

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 学员表单类型，确保与API接口兼容
interface StudentForm {
  id?: number;
  name: string;
  phone: string;
  password: string;
  confirmPassword: string;
  departmentId?: number;
  entryTime: Date | string | null;
  remark: string;
}

// 学员表单
const studentForm = reactive<StudentForm>({
  name: '',
  phone: '',
  password: '',
  confirmPassword: '',
  departmentId: undefined,
  entryTime: null,
  remark: ''
})

// 表单验证规则
const studentRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度6-20位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_: any, value: string, callback: Function) => {
        if (value !== studentForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  departmentId: [{ required: true, message: '请选择部门', trigger: 'change' }]
}

const studentFormRef = ref()

// 处理部门点击
const handleDepartmentClick = (data: Department) => {
  searchForm.departmentId = data.id
  handleSearch()
}

// 搜索
const handleSearch = () => {
  searchForm.page = 1
  loadStudentList()
}

// 加载学员列表
const loadStudentList = async () => {
  loading.value = true
  try {
    const params: StudentQueryParams = {
      keyword: searchForm.keyword,
      departmentId: searchForm.departmentId,
      status: searchForm.status,
      page: pagination.page,
      size: pagination.size
    }
    
    const res = await getStudentList(params)
    studentList.value = res.list
    pagination.total = res.total
  } catch (error) {
    console.error('获取学员列表失败:', error)
    ElMessage.error('获取学员列表失败')
  } finally {
    loading.value = false
  }
}

// 加载部门树
const loadDepartmentTree = async () => {
  try {
    const res = await getDepartmentTree()
    departmentTree.value = res
    
    // 生成扁平化部门列表，用于表单选择
    const flattenDepts: { id: number, name: string }[] = []
    
    const traverse = (departments: Department[], parentName = '') => {
      departments.forEach(dept => {
        const displayName = parentName ? `${parentName} / ${dept.name}` : dept.name
        flattenDepts.push({ id: dept.id, name: displayName })
        
        if (dept.children && dept.children.length > 0) {
          traverse(dept.children, displayName)
        }
      })
    }
    
    traverse(res)
    flatDepartments.value = flattenDepts
  } catch (error) {
    console.error('获取部门树失败:', error)
    ElMessage.error('获取部门树失败')
  }
}

// 新增学员
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑学员
const handleEdit = async (row: Student) => {
  try {
    loading.value = true
    const student = await getStudentById(row.id)
    
    isEdit.value = true
    resetForm()
    
    // 赋值给表单
    Object.assign(studentForm, {
      id: student.id,
      name: student.name,
      phone: student.phone,
      departmentId: student.departmentId,
      entryTime: student.entryTime ? new Date(student.entryTime) : null,
      remark: student.remark || ''
    })
    
    dialogVisible.value = true
  } catch (error) {
    console.error('获取学员详情失败:', error)
    ElMessage.error('获取学员详情失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(studentForm, {
    id: undefined,
    name: '',
    phone: '',
    password: '',
    confirmPassword: '',
    departmentId: undefined,
    entryTime: null,
    remark: ''
  })
  studentFormRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = () => {
  studentFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true
        
        // 创建要提交的数据，不包含confirmPassword
        const submitData = {
          id: studentForm.id,
          name: studentForm.name,
          phone: studentForm.phone,
          departmentId: studentForm.departmentId,
          remark: studentForm.remark
        }
        
        if (isEdit.value) {
          // 编辑学员
          await updateStudent(submitData)
          ElMessage.success('编辑成功')
        } else {
          // 新增学员需要密码
          const addData = {
            ...submitData,
            password: studentForm.password,
            entryTime: studentForm.entryTime instanceof Date 
              ? studentForm.entryTime.toISOString().split('T')[0] 
              : studentForm.entryTime
          }
          await addStudent(addData as any)
          ElMessage.success('新增成功')
        }
        
        dialogVisible.value = false
        loadStudentList()
      } catch (error) {
        console.error('保存学员失败:', error)
        ElMessage.error('保存失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 删除学员
const handleDelete = (row: Student) => {
  ElMessageBox.confirm('确定要删除该学员吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true
      await deleteStudent(row.id)
      ElMessage.success('删除成功')
      
      // 如果当前页只有一条数据，且不是第一页，则返回上一页
      if (studentList.value.length === 1 && pagination.page > 1) {
        pagination.page--
      }
      
      loadStudentList()
    } catch (error) {
      console.error('删除学员失败:', error)
      ElMessage.error('删除失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {})
}

// 重置密码
const handleResetPassword = (row: Student) => {
  ElMessageBox.confirm('确定要重置该学员的密码吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true
      const res = await resetStudentPassword(row.id)
      ElMessage.success(`密码重置成功，新密码为：${res.password}`)
    } catch (error) {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    } finally {
      loading.value = false
    }
  }).catch(() => {})
}

// 查看积分记录
const handleViewPoints = (row: Student) => {
  currentStudent.value = row
  pointsDialogVisible.value = true
}

// 查看帖子
const handleViewPosts = (row: Student) => {
  currentStudent.value = row
  postsDialogVisible.value = true
}

// 查看学习记录
const handleViewStudy = (row: Student) => {
  currentStudent.value = row
  studyDialogVisible.value = true
}

// 查看考试记录
const handleViewExam = (row: Student) => {
  currentStudent.value = row
  examDialogVisible.value = true
}

// 批量导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导出学员
const handleExport = () => {
  exportDialogVisible.value = true
}

// 导出成功处理
const handleExportSuccess = () => {
  // 导出成功后的处理，如果需要的话
}

// 导入相关
const uploadRef = ref()
const previewData = ref<any[]>([])
const validationErrors = ref<string[]>([])

// 学员模板配置
const studentHeaders = [
  { key: 'username', title: '用户名', width: 15, required: true },
  { key: 'name', title: '真实姓名', width: 15, required: true },
  { key: 'email', title: '邮箱', width: 25, required: true },
  { key: 'phone', title: '手机号', width: 15, required: true },
  { key: 'departmentName', title: '部门', width: 20, required: true },
  { key: 'status', title: '状态', width: 10, required: true }
]

// 下载模板
const downloadTemplate = () => {
  const sampleData = [
    {
      username: 'student001',
      name: '张三',
      email: '<EMAIL>',
      phone: '13800138001',
      departmentName: '技术部',
      status: '启用'
    },
    {
      username: 'student002',
      name: '李四',
      email: '<EMAIL>',
      phone: '13800138002',
      departmentName: '市场部',
      status: '启用'
    }
  ]

  ExcelUtils.createTemplate(
    studentHeaders,
    sampleData,
    '学员导入模板.xlsx',
    '学员模板'
  )
}

// 文件变化
const handleFileChange = async (file: any) => {
  try {
    const rawData = await ExcelUtils.readExcelFile(file.raw)

    // 验证数据
    const validation = ExcelUtils.validateExcelData(rawData, [
      ...studentHeaders,
      {
        key: 'email',
        title: '邮箱',
        required: true,
        validator: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
      },
      {
        key: 'phone',
        title: '手机号',
        required: true,
        validator: (value: string) => /^1[3-9]\d{9}$/.test(value)
      }
    ])

    previewData.value = validation.validData
    validationErrors.value = validation.errors

    if (validation.valid) {
      ElMessage.success(`数据验证通过，共 ${validation.validData.length} 条记录`)
    } else {
      ElMessage.warning(`数据验证失败，发现 ${validation.errors.length} 个错误`)
    }
  } catch (error) {
    console.error('文件解析失败:', error)
    ElMessage.error('文件解析失败，请检查文件格式')
  }
}

// 导入提交
const handleImportSubmit = async () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('请先上传文件')
    return
  }

  if (validationErrors.value.length > 0) {
    ElMessage.error('请先修复数据错误')
    return
  }

  try {
    loading.value = true
    // 调用导入API
    // 创建一个临时文件来模拟上传
    const blob = new Blob([JSON.stringify(previewData.value)], { type: 'application/json' })
    const file = new File([blob], 'students.json', { type: 'application/json' })
    await importStudents(file)
    ElMessage.success(`成功导入 ${previewData.value.length} 条学员记录`)
    importDialogVisible.value = false
    loadStudentList()
  } catch (error) {
    console.error('导入学员失败:', error)
    ElMessage.error('导入失败')
  } finally {
    loading.value = false
  }
}

// 导入成功处理
const handleImportSuccess = () => {
  ElMessage.success('学员导入成功')
  loadStudentList() // 刷新列表
}

// 刷新部门
const refreshDepartments = () => {
  loadDepartmentTree()
  ElMessage.success('部门数据已刷新')
}

// 表格选择变化
const handleSelectionChange = (selection: Student[]) => {
  selectedStudents.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  searchForm.size = size
  loadStudentList()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  searchForm.page = page
  loadStudentList()
}

// 更改学员状态
const handleStatusChange = (row: Student) => {
  const newStatus = row.status === 1 ? 0 : 1;
  const statusText = newStatus === 1 ? '启用' : '禁用';
  
  ElMessageBox.confirm(`确定要${statusText}该学员吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true;
      await updateStudentStatus(row.id, newStatus);
      ElMessage.success(`${statusText}成功`);
      loadStudentList();
    } catch (error) {
      console.error(`${statusText}失败:`, error);
      ElMessage.error(`${statusText}失败`);
    } finally {
      loading.value = false;
    }
  }).catch(() => {});
}



onMounted(() => {
  loadDepartmentTree()
  loadStudentList()
})
</script>

<style scoped>
.student-content {
  display: flex;
  gap: 24px;
  margin-top: 20px;
}

.department-tree {
  width: 280px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
  height: fit-content;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.tree-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.student-table {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-tree-node__content) {
  height: 36px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-upload-dragger) {
  width: 100%;
}


</style> 