package com.cy.education.controller;

import com.cy.education.model.entity.Student;
import com.cy.education.model.params.ChangePasswordParams;
import com.cy.education.model.params.LoginParams;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.LoginResponseVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.AuthService;
import com.cy.education.service.StudentService;
import com.cy.education.utils.SecurityUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private AuthService authService;

    @Autowired
    private StudentService studentService;

    /**
     * 学员登录
     */
    @ApiOperation("学员登录")
    @PostMapping("/login")
    public ApiResponse<LoginResponseVO> login(@Validated @RequestBody LoginParams loginParams) {
        LoginResponseVO loginResponse = authService.login(loginParams);
        return ApiResponse.success(loginResponse);
    }

    /**
     * 学员登出
     */
    @ApiOperation("学员登出")
    @PostMapping("/logout")
    public ApiResponse<Boolean> logout() {
        boolean result = authService.logout();
        return ApiResponse.success(result);
    }

    /**
     * 获取当前学员信息
     */
    @ApiOperation("获取当前学员信息")
    @GetMapping("/info")
    public ApiResponse<Student> getUserInfo() {
        Integer currentUserId = SecurityUtil.getCurrentUserId();
        try {
            Student student = studentService.getStudentById(currentUserId);
            student.setPassword(null);
            return ApiResponse.success(student);
        } catch (Exception e) {
            return ApiResponse.error("查询学员详情失败");
        }
    }

    /**
     * 更新学员信息
     */
    @ApiOperation("更新学员信息")
    @PutMapping("/update")
    public ApiResponse<Map<String, Boolean>> update(@Validated @RequestBody Student student) {
        try {
            boolean success = studentService.updateStudent(student);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新信息成功");
        } catch (Exception e) {
            return ApiResponse.error("更新信息失败");
        }
    }

    /**
     * 修改密码
     */
    @ApiOperation("修改密码")
    @PostMapping("/change-password")
    public ApiResponse<Boolean> changePassword(@Validated @RequestBody ChangePasswordParams params) {
        try {
            boolean result = authService.changePassword(params);
            if (!result) {
                return ApiResponse.error("修改密码失败");
            }
            return ApiResponse.success(true, "修改密码成功");
        } catch (Exception e) {
            return ApiResponse.error("修改密码失败: " + e.getMessage());
        }
    }

    // =========================== 论坛相关用户功能 ===========================

    /**
     * 获取用户资料（用于论坛展示）
     */
    @ApiOperation("获取用户资料")
    @GetMapping("/{userId}/profile")
    public ApiResponse<Map<String, Object>> getUserProfile(@PathVariable Integer userId) {
        try {
            Integer currentUserId = SecurityUtil.getCurrentUserId();
            Map<String, Object> profile = studentService.getUserProfile(userId, currentUserId);
            return ApiResponse.success(profile);
        } catch (Exception e) {
            return ApiResponse.error("获取用户资料失败");
        }
    }

    /**
     * 获取用户的粉丝列表
     */
    @ApiOperation("获取用户的粉丝列表")
    @GetMapping("/{userId}/followers")
    public ApiResponse<PageResponse<Map<String, Object>>> getUserFollowers(
            @PathVariable Integer userId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            PageResponse<Map<String, Object>> result = studentService.getUserFollowers(userId, pageNum, pageSize);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("获取用户粉丝失败");
        }
    }

    /**
     * 获取用户关注的用户列表
     */
    @ApiOperation("获取用户关注的用户列表")
    @GetMapping("/{userId}/following")
    public ApiResponse<PageResponse<Map<String, Object>>> getUserFollowing(
            @PathVariable Integer userId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            PageResponse<Map<String, Object>> result = studentService.getUserFollowing(userId, pageNum, pageSize);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("获取用户关注列表失败");
        }
    }
}
