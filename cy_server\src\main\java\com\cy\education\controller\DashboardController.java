package com.cy.education.controller;

import com.cy.education.model.vo.ApiResponse;
import com.cy.education.service.DashboardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Dashboard控制器
 */
@Api(tags = "Dashboard接口")
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    /**
     * 获取Dashboard统计数据
     */
    @ApiOperation("获取Dashboard统计数据")
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getStatistics() {
        Map<String, Object> statistics = dashboardService.getStatistics();
        return ApiResponse.success(statistics);
    }

    /**
     * 获取最新数据
     */
    @ApiOperation("获取最新数据")
    @GetMapping("/recent")
    public ApiResponse<Map<String, Object>> getRecentData() {
        Map<String, Object> recentData = dashboardService.getRecentData();
        return ApiResponse.success(recentData);
    }
}
