package com.cy.education.model.params;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 课程查询参数
 */
@Data
public class CourseQueryParams extends PageParams {
    
    /**
     * 搜索关键词
     */
    private String keyword;
    
    /**
     * 课程级别：beginner, intermediate, advanced
     */
    private String level;
    
    /**
     * 发布状态：0草稿，1已发布
     */
    private Integer status;
    
    /**
     * 开始日期
     */
    private LocalDateTime startDate;
    
    /**
     * 结束日期
     */
    private LocalDateTime endDate;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向：asc, desc
     */
    private String sortOrder;
} 