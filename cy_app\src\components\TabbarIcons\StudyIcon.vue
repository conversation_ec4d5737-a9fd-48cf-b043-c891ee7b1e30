<template>
	<svg :width="size" :height="size" viewBox="0 0 24 24" :fill="active ? '#fff' : '#8E8E93'">
		<path v-if="active" d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
		<path v-else d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4zm2 14h8v-2H8v2zm0-4h8v-2H8v2z"/>
	</svg>
</template>

<script setup lang="ts">
interface Props {
	size?: string | number
	color?: string
	active?: boolean
}

withDefaults(defineProps<Props>(), {
	size: 24,
	color: 'currentColor',
	active: false
})
</script>