package com.cy.education.service.study;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cy.education.model.entity.study.Course;
import com.cy.education.model.vo.CourseVO;
import com.cy.education.model.vo.PageResponse;

public interface CourseService extends IService<Course> {
    PageResponse<Course> getCourseList(Integer page, Integer size, String name);

    CourseVO getCourseByIdWithStudyRecord(Integer id);
}
