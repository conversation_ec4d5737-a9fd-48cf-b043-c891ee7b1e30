<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.StudyRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="StudyRecordResultMap" type="com.cy.education.model.entity.StudyRecord">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="course_id" property="courseId"/>
        <result column="lesson_id" property="lessonId"/>
        <result column="resource_id" property="resourceId"/>
        <result column="resource_type" property="resourceType"/>
        <result column="progress" property="progress"/>
        <result column="duration" property="duration"/>
        <result column="completed" property="completed"/>
        <result column="last_position" property="lastPosition"/>
        <result column="last_study_time" property="lastStudyTime"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <!-- 关联字段 -->
        <result column="student_name" property="studentName"/>
        <result column="department_name" property="departmentName"/>
        <result column="course_name" property="courseName"/>
        <result column="lesson_name" property="lessonName"/>
    </resultMap>

    <!-- 分页查询学习记录，包含详细信息 -->
    <select id="selectPageWithDetails" resultMap="StudyRecordResultMap">
        SELECT
            sr.*,
            s.name as student_name,
            d.name as department_name,
            c.name as course_name
        FROM study_records sr
        LEFT JOIN students s ON sr.user_id = s.id
        LEFT JOIN departments d ON s.department_id = d.id
        LEFT JOIN courses c ON sr.course_id = c.id
        <where>
            <if test="studentId != null">
                AND sr.user_id = #{studentId}
            </if>
            <if test="courseId != null">
                AND sr.course_id = #{courseId}
            </if>
            <if test="departmentId != null">
                AND s.department_id = #{departmentId}
            </if>
            <if test="startTime != null">
                AND sr.created_at &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND sr.created_at &lt;= #{endTime}
            </if>
        </where>
        ORDER BY sr.updated_at DESC
    </select>

    <!-- 获取部门学习统计 -->
    <select id="getDepartmentStatistics" resultType="java.util.Map">
        SELECT
            d.id as departmentId,
            d.name as departmentName,
            COUNT(DISTINCT sr.user_id) as totalStudents,
            COUNT(DISTINCT CASE WHEN sr.last_study_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN sr.user_id END) as activeStudents,
            IFNULL(SUM(sr.duration), 0) as totalStudyTime,
            IFNULL(AVG(sr.duration), 0) as averageStudyTime,
            IFNULL(AVG(sr.progress), 0) as completionRate
        FROM departments d
        LEFT JOIN students s ON s.department_id = d.id
        LEFT JOIN study_records sr ON sr.user_id = s.id
        GROUP BY d.id, d.name
        ORDER BY totalStudyTime DESC
    </select>

    <!-- 获取活跃学员排行 -->
    <select id="getActiveStudents" resultType="java.util.Map">
        SELECT
            s.id as userId,
            s.name as userName,
            d.name as departmentName,
            IFNULL(SUM(sr.duration), 0) as totalStudyTime,
            MAX(sr.last_study_time) as lastStudyTime
        FROM students s
        JOIN departments d ON s.department_id = d.id
        LEFT JOIN study_records sr ON s.id = sr.user_id
        GROUP BY s.id, s.name, d.name
        ORDER BY totalStudyTime DESC
        LIMIT #{limit}
    </select>

    <!-- 获取用户学习记录 -->
    <select id="selectUserStudyRecords" resultType="java.util.Map">
        SELECT
            sr.*,
            c.name as course_name
        FROM study_records sr
        LEFT JOIN courses c ON sr.course_id = c.id
        WHERE sr.user_id = #{userId}
        <if test="courseId != null">
            AND sr.course_id = #{courseId}
        </if>
        ORDER BY sr.last_study_time DESC
    </select>

    <!-- 获取课程学习记录 -->
    <select id="selectCourseStudyRecords" resultType="java.util.Map">
        SELECT
            sr.*,
            s.name as user_name
        FROM study_records sr
        LEFT JOIN students s ON sr.user_id = s.id
        WHERE sr.course_id = #{courseId}
        ORDER BY sr.last_study_time DESC
    </select>

    <!-- 获取课时学习记录 -->
    <select id="selectLessonStudyRecords" resultType="java.util.Map">
        SELECT
            sr.*,
            s.name as user_name,
            c.name as course_name
        FROM study_records sr
        LEFT JOIN students s ON sr.user_id = s.id
        LEFT JOIN courses c ON sr.course_id = c.id
        WHERE sr.lesson_id = #{lessonId}
        ORDER BY sr.last_study_time DESC
    </select>

    <!-- 获取用户课程学习进度 -->
    <select id="selectUserCourseProgress" resultType="java.util.Map">
        SELECT
            IFNULL(AVG(sr.progress), 0) AS progress,
            IFNULL(MAX(sr.completed), 0) AS completed,
            IFNULL(SUM(sr.duration), 0) AS duration,
            MAX(sr.last_study_time) AS lastStudyTime
        FROM study_records sr
        WHERE sr.user_id = #{userId} AND sr.course_id = #{courseId}
    </select>

    <!-- 查询学员的课程学习记录 -->
    <select id="selectByStudentAndCourse" resultMap="StudyRecordResultMap">
        SELECT * FROM study_records
        WHERE user_id = #{studentId} AND course_id = #{courseId}
        ORDER BY updated_at DESC
        LIMIT 1
    </select>

    <!-- 查询学员的课时学习记录 -->
    <select id="selectByStudentAndLesson" resultMap="StudyRecordResultMap">
        SELECT * FROM study_records
        WHERE user_id = #{studentId} AND lesson_id = #{lessonId}
        ORDER BY updated_at DESC
        LIMIT 1
    </select>

    <!-- 更新学习进度 -->
    <update id="updateProgress">
        UPDATE study_records
        SET progress = #{progress},
        completed = #{completed},
        last_study_time = NOW(),
            updated_at = NOW()
        WHERE id = #{id}
    </update>

</mapper>
