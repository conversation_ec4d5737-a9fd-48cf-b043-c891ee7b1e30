/**
 * 论坛模块状态转换工具
 */

// 帖子状态转换
export const postStatusMap = {
  0: 'pending',
  1: 'approved',
  2: 'rejected',
  3: 'deleted'
};

// 评论状态转换
export const commentStatusMap = {
  0: 'pending',
  1: 'approved',
  2: 'rejected',
  3: 'deleted'
};

// 违规举报状态转换
export const violationStatusMap = {
  0: 'pending',
  1: 'processed',
  2: 'ignored'
};

// 违规处理类型转换
export const processTypeMap = {
  0: 'none',
  1: 'delete',
  2: 'warning'
};

// 帖子状态展示文本
export const postStatusTextMap = {
  0: '待审核',
  1: '已通过',
  2: '已拒绝',
  3: '已删除'
};

// 评论状态展示文本
export const commentStatusTextMap = {
  0: '待审核',
  1: '已通过',
  2: '已拒绝',
  3: '已删除'
};

// 违规举报状态展示文本
export const violationStatusTextMap = {
  0: '待处理',
  1: '已处理',
  2: '已忽略'
};

// 违规处理类型展示文本
export const processTypeTextMap = {
  0: '无操作',
  1: '删除',
  2: '警告'
};

// 帖子状态样式类
export const postStatusTypeMap = {
  0: 'warning',
  1: 'success',
  2: 'danger',
  3: 'info'
};

// 评论状态样式类
export const commentStatusTypeMap = {
  0: 'warning',
  1: 'success',
  2: 'danger',
  3: 'info'
};

// 违规举报状态样式类
export const violationStatusTypeMap = {
  0: 'warning',
  1: 'success',
  2: 'info'
};

// 违规处理类型样式类
export const processTypeStyleMap = {
  0: '',
  1: 'danger',
  2: 'warning'
};

/**
 * 获取帖子状态展示对象
 * @param {Number} status 状态码
 * @returns {Object} 状态展示对象
 */
export function getPostStatusInfo(status) {
  const numStatus = Number(status);
  return {
    value: numStatus,
    code: postStatusMap[numStatus] || 'unknown',
    text: postStatusTextMap[numStatus] || '未知状态',
    type: postStatusTypeMap[numStatus] || 'default'
  };
}

/**
 * 获取评论状态展示对象
 * @param {Number} status 状态码
 * @returns {Object} 状态展示对象
 */
export function getCommentStatusInfo(status) {
  const numStatus = Number(status);
  return {
    value: numStatus,
    code: commentStatusMap[numStatus] || 'unknown',
    text: commentStatusTextMap[numStatus] || '未知状态',
    type: commentStatusTypeMap[numStatus] || 'default'
  };
}

/**
 * 获取违规举报状态展示对象
 * @param {Number} status 状态码
 * @returns {Object} 状态展示对象
 */
export function getViolationStatusInfo(status) {
  const numStatus = Number(status);
  return {
    value: numStatus,
    code: violationStatusMap[numStatus] || 'unknown',
    text: violationStatusTextMap[numStatus] || '未知状态',
    type: violationStatusTypeMap[numStatus] || 'default'
  };
}

/**
 * 获取违规处理类型展示对象
 * @param {Number} type 处理类型码
 * @returns {Object} 处理类型展示对象
 */
export function getProcessTypeInfo(type) {
  const numType = Number(type);
  return {
    value: numType,
    code: processTypeMap[numType] || 'unknown',
    text: processTypeTextMap[numType] || '未知类型',
    type: processTypeStyleMap[numType] || 'default'
  };
}

/**
 * 帖子状态选项（用于下拉选择）
 */
export const postStatusOptions = [
  { value: 0, label: '待审核' },
  { value: 1, label: '已通过' },
  { value: 2, label: '已拒绝' },
  { value: 3, label: '已删除' }
];

/**
 * 评论状态选项（用于下拉选择）
 */
export const commentStatusOptions = [
  { value: 0, label: '待审核' },
  { value: 1, label: '已通过' },
  { value: 2, label: '已拒绝' },
  { value: 3, label: '已删除' }
];

/**
 * 违规举报状态选项（用于下拉选择）
 */
export const violationStatusOptions = [
  { value: 0, label: '待处理' },
  { value: 1, label: '已处理' },
  { value: 2, label: '已忽略' }
]; 