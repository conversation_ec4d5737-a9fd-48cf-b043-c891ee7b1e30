package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.Student;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 学员Mapper接口
 */
@Repository
public interface StudentMapper extends BaseMapper<Student> {

    /**
     * 根据ID查询学员详情，并关联部门信息
     *
     * @param id 学员ID
     * @return 学员详情
     */
    Student selectStudentById(@Param("id") Integer id);

    // =========================== 论坛相关用户功能 ===========================

    /**
     * 获取用户发布的帖子数量
     */
    @Select("SELECT COUNT(*) FROM forum_posts WHERE author_id = #{userId} AND status = 1")
    int getUserPostCount(@Param("userId") Integer userId);

    /**
     * 获取用户的粉丝数量
     */
    @Select("SELECT COUNT(*) FROM forum_user_follows WHERE following_id = #{userId}")
    int getUserFollowerCount(@Param("userId") Integer userId);

    /**
     * 获取用户关注的数量
     */
    @Select("SELECT COUNT(*) FROM forum_user_follows WHERE follower_id = #{userId}")
    int getUserFollowingCount(@Param("userId") Integer userId);

    /**
     * 获取用户获得的点赞数量
     */
    @Select("SELECT COALESCE(SUM(p.like_count), 0) FROM forum_posts p WHERE p.author_id = #{userId} AND p.status != 3")
    int getUserLikeCount(@Param("userId") Integer userId);

    /**
     * 检查当前用户是否关注了目标用户
     */
    @Select("SELECT COUNT(*) FROM forum_user_follows WHERE follower_id = #{currentUserId} AND following_id = #{targetUserId}")
    int checkUserFollow(@Param("currentUserId") Integer currentUserId, @Param("targetUserId") Integer targetUserId);

    /**
     * 获取用户的粉丝列表（分页）
     */
    @Select("SELECT s.id, s.name, s.avatar, s.job_title as jobTitle, d.name as departmentName FROM students s " +
            "INNER JOIN departments d ON s.department_id = d.id " +
            "INNER JOIN forum_user_follows f ON s.id = f.follower_id " +
            "WHERE f.following_id = #{userId} " +
            "ORDER BY f.created_at DESC")
    IPage<Map<String, Object>> getUserFollowers(IPage<Map<String, Object>> page, @Param("userId") Integer userId);

    /**
     * 获取用户关注的用户列表（分页）
     */
    @Select("SELECT s.id, s.name, s.avatar, s.job_title as jobTitle, d.name as departmentName FROM students s " +
            "INNER JOIN departments d ON s.department_id = d.id " +
            "INNER JOIN forum_user_follows f ON s.id = f.following_id " +
            "WHERE f.follower_id = #{userId} " +
            "ORDER BY f.created_at DESC")
    IPage<Map<String, Object>> getUserFollowing(IPage<Map<String, Object>> page, @Param("userId") Integer userId);
}
