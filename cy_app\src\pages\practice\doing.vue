<template>
  <view class="practice-doing">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left">
          <view class="back-btn" @click="confirmExit">
            <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
          </view>
          <text class="navbar-title">退出练习</text>
        </view>
        <view class="nav-right">
          <text class="nav-time">{{ formatTime(elapsedTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 进度指示器 -->
    <view class="progress-section">
      <view class="progress-info">
        <text class="progress-text">第{{ currentQuestionIndex + 1 }}题 / 共{{ totalQuestions }}题</text>
        <text class="progress-percent">{{ Math.round((currentQuestionIndex + 1) / totalQuestions * 100) }}%</text>
      </view>
      <view class="progress-bar">
        <view :style="{ width: Math.round((currentQuestionIndex + 1) / totalQuestions * 100) + '%' }"
              class="progress-fill"></view>
      </view>
    </view>

    <!-- 题目卡片 -->
    <view class="question-card" v-if="currentQuestion">
      <view class="question-header">
        <view class="question-type">
          <text class="type-text">{{ getQuestionTypeText(currentQuestion.type) }}</text>
        </view>
        <view class="question-score">
          <text class="score-text">{{ currentQuestion.score || 5 }}分</text>
        </view>
      </view>

      <view class="question-content">
        <text class="question-text">{{ currentQuestion.title }}</text>
      </view>

      <!-- 选择题选项 -->
      <view class="question-options" v-if="currentQuestion.type === 'single' || currentQuestion.type === 'multiple'">
        <view
            v-for="(option, index) in parsedOptions"
            :key="option.key"
            :class="{
            'selected': isOptionSelected(option.key),
            'correct': showAnswer && isCorrectOption(option.key),
            'wrong': showAnswer && isOptionSelected(option.key) && !isCorrectOption(option.key)
          }"
            class="option-item"
            @click="selectOption(option.key)"
        >
          <view class="option-selector">
            <view class="selector-circle">
              <up-icon v-if="isOptionSelected(option.key)" name="checkmark" color="#ffffff" size="12"></up-icon>
            </view>
          </view>
          <view class="option-content">
            <text class="option-label">{{ option.key }}.</text>
            <text class="option-text">{{ option.text }}</text>
          </view>
          <view class="option-status" v-if="showAnswer">
            <up-icon
                v-if="isCorrectOption(option.key)"
                color="#67c23a"
                name="checkmark-circle"
                size="20"
            ></up-icon>
            <up-icon
                v-else-if="isOptionSelected(option.key)"
                color="#f56c6c"
                name="close-circle"
                size="20"
            ></up-icon>
          </view>
        </view>
      </view>

      <!-- 判断题选项 -->
      <view class="judgment-options" v-if="currentQuestion.type === 'judgment'">
        <view
            :class="{
            'selected': selectedAnswers.includes('true'),
            'correct': showAnswer && currentQuestion.correctAnswer === 'true',
            'wrong': showAnswer && selectedAnswers.includes('true') && currentQuestion.correctAnswer !== 'true'
          }"
            class="judgment-item"
            @click="selectJudge('true')"
        >
          <view class="judgment-icon">
            <up-icon name="checkmark-circle" color="#67c23a" size="24"></up-icon>
          </view>
          <text class="judgment-text">正确</text>
        </view>
        <view
            :class="{
            'selected': selectedAnswers.includes('false'),
            'correct': showAnswer && currentQuestion.correctAnswer === 'false',
            'wrong': showAnswer && selectedAnswers.includes('false') && currentQuestion.correctAnswer !== 'false'
          }"
            class="judgment-item"
            @click="selectJudge('false')"
        >
          <view class="judgment-icon">
            <up-icon name="close-circle" color="#f56c6c" size="24"></up-icon>
          </view>
          <text class="judgment-text">错误</text>
        </view>
      </view>

      <!-- 填空题输入 -->
      <view class="fill-input" v-if="currentQuestion.type === 'fill'">
        <up-input
            v-model="fillAnswer"
            :disabled="showAnswer"
            placeholder="请输入答案"
            @change="onAnswerChange"
        ></up-input>
      </view>

      <!-- 简答题输入 -->
      <view class="essay-input" v-if="currentQuestion.type === 'essay'">
        <up-textarea
            v-model="essayAnswer"
            :disabled="showAnswer"
            :maxlength="500"
            placeholder="请输入答案..."
            @change="onAnswerChange"
        ></up-textarea>
        <view class="word-count">
          <text class="count-text">{{ getWordCount(essayAnswer) }}/500</text>
        </view>
      </view>

      <!-- 答案解析 -->
      <view class="answer-analysis" v-if="showAnswer">
        <view class="analysis-header">
          <up-icon name="info-circle" color="#409eff" size="16"></up-icon>
          <text class="analysis-title">答案解析</text>
        </view>
        <view class="correct-answer">
          <text class="answer-label">正确答案：</text>
          <text class="answer-text">{{ getCorrectAnswerText() }}</text>
        </view>
        <view class="explanation" v-if="currentQuestion.explanation">
          <text class="explanation-text">{{ currentQuestion.explanation }}</text>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="nav-btn" @click="previousQuestion" :class="{ disabled: currentQuestionIndex === 0 }">
          <up-icon name="arrow-left" size="16"></up-icon>
          <text class="btn-text">上一题</text>
        </view>
      </view>

      <view class="action-center">
        <view class="submit-btn" @click="submitAnswer" :class="{ disabled: !canSubmit || showAnswer }">
          <text class="btn-text">{{ showAnswer ? '已提交' : '提交答案' }}</text>
        </view>
      </view>

      <view class="action-right">
        <view class="nav-btn" @click="nextQuestion">
          <text class="btn-text">{{ isLastQuestion ? '完成练习' : '下一题' }}</text>
          <up-icon name="arrow-right" size="16"></up-icon>
        </view>
      </view>
    </view>

    <!-- 完成练习弹窗 -->
    <up-popup :show="showCompleteDialog" @close="closeCompleteDialog" mode="center" round="16">
      <view class="complete-dialog">
        <view class="dialog-header">
          <view class="header-icon">
            <up-icon color="#67c23a" name="checkmark-circle" size="32"></up-icon>
          </view>
          <text class="dialog-title">练习完成</text>
          <text class="dialog-subtitle">恭喜你完成了本次练习！</text>
        </view>

        <view class="dialog-content">
          <view class="stats-grid">
            <view class="stat-item">
              <view class="stat-icon">
                <up-icon color="#409eff" name="file-text" size="20"></up-icon>
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ totalQuestions }}</text>
                <text class="stat-label">总题数</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon">
                <up-icon color="#67c23a" name="checkmark-circle" size="20"></up-icon>
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ correctCount }}</text>
                <text class="stat-label">正确</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon">
                <up-icon color="#f56c6c" name="close-circle" size="20"></up-icon>
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ wrongCount }}</text>
                <text class="stat-label">错误</text>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-icon">
                <up-icon color="#fa8c16" name="percent" size="20"></up-icon>
              </view>
              <view class="stat-content">
                <text class="stat-value">{{ getAccuracyRate() }}%</text>
                <text class="stat-label">正确率</text>
              </view>
            </view>
          </view>
        </view>

        <view class="dialog-actions">
          <view class="action-btn secondary" @click="goBack">
            <text class="btn-text">返回</text>
          </view>
          <view class="action-btn primary" @click="viewDetail">
            <text class="btn-text">查看详情</text>
          </view>
        </view>
      </view>
    </up-popup>

    <!-- 退出确认弹窗 -->
    <up-popup :show="showExitDialog" @close="closeExitDialog" mode="center" round="16">
      <view class="exit-dialog">
        <view class="dialog-header">
          <view class="header-icon">
            <up-icon color="#fa709a" name="question-circle" size="24"></up-icon>
          </view>
          <text class="dialog-title">确认退出</text>
        </view>

        <view class="dialog-content">
          <text class="dialog-message">退出后练习进度将会丢失，确定要退出吗？</text>
        </view>

        <view class="dialog-actions">
          <view class="action-btn secondary" @click="closeExitDialog">
            <text class="btn-text">取消</text>
          </view>
          <view class="action-btn danger" @click="confirmExitPractice">
            <text class="btn-text">确定退出</text>
          </view>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import {ref, computed, onMounted, onUnmounted} from 'vue'
import {
  getNextQuestion,
  submitAnswer as submitAnswerApi,
  completePractice as completePracticeApi
} from '@/api/practice'

// 页面参数
const recordId = ref(0)

// 练习状态
const currentQuestion = ref<any>(null)
const currentQuestionIndex = ref(0)
const totalQuestions = ref(0)
const correctCount = ref(0)
const wrongCount = ref(0)
const showAnswer = ref(false)
const selectedAnswers = ref<string[]>([])
const fillAnswer = ref('')
const essayAnswer = ref('')
const elapsedTime = ref(0)

// 弹窗状态
const showCompleteDialog = ref(false)
const showExitDialog = ref(false)

// 计时器
let timer: any = null

// 计算属性
const canSubmit = computed(() => {
  if (!currentQuestion.value) return false

  if (currentQuestion.value.type === 'single' || currentQuestion.value.type === 'multiple' || currentQuestion.value.type === 'judgment') {
    return selectedAnswers.value.length > 0
  } else if (currentQuestion.value.type === 'fill') {
    return fillAnswer.value.trim() !== ''
  } else if (currentQuestion.value.type === 'essay') {
    return essayAnswer.value.trim() !== ''
  }

  return false
})

const isLastQuestion = computed(() => {
  return currentQuestionIndex.value === totalQuestions.value - 1
})

// 解析题目选项
const parsedOptions = computed(() => {
  if (!currentQuestion.value || !currentQuestion.value.options) return []

  try {
    let options = currentQuestion.value.options

    // 如果是字符串，尝试解析为JSON
    if (typeof options === 'string') {
      options = JSON.parse(options)
    }

    // 如果是数组，直接使用
    if (Array.isArray(options)) {
      return options.map((option: any, index: number) => ({
        key: option.id || option.key || String.fromCharCode(65 + index), // A, B, C, D
        text: option.content || option.text || option.value || option
      }))
    }

    // 如果是对象，转换为数组
    if (typeof options === 'object') {
      return Object.keys(options).map(key => ({
        key: key,
        text: options[key]
      }))
    }

    return []
  } catch (error) {
    console.error('解析选项失败:', error)
    return []
  }
})

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    single: '单选题',
    multiple: '多选题',
    judgment: '判断题',
    fill: '填空题',
    essay: '简答题'
  }
  return typeMap[type] || '未知类型'
}

// 检查选项是否被选中
const isOptionSelected = (optionKey: string) => {
  return selectedAnswers.value.includes(optionKey)
}

// 检查选项是否正确
const isCorrectOption = (optionKey: string) => {
  if (!currentQuestion.value || !currentQuestion.value.correctAnswer) return false
  const correctAnswers = currentQuestion.value.correctAnswer.split(',')
  return correctAnswers.includes(optionKey)
}

// 选择选项
const selectOption = (key: string) => {
  if (showAnswer.value) return

  if (currentQuestion.value.type === 'single') {
    selectedAnswers.value = [key]
  } else if (currentQuestion.value.type === 'multiple') {
    const index = selectedAnswers.value.indexOf(key)
    if (index > -1) {
      selectedAnswers.value.splice(index, 1)
    } else {
      selectedAnswers.value.push(key)
    }
  }
}

// 选择判断题答案
const selectJudge = (value: string) => {
  if (showAnswer.value) return
  selectedAnswers.value = [value]
}

// 获取用户答案
const getUserAnswer = () => {
  if (currentQuestion.value.type === 'single' || currentQuestion.value.type === 'multiple' || currentQuestion.value.type === 'judgment') {
    return selectedAnswers.value.join(',')
  } else if (currentQuestion.value.type === 'fill') {
    return fillAnswer.value
  } else if (currentQuestion.value.type === 'essay') {
    return essayAnswer.value
  }
  return ''
}

// 加载当前题目
const loadCurrentQuestion = async () => {
  try {
    const response = await getNextQuestion(recordId.value) as any
    console.log('题目数据:', response)

    if (response && response.question) {
      currentQuestion.value = response.question

      // 更新进度信息
      totalQuestions.value = response.totalQuestions || 1
      currentQuestionIndex.value = (response.currentQuestionIndex || 1) - 1
      correctCount.value = response.correctCount || 0
      wrongCount.value = response.wrongCount || 0

      // 重置答题状态
      resetAnswerState()
    } else if (response && !response.hasNext) {
      // 没有更多题目，完成练习
      completePractice()
    } else {
      uni.showToast({
        title: '题目加载失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载题目失败:', error)
    uni.showToast({
      title: '题目加载失败',
      icon: 'none'
    })
  }
}

// 提交答案
const submitAnswer = async () => {
  if (!canSubmit.value) return

  try {
    const userAnswer = getUserAnswer()
    const response = await submitAnswerApi({
      recordId: recordId.value,
      questionId: currentQuestion.value?.id || 0,
      userAnswer: userAnswer
    })
    console.log('提交答案结果:', response)
    
    showAnswer.value = true

    uni.showToast({
      title: '答案已提交',
      icon: 'success'
    })
  } catch (error) {
    console.error('提交答案失败:', error)
    uni.showToast({
      title: '提交失败',
      icon: 'none'
    })
  }
}

// 上一题
const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
    loadCurrentQuestion()
  }
}

// 下一题
const nextQuestion = () => {
  if (isLastQuestion.value) {
    completePractice()
  } else {
    currentQuestionIndex.value++
    loadCurrentQuestion()
  }
}

// 重置答题状态
const resetAnswerState = () => {
  showAnswer.value = false
  selectedAnswers.value = []
  fillAnswer.value = ''
  essayAnswer.value = ''
}

// 退出确认
const confirmExit = () => {
  showExitDialog.value = true
}

// 关闭退出弹窗
const closeExitDialog = () => {
  showExitDialog.value = false
}

// 确认退出练习
const confirmExitPractice = () => {
  showExitDialog.value = false
  uni.navigateBack()
}

// 输入框变化处理
const onAnswerChange = () => {
  // 处理输入框内容变化
}

// 获取字数
const getWordCount = (text: string) => {
  return text.length
}

// 获取正确答案文本
const getCorrectAnswerText = () => {
  if (!currentQuestion.value) return ''

  if (currentQuestion.value.type === 'single' || currentQuestion.value.type === 'multiple') {
    const correctAnswers = currentQuestion.value.correctAnswer?.split(',') || []
    const correctTexts = correctAnswers.map((answer: string) => {
      const option = parsedOptions.value.find((opt: any) => opt.key === answer)
      return option ? `${answer}. ${option.text}` : answer
    })
    return correctTexts.join(', ')
  } else if (currentQuestion.value.type === 'judgment') {
    return currentQuestion.value.correctAnswer === 'true' ? '正确' : '错误'
  } else {
    return currentQuestion.value.correctAnswer || ''
  }
}

// 获取正确率
const getAccuracyRate = () => {
  const totalAnswered = correctCount.value + wrongCount.value
  if (totalAnswered === 0) return 0
  return Math.round((correctCount.value / totalAnswered) * 100)
}

// 完成练习
const completePractice = async () => {
  try {
    const result = await completePracticeApi(recordId.value) as any
    console.log('完成练习结果:', result)

    // 更新最终统计信息
    if (result) {
      totalQuestions.value = result.totalQuestions || totalQuestions.value
      correctCount.value = result.correctCount || correctCount.value
      wrongCount.value = result.wrongCount || wrongCount.value
    }

    showCompleteDialog.value = true

  } catch (error) {
    console.error('完成练习失败:', error)
    uni.showToast({
      title: '完成练习失败',
      icon: 'none'
    })
  }
}

// 关闭完成弹窗
const closeCompleteDialog = () => {
  showCompleteDialog.value = false
}

// 返回
const goBack = () => {
  showCompleteDialog.value = false
  uni.navigateBack()
}

// 查看详情
const viewDetail = () => {
  showCompleteDialog.value = false
  uni.navigateTo({
    url: `/pages/practice/record-detail?recordId=${recordId.value}`
  })
}

// 格式化时间
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 开始计时
const startTimer = () => {
  timer = setInterval(() => {
    elapsedTime.value++
  }, 1000)
}

// 停止计时
const stopTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 页面加载
onMounted(() => {
  // 从页面参数获取recordId
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  console.log('页面参数:', options)

  recordId.value = parseInt(options.recordId || '0')

  if (!recordId.value) {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
    uni.navigateBack()
    return
  }

  // 开始计时
  startTimer()

  // 加载第一题
  loadCurrentQuestion()
})

// 页面卸载
onUnmounted(() => {
  stopTimer()
})
</script>

<style lang="scss" scoped>
@import '@/styles/pages/practice/doing.scss';
</style>
