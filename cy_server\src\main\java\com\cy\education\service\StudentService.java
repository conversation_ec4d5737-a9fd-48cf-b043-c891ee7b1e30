package com.cy.education.service;

import com.cy.education.model.entity.Student;
import com.cy.education.model.vo.PageResponse;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 学员服务接口
 */
public interface StudentService {
    
    /**
     * 分页查询学员列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param keyword 关键词（用户名、真实姓名、手机号）
     * @param departmentId 部门ID
     * @param status 状态
     * @return 分页结果
     */
    PageResponse<Student> listStudents(Long page, Long size, String keyword, Integer departmentId, Integer status);
    
    /**
     * 根据ID查询学员详情
     *
     * @param id 学员ID
     * @return 学员详情
     */
    Student getStudentById(Integer id);
    
    /**
     * 添加学员
     *
     * @param student 学员信息
     * @return 学员ID
     */
    Integer addStudent(Student student);
    
    /**
     * 更新学员信息
     *
     * @param student 学员信息
     * @return 是否成功
     */
    boolean updateStudent(Student student);
    
    /**
     * 删除学员
     *
     * @param id 学员ID
     * @return 是否成功
     */
    boolean deleteStudent(Integer id);
    
    /**
     * 重置学员密码
     *
     * @param id 学员ID
     * @return 新密码
     */
    String resetStudentPassword(Integer id);
    
    /**
     * 更新学员状态
     *
     * @param id 学员ID
     * @param status 状态（0禁用，1启用）
     * @return 是否成功
     */
    boolean updateStudentStatus(Integer id, Integer status);
    
    /**
     * 导出学员列表
     *
     * @param keyword 关键词（用户名、真实姓名、手机号）
     * @param departmentId 部门ID
     * @param status 状态
     * @return 导出文件URL
     */
    String exportStudentList(String keyword, Integer departmentId, Integer status);
    
    /**
     * 批量导入学员
     *
     * @param inputStream Excel文件输入流
     * @param originalFilename 原始文件名
     * @return 导入结果，包含成功数量
     */
    Map<String, Object> importStudents(InputStream inputStream, String originalFilename);
    
    /**
     * 更新学生积分
     *
     * @param userId 用户ID
     * @param points 积分余额
     * @return 是否成功
     */
    boolean updateStudentPoints(Integer userId, Integer points);

    // ==================== 新版导入导出功能 ====================

    /**
     * 生成学员导入模板
     *
     * @return 模板文件资源
     */
    Resource generateImportTemplate() throws IOException;

    /**
     * 批量导入学员（新版本）
     *
     * @param file Excel文件
     * @return 导入结果
     */
    Map<String, Object> importStudentsV2(MultipartFile file);

    /**
     * 导出学员列表（新版本）
     *
     * @param params 导出参数
     * @param response HTTP响应
     */
    void exportStudentsV2(Map<String, Object> params, HttpServletResponse response) throws IOException;

    /**
     * 获取学员总数（用于测试）
     */
    long getStudentCount();

    /**
     * 获取学员列表（用于测试）
     */
    List<Student> getStudentListForTest(int limit);
}