import { generateId, randomDate, mockResponse } from './utils'
import type { UserInfo } from '@/api/user'

// 管理员账号
const adminUser: UserInfo = {
  id: 'admin001',
  username: 'admin',
  name: '系统管理员',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  permissions: ['user:manage', 'course:manage', 'points:manage', 'product:manage', 'exchange:manage']
}

// 普通账号
const normalUser: UserInfo = {
  id: 'user001',
  username: 'user',
  name: '普通用户',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  permissions: ['course:view', 'course:edit']
}

// 用户列表
export const users: UserInfo[] = [
  adminUser,
  normalUser,
  {
    id: generateId(),
    username: 'teacher1',
    name: '张老师',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    permissions: ['course:view', 'course:edit', 'course:create']
  },
  {
    id: generateId(),
    username: 'student1',
    name: '李同学',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    permissions: ['course:view']
  }
]

/**
 * 登录接口
 * @param username 用户名
 * @param password 密码
 * @returns 登录结果
 */
export function mockLogin(username: string, password: string) {
  console.log('模拟登录调用:', { username, password: '******' })
  
  // 这里简单判断，为了调试目的，总是允许admin登录
  if (username === 'admin') {
    const result = {
      token: 'admin-token-' + Date.now(),
      user: adminUser
    }
    console.log('模拟登录成功(admin):', result)
    // 直接返回登录结果，而不是包装在标准响应格式中
    return {
      code: 200,
      message: '登录成功',
      data: result
    }
  } else if (username === 'user' && password === 'user123') {
    const result = {
      token: 'user-token-' + Date.now(),
      user: normalUser
    }
    console.log('模拟登录成功(user):', result)
    return {
      code: 200,
      message: '登录成功',
      data: result
    }
  } else {
    console.log('模拟登录失败: 用户名或密码错误')
    return {
      code: 401,
      message: '用户名或密码错误',
      data: null
    }
  }
}

/**
 * 登出接口
 * @returns 登出结果
 */
export function mockLogout() {
  console.log('模拟登出接口调用')
  // 模拟退出操作成功
  return mockResponse({ success: true, message: '退出成功' })
}

/**
 * 获取用户信息接口
 * @param token 用户令牌
 * @returns 用户信息
 */
export function mockGetUserInfo(token: string) {
  if (token.startsWith('admin-token-')) {
    return mockResponse(adminUser)
  } else if (token.startsWith('user-token-')) {
    return mockResponse(normalUser)
  } else {
    return mockResponse(null, 401, '无效的令牌')
  }
}

/**
 * 获取验证码接口
 * @param phone 手机号
 * @returns 验证码结果
 */
export function mockGetVerificationCode(phone: string) {
  return mockResponse({ success: true })
}

/**
 * 重置密码接口
 * @param data 重置密码数据
 * @returns 重置结果
 */
export function mockResetPassword(data: any) {
  return mockResponse({ success: true })
}

/**
 * 更新用户信息接口
 * @param data 用户信息
 * @returns 更新结果
 */
export function mockUpdateUserInfo(data: any) {
  return mockResponse({ success: true })
}

/**
 * 更改密码接口
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 * @returns 更改结果
 */
export function mockChangePassword(oldPassword: string, newPassword: string) {
  return mockResponse({ success: true })
}

/**
 * 上传头像接口
 * @param formData 头像文件数据
 * @returns 上传结果
 */
export function mockUploadAvatar(formData: FormData) {
  // 模拟生成一个新的头像URL
  const avatarUrl = `https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png?t=${Date.now()}`
  return mockResponse({ avatarUrl })
} 