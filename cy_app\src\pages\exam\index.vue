<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="navbar-left">
          <text class="navbar-title">考试中心</text>
        </view>
      </view>
    </view>

    <!-- 状态标签栏 -->
    <view class="status-tabs">
      <scroll-view scroll-x class="tabs-scroll">
        <view class="tabs-container">
          <view
              v-for="(tab, index) in statusTabs"
              :key="index"
              :class="{ active: currentStatus === index }"
              class="tab-item"
              @click="switchStatus(index)"
          >
            <text class="tab-text">{{ tab.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 练习入口 -->
      <view class="practice-section">
        <view class="practice-card" @click="goToPractice">
          <view class="practice-left">
            <view class="practice-icon">
              <up-icon name="edit-pen" size="24" color="#667eea"></up-icon>
            </view>
            <view class="practice-content">
              <text class="practice-title">开始练习</text>
              <text class="practice-desc">选择题库进行刷题练习</text>
            </view>
          </view>
          <view class="practice-right">
            <up-icon name="arrow-right" size="16" color="#c7c7cc"></up-icon>
          </view>
        </view>
        <view class="practice-card" @click="goToWrongBook">
          <view class="practice-left">
            <view class="practice-icon wrong">
              <up-icon name="close-circle" size="24" color="#ef4444"></up-icon>
            </view>
            <view class="practice-content">
              <text class="practice-title">错题本</text>
              <text class="practice-desc">复习做错的题目</text>
            </view>
          </view>
          <view class="practice-right">
            <up-icon name="arrow-right" size="16" color="#c7c7cc"></up-icon>
          </view>
        </view>
      </view>

      <!-- 考试统计卡片 -->
      <view v-if="userExamStats" class="stats-section">
        <view class="stats-card">
          <view class="stats-header">
            <text class="stats-title">我的考试统计</text>
          </view>
          <view class="stats-grid">
            <view class="stat-item">
              <text class="stat-number">{{ userExamStats.totalExams || 0 }}</text>
              <text class="stat-label">总考试</text>
            </view>
            <view class="stat-item">
              <text class="stat-number participated">{{ userExamStats.participatedExams || 0 }}</text>
              <text class="stat-label">已参与</text>
            </view>
            <view class="stat-item">
              <text class="stat-number completed">{{ userExamStats.completedExams || 0 }}</text>
              <text class="stat-label">已完成</text>
            </view>
            <view class="stat-item">
              <text class="stat-number passed">{{ userExamStats.passedExams || 0 }}</text>
              <text class="stat-label">已通过</text>
            </view>
          </view>
          <view class="stats-details">
            <view class="detail-row">
              <view class="detail-item">
                <text class="detail-label">未参与</text>
                <text class="detail-value not-participated">{{ userExamStats.notParticipatedExams || 0 }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">进行中</text>
                <text class="detail-value ongoing">{{ userExamStats.ongoingExams || 0 }}</text>
              </view>
            </view>
            <view class="detail-row">
              <view class="detail-item">
                <text class="detail-label">平均分</text>
                <text class="detail-value average">{{ userExamStats.averageScore || 0 }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">通过率</text>
                <text class="detail-value pass-rate">{{ userExamStats.passRate || 0 }}%</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 考试列表 -->
      <view class="exam-section">
        <view class="exam-list">
          <view
              v-for="exam in examList"
              :key="exam.id"
              class="exam-card"
          >
            <!-- 状态标签右上角 -->
            <view :class="getStatusClass(exam.status)" class="status-tag">
              <text class="status-text">{{ getStatusText(exam.status) }}</text>
            </view>
            <view class="exam-header" @click="handleExamTap(exam)">
              <view class="exam-icon">
                <up-icon name="file-text" size="20" color="#667eea"></up-icon>
              </view>
              <view class="exam-title-desc">
                <text class="exam-title">{{ exam.title }}</text>
                <text v-if="exam.description" class="exam-desc">{{ exam.description }}</text>
                <text v-else-if="exam.paper && exam.paper.description" class="exam-desc">{{
                    exam.paper.description
                  }}
                </text>
              </view>
            </view>
            <view class="exam-info" @click="handleExamTap(exam)">
              <view class="info-row">
                <up-icon color="#8e8e93" name="calendar" size="14"></up-icon>
                <text class="info-text">{{ formatExamTime(exam) }}</text>
                <up-icon color="#8e8e93" name="clock" size="14" style="margin-left: 16rpx;"></up-icon>
                <text class="info-text">{{ exam.duration }}分钟</text>
              </view>
              <view class="info-row">
                <up-icon color="#8e8e93" name="integral" size="14"></up-icon>
                <text class="info-text">总分 {{ exam.paper.totalScore }} / 及格 {{ exam.paper.passingScore }}</text>
                <template v-if="exam.score !== undefined">
                  <up-icon
                      :color="exam.score >= exam.passScore ? '#10b981' : '#ef4444'"
                      name="checkmark-circle"
                      size="14"
                      style="margin-left: 16rpx;"
                  ></up-icon>
                  <text
                      :class="{ passed: exam.score >= exam.passScore, failed: exam.score < exam.passScore }"
                      class="info-text score"
                  >
                    得分 {{ exam.score }}
                  </text>
                </template>
              </view>
            </view>
            <view class="exam-footer">
              <view class="exam-action">
                <view
                    :class="['action-btn', getActionClass(exam)]"
                    @click="handleAction(exam)"
                >
                  <text class="action-text">{{ getActionText(exam) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="examList.length > 0 && !loading" class="load-more">
          <view v-if="loadStatus === 'loading'" class="loading-text">
            <up-loading-icon color="#667eea" size="16"></up-loading-icon>
            <text>加载中...</text>
          </view>
          <view v-else-if="loadStatus === 'noMore'" class="nomore-text">
            <text>没有更多了</text>
          </view>
          <view v-else class="loadmore-btn" @click="loadMoreExams">
            <text>加载更多</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="examList.length === 0 && !loading" class="empty-state">
        <view class="empty-icon">📝</view>
        <text class="empty-text">暂无考试安排</text>
        <text class="empty-desc">请等待管理员发布考试</text>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <custom-tabbar :current="2"></custom-tabbar>
  </view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar/index.vue'
import {getExamList, getUserExamStats} from '@/api/exam'

export default {
  components: {
    CustomTabbar
  },
  data() {
    return {
      currentStatus: 0,
      statusTabs: [
        {name: '全部', value: 0},
        {name: '未开始', value: 1},
        {name: '进行中', value: 2},
        {name: '已结束', value: 3}
      ],
      userExamStats: {
        totalExams: 0,
        participatedExams: 0,
        notParticipatedExams: 0,
        completedExams: 0,
        ongoingExams: 0,
        passedExams: 0,
        averageScore: 0,
        passRate: 0
      },
      examList: [], // 当前显示的考试列表
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: true,
      hasMore: true,
      isLoadingMore: false,
      loadStatus: 'more'
    }
  },
  onLoad() {
    this.loadUserExamStats();
    this.fetchExams()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.pageNum = 1;
    this.examList = [];
    this.hasMore = true;
    this.loadUserExamStats();
    this.fetchExams();
  },

  // 上拉加载更多
  onReachBottom() {
    if (!this.hasMore || this.isLoadingMore) return;
    this.pageNum += 1;
    this.fetchExams(true);
  },
  onUnload() {
  },
  methods: {
    // 加载考试统计信息
    async loadUserExamStats() {
      const stats = await getUserExamStats();
      if (stats) {
        this.userExamStats = stats;
      }
    },

    // 获取考试列表
    async fetchExams(isLoadMore = false) {
      if (isLoadMore) {
        this.isLoadingMore = true;
        this.loadStatus = 'loading';
      } else {
        this.loading = true;
        this.pageNum = 1;
        this.examList = [];
        this.hasMore = true;
        this.loadStatus = 'more';
      }

      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize
        };

        // 根据当前状态筛选
        const status = this.statusTabs[this.currentStatus].value;
        if (status !== 0) {
          params.status = status;
        }

        const response = await getExamList(params);
        this.total = response.total || 0;

        // 处理考试数据，添加状态文本和倒计时
        const processedExams = (response.list || []).map(exam => {
          return {
            ...exam,
            isPassed: exam.score !== undefined ? exam.score >= exam.passScore : undefined
          };
        });

        if (isLoadMore) {
          this.examList = this.examList.concat(processedExams);
        } else {
          this.examList = processedExams;
        }

        // 更新加载状态
        if (this.examList.length >= this.total) {
          this.hasMore = false;
          this.loadStatus = 'noMore';
        } else {
          this.hasMore = true;
          this.loadStatus = 'more';
        }

      } catch (error) {
        console.error('加载考试列表失败:', error);
        uni.showToast({
          title: '加载考试列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.isLoadingMore = false;
        uni.stopPullDownRefresh();
      }
    },
    switchStatus(index) {
      this.currentStatus = index;
      this.fetchExams();
    },
    loadMoreExams() {
      if (this.loadStatus === 'noMore' || this.loadStatus === 'loading') return;
      this.pageNum += 1;
      this.fetchExams(true);
    },
    formatExamTime(exam) {
      const start = new Date(exam.startTime).toLocaleString('sv-SE').substring(5, 16);
      const end = new Date(exam.endTime).toLocaleString('sv-SE').substring(5, 16);
      return `${start} ~ ${end}`;
    },
    getStatusText(status) {
      switch (status) {
        case 1:
          return '未开始'
        case 2:
          return '进行中'
        case 3:
          return '已结束'
        default:
          return '草稿'
      }
    },
    getStatusClass(status) {
      const map = {1: 'upcoming', 2: 'ongoing', 3: 'completed'};
      return map[status] || 'unknown';
    },
    getActionInfo(exam) {
      // exam.status: 0-草稿,1-未开始,2-进行中,3-已结束
      // exam.currentUserRecord.status: 0-未开始,1-进行中,2-已结束,3-已超时
      const examStatus = exam.status;
      const recordStatus = exam.currentUserRecord?.status;
      if (examStatus === 1) {
        return {text: '查看详情', page: 'detail'};
      }
      if (examStatus === 2) {
        if (!recordStatus || recordStatus === 0) return {text: '立即参加', page: 'taking'};
        if (recordStatus === 1) return {text: '继续作答', page: 'taking'};
        if (recordStatus === 2) return {text: '等待考试结束', page: 'detail'};
        if (recordStatus === 3) return {text: '已超时', page: 'detail'};
      }
      if (examStatus === 3) {
        if (recordStatus === 2) return {text: '查看成绩', page: 'result'};
        if (recordStatus === 3) return {text: '已超时', page: 'detail'};
        if (!recordStatus || recordStatus === 0 || recordStatus === 1) return {text: '未参加', page: 'detail'};
      }
      return {text: '查看', page: 'detail'};
    },
    getActionText(exam) {
      return this.getActionInfo(exam).text;
    },
    getActionClass(exam) {
      const {page, text} = this.getActionInfo(exam);
      if (page === 'taking') return 'primary';
      if (page === 'result') return 'secondary';
      return 'disabled';
    },
    handleExamTap(exam) {
      uni.navigateTo({url: `/pages/exam/detail?id=${exam.id}`});
    },
    handleAction(exam) {
      const {page} = this.getActionInfo(exam);
      if (page === 'taking') {
        uni.navigateTo({url: `/pages/exam/taking?id=${exam.id}`});
      } else if (page === 'result') {
        uni.navigateTo({url: `/pages/exam/result?id=${exam.id}`});
      } else {
        uni.navigateTo({url: `/pages/exam/detail?id=${exam.id}`});
      }
    },
    // 进入练习页面
    goToPractice() {
      uni.navigateTo({
        url: '/pages/practice/index'
      });
    },
    // 进入错题本
    goToWrongBook() {
      uni.navigateTo({
        url: '/pages/practice/wrong-book'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/exam/exam.scss";
</style>
