<template>
  <view class="custom-tabbar safe-area-inset-bottom">
    <view class="tabbar-container">
      <view class="tabbar-background"></view>
      <view class="tabbar-content">
        <view
            v-for="(item, index) in tabList"
            :key="index"
            :class="{ active: Number(current) === index }"
            class="tab-item"
            @tap="switchTab(index)"
        >
          <view class="tab-icon-container">
            <view :class="{ active:  Number(current) ===index }" class="tab-icon">
              <component :is="item.icon" :active="Number(current) === index"/>
            </view>
          </view>
          <text :class="{ active: Number(current) === index }" class="tab-text">{{ item.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, markRaw} from 'vue'
import HomeIcon from '../TabbarIcons/HomeIcon.vue'
import StudyIcon from '../TabbarIcons/StudyIcon.vue'
import ExamIcon from '../TabbarIcons/ExamIcon.vue'
import ForumIcon from '../TabbarIcons/ForumIcon.vue'
import ProfileIcon from '../TabbarIcons/ProfileIcon.vue'

const props = defineProps<{
  current: number
}>()

const tabList = ref([
  {
    text: '首页',
    pagePath: '/pages/home/<USER>',
    icon: markRaw(HomeIcon)
  },
  {
    text: '学习',
    pagePath: '/pages/study/index',
    icon: markRaw(StudyIcon)
  },
  {
    text: '考试',
    pagePath: '/pages/exam/index',
    icon: markRaw(ExamIcon)
  },
  {
    text: '论坛',
    pagePath: '/pages/forum/index',
    icon: markRaw(ForumIcon)
  },
  {
    text: '我的',
    pagePath: '/pages/profile/index',
    icon: markRaw(ProfileIcon)
  }
])

const switchTab = (index: number) => {
  if (index === props.current) return

  const targetPage = tabList.value[index]

  uni.switchTab({
    url: targetPage.pagePath,
    fail: (err) => {
      // 如果switchTab失败，尝试使用reLaunch
      uni.reLaunch({
        url: targetPage.pagePath
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: transparent;
}

.tabbar-container {
  position: relative;
  margin: 0 16px;
  margin-bottom: calc(16px + env(safe-area-inset-bottom));
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.tabbar-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.tabbar-content {
  position: relative;
  display: flex;
  align-items: center;
  height: 60px;
  padding: 0 8px;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: scale(0.95);
  }
}

.tab-icon-container {
  position: relative;
  margin-bottom: 2px;
}

.tab-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.active {
    transform: scale(1.1);
  }
}

.active-indicator {
  position: absolute;
  top: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  opacity: 0.8;
}

.tab-text {
  font-size: 10px;
  color: #8E8E93;
  font-weight: 500;
  transition: all 0.3s ease;

  &.active {
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
