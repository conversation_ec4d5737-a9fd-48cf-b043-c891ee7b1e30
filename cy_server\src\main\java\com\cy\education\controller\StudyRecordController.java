package com.cy.education.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.model.entity.StudyRecord;
import com.cy.education.model.vo.StudyRecordVO;
import com.cy.education.model.vo.StudyStatisticsVO;
import com.cy.education.model.params.StudyRecordQueryParams;
import com.cy.education.service.StudyRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学习记录控制器
 */
@RestController
@RequestMapping("/study/records")
public class StudyRecordController {
    
    @Autowired
    private StudyRecordService studyRecordService;
    
    /**
     * 获取用户的学习记录
     */
    @GetMapping("/user/{userId}")
    public ApiResponse<List<StudyRecordVO>> getUserStudyRecords(
            @PathVariable Integer userId,
            @RequestParam(required = false) Integer courseId
    ) {
        List<StudyRecordVO> records = studyRecordService.getUserStudyRecords(userId, courseId);
        return ApiResponse.success(records);
    }
    
    /**
     * 获取课程的学习记录
     */
    @GetMapping("/course/{courseId}")
    public ApiResponse<List<StudyRecordVO>> getCourseStudyRecords(@PathVariable Integer courseId) {
        List<StudyRecordVO> records = studyRecordService.getCourseStudyRecords(courseId);
        return ApiResponse.success(records);
    }
    
    /**
     * 获取课时的学习记录
     */
    @GetMapping("/lesson/{lessonId}")
    public ApiResponse<List<StudyRecordVO>> getLessonStudyRecords(@PathVariable Integer lessonId) {
        List<StudyRecordVO> records = studyRecordService.getLessonStudyRecords(lessonId);
        return ApiResponse.success(records);
    }
    
    /**
     * 创建或更新学习记录
     */
    @PostMapping("/save")
    public ApiResponse<Map<String, Object>> saveStudyRecord(@RequestBody StudyRecord studyRecord) {
        StudyRecord savedRecord = studyRecordService.saveStudyRecord(studyRecord);
        
        Map<String, Object> result = new HashMap<>();
        result.put("id", savedRecord.getId());
        
        return ApiResponse.success(result);
    }
    
    /**
     * 获取学习记录详情
     */
    @GetMapping("/{id}")
    public ApiResponse<StudyRecordVO> getStudyRecordById(@PathVariable Integer id) {
        StudyRecordVO record = studyRecordService.getStudyRecordById(id);
        return ApiResponse.success(record);
    }
    
    /**
     * 删除学习记录
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Boolean> deleteStudyRecord(@PathVariable Integer id) {
        boolean success = studyRecordService.deleteStudyRecord(id);
        return ApiResponse.success(success);
    }
    
    /**
     * 获取用户课程学习进度
     */
    @GetMapping("/progress")
    public ApiResponse<Map<String, Object>> getUserCourseProgress(
            @RequestParam Integer userId,
            @RequestParam Integer courseId
    ) {
        Map<String, Object> progress = studyRecordService.getUserCourseProgress(userId, courseId);
        return ApiResponse.success(progress);
    }
    
    /**
     * 记录资源访问日志
     */
    @PostMapping("/access/log")
    public ApiResponse<Boolean> logResourceAccess(@RequestBody Map<String, Object> accessLog) {
        boolean success = studyRecordService.logResourceAccess(accessLog);
        return ApiResponse.success(success);
    }

    /**
     * 获取学习记录列表（分页）
     */
    @GetMapping("/list")
    public ApiResponse<PageResponse<StudyRecordVO>> getStudyRecordList(StudyRecordQueryParams params) {
        IPage<StudyRecordVO> page = studyRecordService.getStudyRecordList(params);
        PageResponse<StudyRecordVO> response = new PageResponse<>();
        response.setList(page.getRecords());
        response.setTotal((int) page.getTotal());
        return ApiResponse.success(response);
    }

    /**
     * 获取学习统计数据
     */
    @GetMapping("/statistics")
    public ApiResponse<StudyStatisticsVO> getStudyStatistics(
            @RequestParam Integer studentId,
            @RequestParam(required = false) Integer courseId
    ) {
        StudyStatisticsVO statistics = studyRecordService.getStudyStatistics(studentId, courseId);
        return ApiResponse.success(statistics);
    }

    /**
     * 获取部门学习统计
     */
    @GetMapping("/department-statistics")
    public ApiResponse<List<Map<String, Object>>> getDepartmentStatistics() {
        List<Map<String, Object>> statistics = studyRecordService.getDepartmentStatistics();
        return ApiResponse.success(statistics);
    }

    /**
     * 获取活跃学员列表
     */
    @GetMapping("/active-students")
    public ApiResponse<List<Map<String, Object>>> getActiveStudents(
            @RequestParam(required = false, defaultValue = "10") Integer limit
    ) {
        List<Map<String, Object>> activeStudents = studyRecordService.getActiveStudents(limit);
        return ApiResponse.success(activeStudents);
    }

    /**
     * 导出学习记录
     */
    @PostMapping("/export")
    public void exportStudyRecords(
            @RequestBody Map<String, Object> params,
            HttpServletResponse response
    ) throws IOException {
        studyRecordService.exportStudyRecords(params, response);
    }

    /**
     * 获取部门学习统计数据
     */
    @GetMapping("/department-stats")
    public ApiResponse<List<Map<String, Object>>> getDepartmentStudyStats() {
        List<Map<String, Object>> stats = studyRecordService.getDepartmentStudyStats();
        return ApiResponse.success(stats);
    }
}