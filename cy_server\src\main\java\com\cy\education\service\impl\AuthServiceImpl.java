package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.Admin;
import com.cy.education.model.entity.Permission;
import com.cy.education.model.params.ChangePasswordParams;
import com.cy.education.model.params.LoginParams;
import com.cy.education.model.params.UpdateUserInfoParams;
import com.cy.education.model.vo.LoginResponseVO;
import com.cy.education.repository.AdminMapper;
import com.cy.education.repository.PermissionMapper;
import com.cy.education.service.AuthService;
import com.cy.education.utils.FileUploadUtil;
import com.cy.education.utils.JwtTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 认证服务实现
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private FileUploadUtil fileUploadUtil;

    @Autowired
    private com.cy.education.service.CaptchaService captchaService;

    @Override
    public LoginResponseVO login(LoginParams loginParams) {
        // 1. 验证验证码
        if (!captchaService.verifyCaptcha(loginParams.getCaptchaId(), loginParams.getCaptcha())) {
            throw new BadCredentialsException("验证码错误或已过期");
        }

        // 2. 查询用户
        Admin admin = adminMapper.selectOne(
                new LambdaQueryWrapper<Admin>()
                        .eq(Admin::getUsername, loginParams.getUsername())
        );

        if (admin == null) {
            throw new BadCredentialsException("用户名或密码不正确");
        }

        // 3. 校验密码
        if (!passwordEncoder.matches(loginParams.getPassword(), admin.getPassword())) {
            throw new BadCredentialsException("用户名或密码不正确");
        }

        // 4. 检查账号状态
        if (admin.getStatus() != 1) {
            throw new BusinessException("账号已被禁用，请联系管理员");
        }

        // 5. 获取用户权限
        List<Permission> permissionList = permissionMapper.selectPermissionsByAdminId(admin.getId());
        String[] permissions = permissionList.stream()
                .map(Permission::getCode)
                .toArray(String[]::new);

        // 6. 生成Token，存储Integer类型的ID
        Map<String, Object> claims = new HashMap<>();
        claims.put("id", admin.getId());
        claims.put("type", "admin");
        String token = jwtTokenUtil.generateToken(admin.getUsername(), claims);

        // 7. 更新登录时间
        admin.setLastLoginTime(LocalDateTime.now());
        adminMapper.updateById(admin);

        // 8. 构建返回对象
        return LoginResponseVO.builder()
                .token(token)
                .user(LoginResponseVO.UserInfoVO.builder()
                        .id(admin.getId())
                        .username(admin.getUsername())
                        .name(admin.getName())
                        .avatar(admin.getAvatar())
                        .permissions(permissions)
                        .build())
                .build();
    }

    @Override
    public boolean logout() {
        // 基于JWT的无状态认证，服务端不需要处理登出逻辑
        // 客户端只需要删除本地存储的token即可
        return true;
    }

    @Override
    public LoginResponseVO.UserInfoVO getCurrentUserInfo() {
        // 从Security上下文获取当前认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BadCredentialsException("用户未登录");
        }

        // 获取用户名
        String username = authentication.getName();

        // 查询管理员信息
        Admin admin = adminMapper.selectOne(
                new LambdaQueryWrapper<Admin>()
                        .eq(Admin::getUsername, username)
        );

        if (admin == null) {
            throw new BadCredentialsException("用户不存在");
        }

        // 获取用户权限
        List<Permission> permissionList = permissionMapper.selectPermissionsByAdminId(admin.getId());
        String[] permissions = permissionList.stream()
                .map(Permission::getCode)
                .toArray(String[]::new);

        // 构建用户信息
        return LoginResponseVO.UserInfoVO.builder()
                .id(admin.getId())
                .username(admin.getUsername())
                .name(admin.getName())
                .avatar(admin.getAvatar())
                .permissions(permissions)
                .build();
    }

    @Override
    public boolean updateUserInfo(UpdateUserInfoParams params) {
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BadCredentialsException("用户未登录");
        }

        String username = authentication.getName();

        // 查询管理员信息
        Admin admin = adminMapper.selectOne(
                new LambdaQueryWrapper<Admin>()
                        .eq(Admin::getUsername, username)
        );

        if (admin == null) {
            throw new BadCredentialsException("用户不存在");
        }

        // 更新用户信息
        Admin updateAdmin = new Admin();
        updateAdmin.setId(admin.getId());

        // 只更新提供的字段
        if (params.getName() != null && !params.getName().trim().isEmpty()) {
            updateAdmin.setName(params.getName());
        }

        if (params.getAvatar() != null && !params.getAvatar().trim().isEmpty()) {
            updateAdmin.setAvatar(params.getAvatar());
        }

        updateAdmin.setUpdatedAt(LocalDateTime.now());

        int result = adminMapper.updateById(updateAdmin);

        log.info("更新用户信息: username={}, name={}, avatar={}, result={}",
                username, params.getName(), params.getAvatar(), result);

        return result > 0;
    }

    @Override
    public boolean changePassword(ChangePasswordParams params) {
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BadCredentialsException("用户未登录");
        }

        String username = authentication.getName();

        // 查询管理员信息
        Admin admin = adminMapper.selectOne(
                new LambdaQueryWrapper<Admin>()
                        .eq(Admin::getUsername, username)
        );

        if (admin == null) {
            throw new BadCredentialsException("用户不存在");
        }

        // 验证当前密码
        if (!passwordEncoder.matches(params.getOldPassword(), admin.getPassword())) {
            throw new BusinessException("当前密码不正确");
        }

        // 检查新密码是否与当前密码相同
        if (passwordEncoder.matches(params.getNewPassword(), admin.getPassword())) {
            throw new BusinessException("新密码不能与当前密码相同");
        }

        // 更新密码
        Admin updateAdmin = new Admin();
        updateAdmin.setId(admin.getId());
        updateAdmin.setPassword(passwordEncoder.encode(params.getNewPassword()));
        updateAdmin.setUpdatedAt(LocalDateTime.now());

        int result = adminMapper.updateById(updateAdmin);

        log.info("修改密码: username={}, result={}", username, result);

        return result > 0;
    }

    @Override
    public String uploadAvatar(MultipartFile file) {
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BadCredentialsException("用户未登录");
        }

        String username = authentication.getName();

        // 上传文件
        String avatarUrl = fileUploadUtil.uploadAvatar(file);

        // 更新用户头像
        Admin admin = adminMapper.selectOne(
                new LambdaQueryWrapper<Admin>()
                        .eq(Admin::getUsername, username)
        );

        if (admin != null) {
            Admin updateAdmin = new Admin();
            updateAdmin.setId(admin.getId());
            updateAdmin.setAvatar(avatarUrl);
            updateAdmin.setUpdatedAt(LocalDateTime.now());

            adminMapper.updateById(updateAdmin);
        }

        return avatarUrl;
    }

    @Override
    public LoginResponseVO.UserInfoVO getUserDetailById(Integer userId) {
        Admin admin = adminMapper.selectAdminById(userId);
        if (admin == null) {
            throw new BusinessException("用户不存在");
        }
        // 获取用户权限
        List<Permission> permissionList = permissionMapper.selectPermissionsByAdminId(admin.getId());
        String[] permissions = permissionList.stream()
                .map(Permission::getCode)
                .toArray(String[]::new);
        return LoginResponseVO.UserInfoVO.builder()
                .id(admin.getId())
                .username(admin.getUsername())
                .name(admin.getName())
                .avatar(admin.getAvatar())
                .permissions(permissions)
                .build();
    }
}
