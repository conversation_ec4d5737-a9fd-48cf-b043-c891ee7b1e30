<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">积分商城</text>
        <view class="placeholder"></view>
      </view>
    </view>
    <!-- 积分余额 -->
    <view class="points-balance">
      <text class="balance-label">我的积分</text>
      <text class="balance-value">{{ currentPoints }}</text>
    </view>

    <!-- 商品分类 -->
    <view class="category-tabs">
      <text
          v-for="category in categories"
          :key="category.id"
          :class="{ active: selectedCategory === category.id }"
          class="tab-item"
          @tap="selectCategory(category.id)"
      >
        {{ category.name }}
      </text>
    </view>

    <!-- 商品列表 -->
    <view class="goods-list">
      <view
          v-for="item in goodsList"
          :key="item.id"
          class="goods-item"
          @tap="showGoodsDetail(item)"
      >
        <image :src="item.image" class="goods-image" mode="aspectFill"/>
        <view class="goods-info">
          <text class="goods-name">{{ item.name }}</text>
          <text class="goods-desc">{{ item.description }}</text>
          <view class="goods-footer">
            <text class="goods-points">{{ item.points }}积分</text>
            <text class="goods-stock">剩余{{ item.stock }}件</text>
          </view>
        </view>
      </view>
      <!-- 分页加载更多 -->
      <view v-if="goodsList.length > 0" class="load-more">
        <view v-if="loadMoreStatus === 'loading'" class="loading-text">
          <text>加载中...</text>
        </view>
        <view v-else-if="loadMoreStatus === 'noMore'" class="nomore-text">
          <text>没有更多了</text>
        </view>
        <view v-else class="loadmore-text">
          <text>加载更多</text>
        </view>
      </view>
    </view>
    <!-- 加载更多 -->
    <view v-if="loading" class="loading-more">
      <up-loading-icon color="#667eea"></up-loading-icon>
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <up-empty
        v-if="goodsList.length === 0 && !loading"
        mode="list"
        text="空空如也"
        textColor="#909399"
        textSize="14"
    >
    </up-empty>

    <!-- 商品详情弹窗 -->
    <view v-if="showDetailDialog" class="detail-dialog" @tap="hideDetailDialog">
      <view class="dialog-content" @tap.stop>
        <image :src="selectedGoods.image" class="detail-image" mode="aspectFill"/>
        <view class="detail-info">
          <text class="detail-name">{{ selectedGoods.name }}</text>
          <text class="detail-desc">{{ selectedGoods.description }}</text>
          <text class="detail-points">{{ selectedGoods.points }}积分</text>
        </view>
        <view class="dialog-actions">
          <button class="btn btn-secondary" @tap="hideDetailDialog">取消</button>
          <button
              :disabled="currentPoints < selectedGoods.points || selectedGoods.stock === 0"
              class="btn btn-primary"
              @tap="exchangeGoods"
          >
            {{ selectedGoods.stock === 0 ? '已售罄' : '立即兑换' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 收货地址弹窗 -->
    <view v-if="showAddressDialog" class="address-dialog">
      <view class="address-dialog-content">
        <text class="form-label">收货地址</text>
        <view class="address-select" @tap="showAreaPopup = true">
          <text v-if="addressText">{{ addressText }}</text>
          <text v-else class="placeholders">请选择省市区</text>
        </view>
        <text class="form-label">详细地址</text>
        <up-textarea v-model="detailAddress" border="bottom" class="address-input"
                     placeholders="请输入内容"></up-textarea>
        <view class="address-dialog-actions">
          <button class="btn btn-secondary" @tap="showAddressDialog = false">取消</button>
          <button class="btn btn-primary" @tap="submitAddress">确认兑换</button>
        </view>
      </view>
    </view>
    <!-- 省市区弹窗选择器 -->
    <up-popup :closeable="true" :mask="true" :show="showAreaPopup" mode="bottom" @close="closeAreaPopup">
      <view class="area-box">
        <view class="u-flex">
          <!-- 省 -->
          <view class="area-item">
            <scroll-view :scroll-y="true" style="height: 100%">
              <up-cell-group>
                <up-cell
                    v-for="(item, pIdx) in provinces"
                    :key="item.value"
                    :arrow="false"
                    :title="item.label"
                    @click="selectProvince(pIdx)"
                >
                  <template #right-icon>
                    <up-icon v-if="provinceIdx === pIdx" name="checkbox-mark" size="17"></up-icon>
                  </template>
                </up-cell>
              </up-cell-group>
            </scroll-view>
          </view>
          <!-- 市 -->
          <view class="area-item">
            <scroll-view :scroll-y="true" style="height: 100%">
              <up-cell-group v-if="cities.length">
                <up-cell
                    v-for="(item, cIdx) in cities"
                    :key="item.value"
                    :arrow="false"
                    :title="item.label"
                    @click="selectCity(cIdx)"
                >
                  <template #right-icon>
                    <up-icon v-if="cityIdx === cIdx" name="checkbox-mark" size="17"></up-icon>
                  </template>
                </up-cell>
              </up-cell-group>
            </scroll-view>
          </view>
          <!-- 区 -->
          <view class="area-item">
            <scroll-view :scroll-y="true" style="height: 100%">
              <up-cell-group v-if="areas.length">
                <up-cell
                    v-for="(item, aIdx) in areas"
                    :key="item.value"
                    :arrow="false"
                    :title="item.label"
                    @click="selectArea(aIdx)"
                >
                  <template #right-icon>
                    <up-icon v-if="areaIdx === aIdx" name="checkbox-mark" size="17"></up-icon>
                  </template>
                </up-cell>
              </up-cell-group>
            </scroll-view>
          </view>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import {ref, onMounted, computed} from 'vue'
import {onPullDownRefresh, onReachBottom} from '@dcloudio/uni-app'

import {getGoodsList, type GoodsItem, type CategoryItem} from '@/api/pointsMall'
import {getPointsBalance, exchangeGoodsApi} from '@/api/points'
import provinceData from '@/utils/province.js'
import cityData from '@/utils/city.js'
import areaData from '@/utils/area.js'

interface Category extends CategoryItem {
}

interface Goods extends GoodsItem {
}

const currentPoints = ref(0)
// 商品分类固定
const categories: Category[] = [
  {id: 'all', name: '全部'},
  {id: 'virtual', name: '虚拟物品'},
  {id: 'physical', name: '实物'},
  {id: 'service', name: '服务'},
  {id: 'other', name: '其他'}
]
const selectedCategory = ref('all')
const goodsList = ref<Goods[]>([])
const showDetailDialog = ref(false)
const showAddressDialog = ref(false)
const selectedGoods = ref<Goods>({} as Goods)
const detailAddress = ref('')

// 省市区弹窗选择器相关
const showAreaPopup = ref(false)
const provinces = provinceData
const provinceIdx = ref(0)
const cities = computed(() => cityData[provinceIdx.value] || [])
const cityIdx = ref(0)
const areas = computed(() => areaData[provinceIdx.value][cityIdx.value] || [])
const areaIdx = ref(0)
const addressText = ref('')

function selectProvince(idx) {
  provinceIdx.value = idx
  cityIdx.value = 0
  areaIdx.value = 0
}

function selectCity(idx) {
  cityIdx.value = idx
  areaIdx.value = 0
}

function selectArea(idx) {
  areaIdx.value = idx
  // 选完区后自动关闭弹窗并回传
  emitResult()
}

function closeAreaPopup() {
  showAreaPopup.value = false
}

function emitResult() {
  const province = provinces[provinceIdx.value]
  const city = cities.value[cityIdx.value]
  const area = areas.value[areaIdx.value]
  addressText.value = `${province.label} ${city.label} ${area.label}`
  showAreaPopup.value = false
}

// 分页相关
const pageNum = ref(1)
const pageSize = ref(3)
const total = ref(0)
const hasMore = ref(true)
const isLoadingMore = ref(false)
const loadMoreStatus = ref<'more' | 'loading' | 'noMore'>('more')
const loading = ref(true)

// 商品列表分页获取
const fetchGoods = async (isLoadMore = false) => {
  if (isLoadMore) {
    isLoadingMore.value = true
    loadMoreStatus.value = 'loading'
  } else {
    loading.value = true
    pageNum.value = 1
    goodsList.value = []
    hasMore.value = true
  }
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      category: selectedCategory.value === 'all' ? undefined : selectedCategory.value
    }
    const res = await getGoodsList(params)
    total.value = res.total || 0
    if (isLoadMore) {
      goodsList.value = goodsList.value.concat(res.list)
    } else {
      goodsList.value = res.list
    }
    if (goodsList.value.length >= total.value) {
      hasMore.value = false
      loadMoreStatus.value = 'noMore'
    } else {
      hasMore.value = true
      loadMoreStatus.value = 'more'
    }
  } finally {
    loading.value = false
    isLoadingMore.value = false
    uni.stopPullDownRefresh();
  }
}

// 分类切换
const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId
  pageNum.value = 1
  goodsList.value = []
  hasMore.value = true
  fetchGoods()
}

// 下拉刷新
onPullDownRefresh(() => {
  pageNum.value = 1
  goodsList.value = []
  hasMore.value = true
  fetchGoods()
})
// 上拉加载更多
onReachBottom(() => {
  if (!hasMore.value || isLoadingMore.value) return
  pageNum.value += 1
  fetchGoods(true)
})

// 显示商品详情
const showGoodsDetail = (goods: Goods) => {
  selectedGoods.value = goods
  showDetailDialog.value = true
}
// 隐藏商品详情
const hideDetailDialog = () => {
  showDetailDialog.value = false
}
// 兑换商品
const exchangeGoods = () => {
  if (currentPoints.value < selectedGoods.value.points) {
    uni.showToast({
      title: '积分不足',
      icon: 'none'
    })
    return
  }
  if (selectedGoods.value.stock === 0) {
    uni.showToast({
      title: '商品已售罄',
      icon: 'none'
    })
    return
  }
  if (selectedGoods.value.category === 'physical') {
    hideDetailDialog()
    showAddressDialog.value = true
    return
  }
  // 虚拟商品，走原流程
  hideDetailDialog()
  uni.showModal({
    title: '确认兑换',
    content: `确定使用${selectedGoods.value.points}积分兑换"${selectedGoods.value.name}"吗？`,
    success: (res) => {
      if (res.confirm) {
        doExchange({})
      } else {
        showDetailDialog.value = true
      }
    }
  })
}

const submitAddress = () => {
  if (!addressText.value) {
    uni.showToast({title: '请选择省市区', icon: 'none'})
    return
  }
  if (!detailAddress.value.trim()) {
    uni.showToast({title: '请输入详细地址', icon: 'none'})
    return
  }
  showAddressDialog.value = false
  uni.showModal({
    title: '确认兑换',
    content: `确定使用${selectedGoods.value.points}积分兑换"${selectedGoods.value.name}"吗？`,
    success: (res) => {
      if (res.confirm) {
        doExchange({
          address: addressText.value + ' ' + detailAddress.value
        })
      } else {
        showDetailDialog.value = true
      }
    }
  })
}

// 执行兑换
const doExchange = async (addressInfo = {}) => {
  uni.showLoading({title: '兑换中...'})
  try {
    await exchangeGoodsApi({productId: selectedGoods.value.id, ...addressInfo})
    // 兑换成功后刷新积分和商品库存
    currentPoints.value -= selectedGoods.value.points
    selectedGoods.value.stock -= 1
    uni.showToast({
      title: '兑换成功',
      icon: 'success'
    })
    fetchGoods()
  } catch (e: any) {
    uni.showToast({
      title: e?.message || '兑换失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

const navigateBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  loading.value = true
  const userId = uni.getStorageSync('userInfo').id
  getPointsBalance(userId).then((res: { points: number }) => currentPoints.value = res)
  fetchGoods()
})
</script>

<style lang="scss" scoped>
@import '@/styles/pages/points/mall.scss';

.area-box {
  width: 100%;
  overflow: hidden;
  height: 400px;

  .u-flex {
    width: 100%;
    display: flex;
  }

  .area-item {
    width: 33.3333%;
    height: 400px;
  }
}

.address-dialog {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1002;
  background: rgba(0, 0, 0, 0.35);
  display: flex;
  align-items: center;
  justify-content: center;
}

.address-dialog-content {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  padding: 32rpx 28rpx 24rpx 28rpx;
  min-width: 90vw;
  max-width: 100vw;
  min-height: 320rpx;
}

.form-label {
  font-size: 32rpx;
  line-height: 48rpx;
  margin-bottom: 16rpx;
}

.address-select,
.address-input {
  flex: 1;
  width: 100%;
  min-height: 48rpx;
  padding: 18rpx 20rpx;
  font-size: 28rpx;
  background: #f7f8fa;
  color: #222;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  margin-top: 5rpx;
}

.address-dialog-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 18rpx;
  gap: 24rpx;
}

.placeholders {
  color: #bfbfbf;
  font-size: 26rpx;
  font-style: italic;
  background: #f2f3f5;
  border-radius: 6rpx;
  padding: 4rpx 10rpx;
  display: inline-block;
  flex: 1
}
</style>
