package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.Permission;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 权限Mapper接口
 */
@Repository
public interface PermissionMapper extends BaseMapper<Permission> {
    
    /**
     * 根据管理员ID查询权限列表
     *
     * @param adminId 管理员ID
     * @return 权限列表
     */
    List<Permission> selectPermissionsByAdminId(@Param("adminId") Integer adminId);
    
    /**
     * 根据模块查询权限列表
     *
     * @param module 模块名称
     * @return 权限列表
     */
    List<Permission> selectPermissionsByModule(@Param("module") String module);
} 