package com.cy.education.model.params;

import lombok.Data;

/**
 * 积分规则查询参数
 */
@Data
public class PointsRuleQueryParam {
    
    /**
     * 当前页码
     */
    private Integer page = 1;
    
    /**
     * 每页记录数
     */
    private Integer limit = 10;
    
    /**
     * 规则分类
     */
    private String category;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 关键词（规则名称/描述）
     */
    private String keyword;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向：asc/desc
     */
    private String sortOrder;
} 