# 程远教育培训管理系统 - API接口文档（管理端）

## 概述

本文档描述了程远教育培训管理系统**管理端**的所有API接口，用于管理员管理学员、课程、考试、论坛、积分等功能模块。

### 基础信息
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token
- **用户对象**: 仅限管理员

### 通用响应格式
```typescript
interface ApiResponse<T> {
  code: number          // 状态码，200表示成功
  message: string       // 响应消息
  data?: T             // 响应数据
  timestamp?: number   // 时间戳
}
```

### 分页参数
```typescript
interface PaginationParams {
  page?: number        // 页码，默认1
  size?: number        // 每页大小，默认10
  sortBy?: string      // 排序字段
  sortOrder?: 'asc' | 'desc'  // 排序方向
}

interface PaginationResponse<T> {
  list: T[]           // 数据列表
  total: number       // 总数量
  page: number        // 当前页码
  size: number        // 每页大小
  totalPages: number  // 总页数
}
```

---

## 1. 管理员认证模块

### 1.1 管理员登录
**接口**: `POST /user/login`
**作用**: 管理员登录系统
**权限**: 无需认证

**请求参数**:
```typescript
interface LoginRequest {
  username: string     // 管理员用户名
  password: string     // 密码
  remember?: boolean   // 是否记住登录状态
}
```

**响应数据**:
```typescript
interface LoginResponse {
  token: string        // JWT令牌
  user: {
    id: string
    username: string
    name: string
    avatar: string
    permissions?: string[]  // 管理员权限列表
  }
}
```

### 1.2 管理员登出
**接口**: `POST /user/logout`
**作用**: 管理员登出
**权限**: 需要认证

**请求参数**: 无
**响应数据**: `{ success: boolean }`

### 1.3 获取管理员信息
**接口**: `GET /user/info`
**作用**: 获取当前登录管理员信息
**权限**: 需要认证

**请求参数**: 无
**响应数据**: 
```typescript
interface UserInfo {
  id: string
  username: string
  name: string
  avatar: string
  permissions?: string[]
}
```

### 1.4 更新管理员信息
**接口**: `POST /user/update`
**作用**: 更新当前管理员信息
**权限**: 需要认证

**请求参数**:
```typescript
interface UpdateUserInfoRequest {
  name?: string
  avatar?: string
  // 其他可更新字段
}
```

**响应数据**: `{ success: boolean }`

### 1.5 修改密码
**接口**: `POST /user/change-password`
**作用**: 修改当前管理员密码
**权限**: 需要认证

**请求参数**:
```typescript
interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
}
```

**响应数据**: `{ success: boolean }`

### 1.6 上传头像
**接口**: `POST /user/upload-avatar`
**作用**: 上传管理员头像
**权限**: 需要认证

**请求参数**:
- **Content-Type**: `multipart/form-data`
- **Form字段**: `file` - 头像文件

**响应数据**: `{ avatarUrl: string }`

---

## 2. 学员管理模块

### 2.1 获取学员列表
**接口**: `GET /student/list`
**作用**: 管理员查看学员列表
**权限**: 需要学员管理权限

**请求参数**:
```typescript
interface StudentQueryParams {
  keyword?: string     // 搜索关键词（姓名或手机号）
  departmentId?: number // 部门ID筛选
  status?: string      // 状态筛选 'active' | 'disabled'
  page?: number        // 页码，默认1
  size?: number        // 每页大小，默认10
}
```

**响应数据**: 
```typescript
interface StudentListResponse {
  list: Student[]
  total: number
}

interface Student {
  id: number
  name: string
  phone: string
  departmentId: number
  department: string   // 部门名称，直接字符串
  avatar: string
  status: 'active' | 'disabled'
  registerTime: string
  lastLoginTime: string
  points: number
  entryTime?: string
  remark?: string
}
```

### 2.2 获取学员详情
**接口**: `GET /student/{id}`
**作用**: 管理员查看学员详细信息
**权限**: 需要学员管理权限

**请求参数**:
- **Path参数**: `id` - 学员ID

**响应数据**: `Student` (同上)

### 2.3 添加学员
**接口**: `POST /student/add`
**作用**: 管理员创建新学员账号
**权限**: 需要学员管理权限

**请求参数**:
```typescript
interface AddStudentRequest {
  name: string
  phone: string
  departmentId: number
  password: string
  avatar?: string
  entryTime?: string
  remark?: string
}
```

**响应数据**: `{ id: number }`

### 2.4 更新学员信息
**接口**: `PUT /student/update`
**作用**: 管理员更新学员信息
**权限**: 需要学员管理权限

**请求参数**: `Partial<Student>` (所有字段都可选，除id必须)

**响应数据**: `{ success: boolean }`

### 2.5 删除学员
**接口**: `DELETE /student/{id}`
**作用**: 管理员删除学员账号
**权限**: 需要学员管理权限

**请求参数**:
- **Path参数**: `id` - 学员ID

**响应数据**: `{ success: boolean }`

### 2.6 重置学员密码
**接口**: `POST /student/{id}/reset-password`
**作用**: 管理员重置学员密码
**权限**: 需要学员管理权限

**请求参数**:
- **Path参数**: `id` - 学员ID

**响应数据**: `{ password: string }` // 返回新密码

### 2.7 更新学员状态
**接口**: `PUT /student/{id}/status`
**作用**: 管理员更新学员状态
**权限**: 需要学员管理权限

**请求参数**:
- **Path参数**: `id` - 学员ID
- **Body参数**: `{ status: 'active' | 'disabled' }`

**响应数据**: `{ success: boolean }`

### 2.8 导出学员列表
**接口**: `GET /student/export`
**作用**: 导出学员数据
**权限**: 需要学员管理权限

**请求参数**: 同查询参数
**响应数据**: `{ url: string }` // 导出文件下载链接

### 2.9 批量导入学员
**接口**: `POST /student/import`
**作用**: 批量导入学员数据
**权限**: 需要学员管理权限

**请求参数**:
- **Content-Type**: `multipart/form-data`
- **Form字段**: `file` - Excel文件

**响应数据**: `{ success: boolean, count: number }`

---

## 3. 管理员管理模块

### 3.1 获取管理员列表
**接口**: `GET /admin/list`
**作用**: 获取管理员列表
**权限**: 需要系统管理权限

**请求参数**:
```typescript
interface AdminQueryParams {
  keyword?: string
  departmentId?: number
  status?: number      // 0禁用，1启用
  page: number
  size: number
}
```

**响应数据**: 
```typescript
interface AdminListResponse {
  list: Admin[]
  total: number
}

interface Admin {
  id: number
  realName: string
  username: string
  departmentId: number
  department?: string  // 部门名称，直接字符串
  phone: string
  email?: string
  avatar: string
  lastLoginTime?: string
  createTime: string
  status: number       // 0禁用，1启用
  permissions?: number[]
  remark?: string
}
```

### 3.2 管理员CRUD接口
**接口**: 
- `GET /admin/{id}` - 获取管理员详情
- `POST /admin/add` - 创建管理员
- `POST /admin/update` - 更新管理员
- `DELETE /admin/{id}` - 删除管理员

### 3.3 管理员状态管理
**接口**: `POST /admin/status`
**作用**: 更新管理员状态
**权限**: 需要系统管理权限

**请求参数**: `{ id: number, status: number }`

### 3.4 重置管理员密码
**接口**: `POST /admin/reset-password`
**作用**: 重置管理员密码
**权限**: 需要系统管理权限

**请求参数**: `{ id: number }`
**响应数据**: `{ password: string }`

### 3.5 管理员权限管理
**接口**: 
- `GET /admin/permissions/{id}` - 获取管理员权限
- `POST /admin/permissions` - 设置管理员权限

**权限设置请求参数**: `{ id: number, permissions: number[] }`

---

## 4. 部门管理模块

### 4.1 获取部门列表
**接口**: `GET /department/list`
**作用**: 获取部门列表
**权限**: 需要认证

**请求参数**:
```typescript
interface DepartmentListParams {
  parentId?: number    // 父部门ID，不传则获取顶级部门
  includeChildren?: boolean  // 是否包含子部门
}
```

**响应数据**:
```typescript
interface Department {
  id: number
  name: string
  parentId?: number
  leader?: string
  sort: number
  status: number
  remark?: string
  children?: Department[]  // 子部门列表
  studentCount?: number    // 部门学员数量
  createdAt: string
  updatedAt: string
}
```

### 4.2 部门CRUD接口
- `GET /department/{id}` - 获取部门详情
- `POST /department` - 创建部门
- `PUT /department/{id}` - 更新部门
- `DELETE /department/{id}` - 删除部门

---

## 5. 课程管理模块

### 5.1 获取课程列表
**接口**: `GET /course/list`
**作用**: 管理员查看课程列表
**权限**: 需要课程管理权限

**请求参数**:
```typescript
interface CourseListParams extends PaginationParams {
  keyword?: string     // 搜索关键词
  category?: string    // 课程分类
  level?: 'beginner' | 'intermediate' | 'advanced'
  teacher?: string     // 教师筛选
  status?: 'published' | 'draft'  // 发布状态
}
```

**响应数据**: `PaginationResponse<Course>`

### 5.2 获取课程详情
**接口**: `GET /course/{id}`
**作用**: 管理员查看课程详细信息
**权限**: 需要课程管理权限

**请求参数**:
- **Path参数**: `id` - 课程ID

**响应数据**:
```typescript
interface CourseDetail {
  id: string
  title: string
  cover?: string
  description?: string
  teacher?: string
  category?: string
  tags: string[]
  level: 'beginner' | 'intermediate' | 'advanced'
  duration: number
  studentsCount: number  // 后端统计
  isPublished: boolean
  visibleDepartments?: number[]
  allowFastForward?: boolean
  objectives?: string[]
  requirements?: string[]
  sections: CourseSection[]
  createdAt: string
  updatedAt: string
}
```

### 5.3 创建课程
**接口**: `POST /course`
**作用**: 管理员创建新课程
**权限**: 需要课程管理权限

### 5.4 更新课程
**接口**: `PUT /course/{id}`
**作用**: 管理员更新课程信息
**权限**: 需要课程管理权限

### 5.5 删除课程
**接口**: `DELETE /course/{id}`
**作用**: 管理员删除课程
**权限**: 需要课程管理权限

### 5.6 发布/取消发布课程
**接口**: `PUT /course/{id}/publish`
**作用**: 管理员发布或取消发布课程
**权限**: 需要课程管理权限

### 5.7 查看学员学习记录
**接口**: `GET /course/study-record/list`
**作用**: 管理员查看学员学习记录
**权限**: 需要课程管理权限

**请求参数**:
```typescript
interface StudyRecordParams extends PaginationParams {
  studentId?: string   // 学员ID
  courseId?: string    // 课程ID
  status?: 'not_started' | 'in_progress' | 'completed'
  departmentId?: number // 部门筛选
  startDate?: string   // 时间范围
  endDate?: string
}
```

**响应数据**: `PaginationResponse<StudyRecord>`

### 5.8 查看学员学习统计
**接口**: `GET /course/study-statistics/{studentId}`
**作用**: 管理员查看学员学习统计数据
**权限**: 需要课程管理权限

**请求参数**:
- **Path参数**: `studentId` - 学员ID
- **Query参数**: `courseId?` - 课程ID（可选）

**响应数据**:
```typescript
interface StudyStatistics {
  totalStudyTime: number     // 总学习时长（分钟）
  lastStudyTime?: string     // 最后学习时间
  completedLessons: string[] // 已完成课时ID列表
  studyCount: number         // 学习次数
}
```

### 5.9 手动记录学员学习进度
**接口**: `POST /course/study-record`
**作用**: 管理员手动记录或调整学员学习进度
**权限**: 需要课程管理权限

**请求参数**:
```typescript
interface StudyRecordRequest {
  studentId: string    // 学员ID
  courseId: string
  lessonId?: string
  studyTime: number    // 本次学习时长（分钟）
  progress: number     // 学习进度（百分比）
  status: 'not_started' | 'in_progress' | 'completed'
  score?: number       // 成绩（如果有的话）
  remark?: string      // 管理员备注
}
```

---

## 6. 考试管理模块

### 6.1 题库管理

#### 6.1.1 获取题库列表
**接口**: `GET /exam/question-bank/list`
**作用**: 管理员查看题库列表
**权限**: 需要考试管理权限

#### 6.1.2 题库CRUD接口
- `GET /exam/question-bank/{id}` - 获取题库详情
- `POST /exam/question-bank` - 创建题库
- `PUT /exam/question-bank/{id}` - 更新题库
- `DELETE /exam/question-bank/{id}` - 删除题库

### 6.2 题目管理

#### 6.2.1 获取题目列表
**接口**: `GET /exam/question/list`
**作用**: 管理员查看题目列表
**权限**: 需要考试管理权限

#### 6.2.2 题目CRUD接口
- `GET /exam/question/{id}` - 获取题目详情
- `POST /exam/question` - 创建题目
- `PUT /exam/question/{id}` - 更新题目
- `DELETE /exam/question/{id}` - 删除题目

### 6.3 试卷管理

#### 6.3.1 获取试卷列表
**接口**: `GET /exam/paper/list`
**权限**: 需要考试管理权限

#### 6.3.2 试卷CRUD接口
**数据结构**:
```typescript
interface Paper {
  id: string
  title: string
  description?: string
  totalScore: number
  passingScore: number
  duration: number
  isPublished: boolean
  questions: PaperQuestion[]
  createdAt: string
  updatedAt: string
}
```

### 6.4 考试管理

#### 6.4.1 获取考试列表
**接口**: `GET /exam/list`
**作用**: 管理员查看考试列表
**权限**: 需要考试管理权限

**请求参数**:
```typescript
interface ExamListParams extends PaginationParams {
  status?: 'upcoming' | 'ongoing' | 'ended'
  departmentId?: number
  keyword?: string
}
```

#### 6.4.2 获取考试详情
**接口**: `GET /exam/{id}`
**作用**: 管理员查看考试详细信息
**权限**: 需要考试管理权限

**响应数据**:
```typescript
interface ExamDetail {
  id: string
  title: string
  description?: string
  paperId: string
  paper?: Paper
  startTime: string
  endTime: string
  duration: number
  passingScore: number
  departmentIds: number[]
  maxAttempts: number
  isPublished: boolean
  participantCount: number   // 后端统计
  completedCount: number     // 后端统计
  passedCount: number        // 后端统计
  createdAt: string
}
```

#### 6.4.3 考试CRUD接口
- `POST /exam` - 创建考试
- `PUT /exam/{id}` - 更新考试
- `DELETE /exam/{id}` - 删除考试
- `PUT /exam/{id}/publish` - 发布/取消发布考试

### 6.5 考试记录管理

#### 6.5.1 查看考试记录
**接口**: `GET /exam/record/list`
**作用**: 管理员查看学员考试记录
**权限**: 需要考试管理权限

**请求参数**:
```typescript
interface ExamRecordParams extends PaginationParams {
  examId?: string
  studentId?: string   // 学员ID
  status?: 'not_started' | 'in_progress' | 'completed' | 'timeout'
  departmentId?: number
  passed?: boolean     // 是否通过
}
```

#### 6.5.2 查看考试详细答题
**接口**: `GET /exam/record/{recordId}/detail`
**作用**: 管理员查看学员考试答题详情
**权限**: 需要考试管理权限

**响应数据**:
```typescript
interface ExamRecordDetail {
  recordId: string
  student: {
    id: string
    name: string
    employeeId?: string
  }
  exam: {
    id: string
    title: string
  }
  score: number
  totalScore: number
  passed: boolean
  duration: number
  startTime: string
  endTime: string
  answers: ExamAnswer[]
}
```

#### 6.5.3 手动阅卷
**接口**: `PUT /exam/record/{recordId}/grade`
**作用**: 管理员手动阅卷（主要针对主观题）
**权限**: 需要考试管理权限

**请求参数**:
```typescript
interface GradeRequest {
  answers: {
    questionId: string
    score: number
    feedback?: string
  }[]
}
```

---

## 7. 积分管理模块

### 7.1 查看积分记录
**接口**: `GET /points/record/list`
**作用**: 管理员查看学员积分记录
**权限**: 需要积分管理权限

**请求参数**:
```typescript
interface PointsRecordParams extends PaginationParams {
  studentId?: string   // 学员ID
  type?: 'course_complete' | 'sign_in' | 'exchange' | 'admin_adjust' | 'other'
  startDate?: string   // 开始日期
  endDate?: string     // 结束日期
  departmentId?: number
}
```

**响应数据**: `PaginationResponse<PointsRecord>`

### 7.2 积分规则管理

#### 7.2.1 获取积分规则列表
**接口**: `GET /points/rule/list`
**权限**: 需要积分管理权限

#### 7.2.2 积分规则CRUD接口
- `GET /points/rule/{id}` - 获取规则详情
- `POST /points/rule` - 创建规则
- `PUT /points/rule/{id}` - 更新规则
- `DELETE /points/rule/{id}` - 删除规则

### 7.3 积分调整
**接口**: `POST /points/adjust`
**作用**: 管理员手动调整学员积分
**权限**: 需要积分管理权限

**请求参数**:
```typescript
interface PointsAdjustRequest {
  studentId: string
  points: number       // 积分变动（正数增加，负数减少）
  description: string  // 调整说明
}
```

### 7.4 积分统计
**接口**: `GET /points/statistics`
**作用**: 管理员查看积分使用统计
**权限**: 需要积分管理权限

**请求参数**:
```typescript
interface PointsStatsParams {
  startDate?: string
  endDate?: string
  departmentId?: number
}
```

---

## 8. 积分商品模块

### 8.1 获取商品列表
**接口**: `GET /product/list`
**作用**: 管理员查看积分商品列表
**权限**: 需要商品管理权限

**请求参数**:
```typescript
interface ProductListParams extends PaginationParams {
  category?: string    // 商品分类
  status?: 'on_shelf' | 'off_shelf' | 'out_of_stock'
  keyword?: string     // 搜索关键词
  minPrice?: number    // 最低积分价格
  maxPrice?: number    // 最高积分价格
}
```

### 8.2 商品CRUD接口
- `GET /product/{id}` - 获取商品详情
- `POST /product` - 创建商品
- `PUT /product/{id}` - 更新商品
- `DELETE /product/{id}` - 删除商品

### 8.3 商品上下架
**接口**: `PUT /product/{id}/status`
**作用**: 管理员更新商品状态
**权限**: 需要商品管理权限

### 8.4 库存管理
**接口**: `PUT /product/{id}/stock`
**作用**: 管理员调整商品库存
**权限**: 需要商品管理权限

**请求参数**:
```typescript
interface UpdateStockRequest {
  stock: number
  operation: 'set' | 'add' | 'subtract'  // 设置/增加/减少
  remark?: string
}
```

---

## 9. 积分兑换模块

### 9.1 查看兑换记录
**接口**: `GET /exchange/list`
**作用**: 管理员查看学员兑换记录
**权限**: 需要兑换管理权限

**请求参数**:
```typescript
interface ExchangeListParams extends PaginationParams {
  studentId?: string   // 学员ID
  status?: 'pending' | 'approved' | 'shipped' | 'completed' | 'cancelled'
  startDate?: string
  endDate?: string
  productId?: string
}
```

### 9.2 兑换订单管理

#### 9.2.1 审核兑换订单
**接口**: `PUT /exchange/{id}/review`
**作用**: 管理员审核兑换订单
**权限**: 需要兑换管理权限

**请求参数**:
```typescript
interface ReviewExchangeRequest {
  status: 'approved' | 'rejected'
  remark?: string
}
```

#### 9.2.2 订单发货
**接口**: `PUT /exchange/{id}/ship`
**作用**: 管理员标记订单发货
**权限**: 需要兑换管理权限

**请求参数**:
```typescript
interface ShipExchangeRequest {
  expressCompany: string
  expressNumber: string
}
```

#### 9.2.3 完成订单
**接口**: `PUT /exchange/{id}/complete`
**作用**: 管理员完成兑换订单
**权限**: 需要兑换管理权限

---

## 10. 论坛管理模块

### 10.1 论坛分类管理

#### 10.1.1 获取分类列表
**接口**: `GET /forum/category/list`
**作用**: 管理员查看论坛分类列表
**权限**: 需要论坛管理权限

#### 10.1.2 分类CRUD接口
- `GET /forum/category/{id}` - 获取分类详情
- `POST /forum/category` - 创建分类
- `PUT /forum/category/{id}` - 更新分类
- `DELETE /forum/category/{id}` - 删除分类

### 10.2 帖子管理

#### 10.2.1 获取帖子列表
**接口**: `GET /forum/post/list`
**作用**: 管理员查看学员发布的帖子
**权限**: 需要论坛管理权限

**请求参数**:
```typescript
interface PostListParams extends PaginationParams {
  categoryId?: number
  authorId?: string    // 发帖学员ID
  status?: 'pending' | 'approved' | 'rejected'
  isTop?: boolean
  isEssence?: boolean
  keyword?: string
  departmentId?: number
}
```

#### 10.2.2 获取帖子详情
**接口**: `GET /forum/post/{id}`
**作用**: 管理员查看帖子详细信息
**权限**: 需要论坛管理权限

#### 10.2.3 帖子审核管理
- `PUT /forum/post/{id}/status` - 审核帖子
- `PUT /forum/post/{id}/top` - 置顶帖子
- `PUT /forum/post/{id}/essence` - 设为精华
- `DELETE /forum/post/{id}` - 删除帖子

### 10.3 评论管理

#### 10.3.1 获取评论列表
**接口**: `GET /forum/comment/list`
**作用**: 管理员查看学员评论
**权限**: 需要论坛管理权限

#### 10.3.2 评论管理接口
- `PUT /forum/comment/{id}/status` - 审核评论
- `DELETE /forum/comment/{id}` - 删除评论

### 10.4 违规举报处理

#### 10.4.1 查看举报列表
**接口**: `GET /forum/report/list`
**作用**: 管理员查看违规举报
**权限**: 需要论坛管理权限

**请求参数**:
```typescript
interface ReportListParams extends PaginationParams {
  status?: 'pending' | 'processed' | 'ignored'
  contentType?: 'post' | 'comment'
  reporterId?: string  // 举报人ID
}
```

#### 10.4.2 处理举报
**接口**: `PUT /forum/report/{id}/process`
**作用**: 管理员处理举报
**权限**: 需要论坛管理权限

**请求参数**:
```typescript
interface ProcessReportRequest {
  status: 'processed' | 'ignored'
  result?: string
  action?: 'delete' | 'warn' | 'none'  // 处理动作
}
```

---

## 11. 内容管理模块

### 11.1 轮播图管理

#### 11.1.1 获取轮播图列表
**接口**: `GET /content/carousel/list`
**作用**: 管理员查看轮播图列表
**权限**: 需要内容管理权限

#### 11.1.2 轮播图CRUD接口
- `GET /content/carousel/{id}` - 获取轮播图详情
- `POST /content/carousel` - 创建轮播图
- `PUT /content/carousel/{id}` - 更新轮播图
- `DELETE /content/carousel/{id}` - 删除轮播图

### 11.2 新闻资讯管理

#### 11.2.1 获取新闻列表
**接口**: `GET /content/news/list`
**权限**: 需要内容管理权限

#### 11.2.2 新闻CRUD接口
- `GET /content/news/{id}` - 获取新闻详情
- `POST /content/news` - 创建新闻
- `PUT /content/news/{id}` - 更新新闻
- `DELETE /content/news/{id}` - 删除新闻
- `PUT /content/news/{id}/publish` - 发布/取消发布新闻

### 11.3 公告通知管理

#### 11.3.1 获取公告列表
**接口**: `GET /content/notice/list`
**权限**: 需要内容管理权限

#### 11.3.2 公告CRUD接口
- `GET /content/notice/{id}` - 获取公告详情
- `POST /content/notice` - 创建公告
- `PUT /content/notice/{id}` - 更新公告
- `DELETE /content/notice/{id}` - 删除公告

---

## 12. 文件资源模块

### 12.1 文件上传
**接口**: `POST /resource/upload`
**作用**: 管理员上传文件
**权限**: 需要认证

**请求参数**:
- **Content-Type**: `multipart/form-data`
- **Form字段**:
  - `file`: 文件对象
  - `category?`: 文件分类 (`image` | `video` | `document` | `other`)
  - `module?`: 所属模块

**响应数据**:
```typescript
interface UploadResponse {
  id: string
  originalName: string
  fileName: string
  filePath: string
  fileSize: number
  fileType: string
  mimeType: string
  category: string
  url: string          // 文件访问URL
}
```

### 12.2 文件管理
- `GET /resource/file/{id}` - 获取文件信息
- `GET /resource/file/list` - 获取文件列表
- `DELETE /resource/file/{id}` - 删除文件

---

## 13. 统计分析模块

### 13.1 系统概览统计
**接口**: `GET /statistics/dashboard`
**作用**: 管理员查看系统概览统计数据
**权限**: 需要管理员权限

**响应数据**:
```typescript
interface DashboardStatistics {
  userStats: {
    totalStudents: number
    activeStudents: number      // 最近30天活跃学员
    totalAdmins: number
  }
  courseStats: {
    totalCourses: number
    publishedCourses: number
    totalStudyTime: number      // 总学习时长
    averageCompletion: number   // 平均完成率
  }
  examStats: {
    totalExams: number
    totalParticipants: number
    averageScore: number
    passRate: number           // 通过率
  }
  forumStats: {
    totalPosts: number
    totalComments: number
    activeUsers: number        // 活跃用户数
  }
  pointsStats: {
    totalPointsIssued: number  // 总发放积分
    totalPointsUsed: number    // 总消费积分
    totalExchanges: number     // 总兑换次数
  }
}
```

### 13.2 学习统计分析
**接口**: `GET /statistics/learning`
**作用**: 管理员查看学习数据统计分析
**权限**: 需要管理员权限

**请求参数**:
```typescript
interface LearningStatsParams {
  startDate?: string
  endDate?: string
  departmentId?: number
  courseId?: string
  type?: 'overview' | 'trend' | 'ranking'  // 统计类型
}
```

### 13.3 考试统计分析
**接口**: `GET /statistics/exam`
**作用**: 管理员查看考试数据统计分析
**权限**: 需要管理员权限

### 13.4 学员个人统计
**接口**: `GET /statistics/student/{studentId}`
**作用**: 管理员查看指定学员的学习统计
**权限**: 需要管理员权限

**响应数据**:
```typescript
interface StudentStatistics {
  studentInfo: {
    id: string
    name: string
    employeeId?: string
    department?: string
  }
  studyStats: {
    totalCourses: number
    completedCourses: number
    totalStudyTime: number
    averageScore: number
  }
  examStats: {
    totalExams: number
    passedExams: number
    averageScore: number
    bestScore: number
  }
  pointsStats: {
    currentPoints: number
    totalEarned: number
    totalSpent: number
    rank: number              // 积分排名
  }
  forumStats: {
    totalPosts: number
    totalComments: number
  }
}
```

---

## 14. 错误代码说明

### 通用错误代码
| 代码 | 说明 |
|------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如用户名已存在） |
| 422 | 请求参数验证失败 |
| 500 | 服务器内部错误 |

### 业务错误代码
| 代码 | 说明 |
|------|------|
| 1001 | 用户名或密码错误 |
| 1002 | 用户已被禁用 |
| 1003 | 用户不存在 |
| 1004 | 权限不足 |
| 2001 | 学员不存在 |
| 2002 | 学员已被禁用 |
| 3001 | 课程不存在 |
| 3002 | 课程未发布 |
| 4001 | 考试不存在 |
| 4002 | 考试未开始 |
| 4003 | 考试已结束 |
| 5001 | 积分不足 |
| 5002 | 商品库存不足 |
| 5003 | 商品已下架 |

---

## 15. 接口调用示例

### 管理员登录示例
```javascript
// 管理员登录
const response = await fetch('/api/user/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'admin123',
    remember: true
  })
});

const data = await response.json();
if (data.code === 200) {
  // 保存token
  localStorage.setItem('token', data.data.token);
}
```

### 查看学员学习记录示例
```javascript
// 查看特定学员的学习记录
const response = await fetch('/api/course/study-record/list?studentId=student001&page=1&size=10', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  }
});

const data = await response.json();
```

### 审核兑换订单示例
```javascript
// 审核兑换订单
const response = await fetch('/api/exchange/exchange001/review', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  },
  body: JSON.stringify({
    status: 'approved',
    remark: '审核通过，准备发货'
  })
});
```

---

## 16. 注意事项

1. **管理端专用**: 本系统仅供管理员使用，所有接口都基于管理员角色设计

2. **权限控制**: 
   - 所有接口都需要管理员认证
   - 根据管理员分配的权限访问不同功能模块
   - 支持细粒度的权限控制

3. **数据范围**: 管理员可以查看和管理所有学员的数据

4. **操作审计**: 重要操作需要记录操作日志，包括操作人、操作时间、操作内容

5. **数据安全**: 
   - 敏感信息（如密码）需要加密存储
   - 重要操作需要二次确认
   - 支持操作回滚机制

6. **统计数据**: 所有统计数据实时计算，确保数据准确性

7. **批量操作**: 支持批量管理学员、课程、考试等

8. **导入导出**: 支持数据的批量导入和导出功能

9. **系统监控**: 提供系统性能监控和异常告警

10. **备份恢复**: 支持数据备份和恢复功能

这份管理端API接口文档为后端开发提供了完整的实现指导，确保管理功能的完整性和易用性。