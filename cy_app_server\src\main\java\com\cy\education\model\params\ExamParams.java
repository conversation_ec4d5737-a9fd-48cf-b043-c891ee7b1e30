package com.cy.education.model.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 考试参数类
 */
@Data
@ApiModel("考试参数")
public class ExamParams {

    /**
     * 考试标题
     */
    @NotBlank(message = "考试标题不能为空")
    @Size(max = 200, message = "考试标题长度不能超过200个字符")
    @ApiModelProperty(value = "考试标题", required = true)
    private String title;

    /**
     * 考试描述
     */
    @Size(max = 500, message = "考试描述长度不能超过500个字符")
    @ApiModelProperty("考试描述")
    private String description;

    /**
     * 试卷ID
     */
    @NotNull(message = "试卷ID不能为空")
    @ApiModelProperty(value = "试卷ID", required = true)
    private Integer paperId;

    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true, example = "2023-12-01 10:00:00")
    private String startTime;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true, example = "2023-12-01 12:00:00")
    private String endTime;

    /**
     * 考试时长(分钟)
     */
    @NotNull(message = "考试时长不能为空")
    @ApiModelProperty(value = "考试时长(分钟)", required = true, example = "120")
    private Integer duration;

    /**
     * 及格分数
     */
    @NotNull(message = "及格分数不能为空")
    @ApiModelProperty(value = "及格分数", required = true, example = "60")
    private Integer passingScore;

    /**
     * 最大考试次数
     */
    @ApiModelProperty(value = "最大考试次数", example = "1")
    private Integer maxAttempts = 1;

    /**
     * 参考部门ID列表
     */
    @NotNull(message = "参考部门不能为空")
    @Size(min = 1, message = "至少选择一个参考部门")
    @ApiModelProperty(value = "参考部门ID列表", required = true)
    private List<Integer> departmentIds;
}
