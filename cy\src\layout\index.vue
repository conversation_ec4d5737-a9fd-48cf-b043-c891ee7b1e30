<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="layout-header" height="60px">
      <div style="display: flex; align-items: center; justify-content: space-between; height: 100%; padding: 0 24px;">
        <div style="display: flex; align-items: center;">
          <el-button 
            :icon="collapsed ? Expand : Fold" 
            text 
            @click="collapsed = !collapsed"
            style="margin-right: 16px;"
          />
          <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div style="display: flex; align-items: center; gap: 16px;">
          <el-badge :value="5" class="item">
            <el-button :icon="Bell" circle />
          </el-badge>
          <el-dropdown @command="handleCommand">
            <div style="display: flex; align-items: center; cursor: pointer;">
              <el-avatar :size="32" :src="userInfo?.avatar || 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'" />
              <span style="margin-left: 8px;">{{ userInfo?.name || '管理员' }}</span>
              <el-icon style="margin-left: 4px;"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="settings">设置</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <div class="layout-main">
      <!-- 侧边栏 -->
      <el-aside class="layout-sidebar" :class="{ collapsed }" :width="collapsed ? '64px' : '256px'">
        <div style="padding: 16px; text-align: center; border-bottom: 1px solid #1f2d3d;">
          <img 
            src="https://element-plus.org/images/element-plus-logo-small.svg" 
            style="height: 32px; vertical-align: middle;"
            v-if="!collapsed"
          />
          <img 
            src="https://element-plus.org/images/element-plus-logo-small.svg" 
            style="height: 24px; vertical-align: middle;"
            v-else
          />
          <h2 v-if="!collapsed" style="color: #fff; margin-top: 8px; font-size: 16px;">
            矿山学习社区
          </h2>
        </div>
        <el-menu
          :default-active="$route.path"
          :collapse="collapsed"
          background-color="#001529"
          text-color="#fff"
          active-text-color="#fff"
          router
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <el-sub-menu v-if="route.children && route.children.length" :index="route.path">
              <template #title>
                <el-icon>
                  <component :is="route.meta?.icon || 'Document'" />
                </el-icon>
                <span>{{ route.meta?.title }}</span>
              </template>
              <el-menu-item 
                v-for="child in route.children" 
                :key="child.path" 
                :index="child.path"
              >
                <el-icon>
                  <component :is="child.meta?.icon || 'Document'" />
                </el-icon>
                <span>{{ child.meta?.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item v-else :index="route.path">
              <el-icon>
                <component :is="route.meta?.icon || 'Document'" />
              </el-icon>
              <span>{{ route.meta?.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>

      <!-- 内容区域 -->
      <main class="layout-content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Fold, Expand, Bell, ArrowDown, Document } from '@element-plus/icons-vue'
import { menuData } from '@/router/menu'
import { useUserStore } from '@/store/modules/user'
import { ElMessageBox, ElLoading } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const collapsed = ref(false)

// 获取用户信息
const userInfo = computed(() => userStore.userInfo)

// 获取菜单路由
const menuRoutes = computed(() => {
  return menuData
})

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  if (command === 'logout') {
    ElMessageBox.confirm('确定要退出登录吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const loading = ElLoading.service({
        lock: true,
        text: '正在退出登录...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      // 调用退出登录方法
      console.log('用户确认退出登录，开始执行退出流程')
      
      // 退出登录方法会自动清除状态并跳转到登录页
      userStore.logout()
      
      // 确保loading状态能够关闭
      setTimeout(() => {
        loading.close()
      }, 1000)
    }).catch(() => {
      console.log('用户取消退出登录')
    })
  } else if (command === 'profile') {
    // 跳转到个人中心页面
    router.push('/profile')
  } else if (command === 'settings') {
    // 跳转到设置页面
    router.push('/settings')
  }
}

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta?.title
  }))
})

// 组件挂载时获取用户信息
onMounted(() => {
  if (userStore.token && !userStore.userInfo) {
    userStore.getUserInfoAction()
  }
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: relative;
  z-index: 1001;
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  background: #001529;
  transition: width 0.2s;
  overflow: hidden;
}

.layout-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f5f7fa;
}

:deep(.el-menu) {
  border-right: none;
}

:deep(.el-sub-menu .el-menu-item) {
  background-color: #000c17 !important;
}

:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover) {
  background-color: #1f2d3d !important;
}

:deep(.el-menu-item.is-active) {
  background-color: #409eff !important;
  color: #fff !important;
}
</style> 