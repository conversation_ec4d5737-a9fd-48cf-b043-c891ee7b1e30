package com.cy.education.model.vo;

import com.cy.education.model.entity.exam.ExamRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 考试记录VO类
 */
@Data
@ApiModel("考试记录信息")
public class ExamRecordVO extends ExamRecord {

    /**
     * 考试标题
     */
    @ApiModelProperty("考试标题")
    private String examTitle;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;

    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String departmentName;

    /**
     * 及格分数
     */
    @ApiModelProperty("及格分数")
    private Integer passingScore;

    /**
     * 答题记录
     */
    @ApiModelProperty("答题记录")
    private List<ExamAnswerVO> answers;

}
