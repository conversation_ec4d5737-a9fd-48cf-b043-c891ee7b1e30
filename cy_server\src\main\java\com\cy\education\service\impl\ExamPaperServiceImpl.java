package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BadRequestException;
import com.cy.education.exception.NotFoundException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.cy.education.model.entity.ExamPaper;
import com.cy.education.model.entity.ExamPaperQuestion;
import com.cy.education.model.entity.ExamQuestion;
import com.cy.education.model.params.ExamPaperParams;
import com.cy.education.model.params.ExamPaperQuestionParams;
import com.cy.education.model.params.ExamPaperQueryParams;
import com.cy.education.model.vo.ExamPaperQuestionVO;
import com.cy.education.model.vo.ExamPaperVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.ExamPaperMapper;
import com.cy.education.repository.ExamPaperQuestionMapper;
import com.cy.education.repository.ExamQuestionMapper;
import com.cy.education.service.ExamPaperService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 试卷服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExamPaperServiceImpl implements ExamPaperService {

    private final ExamPaperMapper examPaperMapper;
    private final ExamPaperQuestionMapper examPaperQuestionMapper;
    private final ExamQuestionMapper examQuestionMapper;

    @Override
    public PageResponse<ExamPaperVO> listPapers(ExamPaperQueryParams params) {
        // 构建分页对象
        Page<ExamPaper> page = new Page<>(params.getPage(), params.getSize());
        
        // 分页查询试卷列表
        IPage<ExamPaper> paperPage = examPaperMapper.selectPaperPage(
            page, 
            params.getKeyword(), 
            params.getIsPublished(),
            params.getSortBy(),
            params.getSortOrder()
        );
        
        // 转换为VO对象
        List<ExamPaperVO> voList = paperPage.getRecords().stream().map(paper -> {
            ExamPaperVO vo = new ExamPaperVO();
            BeanUtils.copyProperties(paper, vo);

            // 统计题目数量
            LambdaQueryWrapper<ExamPaperQuestion> questionQueryWrapper = new LambdaQueryWrapper<>();
            questionQueryWrapper.eq(ExamPaperQuestion::getPaperId, paper.getId());
            long questionCount = examPaperQuestionMapper.selectCount(questionQueryWrapper);
            vo.setQuestionCount((int) questionCount);

            return vo;
        }).collect(Collectors.toList());
        
        return PageResponse.of(voList, paperPage.getTotal(), params.getPage(), params.getSize());
    }

    @Override
    public ExamPaperVO getPaperDetail(Integer id) {
        // 查询试卷基本信息
        ExamPaper paper = examPaperMapper.selectById(id);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        
        // 转换为VO对象
        ExamPaperVO vo = new ExamPaperVO();
        BeanUtils.copyProperties(paper, vo);
        
        // 查询试卷题目列表
        List<ExamPaperQuestionVO> questions = examPaperQuestionMapper.selectQuestionsByPaperId(id);
        vo.setQuestions(questions);
        vo.setQuestionCount(questions != null ? questions.size() : 0);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createPaper(ExamPaperParams params, String createdBy) {
        // 检查试卷标题是否已存在
        LambdaQueryWrapper<ExamPaper> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamPaper::getTitle, params.getTitle());
        if (examPaperMapper.selectCount(queryWrapper) > 0) {
            throw new BadRequestException("试卷标题已存在");
        }
        
        // 创建试卷实体对象
        ExamPaper paper = new ExamPaper();
        BeanUtils.copyProperties(params, paper);
        paper.setCreatedBy(createdBy);
        paper.setCreatedAt(LocalDateTime.now());
        paper.setUpdatedAt(LocalDateTime.now());
        
        // 插入数据库
        examPaperMapper.insert(paper);
        
        return paper.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePaper(Integer id, ExamPaperParams params) {
        // 查询试卷是否存在
        ExamPaper paper = examPaperMapper.selectById(id);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        
        // 如果试卷已发布，则不允许修改
        if (paper.getIsPublished()) {
            throw new BadRequestException("试卷已发布，不能修改");
        }
        
        // 检查试卷标题是否已存在(排除自身)
        if (!paper.getTitle().equals(params.getTitle())) {
            LambdaQueryWrapper<ExamPaper> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExamPaper::getTitle, params.getTitle());
            queryWrapper.ne(ExamPaper::getId, id);
            if (examPaperMapper.selectCount(queryWrapper) > 0) {
                throw new BadRequestException("试卷标题已存在");
            }
        }
        
        // 更新试卷信息
        BeanUtils.copyProperties(params, paper);
        paper.setUpdatedAt(LocalDateTime.now());
        
        // 更新数据库
        return examPaperMapper.updateById(paper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePaper(Integer id) {
        // 查询试卷是否存在
        ExamPaper paper = examPaperMapper.selectById(id);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        
        // 如果试卷已发布，则不允许删除
        if (paper.getIsPublished()) {
            throw new BadRequestException("试卷已发布，不能删除");
        }
        
        // 删除试卷题目关联
        examPaperQuestionMapper.deleteByPaperId(id);
        
        // 删除试卷
        return examPaperMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishPaper(Integer id, Boolean isPublished) {
        // 查询试卷是否存在
        ExamPaper paper = examPaperMapper.selectById(id);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        
        // 如果要发布试卷，则检查试卷是否有题目
        if (isPublished) {
            LambdaQueryWrapper<ExamPaperQuestion> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExamPaperQuestion::getPaperId, id);
            if (examPaperQuestionMapper.selectCount(queryWrapper) == 0) {
                throw new BadRequestException("试卷没有题目，不能发布");
            }
        }
        
        // 更新试卷发布状态
        paper.setIsPublished(isPublished);
        paper.setUpdatedAt(LocalDateTime.now());
        
        return examPaperMapper.updateById(paper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addQuestionsToPaper(Integer paperId, List<ExamPaperQuestionParams> questions) {
        // 检查试卷是否存在
        ExamPaper paper = examPaperMapper.selectById(paperId);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        
        // 如果试卷已发布，则不允许添加题目
        if (paper.getIsPublished()) {
            throw new BadRequestException("试卷已发布，不能添加题目");
        }
        
        // 检查题目是否存在
        List<Integer> questionIds = questions.stream()
                .map(ExamPaperQuestionParams::getQuestionId)
                .collect(Collectors.toList());
        
        List<ExamQuestion> existQuestions = examQuestionMapper.selectBatchIds(questionIds);
        if (existQuestions.size() != questionIds.size()) {
            throw new BadRequestException("部分题目不存在");
        }
        
        // 检查题目是否已经在试卷中
        LambdaQueryWrapper<ExamPaperQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamPaperQuestion::getPaperId, paperId)
                .in(ExamPaperQuestion::getQuestionId, questionIds);
        List<ExamPaperQuestion> existPaperQuestions = examPaperQuestionMapper.selectList(queryWrapper);
        
        if (!CollectionUtils.isEmpty(existPaperQuestions)) {
            List<Integer> existQuestionIds = existPaperQuestions.stream()
                    .map(ExamPaperQuestion::getQuestionId)
                    .collect(Collectors.toList());
            throw new BadRequestException("题目已在试卷中: " + StringUtils.collectionToCommaDelimitedString(existQuestionIds));
        }
        
        // 准备批量插入的数据
        List<ExamPaperQuestion> paperQuestions = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 获取当前最大的排序号
        Integer maxOrder = 0;
        LambdaQueryWrapper<ExamPaperQuestion> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.eq(ExamPaperQuestion::getPaperId, paperId)
                .orderByDesc(ExamPaperQuestion::getQuestionOrder);
        orderQuery.last("LIMIT 1");
        ExamPaperQuestion lastQuestion = examPaperQuestionMapper.selectOne(orderQuery);
        if (lastQuestion != null) {
            maxOrder = lastQuestion.getQuestionOrder();
        }
        
        // 转换为实体对象
        for (int i = 0; i < questions.size(); i++) {
            ExamPaperQuestionParams questionParams = questions.get(i);
            ExamPaperQuestion paperQuestion = new ExamPaperQuestion();
            paperQuestion.setPaperId(paperId);
            paperQuestion.setQuestionId(questionParams.getQuestionId());
            paperQuestion.setScore(questionParams.getScore());
            
            // 如果没有指定排序号，则自动生成
            if (questionParams.getQuestionOrder() == null) {
                paperQuestion.setQuestionOrder(maxOrder + i + 1);
            } else {
                paperQuestion.setQuestionOrder(questionParams.getQuestionOrder());
            }
            
            paperQuestion.setCreatedAt(now);
            paperQuestions.add(paperQuestion);
        }
        
        // 批量插入
        if (paperQuestions.size() > 0) {
            examPaperQuestionMapper.batchInsert(paperQuestions);
            
            // 更新试卷总分
            updatePaperTotalScore(paperId);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeQuestionFromPaper(Integer paperId, Integer questionId) {
        // 检查试卷是否存在
        ExamPaper paper = examPaperMapper.selectById(paperId);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        
        // 如果试卷已发布，则不允许移除题目
        if (paper.getIsPublished()) {
            throw new BadRequestException("试卷已发布，不能移除题目");
        }
        
        // 检查题目是否在试卷中
        LambdaQueryWrapper<ExamPaperQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamPaperQuestion::getPaperId, paperId)
                .eq(ExamPaperQuestion::getQuestionId, questionId);
        if (examPaperQuestionMapper.selectCount(queryWrapper) == 0) {
            throw new BadRequestException("题目不在试卷中");
        }
        
        // 删除题目关联
        examPaperQuestionMapper.deleteByPaperIdAndQuestionId(paperId, questionId);
        
        // 更新试卷总分
        updatePaperTotalScore(paperId);
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePaperQuestions(Integer paperId, List<ExamPaperQuestionParams> questions) {
        // 检查试卷是否存在
        ExamPaper paper = examPaperMapper.selectById(paperId);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        
        // 如果试卷已发布，则不允许修改题目
        if (paper.getIsPublished()) {
            throw new BadRequestException("试卷已发布，不能修改题目");
        }
        
        // 检查题目是否存在
        List<Integer> questionIds = questions.stream()
                .map(ExamPaperQuestionParams::getQuestionId)
                .collect(Collectors.toList());
        
        List<ExamQuestion> existQuestions = examQuestionMapper.selectBatchIds(questionIds);
        if (existQuestions.size() != questionIds.size()) {
            throw new BadRequestException("部分题目不存在");
        }
        
        // 先删除试卷中的所有题目
        examPaperQuestionMapper.deleteByPaperId(paperId);
        
        // 准备批量插入的数据
        List<ExamPaperQuestion> paperQuestions = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 转换为实体对象
        for (int i = 0; i < questions.size(); i++) {
            ExamPaperQuestionParams questionParams = questions.get(i);
            ExamPaperQuestion paperQuestion = new ExamPaperQuestion();
            paperQuestion.setPaperId(paperId);
            paperQuestion.setQuestionId(questionParams.getQuestionId());
            paperQuestion.setScore(questionParams.getScore());
            
            // 使用传入的排序号或者顺序号
            if (questionParams.getQuestionOrder() == null) {
                paperQuestion.setQuestionOrder(i + 1);
            } else {
                paperQuestion.setQuestionOrder(questionParams.getQuestionOrder());
            }
            
            paperQuestion.setCreatedAt(now);
            paperQuestions.add(paperQuestion);
        }
        
        // 批量插入
        if (!paperQuestions.isEmpty()) {
            examPaperQuestionMapper.batchInsert(paperQuestions);
            
            // 更新试卷总分
            updatePaperTotalScore(paperId);
        }
        
        return true;
    }

    /**
     * 更新试卷总分
     *
     * @param paperId 试卷ID
     */
    private void updatePaperTotalScore(Integer paperId) {
        // 查询试卷所有题目的分值之和
        LambdaQueryWrapper<ExamPaperQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamPaperQuestion::getPaperId, paperId);
        List<ExamPaperQuestion> questions = examPaperQuestionMapper.selectList(queryWrapper);
        
        int totalScore = questions.stream().mapToInt(ExamPaperQuestion::getScore).sum();
        
        // 更新试卷总分
        ExamPaper paper = examPaperMapper.selectById(paperId);
        paper.setTotalScore(totalScore);
        paper.setUpdatedAt(LocalDateTime.now());
        
        examPaperMapper.updateById(paper);
    }

    @Override
    public void exportPaper(Integer paperId, Map<String, Object> options, HttpServletResponse response) throws IOException {
        // 查询试卷信息
        ExamPaper paper = examPaperMapper.selectById(paperId);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }

        // 获取导出选项
        String format = (String) options.getOrDefault("format", "docx");
        boolean includeAnswers = (Boolean) options.getOrDefault("includeAnswers", true);
        boolean includeExplanations = (Boolean) options.getOrDefault("includeExplanations", true);
        String answerPosition = (String) options.getOrDefault("answerPosition", "inline");

        if ("docx".equals(format)) {
            exportPaperAsWord(paper, paperId, includeAnswers, includeExplanations, answerPosition, response);
        } else {
            exportPaperAsText(paper, paperId, includeAnswers, includeExplanations, answerPosition, response);
        }
    }

    /**
     * 导出为Word格式
     */
    private void exportPaperAsWord(ExamPaper paper, Integer paperId, boolean includeAnswers, boolean includeExplanations, String answerPosition, HttpServletResponse response) throws IOException {
        // 创建Word文档
        XWPFDocument document = new XWPFDocument();

        // 设置标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(paper.getTitle());
        titleRun.setBold(true);
        titleRun.setFontSize(18);
        titleRun.setFontFamily("宋体");

        // 添加试卷信息
        XWPFParagraph infoParagraph = document.createParagraph();
        XWPFRun infoRun = infoParagraph.createRun();
        infoRun.setText("总分：" + paper.getTotalScore() + "分    考试时长：" + paper.getDuration() + "分钟");
        infoRun.setFontFamily("宋体");
        infoRun.addBreak();
        infoRun.addBreak();

        // 查询试卷题目
        List<ExamPaperQuestionVO> questions = examPaperQuestionMapper.selectQuestionsByPaperId(paperId);

        // 存储答案和解析，用于附录模式
        List<String> answerList = new ArrayList<>();
        List<String> explanationList = new ArrayList<>();

        // 添加题目
        for (int i = 0; i < questions.size(); i++) {
            ExamPaperQuestionVO question = questions.get(i);

            // 题目标题
            XWPFParagraph questionParagraph = document.createParagraph();
            XWPFRun questionRun = questionParagraph.createRun();
            String questionTitle = question.getQuestion() != null ? question.getQuestion().getTitle() : "题目内容缺失";
            questionRun.setText((i + 1) + ". " + questionTitle + " (" + question.getScore() + "分)");
            questionRun.setBold(true);
            questionRun.setFontFamily("宋体");

            // 题目选项（如果是选择题）
            if (question.getQuestion() != null && question.getQuestion().getOptions() != null) {
                List<String> optionTexts = parseQuestionOptions(question.getQuestion().getOptions());
                if (!optionTexts.isEmpty()) {
                    // 添加选项标识符 A、B、C、D
                    String[] optionLabels = {"A", "B", "C", "D", "E", "F", "G", "H"};
                    for (int j = 0; j < optionTexts.size() && j < optionLabels.length; j++) {
                        XWPFParagraph optionParagraph = document.createParagraph();
                        XWPFRun optionRun = optionParagraph.createRun();
                        optionRun.setText(optionLabels[j] + ". " + optionTexts.get(j));
                        optionRun.setFontFamily("宋体");
                    }
                }
            }

            // 收集答案和解析
            if (question.getQuestion() != null) {
                if (question.getQuestion().getCorrectAnswer() != null) {
                    answerList.add("第" + (i + 1) + "题：" + question.getQuestion().getCorrectAnswer());
                }
                if (question.getQuestion().getExplanation() != null) {
                    explanationList.add("第" + (i + 1) + "题：" + question.getQuestion().getExplanation());
                }
            }

            // 内联模式：在题目下方显示答案和解析
            if ("inline".equals(answerPosition)) {
                // 答案（如果需要）
                if (includeAnswers && question.getQuestion() != null && question.getQuestion().getCorrectAnswer() != null) {
                    XWPFParagraph answerParagraph = document.createParagraph();
                    XWPFRun answerRun = answerParagraph.createRun();
                    answerRun.setText("【参考答案】" + question.getQuestion().getCorrectAnswer());
                    answerRun.setFontFamily("宋体");
                    answerRun.setColor("0000FF"); // 蓝色
                }

                // 解析（如果需要）
                if (includeExplanations && question.getQuestion() != null && question.getQuestion().getExplanation() != null) {
                    XWPFParagraph analysisParagraph = document.createParagraph();
                    XWPFRun analysisRun = analysisParagraph.createRun();
                    analysisRun.setText("【解析】" + question.getQuestion().getExplanation());
                    analysisRun.setFontFamily("宋体");
                    analysisRun.setColor("008000"); // 绿色
                }
            }

            // 添加空行
            XWPFParagraph spaceParagraph = document.createParagraph();
            spaceParagraph.createRun().addBreak();
        }

        // 附录模式：在文档末尾添加答案和解析
        if ("appendix".equals(answerPosition)) {
            // 添加答案部分
            if (includeAnswers && !answerList.isEmpty()) {
                // 添加分页符
                XWPFParagraph pageBreak = document.createParagraph();
                pageBreak.setPageBreak(true);

                // 答案标题
                XWPFParagraph answerTitleParagraph = document.createParagraph();
                answerTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun answerTitleRun = answerTitleParagraph.createRun();
                answerTitleRun.setText("参考答案");
                answerTitleRun.setBold(true);
                answerTitleRun.setFontSize(16);
                answerTitleRun.setFontFamily("宋体");

                // 添加空行
                XWPFParagraph emptyLine1 = document.createParagraph();
                emptyLine1.createRun().addBreak();

                // 添加答案列表
                for (String answer : answerList) {
                    XWPFParagraph answerParagraph = document.createParagraph();
                    XWPFRun answerRun = answerParagraph.createRun();
                    answerRun.setText(answer);
                    answerRun.setFontFamily("宋体");
                    answerRun.setColor("0000FF"); // 蓝色
                }
            }

            // 添加解析部分
            if (includeExplanations && !explanationList.isEmpty()) {
                // 添加空行
                XWPFParagraph emptyLine2 = document.createParagraph();
                emptyLine2.createRun().addBreak();

                // 解析标题
                XWPFParagraph explanationTitleParagraph = document.createParagraph();
                explanationTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun explanationTitleRun = explanationTitleParagraph.createRun();
                explanationTitleRun.setText("题目解析");
                explanationTitleRun.setBold(true);
                explanationTitleRun.setFontSize(16);
                explanationTitleRun.setFontFamily("宋体");

                // 添加空行
                XWPFParagraph emptyLine3 = document.createParagraph();
                emptyLine3.createRun().addBreak();

                // 添加解析列表
                for (String explanation : explanationList) {
                    XWPFParagraph explanationParagraph = document.createParagraph();
                    XWPFRun explanationRun = explanationParagraph.createRun();
                    explanationRun.setText(explanation);
                    explanationRun.setFontFamily("宋体");
                    explanationRun.setColor("008000"); // 绿色
                }
            }
        }

        // 设置响应头
        String answerSuffix = includeAnswers ? (includeExplanations ? "_含答案解析" : "_含答案") : "";
        String fileName = URLEncoder.encode(paper.getTitle() + answerSuffix + "_" +
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".docx",
            StandardCharsets.UTF_8.toString());

        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        // 写入响应
        document.write(response.getOutputStream());
        document.close();
    }

    /**
     * 导出为文本格式
     */
    private void exportPaperAsText(ExamPaper paper, Integer paperId, boolean includeAnswers, boolean includeExplanations, String answerPosition, HttpServletResponse response) throws IOException {
        // 设置响应头
        String answerSuffix = includeAnswers ? (includeExplanations ? "_含答案解析" : "_含答案") : "";
        String fileName = URLEncoder.encode(paper.getTitle() + answerSuffix + "_" +
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".txt",
            StandardCharsets.UTF_8.toString());

        response.setContentType("text/plain;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        // 构建内容
        StringBuilder content = new StringBuilder();
        content.append(paper.getTitle()).append("\n\n");
        content.append("总分：").append(paper.getTotalScore()).append("分    ");
        content.append("考试时长：").append(paper.getDuration()).append("分钟\n\n");

        // 查询试卷题目
        List<ExamPaperQuestionVO> questions = examPaperQuestionMapper.selectQuestionsByPaperId(paperId);

        // 存储答案和解析，用于附录模式
        List<String> answerList = new ArrayList<>();
        List<String> explanationList = new ArrayList<>();

        // 添加题目
        for (int i = 0; i < questions.size(); i++) {
            ExamPaperQuestionVO question = questions.get(i);
            String questionTitle = question.getQuestion() != null ? question.getQuestion().getTitle() : "题目内容缺失";
            content.append((i + 1)).append(". ").append(questionTitle)
                   .append(" (").append(question.getScore()).append("分)\n");

            // 题目选项
            if (question.getQuestion() != null && question.getQuestion().getOptions() != null) {
                List<String> optionTexts = parseQuestionOptions(question.getQuestion().getOptions());
                if (!optionTexts.isEmpty()) {
                    // 添加选项标识符 A、B、C、D
                    String[] optionLabels = {"A", "B", "C", "D", "E", "F", "G", "H"};
                    for (int j = 0; j < optionTexts.size() && j < optionLabels.length; j++) {
                        content.append(optionLabels[j]).append(". ").append(optionTexts.get(j)).append("\n");
                    }
                }
            }

            // 收集答案和解析
            if (question.getQuestion() != null) {
                if (question.getQuestion().getCorrectAnswer() != null) {
                    answerList.add("第" + (i + 1) + "题：" + question.getQuestion().getCorrectAnswer());
                }
                if (question.getQuestion().getExplanation() != null) {
                    explanationList.add("第" + (i + 1) + "题：" + question.getQuestion().getExplanation());
                }
            }

            // 内联模式：在题目下方显示答案和解析
            if ("inline".equals(answerPosition)) {
                // 答案
                if (includeAnswers && question.getQuestion() != null && question.getQuestion().getCorrectAnswer() != null) {
                    content.append("【参考答案】").append(question.getQuestion().getCorrectAnswer()).append("\n");
                }

                // 解析
                if (includeExplanations && question.getQuestion() != null && question.getQuestion().getExplanation() != null) {
                    content.append("【解析】").append(question.getQuestion().getExplanation()).append("\n");
                }
            }

            content.append("\n");
        }

        // 附录模式：在文档末尾添加答案和解析
        if ("appendix".equals(answerPosition)) {
            // 添加答案部分
            if (includeAnswers && !answerList.isEmpty()) {
                content.append("\n").append("=".repeat(50)).append("\n");
                content.append("参考答案\n");
                content.append("=".repeat(50)).append("\n\n");

                for (String answer : answerList) {
                    content.append(answer).append("\n");
                }
            }

            // 添加解析部分
            if (includeExplanations && !explanationList.isEmpty()) {
                content.append("\n").append("=".repeat(50)).append("\n");
                content.append("题目解析\n");
                content.append("=".repeat(50)).append("\n\n");

                for (String explanation : explanationList) {
                    content.append(explanation).append("\n");
                }
            }
        }

        // 写入响应
        response.getWriter().write(content.toString());
        response.getWriter().flush();
    }

    /**
     * 解析题目选项
     */
    @SuppressWarnings("unchecked")
    private List<String> parseQuestionOptions(Object options) {
        List<String> optionTexts = new ArrayList<>();

        try {
            if (options instanceof List) {
                List<Object> optionList = (List<Object>) options;
                for (Object option : optionList) {
                    if (option instanceof String) {
                        optionTexts.add((String) option);
                    } else if (option instanceof Map) {
                        Map<String, Object> optionMap = (Map<String, Object>) option;
                        // 处理 {"id":"A","content":"选项内容"} 格式
                        if (optionMap.containsKey("content")) {
                            optionTexts.add(String.valueOf(optionMap.get("content")));
                        } else if (optionMap.containsKey("text")) {
                            optionTexts.add(String.valueOf(optionMap.get("text")));
                        } else {
                            // 如果没有content或text字段，使用整个对象的字符串表示
                            optionTexts.add(option.toString());
                        }
                    } else {
                        optionTexts.add(String.valueOf(option));
                    }
                }
            } else if (options instanceof String) {
                String optionsStr = (String) options;
                // 如果是JSON字符串，尝试解析
                if (optionsStr.trim().startsWith("[") && optionsStr.trim().endsWith("]")) {
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        List<Object> parsedOptions = objectMapper.readValue(optionsStr, List.class);
                        for (Object option : parsedOptions) {
                            if (option instanceof String) {
                                optionTexts.add((String) option);
                            } else if (option instanceof Map) {
                                Map<String, Object> optionMap = (Map<String, Object>) option;
                                if (optionMap.containsKey("content")) {
                                    optionTexts.add(String.valueOf(optionMap.get("content")));
                                } else if (optionMap.containsKey("text")) {
                                    optionTexts.add(String.valueOf(optionMap.get("text")));
                                } else {
                                    optionTexts.add(option.toString());
                                }
                            } else {
                                optionTexts.add(String.valueOf(option));
                            }
                        }
                    } catch (Exception e) {
                        log.warn("JSON解析失败，使用简单字符串处理: {}", e.getMessage());
                        // 如果JSON解析失败，使用简单的字符串分割
                        optionsStr = optionsStr.trim();
                        if (optionsStr.startsWith("[")) optionsStr = optionsStr.substring(1);
                        if (optionsStr.endsWith("]")) optionsStr = optionsStr.substring(0, optionsStr.length() - 1);

                        String[] parts = optionsStr.split(",");
                        for (String part : parts) {
                            part = part.trim();
                            if (part.startsWith("\"") && part.endsWith("\"")) {
                                part = part.substring(1, part.length() - 1);
                            }
                            if (!part.isEmpty()) {
                                optionTexts.add(part);
                            }
                        }
                    }
                } else {
                    // 不是JSON格式，直接使用
                    optionTexts.add(optionsStr);
                }
            }
        } catch (Exception e) {
            log.error("解析题目选项失败", e);
        }

        return optionTexts;
    }

    @Override
    public void batchExportPapers(Map<String, Object> params, HttpServletResponse response) throws IOException {
        // 设置响应头
        String fileName = URLEncoder.encode("试卷列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".txt", StandardCharsets.UTF_8.toString());
        response.setContentType("text/plain;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        // 查询试卷列表
        LambdaQueryWrapper<ExamPaper> queryWrapper = new LambdaQueryWrapper<>();
        String keyword = (String) params.get("keyword");
        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(ExamPaper::getTitle, keyword);
        }

        List<ExamPaper> papers = examPaperMapper.selectList(queryWrapper);

        // 生成导出内容
        StringBuilder content = new StringBuilder();
        content.append("试卷列表导出\n");
        content.append("导出时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        content.append("总数量: ").append(papers.size()).append("\n\n");

        for (int i = 0; i < papers.size(); i++) {
            ExamPaper paper = papers.get(i);
            content.append(i + 1).append(". ").append(paper.getTitle()).append("\n");
            content.append("   描述: ").append(paper.getDescription() != null ? paper.getDescription() : "无").append("\n");
            content.append("   总分: ").append(paper.getTotalScore()).append("\n");
            content.append("   时长: ").append(paper.getDuration()).append("分钟\n");
            content.append("   创建时间: ").append(paper.getCreatedAt()).append("\n\n");
        }

        response.getWriter().write(content.toString());
        response.getWriter().flush();
    }
}