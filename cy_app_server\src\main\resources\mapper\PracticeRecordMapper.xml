<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.PracticeRecordMapper">

    <!-- 根据用户ID和状态查询练习记录 -->
    <select id="selectByUserIdAndStatus" resultType="com.cy.education.model.entity.PracticeRecord">
        SELECT pr.*, eb.name as bank_name FROM practice_record pr
        LEFT JOIN exam_bank eb ON pr.bank_id = eb.id
        WHERE pr.user_id = #{userId} AND pr.status = #{status}
        ORDER BY pr.created_at DESC
    </select>

    <!-- 根据用户ID和题库ID查询练习记录 -->
    <select id="selectByUserIdAndBankId" resultType="com.cy.education.model.entity.PracticeRecord">
        SELECT * FROM practice_record
        WHERE user_id = #{userId} AND bank_id = #{bankId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID和类型查询练习记录 -->
    <select id="selectByUserIdAndType" resultType="com.cy.education.model.entity.PracticeRecord">
        SELECT pr.*, eb.name as bank_name FROM practice_record pr
        LEFT JOIN exam_bank eb ON pr.bank_id = eb.id
        WHERE pr.user_id = #{userId} AND pr.type = #{type}
        ORDER BY pr.created_at DESC
    </select>

</mapper> 