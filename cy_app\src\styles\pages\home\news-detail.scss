// 新闻详情页样式

.news-title {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 12px;
}

.meta-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--text-tertiary);
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-light);
}

.cover-image {
  width: 100%;
  border-radius: 12px;
  margin-bottom: 20px;
  display: block;
}

.news-body {
  font-size: 16px;
  line-height: 1.8;
  color: var(--text-secondary);

  // 富文本样式重置
  :deep(p) {
    margin-bottom: 1em;
  }

  :deep(h3) {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 1.5em 0 1em;
    padding-bottom: 0.5em;
    border-bottom: 1px solid var(--border-light);
  }

  :deep(ul) {
    padding-left: 1.5em;
    margin-bottom: 1em;
  }

  :deep(li) {
    margin-bottom: 0.5em;
  }
}

.interaction-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--border-light);
}

.like-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 24px;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  transition: var(--transition-normal);

  .icon {
    font-size: 16px;
  }

  .text {
    font-size: 14px;
    font-weight: 500;
  }

  &.liked {
    background-color: var(--primary-color-soft);
    color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
  }
}
