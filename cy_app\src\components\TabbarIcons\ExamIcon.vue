<template>
	<svg :width="size" :height="size" viewBox="0 0 24 24" :fill="active ? '#fff' : '#8E8E93'">
		<path v-if="active" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
		<path v-if="active" d="M8,12V14H16V12H8M8,16V18H13V16H8Z"/>
		<path v-else d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
		<path v-else d="M8,12V14H16V12H8M8,16V18H13V16H8Z"/>
	</svg>
</template>

<script setup lang="ts">
interface Props {
	size?: string | number
	color?: string
	active?: boolean
}

withDefaults(defineProps<Props>(), {
	size: 24,
	color: 'currentColor',
	active: false
})
</script>