package com.cy.education.model.entity.exam;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 答题记录实体类
 */
@Data
@TableName("exam_answer")
public class ExamAnswer {

    /**
     * 答题ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 考试记录ID
     */
    private Integer recordId;

    /**
     * 题目ID
     */
    private Integer questionId;

    /**
     * 用户答案
     */
    private String answer;

    /**
     * 是否正确
     */
    private Boolean isCorrect;

    /**
     * 得分
     */
    private Integer score;

    /**
     * 评语(主观题使用)
     */
    private String comment;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
