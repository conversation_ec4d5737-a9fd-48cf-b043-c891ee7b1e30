package com.cy.education.controller;

import com.cy.education.repository.PkRoomMapper;
import com.cy.education.repository.PkParticipantMapper;
import com.cy.education.repository.PkAnswerMapper;
import com.cy.education.repository.PkQuestionMapper;
import com.cy.education.repository.ExamQuestionMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * PK Mapper测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/pk/mapper-test")
@Api(tags = "PK Mapper测试")
public class PkMapperTestController {
    
    @Autowired(required = false)
    private PkRoomMapper pkRoomMapper;
    
    @Autowired(required = false)
    private PkParticipantMapper pkParticipantMapper;
    
    @Autowired(required = false)
    private PkAnswerMapper pkAnswerMapper;
    
    @Autowired(required = false)
    private PkQuestionMapper pkQuestionMapper;
    
    @Autowired(required = false)
    private ExamQuestionMapper examQuestionMapper;
    
    /**
     * 测试所有Mapper是否正常注入
     */
    @GetMapping("/injection")
    @ApiOperation("测试Mapper注入")
    public Map<String, Object> testMapperInjection() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("pkRoomMapper", pkRoomMapper != null ? "注入成功" : "注入失败");
            result.put("pkParticipantMapper", pkParticipantMapper != null ? "注入成功" : "注入失败");
            result.put("pkAnswerMapper", pkAnswerMapper != null ? "注入成功" : "注入失败");
            result.put("pkQuestionMapper", pkQuestionMapper != null ? "注入成功" : "注入失败");
            result.put("examQuestionMapper", examQuestionMapper != null ? "注入成功" : "注入失败");
            
            boolean allSuccess = pkRoomMapper != null && 
                               pkParticipantMapper != null && 
                               pkAnswerMapper != null && 
                               pkQuestionMapper != null && 
                               examQuestionMapper != null;
            
            result.put("success", allSuccess);
            result.put("message", allSuccess ? "所有Mapper注入成功" : "部分Mapper注入失败");
            
            log.info("Mapper注入测试结果: {}", result);
            
        } catch (Exception e) {
            log.error("Mapper注入测试失败", e);
            result.put("success", false);
            result.put("message", "Mapper注入测试失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 测试数据库连接
     */
    @GetMapping("/database")
    @ApiOperation("测试数据库连接")
    public Map<String, Object> testDatabaseConnection() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (examQuestionMapper != null) {
                // 测试查询一个简单的表
                long count = examQuestionMapper.selectCount(null);
                result.put("examQuestionCount", count);
                result.put("databaseConnection", "正常");
            } else {
                result.put("databaseConnection", "Mapper未注入");
            }
            
            result.put("success", true);
            result.put("message", "数据库连接测试完成");
            
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            result.put("success", false);
            result.put("message", "数据库连接测试失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }
    
    /**
     * 测试PK表是否存在
     */
    @GetMapping("/tables")
    @ApiOperation("测试PK表是否存在")
    public Map<String, Object> testPkTables() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> tableStatus = new HashMap<>();
            
            if (pkRoomMapper != null) {
                try {
                    long roomCount = pkRoomMapper.selectCount(null);
                    tableStatus.put("pk_room", "存在，记录数: " + roomCount);
                } catch (Exception e) {
                    tableStatus.put("pk_room", "不存在或无权限: " + e.getMessage());
                }
            } else {
                tableStatus.put("pk_room", "Mapper未注入");
            }
            
            if (pkParticipantMapper != null) {
                try {
                    long participantCount = pkParticipantMapper.selectCount(null);
                    tableStatus.put("pk_participant", "存在，记录数: " + participantCount);
                } catch (Exception e) {
                    tableStatus.put("pk_participant", "不存在或无权限: " + e.getMessage());
                }
            } else {
                tableStatus.put("pk_participant", "Mapper未注入");
            }
            
            if (pkAnswerMapper != null) {
                try {
                    long answerCount = pkAnswerMapper.selectCount(null);
                    tableStatus.put("pk_answer", "存在，记录数: " + answerCount);
                } catch (Exception e) {
                    tableStatus.put("pk_answer", "不存在或无权限: " + e.getMessage());
                }
            } else {
                tableStatus.put("pk_answer", "Mapper未注入");
            }
            
            if (pkQuestionMapper != null) {
                try {
                    long questionCount = pkQuestionMapper.selectCount(null);
                    tableStatus.put("pk_question", "存在，记录数: " + questionCount);
                } catch (Exception e) {
                    tableStatus.put("pk_question", "不存在或无权限: " + e.getMessage());
                }
            } else {
                tableStatus.put("pk_question", "Mapper未注入");
            }
            
            result.put("tableStatus", tableStatus);
            result.put("success", true);
            result.put("message", "PK表检查完成");
            
        } catch (Exception e) {
            log.error("PK表检查失败", e);
            result.put("success", false);
            result.put("message", "PK表检查失败: " + e.getMessage());
        }
        
        return result;
    }
}
