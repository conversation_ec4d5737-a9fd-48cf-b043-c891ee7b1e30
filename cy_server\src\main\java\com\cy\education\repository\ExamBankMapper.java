package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.ExamBank;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 题库Mapper接口
 */
@Mapper
public interface ExamBankMapper extends BaseMapper<ExamBank> {

    /**
     * 获取题库中的题目数量
     *
     * @param bankId 题库ID
     * @return 题目数量
     */
    @Select("SELECT COUNT(*) FROM exam_question WHERE bank_id = #{bankId} AND deleted = 0")
    Integer getQuestionCount(@Param("bankId") Integer bankId);
} 