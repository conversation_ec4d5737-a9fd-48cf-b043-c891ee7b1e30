package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.ForumCategory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * 论坛分类Mapper接口
 */
@Repository
public interface ForumCategoryMapper extends BaseMapper<ForumCategory> {
    
    /**
     * 查询分类下的帖子数量
     * @param categoryId 分类ID
     * @return 帖子数量
     */
    @Select("SELECT COUNT(*) FROM forum_posts WHERE category_id = #{categoryId}")
    int countPostsByCategoryId(@Param("categoryId") Integer categoryId);
} 