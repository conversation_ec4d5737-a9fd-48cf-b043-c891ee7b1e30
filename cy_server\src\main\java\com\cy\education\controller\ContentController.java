package com.cy.education.controller;

import com.cy.education.model.entity.Carousel;
import com.cy.education.model.entity.News;
import com.cy.education.model.entity.Notice;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.CarouselService;
import com.cy.education.service.NewsService;
import com.cy.education.service.NoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 内容管理控制器
 */
@Api(tags = "内容管理接口")
@RestController
@RequestMapping("/content")
@Slf4j
public class ContentController {

    @Autowired
    private CarouselService carouselService;
    
    @Autowired
    private NewsService newsService;
    
    @Autowired
    private NoticeService noticeService;
    
    //=========================== 轮播图接口 ===========================
    
    /**
     * 分页查询轮播图列表
     */
    @ApiOperation("分页查询轮播图列表")
    @GetMapping("/carousel/list")
    public ApiResponse<PageResponse<Carousel>> listCarousels(ContentQueryParam param) {
        try {
            PageResponse<Carousel> pageResponse = carouselService.listCarousels(param);
            return ApiResponse.success(pageResponse);
        } catch (Exception e) {
            log.error("查询轮播图列表失败", e);
            return ApiResponse.error("查询轮播图列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取轮播图详情
     */
    @ApiOperation("获取轮播图详情")
    @GetMapping("/carousel/{id}")
    public ApiResponse<Carousel> getCarouselById(@PathVariable Integer id) {
        try {
            Carousel carousel = carouselService.getCarouselById(id);
            return ApiResponse.success(carousel);
        } catch (Exception e) {
            log.error("获取轮播图详情失败", e);
            return ApiResponse.error("获取轮播图详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增轮播图
     */
    @ApiOperation("新增轮播图")
    @PostMapping("/carousel")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Object>> addCarousel(@Validated @RequestBody Carousel carousel) {
        try {
            Integer id = carouselService.addCarousel(carousel);
            Map<String, Object> result = new HashMap<>();
            result.put("id", id);
            return ApiResponse.success(result, "添加轮播图成功");
        } catch (Exception e) {
            log.error("添加轮播图失败", e);
            return ApiResponse.error("添加轮播图失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新轮播图
     */
    @ApiOperation("更新轮播图")
    @PutMapping("/carousel/{id}")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> updateCarousel(@PathVariable Integer id, @Validated @RequestBody Carousel carousel) {
        try {
            carousel.setId(id);
            boolean success = carouselService.updateCarousel(carousel);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新轮播图成功");
        } catch (Exception e) {
            log.error("更新轮播图失败", e);
            return ApiResponse.error("更新轮播图失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除轮播图
     */
    @ApiOperation("删除轮播图")
    @DeleteMapping("/carousel/{id}")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> deleteCarousel(@PathVariable Integer id) {
        try {
            boolean success = carouselService.deleteCarousel(id);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "删除轮播图成功");
        } catch (Exception e) {
            log.error("删除轮播图失败", e);
            return ApiResponse.error("删除轮播图失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新轮播图状态
     */
    @ApiOperation("更新轮播图状态")
    @PutMapping("/carousel/{id}/status")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> updateCarouselStatus(@PathVariable Integer id, @RequestBody Map<String, Integer> param) {
        try {
            Integer status = param.get("status");
            if (status == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            boolean success = carouselService.updateCarouselStatus(id, status);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新状态成功");
        } catch (Exception e) {
            log.error("更新轮播图状态失败", e);
            return ApiResponse.error("更新状态失败: " + e.getMessage());
        }
    }
    
    //=========================== 新闻接口 ===========================
    
    /**
     * 分页查询新闻列表
     */
    @ApiOperation("分页查询新闻列表")
    @GetMapping("/news/list")
    public ApiResponse<PageResponse<News>> listNews(ContentQueryParam param) {
        try {
            PageResponse<News> pageResponse = newsService.listNews(param);
            return ApiResponse.success(pageResponse);
        } catch (Exception e) {
            log.error("查询新闻列表失败", e);
            return ApiResponse.error("查询新闻列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取新闻详情
     */
    @ApiOperation("获取新闻详情")
    @GetMapping("/news/{id}")
    public ApiResponse<News> getNewsById(@PathVariable Integer id) {
        try {
            News news = newsService.getNewsById(id);
            return ApiResponse.success(news);
        } catch (Exception e) {
            log.error("获取新闻详情失败", e);
            return ApiResponse.error("获取新闻详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增新闻
     */
    @ApiOperation("新增新闻")
    @PostMapping("/news")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Object>> addNews(@Validated @RequestBody News news) {
        try {
            Integer id = newsService.addNews(news);
            Map<String, Object> result = new HashMap<>();
            result.put("id", id);
            return ApiResponse.success(result, "添加新闻成功");
        } catch (Exception e) {
            log.error("添加新闻失败", e);
            return ApiResponse.error("添加新闻失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新新闻
     */
    @ApiOperation("更新新闻")
    @PutMapping("/news/{id}")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> updateNews(@PathVariable Integer id, @Validated @RequestBody News news) {
        try {
            news.setId(id);
            boolean success = newsService.updateNews(news);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新新闻成功");
        } catch (Exception e) {
            log.error("更新新闻失败", e);
            return ApiResponse.error("更新新闻失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除新闻
     */
    @ApiOperation("删除新闻")
    @DeleteMapping("/news/{id}")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> deleteNews(@PathVariable Integer id) {
        try {
            boolean success = newsService.deleteNews(id);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "删除新闻成功");
        } catch (Exception e) {
            log.error("删除新闻失败", e);
            return ApiResponse.error("删除新闻失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新新闻状态
     */
    @ApiOperation("更新新闻状态")
    @PutMapping("/news/{id}/status")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> updateNewsStatus(@PathVariable Integer id, @RequestBody Map<String, Integer> param) {
        try {
            Integer status = param.get("status");
            if (status == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            boolean success = newsService.updateNewsStatus(id, status);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新状态成功");
        } catch (Exception e) {
            log.error("更新新闻状态失败", e);
            return ApiResponse.error("更新状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新新闻置顶状态
     */
    @ApiOperation("更新新闻置顶状态")
    @PutMapping("/news/{id}/top")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> updateNewsTop(@PathVariable Integer id, @RequestBody Map<String, Boolean> param) {
        try {
            Boolean isTop = param.get("isTop");
            if (isTop == null) {
                return ApiResponse.validateFailed("置顶状态不能为空");
            }
            
            boolean success = newsService.updateNewsTopStatus(id, isTop);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, isTop ? "置顶成功" : "取消置顶成功");
        } catch (Exception e) {
            log.error("更新新闻置顶状态失败", e);
            return ApiResponse.error("更新置顶状态失败: " + e.getMessage());
        }
    }
    
    //=========================== 公告接口 ===========================
    
    /**
     * 分页查询公告列表
     */
    @ApiOperation("分页查询公告列表")
    @GetMapping("/notice/list")
    public ApiResponse<PageResponse<Notice>> listNotices(ContentQueryParam param) {
        try {
            PageResponse<Notice> pageResponse = noticeService.listNotices(param);
            return ApiResponse.success(pageResponse);
        } catch (Exception e) {
            log.error("查询公告列表失败", e);
            return ApiResponse.error("查询公告列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取公告详情
     */
    @ApiOperation("获取公告详情")
    @GetMapping("/notice/{id}")
    public ApiResponse<Notice> getNoticeById(@PathVariable Integer id) {
        try {
            Notice notice = noticeService.getNoticeById(id);
            return ApiResponse.success(notice);
        } catch (Exception e) {
            log.error("获取公告详情失败", e);
            return ApiResponse.error("获取公告详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增公告
     */
    @ApiOperation("新增公告")
    @PostMapping("/notice")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Object>> addNotice(@Validated @RequestBody Notice notice) {
        try {
            Integer id = noticeService.addNotice(notice);
            Map<String, Object> result = new HashMap<>();
            result.put("id", id);
            return ApiResponse.success(result, "添加公告成功");
        } catch (Exception e) {
            log.error("添加公告失败", e);
            return ApiResponse.error("添加公告失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新公告
     */
    @ApiOperation("更新公告")
    @PutMapping("/notice/{id}")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> updateNotice(@PathVariable Integer id, @Validated @RequestBody Notice notice) {
        try {
            notice.setId(id);
            boolean success = noticeService.updateNotice(notice);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新公告成功");
        } catch (Exception e) {
            log.error("更新公告失败", e);
            return ApiResponse.error("更新公告失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除公告
     */
    @ApiOperation("删除公告")
    @DeleteMapping("/notice/{id}")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> deleteNotice(@PathVariable Integer id) {
        try {
            boolean success = noticeService.deleteNotice(id);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "删除公告成功");
        } catch (Exception e) {
            log.error("删除公告失败", e);
            return ApiResponse.error("删除公告失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新公告状态
     */
    @ApiOperation("更新公告状态")
    @PutMapping("/notice/{id}/status")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> updateNoticeStatus(@PathVariable Integer id, @RequestBody Map<String, Integer> param) {
        try {
            Integer status = param.get("status");
            if (status == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            boolean success = noticeService.updateNoticeStatus(id, status);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新状态成功");
        } catch (Exception e) {
            log.error("更新公告状态失败", e);
            return ApiResponse.error("更新状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新公告置顶状态
     */
    @ApiOperation("更新公告置顶状态")
    @PutMapping("/notice/{id}/top")
    @PreAuthorize("hasAuthority('content:manage')")
    public ApiResponse<Map<String, Boolean>> updateNoticeTop(@PathVariable Integer id, @RequestBody Map<String, Boolean> param) {
        try {
            Boolean isTop = param.get("isTop");
            if (isTop == null) {
                return ApiResponse.validateFailed("置顶状态不能为空");
            }
            
            boolean success = noticeService.updateNoticeTopStatus(id, isTop);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, isTop ? "置顶成功" : "取消置顶成功");
        } catch (Exception e) {
            log.error("更新公告置顶状态失败", e);
            return ApiResponse.error("更新置顶状态失败: " + e.getMessage());
        }
    }
} 