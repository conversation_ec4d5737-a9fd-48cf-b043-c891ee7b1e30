package com.cy.education.controller;

import com.cy.education.model.vo.ApiResponse;
import com.cy.education.service.OssService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 阿里云OSS相关接口
 */
@RestController
@RequestMapping("/api/oss")
public class OssController {
    
    private static final Logger logger = LoggerFactory.getLogger(OssController.class);
    
    @Autowired
    private OssService ossService;
    
    /**
     * 获取OSS上传签名
     * 
     * @param directory 指定上传目录，为空则使用默认目录
     * @param fileType 文件类型限制，如image、video、document等，为空则不限制
     * @return 包含签名信息的Map
     */
    @GetMapping("/signature")
    public ApiResponse<Map<String, String>> getSignature(
            @RequestParam(required = false) String directory,
            @RequestParam(required = false) String fileType) {
        try {
            logger.info("获取OSS签名 - 目录: {}, 文件类型: {}", directory, fileType);
            Map<String, String> signatureInfo = ossService.getPostSignature(directory, fileType);
            logger.info("OSS签名生成成功: {}", signatureInfo);
            return ApiResponse.success(signatureInfo, "获取签名成功");
        } catch (Exception e) {
            logger.error("获取OSS签名失败", e);
            return ApiResponse.error("获取签名失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取OSS上传签名（适用于视频资源）
     */
    @GetMapping("/video/signature")
    public ApiResponse<Map<String, String>> getVideoSignature() {
        try {
            logger.info("获取视频上传OSS签名");
            Map<String, String> signatureInfo = ossService.getPostSignature("video", "video");
            logger.info("视频OSS签名生成成功: {}", signatureInfo);
            return ApiResponse.success(signatureInfo, "获取视频上传签名成功");
        } catch (Exception e) {
            logger.error("获取视频上传OSS签名失败", e);
            return ApiResponse.error("获取视频上传签名失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取OSS上传签名（适用于图片资源）
     */
    @GetMapping("/image/signature")
    public ApiResponse<Map<String, String>> getImageSignature() {
        try {
            logger.info("获取图片上传OSS签名");
            Map<String, String> signatureInfo = ossService.getPostSignature("image", "image");
            logger.info("图片OSS签名生成成功: {}", signatureInfo);
            return ApiResponse.success(signatureInfo, "获取图片上传签名成功");
        } catch (Exception e) {
            logger.error("获取图片上传OSS签名失败", e);
            return ApiResponse.error("获取图片上传签名失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取OSS上传签名（适用于文档资源）
     */
    @GetMapping("/document/signature")
    public ApiResponse<Map<String, String>> getDocumentSignature() {
        try {
            logger.info("获取文档上传OSS签名");
            Map<String, String> signatureInfo = ossService.getPostSignature("document", "application");
            logger.info("文档OSS签名生成成功: {}", signatureInfo);
            return ApiResponse.success(signatureInfo, "获取文档上传签名成功");
        } catch (Exception e) {
            logger.error("获取文档上传OSS签名失败", e);
            return ApiResponse.error("获取文档上传签名失败: " + e.getMessage());
        }
    }
} 