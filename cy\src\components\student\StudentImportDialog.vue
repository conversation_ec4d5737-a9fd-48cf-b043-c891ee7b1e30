<template>
  <el-dialog v-model="visible" title="批量导入学员" width="800px" :close-on-click-modal="false">
    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" finish-status="success" style="margin-bottom: 30px;">
      <el-step title="下载模板" description="下载并填写学员信息模板" />
      <el-step title="上传文件" description="上传填写好的Excel文件" />
      <el-step title="开始导入" description="确认信息并开始导入" />
      <el-step title="导入完成" description="查看导入结果" />
    </el-steps>

    <!-- 步骤1: 下载模板 -->
    <div v-if="currentStep === 0" class="step-content">
      <div class="step-description">
        <h3>第一步：下载学员导入模板</h3>
        <p>请先下载模板文件，按照模板格式填写学员信息</p>
        <ul>
          <li>姓名：必填，学员真实姓名</li>
          <li>用户名：必填，用于登录的用户名，不能重复</li>
          <li>手机号：必填，11位手机号码，不能重复</li>
          <li>邮箱：选填，有效的邮箱地址</li>
          <li>部门名称：选填，学员所属部门</li>
          <li>备注：选填，其他说明信息</li>
        </ul>
      </div>
      <div class="step-actions">
        <el-button type="primary" :icon="Download" @click="downloadTemplate" :loading="downloading">
          下载模板
        </el-button>
      </div>
    </div>

    <!-- 步骤2: 上传文件 -->
    <div v-if="currentStep === 1" class="step-content">
      <div class="step-description">
        <h3>第二步：上传填写好的Excel文件</h3>
        <p>请选择填写好学员信息的Excel文件进行上传</p>
      </div>
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :before-upload="beforeUpload"
          accept=".xlsx,.xls"
          :limit="1"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传xlsx/xls文件，且不超过5MB
            </div>
          </template>
        </el-upload>
      </div>
    </div>

    <!-- 步骤3: 开始导入 -->
    <div v-if="currentStep === 2" class="step-content">
      <div class="step-description">
        <h3>第三步：确认导入信息</h3>
        <p>文件上传成功，准备开始导入</p>
      </div>
      <div class="file-info" v-if="uploadFile">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="文件名">{{ uploadFile.name }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(uploadFile.size) }}</el-descriptions-item>
          <el-descriptions-item label="上传时间">{{ new Date().toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="预计记录数">{{ uploadFile.size > 0 ? '待解析' : '0' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <!-- 步骤4: 导入结果 -->
    <div v-if="currentStep === 3" class="step-content">
      <div class="step-description">
        <h3>导入完成</h3>
      </div>
      
      <!-- 导入进度 -->
      <div v-if="importing" class="import-progress">
        <el-progress :percentage="importProgress" :status="importStatus" />
        <p style="text-align: center; margin-top: 10px;">{{ importMessage }}</p>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult && !importing" class="import-result">
        <el-result
          :icon="importResult.success ? 'success' : 'warning'"
          :title="importResult.success ? '导入成功' : '导入完成（部分失败）'"
          :sub-title="importResult.message"
        >
          <template #extra>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="总记录数">{{ importResult.totalCount }}</el-descriptions-item>
              <el-descriptions-item label="成功导入">{{ importResult.successCount }}</el-descriptions-item>
              <el-descriptions-item label="失败记录">{{ importResult.failCount }}</el-descriptions-item>
              <el-descriptions-item label="导入状态">
                <el-tag :type="importResult.success ? 'success' : 'warning'">
                  {{ importResult.success ? '全部成功' : '部分失败' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
            
            <!-- 错误文件下载 -->
            <div v-if="importResult.hasErrors" style="margin-top: 20px;">
              <el-alert
                title="部分记录导入失败"
                type="warning"
                description="请下载错误文件查看失败原因，修正后重新导入"
                show-icon
                :closable="false"
              />
              <el-button 
                type="warning" 
                :icon="Download" 
                style="margin-top: 10px;"
                @click="downloadErrorFile"
              >
                下载错误文件
              </el-button>
            </div>
          </template>
        </el-result>
      </div>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ currentStep === 3 ? '关闭' : '取消' }}</el-button>
        <el-button v-if="currentStep > 0 && currentStep < 3" @click="prevStep">上一步</el-button>
        <el-button 
          v-if="currentStep < 2" 
          type="primary" 
          @click="nextStep"
          :disabled="currentStep === 1 && !uploadFile"
        >
          下一步
        </el-button>
        <el-button 
          v-if="currentStep === 2" 
          type="primary" 
          @click="startImport"
          :loading="importing"
        >
          开始导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import { downloadStudentTemplate, importStudentsV2 } from '@/api/student'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 步骤控制
const currentStep = ref(0)
const downloading = ref(false)
const uploadFile = ref<File | null>(null)
const uploadRef = ref()

// 导入相关
const importing = ref(false)
const importProgress = ref(0)
const importStatus = ref<'success' | 'exception' | undefined>(undefined)
const importMessage = ref('')
const importResult = ref<any>(null)

// 重置状态
const resetState = () => {
  currentStep.value = 0
  uploadFile.value = null
  importing.value = false
  importProgress.value = 0
  importStatus.value = undefined
  importMessage.value = ''
  importResult.value = null
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    resetState()
  }
})

// 下载模板
const downloadTemplate = async () => {
  try {
    downloading.value = true
    await downloadStudentTemplate()
    ElMessage.success('模板下载成功，请查看浏览器下载文件夹')
    // 给用户一点时间看到成功消息，然后自动进入下一步
    setTimeout(() => {
      nextStep()
    }, 1500)
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败: ' + (error.message || error))
  } finally {
    downloading.value = false
  }
}

// 文件上传前验证
const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.name.endsWith('.xlsx') ||
                  file.name.endsWith('.xls')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(.xlsx或.xls格式)!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过5MB!')
    return false
  }
  return false // 阻止自动上传
}

// 文件变化处理
const handleFileChange = (file: any) => {
  uploadFile.value = file.raw
}

// 步骤控制
const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 开始导入
const startImport = async () => {
  if (!uploadFile.value) {
    ElMessage.error('请先上传文件')
    return
  }

  try {
    importing.value = true
    importProgress.value = 0
    importMessage.value = '正在解析文件...'
    
    // 模拟进度更新
    const progressInterval = setInterval(() => {
      if (importProgress.value < 90) {
        importProgress.value += 10
        if (importProgress.value < 30) {
          importMessage.value = '正在解析文件...'
        } else if (importProgress.value < 60) {
          importMessage.value = '正在验证数据...'
        } else {
          importMessage.value = '正在导入数据...'
        }
      }
    }, 200)

    const result = await importStudentsV2(uploadFile.value)
    
    clearInterval(progressInterval)
    importProgress.value = 100
    importStatus.value = result.success ? 'success' : 'exception'
    importMessage.value = '导入完成'
    importResult.value = result
    
    nextStep()
    
    if (result.success && result.failCount === 0) {
      emit('success')
    }
    
  } catch (error) {
    console.error('导入失败:', error)
    importStatus.value = 'exception'
    importMessage.value = '导入失败'
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

// 下载错误文件
const downloadErrorFile = () => {
  if (importResult.value?.errorFileUrl) {
    window.open(importResult.value.errorFileUrl, '_blank')
  } else {
    ElMessage.error('错误文件不存在')
  }
}

// 取消/关闭
const handleCancel = () => {
  visible.value = false
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / (1024 * 1024)).toFixed(1) + ' MB'
}
</script>

<style scoped>
.step-content {
  min-height: 300px;
  padding: 20px 0;
}

.step-description {
  margin-bottom: 30px;
}

.step-description h3 {
  color: #303133;
  margin-bottom: 10px;
}

.step-description p {
  color: #606266;
  margin-bottom: 15px;
}

.step-description ul {
  color: #909399;
  padding-left: 20px;
}

.step-description li {
  margin-bottom: 5px;
}

.step-actions {
  text-align: center;
}

.upload-area {
  margin: 20px 0;
}

.file-info {
  margin: 20px 0;
}

.import-progress {
  margin: 30px 0;
}

.import-result {
  margin: 20px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
