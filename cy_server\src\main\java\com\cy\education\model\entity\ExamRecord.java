package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试记录实体类
 */
@Data
@TableName("exam_record")
public class ExamRecord {
    
    /**
     * 记录ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 考试ID
     */
    private Integer examId;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 得分
     */
    private Integer score;
    
    /**
     * 总分
     */
    private Integer totalScore;
    
    /**
     * 是否通过
     */
    private Boolean isPassed;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 实际用时(分钟)
     */
    private Integer duration;
    
    /**
     * 状态(0-未开始,1-进行中,2-已完成,3-超时)
     */
    private Integer status;
    
    /**
     * 考试次数
     */
    private Integer attemptNumber;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 