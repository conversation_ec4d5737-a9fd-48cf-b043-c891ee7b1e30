import { get, post, put, del } from '@/utils/request'

/**
 * 学员信息接口数据类型
 */
export interface Student {
  id: number
  name: string
  phone: string
  departmentId: number
  department: string
  avatar: string
  status: number // 0: 禁用, 1: 正常
  registerTime: string
  lastLoginTime: string
  points: number
  entryTime?: string
  remark?: string
}

/**
 * 学员查询参数
 */
export interface StudentQueryParams {
  keyword?: string
  departmentId?: number
  status?: string | number
  page?: number
  size?: number
}

/**
 * 获取学员列表
 * @param params 查询参数
 */
export function getStudentList(params: StudentQueryParams) {
  return get<{
    list: Student[]
    total: number
  }>('/student/list', params)
}

/**
 * 获取学员详情
 * @param id 学员ID
 */
export function getStudentById(id: number) {
  return get<Student>(`/student/${id}`)
}

/**
 * 添加学员
 * @param data 学员信息
 */
export function addStudent(data: Partial<Student> & { password: string }) {
  return post<{ id: number }>('/student/add', data)
}

/**
 * 更新学员信息
 * @param data 学员信息
 */
export function updateStudent(data: Partial<Student>) {
  return put<{ success: boolean }>('/student/update', data)
}

/**
 * 删除学员
 * @param id 学员ID
 */
export function deleteStudent(id: number) {
  return del<{ success: boolean }>(`/student/${id}`)
}

/**
 * 重置学员密码
 * @param id 学员ID
 */
export function resetStudentPassword(id: number) {
  return post<{ password: string }>(`/student/${id}/reset-password`)
}

/**
 * 更新学员状态
 * @param id 学员ID
 * @param status 状态 0: 禁用, 1: 正常
 */
export function updateStudentStatus(id: number, status: number) {
  return put<{ success: boolean }>(`/student/${id}/status`, { status })
}

/**
 * 导出学员列表
 * @param params 查询参数
 */
export function exportStudentList(params: Partial<StudentQueryParams>) {
  return get<{ url: string }>('/student/export', params)
}

/**
 * 批量导入学员
 * @param file 导入文件
 */
export function importStudents(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return post<{ success: boolean, count: number }>('/student/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 新版导入导出功能 ====================

/**
 * 下载学员导入模板
 */
export function downloadStudentTemplate() {
  return new Promise<void>((resolve, reject) => {
    // 使用fetch获取文件，确保正确处理Excel文件
    fetch('/api/student/import/template', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response.blob()
    })
    .then(blob => {
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '学员导入模板.xlsx'
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      window.URL.revokeObjectURL(url)
      resolve()
    })
    .catch(error => {
      console.error('下载模板失败:', error)
      reject(error)
    })
  })
}

/**
 * 批量导入学员（新版本）
 */
export function importStudentsV2(file: File) {
  const formData = new FormData()
  formData.append('file', file)

  return post<{
    success: boolean
    totalCount: number
    successCount: number
    failCount: number
    message: string
    hasErrors?: boolean
    errorFileUrl?: string
  }>('/student/import/v2', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    showLoading: true
  })
}

/**
 * 导出学员列表（新版本）
 */
export function exportStudentsV2(params: any) {
  return post<Blob>('/student/export/v2', params, {
    responseType: 'blob',
    showLoading: true
  }).then(response => {
    console.log('导出响应:', response)

    // 检查响应是否为Blob
    if (!(response instanceof Blob)) {
      console.error('响应不是Blob类型:', response)
      throw new Error('导出失败：响应格式错误')
    }

    // 检查Blob大小
    if (response.size === 0) {
      console.error('导出文件为空')
      throw new Error('导出失败：文件为空')
    }

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '_')
    const format = params.format || 'xlsx'
    link.download = `学员列表_${timestamp}.${format}`
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    console.log('文件下载完成')
  }).catch(error => {
    console.error('导出失败:', error)
    throw error
  })
}