<template>
  <div class="question-bank">
    <div class="page-container">
      <div class="page-toolbar">
        <div class="toolbar-left">
          <h3>题库管理</h3>
        </div>
        <div class="toolbar-right">
          <el-button type="primary" :icon="Plus" @click="handleAddQuestion">新增题目</el-button>
          <el-button :icon="Upload" @click="handleBatchImport">批量导入</el-button>
          <el-button :icon="Download" @click="handleExport">导出题目</el-button>
          <el-button :icon="Refresh" @click="refreshQuestionList">刷新</el-button>
        </div>
      </div>

      <div class="bank-content">
        <!-- 左侧题库分类 -->
        <bank-tree
          :bank-list="bankList"
          :tree-props="treeProps"
          @add-bank="handleAddBank"
          @edit-bank="handleEditBank"
          @delete-bank="handleDeleteBank"
          @select-bank="handleBankSelect"
        />

        <!-- 右侧题目列表 -->
        <div class="bank-main">
          <question-table
            :question-list="questionList"
            :question-types="questionTypes"
            :loading="loading"
            :total="total"
            @search="handleQuery"
            @reset="resetQuery"
            @selection-change="handleSelectionChange"
            @preview-question="handlePreview"
            @edit-question="handleEditQuestion"
            @delete-question="handleDeleteQuestion"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 题库表单弹窗 -->
    <bank-form-dialog
      v-model="bankDialogVisible"
      :is-edit="isEdit"
      :bank-data="bankForm"
      @submit="submitBankForm"
    />

    <!-- 题目预览弹窗 -->
    <question-preview-dialog
      v-model="previewDialogVisible"
      :question="previewQuestion"
      :question-types="questionTypes"
    />

    <!-- 题目编辑弹窗 -->
    <question-edit-dialog
      v-model="questionEditDialogVisible"
      :is-edit="isQuestionEdit"
      :question-data="editQuestionData"
      :bank-list="bankList.filter(b => b.id !== '0')"
      :question-types="questionTypes"
      @submit="submitQuestionForm"
    />

    <!-- 批量导入弹窗 -->
    <import-dialog
      v-model="importDialogVisible"
      :bank-list="bankList"
      :current-bank-id="queryParams.bankId"
      @download-template="downloadTemplate"
      @submit="submitImport"
    />

    <!-- 新的导入对话框 -->
    <ImportExportDialog
      v-model="newImportDialogVisible"
      title="导入题目"
      template-name="题目"
      template-url="/api/exam/question/import/template"
      upload-action="/api/exam/question/import"
      import-rules="请确保题目格式正确，选择题必须有选项，判断题答案为true/false"
      @success="handleImportSuccess"
    />

    <!-- 导出对话框 -->
    <ExportDialog
      v-model="exportDialogVisible"
      title="导出题目"
      export-url="/api/exam/question/export"
      :support-csv="true"
      :support-range="true"
      :support-time-range="true"
      :selected-count="selectedQuestions.length"
      :fields="[
        { key: 'title', label: '题目标题', required: true },
        { key: 'type', label: '题目类型', required: true },
        { key: 'bankName', label: '所属题库' },
        { key: 'difficulty', label: '难度' },
        { key: 'score', label: '分值' },
        { key: 'options', label: '选项' },
        { key: 'answer', label: '答案' },
        { key: 'explanation', label: '解析' },
        { key: 'createdAt', label: '创建时间' }
      ]"
      :default-params="{
        bankId: queryParams.bankId,
        type: queryParams.type,
        keyword: queryParams.keyword,
        selectedIds: selectedQuestions.map(q => q.id)
      }"
      description="可选择导出全部题目、当前页面题目或已选择的题目"
      @success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import {
  Plus,
  Edit,
  Delete,
  Refresh,
  Upload,
  Download,
  View,
  DocumentCopy
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getQuestionList, createQuestion, updateQuestion, deleteQuestion, getBankList, createBank, updateBank, deleteBank, type Question, type Bank } from '@/api/exam'
import BankTree from './components/BankTree.vue'
import QuestionTable from './components/QuestionTable.vue'
import BankFormDialog from './components/BankFormDialog.vue'
import QuestionPreviewDialog from './components/QuestionPreviewDialog.vue'
import QuestionEditDialog from './components/QuestionEditDialog.vue'
import ImportDialog from './components/ImportDialog.vue'
import ImportExportDialog from '@/components/ImportExport/ImportDialog.vue'
import ExportDialog from '@/components/ImportExport/ExportDialog.vue'

// 题库数据类型
interface BankData {
  id: number | string
  name: string
  description: string
  scope: string
  questionCount: number
  createTime: string
}

// 题目数据类型
interface QuestionData {
  id: number
  bankId: number
  type: string
  content: string
  options?: OptionData[]
  answer: string
  analysis?: string
  difficulty: number
  createTime: string
}

// 选项数据类型
interface OptionData {
  label: string
  content: string
}

// 树形配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 题目类型选项
const questionTypes = [
  { label: '单选题', value: 'single' },
  { label: '多选题', value: 'multiple' },
  { label: '判断题', value: 'judgment' },
  { label: '填空题', value: 'fill' },
  { label: '简答题', value: 'essay' }
]

// 难度选项
const difficultyOptions = [
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' }
]

// 数据定义
const loading = ref(false)
const questionList = ref<Question[]>([])
const bankList = ref<Bank[]>([])

const total = ref(0)
const queryParams = reactive({
  page: 1,
  limit: 10,
  keyword: '',
  bankId: '',
  type: ''
})

// 存储所有题目的列表，用于统计数量
const allQuestionsList = ref<Question[]>([])

// 选中的题目列表
const selectedQuestions = ref<Question[]>([])

// 获取题库列表
const fetchBankList = async () => {
  try {
    const res = await getBankList({
      page: 1,
      limit: 50, // 获取所有题库
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })
    
    // 添加"全部题目"选项
    bankList.value = [
      {
        id: '0',
        name: '全部题目',
        description: '查看所有题目',
        scope: '全体员工',
        questionCount: 0,
        createTime: '2023-01-01 10:00:00',
        createdAt: '2023-01-01T10:00:00Z',
        updatedAt: '2023-01-01T10:00:00Z',
        createdBy: '系统'
      },
      ...res.list
    ]
  } catch (error) {
    console.error('获取题库列表失败:', error)
    ElMessage.error('获取题库列表失败')
  }
}

// 获取所有题目数据，用于统计
const fetchAllQuestions = async () => {
  try {
    const res = await getQuestionList({
      page: 1,
      limit: 1000, // 设置较大的值以获取所有题目
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
    
    allQuestionsList.value = res.list;
    console.log('获取全部题目成功:', allQuestionsList.value.length);
    
    // 获取到所有题目后更新统计
    updateBankQuestionCount();
  } catch (error) {
    console.error('获取所有题目失败:', error);
  }
};

// 获取题目列表
const fetchQuestionList = async () => {
  loading.value = true
  try {
    const params = {
      page: queryParams.page,
      limit: queryParams.limit,
      keyword: queryParams.keyword || undefined,
      bankId: queryParams.bankId || undefined,
      type: queryParams.type || undefined,
      sortBy: 'createdAt',
      sortOrder: 'desc' as 'desc' | 'asc'
    }
    
    const res = await getQuestionList(params)
    // 直接使用返回的数据，添加组件需要的字段
    questionList.value = res.list.map(question => ({
      ...question,
      // 添加组件期望的字段
      content: question.title,
      createTime: formatDateTime(question.createdAt)
    }))
    total.value = res.total
  } catch (error) {
    console.error('获取题目列表失败:', error)
    ElMessage.error('获取题目列表失败')
  } finally {
    loading.value = false
  }
}

// 将难度字符串转换为数字
const getDifficultyLevel = (difficulty: string): string => {
  const difficultyMap: { [key: string]: string } = {
    'easy': 'easy',
    'medium': 'medium', 
    'hard': 'hard'
  }
  return difficultyMap[difficulty] || 'easy'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 更新题库题目数量统计
const updateBankQuestionCount = () => {
  // 确保有题目数据
  if (allQuestionsList.value.length === 0) {
    console.warn('没有题目数据用于统计');
    return;
  }

  const totalQuestions = allQuestionsList.value.length;
  console.log(`更新题目统计: 总数 ${totalQuestions}`);
  
  bankList.value.forEach(bank => {
    if (bank.id === '0') {
      // 全部题目
      bank.questionCount = totalQuestions;
    } else {
      // 统计该题库下的题目数量，使用allQuestionsList而不是当前filtered的questionList
      // 确保ID类型匹配 - 将bank.id转为字符串进行比较
      const bankIdStr = String(bank.id);
      const count = allQuestionsList.value.filter(q => String(q.bankId) === bankIdStr).length;
      bank.questionCount = count;
      console.log(`题库 ${bank.name} (ID=${bank.id}, 字符串ID=${bankIdStr}) 数量: ${count}`);
    }
  });
}

// 查询处理
const handleQuery = (params: any) => {
  Object.assign(queryParams, params)
  queryParams.page = 1
  fetchQuestionList()
}

// 重置查询
const resetQuery = () => {
  Object.assign(queryParams, {
    page: 1,
    limit: 10,
    keyword: '',
    bankId: '',
    type: ''
  })
  fetchQuestionList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  queryParams.limit = size
  queryParams.page = 1
  fetchQuestionList()
}

const handleCurrentChange = (page: number) => {
  queryParams.page = page
  fetchQuestionList()
}

// 选择题库
const handleBankSelect = (bank: Bank) => {
  console.log('选择题库:', bank.name, bank);
  
  // 如果选择"全部题目"，清空筛选条件
  if (bank.id === '0' || bank.name === '全部题目') {
    queryParams.bankId = '';
    console.log('显示全部题目');
  } else {
    // 直接使用题库ID进行筛选
    queryParams.bankId = bank.id;
    console.log('筛选条件:', queryParams.bankId);
  }
  
  queryParams.page = 1;
  fetchQuestionList();
}

// 刷新列表
const refreshQuestionList = () => {
  fetchQuestionList()
}

// 页面加载时获取数据
onMounted(async () => {
  await fetchBankList();
  await fetchAllQuestions();
  fetchQuestionList();
})

// 题库表单
const bankDialogVisible = ref(false)
const isEdit = ref(false)
const bankForm = reactive<Partial<BankData>>({
  id: undefined,
  name: '',
  description: '',
  scope: ''
})

// 题目预览弹窗
const previewDialogVisible = ref(false)
const previewQuestion = ref<Question | null>(null)

// 题目编辑弹窗
const questionEditDialogVisible = ref(false)
const isQuestionEdit = ref(false)
const editQuestionData = ref<Partial<QuestionData>>({})

// 导入弹窗
const importDialogVisible = ref(false)

// 新的导入导出弹窗
const newImportDialogVisible = ref(false)
const exportDialogVisible = ref(false)

// 添加题库
const handleAddBank = () => {
  isEdit.value = false
  Object.assign(bankForm, {
    id: undefined,
    name: '',
    description: '',
    scope: ''
  })
  bankDialogVisible.value = true
}

// 编辑题库
const handleEditBank = (data: BankData) => {
  isEdit.value = true
  Object.assign(bankForm, {
    id: data.id,
    name: data.name,
    description: data.description,
    scope: data.scope
  })
  bankDialogVisible.value = true
}

// 删除题库
const handleDeleteBank = (data: BankData) => {
  // 检查题库下是否有题目
  const hasQuestions = questionList.value.some(q => q.bankId === data.id.toString())
  
  if (hasQuestions) {
    ElMessage.warning('该题库下有题目，不能直接删除')
    return
  }

  ElMessageBox.confirm(
    `确定要删除题库"${data.name}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = bankList.value.findIndex(item => item.id === data.id)
    if (index !== -1) {
      bankList.value.splice(index, 1)
      
      if (queryParams.bankId === data.id.toString()) {
        queryParams.bankId = ''
        fetchQuestionList()
      }
      
      ElMessage.success('删除成功')
    }
  })
}

// 提交题库表单
const submitBankForm = (formData: BankData) => {
  if (isEdit.value) {
    const index = bankList.value.findIndex(item => item.id === formData.id)
    if (index !== -1) {
      bankList.value[index] = { ...formData }
      
      if (queryParams.bankId === formData.id.toString()) {
        queryParams.bankId = ''
        fetchQuestionList()
      }
      
      ElMessage.success('编辑成功')
    }
  } else {
    const newBank: BankData = {
      ...formData,
      id: Date.now(),
      questionCount: 0,
      createTime: new Date().toLocaleString()
    }
    bankList.value.push(newBank)
    ElMessage.success('添加成功')
  }
  
  bankDialogVisible.value = false
}

// 添加题目
const handleAddQuestion = () => {
  isQuestionEdit.value = false
  editQuestionData.value = {
    bankId: queryParams.bankId || '',
    type: 'single',
    title: '',
    correctAnswer: 'A',
    explanation: ''
  }
  questionEditDialogVisible.value = true
}

// 编辑题目
const handleEditQuestion = (row: Question) => {
  isQuestionEdit.value = true
  editQuestionData.value = { ...row }
  questionEditDialogVisible.value = true
}

// 删除题目
const handleDeleteQuestion = (row: Question) => {
  ElMessageBox.confirm(
    `确定要删除题目"${row.title}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteQuestion(row.id);
      ElMessage.success('删除成功');
      // 删除后重新获取所有题目数据以更新统计
      await fetchAllQuestions();
      // 然后重新加载当前过滤的题目列表
      fetchQuestionList();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  });
}

// 预览题目
const handlePreview = (row: Question) => {
  previewQuestion.value = row
  previewDialogVisible.value = true
}

// 批量导入
const handleBatchImport = () => {
  newImportDialogVisible.value = true
}

// 导出题目
const handleExport = () => {
  exportDialogVisible.value = true
}

// 导入成功回调
const handleImportSuccess = () => {
  refreshQuestionList()
  ElMessage.success('导入完成')
}

// 导出成功回调
const handleExportSuccess = () => {
  ElMessage.success('导出完成')
}

// 下载模板
const downloadTemplate = () => {
  ElMessage.success('模板下载成功')
}

// 提交导入
const submitImport = (formData: any) => {
  // 模拟导入
  setTimeout(() => {
    // 更新题库的题目数量
    const bank = bankList.value.find(item => item.id === formData.bankId)
    if (bank) {
      bank.questionCount += 10
    }
    
    ElMessage.success('成功导入10道题目')
    refreshQuestionList()
  }, 1000)
}

// 表格选择变更
const handleSelectionChange = (selection: Question[]) => {
  // 实现选择变更逻辑
  console.log('选择变更:', selection)
}

// 提交题目表单
const submitQuestionForm = async (formData: any) => {
  try {
    console.log('提交题目表单:', formData);
    
    // 确保表单数据格式正确
    const submitData = {
      ...formData,
      // 确保bankId是字符串类型
      bankId: formData.bankId?.toString()
    };

    if (isQuestionEdit.value) {
      // 更新题目
      await updateQuestion(editQuestionData.value.id!.toString(), submitData);
      ElMessage.success('题目更新成功');
    } else {
      // 创建题目
      await createQuestion(submitData);
      ElMessage.success('题目创建成功');
    }
    
    questionEditDialogVisible.value = false;
    // 题目增删改后，需要重新获取所有题目以更新统计
    await fetchAllQuestions();
    fetchQuestionList();
  } catch (error) {
    console.error('提交题目失败:', error);
    ElMessage.error('操作失败，请检查数据格式');
  }
}
</script>

<style scoped>
.bank-content {
  display: flex;
  gap: 24px;
  margin-top: 20px;
}

.bank-main {
  flex: 1;
}
</style> 