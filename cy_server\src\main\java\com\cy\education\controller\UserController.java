package com.cy.education.controller;

import com.cy.education.model.params.ChangePasswordParams;
import com.cy.education.model.params.LoginParams;
import com.cy.education.model.params.UpdateUserInfoParams;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.LoginResponseVO;
import com.cy.education.model.vo.UploadResponseVO;
import com.cy.education.service.AuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户认证控制器
 */
@Api(tags = "用户认证接口")
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private AuthService authService;

    /**
     * 管理员登录
     */
    @ApiOperation("管理员登录")
    @PostMapping("/login")
    public ApiResponse<LoginResponseVO> login(@Validated @RequestBody LoginParams loginParams) {
        LoginResponseVO loginResponse = authService.login(loginParams);
        return ApiResponse.success(loginResponse);
    }

    /**
     * 管理员登出
     */
    @ApiOperation("管理员登出")
    @PostMapping("/logout")
    public ApiResponse<Boolean> logout() {
        boolean result = authService.logout();
        return ApiResponse.success(result);
    }

    /**
     * 获取当前用户信息
     */
    @ApiOperation("获取当前用户信息")
    @GetMapping("/info")
    public ApiResponse<LoginResponseVO.UserInfoVO> getUserInfo() {
        LoginResponseVO.UserInfoVO userInfo = authService.getCurrentUserInfo();
        return ApiResponse.success(userInfo);
    }

    /**
     * 更新用户信息
     */
    @ApiOperation("更新用户信息")
    @PostMapping("/update")
    public ApiResponse<Boolean> updateUserInfo(@Validated @RequestBody UpdateUserInfoParams params) {
        boolean result = authService.updateUserInfo(params);
        return ApiResponse.success(result);
    }

    /**
     * 修改密码
     */
    @ApiOperation("修改密码")
    @PostMapping("/change-password")
    public ApiResponse<Boolean> changePassword(@Validated @RequestBody ChangePasswordParams params) {
        boolean result = authService.changePassword(params);
        return ApiResponse.success(result);
    }

    /**
     * 上传头像
     */
    @ApiOperation("上传头像")
    @PostMapping("/upload-avatar")
    public ApiResponse<UploadResponseVO> uploadAvatar(@RequestParam("avatar") MultipartFile file) {
        String avatarUrl = authService.uploadAvatar(file);
        UploadResponseVO response = new UploadResponseVO(avatarUrl, file.getOriginalFilename(), file.getSize());
        return ApiResponse.success(response);
    }
}
