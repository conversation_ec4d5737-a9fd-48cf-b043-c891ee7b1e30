<template>
  <div class="carousel-table">
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="carouselList"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="预览图" width="120" align="center">
        <template #default="{ row }">
          <el-image
            style="width: 80px; height: 45px"
            :src="row.imageUrl"
            :preview-src-list="[row.imageUrl]"
            fit="cover"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" min-width="180" show-overflow-tooltip />
      <el-table-column prop="link" label="跳转链接" min-width="180" show-overflow-tooltip />
      <el-table-column prop="sort" label="排序" width="80" align="center" />
      <el-table-column label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status ? 'success' : 'info'">
            {{ row.status ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link :icon="Edit" @click="$emit('edit', row)">
            编辑
          </el-button>
          <el-button type="primary" link :icon="View" @click="$emit('preview', row)">
            预览
          </el-button>
          <el-button
            type="primary" 
            link 
            :icon="row.status ? Hide : View" 
            @click="$emit('toggle-status', row)"
          >
            {{ row.status ? '禁用' : '启用' }}
          </el-button>
          <el-button type="danger" link :icon="Delete" @click="$emit('delete', row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="$emit('size-change', $event)"
        @current-change="$emit('current-change', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus, Edit, Delete, View, Refresh, Hide, Picture } from '@element-plus/icons-vue'

interface CarouselItem {
  id: number
  title: string
  imageUrl: string
  link: string
  sort: number
  status: number
  createTime: string
}

defineProps({
  carouselList: {
    type: Array as () => CarouselItem[],
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits([
  'add', 
  'edit', 
  'delete', 
  'preview', 
  'toggle-status',
  'selection-change',
  'refresh',
  'size-change',
  'current-change'
])

const currentPage = ref(1)
const pageSize = ref(10)
const tableRef = ref()

const handleSelectionChange = (selection: CarouselItem[]) => {
  emit('selection-change', selection)
}
</script>

<style scoped>
.carousel-table {
  margin-top: 20px;
}

.table-toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style> 