import { get, post, put, del } from '@/utils/request'

/**
 * 积分记录接口数据类型
 */
export interface PointsRecord {
  id: string
  userId: string
  userName: string
  points: number // 积分变动数量，正数表示增加，负数表示减少
  balance: number // 积分余额
  type: 'course_complete' | 'sign_in' | 'exchange' | 'admin_adjust' | 'other'
  description: string
  relatedId?: string
  relatedType?: 'course' | 'product' | 'other'
  operator?: string
  createdAt: string
}

export interface PointsStatistics {
  totalUsers: number
  totalPoints: number
  averagePoints: number
  monthlyPointsChange: {
    month: string
    earned: number
    consumed: number
  }[]
  topUsers: {
    userId: string
    userName: string
    points: number
  }[]
  pointsDistribution: {
    range: string
    count: number
  }[]
}

export interface PointsAdjustment {
  userId: string
  points: number
  description: string
  operator: string
}

export interface PointsQueryParams {
  page?: number
  limit?: number
  userId?: string
  userName?: string
  type?: string
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

/**
 * 积分规则接口数据类型
 */
export interface PointsRule {
  id: string
  category: 'login' | 'learning' | 'forum' | 'exam' | 'other'
  name: string
  code: string
  description: string
  points: number
  dailyLimit: number // -1表示无限制
  timesLimit: number // -1表示无限制
  usedCount: number
  status: 0 | 1 // 0禁用，1启用
  createTime: string
  updateTime?: string
}

export interface PointsRuleQueryParams {
  page?: number
  limit?: number
  category?: string
  status?: 0 | 1
  keyword?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PointsRuleStatistics {
  totalRules: number
  enabledRules: number
  totalUsed: number
  positivePoints: number
  negativePoints: number
  categoryDistribution: {
    category: string
    count: number
  }[]
  usageRanking: {
    id: string
    name: string
    category: string
    usedCount: number
    points: number
  }[]
}

/**
 * 获取积分记录列表
 * @param params 查询参数
 */
export function getPointsRecordList(params?: PointsQueryParams) {
  return get<{ list: PointsRecord[]; total: number }>('/points/records', params)
}

/**
 * 获取用户积分余额
 * @param userId 用户ID
 */
export function getUserPointsBalance(userId: string) {
  return get<{ userId: string; balance: number }>(`/points/balance/${userId}`)
}

/**
 * 积分调整
 * @param data 积分调整数据
 */
export function adjustUserPoints(data: PointsAdjustment) {
  return post<{ success: boolean; newBalance: number }>('/points/adjust', data)
}

/**
 * 批量调整积分
 * @param data 批量积分调整数据
 */
export function batchAdjustPoints(data: PointsAdjustment[]) {
  return post<{ success: boolean; failedCount: number }>('/points/batch-adjust', data)
}

/**
 * 获取积分统计数据
 */
export function getPointsStatistics() {
  return get<PointsStatistics>('/points/statistics')
}

/**
 * 获取用户积分趋势
 * @param userId 用户ID
 * @param period 时间段：week, month, year, all
 */
export function getUserPointsTrend(userId: string, period: 'week' | 'month' | 'year' | 'all') {
  return get<{
    userId: string
    userName: string
    data: { date: string; points: number; balance: number }[]
  }>(`/points/trend/${userId}`, { period })
}

/**
 * 导出积分记录
 * @param params 查询参数
 */
export function exportPointsRecords(params?: PointsQueryParams) {
  return get<Blob>('/points/export', params, {
    responseType: 'blob',
    showLoading: true
  })
}

/**
 * ========== 积分规则相关接口 ==========
 */

/**
 * 获取积分规则列表
 * @param params 查询参数
 */
export function getPointsRuleList(params?: PointsRuleQueryParams) {
  return get<{ list: PointsRule[]; total: number }>('/points/rules', params)
}

/**
 * 获取积分规则详情
 * @param ruleId 规则ID
 */
export function getPointsRuleDetail(ruleId: string) {
  return get<PointsRule>(`/points/rules/${ruleId}`)
}

/**
 * 创建积分规则
 * @param data 规则数据
 */
export function createPointsRule(data: Omit<PointsRule, 'id' | 'usedCount' | 'createTime' | 'updateTime'>) {
  return post<{ id: string; success: boolean }>('/points/rules', data)
}

/**
 * 更新积分规则
 * @param ruleId 规则ID
 * @param data 规则数据
 */
export function updatePointsRule(ruleId: string, data: Partial<PointsRule>) {
  return put<{ success: boolean }>(`/points/rules/${ruleId}`, data)
}

/**
 * 删除积分规则
 * @param ruleId 规则ID
 */
export function deletePointsRule(ruleId: string) {
  return del<{ success: boolean }>(`/points/rules/${ruleId}`)
}

/**
 * 批量删除积分规则
 * @param ruleIds 规则ID数组
 */
export function batchDeletePointsRules(ruleIds: string[]) {
  return post<{ success: boolean; failedCount: number }>('/points/rules/batch-delete', { ruleIds })
}

/**
 * 切换积分规则状态
 * @param ruleId 规则ID
 * @param status 状态
 */
export function togglePointsRuleStatus(ruleId: string, status: 0 | 1) {
  return put<{ success: boolean }>(`/points/rules/${ruleId}/status`, { status })
}

/**
 * 获取积分规则统计数据
 */
export function getPointsRuleStatistics() {
  return get<PointsRuleStatistics>('/points/rules/statistics')
}

/**
 * 导出积分规则
 * @param params 查询参数
 */
export function exportPointsRules(params?: PointsRuleQueryParams) {
  return get<Blob>('/points/rules/export', params, {
    responseType: 'blob',
    showLoading: true
  })
}
