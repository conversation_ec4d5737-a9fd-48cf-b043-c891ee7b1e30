<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="navbar-left">
          <view class="back-btn" @click="goBack">
            <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
          </view>
          <text class="navbar-title">帖子详情</text>
        </view>
        <view class="navbar-right">
          <view class="back-btn" @click="sharePost">
            <up-icon color="#fff" name="share" size="12"></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-area" scroll-y>
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <up-loading-icon color="#667eea" size="24"></up-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 帖子内容卡片 -->
      <view v-else class="post-card">
        <!-- 作者信息 -->
        <view class="author-section">
          <view class="author-info" @click="viewProfile(postDetail.author.id)">
            <up-image
                :src="postDetail.author.avatar"
                width="48"
                height="48"
                shape="circle"
                class="author-avatar"
            ></up-image>
            <view class="author-details">
              <text class="author-name">{{ postDetail.author }}</text>
              <text class="post-time">{{ formatTime(postDetail.createdAt) }}</text>
            </view>
          </view>
          <view class="follow-btn" :class="{ following: postDetail.isFollowing }" @click="toggleFollow">
            <up-icon
                :color="postDetail.isFollowing ? '#43e97b' : '#667eea'"
                :name="postDetail.isFollowing ? 'checkmark' : 'plus'"
                size="14"
            ></up-icon>
            <text class="follow-text">{{ postDetail.isFollowing ? '已关注' : '关注' }}</text>
          </view>
        </view>

        <!-- 帖子主体内容 -->
        <view class="post-main">
          <text class="post-title">{{ postDetail.title }}</text>
          <view class="post-tags">
            <view class="category-tag">
              <text class="tag-text">{{ postDetail.category }}</text>
            </view>
          </view>
          <view class="post-content">
            <rich-text :nodes="postDetail.content"/>
          </view>

          <!-- 图片展示区域 -->
          <view v-if="postImages.length > 0" :class="getImagesContainerClass()" class="post-images">
            <view
                v-for="(image, index) in postImages"
                :key="index"
                class="image-item"
                @click="previewImage(index)"
            >
              <up-image
                  :fade="true"
                  :lazy-load="true"
                  :src="image"
                  class="post-image"
                  height="100%"
                  mode="aspectFill"
                  width="100%"
              ></up-image>
            </view>
          </view>
        </view>

        <!-- 互动统计 -->
        <view class="post-stats">
          <view class="stat-item">
            <up-icon name="eye" color="#8e8e93" size="16"></up-icon>
            <text class="stat-text">{{ postDetail.viewCount || 0 }}</text>
          </view>
          <view class="stat-item">
            <up-icon color="#8e8e93" name="thumb-up" size="16"></up-icon>
            <text class="stat-text">{{ postDetail.likeCount || 0 }}</text>
          </view>
          <view class="stat-item">
            <up-icon color="#8e8e93" name="star" size="16"></up-icon>
            <text class="stat-text">{{ postDetail.collectCount || 0 }}</text>
          </view>
          <view class="stat-item">
            <up-icon color="#8e8e93" name="chat" size="16"></up-icon>
            <text class="stat-text">{{ postDetail.replyCount || 0 }}</text>
          </view>
          <view class="stat-item">
            <up-icon name="share" color="#8e8e93" size="16"></up-icon>
            <text class="stat-text">{{ postDetail.shareCount || 0 }}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="post-actions">
          <view class="action-btn" :class="{ active: postDetail.isLiked }" @click="toggleLike">
            <up-icon
                :color="postDetail.isLiked ? '#fa709a' : '#8e8e93'"
                :name="postDetail.isLiked ? 'thumb-up-fill' : 'thumb-up'"
                size="20"
            ></up-icon>
            <text class="action-text">{{ postDetail.isLiked ? '已赞' : '点赞' }}</text>
          </view>
          <view class="action-btn" :class="{ active: postDetail.isCollected }" @click="toggleCollect">
            <up-icon
                :color="postDetail.isCollected ? '#fa8c16' : '#8e8e93'"
                :name="postDetail.isCollected ? 'star-fill' : 'star'"
                size="20"
            ></up-icon>
            <text class="action-text">{{ postDetail.isCollected ? '已收藏' : '收藏' }}</text>
          </view>
          <view class="action-btn" @click="focusCommentInput">
            <up-icon color="#8e8e93" name="chat" size="20"></up-icon>
            <text class="action-text">评论</text>
          </view>
          <view class="action-btn" @click="sharePost">
            <up-icon name="share" color="#8e8e93" size="20"></up-icon>
            <text class="action-text">分享</text>
          </view>
        </view>
      </view>

      <!-- 评论区域 -->
      <view class="comments-section">
        <view class="comments-header">
          <view class="header-left">
            <view class="comments-icon">
              <up-icon name="chat" color="#667eea" size="20"></up-icon>
            </view>
            <text class="comments-title">评论</text>
          </view>
          <view class="sort-options">
            <view
                :class="{ active: sortType === 'time' }"
                class="sort-item"
                @click="setSortType('time')"
            >
              <text class="sort-text">最新</text>
            </view>
            <view
                :class="{ active: sortType === 'hot' }"
                class="sort-item"
                @click="setSortType('hot')"
            >
              <text class="sort-text">最热</text>
            </view>
          </view>
        </view>

        <view class="comment-list">
          <!-- 空状态 -->
          <up-empty
              v-if="commentList.length === 0 && !loadingComments"
              mode="list"
              text="空空如也"
              textColor="#909399"
              textSize="14"
          >
            <template #bottom>
              <up-button size="small" text="发表第一个评论" type="primary" @click="showCommentInput"></up-button>
            </template>
          </up-empty>

          <view v-for="comment in commentList" :key="comment.id" class="comment-item">
            <view class="comment-avatar" @click="viewProfile(comment.authorId)">
              <up-image
                  :src="comment.authorAvatar"
                  class="avatar-image"
                  height="36"
                  shape="circle"
                  width="36"
              ></up-image>
            </view>
            <view class="comment-content">
              <view class="comment-header">
                <text class="comment-author">{{ comment.author }}</text>
                <text class="comment-time">{{ formatTime(comment.createdAt) }}</text>
              </view>
              <text class="comment-text">{{ comment.content }}</text>

              <!-- 回复列表 -->
              <view v-if="comment.children && comment.children.length > 0" class="reply-list">
                <view v-for="reply in comment.children" :key="reply.id" class="reply-item">
                  <text class="reply-author">{{ reply.author }}</text>
                  <text class="reply-separator">：</text>
                  <text class="reply-content">{{ reply.content }}</text>
                </view>
              </view>

              <view class="comment-actions">
                <view class="comment-action" :class="{ active: comment.isLiked }" @click="toggleCommentLike(comment)">
                  <up-icon
                      :color="comment.isLiked ? '#fa709a' : '#8e8e93'"
                      :name="comment.isLiked ? 'thumb-up-fill' : 'thumb-up'"
                      size="14"
                  ></up-icon>
                  <text class="action-count">{{ comment.likeCount || 0 }}</text>
                </view>
                <view class="comment-action" @click="replyComment(comment)">
                  <up-icon name="chat" color="#8e8e93" size="14"></up-icon>
                  <text class="action-text">回复</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部评论输入栏 -->
    <view class="bottom-comment-bar">
      <view class="comment-input-wrapper">
        <up-image
            :src="currentUser.avatar"
            class="user-avatar"
            height="32"
            shape="circle"
            width="32"
        ></up-image>
        <view class="input-container" @click="showCommentInput">
          <text class="input-placeholder">说点什么...</text>
        </view>
        <view class="quick-actions">
          <view class="quick-action" @click="toggleLike">
            <up-icon
                :color="postDetail.isLiked ? '#fa709a' : '#8e8e93'"
                :name="postDetail.isLiked ? 'thumb-up-fill' : 'thumb-up'"
                size="20"
            ></up-icon>
          </view>
          <view class="quick-action" @click="toggleCollect">
            <up-icon
                :color="postDetail.isCollected ? '#fa8c16' : '#8e8e93'"
                :name="postDetail.isCollected ? 'star-fill' : 'star'"
                size="20"
            ></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 评论输入弹窗 -->
    <up-popup :show="showCommentDialog" @close="hideCommentInput" mode="bottom" round="16">
      <view class="comment-input-popup">
        <view class="popup-header">
          <text class="popup-title">{{ replyTarget ? '回复评论' : '发表评论' }}</text>
          <view class="close-btn" @click="hideCommentInput">
            <up-icon name="close" size="20" color="#8e8e93"></up-icon>
          </view>
        </view>

        <view v-if="replyTarget" class="reply-target">
          <view class="target-info">
            <text class="reply-hint">回复 @{{ replyTarget.author }}：</text>
            <text class="target-content">{{ replyTarget.content }}</text>
          </view>
        </view>

        <view class="input-section">
          <textarea
              v-model="commentContent"
              :focus="showCommentDialog"
              auto-height
              class="comment-textarea"
              maxlength="500"
              placeholder="请输入评论内容..."
          />
          <view class="input-footer">
            <text class="word-count">{{ commentContent.length }}/500</text>
            <view class="send-btn" :class="{ disabled: !commentContent.trim() }" @click="sendComment">
              <text class="send-text">发送</text>
            </view>
          </view>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script>
import {
  getPostById,
  getPostComments,
  createComment,
  likePost,
  unlikePost,
  collectPost,
  uncollectPost,
  followUser,
  unfollowUser,
  likeComment,
  unlikeComment
} from '@/api/forum'
import {formatTime} from '@/utils/timeUtil'

export default {
  data() {
    return {
      // 帖子ID
      postId: null,

      // 当前用户信息
      currentUser: {
        id: '',
        name: '',
        avatar: ''
      },

      // 页面状态
      loading: true,
      sortType: 'time',
      showCommentDialog: false,
      commentContent: '',
      replyTarget: null,
      loadingComments: false,

      // 帖子详情
      postDetail: {
        id: 0,
        title: '',
        content: '',
        category: {
          id: 0,
          name: '',
          color: ''
        },
        tags: [],
        images: [],
        author: {
          id: 0,
          name: '',
          avatar: '',
          title: '',
          isFollowing: false
        },
        createdAt: '',
        viewCount: 0,
        commentCount: 0,
        likeCount: 0,
        shareCount: 0,
        collectCount: 0,
        isLiked: false,
        isCollected: false
      },

      // 评论列表
      commentList: []
    }
  },

  computed: {
    // 处理帖子图片列表
    postImages() {
      if (!this.postDetail.images) return [];

      // 如果images是字符串，按逗号分割
      if (typeof this.postDetail.images === 'string') {
        return this.postDetail.images.split(',').filter(img => img.trim());
      }

      // 如果images是数组，直接返回
      if (Array.isArray(this.postDetail.images)) {
        return this.postDetail.images;
      }

      return [];
    }
  },

  onLoad(options) {
    // 获取帖子ID
    if (options.id) {
      this.postId = parseInt(options.id);
    }

    // 加载帖子详情和评论
    this.loadPostDetail();
    this.loadComments();

    this.currentUser = uni.getStorageSync('userInfo');
  },

  methods: {
    // 页面导航
    goBack() {
      uni.navigateBack();
    },

    // 加载帖子详情
    async loadPostDetail() {
      try {
        this.loading = true;
        const response = await getPostById(this.postId);
        this.postDetail = response;
      } catch (error) {
        console.error('加载帖子详情失败:', error);
        uni.showToast({
          title: '加载帖子详情失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载评论列表
    async loadComments() {
      const params = {
        sortBy: this.sortType === 'time' ? 'latest' : 'hot'
      };
      const response = await getPostComments(this.postId, params);
      this.commentList = response || [];
    },

    // 时间格式化方法
    formatTime(timeStr) {
      return formatTime(timeStr);
    },

    // 切换关注
    async toggleFollow() {
      try {
        if (this.postDetail.isFollowing) {
          await unfollowUser(this.postDetail.authorId);
        } else {
          await followUser(this.postDetail.authorId);
        }
        this.postDetail.isFollowing = !this.postDetail.isFollowing;
        uni.showToast({
          title: this.postDetail.isFollowing ? '关注成功' : '取消关注',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 切换点赞
    async toggleLike() {
      try {
        if (this.postDetail.isLiked) {
          await unlikePost(this.postDetail.id);
          this.postDetail.likeCount -= 1;
        } else {
          await likePost(this.postDetail.id);
          this.postDetail.likeCount += 1;
        }
        this.postDetail.isLiked = !this.postDetail.isLiked;
        uni.showToast({
          title: this.postDetail.isLiked ? '点赞成功' : '取消点赞',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 切换收藏
    async toggleCollect() {
      try {
        if (this.postDetail.isCollected) {
          await uncollectPost(this.postDetail.id);
          this.postDetail.collectCount -= 1;
        } else {
          await collectPost(this.postDetail.id);
          this.postDetail.collectCount += 1;
        }

        this.postDetail.isCollected = !this.postDetail.isCollected;
        uni.showToast({
          title: this.postDetail.isCollected ? '收藏成功' : '取消收藏',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 分享帖子
    sharePost() {
      uni.showActionSheet({
        itemList: ['分享给朋友', '举报'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              uni.showToast({title: '分享成功', icon: 'success'});
              break;
            case 1:
              uni.showModal({
                title: '举报',
                content: '确定要举报这个帖子吗？',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    uni.showToast({title: '举报成功', icon: 'success'});
                  }
                }
              });
              break;
          }
        }
      });
    },

    // 设置排序类型
    setSortType(type) {
      this.sortType = type;
      // 切换排序时重新加载评论
      this.commentList = [];
      this.loadComments();
    },

    // 显示评论输入框
    showCommentInput() {
      this.showCommentDialog = true;
      this.replyTarget = null;
      this.commentContent = '';
    },

    // 聚焦评论输入框
    focusCommentInput() {
      this.showCommentInput();
    },

    // 隐藏评论输入框
    hideCommentInput() {
      this.showCommentDialog = false;
      this.replyTarget = null;
      this.commentContent = '';
    },

    // 回复评论
    replyComment(comment) {
      this.replyTarget = comment;
      this.showCommentDialog = true;
      this.commentContent = '';
    },

    // 发送评论
    async sendComment() {
      if (!this.commentContent.trim()) return;

      try {
        const params = {
          postId: this.postId,
          content: this.commentContent,
          parentId: this.replyTarget ? this.replyTarget.id : undefined,
          replyToId: this.replyTarget ? this.replyTarget.authorId : undefined
        };

        const newComment = await createComment(params);

        if (this.replyTarget) {
          // 添加回复
          if (!this.replyTarget.children) {
            this.replyTarget.children = [];
          }
          this.replyTarget.children.push(newComment);
        } else {
          // 添加评论
          this.commentList.unshift(newComment);
          this.postDetail.commentCount++;
        }

        this.hideCommentInput();
        uni.showToast({
          title: '发送成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('发送评论失败:', error);
        uni.showToast({
          title: '发送失败',
          icon: 'none'
        });
      }
    },

    // 切换评论点赞
    async toggleCommentLike(comment) {
      try {
        if (comment.isLiked) {
          await unlikeComment(comment.id);
          comment.likeCount -= 1;
        } else {
          await likeComment(comment.id);
          comment.likeCount += 1;
        }

        comment.isLiked = !comment.isLiked;
      } catch (error) {
        console.error('评论点赞失败:', error);
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 查看用户资料
    viewProfile(userId) {
      uni.navigateTo({
        url: `/pages/profile/user-profile?userId=${userId}`
      });
    },

    // 预览图片
    previewImage(currentIndex) {
      if (this.postImages.length === 0) return;

      uni.previewImage({
        current: currentIndex,
        urls: this.postImages,
        indicator: 'number',
        loop: true,
        fail: (err) => {
          console.error('图片预览失败:', err);
          uni.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
    },

    // 获取图片容器样式类
    getImagesContainerClass() {
      const count = this.postImages.length;
      if (count === 1) return 'single';
      if (count === 2) return 'double';
      if (count === 3) return 'triple';
      return 'grid';
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/pages/forum/forum-detail.scss';
</style>
