package com.cy.education.security;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cy.education.model.entity.Student;
import com.cy.education.repository.StudentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * 用户认证服务实现
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private StudentMapper studentMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 查询学生用户
        Student admin = studentMapper.selectOne(
                new LambdaQueryWrapper<Student>()
                        .eq(Student::getUsername, username)
        );

        if (admin == null) {
            throw new UsernameNotFoundException("用户名不存在");
        }

        // 检查用户状态
        if (admin.getStatus() != 1) {
            throw new UsernameNotFoundException("用户已被禁用");
        }

        // 创建自定义的JwtUserDetails
        JwtUserDetails userDetails = new JwtUserDetails(
                admin.getUsername(),
                admin.getPassword(),
                Collections.emptyList()
        );

        // 设置额外信息
        userDetails.setUserId(admin.getId().toString());
        userDetails.setRealName(admin.getName());

        return userDetails;
    }
}
