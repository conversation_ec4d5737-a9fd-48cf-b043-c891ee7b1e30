<template>
  <!--  TODO: 未完成-->
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="navbar-left">
          <view class="back-btn" @click="goBack">
            <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
          </view>
          <text class="navbar-title">消息中心</text>
        </view>
        <view class="navbar-right">
          <view class="back-btn" @click="markAllRead">
            <up-icon color="#fff" name="checkmark" size="12"></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="content-container">
      <view class="message-list">
        <view
            v-for="message in messages"
            :key="message.id"
            :class="{ unread: !message.isRead }"
            class="message-item"
            @click="goToMessageDetail(message)"
        >
          <view class="message-avatar">
            <up-avatar
                :src="message.avatar"
                size="48"
            ></up-avatar>
            <view v-if="!message.isRead" class="unread-dot"></view>
          </view>

          <view class="message-content">
            <view class="message-header">
              <text class="sender-name">{{ message.title }}</text>
              <text class="message-time">{{ formatTime(message.date) }}</text>
            </view>
            <text class="message-preview">{{ message.content }}</text>
            <view v-if="message.type" class="message-type">
              <up-tag
                  :text="getTypeLabel(message.type)"
                  :type="getTypeColor(message.type)"
                  size="mini"
              ></up-tag>
            </view>
          </view>

          <view class="message-actions">
            <up-icon name="arrow-right" size="16" color="#c0c4cc"></up-icon>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="messages.length === 0" class="empty-state">
        <up-icon name="chat" size="64" color="#e0e0e0"></up-icon>
        <text class="empty-text">暂无消息</text>
      </view>
    </view>
  </view>
</template>

<script>
import {formatTime} from "@/utils/timeUtil";

export default {
  data() {
    return {
      messages: [
        {
          id: '1',
          title: '系统通知',
          content: '您有新的课程可以学习，请及时查看学习计划',
          date: '2024-06-01 14:30:00',
          avatar: '/static/system-avatar.png',
          isRead: false,
          type: 'system'
        },
        {
          id: '2',
          title: '考试提醒',
          content: '期末考试将于明天开始，请做好准备',
          date: '2024-06-01 10:15:00',
          avatar: '/static/exam-avatar.png',
          isRead: true,
          type: 'exam'
        },
        {
          id: '3',
          title: '学习进度',
          content: '恭喜您完成了本周的学习目标！',
          date: '2024-05-31 16:20:00',
          avatar: '/static/progress-avatar.png',
          isRead: false,
          type: 'progress'
        }
      ]
    }
  },

  onLoad() {
    this.loadMessages();
  },

  methods: {
    formatTime,
    goBack() {
      uni.navigateBack();
    },

    loadMessages() {
      // 模拟加载消息数据
      console.log('加载消息列表');
    },

    markAllRead() {
      this.messages.forEach(message => {
        message.isRead = true;
      });
      uni.showToast({
        title: '已全部标记为已读',
        icon: 'success'
      });
    },

    goToMessageDetail(message) {
      message.isRead = true;
      uni.navigateTo({
        url: `/pages/profile/message-detail?id=${message.id}`
      });
    },

    getTypeLabel(type) {
      const typeMap = {
        system: '系统',
        exam: '考试',
        progress: '进度'
      };
      return typeMap[type] || '消息';
    },

    getTypeColor(type) {
      const colorMap = {
        system: 'primary',
        exam: 'warning',
        progress: 'success'
      };
      return colorMap[type] || 'info';
    }
  }
}
</script>

<style lang="scss" scoped>

// 内容区域
.content-container {
  padding: 20px;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.message-item:active {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.message-item.unread {
  border-left: 4px solid #667eea;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
}

.message-avatar {
  position: relative;
  flex-shrink: 0;
}

.unread-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: #f56c6c;
  border-radius: 50%;
  border: 2px solid #fff;
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sender-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.message-time {
  font-size: 12px;
  color: #8e8e93;
}

.message-preview {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-type {
  margin-top: 4px;
}

.message-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-text {
  font-size: 16px;
  color: #8e8e93;
  margin-top: 16px;
}
</style>
