package com.cy.education.model.params;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 管理员查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminQueryParams extends PageParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 关键词（姓名或用户名）
     */
    private String keyword;

    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 状态：0禁用，1启用
     */
    private Integer status;
} 