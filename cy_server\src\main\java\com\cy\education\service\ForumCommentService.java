package com.cy.education.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.ForumComment;
import com.cy.education.model.vo.ForumCommentQueryParam;

import java.util.List;

/**
 * 论坛评论服务接口
 */
public interface ForumCommentService {
    
    /**
     * 获取评论列表（分页）
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<ForumComment> getCommentPage(ForumCommentQueryParam param);
    
    /**
     * 获取帖子的评论列表（树形结构）
     * 
     * @param postId 帖子ID
     * @return 评论列表
     */
    List<ForumComment> getPostComments(Integer postId);
    
    /**
     * 获取评论详情
     * 
     * @param id 评论ID
     * @return 评论信息
     */
    ForumComment getCommentById(Integer id);
    
    /**
     * 审核评论
     * 
     * @param id 评论ID
     * @param status 状态
     * @return 是否成功
     */
    boolean reviewComment(Integer id, Integer status);
    
    /**
     * 删除评论（假删除）
     * 
     * @param id 评论ID
     * @return 是否成功
     */
    boolean deleteComment(Integer id);
    
    /**
     * 批量审核评论
     * 
     * @param ids 评论ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchReviewComments(List<Integer> ids, Integer status);
    
    /**
     * 批量删除评论（假删除）
     * 
     * @param ids 评论ID列表
     * @return 是否成功
     */
    boolean batchDeleteComments(List<Integer> ids);
} 