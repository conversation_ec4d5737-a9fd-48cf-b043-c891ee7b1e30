<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left">
          <view class="back-btn" @click="goBack">
            <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
          </view>
        </view>
        <view class="navbar-right">
          <view class="back-btn" @click="toggleBookmark">
            <up-icon :name="isBookmarked ? 'heart-fill' : 'heart'" color="#fff" size="12"></up-icon>
          </view>
          <view class="back-btn" @click="shareDocument">
            <up-icon color="#fff" name="share" size="12"></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 文档信息卡片 -->
    <view class="lesson-card">
      <view class="lesson-header">
        <view class="lesson-details">
          <text class="lesson-title">{{ lessonInfo.title }}</text>
          <view class="lesson-meta-row">
            <text v-if="lessonInfo.duration" class="lesson-meta">时长：{{ lessonInfo.duration }}</text>
          </view>
        </view>
        <view class="lesson-stats">
          <view class="stat-item">
            <up-icon color="#8e8e93" name="eye" size="14"></up-icon>
            <text class="stat-text">{{ lessonInfo.views }}</text>
          </view>
          <view class="stat-item">
            <up-icon color="#8e8e93" name="thumb-up" size="14"></up-icon>
            <text class="stat-text">{{ lessonInfo.likes }}</text>
          </view>
        </view>
      </view>
      <!-- tags展示 -->
      <view v-if="lessonInfo.tags" class="tags-container-opt">
        <view v-for="tag in parseTags(lessonInfo.tags)" :key="tag" class="tag-item-opt">
          {{ tag }}
        </view>
      </view>
      <view class="progress-section">
        <view class="progress-header">
          <text class="progress-label">阅读进度</text>
          <text class="progress-value">{{ Math.round(progress) }}%</text>
        </view>
        <view class="progress-bar">
          <view :style="{ width: progress + '%' }" class="progress-fill"></view>
        </view>
      </view>
    </view>

    <!-- 文档内容区域 -->
    <view class="document-content" @scroll="onContentScroll" @click="onContentClick">
      <view class="content-wrapper">
        <view class="content-page">
          <rich-text :nodes="lessonInfo.content"></rich-text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, onUnmounted} from 'vue'
import {saveStudyRecord, type StudyRecordSaveParams} from '@/api/course'

const lessonInfo = reactive({
  id: 0,
  title: '',
  type: '',
  duration: '',
  tags: '',
  views: 0,
  likes: 0,
  content: '',
  courseId: 0,
  resourceId: 0,
  progress: 0 // 用于存储上次保存的进度
})
const isBookmarked = ref(false)
const progress = ref(0)
const studyDuration = ref(0) // 学习时长（秒）
let progressTimer: ReturnType<typeof setInterval> | null = null
let saveTimer: ReturnType<typeof setInterval> | null = null
let startTime = Date.now()

onMounted(() => {
  // 获取lesson参数
  const pages = getCurrentPages() as any[]
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  if (options.lesson) {
    const lesson = JSON.parse(decodeURIComponent(options.lesson))
    Object.assign(lessonInfo, lesson)
  }

  if (options.courseId) {
    lessonInfo.courseId = parseInt(options.courseId)
  }

  // 记录开始时间
  startTime = Date.now()

  // 从上次保存的进度开始，如果没有则从0开始
  progress.value = lessonInfo.progress || 0

  // 如果进度已经完成，直接设置为100%
  if (progress.value >= 100) {
    progress.value = 100
  } else {
    // 自动增长阅读进度（从当前进度开始）
    progressTimer = setInterval(() => {
      if (progress.value < 100) {
        progress.value = Math.min(100, progress.value + Math.random() * 2)
        studyDuration.value = Math.floor((Date.now() - startTime) / 1000)
      } else if (progressTimer) {
        clearInterval(progressTimer)
      }
    }, 2000)
  }

  // 定时保存学习进度（每30秒保存一次）
  saveTimer = setInterval(() => {
    saveStudyProgress()
  }, 30000)

  // 页面加载时立即保存一次
  setTimeout(() => {
    saveStudyProgress()
  }, 1000)
})

onUnmounted(() => {
  if (progressTimer) clearInterval(progressTimer)
  if (saveTimer) clearInterval(saveTimer)

  // 页面卸载时保存最终进度
  saveStudyProgress()
})

// 保存学习进度
const saveStudyProgress = async () => {
  try {
    console.log(lessonInfo)
    if (!lessonInfo.courseId || !lessonInfo.id || !lessonInfo.resourceId) {
      console.warn('缺少必要的课程信息，无法保存学习进度')
      return
    }

    const params: StudyRecordSaveParams = {
      courseId: lessonInfo.courseId,
      lessonId: lessonInfo.id,
      resourceId: lessonInfo.resourceId,
      resourceType: 'article', // 文档类型
      progress: Math.round(progress.value),
      duration: studyDuration.value,
      completed: progress.value >= 100 ? 1 : 0,
      lastPosition: studyDuration.value
    }

    const result = await saveStudyRecord(params)
    console.log('学习进度保存成功:', result)
  } catch (error) {
    console.error('保存学习进度失败:', error)
  }
}

const goBack = () => {
  // 返回前保存进度
  saveStudyProgress()
  uni.navigateBack()
}

// 内容滚动事件
const onContentScroll = () => {
  // 用户滚动时增加进度
  if (progress.value < 100) {
    progress.value = Math.min(100, progress.value + 1)
  }
}

// 内容点击事件
const onContentClick = () => {
  // 用户点击时增加进度
  if (progress.value < 100) {
    progress.value = Math.min(100, progress.value + 0.5)
  }
}

// 格式化学习时长
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

const toggleBookmark = () => {
  isBookmarked.value = !isBookmarked.value
  uni.showToast({title: isBookmarked.value ? '已收藏' : '已取消收藏', icon: 'none'})
}
const shareDocument = () => {
  uni.showActionSheet({
    itemList: ['分享给朋友'],
    success: (res) => {
      if (res.tapIndex === 0) {
        uni.showToast({title: '分享成功', icon: 'success'})
      }
    }
  })
}

function parseTags(tags: any) {
  if (!tags) return []
  if (Array.isArray(tags)) return tags
  try {
    const arr = JSON.parse(tags)
    if (Array.isArray(arr)) return arr
  } catch (e) {
  }
  if (typeof tags === 'string') return tags.split(',').map((t) => t.trim()).filter(Boolean)
  return []
}
</script>

<style lang="scss" scoped>
.lesson-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin: 16px 16px 0 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.lesson-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.lesson-details {
  flex: 1;
}

.lesson-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
  line-height: 1.3;
  margin-bottom: 4px;
}

.lesson-meta-row {
  display: flex;
  gap: 16px;
  margin-top: 4px;
}

.lesson-meta {
  font-size: 13px;
  color: #8e8e93;
}

.lesson-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-text {
  font-size: 12px;
  color: #8e8e93;
}

.tags-container-opt {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0 0 0;
}

.tag-item-opt {
  padding: 4px 14px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  border-radius: 16px;
  font-size: 12px;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 4px;
}

.progress-section {
  margin-top: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  color: #1a1d2e;
  font-weight: 500;
}

.progress-value {
  font-size: 14px;
  color: #667eea;
  font-weight: 600;
}

.progress-bar {
  height: 6px;
  background: #f0f2f5;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.document-content {
  flex: 1;
  overflow-y: auto;
  margin: 16px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  padding: 20px;
}

.content-wrapper {
  padding: 0;
}

.content-page {
  margin-bottom: 32px;
}

.study-duration {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  z-index: 1000;
}

.duration-text {
  color: #fff;
}
</style>
