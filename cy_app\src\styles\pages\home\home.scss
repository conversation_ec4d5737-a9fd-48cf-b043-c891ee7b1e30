.home-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f6fa 0%, #ffffff 100%);
}

.header-section {
  background: linear-gradient(135deg, #6a8dff 0%, #a084ee 100%);
  padding: 16px 0;
  position: relative;
  border-radius: 0 0 26px 26px;
  box-shadow: 0 8px 32px rgba(106, 141, 255, 0.10);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  z-index: 2;
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  border-radius: 20px;
}

.left-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar-container {
  margin-right: 16px;
  position: relative;
}

.user-text {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.greeting {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 4px;
  font-weight: 400;
}

.name {
  font-size: 20px;
  color: #fff;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.right-content {
  display: flex;
  align-items: center;
}

.notification-btn {
  position: relative;
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.notification-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  min-width: 18px;
  height: 18px;
  background: #ff4757;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
}

.badge-text {
  font-size: 10px;
  color: #fff;
  font-weight: 600;
  line-height: 1;
}

.content-container {
  padding: 0 20px 120px 20px;
  margin-top: -16px;
  position: relative;
  z-index: 1;
}

.carousel-section {
  margin-bottom: 28px;
}

.carousel-swiper {
  height: 180px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.carousel-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.carousel-image {
  width: 100%;
  height: 100%;
}

.carousel-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 16px 20px;
}

.carousel-title {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.function-section {
  background: #fff;
  border-radius: 24px;
  padding: 14px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 0;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 0 4px;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.function-item:active {
  transform: translateY(-2px);
}

.function-icon-container {
  position: relative;
  margin-bottom: 12px;
}

.function-icon {
  width: 50px;
  height: 50px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.function-item:active .function-icon {
  transform: scale(0.95);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.function-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 20px;
  height: 20px;
  background: #ff4757;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
}

.function-badge .badge-text {
  font-size: 11px;
  color: #fff;
  font-weight: 600;
  line-height: 1;
}

.function-name {
  font-size: 14px;
  color: #4a5568;
  text-align: center;
  font-weight: 500;
  line-height: 1.2;
}

.notice-section {
  background: #fff;
  border-radius: 24px;
  padding: 14px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.more-btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #f5f7ff;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.more-btn:active {
  background: #e8f2ff;
  transform: scale(0.95);
}

.more-text {
  font-size: 13px;
  color: #667eea;
  margin-right: 6px;
  font-weight: 500;
}

.notice-list {
  display: flex;
  flex-direction: column;
}

.notice-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.notice-item:active {
  background: #f8faff;
  padding: 16px 12px;
  margin: 0 -12px;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.notice-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 12px;
}

.notice-title {
  font-size: 15px;
  color: #1a1d2e;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  line-height: 1.3;
}

.notice-time {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 400;
}

// 最新动态样式
.news-section {
  background: #fff;
  border-radius: 24px;
  padding: 14px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.news-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.news-item:active {
  background: #f8faff;
  padding: 12px;
  margin: 0 -12px;
}

.news-item:last-child {
  border-bottom: none;
}

.news-cover {
  flex-shrink: 0;
}

.news-image {
  border-radius: 8px;
  overflow: hidden;
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.news-title {
  font-size: 15px;
  color: #1a1d2e;
  font-weight: 600;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-summary {
  font-size: 13px;
  color: #8e8e93;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.news-time,
.news-views {
  font-size: 12px;
  color: #8e8e93;
}
