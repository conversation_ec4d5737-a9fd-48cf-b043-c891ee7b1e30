package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PK参与者实体
 */
@Data
@TableName("pk_participant")
public class PkParticipant {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 房间ID
     */
    private Long roomId;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 位置: 1-左侧, 2-右侧
     */
    private Integer position;
    
    /**
     * 得分
     */
    private Integer score;
    
    /**
     * 正确数
     */
    private Integer correctCount;
    
    /**
     * 错误数
     */
    private Integer wrongCount;
    
    /**
     * 总答题时间(毫秒)
     */
    private Integer answerTime;
    
    /**
     * 是否准备: 0-未准备, 1-已准备
     */
    private Boolean isReady;
    
    /**
     * 是否完成: 0-未完成, 1-已完成
     */
    private Boolean isFinished;
    
    /**
     * 加入时间
     */
    private LocalDateTime joinedAt;
    
    /**
     * 完成时间
     */
    private LocalDateTime finishedAt;
}
