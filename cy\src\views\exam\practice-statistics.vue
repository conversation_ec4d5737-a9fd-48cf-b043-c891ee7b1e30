<template>
  <div class="practice-statistics">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>练习统计管理</h2>
      <p>查看和管理学员练习统计数据</p>
    </div>

    <!-- 统计概览卡片
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon total-students">
                <i class="el-icon-user"></i>
              </div>
              <div class="card-info">
                <h3>{{ overviewData.totalStudents || 0 }}</h3>
                <p>总学员数</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon total-banks">
                <i class="el-icon-collection"></i>
              </div>
              <div class="card-info">
                <h3>{{ overviewData.totalBanks || 0 }}</h3>
                <p>题库数量</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon total-answered">
                <i class="el-icon-edit"></i>
              </div>
              <div class="card-info">
                <h3>{{ overviewData.totalAnsweredQuestions || 0 }}</h3>
                <p>总答题数</p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon accuracy-rate">
                <i class="el-icon-trophy"></i>
              </div>
              <div class="card-info">
                <h3>{{ overviewData.overallAccuracyRate || 0 }}%</h3>
                <p>总体正确率</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div> -->

    <!-- 搜索筛选区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="学员姓名">
          <el-input
            v-model="searchForm.studentName"
            placeholder="请输入学员姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="工号">
          <el-input
            v-model="searchForm.studentNumber"
            placeholder="请输入工号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="部门">
          <el-select
            v-model="searchForm.departmentId"
            placeholder="请选择部门"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dept in departmentList"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题库">
          <el-select
            v-model="searchForm.bankId"
            placeholder="请选择题库"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="bank in bankList"
              :key="bank.id"
              :label="bank.name"
              :value="bank.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="正确率">
          <el-input-number
            v-model="searchForm.minAccuracyRate"
            placeholder="最小值"
            :min="0"
            :max="100"
            :precision="2"
            style="width: 100px"
          />
          <span style="margin: 0 10px">-</span>
          <el-input-number
            v-model="searchForm.maxAccuracyRate"
            placeholder="最大值"
            :min="0"
            :max="100"
            :precision="2"
            style="width: 100px"
          />
        </el-form-item>
        <el-form-item label="练习时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
          <el-button type="success" @click="handleExport">
            <i class="el-icon-download"></i> 导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <div class="table-header">
        <h3>练习统计列表</h3>
        <div class="table-actions">
          <el-switch
            v-model="searchForm.onlyWithRecords"
            active-text="仅显示有练习记录"
            @change="handleSearch"
          />
        </div>
      </div>

      <!-- 调试信息 -->
      <div style="margin: 10px 0; color: #666; font-size: 12px;">
        数据条数: {{ tableData.length }} | 总数: {{ pagination.total }}
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="studentName" label="学员姓名" width="120" fixed="left" />
        <el-table-column prop="studentNumber" label="工号" width="120" />
        <el-table-column prop="departmentName" label="部门" width="120" />
        <el-table-column prop="bankName" label="题库" width="150" />
        <el-table-column prop="totalQuestions" label="总题数" width="80" align="center" />
        <el-table-column prop="answeredQuestions" label="已答题数" width="90" align="center" />
        <el-table-column label="练习进度" width="120" align="center">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.progressRate || 0"
              :color="getProgressColor(scope.row.progressRate)"
              :stroke-width="8"
            />
          </template>
        </el-table-column>
        <el-table-column prop="correctCount" label="正确数" width="80" align="center" />
        <el-table-column prop="wrongCount" label="错误数" width="80" align="center" />
        <el-table-column label="正确率" width="100" align="center" sortable="custom">
          <template #default="scope">
            <el-tag
              :type="getAccuracyType(scope.row.accuracyRate)"
              size="small"
            >
              {{ (scope.row.accuracyRate * 100).toFixed(1) }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="practiceCount" label="练习次数" width="90" align="center" sortable="custom" />
        <el-table-column prop="totalPracticeMinutes" label="总时长(分)" width="100" align="center" />
        <el-table-column prop="avgPracticeMinutes" label="平均时长(分)" width="110" align="center" />
        <el-table-column prop="wrongQuestionCount" label="错题数" width="80" align="center" />
        <el-table-column prop="lastPracticeTime" label="最后练习时间" width="160" sortable="custom">
          <template #default="scope">
            {{ formatDateTime(scope.row.lastPracticeTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              type="text"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleViewRecords(scope.row)"
            >
              练习记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      title="练习统计详情"
      :visible.sync="detailDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="detailData" class="detail-content">
        <!-- 详情内容将在后续实现 -->
        <p>详情内容开发中...</p>
      </div>
    </el-dialog>

    <!-- 练习记录对话框 -->
    <el-dialog
      title="练习记录"
      :visible.sync="recordsDialogVisible"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div v-if="recordsData" class="records-content">
        <!-- 练习记录内容将在后续实现 -->
        <p>练习记录开发中...</p>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPracticeStatistics, getAllPracticeOverview, getStudentPracticeRecords, getPracticeStatisticsDetail, exportPracticeStatistics } from '@/api/practice-statistics'
import { getDepartmentList } from '@/api/department'
import { getBankList } from '@/api/exam'

export default {
  name: 'PracticeStatistics',
  data() {
    return {
      loading: false,
      overviewData: {},
      searchForm: {
        studentName: '',
        studentNumber: '', // 实际是工号
        departmentId: null,
        bankId: null,
        minAccuracyRate: null,
        maxAccuracyRate: null,
        dateRange: null,
        onlyWithRecords: false,
        sortField: '',
        sortOrder: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      departmentList: [],
      bankList: [],
      detailDialogVisible: false,
      detailData: null,
      recordsDialogVisible: false,
      recordsData: null
    }
  },
  created() {
    this.loadOverviewData()
    this.loadDepartmentList()
    this.loadBankList()
    this.loadTableData()
  },
  methods: {
    // 加载概览数据
    async loadOverviewData() {
      try {
        const response = await getAllPracticeOverview()
        this.overviewData = response.data || {}
      } catch (error) {
        console.error('加载概览数据失败:', error)
        this.$message.error('加载概览数据失败')
      }
    },

    // 加载部门列表
    async loadDepartmentList() {
      try {
        const response = await getDepartmentList()
        this.departmentList = response.data || []
      } catch (error) {
        console.error('加载部门列表失败:', error)
      }
    },

    // 加载题库列表
    async loadBankList() {
      try {
        const response = await getBankList({})
        this.bankList = response.data?.list || []
      } catch (error) {
        console.error('加载题库列表失败:', error)
      }
    },

    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const params = {
          ...this.searchForm,
          page: this.pagination.page,
          size: this.pagination.size
        }

        // 处理日期范围
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.startTime = this.searchForm.dateRange[0]
          params.endTime = this.searchForm.dateRange[1]
        }
        delete params.dateRange

        const response = await getPracticeStatistics(params)
        this.tableData = response.list || []
        this.pagination.total = response.total || 0
        console.log('设置tableData:', this.tableData.length, '条记录')
        console.log('第一条数据:', this.tableData[0])
        // 强制触发Vue响应式更新
        this.$nextTick(() => {
          console.log('nextTick后的tableData长度:', this.tableData.length)
        })
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadTableData()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        studentName: '',
        studentNumber: '',
        departmentId: null,
        bankId: null,
        minAccuracyRate: null,
        maxAccuracyRate: null,
        dateRange: null,
        onlyWithRecords: false,
        sortField: '',
        sortOrder: ''
      }
      this.handleSearch()
    },

    // 导出数据
    async handleExport() {
      try {
        this.$message.info('正在导出数据，请稍候...')

        // 使用当前搜索条件导出
        const params = {
          ...this.searchForm
        }

        await exportPracticeStatistics(params)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    },

    // 排序变化
    handleSortChange({ column, prop, order }) {
      this.searchForm.sortField = prop
      this.searchForm.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      this.loadTableData()
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.page = 1
      this.loadTableData()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadTableData()
    },

    // 查看详情
    async handleViewDetail(row) {
      try {
        const response = await getPracticeStatisticsDetail(row.userId, row.bankId)
        this.detailData = response.data
        this.detailDialogVisible = true
      } catch (error) {
        console.error('加载详情失败:', error)
        this.$message.error('加载详情失败')
      }
    },

    // 查看练习记录
    async handleViewRecords(row) {
      try {
        const response = await getStudentPracticeRecords(row.userId, row.bankId)
        this.recordsData = response.data
        this.recordsDialogVisible = true
      } catch (error) {
        console.error('加载练习记录失败:', error)
        this.$message.error('加载练习记录失败')
      }
    },

    // 获取进度条颜色
    getProgressColor(percentage) {
      if (percentage < 30) return '#f56c6c'
      if (percentage < 70) return '#e6a23c'
      return '#67c23a'
    },

    // 获取正确率标签类型
    getAccuracyType(rate) {
      const percentage = rate * 100
      if (percentage < 60) return 'danger'
      if (percentage < 80) return 'warning'
      return 'success'
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.practice-statistics {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .overview-cards {
    margin-bottom: 20px;

    .overview-card {
      .card-content {
        display: flex;
        align-items: center;
        padding: 10px 0;

        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          i {
            font-size: 24px;
            color: white;
          }

          &.total-students {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.total-banks {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          &.total-answered {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.accuracy-rate {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }

        .card-info {
          flex: 1;

          h3 {
            margin: 0 0 4px 0;
            font-size: 28px;
            font-weight: 600;
            color: #303133;
          }

          p {
            margin: 0;
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }

  .search-card {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 16px;
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
      }

      .table-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      text-align: right;
    }
  }

  .detail-content,
  .records-content {
    padding: 20px 0;
  }

  // 表格样式优化
  ::v-deep .el-table {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #606266;
        font-weight: 600;
      }
    }

    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  // 进度条样式
  ::v-deep .el-progress {
    .el-progress__text {
      font-size: 12px !important;
    }
  }

  // 标签样式
  .el-tag {
    font-weight: 600;
  }

  // 按钮样式
  .el-button--text {
    padding: 0;
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
