package com.cy.education.model.params;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 题目添加/修改参数
 */
@Data
public class ExamQuestionParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID(修改时使用)
     */
    private Integer id;

    /**
     * 所属题库ID
     */
    @NotNull(message = "所属题库不能为空")
    private Integer bankId;

    /**
     * 题目标题
     */
    @NotBlank(message = "题目标题不能为空")
    private String title;

    /**
     * 题目类型(single-单选题,multiple-多选题,judgment-判断题,fill-填空题,essay-简答题)
     */
    @NotBlank(message = "题目类型不能为空")
    private String type;

    /**
     * 选项(单选、多选题使用)
     */
    private List<String> options;

    /**
     * 正确答案
     */
    private String correctAnswer;

    /**
     * 题目解析
     */
    private String explanation;
} 