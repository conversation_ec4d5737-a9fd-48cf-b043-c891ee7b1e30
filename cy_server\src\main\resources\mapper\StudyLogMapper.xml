<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.StudyLogMapper">

    <!-- 结果映射 -->
    <resultMap id="StudyLogResultMap" type="com.cy.education.model.entity.StudyLog">
        <id column="id" property="id"/>
        <result column="record_id" property="recordId"/>
        <result column="user_id" property="userId"/>
        <result column="course_id" property="courseId"/>
        <result column="lesson_id" property="lessonId"/>
        <result column="resource_id" property="resourceId"/>
        <result column="start_position" property="startPosition"/>
        <result column="end_position" property="endPosition"/>
        <result column="duration" property="duration"/>
        <result column="progress" property="progress"/>
        <result column="completed" property="completed"/>
        <result column="study_time" property="studyTime"/>
        <result column="created_at" property="createdAt"/>
        <!-- 关联字段 -->
        <result column="student_name" property="studentName"/>
        <result column="course_name" property="courseName"/>
        <result column="lesson_name" property="lessonName"/>
    </resultMap>

    <!-- 分页查询学习日志，包含详细信息 -->
    <select id="selectPageWithDetails" resultMap="StudyLogResultMap">
        SELECT
            sl.*,
            s.name as student_name,
            c.name as course_name
        FROM study_logs sl
        LEFT JOIN students s ON sl.user_id = s.id
        LEFT JOIN courses c ON sl.course_id = c.id
        <where>
            <if test="studentId != null">
                AND sl.user_id = #{studentId}
            </if>
            <if test="courseId != null">
                AND sl.course_id = #{courseId}
            </if>
            <if test="lessonId != null">
                AND sl.lesson_id = #{lessonId}
            </if>
            <if test="startTime != null">
                AND sl.created_at &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND sl.created_at &lt;= #{endTime}
            </if>
        </where>
        ORDER BY sl.created_at DESC
    </select>

    <!-- 获取学员的学习时间趋势 -->
    <select id="getStudyTimeTrend" resultType="java.util.Map">
        SELECT
            DATE(study_time) as date,
            SUM(duration) as duration
        FROM study_logs
        WHERE user_id = #{studentId}
        AND study_time >= DATE_SUB(CURRENT_DATE(), INTERVAL #{days} DAY)
        GROUP BY DATE(study_time)
        ORDER BY date
    </select>

    <!-- 获取课程的学习活动记录 -->
    <select id="getCourseActivities" resultMap="StudyLogResultMap">
        SELECT
            sl.*,
            s.name as student_name
        FROM study_logs sl
        JOIN students s ON sl.user_id = s.id
        WHERE sl.course_id = #{courseId}
        ORDER BY sl.created_at DESC
        LIMIT #{limit}
    </select>

    <!-- 查询用户的学习日志 -->
    <select id="selectUserStudyLogs" resultType="java.util.Map">
        SELECT
            sl.*,
            c.name as course_name
        FROM study_logs sl
        LEFT JOIN courses c ON sl.course_id = c.id
        WHERE sl.user_id = #{userId}
        <if test="startTime != null">
            AND sl.study_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND sl.study_time &lt;= #{endTime}
        </if>
        ORDER BY sl.study_time DESC
    </select>

    <!-- 统计用户每日学习时长 -->
    <select id="statisticsDailyStudyDuration" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(sl.study_time, '%Y-%m-%d') as study_date,
            SUM(sl.duration) as total_duration
        FROM study_logs sl
        WHERE sl.user_id = #{userId}
        <if test="startDate != null">
            AND DATE(sl.study_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND DATE(sl.study_time) &lt;= #{endDate}
        </if>
        GROUP BY DATE_FORMAT(sl.study_time, '%Y-%m-%d')
        ORDER BY study_date
    </select>

    <!-- 统计用户课程学习时长 -->
    <select id="statisticsCourseStudyDuration" resultType="java.util.Map">
        SELECT
            c.id as course_id,
            c.name as course_name,
            SUM(sl.duration) as total_duration
        FROM study_logs sl
        LEFT JOIN courses c ON sl.course_id = c.id
        WHERE sl.user_id = #{userId}
        GROUP BY c.id, c.name
        ORDER BY total_duration DESC
    </select>

</mapper>
