package com.cy.education.service;

import com.cy.education.model.entity.Department;

import java.util.List;
import java.util.Map;

/**
 * 部门服务接口
 */
public interface DepartmentService {

    /**
     * 获取部门列表
     *
     * @param parentId 父部门ID，如果为null则获取顶级部门
     * @param includeChildren 是否包含子部门
     * @return 部门列表
     */
    List<Department> listDepartments(Integer parentId, boolean includeChildren);

    /**
     * 获取部门树形结构
     *
     * @return 部门树形结构
     */
    List<Department> getDepartmentTree();

    /**
     * 根据ID获取部门详情
     *
     * @param id 部门ID
     * @return 部门详情
     */
    Department getDepartmentById(Integer id);

    /**
     * 创建部门
     *
     * @param department 部门信息
     * @return 是否成功
     */
    boolean createDepartment(Department department);

    /**
     * 更新部门
     *
     * @param department 部门信息
     * @return 是否成功
     */
    boolean updateDepartment(Department department);

    /**
     * 删除部门
     *
     * @param id 部门ID
     * @return 是否成功
     */
    boolean deleteDepartment(Integer id);

    /**
     * 更新部门排序
     *
     * @param sortData 排序数据列表，包含id, parentId, sort
     * @return 是否成功
     */
    boolean updateDepartmentSort(List<Map<String, Object>> sortData);

    /**
     * 获取部门及其所有子部门的ID列表
     *
     * @param departmentId 部门ID
     * @return 部门及其所有子部门的ID列表
     */
    List<Integer> getDepartmentAndChildrenIds(Integer departmentId);
    
    /**
     * 根据部门ID列表获取部门名称映射
     *
     * @param departmentIds 部门ID列表
     * @return 部门ID与部门名称的映射
     */
    Map<Integer, String> getDepartmentMapByIds(List<Integer> departmentIds);
} 