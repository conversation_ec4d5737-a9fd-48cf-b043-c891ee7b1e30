<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">编辑资料</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 头像区域 -->
      <view class="avatar-section">
        <view class="avatar-container" @click="changeAvatar">
          <up-image
              :src="userInfo.avatar"
              width="80"
              height="80"
              shape="circle"
              class="avatar-image"
          ></up-image>
          <view class="avatar-overlay">
            <up-icon name="camera" color="#fff" size="20"></up-icon>
          </view>
        </view>
        <text class="avatar-tip">点击更换头像</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">

        <!-- 基本信息 -->
        <view class="form-card">
          <view class="info-header">
            <view class="header-icon">
              <up-icon name="order" color="#667eea" size="20"></up-icon>
            </view>
            <text class="card-title">基本信息</text>
          </view>

          <view class="form-item">
            <view class="item-header">
              <text class="form-label">姓名</text>
              <text class="required">*</text>
            </view>
            <view class="input-wrapper">
              <up-icon name="account" color="#8e8e93" size="18"></up-icon>
              <input
                  v-model="userInfo.name"
                  class="form-input"
                  placeholder="请输入姓名"
                  maxlength="20"
              />
            </view>
          </view>

          <view class="form-item">
            <view class="item-header">
              <text class="form-label">手机号</text>
              <text class="required">*</text>
            </view>
            <view class="input-wrapper">
              <up-icon name="phone" color="#8e8e93" size="18"></up-icon>
              <input
                  v-model="userInfo.phone"
                  class="form-input"
                  placeholder="请输入手机号"
                  maxlength="11"
                  type="number"
              />
            </view>
          </view>

          <view class="form-item">
            <view class="item-header">
              <text class="form-label">邮箱</text>
            </view>
            <view class="input-wrapper">
              <up-icon name="email" color="#8e8e93" size="18"></up-icon>
              <input
                  v-model="userInfo.email"
                  class="form-input"
                  placeholder="请输入邮箱"
                  type="email"
              />
            </view>
          </view>
        </view>

        <!-- 工作信息 -->
        <view class="form-card">
          <view class="info-header">
            <view class="header-icon">
              <up-icon name="order" color="#f093fb" size="20"></up-icon>
            </view>
            <text class="card-title">工作信息</text>
          </view>

          <view class="form-item readonly">
            <view class="item-header">
              <text class="form-label">部门</text>
            </view>
            <view class="input-wrapper disabled">
              <up-icon name="home" color="#c0c4cc" size="18"></up-icon>
              <input
                  :value="userInfo.department"
                  class="form-input"
                  placeholder="部门信息"
                  disabled
              />
              <up-icon name="lock" color="#c0c4cc" size="16"></up-icon>
            </view>
          </view>

          <view class="form-item readonly">
            <view class="item-header">
              <text class="form-label">工号</text>
            </view>
            <view class="input-wrapper disabled">
              <up-icon name="account" color="#c0c4cc" size="18"></up-icon>
              <input
                  :value="userInfo.employeeId"
                  class="form-input"
                  placeholder="工号"
                  disabled
              />
              <up-icon name="lock" color="#c0c4cc" size="16"></up-icon>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-btn secondary" @click="resetForm">
        <up-icon name="reload" color="#8e8e93" size="18"></up-icon>
        <text class="btn-text">重置</text>
      </view>
      <view class="action-btn primary" @click="saveProfile">
        <up-icon name="checkmark" color="#fff" size="18"></up-icon>
        <text class="btn-text">保存</text>
      </view>
    </view>
  </view>
</template>

<script>
import {getUserInfo, updateUserInfo} from '@/api/user'
import {getImageSignature, uploadToOss} from '@/api/oss'

export default {
  data() {
    return {
      originalUserInfo: {},
      userInfo: {
        id: '',
        name: '',
        phone: '',
        email: '',
        avatar: '',
        department: '',
        employeeId: ''
      }
    }
  },

  async onLoad() {
    this.userInfo = await getUserInfo();
    this.originalUserInfo = JSON.parse(JSON.stringify(this.userInfo));
  },

  methods: {
    goBack() {
      // 检查是否有未保存的修改
      if (this.hasChanges()) {
        uni.showModal({
          title: '提示',
          content: '您有未保存的修改，确定要离开吗？',
          success: (res) => {
            if (res.confirm) {
              uni.navigateBack();
            }
          }
        });
      } else {
        uni.navigateBack();
      }
    },

    hasChanges() {
      return JSON.stringify(this.userInfo) !== JSON.stringify(this.originalUserInfo);
    },

    changeAvatar() {
      uni.showActionSheet({
        itemList: ['拍照', '从相册选择'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拍照
            uni.chooseImage({
              count: 1,
              sizeType: ['compressed'],
              sourceType: ['camera'],
              success: (imageRes) => {
                const isH5 = typeof window !== 'undefined' && !!window.File;
                const files = isH5 ? imageRes.tempFiles : imageRes.tempFilePaths;
                if (files.length > 0) {
                  this.uploadAvatar(files[0]);
                }
              }
            });
          } else if (res.tapIndex === 1) {
            // 从相册选择
            uni.chooseImage({
              count: 1,
              sizeType: ['compressed'],
              sourceType: ['album'],
              success: (imageRes) => {
                const isH5 = typeof window !== 'undefined' && !!window.File;
                const files = isH5 ? imageRes.tempFiles : imageRes.tempFilePaths;
                if (files.length > 0) {
                  this.uploadAvatar(files[0]);
                }
              }
            });
          }
        }
      });
    },

    async uploadAvatar(filePath) {
      uni.showLoading({title: '上传中...'});
      try {
        const isH5 = typeof window !== 'undefined' && !!window.File;
        let fileObj;

        if (isH5) {
          // H5端，需要将filePath转换为File对象
          // 这里假设filePath是File对象，如果不是需要额外处理
          fileObj = filePath;
        } else {
          // 小程序端，需转为File对象
          const fileName = filePath.split('/').pop();
          // 读取为base64并转为File对象
          fileObj = await new Promise((resolve, reject) => {
            uni.getFileSystemManager().readFile({
              filePath,
              encoding: 'base64',
              success: (res) => {
                const byteCharacters = atob(res.data);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                  byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], {type: 'image/jpeg'});
                const file = new File([blob], fileName, {type: 'image/jpeg'});
                resolve(file);
              },
              fail: reject
            });
          });
        }

        // 获取OSS签名
        const signature = await getImageSignature();

        // 上传到OSS
        const {url} = await uploadToOss(fileObj, signature);

        // 更新头像URL
        this.userInfo.avatar = url;

        uni.hideLoading();
        uni.showToast({
          title: '头像上传成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('头像上传失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '头像上传失败',
          icon: 'none'
        });
      }
    },

    resetForm() {
      uni.showModal({
        title: '确认重置',
        content: '确定要重置所有修改吗？',
        success: (res) => {
          if (res.confirm) {
            this.userInfo = JSON.parse(JSON.stringify(this.originalUserInfo));
            uni.showToast({
              title: '已重置',
              icon: 'success'
            });
          }
        }
      });
    },

    validateForm() {
      if (!this.userInfo.name.trim()) {
        uni.showToast({
          title: '请输入姓名',
          icon: 'none'
        });
        return false;
      }

      if (!this.userInfo.phone.trim()) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        });
        return false;
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.userInfo.phone)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        });
        return false;
      }

      // 验证邮箱格式
      if (this.userInfo.email && this.userInfo.email.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(this.userInfo.email)) {
          uni.showToast({
            title: '邮箱格式不正确',
            icon: 'none'
          });
          return false;
        }
      }

      return true;
    },

    async saveProfile() {
      if (!this.validateForm()) {
        return;
      }
      uni.showLoading({title: '保存中...'});
      try {
        const res = await updateUserInfo(this.userInfo);
        if (res.success) {
          uni.hideLoading();
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          // 更新原始信息
          this.originalUserInfo = JSON.parse(JSON.stringify(this.userInfo));
          // 同步更新本地缓存userInfo
          console.log("this.userInfo")
          console.log(this.userInfo)
          uni.setStorageSync('userInfo', JSON.parse(JSON.stringify(this.userInfo)));
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1000);
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none'
          })
        }
      } catch (e) {
        uni.hideLoading();
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 头像区域
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.avatar-container {
  position: relative;
  margin-bottom: 12px;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 28px;
  height: 28px;
  border-radius: 14px;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
}

.avatar-tip {
  font-size: 12px;
  color: #8e8e93;
}

// 表单区域
.form-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
}

.header-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #1a1d2e;
}

.required {
  color: #f56c6c;
  font-size: 14px;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  background: #fff;
}

.input-wrapper.disabled {
  background: #f5f6fa;
  border-color: #e0e0e0;
}

.form-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #1a1d2e;
}

.form-input::placeholder {
  color: #8e8e93;
}

.form-input:disabled {
  color: #c0c4cc;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #f0f2f5;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
  z-index: 999;
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.action-btn.primary:active {
  transform: scale(0.98);
}

.action-btn.secondary {
  background: #f8faff;
  color: #8e8e93;
  border: 1px solid #e0e6ff;
}

.action-btn.secondary:active {
  background: #f0f4ff;
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
}
</style>
