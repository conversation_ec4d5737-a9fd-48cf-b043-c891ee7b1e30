# 通知已读状态功能说明

## 功能概述

本功能实现了通知公告的已读、未读状态管理，使用本地storage存储状态，无需数据库支持。

## 主要功能

### 1. 已读状态管理

- **存储方式**: 使用uni-app的本地storage API
- **存储内容**: 已读通知的ID列表
- **存储键名**: `notice_read_status`

### 2. 通知列表页面功能

- **未读数量显示**: 在页面顶部显示当前未读通知数量
- **全部标记已读**: 点击"全部已读"按钮可一键标记所有通知为已读
- **单个标记已读**: 点击通知卡片进入详情时自动标记为已读
- **状态标识**: 未读通知显示红色圆点标识

### 3. 首页集成

- **未读徽章**: 通知图标右上角显示未读数量徽章
- **实时更新**: 从通知详情页返回时自动更新未读数量
- **数量限制**: 超过99条显示为"99+"

### 4. 通知详情页面

- **自动标记**: 进入详情页面时自动标记该通知为已读
- **状态同步**: 返回列表页面时状态已更新

## 技术实现

### 核心工具函数 (`utils/storage.ts`)

```typescript
// 获取已读通知ID列表
export const getReadNoticeIds = (): number[]

// 标记单个通知为已读
export const markNoticeAsRead = (noticeId: number): void

// 批量标记通知为已读
export const markNoticesAsRead = (noticeIds: number[]): void

// 检查通知是否已读
export const isNoticeRead = (noticeId: number): boolean

// 获取未读通知数量
export const getUnreadCount = (noticeList: any[]): number

// 清除所有已读状态
export const clearReadStatus = (): void
```

### 页面集成

#### 通知列表页面 (`pages/home/<USER>

- 使用`addReadStatusToNotices()`为通知添加已读状态
- 使用`computed`属性实时计算未读数量
- 集成"全部标记已读"功能

#### 首页 (`pages/home/<USER>

- 在`fetchHomeData()`中计算未读数量
- 在`onShow()`生命周期中更新未读数量
- 显示未读数量徽章

#### 通知详情页面 (`pages/home/<USER>

- 在`onLoad()`生命周期中标记通知为已读

## 使用流程

1. **用户进入首页**: 系统显示未读通知数量徽章
2. **用户点击通知图标**: 跳转到通知列表页面
3. **查看通知列表**: 显示未读数量和"全部已读"按钮
4. **点击通知卡片**: 进入详情页面，自动标记为已读
5. **返回列表页面**: 未读数量自动更新
6. **点击全部已读**: 一键标记所有通知为已读

## 数据存储格式

```json
{
  "notice_read_status": "[1, 2, 3, 4, 5]"
}
```

存储的是已读通知ID的JSON字符串数组。

## 注意事项

1. **本地存储**: 已读状态仅存储在本地，清除应用数据会重置状态
2. **跨设备同步**: 不同设备间的已读状态不会同步
3. **性能优化**: 使用Set数据结构避免重复ID
4. **错误处理**: 所有storage操作都有try-catch错误处理
5. **内存管理**: 定期清理过期的已读状态（可选）

## 扩展功能

### 可选的增强功能

1. **已读状态同步**: 将已读状态上传到服务器
2. **过期清理**: 定期清理超过30天的已读记录
3. **批量操作**: 支持选择多个通知进行批量标记
4. **已读统计**: 显示已读通知数量统计

### 实现示例

```typescript
// 定期清理过期已读状态
export const cleanExpiredReadStatus = () => {
  const readIds = getReadNoticeIds()
  const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000
  // 这里可以添加时间戳逻辑来清理过期记录
}
``` 
