package com.cy.education.controller;

import com.cy.education.model.entity.home.Carousel;
import com.cy.education.model.entity.home.News;
import com.cy.education.model.entity.home.Notice;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.params.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.home.CarouselService;
import com.cy.education.service.home.NewsService;
import com.cy.education.service.home.NoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 内容管理接口（App端只读）
 */
@Api(tags = "内容管理接口")
@RestController
@RequestMapping("/content")
@Slf4j
public class ContentController {

    @Autowired
    private CarouselService carouselService;
    @Autowired
    private NewsService newsService;
    @Autowired
    private NoticeService noticeService;

    // =========================== 轮播图接口 ===========================

    /**
     * 分页查询轮播图列表
     */
    @ApiOperation("分页查询轮播图列表")
    @GetMapping("/carousel/list")
    public ApiResponse<PageResponse<Carousel>> listCarousels(ContentQueryParam param) {
        try {
            PageResponse<Carousel> pageResponse = carouselService.listCarousels(param);
            return ApiResponse.success(pageResponse);
        } catch (Exception e) {
            log.error("查询轮播图列表失败", e);
            return ApiResponse.error("查询轮播图列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取轮播图详情
     */
    @ApiOperation("获取轮播图详情")
    @GetMapping("/carousel/{id}")
    public ApiResponse<Carousel> getCarouselById(@PathVariable Integer id) {
        try {
            Carousel carousel = carouselService.getCarouselById(id);
            return ApiResponse.success(carousel);
        } catch (Exception e) {
            log.error("获取轮播图详情失败", e);
            return ApiResponse.error("获取轮播图详情失败: " + e.getMessage());
        }
    }

    // =========================== 新闻接口 ===========================

    /**
     * 分页查询新闻列表
     */
    @ApiOperation("分页查询新闻列表")
    @GetMapping("/news/list")
    public ApiResponse<PageResponse<News>> listNews(ContentQueryParam param) {
        try {
            PageResponse<News> pageResponse = newsService.listNews(param);
            return ApiResponse.success(pageResponse);
        } catch (Exception e) {
            log.error("查询新闻列表失败", e);
            return ApiResponse.error("查询新闻列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取新闻详情
     */
    @ApiOperation("获取新闻详情")
    @GetMapping("/news/{id}")
    public ApiResponse<News> getNewsById(@PathVariable Integer id) {
        try {
            News news = newsService.getNewsById(id);
            newsService.incrementViewCount(id);
            return ApiResponse.success(news);
        } catch (Exception e) {
            log.error("获取新闻详情失败", e);
            return ApiResponse.error("获取新闻详情失败: " + e.getMessage());
        }
    }

    // =========================== 公告接口 ===========================

    /**
     * 分页查询公告列表
     */
    @ApiOperation("分页查询公告列表")
    @GetMapping("/notice/list")
    public ApiResponse<PageResponse<Notice>> listNotices(ContentQueryParam param) {
        try {
            PageResponse<Notice> pageResponse = noticeService.listNotices(param);
            return ApiResponse.success(pageResponse);
        } catch (Exception e) {
            log.error("查询公告列表失败", e);
            return ApiResponse.error("查询公告列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取公告详情
     */
    @ApiOperation("获取公告详情")
    @GetMapping("/notice/{id}")
    public ApiResponse<Notice> getNoticeById(@PathVariable Integer id) {
        try {
            Notice notice = noticeService.getNoticeById(id);
            return ApiResponse.success(notice);
        } catch (Exception e) {
            log.error("获取公告详情失败", e);
            return ApiResponse.error("获取公告详情失败: " + e.getMessage());
        }
    }
}
