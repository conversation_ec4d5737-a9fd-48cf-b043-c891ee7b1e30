<template>
	<view class="forum-post">
		<view class="form-section">
			<!-- 分类选择 -->
			<view class="form-item">
				<text class="form-label">分类</text>
				<picker :value="selectedCategoryIndex" :range="categoryOptions" @change="onCategoryChange">
					<view class="picker-value">{{ categoryOptions[selectedCategoryIndex] }}</view>
				</picker>
			</view>

			<!-- 标题输入 -->
			<view class="form-item">
				<text class="form-label">标题</text>
				<input 
					v-model="postData.title" 
					class="form-input" 
					placeholder="请输入帖子标题"
					maxlength="50"
				/>
			</view>

			<!-- 内容输入 -->
			<view class="form-item content-item">
				<text class="form-label">内容</text>
				<textarea 
					v-model="postData.content" 
					class="form-textarea" 
					placeholder="请输入帖子内容..."
					maxlength="2000"
				/>
				<text class="word-count">{{ postData.content.length }}/2000</text>
			</view>

			<!-- 图片上传 -->
			<view class="form-item">
				<text class="form-label">图片</text>
				<view class="image-upload">
					<view v-for="(image, index) in postData.images" :key="index" class="image-item">
						<image :src="image" class="uploaded-image" mode="aspectFill" />
						<view class="image-delete" @tap="removeImage(index)">×</view>
					</view>
					<view v-if="postData.images.length < 9" class="add-image" @tap="addImage">
						<text class="add-icon">+</text>
						<text class="add-text">添加图片</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 发布按钮 -->
		<view class="action-buttons">
			<button class="btn btn-secondary" @tap="saveDraft">保存草稿</button>
			<button class="btn btn-primary" @tap="publishPost">发布</button>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

interface PostData {
	title: string
	content: string
	category: string
	images: string[]
}

// 表单数据
const postData = reactive<PostData>({
	title: '',
	content: '',
	category: 'study',
	images: []
})

// 分类选项
const categoryOptions = ['学习交流', '问题求助', '经验分享', '讨论建议']
const categoryValues = ['study', 'help', 'share', 'discuss']
const selectedCategoryIndex = ref(0)

onMounted(() => {
	console.log('Forum post page loaded')
})

// 分类选择
const onCategoryChange = (e: any) => {
	selectedCategoryIndex.value = e.detail.value
	postData.category = categoryValues[e.detail.value]
}

// 添加图片
const addImage = () => {
	uni.chooseImage({
		count: 9 - postData.images.length,
		success: (res) => {
			const paths = Array.isArray(res.tempFilePaths) ? res.tempFilePaths : [res.tempFilePaths]
			paths.forEach(path => postData.images.push(path))
		}
	})
}

// 移除图片
const removeImage = (index: number) => {
	postData.images.splice(index, 1)
}

// 保存草稿
const saveDraft = () => {
	if (!postData.title.trim()) {
		uni.showToast({
			title: '请输入标题',
			icon: 'none'
		})
		return
	}

	// TODO: 保存草稿到本地或服务器
	uni.setStorageSync('forum_draft', postData)
	uni.showToast({
		title: '草稿已保存',
		icon: 'success'
	})
}

// 发布帖子
const publishPost = () => {
	if (!postData.title.trim()) {
		uni.showToast({
			title: '请输入标题',
			icon: 'none'
		})
		return
	}

	if (!postData.content.trim()) {
		uni.showToast({
			title: '请输入内容',
			icon: 'none'
		})
		return
	}

	uni.showLoading({ title: '发布中...' })

	// TODO: 调用API发布帖子
	setTimeout(() => {
		uni.hideLoading()
		uni.showToast({
			title: '发布成功',
			icon: 'success'
		})
		
		// 清除草稿
		uni.removeStorageSync('forum_draft')
		
		// 返回论坛页面
		setTimeout(() => {
			uni.switchTab({
				url: '/pages/forum/index'
			})
		}, 1000)
	}, 1000)
}
</script>

<style lang="scss" scoped>
.forum-post {
	background-color: var(--bg-secondary);
	min-height: 100vh;
	padding: var(--spacing-md);
}

.form-section {
	background: var(--bg-primary);
	border-radius: var(--radius-lg);
	overflow: hidden;
	margin-bottom: var(--spacing-lg);
}

.form-item {
	padding: var(--spacing-md);
	border-bottom: 1px solid var(--border-color);
	
	&:last-child {
		border-bottom: none;
	}
	
	&.content-item {
		flex-direction: column;
		align-items: stretch;
	}
}

.form-label {
	font-size: 28rpx;
	color: var(--text-primary);
	margin-bottom: var(--spacing-sm);
}

.picker-value {
	color: var(--text-primary);
	font-size: 28rpx;
}

.form-input {
	height: 60rpx;
	font-size: 28rpx;
	color: var(--text-primary);
}

.form-textarea {
	min-height: 300rpx;
	font-size: 28rpx;
	color: var(--text-primary);
	line-height: 1.5;
}

.word-count {
	align-self: flex-end;
	font-size: 24rpx;
	color: var(--text-tertiary);
	margin-top: var(--spacing-xs);
}

.image-upload {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: var(--spacing-sm);
}

.image-item {
	position: relative;
	aspect-ratio: 1;
}

.uploaded-image {
	width: 100%;
	height: 100%;
	border-radius: var(--radius-md);
}

.image-delete {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 40rpx;
	height: 40rpx;
	background: #ff4d4f;
	color: #FFFFFF;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}

.add-image {
	aspect-ratio: 1;
	border: 2rpx dashed var(--border-color);
	border-radius: var(--radius-md);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.add-icon {
	font-size: 48rpx;
	color: var(--text-tertiary);
	margin-bottom: 8rpx;
}

.add-text {
	font-size: 24rpx;
	color: var(--text-tertiary);
}

.action-buttons {
	display: flex;
	gap: var(--spacing-md);
}

.btn {
	flex: 1;
	height: 88rpx;
	border-radius: var(--radius-lg);
	font-size: 32rpx;
	font-weight: 600;
	border: none;
	
	&.btn-secondary {
		background: var(--bg-primary);
		color: var(--text-primary);
		border: 2rpx solid var(--border-color);
	}
	
	&.btn-primary {
		background: var(--primary-color);
		color: #FFFFFF;
	}
}
</style> 