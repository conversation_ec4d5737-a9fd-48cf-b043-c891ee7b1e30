<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.cy_app_server.mapper.PracticeMapper">

    <select id="getAvailableBanks" resultType="java.util.Map">
        SELECT id, name, description FROM question_banks WHERE status = 1
    </select>

    <select id="getUserPracticeStats" resultType="java.util.Map">
        SELECT
            ps.bank_id as bankId,
            ps.total_questions as totalQuestions,
            ps.answered_questions as answeredQuestions,
            ps.correct_count as correctCount,
            ps.wrong_count as wrongCount,
            ps.accuracy_rate as accuracy
        FROM practice_stats ps
        WHERE ps.user_id = #{userId}
    </select>

    <select id="getWrongQuestions" resultType="java.util.Map">
        SELECT DISTINCT q.id, q.title as questionContent, q.type, q.options, q.correct_answer as correctAnswer, q.explanation
        FROM exam_question q
        JOIN practice_answer pa ON q.id = pa.question_id
        JOIN practice_record pr ON pa.record_id = pr.id
        WHERE pr.user_id = #{userId}
          AND pa.is_correct = 0
          <if test="bankId != null">
            AND q.bank_id = #{bankId}
          </if>
    </select>

    <select id="getWrongQuestionsCount" resultType="java.lang.Integer">
        SELECT COUNT(q.id)
        FROM questions q
        JOIN user_question_answers uqa ON q.id = uqa.question_id
        WHERE uqa.user_id = #{userId} AND uqa.is_correct = 0
        <if test="bankId != null">
            AND q.bank_id = #{bankId}
        </if>
    </select>

    <select id="getBankQuestionsCount" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM questions
        <where>
            <if test="bankId != null">
                bank_id = #{bankId}
            </if>
        </where>
    </select>

    <insert id="createPracticeRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO practice_record (user_id, bank_id, type, total_questions, status, start_time)
        VALUES (#{userId}, #{bankId}, #{type}, #{totalQuestions}, #{status}, #{startTime})
    </insert>

    <select id="getPracticeStats" resultType="com.example.cy_app_server.model.PracticeStats">
        SELECT * FROM practice_stats WHERE user_id = #{userId} AND bank_id = #{bankId}
    </select>

    <insert id="createPracticeStats">
        INSERT INTO practice_stats (user_id, bank_id, total_questions, answered_questions, correct_count, wrong_count, accuracy_rate, last_practice_time)
        VALUES (#{userId}, #{bankId}, #{totalQuestions}, #{answeredQuestions}, #{correctCount}, #{wrongCount}, #{accuracyRate}, #{lastPracticeTime})
    </insert>

    <update id="updatePracticeStats">
        UPDATE practice_stats
        SET
            total_questions = #{totalQuestions},
            answered_questions = #{answeredQuestions},
            correct_count = #{correctCount},
            wrong_count = #{wrongCount},
            accuracy_rate = #{accuracyRate},
            last_practice_time = #{lastPracticeTime}
        WHERE id = #{id}
    </update>

    <insert id="createPracticeAnswer">
        INSERT INTO practice_answer (record_id, question_id, user_answer, is_correct)
        SELECT #{recordId}, #{questionId}, #{userAnswer}, CASE WHEN q.correct_answer = #{userAnswer} THEN 1 ELSE 0 END
        FROM exam_question q
        WHERE q.id = #{questionId}
    </insert>

    <select id="getPracticeRecordById" resultType="com.example.cy_app_server.model.PracticeRecord">
        SELECT * FROM practice_record WHERE id = #{recordId}
    </select>

    <select id="getUserAnswersForBank" resultType="java.util.Map">
        SELECT pa.is_correct
        FROM practice_answer pa
        JOIN exam_question q ON pa.question_id = q.id
        WHERE pa.user_id = #{userId} AND q.bank_id = #{bankId}
    </select>
    
    <select id="getPracticeRecord" resultType="java.util.Map">
        SELECT * FROM practice_records WHERE id = #{recordId}
    </select>
    
    <select id="getPracticeAnswers" resultType="java.util.Map">
        SELECT 
            pra.user_answer, 
            pra.is_correct as correct, 
            q.content as questionContent, 
            q.answer as correctAnswer, 
            q.options, 
            q.type, 
            q.explanation
        FROM practice_record_answers pra
        JOIN questions q ON pra.question_id = q.id
        WHERE pra.record_id = #{recordId}
    </select>

</mapper>
