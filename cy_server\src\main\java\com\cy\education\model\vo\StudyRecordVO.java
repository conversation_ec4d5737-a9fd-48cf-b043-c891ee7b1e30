package com.cy.education.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 学习记录VO
 */
@Data
public class StudyRecordVO {
    
    /**
     * 记录ID
     */
    private Integer id;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 用户名称（非数据库字段）
     */
    private String userName;

    /**
     * 学员姓名（非数据库字段）
     */
    private String studentName;

    /**
     * 部门名称（非数据库字段）
     */
    private String departmentName;
    
    /**
     * 课程ID
     */
    private Integer courseId;
    
    /**
     * 课程名称（非数据库字段）
     */
    private String courseName;
    
    /**
     * 课时ID
     */
    private Integer lessonId;
    
    /**
     * 课时名称（非数据库字段）
     */
    private String lessonName;
    
    /**
     * 资源ID
     */
    private Integer resourceId;
    
    /**
     * 资源类型：video, file, article
     */
    private String resourceType;
    
    /**
     * 资源名称（非数据库字段）
     */
    private String resourceName;
    
    /**
     * 学习进度（百分比）
     */
    private Integer progress;
    
    /**
     * 学习时长（秒）
     */
    private Integer duration;
    
    /**
     * 是否完成：0未完成，1已完成
     */
    private Integer completed;
    
    /**
     * 最后学习位置（秒）
     */
    private Integer lastPosition;
    
    /**
     * 最后学习时间
     */
    private LocalDateTime lastStudyTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 