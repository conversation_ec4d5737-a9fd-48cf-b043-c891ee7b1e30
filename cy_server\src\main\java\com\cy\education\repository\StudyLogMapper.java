package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.StudyLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 学习日志Mapper接口
 */
@Mapper
public interface StudyLogMapper extends BaseMapper<StudyLog> {
    
    /**
     * 分页查询学习日志，包含学员姓名、课程名称等信息
     * @param page 分页参数
     * @param studentId 学员ID
     * @param courseId 课程ID
     * @param lessonId 课时ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT l.*, s.name as student_name, c.name as course_name " +
            "FROM study_logs l " +
            "LEFT JOIN students s ON l.user_id = s.id " +
            "LEFT JOIN courses c ON l.course_id = c.id " +
            "<where> " +
            "  <if test='studentId != null'> " +
            "    AND l.user_id = #{studentId} " +
            "  </if> " +
            "  <if test='courseId != null'> " +
            "    AND l.course_id = #{courseId} " +
            "  </if> " +
            "  <if test='lessonId != null'> " +
            "    AND l.lesson_id = #{lessonId} " +
            "  </if> " +
            "  <if test='startTime != null'> " +
            "    AND l.created_at &gt;= #{startTime} " +
            "  </if> " +
            "  <if test='endTime != null'> " +
            "    AND l.created_at &lt;= #{endTime} " +
            "  </if> " +
            "</where> " +
            "ORDER BY l.created_at DESC" +
            "</script>")
    IPage<StudyLog> selectPageWithDetails(Page<StudyLog> page, 
                                        @Param("studentId") Integer studentId,
                                        @Param("courseId") Integer courseId,
                                        @Param("lessonId") Integer lessonId,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取学员的学习时间趋势
     * @param studentId 学员ID
     * @param days 天数
     * @return 学习时间趋势数据
     */
    @Select("SELECT DATE(study_time) as date, SUM(duration) as duration " +
            "FROM study_logs " +
            "WHERE user_id = #{studentId} " +
            "AND study_time >= DATE_SUB(CURRENT_DATE(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE(study_time) " +
            "ORDER BY date")
    List<Map<String, Object>> getStudyTimeTrend(@Param("studentId") Integer studentId, @Param("days") Integer days);

    /**
     * 获取课程的学习活动记录
     * @param courseId 课程ID
     * @param limit 限制数量
     * @return 学习活动记录
     */
    @Select("SELECT l.*, s.name as student_name " +
            "FROM study_logs l " +
            "JOIN students s ON l.user_id = s.id " +
            "WHERE l.course_id = #{courseId} " +
            "ORDER BY l.created_at DESC " +
            "LIMIT #{limit}")
    List<StudyLog> getCourseActivities(@Param("courseId") Integer courseId, @Param("limit") Integer limit);

    /**
     * 查询用户的学习日志
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 学习日志列表
     */
    @Select("<script>" +
            "SELECT sl.*, c.name as course_name " +
            "FROM study_logs sl " +
            "LEFT JOIN courses c ON sl.course_id = c.id " +
            "WHERE sl.user_id = #{userId} " +
            "<if test='startTime != null'> " +
            "  AND sl.study_time &gt;= #{startTime} " +
            "</if> " +
            "<if test='endTime != null'> " +
            "  AND sl.study_time &lt;= #{endTime} " +
            "</if> " +
            "ORDER BY sl.study_time DESC" +
            "</script>")
    List<Map<String, Object>> selectUserStudyLogs(@Param("userId") Integer userId,
                                                 @Param("startTime") String startTime,
                                                 @Param("endTime") String endTime);
    
    /**
     * 统计用户每日学习时长
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 每日学习时长
     */
    @Select("<script>" +
            "SELECT " +
            "  DATE_FORMAT(sl.study_time, '%Y-%m-%d') as study_date, " +
            "  SUM(sl.duration) as total_duration " +
            "FROM study_logs sl " +
            "WHERE sl.user_id = #{userId} " +
            "<if test='startDate != null'> " +
            "  AND DATE(sl.study_time) &gt;= #{startDate} " +
            "</if> " +
            "<if test='endDate != null'> " +
            "  AND DATE(sl.study_time) &lt;= #{endDate} " +
            "</if> " +
            "GROUP BY DATE_FORMAT(sl.study_time, '%Y-%m-%d') " +
            "ORDER BY study_date" +
            "</script>")
    List<Map<String, Object>> statisticsDailyStudyDuration(@Param("userId") Integer userId, 
                                                          @Param("startDate") String startDate, 
                                                          @Param("endDate") String endDate);
    
    /**
     * 统计用户课程学习时长
     * @param userId 用户ID
     * @return 课程学习时长
     */
    @Select("SELECT " +
            "  c.id as course_id, " +
            "  c.name as course_name, " +
            "  SUM(sl.duration) as total_duration " +
            "FROM study_logs sl " +
            "LEFT JOIN courses c ON sl.course_id = c.id " +
            "WHERE sl.user_id = #{userId} " +
            "GROUP BY c.id, c.name " +
            "ORDER BY total_duration DESC")
    List<Map<String, Object>> statisticsCourseStudyDuration(@Param("userId") Integer userId);
} 