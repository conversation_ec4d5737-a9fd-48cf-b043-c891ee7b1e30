// 通知公告列表页样式

// 操作栏样式
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.unread-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unread-count {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

.mark-all-read {
  padding: 6px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

.mark-all-text {
  font-size: 12px;
  color: #fff;
  font-weight: 500;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notice-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 10px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  &.unread {
    border-left: 4px solid #667eea;
    background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  }

  &.important {
    border-left: 4px solid #fa709a;
    background: linear-gradient(135deg, #fff8fb 0%, #ffffff 100%);
  }
}

.notice-icon {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.unread-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: #f56c6c;
  border-radius: 50%;
  border: 2px solid #fff;
}

.notice-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.notice-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
  line-height: 1.3;
}

.notice-tags {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.tag {
  padding: 4px 8px;
  border-radius: 8px;

  &.level-1 {
    background: #acc4dc; // 蓝色 一般
  }
  &.level-2 {
    background: #e8bea4; // 橙色 重要
  }
  &.level-3 {
    background: #e6b5b5; // 红色 紧急
  }

  &.unread {
    background: rgba(102, 126, 234, 0.1);
  }
}

.tag-text {
  font-size: 10px;
  font-weight: 500;

  .tag.important & {
    color: #fa709a;
  }

  .tag.unread & {
    color: #667eea;
  }
}

.notice-summary {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notice-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.notice-time {
  font-size: 12px;
  color: #8e8e93;
}

.notice-author {
  font-size: 12px;
  color: #8e8e93;
}

.notice-arrow {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  margin-top: 12px;
}