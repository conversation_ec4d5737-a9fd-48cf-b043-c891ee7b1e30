package com.cy.education.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.ForumViolation;
import com.cy.education.model.vo.ForumViolationQueryParam;

import java.util.List;

/**
 * 论坛违规举报服务接口
 */
public interface ForumViolationService {
    
    /**
     * 获取违规举报列表（分页）
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<ForumViolation> getViolationPage(ForumViolationQueryParam param);
    
    /**
     * 获取违规举报详情
     * 
     * @param id 违规举报ID
     * @return 违规举报信息
     */
    ForumViolation getViolationById(Integer id);
    
    /**
     * 处理违规举报
     * 
     * @param id 违规举报ID
     * @param status 状态
     * @param result 处理结果
     * @param operatorId 处理人ID
     * @return 是否成功
     */
    boolean processViolation(Integer id, Integer status, String result, Integer operatorId);
    
    /**
     * 批量处理违规举报
     * 
     * @param ids 违规举报ID列表
     * @param status 状态
     * @param result 处理结果
     * @param operatorId 处理人ID
     * @return 是否成功
     */
    boolean batchProcessViolations(List<Integer> ids, Integer status, String result, Integer operatorId);
} 