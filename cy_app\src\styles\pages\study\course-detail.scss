// 课程封面
.course-header {
  position: relative;
  margin-top: -56px;
  z-index: 1;
}

.cover-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.play-btn {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.play-btn:active {
  transform: scale(0.95);
}

.course-info-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px;
  color: white;
}

.course-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  line-height: 1.3;
  margin-bottom: 8px;
}

.course-instructor {
  font-size: 14px;
  color: #f2f2f2;
  margin-bottom: 12px;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #43e97b;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #f2f2f2;
  white-space: nowrap;
}

// 标签栏
.tabs-section {
  background: #fff;
  border-bottom: 1px solid #f0f2f5;
}

.tab-container {
  display: flex;
  padding: 0 20px;
}

.tab-item {
  flex: 1;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #667eea;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background: #667eea;
  border-radius: 2px;
}

.tab-text {
  font-size: 16px;
  font-weight: 500;
  color: #8e8e93;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #667eea;
  font-weight: 600;
}

// 内容区域
.content-container {
  padding: 20px;
  padding-bottom: 100px;
}

.info-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.header-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.description-text {
  font-size: 14px;
  line-height: 1.6;
  color: #4a5568;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  padding: 6px 12px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  border-radius: 16px;
}

.tag-text {
  font-size: 12px;
  color: #667eea;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  text-align: center;
  padding: 16px;
  background: #f8faff;
  border-radius: 12px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8e8e93;
}

// 课程目录
.chapter-card {
  background: #fff;
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
}

.chapter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: #f8faff;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
}

.chapter-header:active {
  background: #f0f4ff;
}

.chapter-info {
  flex: 1;
}

.chapter-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 4px;
}

.chapter-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #8e8e93;
}

.lesson-count {
  color: #8e8e93;
}

.chapter-progress {
  color: #667eea;
  font-weight: 500;
}

.chapter-toggle {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.lessons-list {
  padding: 0;
}

.lesson-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
}

.lesson-item:last-child {
  border-bottom: none;
}

.lesson-item:active {
  background: #f8faff;
}

.lesson-item.completed {
  background: rgba(67, 233, 123, 0.05);
}

.lesson-item.current {
  background: rgba(102, 126, 234, 0.05);
  border-left: 4px solid #667eea;
}

.lesson-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.lesson-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.lesson-title {
  font-size: 14px;
  color: #1a1d2e;
  font-weight: 500;
  line-height: 1.3;
}

.lesson-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #8e8e93;
}

.lesson-duration {
  color: #8e8e93;
}

.lesson-progress {
  color: #667eea;
  font-weight: 500;
}

.lesson-progress-bar {
  height: 2px;
  background: #f0f2f5;
  border-radius: 1px;
  overflow: hidden;
  margin-top: 4px;
}

.lesson-progress-fill {
  height: 100%;
  background: #667eea;
  border-radius: 1px;
  transition: width 0.3s ease;
}

.lesson-status {
  flex-shrink: 0;
}

.status-badge {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-badge.completed {
  background: rgba(67, 233, 123, 0.1);
}

.status-badge.current {
  background: rgba(102, 126, 234, 0.1);
}

.status-badge.in-progress {
  background: rgba(255, 149, 0, 0.1);
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #f0f2f5;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
  z-index: 999;
}

.action-btn {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.action-btn.primary:active {
  transform: scale(0.98);
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
}
