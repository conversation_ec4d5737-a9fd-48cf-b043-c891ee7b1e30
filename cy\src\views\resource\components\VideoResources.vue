<template>
  <div>
    <!-- Search and Action Bar -->
    <div class="filter-container">
      <el-input v-model="query.name" placeholder="视频名称" style="width: 200px;" class="filter-item" @keyup.enter="handleFilter" />

      <!-- 标签筛选 -->
      <el-select
        v-model="selectedTags"
        multiple
        placeholder="选择标签筛选"
        style="width: 200px; margin-left: 10px;"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option
          v-for="tag in availableTags"
          :key="tag"
          :label="tag"
          :value="tag"
        />
      </el-select>

      <el-button class="filter-item" type="primary" :icon="Search" @click="handleFilter">搜索</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" :icon="Plus" @click="handleCreate">新增视频</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" :icon="Upload" @click="handleShowBatchUpload">批量新增</el-button>
    </div>

    <!-- Table -->
    <el-table :data="list" v-loading="loading" border fit highlight-current-row style="width: 100%;">
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="视频预览" align="center" width="150">
          <template #default="{row}">
            <video :src="row.content" style="width: 120px; height: 67px; object-fit: cover;" controls></video>
          </template>
      </el-table-column>
      <el-table-column label="资源名称" prop="name" />
      <el-table-column label="标签" prop="tags" width="200">
        <template #default="{row}">
          <el-tag
            v-for="tag in getResourceTags(row.tags)"
            :key="tag"
            size="small"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" align="center" width="160" />
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template #default="{row}">
          <el-button type="primary" size="small" :icon="Edit" @click="handleUpdate(row)">编辑</el-button>
          <el-button size="small" type="danger" :icon="Delete" @click="handleDelete(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- Pagination and Dialogs are the same as FileResources.vue -->
    <el-pagination v-show="total>0" :total="total" v-model:current-page="query.page" v-model:page-size="query.size" @size-change="getList" @current-change="getList" />

    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px">
      <el-form :model="tempResource" label-width="120px" :rules="formRules" ref="formRef">
        <el-form-item label="视频名称" prop="name">
          <el-input v-model="tempResource.name" placeholder="请输入视频名称" />
        </el-form-item>
        <el-form-item label="视频上传" prop="content">
          <video-upload
            v-model="tempResource.content"
            :max-size="500"
          />
        </el-form-item>
        <el-form-item label="标签">
          <tag-input
            v-model="tempResourceTags"
            :suggestions="commonTags"
            :max-tags="10"
          />
          <div class="form-tip">添加标签便于分类和搜索</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Batch Upload Dialog -->
    <batch-upload-dialog
      :visible="batchUploadDialogVisible"
      @update:visible="batchUploadDialogVisible = $event"
      resource-type="video"
      @upload-success="handleUploadSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Edit, Plus, Upload, Delete } from '@element-plus/icons-vue';
import { getResourceList, deleteResource, createResource, updateResource, Resource, ResourceQuery } from '@/api/resource';
import BatchUploadDialog from './BatchUploadDialog.vue'
import VideoUpload from '@/components/Upload/VideoUpload.vue'
import TagInput from '@/components/TagInput/index.vue';

const list = ref<Resource[]>([])
const total = ref(0)
const loading = ref(true)
const submitting = ref(false)
const formRef = ref()

const query = reactive<ResourceQuery>({
  page: 1,
  size: 10,
  type: 'video',
  name: undefined
})

// 标签相关
const selectedTags = ref<string[]>([])
const availableTags = ref<string[]>([])
const commonTags = ref<string[]>(['教学视频', '课程录像', '演示', '讲座', '培训', '实操', '案例', '直播'])

const dialogVisible = ref(false)
const dialogStatus = ref<'create' | 'update'>('create')
const dialogTitle = computed(() => dialogStatus.value === 'create' ? '新增视频资源' : '编辑视频资源')

const tempResource = ref<Resource>({
  name: '',
  type: 'video',
  content: '',
  tags: ''
})
const tempResourceTags = ref<string[]>([])

const batchUploadDialogVisible = ref(false)

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入视频名称', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请上传视频', trigger: 'change' }
  ]
}

const getList = async () => {
  loading.value = true
  try {
    // 添加标签筛选到查询参数
    const queryParams = {
      ...query,
      tags: selectedTags.value.length > 0 ? selectedTags.value.join(',') : undefined
    }

    const response: any = await getResourceList(queryParams)
    list.value = response.list
    total.value = response.total

    // 收集可用标签
    collectAvailableTags()
  } catch (error) {
    console.error('Failed to fetch resources:', error)
    ElMessage.error('获取资源列表失败')
  } finally {
    loading.value = false
  }
}

const handleFilter = () => {
  query.page = 1
  getList()
}

const handleCreate = () => {
  resetTemp()
  dialogStatus.value = 'create'
  dialogVisible.value = true
}

const handleShowBatchUpload = () => {
  batchUploadDialogVisible.value = true
}

// 解析资源标签
const getResourceTags = (tags: string): string[] => {
  if (!tags) return []
  try {
    return JSON.parse(tags)
  } catch {
    return []
  }
}

// 收集所有可用标签
const collectAvailableTags = () => {
  const tagSet = new Set<string>()
  list.value.forEach(resource => {
    const tags = getResourceTags(resource.tags)
    tags.forEach(tag => tagSet.add(tag))
  })
  availableTags.value = Array.from(tagSet)
}

const resetTemp = () => {
  tempResource.value = {
    name: '',
    type: 'video',
    content: '',
    tags: ''
  }
  tempResourceTags.value = []
}

const handleUpdate = (row: Resource) => {
  tempResource.value = { ...row }
  tempResourceTags.value = getResourceTags(row.tags)
  dialogStatus.value = 'update'
  dialogVisible.value = true
}

// 新的统一提交方法
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 将标签数组转换为JSON字符串
    const resourceData = {
      ...tempResource.value,
      tags: JSON.stringify(tempResourceTags.value)
    }

    if (dialogStatus.value === 'create') {
      await createResource(resourceData)
      ElMessage.success('创建成功')
    } else {
      await updateResource(resourceData.id, resourceData)
      ElMessage.success('更新成功')
    }

    dialogVisible.value = false
    getList()
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error(dialogStatus.value === 'create' ? '创建失败' : '更新失败')
    }
  } finally {
    submitting.value = false
  }
}

const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个资源吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    await deleteResource(id);
    ElMessage.success('删除成功');
    getList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleUploadSuccess = () => {
  getList();
};

onMounted(() => {
  getList()
})
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>