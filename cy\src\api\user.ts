import { get, post } from '@/utils/request'

/**
 * 用户接口数据类型
 */
export interface UserInfo {
  id: string
  username: string
  name: string
  avatar: string
  permissions?: string[]
}

export interface LoginParams {
  username: string
  password: string
  remember?: boolean
  captchaId?: string
  captcha?: string
}

export interface ResetPasswordParams {
  username: string
  phone: string
  code: string
  newPassword: string
  confirmPassword: string
}

/**
 * 用户登录
 * @param data 登录参数
 */
export function login(data: LoginParams) {
  console.log('发送登录请求，参数：', {
    username: data.username,
    password: '******', // 不显示实际密码
    remember: data.remember
  })
  return post<{ token: string; user: UserInfo }>('/user/login', data)
    .then(response => {
      console.log('登录请求成功响应:', response)
      return response
    })
    .catch(error => {
      console.error('登录请求失败:', error)
      throw error
    })
}

/**
 * 用户登出
 */
export function logout() {
  return post<{ success: boolean }>('/user/logout')
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return get<UserInfo>('/user/info')
}

/**
 * 获取验证码
 * @param phone 手机号
 */
export function getVerificationCode(phone: string) {
  return get<{ success: boolean }>('/user/code', { phone })
}

/**
 * 重置密码
 * @param data 重置密码参数
 */
export function resetPassword(data: ResetPasswordParams) {
  return post<{ success: boolean }>('/user/reset-password', data)
}

/**
 * 更新用户信息
 * @param data 用户信息
 */
export function updateUserInfo(data: Partial<UserInfo>) {
  return post<{ success: boolean }>('/user/update', data)
}

/**
 * 更改密码
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 */
export function changePassword(oldPassword: string, newPassword: string) {
  return post<{ success: boolean }>('/user/change-password', { oldPassword, newPassword })
}

/**
 * 上传头像
 * @param formData 头像文件数据
 */
export function uploadAvatar(formData: FormData) {
  return post<{ avatarUrl: string }>('/user/upload-avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
