package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.PracticeRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 练习记录Mapper接口
 */
@Mapper
public interface PracticeRecordMapper extends BaseMapper<PracticeRecord> {
    
    /**
     * 根据用户ID和状态查询练习记录
     * @param userId 用户ID
     * @param status 状态
     * @return 练习记录列表
     */
    List<PracticeRecord> selectByUserIdAndStatus(@Param("userId") Integer userId, @Param("status") String status);
    
    /**
     * 根据用户ID和题库ID查询练习记录
     * @param userId 用户ID
     * @param bankId 题库ID
     * @return 练习记录列表
     */
    List<PracticeRecord> selectByUserIdAndBankId(@Param("userId") Integer userId, @Param("bankId") Integer bankId);
    
    /**
     * 根据用户ID和类型查询练习记录
     * @param userId 用户ID
     * @param type 类型
     * @return 练习记录列表
     */
    List<PracticeRecord> selectByUserIdAndType(@Param("userId") Integer userId, @Param("type") String type);
} 