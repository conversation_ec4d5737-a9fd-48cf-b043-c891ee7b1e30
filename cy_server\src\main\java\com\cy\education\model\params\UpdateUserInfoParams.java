package com.cy.education.model.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;

/**
 * 更新用户信息参数
 */
@ApiModel("更新用户信息参数")
public class UpdateUserInfoParams {
    
    @ApiModelProperty("姓名")
    @Size(min = 2, max = 20, message = "姓名长度在2到20个字符")
    private String name;
    
    @ApiModelProperty("头像URL")
    private String avatar;
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
} 