package com.cy.education.model.params;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 学习记录查询参数
 */
@Data
public class StudyRecordQueryParams extends PageParams {
    
    /**
     * 学员ID
     */
    private Integer studentId;
    
    /**
     * 课程ID
     */
    private Integer courseId;
    
    /**
     * 课时ID
     */
    private Integer lessonId;
    
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 学习状态：not_started, in_progress, completed
     */
    private String status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向：asc, desc
     */
    private String sortOrder;

    /**
     * 完成状态：0未完成，1已完成
     */
    private Integer completed;

    /**
     * 关键词搜索（学员姓名）
     */
    private String keyword;
} 