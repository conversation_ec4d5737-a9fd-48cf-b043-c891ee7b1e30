package com.cy.education.controller;

import com.cy.education.model.params.*;
import com.cy.education.model.vo.*;
import com.cy.education.service.exam.ExamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 试题考试相关控制器
 */
@Api(tags = "试题考试相关接口")
@RestController
@RequestMapping("/exam")
@RequiredArgsConstructor
public class ExamController {

    private final ExamService examService;

    /**
     * 获取考试列表（不包含试卷题目/答案，包含当前用户作答记录）
     */
    @ApiOperation("获取考试列表（含用户作答记录）")
    @GetMapping("/list")
    public ApiResponse<PageResponse<ExamVO>> listExams(ExamQueryParams params) {
        return ApiResponse.success(examService.listExams(params));
    }

    /**
     * 获取考试详情（不包含试卷正确答案，包含当前用户作答记录）
     */
    @ApiOperation("获取考试详情（不含答案，含用户作答记录）")
    @GetMapping("/detail/{id}")
    public ApiResponse<ExamVO> getExamDetail(@PathVariable Integer id) {
        return ApiResponse.success(examService.getExamDetail(id));
    }

    /**
     * 获取考试完整详情（包含所有信息，包括题目、正确答案、用户作答记录），用于考试作答和判题
     */
    @ApiOperation("获取考试完整详情（含答案，含用户作答记录）")
    @GetMapping("/full-detail/{id}")
    public ApiResponse<ExamVO> getExamFullDetail(@PathVariable Integer id) {
        return ApiResponse.success(examService.getExamFullDetail(id));
    }

    /**
     * 更新考试状态
     */
    @ApiOperation("更新考试状态")
    @PostMapping("/update-status/{id}")
    public ApiResponse<Void> updateExamStatus(
            @PathVariable Integer id,
            @RequestBody Map<String, Integer> params) {
        Integer status = params.get("status");
        examService.updateExamStatus(id, status);
        String message = "考试状态更新成功";
        return ApiResponse.success(null, message);
    }

}
