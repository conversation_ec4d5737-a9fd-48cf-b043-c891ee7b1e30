-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- 主机： 8.130.11.4
-- 生成日期： 2025-07-13 02:01:34
-- 服务器版本： 8.4.3
-- PHP 版本： 8.2.28

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `test`
--

-- --------------------------------------------------------

--
-- 表的结构 `admins`
--

CREATE TABLE `admins` (
  `id` int NOT NULL COMMENT '管理员ID',
  `username` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '真实姓名',
  `phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像URL',
  `department_id` int DEFAULT NULL COMMENT '部门ID',
  `status` tinyint DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `remark` text COLLATE utf8mb4_general_ci COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='管理员表';

--
-- 转存表中的数据 `admins`
--

INSERT INTO `admins` (`id`, `username`, `password`, `name`, `phone`, `email`, `avatar`, `department_id`, `status`, `last_login_time`, `remark`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2a$10$VpBJ4aT/8Nuzeq7UZ6o7ou3cDZvG9bozltg.wCueOE8XJLwcvsCgW', '超级管理员', '13800000001', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 1, 1, '2025-07-12 23:00:38', NULL, '2025-05-31 22:36:44', '2025-05-31 22:47:41'),
(2, 'hr_admin', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '人事管理员', '13800000002', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 2, 1, NULL, NULL, '2025-05-31 22:36:44', '2025-06-01 13:17:00'),
(3, 'tech_admin', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '技术管理员', '13800000003', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 7, 1, NULL, NULL, '2025-05-31 22:36:44', '2025-06-01 13:21:20'),
(4, 'market_admin', '$2a$10$Brmah8TTbUs0U49GWjlFU.wrIBmA4As9YY1B/kLPdxNKcjIVEjiQu', '市场管理员', '13800000004', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 4, 1, NULL, NULL, '2025-05-31 22:36:44', '2025-06-01 13:11:57'),
(5, 'sales_admin', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '销售管理员', '13800000005', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 5, 1, NULL, NULL, '2025-05-31 22:36:44', '2025-06-01 13:11:49');

-- --------------------------------------------------------

--
-- 表的结构 `admin_permissions`
--

CREATE TABLE `admin_permissions` (
  `id` int NOT NULL,
  `admin_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='管理员权限关联表';

--
-- 转存表中的数据 `admin_permissions`
--

INSERT INTO `admin_permissions` (`id`, `admin_id`, `permission_id`, `created_at`) VALUES
(1, 1, 6, '2025-05-31 22:36:44'),
(2, 1, 3, '2025-05-31 22:36:44'),
(3, 1, 10, '2025-05-31 22:36:44'),
(4, 1, 4, '2025-05-31 22:36:44'),
(5, 1, 5, '2025-05-31 22:36:44'),
(6, 1, 8, '2025-05-31 22:36:44'),
(7, 1, 7, '2025-05-31 22:36:44'),
(8, 1, 9, '2025-05-31 22:36:44'),
(9, 1, 1, '2025-05-31 22:36:44'),
(10, 1, 2, '2025-05-31 22:36:44'),
(16, 2, 2, '2025-05-31 22:36:44'),
(17, 2, 10, '2025-05-31 22:36:44'),
(18, 3, 3, '2025-05-31 22:36:44'),
(19, 3, 4, '2025-05-31 22:36:44'),
(20, 3, 7, '2025-05-31 22:36:44'),
(21, 4, 5, '2025-05-31 22:36:44'),
(22, 4, 6, '2025-05-31 22:36:44'),
(23, 5, 8, '2025-05-31 22:36:44'),
(24, 5, 9, '2025-05-31 22:36:44');

-- --------------------------------------------------------

--
-- 表的结构 `carousels`
--

CREATE TABLE `carousels` (
  `id` int NOT NULL COMMENT '轮播图ID',
  `title` varchar(100) NOT NULL COMMENT '轮播图标题',
  `image_url` varchar(255) NOT NULL COMMENT '图片URL',
  `link` varchar(255) DEFAULT NULL COMMENT '点击链接',
  `sort` int DEFAULT '0' COMMENT '排序号，值越小越靠前',
  `status` tinyint DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='首页轮播图';

--
-- 转存表中的数据 `carousels`
--

INSERT INTO `carousels` (`id`, `title`, `image_url`, `link`, `sort`, `status`, `created_at`, `updated_at`) VALUES
(1, '欢迎访问学习平台', 'https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com/image/1751171626178_4lzgyp0a.jpg', '/home', 1, 1, '2025-05-31 23:37:47', '2025-06-29 12:33:49'),
(2, '精品课程推荐', 'https://via.placeholder.com/1200x400/e74c3c/ffffff?text=精品课程推荐', '/course', 2, 1, '2025-05-31 23:37:47', '2025-05-31 23:37:47'),
(3, '教学活动预告', 'https://via.placeholder.com/1200x400/2ecc71/ffffff?text=教学活动预告', '/activities', 3, 1, '2025-05-31 23:37:47', '2025-05-31 23:37:47'),
(4, '学习资源中心', 'https://via.placeholder.com/1200x400/f39c12/ffffff?text=学习资源中心', '/resources', 4, 1, '2025-05-31 23:37:47', '2025-05-31 23:37:47'),
(5, '优秀学员展示', 'https://via.placeholder.com/1200x400/9b59b6/ffffff?text=优秀学员展示', '/students', 5, 0, '2025-05-31 23:37:47', '2025-05-31 23:37:47');

-- --------------------------------------------------------

--
-- 表的结构 `courses`
--

CREATE TABLE `courses` (
  `id` bigint NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '课程名称',
  `description` text COLLATE utf8mb4_general_ci COMMENT '课程描述',
  `cover_image_url` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '课程封面图片URL',
  `status` enum('draft','published') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'draft' COMMENT '课程状态',
  `structure` json DEFAULT NULL COMMENT '课程目录结构 (章节和课时)',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='课程表，包含目录结构';

--
-- 转存表中的数据 `courses`
--

INSERT INTO `courses` (`id`, `name`, `description`, `cover_image_url`, `status`, `structure`, `creator_id`, `create_time`, `update_time`) VALUES
(2, '新的课程', '新的课程', NULL, 'published', '[{\"id\": 1, \"type\": \"chapter\", \"label\": \"111\", \"children\": [{\"id\": 1, \"type\": \"lesson\", \"label\": \"111\", \"resourceId\": 2}, {\"id\": 2, \"type\": \"lesson\", \"label\": \"7_1737423395\", \"resourceId\": 1}, {\"id\": 3, \"type\": \"lesson\", \"label\": \"2025-02-28 18-35-49\", \"resourceId\": 3}]}]', NULL, '2025-06-25 17:08:34', '2025-07-11 22:36:44'),
(3, '新的课程', NULL, NULL, 'published', '[{\"id\": 1752322562, \"type\": \"chapter\", \"label\": \"11111\", \"children\": [{\"id\": 1752322594, \"type\": \"lesson\", \"label\": \"2025-02-28 18-35-49\", \"resourceId\": 3}, {\"id\": 1752323382, \"type\": \"lesson\", \"label\": \"111\", \"resourceId\": 2}, {\"id\": 1752323090, \"type\": \"lesson\", \"label\": \"7_1737423395\", \"resourceId\": 1}]}]', NULL, '2025-06-27 16:36:06', '2025-07-12 20:16:11'),
(4, '新的课程', NULL, NULL, 'draft', NULL, NULL, '2025-07-01 10:53:01', '2025-07-01 10:53:01'),
(5, '新的课程', NULL, NULL, 'draft', NULL, NULL, '2025-07-09 16:26:29', '2025-07-09 16:26:29');

-- --------------------------------------------------------

--
-- 表的结构 `departments`
--

CREATE TABLE `departments` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门名称',
  `parent_id` int DEFAULT NULL COMMENT '父部门ID',
  `leader` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门负责人',
  `sort` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `remark` text COLLATE utf8mb4_general_ci COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部门表';

--
-- 转存表中的数据 `departments`
--

INSERT INTO `departments` (`id`, `name`, `parent_id`, `leader`, `sort`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(1, '总公司', NULL, '张总', 1, 1, '公司总部', '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(2, '人力资源部', 1, '王经理', 2, 1, '负责人力资源管理', '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(3, '技术部', 1, '李经理', 3, 1, '负责技术研发', '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(4, '市场部', 1, '赵经理', 4, 1, '负责市场营销', '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(5, '销售部', 1, '钱经理', 5, 1, '负责销售业务', '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(6, '财务部', 1, '孙经理', 6, 1, '负责财务管理', '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(7, '技术一组', 3, '周组长', 1, 1, '前端开发', '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(8, '技术二组', 3, '吴组长', 2, 1, '后端开发', '2025-05-31 22:36:44', '2025-07-01 09:58:15'),
(9, '技术三组', 3, '郑组长', 0, 1, '测试组', '2025-05-31 22:36:44', '2025-05-31 22:53:21');

-- --------------------------------------------------------

--
-- 表的结构 `exam_answer`
--

CREATE TABLE `exam_answer` (
  `id` int NOT NULL COMMENT '答题ID',
  `record_id` int NOT NULL COMMENT '考试记录ID',
  `question_id` int NOT NULL COMMENT '题目ID',
  `answer` text COMMENT '用户答案',
  `is_correct` tinyint(1) DEFAULT NULL COMMENT '是否正确',
  `score` int DEFAULT NULL COMMENT '得分',
  `comment` varchar(500) DEFAULT NULL COMMENT '评语(主观题使用)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='答题记录表';

--
-- 转存表中的数据 `exam_answer`
--

INSERT INTO `exam_answer` (`id`, `record_id`, `question_id`, `answer`, `is_correct`, `score`, `comment`, `created_at`, `updated_at`) VALUES
(8, 3, 4, 'A', 1, 10, NULL, '2025-07-11 18:24:52', '2025-07-11 18:24:52'),
(9, 3, 5, 'D', 1, 10, NULL, '2025-07-11 18:24:52', '2025-07-11 18:24:52'),
(10, 3, 8, 'C,B', 0, 0, NULL, '2025-07-11 18:24:52', '2025-07-11 18:24:52'),
(11, 3, 11, 'true', 0, 0, NULL, '2025-07-11 18:24:52', '2025-07-11 18:24:52'),
(12, 3, 14, '123', 0, 0, NULL, '2025-07-11 18:24:52', '2025-07-11 18:24:52'),
(13, 3, 17, '123456', 0, 0, NULL, '2025-07-11 18:24:52', '2025-07-11 18:24:52');

-- --------------------------------------------------------

--
-- 表的结构 `exam_bank`
--

CREATE TABLE `exam_bank` (
  `id` int NOT NULL COMMENT '题库ID',
  `name` varchar(100) NOT NULL COMMENT '题库名称',
  `description` varchar(500) DEFAULT NULL COMMENT '题库描述',
  `scope` varchar(100) DEFAULT NULL COMMENT '适用范围',
  `is_practice_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否允许学员刷题(0-不允许，1-允许)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除，1-已删除)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='题库表';

--
-- 转存表中的数据 `exam_bank`
--

INSERT INTO `exam_bank` (`id`, `name`, `description`, `scope`, `is_practice_enabled`, `created_at`, `updated_at`, `created_by`, `deleted`) VALUES
(1, '前端开发基础题库', '包含HTML、CSS、JavaScript等前端基础知识题目', '前端开发初学者', 1, '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(2, 'Java编程题库', '涵盖Java基础语法、面向对象、集合框架等', 'Java开发工程师', 1, '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(3, '数据库基础题库', '包含SQL语法、数据库设计、事务等基础知识', '数据库管理员、开发人员', 1, '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(4, '框架与工具题库', '包含Spring、Vue、React等主流框架知识', '中高级开发工程师', 1, '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(5, '算法与数据结构', '包含常见算法和数据结构相关题目', '所有开发人员', 1, '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0);

-- --------------------------------------------------------

--
-- 表的结构 `exam_exam`
--

CREATE TABLE `exam_exam` (
  `id` int NOT NULL COMMENT '考试ID',
  `title` varchar(200) NOT NULL COMMENT '考试标题',
  `description` varchar(500) DEFAULT NULL COMMENT '考试描述',
  `paper_id` int NOT NULL COMMENT '试卷ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `duration` int NOT NULL DEFAULT '60' COMMENT '考试时长(分钟)',
  `passing_score` int NOT NULL DEFAULT '60' COMMENT '及格分数',
  `max_attempts` int NOT NULL DEFAULT '1' COMMENT '最大考试次数',
  `is_published` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否发布(0-未发布,1-已发布)',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态(0-草稿,1-未开始,2-进行中,3-已结束)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除，1-已删除)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='考试表';

--
-- 转存表中的数据 `exam_exam`
--

INSERT INTO `exam_exam` (`id`, `title`, `description`, `paper_id`, `start_time`, `end_time`, `duration`, `passing_score`, `max_attempts`, `is_published`, `status`, `created_at`, `updated_at`, `created_by`, `deleted`) VALUES
(1, '11', '测试', 2, '2025-07-10 16:00:00', '2025-07-13 01:00:00', 60, 60, 1, 1, 2, '2025-06-03 11:04:09', '2025-07-11 00:55:29', 'admin', 0);

-- --------------------------------------------------------

--
-- 表的结构 `exam_exam_department`
--

CREATE TABLE `exam_exam_department` (
  `id` int NOT NULL COMMENT 'ID',
  `exam_id` int NOT NULL COMMENT '考试ID',
  `department_id` int NOT NULL COMMENT '部门ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='考试部门关联表';

--
-- 转存表中的数据 `exam_exam_department`
--

INSERT INTO `exam_exam_department` (`id`, `exam_id`, `department_id`, `created_at`) VALUES
(10, 1, 2, '2025-06-03 14:24:00'),
(11, 1, 4, '2025-06-03 14:24:00'),
(12, 1, 6, '2025-06-03 14:24:00'),
(13, 1, 1, '2025-06-03 14:24:00'),
(14, 1, 3, '2025-06-03 14:24:00'),
(15, 1, 5, '2025-06-03 14:24:00'),
(16, 1, 7, '2025-06-03 14:24:00'),
(17, 1, 8, '2025-06-03 14:24:00'),
(18, 1, 9, '2025-06-03 14:24:00');

-- --------------------------------------------------------

--
-- 表的结构 `exam_paper`
--

CREATE TABLE `exam_paper` (
  `id` int NOT NULL COMMENT '试卷ID',
  `title` varchar(200) NOT NULL COMMENT '试卷标题',
  `description` varchar(500) DEFAULT NULL COMMENT '试卷描述',
  `total_score` int NOT NULL DEFAULT '100' COMMENT '总分',
  `passing_score` int NOT NULL DEFAULT '60' COMMENT '及格分数',
  `duration` int NOT NULL DEFAULT '60' COMMENT '考试时长(分钟)',
  `is_published` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否发布(0-未发布,1-已发布)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除，1-已删除)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试卷表';

--
-- 转存表中的数据 `exam_paper`
--

INSERT INTO `exam_paper` (`id`, `title`, `description`, `total_score`, `passing_score`, `duration`, `is_published`, `created_at`, `updated_at`, `created_by`, `deleted`) VALUES
(1, '前端开发基础测试', '用于测试前端开发基础知识的考试', 73, 60, 60, 0, '2025-06-01 14:27:01', '2025-06-03 11:14:44', 'admin', 0),
(2, 'Java编程基础测试', '用于测试Java编程基础知识的考试', 100, 60, 90, 1, '2025-06-01 14:27:01', '2025-06-01 14:27:01', 'admin', 0),
(3, '数据库基础测试', '用于测试数据库基础知识的考试', 100, 60, 60, 1, '2025-06-01 14:27:01', '2025-06-01 14:27:01', 'admin', 0),
(4, '高级前端开发测试', '测试高级前端开发技能，包括框架和工具使用', 120, 80, 120, 0, '2025-06-01 14:27:01', '2025-06-01 14:27:01', 'admin', 0),
(5, '全栈开发综合测试', '测试全栈开发技能，包括前端、后端和数据库', 150, 90, 150, 0, '2025-06-01 14:27:01', '2025-06-01 14:27:01', 'admin', 0),
(6, '前端开发基础测试 (副本)', '用于测试前端开发基础知识的考试', 100, 60, 60, 0, '2025-06-02 21:53:42', '2025-06-02 21:53:42', 'admin', 0);

-- --------------------------------------------------------

--
-- 表的结构 `exam_paper_question`
--

CREATE TABLE `exam_paper_question` (
  `id` int NOT NULL COMMENT 'ID',
  `paper_id` int NOT NULL COMMENT '试卷ID',
  `question_id` int NOT NULL COMMENT '题目ID',
  `score` int NOT NULL DEFAULT '5' COMMENT '分值',
  `question_order` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试卷题目关联表';

--
-- 转存表中的数据 `exam_paper_question`
--

INSERT INTO `exam_paper_question` (`id`, `paper_id`, `question_id`, `score`, `question_order`, `created_at`) VALUES
(8, 2, 4, 10, 1, '2025-06-01 14:27:01'),
(9, 2, 5, 10, 2, '2025-06-01 14:27:01'),
(10, 2, 8, 20, 3, '2025-06-01 14:27:01'),
(11, 2, 11, 10, 4, '2025-06-01 14:27:01'),
(12, 2, 14, 10, 5, '2025-06-01 14:27:01'),
(13, 2, 17, 40, 6, '2025-06-01 14:27:01'),
(14, 3, 6, 10, 1, '2025-06-01 14:27:01'),
(15, 3, 9, 20, 2, '2025-06-01 14:27:01'),
(16, 3, 12, 10, 3, '2025-06-01 14:27:01'),
(17, 3, 15, 10, 4, '2025-06-01 14:27:01'),
(18, 3, 18, 50, 5, '2025-06-01 14:27:01'),
(19, 4, 1, 5, 1, '2025-06-01 14:27:01'),
(20, 4, 2, 5, 2, '2025-06-01 14:27:01'),
(21, 4, 3, 5, 3, '2025-06-01 14:27:01'),
(22, 4, 7, 15, 4, '2025-06-01 14:27:01'),
(23, 4, 10, 5, 5, '2025-06-01 14:27:01'),
(24, 4, 13, 5, 6, '2025-06-01 14:27:01'),
(25, 4, 16, 20, 7, '2025-06-01 14:27:01'),
(26, 4, 21, 20, 8, '2025-06-01 14:27:01'),
(27, 4, 22, 40, 9, '2025-06-01 14:27:01'),
(28, 5, 3, 10, 1, '2025-06-01 14:27:01'),
(29, 5, 4, 10, 2, '2025-06-01 14:27:01'),
(30, 5, 6, 10, 3, '2025-06-01 14:27:01'),
(31, 5, 7, 15, 4, '2025-06-01 14:27:01'),
(32, 5, 8, 15, 5, '2025-06-01 14:27:01'),
(33, 5, 9, 15, 6, '2025-06-01 14:27:01'),
(34, 5, 16, 25, 7, '2025-06-01 14:27:01'),
(35, 5, 17, 25, 8, '2025-06-01 14:27:01'),
(36, 5, 18, 25, 9, '2025-06-01 14:27:01'),
(43, 1, 1, 10, 1, '2025-06-03 11:14:44'),
(44, 1, 2, 10, 2, '2025-06-03 11:14:44'),
(45, 1, 23, 3, 3, '2025-06-03 11:14:44'),
(46, 1, 3, 10, 4, '2025-06-03 11:14:44'),
(47, 1, 7, 20, 5, '2025-06-03 11:14:44'),
(48, 1, 10, 10, 6, '2025-06-03 11:14:44'),
(49, 1, 13, 10, 7, '2025-06-03 11:14:44');

-- --------------------------------------------------------

--
-- 表的结构 `exam_question`
--

CREATE TABLE `exam_question` (
  `id` int NOT NULL COMMENT '题目ID',
  `bank_id` int NOT NULL COMMENT '所属题库ID',
  `title` text NOT NULL COMMENT '题目标题',
  `type` varchar(20) NOT NULL COMMENT '题目类型(single-单选题,multiple-多选题,judgment-判断题,fill-填空题,essay-简答题)',
  `options` text COMMENT '选项JSON(单选、多选题使用)',
  `correct_answer` text COMMENT '正确答案',
  `explanation` text COMMENT '题目解析',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0-未删除，1-已删除)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='题目表';

--
-- 转存表中的数据 `exam_question`
--

INSERT INTO `exam_question` (`id`, `bank_id`, `title`, `type`, `options`, `correct_answer`, `explanation`, `created_at`, `updated_at`, `created_by`, `deleted`) VALUES
(1, 1, 'HTML5中，哪个元素用于播放视频？', 'single', '[{\"id\":\"A\",\"content\":\"<video>\"},{\"id\":\"B\",\"content\":\"<media>\"},{\"id\":\"C\",\"content\":\"<movie>\"},{\"id\":\"D\",\"content\":\"<play>\"}]', 'A', 'HTML5中引入的<video>元素用于在网页中嵌入视频内容。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(2, 1, '在CSS中，哪个属性用于控制元素的文字颜色？', 'single', '[{\"id\":\"A\",\"content\":\"text-color\"},{\"id\":\"B\",\"content\":\"font-color\"},{\"id\":\"C\",\"content\":\"color\"},{\"id\":\"D\",\"content\":\"text-style\"}]', 'C', 'CSS中使用color属性来设置文本内容的颜色。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(3, 1, '以下哪个不是JavaScript的数据类型？', 'single', '[{\"id\":\"A\",\"content\":\"String\"},{\"id\":\"B\",\"content\":\"Boolean\"},{\"id\":\"C\",\"content\":\"Integer\"},{\"id\":\"D\",\"content\":\"Object\"}]', 'C', 'JavaScript中没有Integer类型，而是使用Number类型表示数字。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(4, 2, 'Java中，以下哪个关键字用于继承类？', 'single', '[{\"id\":\"A\",\"content\":\"extends\"},{\"id\":\"B\",\"content\":\"implements\"},{\"id\":\"C\",\"content\":\"inherits\"},{\"id\":\"D\",\"content\":\"super\"}]', 'A', 'Java中使用extends关键字来实现类的继承。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(5, 2, '在Java中，以下哪个不是访问修饰符？', 'single', '[{\"id\":\"A\",\"content\":\"public\"},{\"id\":\"B\",\"content\":\"private\"},{\"id\":\"C\",\"content\":\"protected\"},{\"id\":\"D\",\"content\":\"static\"}]', 'D', 'static是一个非访问修饰符，而不是访问修饰符。Java的访问修饰符包括public、protected、default和private。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(6, 3, 'SQL中，哪个命令用于从数据库表中检索数据？', 'single', '[\"GET\",\"SELECT\",\"FETCH\"]', 'C', 'SQL中使用SELECT语句从数据库表中检索数据。', '2025-06-01 13:26:05', '2025-06-01 13:41:01', 'admin', 0),
(7, 1, '以下哪些是前端JavaScript框架或库？', 'multiple', '[{\"id\":\"A\",\"content\":\"React\"},{\"id\":\"B\",\"content\":\"Vue\"},{\"id\":\"C\",\"content\":\"Angular\"},{\"id\":\"D\",\"content\":\"Django\"},{\"id\":\"E\",\"content\":\"jQuery\"}]', 'A,B,C,E', 'React、Vue、Angular和jQuery都是前端JavaScript框架或库，而Django是Python的Web框架。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(8, 2, 'Java中的集合框架包含哪些接口？', 'multiple', '[{\"id\":\"A\",\"content\":\"List\"},{\"id\":\"B\",\"content\":\"Set\"},{\"id\":\"C\",\"content\":\"Array\"},{\"id\":\"D\",\"content\":\"Map\"},{\"id\":\"E\",\"content\":\"Queue\"}]', 'A,B,D,E', 'Java集合框架主要包含List、Set、Map和Queue接口，Array是Java的数组类型，不属于集合框架。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(9, 3, '以下哪些是关系型数据库管理系统？', 'multiple', '[\"MySQL\",\"MongoDB\",\"PostgreSQL\",\"Redis\",\"11111\"]', 'A,C', 'MySQL、PostgreSQL和Oracle是关系型数据库，而MongoDB是文档型数据库，Redis是键值存储数据库。', '2025-06-01 13:26:05', '2025-06-03 11:14:22', 'admin', 0),
(10, 1, 'HTML5中可以使用localStorage在浏览器中永久存储数据。', 'judgment', '[{\"id\":\"A\",\"content\":\"正确\"},{\"id\":\"B\",\"content\":\"错误\"}]', 'A', 'localStorage确实可以在浏览器中永久存储数据，除非用户手动清除浏览器数据。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(11, 2, 'Java是一种解释型语言。', 'judgment', '[{\"id\":\"A\",\"content\":\"正确\"},{\"id\":\"B\",\"content\":\"错误\"}]', 'B', 'Java不是纯解释型语言，而是先编译成字节码，再由JVM解释执行，属于混合型语言。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(12, 3, 'SQL中的JOIN操作用于合并两个表中的行。', 'judgment', '[{\"id\":\"A\",\"content\":\"正确\"},{\"id\":\"B\",\"content\":\"错误\"}]', 'A', 'SQL中的JOIN操作确实用于基于相关列的值合并两个表中的行。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(13, 1, 'CSS中，使用_____属性可以设置元素的背景颜色。', 'fill', '[]', 'background-color', 'CSS中使用background-color属性设置元素的背景颜色。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(14, 2, 'Java中，声明一个常量需要使用_____关键字。', 'fill', '[]', 'final', 'Java中使用final关键字声明常量。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(15, 3, 'SQL中，用于对查询结果分组的子句是_____。', 'fill', '[]', 'GROUP BY', 'SQL中使用GROUP BY子句对查询结果进行分组。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(16, 1, '简述CSS盒模型的组成部分及其特点。', 'essay', '[]', 'CSS盒模型从内到外包括：content（内容）、padding（内边距）、border（边框）和margin（外边距）。标准盒模型的width和height只包括content部分，而IE盒模型的width和height包括content、padding和border。可以通过box-sizing属性控制使用哪种盒模型。', '完整回答应包括盒模型的四个组成部分及其作用，还应提及标准盒模型和IE盒模型的区别。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(17, 2, '解释Java中的多态性并给出一个示例。', 'essay', '[]', '多态性是面向对象编程的三大特性之一，允许使用父类类型的引用指向子类对象，并且调用同一个方法时会执行子类的实现。示例：Animal animal = new Dog(); animal.makeSound(); // 调用Dog类的makeSound方', '完整回答应包括多态的定义、实现多态的方式（继承、重写方法）以及多态的作用和优势。', '2025-06-01 13:26:05', '2025-06-01 13:34:46', 'admin', 0),
(18, 3, '说明数据库事务的ACID特性。', 'essay', '[]', 'ACID是数据库事务的四个基本特性：原子性（Atomicity）事务是不可分割的最小单位；一致性（Consistency）事务执行前后数据库状态保持一致；隔离性（Isolation）并发事务之间相互隔离；持久性（Durability）事务一旦提交，其结果永久保存。', '完整回答应详细解释ACID四个特性的含义，并可以适当举例说明每个特性的作用。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(19, 5, '时间复杂度为O(n log n)的排序算法有哪些？', 'multiple', '[{\"id\":\"A\",\"content\":\"冒泡排序\"},{\"id\":\"B\",\"content\":\"快速排序\"},{\"id\":\"C\",\"content\":\"归并排序\"},{\"id\":\"D\",\"content\":\"选择排序\"},{\"id\":\"E\",\"content\":\"堆排序\"}]', 'B,C,E', '快速排序、归并排序和堆排序的平均时间复杂度为O(n log n)，而冒泡排序和选择排序的时间复杂度为O(n²)。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(20, 5, '用于查找一个有序数组中某个元素的最高效算法是？', 'single', '[{\"id\":\"A\",\"content\":\"线性查找\"},{\"id\":\"B\",\"content\":\"二分查找\"},{\"id\":\"C\",\"content\":\"哈希查找\"},{\"id\":\"D\",\"content\":\"插值查找\"}]', 'B', '在有序数组中，二分查找的时间复杂度为O(log n)，是最高效的查找算法之一。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(21, 4, 'Vue.js中，哪个选项用于监听数据变化？', 'single', '[{\"id\":\"A\",\"content\":\"methods\"},{\"id\":\"B\",\"content\":\"computed\"},{\"id\":\"C\",\"content\":\"watch\"},{\"id\":\"D\",\"content\":\"filters\"}]', 'C', 'Vue.js中，watch选项用于监听数据变化并执行自定义操作。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(22, 4, 'Spring框架的核心模块包括？', 'multiple', '[{\"id\":\"A\",\"content\":\"IoC容器\"},{\"id\":\"B\",\"content\":\"AOP\"},{\"id\":\"C\",\"content\":\"数据访问\"},{\"id\":\"D\",\"content\":\"Web模块\"},{\"id\":\"E\",\"content\":\"测试模块\"}]', 'A,B,C,D,E', 'Spring框架的核心模块包括IoC容器、AOP、数据访问、Web模块和测试模块等。', '2025-06-01 13:26:05', '2025-06-01 13:26:05', 'admin', 0),
(23, 1, '111111', 'single', '[{\"id\":\"A\",\"content\":\"methods\"},{\"id\":\"B\",\"content\":\"computed\"},{\"id\":\"C\",\"content\":\"watch\"},{\"id\":\"D\",\"content\":\"filters\"}]', 'B', '', '2025-06-01 13:50:37', '2025-07-11 18:09:44', 'admin', 0);

-- --------------------------------------------------------

--
-- 表的结构 `exam_record`
--

CREATE TABLE `exam_record` (
  `id` int NOT NULL COMMENT '记录ID',
  `exam_id` int NOT NULL COMMENT '考试ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `department_id` int DEFAULT NULL COMMENT '部门ID',
  `score` int DEFAULT NULL COMMENT '得分',
  `total_score` int NOT NULL DEFAULT '100' COMMENT '试卷总分',
  `is_passed` tinyint(1) DEFAULT NULL COMMENT '是否通过',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int DEFAULT NULL COMMENT '实际用时(分)',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态(0-未开始,1-进行中,2-已完成,3-超时)',
  `attempt_number` int NOT NULL DEFAULT '1' COMMENT '考试次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='考试记录表';

--
-- 转存表中的数据 `exam_record`
--

INSERT INTO `exam_record` (`id`, `exam_id`, `user_id`, `department_id`, `score`, `total_score`, `is_passed`, `start_time`, `end_time`, `duration`, `status`, `attempt_number`, `created_at`, `updated_at`) VALUES
(3, 1, 1, 7, 20, 100, 0, '2025-07-11 18:24:34', '2025-07-11 18:24:52', 0, 2, 1, '2025-07-11 18:24:34', '2025-07-11 18:24:34');

-- --------------------------------------------------------

--
-- 表的结构 `forum_categories`
--

CREATE TABLE `forum_categories` (
  `id` int NOT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` int DEFAULT NULL COMMENT '父分类ID',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int DEFAULT '0' COMMENT '排序号',
  `status` tinyint DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='论坛分类表';

--
-- 转存表中的数据 `forum_categories`
--

INSERT INTO `forum_categories` (`id`, `name`, `parent_id`, `description`, `icon`, `sort`, `status`, `created_at`, `updated_at`) VALUES
(1, '学习交流', NULL, '交流学习心得和经验', 'el-icon-reading', 1, 1, '2025-06-01 11:01:11', '2025-06-01 11:01:11'),
(2, '课程讨论', NULL, '讨论各类课程内容和问题', 'el-icon-notebook', 2, 0, '2025-06-01 11:01:11', '2025-06-01 11:02:41'),
(3, '考试资料', NULL, '分享考试资料和备考经验', 'el-icon-document', 3, 1, '2025-06-01 11:01:11', '2025-06-01 11:01:11'),
(4, '校园生活', NULL, '分享校园生活的点滴', 'el-icon-house', 4, 1, '2025-06-01 11:01:11', '2025-06-01 11:01:11'),
(5, '编程技术', 1, '讨论编程相关技术和问题', 'el-icon-cpu', 1, 1, '2025-06-01 11:01:11', '2025-06-01 11:01:11'),
(6, '外语学习', 1, '交流外语学习方法和资源', 'el-icon-chat', 2, 1, '2025-06-01 11:01:11', '2025-06-01 11:01:11'),
(7, '数学讨论', 1, '讨论数学问题和解题思路', 'el-icon-data-analysis', 3, 1, '2025-06-01 11:01:11', '2025-06-01 11:01:11'),
(8, '物理化学', 1, '讨论物理化学相关问题', 'el-icon-set-up', 4, 1, '2025-06-01 11:01:11', '2025-06-01 11:01:11'),
(9, '初中课程', 2, '初中各学科课程讨论', 'el-icon-collection', 1, 0, '2025-06-01 11:01:11', '2025-06-01 11:02:41'),
(10, '高中课程', 2, '高中各学科课程讨论', 'el-icon-collection', 2, 0, '2025-06-01 11:01:11', '2025-06-01 11:02:41');

-- --------------------------------------------------------

--
-- 表的结构 `forum_comments`
--

CREATE TABLE `forum_comments` (
  `id` int NOT NULL COMMENT '评论ID',
  `content` text NOT NULL COMMENT '评论内容',
  `post_id` int NOT NULL COMMENT '帖子ID',
  `reply_to_id` int DEFAULT NULL COMMENT '回复目标用户ID',
  `parent_id` int DEFAULT NULL COMMENT '父评论ID',
  `author_id` int NOT NULL COMMENT '作者ID',
  `status` int DEFAULT '0' COMMENT '状态：0待审核，1已通过，2已拒绝，3已删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `like_count` int DEFAULT '0' COMMENT '点赞数'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='论坛评论表';

--
-- 转存表中的数据 `forum_comments`
--

INSERT INTO `forum_comments` (`id`, `content`, `post_id`, `reply_to_id`, `parent_id`, `author_id`, `status`, `created_at`, `updated_at`, `like_count`) VALUES
(1, '推荐你先从Python入门，相对简单易学。', 1, NULL, NULL, 2, 1, '2025-05-03 11:01:11', '2025-06-01 11:01:11', 0),
(2, '建议结合实际项目学习，比单纯看书效果更好。', 1, NULL, NULL, 3, 1, '2025-05-04 11:01:11', '2025-06-01 11:01:11', 0),
(3, '是的，项目驱动学习非常有效。', 1, NULL, 2, 4, 1, '2025-05-05 11:01:11', '2025-06-01 11:01:11', 0),
(4, '可以看看《Python编程：从入门到实践》这本书。', 1, NULL, NULL, 5, 1, '2025-05-06 11:01:11', '2025-07-12 18:21:36', 1),
(5, '建议同时学习算法基础，对编程能力提升很有帮助。', 1, NULL, NULL, 6, 1, '2025-05-07 11:01:11', '2025-07-12 18:21:37', 0),
(6, '感谢分享！这些资源非常有用。', 2, NULL, NULL, 7, 1, '2025-05-08 11:01:11', '2025-06-01 11:01:11', 0),
(7, '可以补充一些口语练习的资源吗？', 2, NULL, NULL, 8, 1, '2025-05-09 11:01:11', '2025-06-01 11:01:11', 0),
(8, '推荐BBC Learning English，有很多口语练习材料。', 2, NULL, 7, 2, 1, '2025-05-10 11:01:11', '2025-06-01 11:01:11', 0),
(9, '这道题需要用到三角函数的和差公式...', 3, NULL, NULL, 9, 1, '2025-05-11 11:01:11', '2025-06-01 11:01:11', 0),
(10, '还可以尝试用复数的方法解决，可能会更简单。', 3, NULL, NULL, 10, 1, '2025-05-13 11:01:11', '2025-06-01 11:01:11', 0),
(11, '这份总结太有用了，正好我最近在复习这部分内容。', 5, NULL, NULL, 2, 1, '2025-05-18 11:01:11', '2025-07-09 14:53:36', 0),
(12, '可以再补充一些关于时态的用法吗？', 5, NULL, NULL, 3, 1, '2025-05-19 11:01:11', '2025-07-09 14:53:36', 0),
(13, '非常全面的总结，对我备考很有帮助！', 5, NULL, NULL, 4, 1, '2025-05-20 11:01:11', '2025-07-09 14:53:36', 0),
(14, '我会再补充一些时态用法的内容。', 5, NULL, 12, 5, 1, '2025-05-21 11:01:11', '2025-07-09 14:53:36', 0),
(15, '这些技巧对我解题很有帮助，谢谢分享！', 6, NULL, NULL, 6, 1, '2025-05-21 11:01:11', '2025-07-09 14:53:36', 0),
(16, '动量守恒在很多题目中都能用到，是个很好的切入点。', 6, NULL, NULL, 7, 1, '2025-05-22 11:01:11', '2025-07-09 14:53:36', 0),
(17, '建议大家多总结题型，物理解题有很多固定模式。', 6, NULL, NULL, 8, 1, '2025-05-23 11:01:11', '2025-07-09 14:53:36', 0),
(18, '我想提议举办一个科技创新展示活动。', 7, NULL, NULL, 2, 1, '2025-05-23 11:01:11', '2025-07-09 14:53:36', 0),
(19, '支持！这个活动很有意义。', 7, NULL, 18, 9, 1, '2025-05-24 11:01:11', '2025-07-09 14:53:36', 0),
(20, '建议增加一些文学类的活动，比如诗歌朗诵。', 7, NULL, NULL, 10, 1, '2025-05-24 11:01:11', '2025-07-09 15:12:58', 1),
(21, '这个评论需要审核', 8, NULL, NULL, 2, 0, '2025-05-25 11:01:11', '2025-07-09 14:53:39', 0),
(22, '这个评论被拒绝了', 8, NULL, NULL, 3, 2, '2025-05-26 11:01:11', '2025-07-09 14:53:39', 0),
(23, '这个评论将被举报并删除', 1, NULL, NULL, 4, 3, '2025-05-31 11:01:11', '2025-07-09 14:53:39', 0),
(24, '你好', 7, NULL, 20, 1, 1, '2025-07-11 14:30:22', '2025-07-11 14:30:22', 0),
(25, '123', 7, NULL, 20, 1, 1, '2025-07-11 14:31:30', '2025-07-11 14:31:30', 0),
(26, '111', 1, NULL, 1, 1, 1, '2025-07-12 18:21:17', '2025-07-12 18:21:17', 0);

-- --------------------------------------------------------

--
-- 表的结构 `forum_comment_likes`
--

CREATE TABLE `forum_comment_likes` (
  `id` int NOT NULL,
  `comment_id` int NOT NULL COMMENT '评论ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='评论点赞表';

--
-- 转存表中的数据 `forum_comment_likes`
--

INSERT INTO `forum_comment_likes` (`id`, `comment_id`, `user_id`, `created_at`) VALUES
(1, 1, 1, '2025-07-09 14:39:01'),
(2, 1, 2, '2025-07-09 14:39:01'),
(3, 20, 1, '2025-07-09 15:12:58'),
(5, 4, 1, '2025-07-12 18:21:36');

-- --------------------------------------------------------

--
-- 表的结构 `forum_posts`
--

CREATE TABLE `forum_posts` (
  `id` int NOT NULL COMMENT '帖子ID',
  `title` varchar(200) NOT NULL COMMENT '帖子标题',
  `content` text NOT NULL COMMENT '帖子内容',
  `category_id` int NOT NULL COMMENT '分类ID',
  `author_id` int NOT NULL COMMENT '作者ID',
  `view_count` int DEFAULT '0' COMMENT '浏览量',
  `reply_count` int DEFAULT '0' COMMENT '回复数',
  `collect_count` int DEFAULT '0' COMMENT '分享数',
  `share_count` int DEFAULT '0' COMMENT '收藏数',
  `like_count` int DEFAULT '0' COMMENT '点赞数',
  `status` int DEFAULT '0' COMMENT '状态：0待审核，1已通过，2已拒绝，3已删除',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
  `is_essence` tinyint(1) DEFAULT '0' COMMENT '是否精华',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `images` text COMMENT '图片地址列表'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='论坛帖子表';

--
-- 转存表中的数据 `forum_posts`
--

INSERT INTO `forum_posts` (`id`, `title`, `content`, `category_id`, `author_id`, `view_count`, `reply_count`, `collect_count`, `share_count`, `like_count`, `status`, `is_top`, `is_essence`, `created_at`, `updated_at`, `images`) VALUES
(1, '如何高效学习编程？', '最近开始学习编程，有什么好的学习方法吗？希望能够快速掌握基础知识。', 5, 1, 122, 6, 0, 0, 0, 1, 1, 1, '2025-05-02 11:01:11', '2025-07-12 18:21:17', NULL),
(2, '分享一些英语学习资源', '这里有一些我收集的英语学习资源，包括听力、口语和阅读材料，希望对大家有帮助。', 6, 2, 85, 3, 0, 0, 0, 1, 0, 1, '2025-05-07 11:01:11', '2025-07-10 12:38:22', NULL),
(3, '求解这道数学题', '在三角函数的学习中遇到了一道难题，请大家帮忙看看如何解答...', 7, 3, 56, 2, 0, 0, 0, 1, 0, 0, '2025-05-12 11:01:11', '2025-07-10 12:38:22', NULL),
(4, '化学实验注意事项', '总结了一些做化学实验时的注意事项，避免安全隐患...', 8, 4, 47, 0, 0, 0, 0, 1, 0, 0, '2025-05-14 11:01:11', '2025-07-10 12:38:22', NULL),
(5, '初中英语语法总结', '分享我整理的初中英语语法知识点，希望对备考的同学有帮助...', 9, 2, 212, 8, 0, 0, 0, 1, 1, 1, '2025-05-17 11:01:11', '2025-07-10 13:32:09', NULL),
(6, '高中物理解题技巧', '整理了一些高中物理的解题思路和技巧，主要针对力学部分...', 10, 5, 178, 6, 0, 0, 0, 1, 0, 1, '2025-05-20 11:01:11', '2025-07-10 12:38:22', NULL),
(7, '校园文化节活动征集', '下个月将举办校园文化节，现在开始征集活动创意，欢迎大家踊跃提出建议...', 4, 6, 174, 5, 1, 0, 2, 1, 1, 0, '2025-05-22 11:01:11', '2025-07-12 19:37:53', NULL),
(8, '中考备考经验分享', '刚刚经历了中考，来分享一下我的备考经验和一些注意事项...', 3, 7, 320, 15, 0, 0, 0, 1, 0, 1, '2025-05-24 11:01:11', '2025-07-10 12:38:22', NULL),
(9, '求推荐编程入门书籍', '想学习Python编程，有没有推荐的入门书籍？最好是比较通俗易懂的...', 5, 8, 88, 7, 0, 0, 0, 1, 0, 0, '2025-05-27 11:01:11', '2025-07-11 14:26:42', NULL),
(10, '交流一下学习方法', '每个人的学习方法都不同，来交流一下大家是如何高效学习的...', 1, 9, 114, 9, 0, 0, 0, 1, 0, 0, '2025-05-29 11:01:11', '2025-07-11 14:23:37', NULL),
(11, '这个帖子需要审核', '这是一个待审核的帖子内容，管理员需要审核后才能显示...', 1, 10, 0, 0, 0, 0, 0, 0, 0, 0, '2025-05-30 11:01:11', '2025-07-10 12:38:22', NULL),
(12, '这个帖子被拒绝了', '这是一个被拒绝的帖子内容，因为内容不符合社区规范...', 2, 11, 0, 0, 0, 0, 0, 2, 0, 0, '2025-05-31 11:01:11', '2025-07-10 12:38:22', NULL),
(13, '这个帖子将被举报并删除', '这是一个包含不当内容的帖子，将被举报并标记为删除状态', 1, 10, 6, 0, 0, 0, 0, 3, 0, 0, '2025-05-31 11:01:11', '2025-07-10 12:38:22', NULL),
(14, 'test', 'test', 6, 1, 0, 0, 0, 0, 0, 0, 0, 0, '2025-07-10 18:31:02', '2025-07-10 18:31:02', 'https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com/image/1752143459656_5ongfjs1.jpg'),
(15, 'test', 'set', 5, 1, 2, 0, 0, 0, 1, 1, 0, 0, '2025-07-10 18:32:12', '2025-07-12 18:41:12', 'https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com/image/1752143521966_h4jf5o56.jpg,https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com/image/1752143522610_h6qtofhf.jpg');

-- --------------------------------------------------------

--
-- 表的结构 `forum_post_collects`
--

CREATE TABLE `forum_post_collects` (
  `id` int NOT NULL,
  `post_id` int NOT NULL COMMENT '帖子ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='帖子收藏表';

--
-- 转存表中的数据 `forum_post_collects`
--

INSERT INTO `forum_post_collects` (`id`, `post_id`, `user_id`, `created_at`) VALUES
(7, 7, 1, '2025-07-10 19:34:49');

-- --------------------------------------------------------

--
-- 表的结构 `forum_post_likes`
--

CREATE TABLE `forum_post_likes` (
  `id` int NOT NULL,
  `post_id` int NOT NULL COMMENT '帖子ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='帖子点赞表';

--
-- 转存表中的数据 `forum_post_likes`
--

INSERT INTO `forum_post_likes` (`id`, `post_id`, `user_id`, `created_at`) VALUES
(6, 7, 1, '2025-07-09 15:27:46'),
(7, 15, 1, '2025-07-12 18:41:12');

-- --------------------------------------------------------

--
-- 表的结构 `forum_user_follows`
--

CREATE TABLE `forum_user_follows` (
  `id` int NOT NULL,
  `follower_id` int NOT NULL COMMENT '关注者ID',
  `following_id` int NOT NULL COMMENT '被关注者ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户关注表';

--
-- 转存表中的数据 `forum_user_follows`
--

INSERT INTO `forum_user_follows` (`id`, `follower_id`, `following_id`, `created_at`) VALUES
(1, 1, 2, '2025-07-09 14:39:01'),
(2, 2, 1, '2025-07-09 14:39:01'),
(3, 1, 6, '2025-07-10 12:34:27'),
(5, 1, 10, '2025-07-10 14:16:07');

-- --------------------------------------------------------

--
-- 表的结构 `forum_violations`
--

CREATE TABLE `forum_violations` (
  `id` int NOT NULL COMMENT '举报ID',
  `content_type` varchar(20) NOT NULL COMMENT '内容类型：post帖子，comment评论',
  `content_id` int NOT NULL COMMENT '内容ID',
  `reporter_id` int NOT NULL COMMENT '举报人ID',
  `report_reason` varchar(255) NOT NULL COMMENT '举报原因',
  `status` int DEFAULT '0' COMMENT '状态：0待处理，1已处理，2已忽略',
  `process_type` int DEFAULT '0' COMMENT '处理类型：0无操作，1删除，2警告',
  `process_result` varchar(255) DEFAULT NULL COMMENT '处理结果',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `operator_id` int DEFAULT NULL COMMENT '处理人ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='论坛违规举报表';

--
-- 转存表中的数据 `forum_violations`
--

INSERT INTO `forum_violations` (`id`, `content_type`, `content_id`, `reporter_id`, `report_reason`, `status`, `process_type`, `process_result`, `process_time`, `operator_id`, `created_at`, `updated_at`) VALUES
(1, 'post', 12, 1, '内容包含广告信息，违反社区规范', 1, 1, '确认为广告内容，已删除', '2025-05-31 23:01:11', 1, '2025-05-31 11:01:11', '2025-06-01 11:01:11'),
(2, 'comment', 22, 2, '评论内容不友善，带有人身攻击', 1, 1, '评论确实存在不友善言论，已删除', '2025-06-01 01:01:11', 1, '2025-05-31 17:01:11', '2025-06-01 11:01:11'),
(3, 'post', 9, 3, '帖子内容涉嫌抄袭', 2, 0, '经核实未发现抄袭内容，忽略举报', '2025-06-01 03:01:11', 2, '2025-05-31 20:01:11', '2025-06-01 11:01:11'),
(4, 'comment', 15, 4, '评论内容与主题无关', 0, 0, NULL, NULL, NULL, '2025-06-01 06:01:11', '2025-06-01 11:01:11'),
(5, 'post', 10, 5, '疑似发布虚假信息', 0, 0, NULL, NULL, NULL, '2025-06-01 08:01:11', '2025-06-01 11:01:11'),
(6, 'post', 13, 6, '帖子包含违规内容', 1, 1, '确认违规，已删除', '2025-06-01 09:01:11', 1, '2025-06-01 07:01:11', '2025-06-01 11:01:11'),
(7, 'comment', 23, 7, '评论包含不当言论', 1, 1, '确认不当言论，已删除', '2025-06-01 10:01:11', 1, '2025-06-01 09:01:11', '2025-06-01 11:01:11');

-- --------------------------------------------------------

--
-- 表的结构 `news`
--

CREATE TABLE `news` (
  `id` int NOT NULL COMMENT '新闻ID',
  `title` varchar(200) NOT NULL COMMENT '新闻标题',
  `cover_url` varchar(255) DEFAULT NULL COMMENT '封面图URL',
  `content` text NOT NULL COMMENT '新闻内容',
  `publish_time` datetime NOT NULL COMMENT '发布时间',
  `view_count` int DEFAULT '0' COMMENT '浏览次数',
  `status` tinyint DEFAULT '1' COMMENT '状态：0草稿，1发布',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新闻动态';

--
-- 转存表中的数据 `news`
--

INSERT INTO `news` (`id`, `title`, `cover_url`, `content`, `publish_time`, `view_count`, `status`, `is_top`, `created_at`, `updated_at`) VALUES
(1, '平台新版本上线公告', 'https://via.placeholder.com/600x400/3498db/ffffff?text=平台新版本', '<p>我们很高兴地宣布，学习平台已经完成了重大升级，新版本带来了更好的用户体验和更丰富的功能。</p><p>主要更新内容：</p><ul><li>全新的界面设计</li><li>个性化学习路径</li><li>更强大的搜索功能</li><li>支持移动设备学习</li><li>www</li></ul>', '2025-05-31 23:37:47', 158, 1, 1, '2025-05-31 23:37:47', '2025-07-12 18:37:46'),
(2, '2023年度优秀学员表彰名单', 'https://via.placeholder.com/600x400/e74c3c/ffffff?text=优秀学员', '<p>经过严格评选，2023年度优秀学员评选结果已经揭晓。恭喜以下学员获得此项荣誉！</p><p>特等奖：</p><ul><li>张三（销售部）</li><li>李四（技术部）</li></ul><p>一等奖：</p><ul><li>王五（市场部）</li><li>赵六（客服部）</li></ul>', '2025-05-29 23:37:47', 90, 1, 0, '2025-05-31 23:37:47', '2025-07-12 18:37:49'),
(3, '新增多门专业技能培训课程', 'https://via.placeholder.com/600x400/2ecc71/ffffff?text=新课程', '<p>为满足员工技能提升需求，平台新增以下专业技能培训课程：</p><ol><li>高级项目管理</li><li>数据分析与可视化</li><li>有效沟通技巧</li><li>团队领导力培养</li><li>客户服务心理学</li></ol><p>欢迎各位员工根据自身发展需要选择相应课程进行学习。</p>', '2025-05-26 23:37:47', 121, 1, 0, '2025-05-31 23:37:47', '2025-07-12 19:39:19'),
(4, '关于举办年度学习经验分享会的通知', 'https://via.placeholder.com/600x400/f39c12/ffffff?text=经验分享会', '<p>为促进员工之间的学习交流，公司决定于下月举办年度学习经验分享会。</p><p>时间：2023年12月15日 14:00-17:00</p><p>地点：公司大会议室</p><p>参与人员：各部门学习委员及优秀学员代表</p><p>请相关人员提前准备5-10分钟的经验分享内容。</p>', '2025-05-24 23:37:47', 66, 1, 0, '2025-05-31 23:37:47', '2025-07-09 16:20:28'),
(5, '学习平台使用满意度调查', NULL, '<p>为了不断提高平台服务质量，我们诚邀您参与此次满意度调查。</p><p>调查内容包括：</p><ul><li>平台功能体验</li><li>课程内容质量</li><li>学习支持服务</li><li>意见与建议</li></ul><p>完成调查的员工将获得额外的学习积分奖励。</p><p><a href=\"/survey\">点击参与调查</a></p>', '2025-05-21 23:37:47', 42, 0, 0, '2025-05-31 23:37:47', '2025-06-27 16:24:28');

-- --------------------------------------------------------

--
-- 表的结构 `notices`
--

CREATE TABLE `notices` (
  `id` int NOT NULL COMMENT '公告ID',
  `title` varchar(200) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `publish_time` datetime NOT NULL COMMENT '发布时间',
  `importance` tinyint DEFAULT '1' COMMENT '重要程度：1一般，2重要，3紧急',
  `status` tinyint DEFAULT '1' COMMENT '状态：0草稿，1发布',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知公告';

--
-- 转存表中的数据 `notices`
--

INSERT INTO `notices` (`id`, `title`, `content`, `publish_time`, `importance`, `status`, `is_top`, `created_at`, `updated_at`) VALUES
(1, '关于系统计划维护的通知', '<p>为保障系统稳定运行，平台将于本周六凌晨2:00-4:00进行例行维护，期间系统将暂停服务。</p><p>请各位用户提前做好学习安排，由此带来的不便敬请谅解。</p>', '2025-05-31 23:37:47', 2, 1, 1, '2025-05-31 23:37:47', '2025-06-01 13:13:02'),
(2, '年度学习目标完成情况通报', '<p>截至目前，公司整体学习目标完成率为78%，距离年度目标尚有差距。</p><p>请各部门负责人督促本部门员工加强学习，确保年底前完成既定学习目标。</p><p>各部门完成情况：</p><ul><li>销售部：85%</li><li>技术部：92%</li><li>市场部：75%</li><li>客服部：68%</li><li>行政部：70%</li></ul>', '2025-05-30 23:37:47', 1, 1, 0, '2025-05-31 23:37:47', '2025-05-31 23:41:12'),
(3, '员工学习积分兑换规则调整公告', '<p>经公司决定，员工学习积分兑换规则将作如下调整：</p><ol><li>积分兑换比例由原来的10:1调整为8:1</li><li>新增多种兑换商品</li><li>设立季度和年度积分兑换特惠活动</li></ol><p>新规则将从下月1日起实施。</p>', '2025-05-28 23:37:47', 1, 1, 0, '2025-05-31 23:37:47', '2025-05-31 23:37:47'),
(4, '紧急通知：密码安全升级', '<p>为提升账号安全性，系统将于今日对所有用户密码安全策略进行升级。</p><p>请所有用户在登录后及时修改密码，新密码必须：</p><ul><li>长度不少于8位</li><li>包含大小写字母、数字和特殊符号</li><li>不能与前3次使用的密码相同</li></ul><p>请务必在本周内完成密码更新，感谢配合<img src=\"https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com/image/1751171641672_bb89v67y.png\" alt=\"男士.png\" data-href=\"https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com/image/1751171641672_bb89v67y.png\" style=\"\"/>！</p>', '2025-05-27 23:37:47', 3, 1, 1, '2025-05-31 23:37:47', '2025-06-29 12:34:04'),
(5, '学习平台微信小程序上线通知', '<p>为方便员工随时随地学习，公司学习平台微信小程序现已正式上线。</p><p>功能特点：</p><ul><li>课程离线缓存</li><li>碎片化学习提醒</li><li>学习进度云同步</li><li>社区互动讨论</li></ul><p>扫描公告下方二维码即可关注使用。</p>', '2025-05-25 23:37:47', 1, 1, 0, '2025-05-31 23:37:47', '2025-05-31 23:37:47');

-- --------------------------------------------------------

--
-- 表的结构 `permissions`
--

CREATE TABLE `permissions` (
  `id` int NOT NULL,
  `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限名称',
  `code` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限代码',
  `module` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属模块',
  `description` text COLLATE utf8mb4_general_ci COMMENT '权限描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权限表';

--
-- 转存表中的数据 `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `code`, `module`, `description`, `created_at`) VALUES
(1, '系统管理', 'system:manage', 'system', '系统基础管理权限', '2025-05-31 22:36:44'),
(2, '用户管理', 'user:manage', 'user', '用户增删改查权限', '2025-05-31 22:36:44'),
(3, '课程管理', 'course:manage', 'course', '课程管理权限', '2025-05-31 22:36:44'),
(4, '考试管理', 'exam:manage', 'exam', '考试管理权限', '2025-05-31 22:36:44'),
(5, '论坛管理', 'forum:manage', 'forum', '论坛管理权限', '2025-05-31 22:36:44'),
(6, '内容管理', 'content:manage', 'content', '内容管理权限', '2025-05-31 22:36:44'),
(7, '资源管理', 'resource:manage', 'resource', '资源管理权限', '2025-05-31 22:36:44'),
(8, '积分管理', 'points:manage', 'points', '积分管理权限', '2025-05-31 22:36:44'),
(9, '统计分析', 'statistics:view', 'statistics', '统计分析权限', '2025-05-31 22:36:44'),
(10, '部门管理', 'department:manage', 'system', '部门管理权限', '2025-05-31 22:36:44');

-- --------------------------------------------------------

--
-- 表的结构 `points_exchange`
--

CREATE TABLE `points_exchange` (
  `id` int NOT NULL COMMENT '兑换记录ID',
  `user_id` int NOT NULL COMMENT '学员ID',
  `product_id` int NOT NULL COMMENT '商品ID',
  `points` int NOT NULL COMMENT '消耗积分',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '兑换状态：pending(待审核)、approved(已审核)、shipped(已发货)、delivered(已送达)、completed(已完成)、cancelled(已取消)',
  `address` text COMMENT '收货地址信息（JSON格式）',
  `logistics_company` varchar(50) DEFAULT NULL COMMENT '物流公司',
  `tracking_number` varchar(50) DEFAULT NULL COMMENT '物流单号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `exchange_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '兑换时间',
  `approval_time` datetime DEFAULT NULL COMMENT '审核时间',
  `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分兑换记录表';

--
-- 转存表中的数据 `points_exchange`
--

INSERT INTO `points_exchange` (`id`, `user_id`, `product_id`, `points`, `status`, `address`, `logistics_company`, `tracking_number`, `remark`, `exchange_time`, `approval_time`, `ship_time`, `completion_time`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 200, '已发货', '北京市朝阳区xx路1号', '顺丰', 'SF123456789', '请尽快发货', '2024-05-01 10:00:00', '2024-05-01 12:00:00', '2024-05-02 09:00:00', NULL, '2025-07-09 16:01:23', NULL),
(2, 1, 1, 500, '已完成', '上海市浦东新区yy路2号', '中通', 'ZT987654321', NULL, '2024-05-03 14:30:00', '2024-05-03 15:00:00', '2024-05-04 08:00:00', '2024-05-06 18:00:00', '2025-07-09 16:01:23', NULL),
(3, 1, 1, 1, '待审核', NULL, NULL, NULL, NULL, '2025-07-10 15:54:06', NULL, NULL, NULL, '2025-07-10 15:54:06', '2025-07-10 15:54:40'),
(4, 1, 1, 1, '待审核', '北京市 市辖区 朝阳区 撒旦发射点', NULL, NULL, NULL, '2025-07-10 17:10:10', NULL, NULL, NULL, '2025-07-10 17:10:10', '2025-07-10 17:11:35');

-- --------------------------------------------------------

--
-- 表的结构 `points_product`
--

CREATE TABLE `points_product` (
  `id` int NOT NULL COMMENT '商品ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `category` varchar(20) NOT NULL COMMENT '商品分类：virtual(虚拟物品)、physical(实物)、service(服务)、other(其他)',
  `points` int NOT NULL COMMENT '所需积分',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `low_stock_threshold` int DEFAULT '10' COMMENT '库存预警阈值',
  `image` text COMMENT '商品图片URL',
  `description` text COMMENT '商品描述',
  `exchange_count` int NOT NULL DEFAULT '0' COMMENT '兑换次数',
  `status` varchar(10) NOT NULL DEFAULT 'active' COMMENT '状态：active(上架)、inactive(下架)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分商品表';

--
-- 转存表中的数据 `points_product`
--

INSERT INTO `points_product` (`id`, `name`, `category`, `points`, `stock`, `low_stock_threshold`, `image`, `description`, `exchange_count`, `status`, `created_at`, `updated_at`) VALUES
(1, '定制笔记本电脑', 'physical', 1, 49, 10, 'data:image/jpeg;base64,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', '公司定制笔记本，精美实用', 2, 'active', '2025-06-06 11:08:37', '2025-07-10 17:10:10'),
(2, '定制保温杯', 'physical', 150, 30, 5, 'https://example.com/images/cup.jpg', '公司定制保温杯，保温效果好', 0, 'active', '2025-06-06 11:08:37', NULL),
(3, '定制T恤', 'physical', 200, 40, 8, 'https://example.com/images/tshirt.jpg', '公司定制T恤，舒适透气', 0, 'active', '2025-06-06 11:08:37', NULL),
(4, '半天带薪休假', 'service', 300, 100, 20, 'https://example.com/images/holiday.jpg', '兑换半天带薪休假', 0, 'active', '2025-06-06 11:08:37', NULL),
(5, '优先选择培训机会', 'service', 500, 10, 2, 'https://example.com/images/training.jpg', '优先获得外部培训机会', 0, 'active', '2025-06-06 11:08:37', NULL);

-- --------------------------------------------------------

--
-- 表的结构 `points_record`
--

CREATE TABLE `points_record` (
  `id` int NOT NULL COMMENT '记录ID',
  `user_id` int NOT NULL COMMENT '学员ID',
  `points` int NOT NULL COMMENT '积分变动：正数表示增加，负数表示减少',
  `balance` int NOT NULL COMMENT '变动后余额',
  `type` varchar(20) NOT NULL COMMENT '积分类型：course_complete(课程完成)、sign_in(签到)、exchange(兑换)、admin_adjust(管理员调整)、other(其他)',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `related_id` int DEFAULT NULL COMMENT '关联ID：可能关联到课程、商品等',
  `related_type` varchar(20) DEFAULT NULL COMMENT '关联类型：course(课程)、product(商品)、other(其他)',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作员：可能是管理员操作的，也可能是系统自动的',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分记录表';

--
-- 转存表中的数据 `points_record`
--

INSERT INTO `points_record` (`id`, `user_id`, `points`, `balance`, `type`, `description`, `related_id`, `related_type`, `operator`, `created_at`) VALUES
(1, 1, 2, 2, 'admin_adjust', '好好学习', NULL, NULL, '我', '2025-06-06 15:24:49'),
(2, 1, 5, 7, 'admin_adjust', '哈哈哈', NULL, NULL, '火锅', '2025-06-06 15:39:21'),
(3, 2, 3, 153, 'admin_adjust', '相加', NULL, NULL, '我', '2025-06-20 19:21:55'),
(4, 2, -150, 3, 'admin_adjust', '111', NULL, NULL, '111', '2025-06-20 19:22:14'),
(5, 1, 3, 10, 'admin_adjust', '号', NULL, NULL, '11', '2025-06-29 10:10:00'),
(6, 1, -1, 9, 'exchange', '兑换商品：定制笔记本电脑 x1', 1, 'product', 'system', '2025-07-10 15:54:06'),
(7, 1, -1, 8, 'exchange', '兑换商品：定制笔记本电脑 x1', 1, 'product', 'system', '2025-07-10 17:10:10');

-- --------------------------------------------------------

--
-- 表的结构 `points_rule`
--

CREATE TABLE `points_rule` (
  `id` int NOT NULL COMMENT '规则ID',
  `category` varchar(20) NOT NULL COMMENT '规则类别：login(登录)、learning(学习)、forum(论坛)、exam(考试)、other(其他)',
  `name` varchar(50) NOT NULL COMMENT '规则名称',
  `code` varchar(50) NOT NULL COMMENT '规则标识码',
  `description` varchar(255) DEFAULT NULL COMMENT '规则描述',
  `points` int NOT NULL COMMENT '积分值：正数表示增加，负数表示减少',
  `daily_limit` int NOT NULL DEFAULT '-1' COMMENT '每日使用上限：-1表示无上限',
  `times_limit` int NOT NULL DEFAULT '-1' COMMENT '总次数上限：-1表示无上限',
  `used_count` int NOT NULL DEFAULT '0' COMMENT '已使用次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分规则表';

--
-- 转存表中的数据 `points_rule`
--

INSERT INTO `points_rule` (`id`, `category`, `name`, `code`, `description`, `points`, `daily_limit`, `times_limit`, `used_count`, `status`, `create_time`, `update_time`) VALUES
(1, 'login', '每日首次登录', 'daily_login', '每天首次登录系统获得积分', 5, 1, -1, 0, 1, '2025-06-06 11:08:37', NULL),
(2, 'learning', '完成课程学习', 'course_complete', '完成一门课程学习获得积分', 20, -1, -1, 0, 1, '2025-06-06 11:08:37', NULL),
(3, 'learning', '观看视频', 'watch_video', '观看教学视频获得积分', 2, 10, -1, 0, 1, '2025-06-06 11:08:37', NULL),
(4, 'learning', '阅读文章', 'read_article', '阅读教学文章获得积分', 1, 10, -1, 0, 1, '2025-06-06 11:08:37', NULL),
(5, 'forum', '发表帖子', 'post_forum', '在论坛发表帖子获得积分', 5, 3, -1, 0, 1, '2025-06-06 11:08:37', NULL),
(6, 'forum', '回复帖子', 'reply_forum', '回复论坛帖子获得积分', 2, 10, -1, 0, 1, '2025-06-06 11:08:37', NULL),
(7, 'exam', '参加考试', 'take_exam', '参加系统考试获得积分', 5, 3, -1, 0, 1, '2025-06-06 11:08:37', NULL),
(8, 'exam', '考试成绩优秀', 'exam_excellent', '考试成绩达到90分以上额外获得积分', 10, 3, -1, 0, 1, '2025-06-06 11:08:37', NULL),
(9, 'other', '完善个人资料', 'complete_profile', '完善个人资料获得积分', 10, -1, 1, 0, 1, '2025-06-06 11:08:37', NULL),
(10, 'other', '邀请新用户', 'invite_user', '邀请新用户注册获得积分', 20, -1, -1, 0, 1, '2025-06-06 11:08:37', NULL);

-- --------------------------------------------------------

--
-- 表的结构 `practice_answer`
--

CREATE TABLE `practice_answer` (
  `id` int NOT NULL COMMENT '答案ID',
  `record_id` int NOT NULL COMMENT '练习记录ID',
  `question_id` int NOT NULL COMMENT '题目ID',
  `user_answer` text COMMENT '用户答案',
  `correct_answer` text COMMENT '正确答案',
  `is_correct` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否正确(0-错误，1-正确)',
  `answer_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习答案表';

--
-- 转存表中的数据 `practice_answer`
--

INSERT INTO `practice_answer` (`id`, `record_id`, `question_id`, `user_answer`, `correct_answer`, `is_correct`, `answer_time`, `created_at`) VALUES
(1, 4, 1, '', 'A', 0, '2025-07-13 09:08:56', '2025-07-13 09:08:56'),
(2, 9, 1, 'B', 'A', 0, '2025-07-13 09:35:27', '2025-07-13 09:35:27'),
(3, 9, 2, 'B', 'C', 0, '2025-07-13 09:35:34', '2025-07-13 09:35:34'),
(4, 10, 1, 'C', 'A', 0, '2025-07-13 09:35:50', '2025-07-13 09:35:50'),
(5, 10, 2, 'B', 'C', 0, '2025-07-13 09:35:54', '2025-07-13 09:35:54'),
(6, 10, 3, 'A', 'C', 0, '2025-07-13 09:35:57', '2025-07-13 09:35:57'),
(7, 10, 7, 'A', 'A,B,C,E', 0, '2025-07-13 09:36:00', '2025-07-13 09:36:00'),
(8, 10, 10, 'false', 'A', 0, '2025-07-13 09:36:07', '2025-07-13 09:36:07'),
(9, 10, 13, '323', 'background-color', 0, '2025-07-13 09:36:13', '2025-07-13 09:36:13'),
(10, 10, 16, '嗯问问', 'CSS盒模型从内到外包括：content（内容）、padding（内边距）、border（边框）和margin（外边距）。标准盒模型的width和height只包括content部分，而IE盒模型的width和height包括content、padding和border。可以通过box-sizing属性控制使用哪种盒模型。', 0, '2025-07-13 09:36:20', '2025-07-13 09:36:20'),
(11, 10, 23, 'A', 'B', 0, '2025-07-13 09:36:29', '2025-07-13 09:36:29'),
(12, 11, 1, 'B', 'A', 0, '2025-07-13 09:54:59', '2025-07-13 09:54:59'),
(13, 11, 2, 'B', 'C', 0, '2025-07-13 09:57:11', '2025-07-13 09:57:11'),
(14, 11, 3, 'C', 'C', 1, '2025-07-13 09:57:16', '2025-07-13 09:57:16'),
(15, 11, 4, 'C', 'A', 0, '2025-07-13 09:57:21', '2025-07-13 09:57:21'),
(16, 11, 5, 'B', 'D', 0, '2025-07-13 09:57:25', '2025-07-13 09:57:25'),
(17, 11, 6, 'C', 'C', 1, '2025-07-13 09:57:29', '2025-07-13 09:57:29'),
(18, 11, 7, 'D', 'A,B,C,E', 0, '2025-07-13 09:57:33', '2025-07-13 09:57:33'),
(19, 11, 8, 'B', 'A,B,D,E', 0, '2025-07-13 09:57:36', '2025-07-13 09:57:36'),
(20, 11, 9, 'B', 'A,C', 0, '2025-07-13 09:57:40', '2025-07-13 09:57:40'),
(21, 11, 10, 'false', 'A', 0, '2025-07-13 09:57:44', '2025-07-13 09:57:44'),
(22, 11, 11, 'false', 'B', 0, '2025-07-13 09:57:52', '2025-07-13 09:57:52'),
(23, 11, 12, 'false', 'A', 0, '2025-07-13 09:58:37', '2025-07-13 09:58:37'),
(24, 11, 13, '我的我的是', 'background-color', 0, '2025-07-13 09:58:43', '2025-07-13 09:58:43'),
(25, 11, 14, '但是都是', 'final', 0, '2025-07-13 09:58:47', '2025-07-13 09:58:47'),
(26, 11, 15, '倒数第三段', 'GROUP BY', 0, '2025-07-13 09:58:51', '2025-07-13 09:58:51'),
(27, 11, 16, '但是都是', 'CSS盒模型从内到外包括：content（内容）、padding（内边距）、border（边框）和margin（外边距）。标准盒模型的width和height只包括content部分，而IE盒模型的width和height包括content、padding和border。可以通过box-sizing属性控制使用哪种盒模型。', 0, '2025-07-13 09:58:55', '2025-07-13 09:58:55'),
(28, 11, 17, '是多少', '多态性是面向对象编程的三大特性之一，允许使用父类类型的引用指向子类对象，并且调用同一个方法时会执行子类的实现。示例：Animal animal = new Dog(); animal.makeSound(); // 调用Dog类的makeSound方', 0, '2025-07-13 09:58:59', '2025-07-13 09:58:59'),
(29, 11, 18, '是多少多少的', 'ACID是数据库事务的四个基本特性：原子性（Atomicity）事务是不可分割的最小单位；一致性（Consistency）事务执行前后数据库状态保持一致；隔离性（Isolation）并发事务之间相互隔离；持久性（Durability）事务一旦提交，其结果永久保存。', 0, '2025-07-13 09:59:02', '2025-07-13 09:59:02'),
(30, 11, 19, 'A', 'B,C,E', 0, '2025-07-13 09:59:05', '2025-07-13 09:59:05'),
(31, 11, 20, 'A', 'B', 0, '2025-07-13 09:59:08', '2025-07-13 09:59:08'),
(32, 11, 21, 'B', 'C', 0, '2025-07-13 09:59:11', '2025-07-13 09:59:11'),
(33, 11, 22, 'C', 'A,B,C,D,E', 0, '2025-07-13 09:59:14', '2025-07-13 09:59:14'),
(34, 11, 23, 'B', 'B', 1, '2025-07-13 09:59:18', '2025-07-13 09:59:18');

-- --------------------------------------------------------

--
-- 表的结构 `practice_record`
--

CREATE TABLE `practice_record` (
  `id` int NOT NULL COMMENT '练习记录ID',
  `user_id` int NOT NULL COMMENT '学员ID',
  `bank_id` int DEFAULT NULL COMMENT '题库ID(为空表示全部题库)',
  `type` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '练习类型(normal-正常练习，wrong-错题练习)',
  `total_questions` int NOT NULL DEFAULT '0' COMMENT '总题数',
  `answered_questions` int NOT NULL DEFAULT '0' COMMENT '已答题数',
  `correct_count` int NOT NULL DEFAULT '0' COMMENT '正确数',
  `wrong_count` int NOT NULL DEFAULT '0' COMMENT '错误数',
  `score` int DEFAULT NULL COMMENT '得分',
  `status` varchar(20) NOT NULL DEFAULT 'ongoing' COMMENT '状态(ongoing-进行中,completed-已完成,abandoned-已放弃)',
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习记录表';

--
-- 转存表中的数据 `practice_record`
--

INSERT INTO `practice_record` (`id`, `user_id`, `bank_id`, `type`, `total_questions`, `answered_questions`, `correct_count`, `wrong_count`, `score`, `status`, `start_time`, `end_time`, `created_at`, `updated_at`) VALUES
(1, 1, NULL, 'normal', 23, 0, 0, 0, NULL, 'ongoing', '2025-07-13 08:53:59', NULL, '2025-07-13 08:53:59', '2025-07-13 08:53:59'),
(2, 1, NULL, 'normal', 23, 0, 0, 0, NULL, 'ongoing', '2025-07-13 08:54:14', NULL, '2025-07-13 08:54:14', '2025-07-13 08:54:14'),
(3, 1, 1, 'normal', 8, 0, 0, 0, NULL, 'ongoing', '2025-07-13 08:59:26', NULL, '2025-07-13 08:59:26', '2025-07-13 08:59:26'),
(4, 1, 1, 'normal', 8, 1, 0, 1, 0, 'completed', '2025-07-13 09:08:23', '2025-07-13 09:09:08', '2025-07-13 09:08:23', '2025-07-13 09:09:08'),
(5, 1, NULL, 'normal', 23, 0, 0, 0, NULL, 'ongoing', '2025-07-13 09:09:54', NULL, '2025-07-13 09:09:54', '2025-07-13 09:09:54'),
(6, 1, NULL, 'normal', 23, 0, 0, 0, NULL, 'ongoing', '2025-07-13 09:22:07', NULL, '2025-07-13 09:22:07', '2025-07-13 09:22:07'),
(7, 1, 1, 'normal', 8, 0, 0, 0, NULL, 'ongoing', '2025-07-13 09:27:25', NULL, '2025-07-13 09:27:25', '2025-07-13 09:27:25'),
(8, 1, 1, 'normal', 8, 0, 0, 0, NULL, 'ongoing', '2025-07-13 09:28:57', NULL, '2025-07-13 09:28:57', '2025-07-13 09:28:57'),
(9, 1, NULL, 'normal', 23, 2, 0, 2, NULL, 'ongoing', '2025-07-13 09:34:59', NULL, '2025-07-13 09:34:59', '2025-07-13 09:35:34'),
(10, 1, 1, 'normal', 8, 8, 0, 8, 0, 'completed', '2025-07-13 09:35:47', '2025-07-13 09:36:30', '2025-07-13 09:35:47', '2025-07-13 09:36:30'),
(11, 1, NULL, 'normal', 23, 23, 3, 20, 13, 'completed', '2025-07-13 09:54:54', '2025-07-13 09:59:20', '2025-07-13 09:54:54', '2025-07-13 09:59:20');

-- --------------------------------------------------------

--
-- 表的结构 `practice_stats`
--

CREATE TABLE `practice_stats` (
  `id` int NOT NULL COMMENT '统计ID',
  `user_id` int NOT NULL COMMENT '学员ID',
  `bank_id` int NOT NULL COMMENT '题库ID',
  `total_questions` int NOT NULL DEFAULT '0' COMMENT '题库总题数',
  `answered_questions` int NOT NULL DEFAULT '0' COMMENT '已答题数',
  `correct_count` int NOT NULL DEFAULT '0' COMMENT '正确数',
  `wrong_count` int NOT NULL DEFAULT '0' COMMENT '错误数',
  `accuracy_rate` decimal(5,2) DEFAULT '0.00' COMMENT '正确率',
  `last_practice_time` datetime DEFAULT NULL COMMENT '最后练习时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习统计表';

--
-- 转存表中的数据 `practice_stats`
--

INSERT INTO `practice_stats` (`id`, `user_id`, `bank_id`, `total_questions`, `answered_questions`, `correct_count`, `wrong_count`, `accuracy_rate`, `last_practice_time`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 8, 0, 0, 0, 0.00, '2025-07-13 09:59:18', '2025-07-13 09:08:56', '2025-07-13 09:59:18'),
(2, 1, 2, 6, 0, 0, 0, 0.00, '2025-07-13 09:58:59', '2025-07-13 09:57:21', '2025-07-13 09:58:59'),
(3, 1, 3, 5, 0, 0, 0, 0.00, '2025-07-13 09:59:02', '2025-07-13 09:57:29', '2025-07-13 09:59:02'),
(4, 1, 5, 2, 0, 0, 0, 0.00, '2025-07-13 09:59:08', '2025-07-13 09:59:05', '2025-07-13 09:59:08'),
(5, 1, 4, 2, 0, 0, 0, 0.00, '2025-07-13 09:59:14', '2025-07-13 09:59:11', '2025-07-13 09:59:14');

-- --------------------------------------------------------

--
-- 表的结构 `resources`
--

CREATE TABLE `resources` (
  `id` bigint NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源名称',
  `type` enum('file','video','article') COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源类型',
  `content` longtext COLLATE utf8mb4_general_ci COMMENT '文件或视频的URL，或者文章的内容',
  `tags` text COLLATE utf8mb4_general_ci COMMENT '标签 (JSON 数组格式)',
  `uploader_id` bigint DEFAULT NULL COMMENT '上传者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='统一资源表';

--
-- 转存表中的数据 `resources`
--

INSERT INTO `resources` (`id`, `name`, `type`, `content`, `tags`, `uploader_id`, `create_time`, `update_time`) VALUES
(1, '7_1737423395', 'file', 'https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com/upload/1750847032085_3t61sd2c.mp4', '[]', NULL, '2025-06-25 18:25:01', '2025-06-25 18:25:01'),
(2, '111', 'article', '<p>你好</p>', '[\"111\",\"222\"]', NULL, '2025-06-25 18:30:25', '2025-06-25 18:30:40'),
(3, '2025-02-28 18-35-49', 'video', 'https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com/upload/1751012788544_2efnwjmw.mkv', '[\"nihao\"]', NULL, '2025-06-27 16:26:34', '2025-06-27 16:26:34');

-- --------------------------------------------------------

--
-- 表的结构 `students`
--

CREATE TABLE `students` (
  `id` int NOT NULL COMMENT '学员ID',
  `username` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '真实姓名',
  `phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像URL',
  `department_id` int DEFAULT NULL COMMENT '部门ID',
  `job_title` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位',
  `employee_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工号',
  `status` tinyint DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `points` int DEFAULT '0' COMMENT '积分余额',
  `entry_time` date DEFAULT NULL COMMENT '入职时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `remark` text COLLATE utf8mb4_general_ci COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学员表';

--
-- 转存表中的数据 `students`
--

INSERT INTO `students` (`id`, `username`, `password`, `name`, `phone`, `email`, `avatar`, `department_id`, `job_title`, `employee_id`, `status`, `points`, `entry_time`, `last_login_time`, `remark`, `created_at`, `updated_at`) VALUES
(1, 'student1', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '张三', '13900000001', '<EMAIL>', 'https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com/image/1752144111820_qbhviyyz.jpg', 7, '前端工程师', 'EMP001', 1, 8, '2022-01-15', '2025-07-12 23:39:17', NULL, '2025-05-31 22:36:44', '2025-07-12 16:27:20'),
(2, 'student2', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '李四', '13900000002', '<EMAIL>', '/uploads/avatar/2025/07/09/db7908a8-e748-4562-b79f-83ec1946fc96.jpg', 7, '前端工程师', 'EMP002', 1, 3, '2022-02-20', '2025-07-11 21:25:12', NULL, '2025-05-31 22:36:44', '2025-07-09 02:01:15'),
(3, 'student3', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '王五', '13900000003', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 8, '后端工程师', 'EMP003', 1, 200, '2022-03-10', NULL, NULL, '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(4, 'student4', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '赵六', '13900000004', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 8, '后端工程师', 'EMP004', 1, 120, '2022-04-05', NULL, NULL, '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(5, 'student5', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '钱七', '13900000005', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 9, '测试工程师', 'EMP005', 1, 80, '2022-05-22', NULL, NULL, '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(6, 'student6', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '孙八', '13900000006', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 9, '测试工程师', 'EMP006', 0, 50, '2022-06-18', NULL, NULL, '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(7, 'student7', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '周九', '13900000007', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 2, '人事专员', 'EMP007', 1, 90, '2022-07-08', NULL, NULL, '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(8, 'student8', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '吴十', '13900000008', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 4, '市场专员', 'EMP008', 1, 110, '2022-08-12', NULL, NULL, '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(9, 'student9', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '郑十一', '13900000009', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 5, '销售专员', 'EMP009', 1, 70, '2022-09-25', NULL, NULL, '2025-05-31 22:36:44', '2025-05-31 22:36:44'),
(10, 'student10', '$2a$10$KBkJVofeK7KWeMI0XlTfyeNUNyCByXhh8Fy3B3QT7lHaDR.7TYGti', '冯十二', '13900000010', '<EMAIL>', 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 6, '财务专员', 'EMP010', 1, 130, '2022-10-30', NULL, NULL, '2025-05-31 22:36:44', '2025-05-31 22:36:44');

-- --------------------------------------------------------

--
-- 表的结构 `study_logs`
--

CREATE TABLE `study_logs` (
  `id` int NOT NULL COMMENT '日志ID',
  `record_id` int DEFAULT NULL COMMENT '学习记录ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `course_id` int NOT NULL COMMENT '课程ID',
  `lesson_id` int DEFAULT NULL COMMENT '课时ID',
  `resource_id` int DEFAULT NULL COMMENT '资源ID',
  `start_position` int DEFAULT '0' COMMENT '开始位置（秒）',
  `end_position` int DEFAULT '0' COMMENT '结束位置（秒）',
  `duration` int DEFAULT '0' COMMENT '学习时长（秒）',
  `progress` int DEFAULT '0' COMMENT '学习进度（百分比）',
  `completed` tinyint DEFAULT '0' COMMENT '是否完成：0未完成，1已完成',
  `study_time` datetime NOT NULL COMMENT '学习时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学习日志表';

--
-- 转存表中的数据 `study_logs`
--

INSERT INTO `study_logs` (`id`, `record_id`, `user_id`, `course_id`, `lesson_id`, `resource_id`, `start_position`, `end_position`, `duration`, `progress`, `completed`, `study_time`, `created_at`) VALUES
(1, 1, 1, 1, 1, 1, 0, 600, 600, 25, 0, '2025-06-27 10:00:00', '2025-06-27 19:00:51'),
(2, 1, 1, 1, 1, 1, 600, 1350, 750, 75, 0, '2025-06-27 10:30:00', '2025-06-27 19:00:51'),
(3, 2, 1, 1, 2, 2, 0, 0, 600, 100, 1, '2025-06-27 11:15:00', '2025-06-27 19:00:51'),
(4, 3, 1, 2, 3, 3, 0, 405, 405, 45, 0, '2025-06-27 14:20:00', '2025-06-27 19:00:51'),
(5, 4, 2, 1, 1, 1, 0, 1200, 1200, 50, 0, '2025-06-27 09:15:00', '2025-06-27 19:00:51'),
(6, 4, 2, 1, 1, 1, 1200, 2400, 1200, 100, 1, '2025-06-27 09:45:00', '2025-06-27 19:00:51'),
(7, 5, 2, 1, 2, 2, 0, 0, 480, 80, 0, '2025-06-27 10:30:00', '2025-06-27 19:00:51'),
(8, 6, 3, 2, 3, 3, 0, 270, 270, 30, 0, '2025-06-27 16:10:00', '2025-06-27 19:00:51'),
(9, 7, 3, 3, 4, 4, 0, 0, 300, 100, 1, '2025-06-27 13:25:00', '2025-06-27 19:00:51');

-- --------------------------------------------------------

--
-- 表的结构 `study_records`
--

CREATE TABLE `study_records` (
  `id` int NOT NULL COMMENT '记录ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `course_id` int NOT NULL COMMENT '课程ID',
  `lesson_id` int DEFAULT NULL COMMENT '课时ID（可选，为空表示整体课程记录）',
  `resource_id` int DEFAULT NULL COMMENT '资源ID',
  `resource_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资源类型：video, file, article',
  `progress` int DEFAULT '0' COMMENT '学习进度（百分比）',
  `duration` int DEFAULT '0' COMMENT '学习时长（秒）',
  `completed` tinyint DEFAULT '0' COMMENT '是否完成：0未完成，1已完成',
  `last_position` int DEFAULT '0' COMMENT '最后学习位置（秒）',
  `last_study_time` datetime DEFAULT NULL COMMENT '最后学习时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学习记录表';

--
-- 转存表中的数据 `study_records`
--

INSERT INTO `study_records` (`id`, `user_id`, `course_id`, `lesson_id`, `resource_id`, `resource_type`, `progress`, `duration`, `completed`, `last_position`, `last_study_time`, `created_at`, `updated_at`) VALUES
(1, 1, 2, 1, 2, 'article', 27, 1956, 0, 0, '2025-06-27 10:30:00', '2025-06-27 19:00:51', '2025-07-12 18:39:44'),
(2, 1, 1, 2, 2, 'article', 100, 600, 1, 0, '2025-06-27 11:15:00', '2025-06-27 19:00:51', '2025-06-27 19:00:51'),
(3, 1, 2, 3, 3, 'video', 100, 1101, 1, 26, '2025-06-27 14:20:00', '2025-06-27 19:00:51', '2025-07-12 18:38:57'),
(4, 2, 1, 1, 1, 'video', 100, 2400, 1, 2400, '2025-06-27 09:45:00', '2025-06-27 19:00:51', '2025-06-27 19:00:51'),
(5, 2, 1, 2, 2, 'article', 80, 480, 0, 0, '2025-06-27 10:30:00', '2025-06-27 19:00:51', '2025-06-27 19:00:51'),
(6, 3, 2, 3, 3, 'video', 30, 540, 0, 270, '2025-06-27 16:10:00', '2025-06-27 19:00:51', '2025-06-27 19:00:51'),
(7, 3, 3, 4, 4, 'file', 100, 300, 1, 0, '2025-06-27 13:25:00', '2025-06-27 19:00:51', '2025-06-27 19:00:51'),
(8, 1, 3, 1752322594, 3, 'video', 100, 41, 1, 26, NULL, '2025-07-12 20:16:20', '2025-07-12 20:17:01'),
(9, 1, 3, 1752323382, 2, 'article', 0, 0, 0, 0, NULL, '2025-07-12 22:46:54', '2025-07-12 22:46:54');

-- --------------------------------------------------------

--
-- 替换视图以便查看 `v_study_records_detail`
-- （参见下面的实际视图）
--
CREATE TABLE `v_study_records_detail` (
`id` int
,`user_id` int
,`student_name` varchar(50)
,`department_name` varchar(100)
,`course_id` int
,`course_name` varchar(255)
,`lesson_id` int
,`resource_id` int
,`resource_type` varchar(20)
,`progress` int
,`duration` int
,`completed` tinyint
,`last_position` int
,`last_study_time` datetime
,`created_at` datetime
,`updated_at` datetime
);

--
-- 转储表的索引
--

--
-- 表的索引 `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `phone` (`phone`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_phone` (`phone`),
  ADD KEY `idx_department` (`department_id`);

--
-- 表的索引 `admin_permissions`
--
ALTER TABLE `admin_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_admin_permission` (`admin_id`,`permission_id`),
  ADD KEY `permission_id` (`permission_id`);

--
-- 表的索引 `carousels`
--
ALTER TABLE `carousels`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `courses`
--
ALTER TABLE `courses`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `departments`
--
ALTER TABLE `departments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_parent` (`parent_id`);

--
-- 表的索引 `exam_answer`
--
ALTER TABLE `exam_answer`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_record_id` (`record_id`),
  ADD KEY `idx_question_id` (`question_id`);

--
-- 表的索引 `exam_bank`
--
ALTER TABLE `exam_bank`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `exam_exam`
--
ALTER TABLE `exam_exam`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_paper_id` (`paper_id`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `exam_exam_department`
--
ALTER TABLE `exam_exam_department`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_exam_department` (`exam_id`,`department_id`),
  ADD KEY `idx_exam_id` (`exam_id`),
  ADD KEY `idx_department_id` (`department_id`);

--
-- 表的索引 `exam_paper`
--
ALTER TABLE `exam_paper`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `exam_paper_question`
--
ALTER TABLE `exam_paper_question`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_paper_question` (`paper_id`,`question_id`),
  ADD KEY `idx_paper_id` (`paper_id`),
  ADD KEY `idx_question_id` (`question_id`);

--
-- 表的索引 `exam_question`
--
ALTER TABLE `exam_question`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_bank_id` (`bank_id`);

--
-- 表的索引 `exam_record`
--
ALTER TABLE `exam_record`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exam_id` (`exam_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_department_id` (`department_id`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `forum_categories`
--
ALTER TABLE `forum_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_parent` (`parent_id`);

--
-- 表的索引 `forum_comments`
--
ALTER TABLE `forum_comments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_post` (`post_id`),
  ADD KEY `idx_parent` (`parent_id`),
  ADD KEY `idx_author` (`author_id`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `forum_comment_likes`
--
ALTER TABLE `forum_comment_likes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_comment_user` (`comment_id`,`user_id`) COMMENT '用户评论点赞唯一索引',
  ADD KEY `idx_comment_id` (`comment_id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- 表的索引 `forum_posts`
--
ALTER TABLE `forum_posts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category_id`),
  ADD KEY `idx_author` (`author_id`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `forum_post_collects`
--
ALTER TABLE `forum_post_collects`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_post_user` (`post_id`,`user_id`) COMMENT '用户帖子收藏唯一索引',
  ADD KEY `idx_post_id` (`post_id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- 表的索引 `forum_post_likes`
--
ALTER TABLE `forum_post_likes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_post_user` (`post_id`,`user_id`) COMMENT '用户帖子点赞唯一索引',
  ADD KEY `idx_post_id` (`post_id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- 表的索引 `forum_user_follows`
--
ALTER TABLE `forum_user_follows`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_follower_following` (`follower_id`,`following_id`) COMMENT '关注关系唯一索引',
  ADD KEY `idx_follower_id` (`follower_id`),
  ADD KEY `idx_following_id` (`following_id`);

--
-- 表的索引 `forum_violations`
--
ALTER TABLE `forum_violations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_content` (`content_type`,`content_id`),
  ADD KEY `idx_reporter` (`reporter_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_operator` (`operator_id`);

--
-- 表的索引 `news`
--
ALTER TABLE `news`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `notices`
--
ALTER TABLE `notices`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- 表的索引 `points_exchange`
--
ALTER TABLE `points_exchange`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_product_id` (`product_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_exchange_time` (`exchange_time`);

--
-- 表的索引 `points_product`
--
ALTER TABLE `points_product`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `points_record`
--
ALTER TABLE `points_record`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_type` (`type`);

--
-- 表的索引 `points_rule`
--
ALTER TABLE `points_rule`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_code` (`code`);

--
-- 表的索引 `practice_answer`
--
ALTER TABLE `practice_answer`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_record_question` (`record_id`,`question_id`),
  ADD KEY `idx_record_id` (`record_id`),
  ADD KEY `idx_question_id` (`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`);

--
-- 表的索引 `practice_record`
--
ALTER TABLE `practice_record`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_bank_id` (`bank_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_start_time` (`start_time`);

--
-- 表的索引 `practice_stats`
--
ALTER TABLE `practice_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_user_bank` (`user_id`,`bank_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_bank_id` (`bank_id`);

--
-- 表的索引 `resources`
--
ALTER TABLE `resources`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `students`
--
ALTER TABLE `students`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `phone` (`phone`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_phone` (`phone`),
  ADD KEY `idx_department` (`department_id`),
  ADD KEY `idx_employee_id` (`employee_id`);

--
-- 表的索引 `study_logs`
--
ALTER TABLE `study_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_record_id` (`record_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_lesson_id` (`lesson_id`),
  ADD KEY `idx_study_time` (`study_time`),
  ADD KEY `idx_user_study_time` (`user_id`,`study_time` DESC),
  ADD KEY `idx_course_study_time` (`course_id`,`study_time` DESC);

--
-- 表的索引 `study_records`
--
ALTER TABLE `study_records`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_course_lesson` (`user_id`,`course_id`,`lesson_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_course_id` (`course_id`),
  ADD KEY `idx_lesson_id` (`lesson_id`),
  ADD KEY `idx_last_study_time` (`last_study_time`),
  ADD KEY `idx_completed` (`completed`),
  ADD KEY `idx_user_course_updated` (`user_id`,`course_id`,`updated_at` DESC),
  ADD KEY `idx_course_completed` (`course_id`,`completed`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '管理员ID', AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `admin_permissions`
--
ALTER TABLE `admin_permissions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- 使用表AUTO_INCREMENT `carousels`
--
ALTER TABLE `carousels`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '轮播图ID', AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `courses`
--
ALTER TABLE `courses`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `departments`
--
ALTER TABLE `departments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- 使用表AUTO_INCREMENT `exam_answer`
--
ALTER TABLE `exam_answer`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '答题ID', AUTO_INCREMENT=14;

--
-- 使用表AUTO_INCREMENT `exam_bank`
--
ALTER TABLE `exam_bank`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '题库ID', AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `exam_exam`
--
ALTER TABLE `exam_exam`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '考试ID', AUTO_INCREMENT=2;

--
-- 使用表AUTO_INCREMENT `exam_exam_department`
--
ALTER TABLE `exam_exam_department`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID', AUTO_INCREMENT=19;

--
-- 使用表AUTO_INCREMENT `exam_paper`
--
ALTER TABLE `exam_paper`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '试卷ID', AUTO_INCREMENT=7;

--
-- 使用表AUTO_INCREMENT `exam_paper_question`
--
ALTER TABLE `exam_paper_question`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID', AUTO_INCREMENT=50;

--
-- 使用表AUTO_INCREMENT `exam_question`
--
ALTER TABLE `exam_question`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '题目ID', AUTO_INCREMENT=24;

--
-- 使用表AUTO_INCREMENT `exam_record`
--
ALTER TABLE `exam_record`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID', AUTO_INCREMENT=4;

--
-- 使用表AUTO_INCREMENT `forum_categories`
--
ALTER TABLE `forum_categories`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '分类ID', AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `forum_comments`
--
ALTER TABLE `forum_comments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '评论ID', AUTO_INCREMENT=27;

--
-- 使用表AUTO_INCREMENT `forum_comment_likes`
--
ALTER TABLE `forum_comment_likes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `forum_posts`
--
ALTER TABLE `forum_posts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '帖子ID', AUTO_INCREMENT=16;

--
-- 使用表AUTO_INCREMENT `forum_post_collects`
--
ALTER TABLE `forum_post_collects`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- 使用表AUTO_INCREMENT `forum_post_likes`
--
ALTER TABLE `forum_post_likes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- 使用表AUTO_INCREMENT `forum_user_follows`
--
ALTER TABLE `forum_user_follows`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `forum_violations`
--
ALTER TABLE `forum_violations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '举报ID', AUTO_INCREMENT=8;

--
-- 使用表AUTO_INCREMENT `news`
--
ALTER TABLE `news`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '新闻ID', AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `notices`
--
ALTER TABLE `notices`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID', AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `points_exchange`
--
ALTER TABLE `points_exchange`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '兑换记录ID', AUTO_INCREMENT=5;

--
-- 使用表AUTO_INCREMENT `points_product`
--
ALTER TABLE `points_product`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '商品ID', AUTO_INCREMENT=7;

--
-- 使用表AUTO_INCREMENT `points_record`
--
ALTER TABLE `points_record`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID', AUTO_INCREMENT=8;

--
-- 使用表AUTO_INCREMENT `points_rule`
--
ALTER TABLE `points_rule`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '规则ID', AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `practice_answer`
--
ALTER TABLE `practice_answer`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '答案ID', AUTO_INCREMENT=35;

--
-- 使用表AUTO_INCREMENT `practice_record`
--
ALTER TABLE `practice_record`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '练习记录ID', AUTO_INCREMENT=12;

--
-- 使用表AUTO_INCREMENT `practice_stats`
--
ALTER TABLE `practice_stats`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '统计ID', AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `resources`
--
ALTER TABLE `resources`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- 使用表AUTO_INCREMENT `students`
--
ALTER TABLE `students`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '学员ID', AUTO_INCREMENT=11;

--
-- 使用表AUTO_INCREMENT `study_logs`
--
ALTER TABLE `study_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID', AUTO_INCREMENT=10;

--
-- 使用表AUTO_INCREMENT `study_records`
--
ALTER TABLE `study_records`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID', AUTO_INCREMENT=10;

-- --------------------------------------------------------

--
-- 视图结构 `v_study_records_detail`
--
DROP TABLE IF EXISTS `v_study_records_detail`;

CREATE ALGORITHM=UNDEFINED DEFINER=`test`@`%` SQL SECURITY DEFINER VIEW `v_study_records_detail`  AS SELECT `sr`.`id` AS `id`, `sr`.`user_id` AS `user_id`, `s`.`name` AS `student_name`, `d`.`name` AS `department_name`, `sr`.`course_id` AS `course_id`, `c`.`name` AS `course_name`, `sr`.`lesson_id` AS `lesson_id`, `sr`.`resource_id` AS `resource_id`, `sr`.`resource_type` AS `resource_type`, `sr`.`progress` AS `progress`, `sr`.`duration` AS `duration`, `sr`.`completed` AS `completed`, `sr`.`last_position` AS `last_position`, `sr`.`last_study_time` AS `last_study_time`, `sr`.`created_at` AS `created_at`, `sr`.`updated_at` AS `updated_at` FROM (((`study_records` `sr` left join `students` `s` on((`sr`.`user_id` = `s`.`id`))) left join `departments` `d` on((`s`.`department_id` = `d`.`id`))) left join `courses` `c` on((`sr`.`course_id` = `c`.`id`))) ;

--
-- 限制导出的表
--

--
-- 限制表 `admins`
--
ALTER TABLE `admins`
  ADD CONSTRAINT `admins_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;

--
-- 限制表 `admin_permissions`
--
ALTER TABLE `admin_permissions`
  ADD CONSTRAINT `admin_permissions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `admin_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- 限制表 `departments`
--
ALTER TABLE `departments`
  ADD CONSTRAINT `departments_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;

--
-- 限制表 `forum_categories`
--
ALTER TABLE `forum_categories`
  ADD CONSTRAINT `forum_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `forum_categories` (`id`) ON DELETE SET NULL;

--
-- 限制表 `forum_comments`
--
ALTER TABLE `forum_comments`
  ADD CONSTRAINT `forum_comments_ibfk_1` FOREIGN KEY (`post_id`) REFERENCES `forum_posts` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `forum_comments_ibfk_2` FOREIGN KEY (`parent_id`) REFERENCES `forum_comments` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `forum_comments_ibfk_3` FOREIGN KEY (`author_id`) REFERENCES `students` (`id`) ON DELETE CASCADE;

--
-- 限制表 `forum_posts`
--
ALTER TABLE `forum_posts`
  ADD CONSTRAINT `forum_posts_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `forum_categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `forum_posts_ibfk_2` FOREIGN KEY (`author_id`) REFERENCES `students` (`id`) ON DELETE CASCADE;

--
-- 限制表 `forum_violations`
--
ALTER TABLE `forum_violations`
  ADD CONSTRAINT `forum_violations_ibfk_1` FOREIGN KEY (`reporter_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `forum_violations_ibfk_2` FOREIGN KEY (`operator_id`) REFERENCES `admins` (`id`) ON DELETE SET NULL;

--
-- 限制表 `practice_answer`
--
ALTER TABLE `practice_answer`
  ADD CONSTRAINT `fk_practice_answer_question` FOREIGN KEY (`question_id`) REFERENCES `exam_question` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_practice_answer_record` FOREIGN KEY (`record_id`) REFERENCES `practice_record` (`id`) ON DELETE CASCADE;

--
-- 限制表 `practice_record`
--
ALTER TABLE `practice_record`
  ADD CONSTRAINT `fk_practice_record_bank` FOREIGN KEY (`bank_id`) REFERENCES `exam_bank` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_practice_record_user` FOREIGN KEY (`user_id`) REFERENCES `students` (`id`) ON DELETE CASCADE;

--
-- 限制表 `practice_stats`
--
ALTER TABLE `practice_stats`
  ADD CONSTRAINT `fk_practice_stats_bank` FOREIGN KEY (`bank_id`) REFERENCES `exam_bank` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_practice_stats_user` FOREIGN KEY (`user_id`) REFERENCES `students` (`id`) ON DELETE CASCADE;

--
-- 限制表 `students`
--
ALTER TABLE `students`
  ADD CONSTRAINT `students_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;

--
-- 限制表 `study_logs`
--
ALTER TABLE `study_logs`
  ADD CONSTRAINT `fk_study_logs_record` FOREIGN KEY (`record_id`) REFERENCES `study_records` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
