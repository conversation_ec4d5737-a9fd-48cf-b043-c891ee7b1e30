<template>
  <view class="login-container">
    <!-- 渐变背景 -->
    <view class="background-gradient"></view>
    <view class="background-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 状态栏占位 -->
    <view class="status-bar-placeholder"></view>

    <!-- 顶部Logo区域 -->
    <view class="logo-section">
      <view class="logo-container">
        <up-avatar :src="logoUrl" size="70" shape="circle" class="logo-avatar"></up-avatar>
        <view class="logo-shine"></view>
      </view>
      <view class="app-info">
        <text class="app-name">成远教育</text>
        <text class="app-desc">企业培训学习平台</text>
      </view>
    </view>

    <!-- 登录表单卡片 -->
    <view class="form-card">
      <view class="card-background"></view>
      <view class="form-content">
        <view class="form-main">
          <view class="welcome-section">
            <text class="welcome-title">欢迎登录</text>
            <text class="welcome-subtitle">请输入您的账号信息</text>
          </view>

          <up-form :model="loginForm" ref="uForm">
            <view class="input-group">
              <view class="input-wrapper">
                <view class="input-icon">
                  <up-icon name="account-fill" color="#667eea" size="20"></up-icon>
                </view>
                <up-input
                    v-model="loginForm.username"
                    placeholder="请输入工号"
                    clearable
                    fontSize="16"
                    border="none"
                    class="custom-input"
                ></up-input>
              </view>
            </view>
            
            <view class="input-group">
              <view class="input-wrapper">
                <view class="input-icon">
                  <up-icon name="lock-fill" color="#667eea" size="20"></up-icon>
                </view>
                <up-input
                    v-model="loginForm.password"
                    placeholder="请输入密码"
                    type="password"
                    clearable
                    fontSize="16"
                    border="none"
                    class="custom-input"
                ></up-input>
              </view>
            </view>
          </up-form>

          <view class="form-options">
            <view class="remember-section">
              <up-checkbox 
                v-model="rememberPassword" 
                activeColor="#667eea"
                size="16"
              ></up-checkbox>
              <text class="remember-text">记住密码</text>
            </view>
            <view class="forgot-password" @click="goToForgotPassword">
              <text class="forgot-text">忘记密码？</text>
            </view>
          </view>
        </view>

        <view class="login-button-container">
          <up-button
              type="primary"
              text="登 录"
              size="large"
              :loading="loading"
              @click="handleLogin"
              class="login-button"
          ></up-button>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration">
      <text class="copyright">© 2024 成远教育科技有限公司</text>
    </view>

    <!-- 协议弹窗 -->
    <up-modal
        :show="showAgreement"
        title="用户协议和隐私政策"
        :showCancelButton="true"
        @confirm="agreeTerms"
        @cancel="closeAgreement"
        confirmText="同意"
        cancelText="不同意"
        :customStyle="modalStyle"
    >
      <scroll-view scroll-y style="max-height: 40vh; padding: 0 10px;">
        <text class="agreement-text" space="em">
          欢迎使用成远教育学习平台！

          为了更好地保护您的权益，请您仔细阅读以下条款：

          一、用户协议
          1. 本应用由成远教育科技有限公司提供
          2. 用户应当如实提供注册信息
          3. 用户应当妥善保管账号和密码
          4. 禁止恶意攻击和破坏系统行为

          二、隐私政策
          1. 我们重视您的隐私保护
          2. 仅收集必要的个人信息
          3. 不会向第三方泄露您的信息
          4. 您可以随时查看和管理个人信息

          三、免责声明
          1. 因网络原因导致的服务中断
          2. 因用户操作不当造成的损失
          3. 不可抗力因素导致的问题

          如您同意以上条款，请点击"同意"继续使用。
        </text>
      </scroll-view>
    </up-modal>
  </view>
</template>

<script>
import {login, getUserInfo} from '@/api/user'
import {checkAuth, setToken, redirectToHome} from '@/utils/auth'

export default {
  data() {
    return {
      logoUrl: '/static/logo.png',
      loginForm: {
        username: '',
        password: ''
      },
      rememberPassword: false,
      loading: false,
      showAgreement: false,
      loginButtonStyle: {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '16px',
        height: '50px',
        fontSize: '16px',
        fontWeight: '600',
        color: '#ffffff'
      },
      modalStyle: {
        borderRadius: '16px'
      }
    }
  },
  computed: {
    canLogin() {
      return this.loginForm.username.trim() && this.loginForm.password.trim();
    }
  },
  onReady() {
    // 如果需要，可以在这里设置表单验证规则
    // this.$refs.uForm.setRules(this.rules);
  },
  async onLoad() {
    // 检查是否已经登录
    const isLoggedIn = await checkAuth();
    if (isLoggedIn) {
      // 已登录，直接跳转到首页
      redirectToHome();
      return;
    }
    
    this.checkFirstUse();
    this.loadRememberedLogin();
    // 设置页面背景色
    this.setPageBackground();
  },
  onUnload() {
    // 页面卸载时恢复默认背景
    this.resetPageBackground();
  },
  methods: {
    setPageBackground() {
      // 设置页面背景为渐变色
      try {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.$page) {
          currentPage.$page.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }
      } catch (e) {
        console.log('设置页面背景失败:', e);
      }
    },
    resetPageBackground() {
      // 恢复默认页面背景
      try {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (currentPage && currentPage.$page) {
          currentPage.$page.style.background = '';
        }
      } catch (e) {
        console.log('重置页面背景失败:', e);
      }
    },
    checkFirstUse() {
      const hasAgreed = uni.getStorageSync('hasAgreedTerms');
      if (!hasAgreed) {
        this.showAgreement = true;
      }
    },
    loadRememberedLogin() {
      const remembered = uni.getStorageSync('rememberedLogin');
      if (remembered) {
        this.loginForm.username = remembered.username || '';
        this.loginForm.password = remembered.password || '';
        this.rememberPassword = true;
      }
    },
    goToForgotPassword() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      });
    },
    async handleLogin() {
      if (!this.canLogin || this.loading) return;

      if (this.loginForm.username.length < 3) {
        uni.showToast({title: '用户名至少3位字符', icon: 'none'});
        return;
      }

      if (this.loginForm.password.length < 6) {
        uni.showToast({title: '密码至少6位字符', icon: 'none'});
        return;
      }

      this.loading = true;
      try {
        const res = await login({
          username: this.loginForm.username,
          password: this.loginForm.password
        })
        if (res.token) {
          setToken(res.token)
          const userInfo = await getUserInfo();
          uni.setStorageSync('userInfo', userInfo);
          uni.showToast({title: '登录成功', icon: 'success'});
          redirectToHome();
        }
        if (this.rememberPassword) {
          uni.setStorageSync('rememberedLogin', {
            username: this.loginForm.username,
            password: this.loginForm.password
          });
        } else {
          uni.removeStorageSync('rememberedLogin');
        }
      } catch (e) {
        uni.showToast({title: e.message || '登录失败', icon: 'none'});
      } finally {
        this.loading = false;
      }
    },
    agreeTerms() {
      uni.setStorageSync('hasAgreedTerms', true);
      this.showAgreement = false;
    },
    closeAgreement() {
      // 强制退出或给出提示
      uni.showModal({
        title: '提示',
        content: '您必须同意用户协议和隐私政策才能使用本应用',
        showCancel: false,
        success: () => {
          // 在小程序等环境中，可能需要退出
          // #ifdef MP
          uni.exitMiniProgram();
          // #endif
          // #ifdef APP-PLUS
          plus.runtime.quit();
          // #endif
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* 覆盖全局样式，确保登录页面有正确的背景 */
::v-deep page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.login-container {
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* 渐变背景 */
.background-gradient {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  z-index: -2;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  
  &.circle-1 {
    width: 150px;
    height: 150px;
    top: -75px;
    right: -75px;
    animation: float 6s ease-in-out infinite;
  }
  
  &.circle-2 {
    width: 100px;
    height: 100px;
    top: 25%;
    left: -50px;
    animation: float 8s ease-in-out infinite reverse;
  }
  
  &.circle-3 {
    width: 80px;
    height: 80px;
    bottom: 25%;
    right: 10px;
    animation: float 7s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(8deg);
  }
}

/* 状态栏占位 */
.status-bar-placeholder {
  height: var(--status-bar-height, 44px);
  flex-shrink: 0;
}

/* Logo区域 */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 40px 20px;
  flex-shrink: 0;
}

.logo-container {
  position: relative;
  margin-bottom: 16px;
}

.logo-avatar {
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
}

.logo-shine {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: rotate 3s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.app-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.app-name {
  font-size: 26px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  margin-bottom: 2px;
}

.app-desc {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 表单卡片 */
.form-card {
  flex: 1;
  margin: 0 20px;
  position: relative;
  border-radius: 24px 24px 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子元素不会超出父容器 */
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.form-content {
  position: relative;
  padding: 28px 24px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保内容不会溢出 */
}

/* 表单主体区域 */
.form-main {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.welcome-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 4px;
  display: block;
}

.welcome-subtitle {
  font-size: 14px;
  color: #8e8e93;
  display: block;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 14px;
  flex-shrink: 0;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: #f8faff;
  border: 2px solid #e8f0ff;
  border-radius: 14px;
  padding: 0 16px;
  height: 48px;
  transition: all 0.3s ease;
  
  &:focus-within {
    border-color: #667eea;
    background: white;
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.15);
  }
}

.input-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-input {
  flex: 1;
  font-size: 16px;
  color: #1a1d2e;
  
  ::v-deep .u-input__content {
    background: transparent !important;
  }
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 4px;
  flex-shrink: 0;
}

.remember-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.remember-text {
  font-size: 14px;
  color: #4a5568;
}

.forgot-password {
  padding: 4px 8px;
}

.forgot-text {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

/* 登录按钮 */
.login-button-container {
  margin-top: 12px;
  padding-bottom: 0;
  flex-shrink: 0;
  width: 100%;
}

.login-button {
  width: 100% !important;
  height: 50px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 16px !important;
  border: none !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: white !important;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
  
  /* 确保按钮内容可见 */
  ::v-deep .u-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    color: white !important;
  }
  
  ::v-deep .u-button__text {
    color: white !important;
    font-weight: 600 !important;
  }
}

/* 底部装饰 */
.bottom-decoration {
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.copyright {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 协议文本 */
.agreement-text {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.8;
  white-space: pre-line;
}

/* 响应式调整 */
@media screen and (max-height: 667px) {
  .logo-section {
    padding: 20px 40px 15px;
  }
  
  .app-name {
    font-size: 24px;
  }
  
  .form-content {
    padding: 28px 28px 16px;
  }
  
  .input-wrapper {
    height: 46px;
  }
  
  .welcome-section {
    margin-bottom: 20px;
  }
  
  .welcome-title {
    font-size: 20px;
  }
}

@media screen and (max-height: 600px) {
  .logo-section {
    padding: 15px 40px 10px;
  }
  
  .app-name {
    font-size: 22px;
  }
  
  .form-content {
    padding: 24px 28px 12px;
  }
  
  .welcome-section {
    margin-bottom: 16px;
  }
  
  .input-group {
    margin-bottom: 14px;
  }
  
  .form-options {
    margin-bottom: 20px;
  }
}
</style>
