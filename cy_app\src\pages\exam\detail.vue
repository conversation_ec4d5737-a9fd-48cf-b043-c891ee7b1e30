<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon name="arrow-left" size="12" color="#fff"></up-icon>
        </view>
        <text class="navbar-title">考试详情</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <view v-if="loading" class="loading-state">
        <uni-load-more status="loading"/>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="!examInfo" class="error-state">
        <up-icon name="info-circle" color="#8e8e93" size="48"></up-icon>
        <text class="error-text">考试信息加载失败</text>
        <view class="retry-btn" @click="retryLoad">
          <text class="retry-text">重新加载</text>
        </view>
      </view>

      <!-- 考试信息卡片 -->
      <view v-if="examInfo" class="exam-info-card">
        <view class="exam-header">
          <view class="exam-title-section">
            <text class="exam-title">{{ examInfo.title }}</text>
            <view class="exam-status" :class="getStatusClass(examInfo.status)">
              <text class="status-text">{{ getStatusText(examInfo.status) }}</text>
            </view>
          </view>
        </view>

        <view class="exam-meta-row">
          <view class="meta-item">
            <view class="meta-icon">
              <up-icon name="clock" color="#667eea" size="18"></up-icon>
            </view>
            <view class="meta-content">
              <text class="meta-label">考试时长</text>
              <text class="meta-value">{{ examInfo.duration }}分钟</text>
            </view>
          </view>
          <view class="meta-divider"></view>
          <view class="meta-item">
            <view class="meta-icon">
              <up-icon name="star" color="#f093fb" size="18"></up-icon>
            </view>
            <view class="meta-content">
              <text class="meta-label">总分</text>
              <text class="meta-value">{{ examInfo.paper?.totalScore || 0 }}分</text>
            </view>
          </view>
          <view class="meta-divider"></view>
          <view class="meta-item">
            <view class="meta-icon">
              <up-icon name="checkmark-circle" color="#43e97b" size="18"></up-icon>
            </view>
            <view class="meta-content">
              <text class="meta-label">及格分</text>
              <text class="meta-value">{{ examInfo.paper?.passingScore || 0 }}分</text>
            </view>
          </view>
        </view>

        <!-- 考试时间单独一行 -->
        <view class="exam-time-section">
          <view class="time-item" :class="getTimeStatusClass()">
            <view class="time-icon">
              <up-icon name="calendar" color="#4facfe" size="20"></up-icon>
            </view>
            <view class="time-content">
              <text class="time-label">考试时间</text>
              <view class="time-range">
                <view class="time-block">
                  <text class="time-date">{{ formatDate(examInfo.startTime) }}</text>
                  <text class="time-hour">{{ formatTime(examInfo.startTime) }}</text>
                </view>
                <text class="time-separator">至</text>
                <view class="time-block">
                  <text class="time-date">{{ formatDate(examInfo.endTime) }}</text>
                  <text class="time-hour">{{ formatTime(examInfo.endTime) }}</text>
                </view>
              </view>
              <text class="time-tip">{{ getTimeStatusText() }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 考试说明 -->
      <view v-if="examInfo" class="section-card">
        <view class="section-header">
          <view class="section-icon">
            <up-icon name="info-circle" color="#667eea" size="20"></up-icon>
          </view>
          <text class="section-title">考试说明</text>
        </view>
        <text class="description-text">{{ examInfo.description || '暂无说明' }}</text>
      </view>

      <!-- 题型分布 -->
      <view v-if="examInfo" class="section-card">
        <view class="section-header">
          <view class="section-icon">
            <up-icon name="list" color="#f093fb" size="20"></up-icon>
          </view>
          <text class="section-title">题型分布</text>
        </view>
        <view class="type-grid">
          <view class="type-item" v-for="type in getQuestionTypes()" :key="type.type">
            <view class="type-header">
              <text class="type-name">{{ type.name }}</text>
              <text class="type-score">{{ type.score }}分</text>
            </view>
            <text class="type-count">{{ type.count }}题</text>
          </view>
        </view>
      </view>

      <!-- 考试规则 -->
      <view v-if="examInfo" class="section-card">
        <view class="section-header">
          <view class="section-icon">
            <up-icon name="warning" color="#fa709a" size="20"></up-icon>
          </view>
          <text class="section-title">考试规则</text>
        </view>
        <view class="rules-list">
          <view class="rule-item" v-for="(rule, index) in examRules" :key="index">
            <view class="rule-number">{{ index + 1 }}</view>
            <text class="rule-text">{{ rule }}</text>
          </view>
        </view>
      </view>

      <!-- 参考统计入口 -->
      <view v-if="examInfo && (examInfo.status == 2 || examInfo.status == 3)" class="section-card">
        <view class="section-header">
          <view class="section-icon">
            <up-icon name="file-text" color="#43e97b" size="20"></up-icon>
          </view>
          <text class="section-title">参考统计</text>
        </view>

        <!-- 统计概览 -->
        <view class="stats-overview">
          <view class="overview-item">
            <text class="overview-label">总参与人数</text>
            <text class="overview-value">{{ examStatistics?.totalParticipants || 0 }}</text>
          </view>
          <view class="overview-item">
            <text class="overview-label">已完成人数</text>
            <text class="overview-value">{{ examStatistics?.completedCount || 0 }}</text>
          </view>
          <view class="overview-item">
            <text class="overview-label">平均分</text>
            <text class="overview-value">{{ examStatistics?.averageScore || 0 }}</text>
          </view>
          <view class="overview-item">
            <text class="overview-label">通过率</text>
            <text class="overview-value">{{ examStatistics?.passRate || 0 }}%</text>
          </view>
        </view>

        <!-- 查看详细统计按钮 -->
        <view class="stats-action">
          <view class="stats-btn" @click="viewStatistics">
            <text class="stats-btn-text">查看详细统计</text>
            <up-icon color="#fff" name="arrow-right" size="14"></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view v-if="examInfo" class="action-buttons">
      <view
          v-if="getActionInfo(examInfo).page === 'taking'"
          class="action-btn primary"
          @click="handleAction(examInfo)"
      >
        <up-icon color="#fff" name="edit-pen" size="20"></up-icon>
        <text class="btn-text">{{ getActionInfo(examInfo).text }}</text>
      </view>
      <view
          v-else-if="getActionInfo(examInfo).page === 'result'"
          class="action-btn secondary"
          @click="handleAction(examInfo)"
      >
        <up-icon name="file-text" color="#667eea" size="20"></up-icon>
        <text class="btn-text">{{ getActionInfo(examInfo).text }}</text>
      </view>
      <view
          v-else
          class="action-btn disabled"
      >
        <up-icon color="#8e8e93" name="info-circle" size="20"></up-icon>
        <text class="btn-text" style="color: #6c757d">{{ getActionInfo(examInfo).text }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {getExamDetail, type ExamVO, getExamStats, type ExamStats} from '@/api/exam'
import {onLoad, onUnload} from "@dcloudio/uni-app";

// 考试信息
const examInfo = ref<ExamVO | null>(null)
const examStatistics = ref<ExamStats | null>(null)
const loading = ref(true)
// 考试规则
const examRules = ref<string[]>([])
// 倒计时
const countdown = ref('')

onLoad(async (options) => {
  const examId = options?.id;
  if (!examId) {
    uni.showToast({
      title: '考试ID不能为空',
      icon: 'none'
    })
    loading.value = false
    return
  }

  // 保存考试ID到本地存储，用于重试
  uni.setStorageSync('currentExamId', examId)

  // 加载考试详情
  await loadExamDetail(examId)
  await loadExamStats(examId)

})

// 页面卸载时清理存储和倒计时
onUnload(() => {
  uni.removeStorageSync('currentExamId')
  stopCountdown()
})

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '未开始'
    case 2:
      return '进行中'
    case 3:
      return '已结束'
    default:
      return '草稿'
  }
}

// 获取状态样式类名
const getStatusClass = (status: number) => {
  switch (status) {
    case 1:
      return 'upcoming'
    case 2:
      return 'ongoing'
    case 3:
      return 'completed'
    default:
      return 'draft'
  }
}

// 格式化日期（仅显示日期）
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${month}月${day}日`
  } catch (error) {
    return ''
  }
}

// 格式化时间（仅显示时间）
const formatTime = (dateStr: string) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  } catch (error) {
    return ''
  }
}

// 获取时间状态提示文本
const getTimeStatusText = () => {
  if (!examInfo.value) return '请在规定时间内完成考试'

  const now = new Date()
  const startTime = new Date(examInfo.value.startTime)
  const endTime = new Date(examInfo.value.endTime)

  if (now < startTime) {
    return `考试尚未开始，距离开始还有 ${countdown.value}`
  } else if (now >= startTime && now <= endTime) {
    return `考试进行中，剩余时间 ${countdown.value}`
  } else {
    return '考试已结束，无法参加'
  }
}

// 计算倒计时
const calculateCountdown = () => {
  if (!examInfo.value) return

  const now = new Date()
  const startTime = new Date(examInfo.value.startTime)
  const endTime = new Date(examInfo.value.endTime)

  let targetTime: Date
  if (now < startTime) {
    targetTime = startTime
  } else if (now >= startTime && now <= endTime) {
    targetTime = endTime
  } else {
    countdown.value = ''
    return
  }

  const diff = targetTime.getTime() - now.getTime()
  if (diff <= 0) {
    countdown.value = ''
    return
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)

  if (days > 0) {
    countdown.value = `${days}天${hours}小时`
  } else if (hours > 0) {
    countdown.value = `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    countdown.value = `${minutes}分钟${seconds}秒`
  } else {
    countdown.value = `${seconds}秒`
  }
}

// 启动倒计时
let countdownTimer: number | null = null

const startCountdown = () => {
  calculateCountdown()
  countdownTimer = setInterval(calculateCountdown, 1000)
}

const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 获取时间状态样式类
const getTimeStatusClass = () => {
  if (!examInfo.value) return ''

  const now = new Date()
  const startTime = new Date(examInfo.value.startTime)
  const endTime = new Date(examInfo.value.endTime)

  if (now < startTime) {
    return 'time-upcoming'
  } else if (now >= startTime && now <= endTime) {
    return 'time-ongoing'
  } else {
    return 'time-ended'
  }
}

// 开始考试
const startExam = () => {
  if (!examInfo.value) return

  uni.showModal({
    title: '开始考试',
    content: `您即将开始"${examInfo.value.title}"考试，考试时长${examInfo.value.duration}分钟，确定开始吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: `/pages/exam/taking?examId=${examInfo.value!.id}`
        })
      }
    }
  })
}

// 查看考试结果
const viewResult = () => {
  if (!examInfo.value) return

  uni.navigateTo({
    url: `/pages/exam/result?examId=${examInfo.value.id}`
  })
}

// 查看详细统计
const viewStatistics = () => {
  if (!examInfo.value) return

  uni.navigateTo({
    url: `/pages/exam/statistics?id=${examInfo.value.id}`
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 重新加载
const retryLoad = () => {
  loading.value = true
  examInfo.value = null
  // 重新执行onLoad逻辑
  const examId = uni.getStorageSync('currentExamId')
  if (examId) {
    loadExamDetail(examId)
  }
}

// 加载考试详情
const loadExamDetail = async (examId: string) => {
  try {
    const data = await getExamDetail(Number(examId))
    if (data) {
      examInfo.value = data
      // 设置考试规则 - 在数据加载完成后设置
      examRules.value = [
        `考试时间为${data.duration}分钟`,
        "考试开始后不可中途退出",
        "考试结束后自动提交",
        "请勿作弊，违者将取消考试资格"
      ]
      // 启动倒计时
      startCountdown()
    } else {
      uni.showToast({
        title: '考试信息不存在',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '加载考试信息失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const loadExamStats = async (examId: string) => {
  try {
    const stats = await getExamStats(Number(examId))
    console.log('获取到的考试统计数据:', stats)
    examStatistics.value = stats

    // 检查各个统计数据
    if (stats.scoreDistribution) {
      console.log('分数分布数据:', stats.scoreDistribution)
    }
    if (stats.departmentStats) {
      console.log('部门统计数据:', stats.departmentStats)
    }
  } catch (error) {
    console.error('加载考试统计信息失败:', error)
    uni.showToast({
      title: '加载考试统计信息失败',
      icon: 'none'
    })
  }
}

// 题型分布统计
const getQuestionTypes = () => {
  if (!examInfo.value?.paper || !Array.isArray(examInfo.value.paper.questions)) return []

  const typeMap: Record<string, { name: string; count: number; score: number }> = {}
  const typeNameMap: Record<string, string> = {
    'single': '单选题',
    'multiple': '多选题',
    'judgment': '判断题',
    'judge': '判断题', // 兼容不同的命名
    'fill': '填空题',
    'essay': '简答题'
  }

  examInfo.value.paper.questions.forEach((q: any) => {
    const type = q.question?.type
    if (!typeMap[type]) {
      typeMap[type] = {name: typeNameMap[type] || '未知题型', count: 0, score: 0}
    }
    typeMap[type].count += 1
    typeMap[type].score += q.score || 0
  })

  return Object.entries(typeMap).map(([type, v]) => ({type, ...v}))
}

// 新增统一按钮逻辑
const getActionInfo = (exam: any) => {
// exam.status: 0-草稿,1-未开始,2-进行中,3-已结束
  // exam.currentUserRecord.status: 0-未开始,1-进行中,2-已结束,3-已超时
  const examStatus = exam.status;
  const recordStatus = exam.currentUserRecord?.status;
  if (examStatus === 1) {
    return {text: '查看详情', page: 'detail'};
  }
  if (examStatus === 2) {
    if (!recordStatus || recordStatus === 0) return {text: '立即参加', page: 'taking'};
    if (recordStatus === 1) return {text: '继续作答', page: 'taking'};
    if (recordStatus === 2) return {text: '等待考试结束', page: 'detail'};
    if (recordStatus === 3) return {text: '已超时', page: 'detail'};
  }
  if (examStatus === 3) {
    if (recordStatus === 2) return {text: '查看成绩', page: 'result'};
    if (recordStatus === 3) return {text: '已超时', page: 'detail'};
    if (!recordStatus || recordStatus === 0 || recordStatus === 1) return {text: '未参加', page: 'detail'};
  }
  return {text: '查看', page: 'detail'};
};

const handleAction = (exam: any) => {
  const {page} = getActionInfo(exam);
  if (page === 'taking') {
    uni.navigateTo({url: `/pages/exam/taking?id=${exam.id}`});
  } else if (page === 'result') {
    uni.navigateTo({url: `/pages/exam/result?id=${exam.id}`});
  } else {
    // 详情页本身无需跳转
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/pages/exam/detail.scss';
</style>
