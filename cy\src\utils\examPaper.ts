import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, Table, TableRow, TableCell, WidthType } from 'docx'
import { saveAs } from 'file-saver'

/**
 * 试卷下载选项
 */
export interface ExamPaperOptions {
  includeAnswer: boolean // 是否包含答案
  includeAnalysis: boolean // 是否包含解析
  answerPosition: 'inline' | 'appendix' // 答案位置：题目内 | 附录
}

/**
 * 题目接口
 */
export interface Question {
  id: number
  type: 'single' | 'multiple' | 'judge' | 'fill' | 'essay'
  content: string
  options?: string[]
  answer: string
  analysis?: string
  score: number
}

/**
 * 试卷接口
 */
export interface ExamPaper {
  id: number
  title: string
  description?: string
  duration: number // 考试时长（分钟）
  totalScore: number
  questions: Question[]
  createTime?: string
}

/**
 * 试卷Word生成器
 */
export class ExamPaperGenerator {
  /**
   * 生成试卷Word文档
   */
  static async generatePaper(paper: ExamPaper, options: ExamPaperOptions): Promise<void> {
    const children: any[] = []

    // 试卷标题
    children.push(
      new Paragraph({
        text: paper.title,
        heading: HeadingLevel.TITLE,
        alignment: AlignmentType.CENTER,
        spacing: { after: 400 }
      })
    )

    // 试卷信息
    children.push(
      new Paragraph({
        children: [
          new TextRun({ text: `考试时长：${paper.duration}分钟`, bold: true }),
          new TextRun({ text: `　　总分：${paper.totalScore}分`, bold: true })
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 300 }
      })
    )

    if (paper.description) {
      children.push(
        new Paragraph({
          text: paper.description,
          alignment: AlignmentType.CENTER,
          spacing: { after: 400 }
        })
      )
    }

    // 考生信息表格
    children.push(this.createStudentInfoTable())

    // 注意事项
    children.push(this.createNoticeSection())

    // 按题型分组题目
    const groupedQuestions = this.groupQuestionsByType(paper.questions)

    // 生成题目
    let questionNumber = 1
    for (const [type, questions] of Object.entries(groupedQuestions)) {
      if (questions.length === 0) continue

      // 题型标题
      children.push(
        new Paragraph({
          text: `${this.getTypeTitle(type)}（共${questions.length}题，每题${questions[0].score}分，共${questions.length * questions[0].score}分）`,
          heading: HeadingLevel.HEADING_2,
          spacing: { before: 400, after: 200 }
        })
      )

      // 题目内容
      for (const question of questions) {
        children.push(...this.createQuestionParagraphs(
          question,
          questionNumber++,
          options.includeAnswer && options.answerPosition === 'inline',
          options.includeAnalysis && options.answerPosition === 'inline'
        ))
      }
    }

    // 如果答案在附录，添加答案部分
    if (options.includeAnswer && options.answerPosition === 'appendix') {
      children.push(...this.createAnswerSection(paper.questions, options.includeAnalysis))
    }

    // 创建文档
    const doc = new Document({
      sections: [{
        properties: {
          page: {
            margin: {
              top: 1440, // 2cm
              right: 1440,
              bottom: 1440,
              left: 1440
            }
          }
        },
        children
      }]
    })

    // 生成并下载
    const blob = await Packer.toBlob(doc)
    const filename = `${paper.title}_${new Date().toISOString().slice(0, 10)}.docx`
    saveAs(blob, filename)
  }

  /**
   * 创建考生信息表格
   */
  private static createStudentInfoTable(): Table {
    return new Table({
      width: { size: 100, type: WidthType.PERCENTAGE },
      rows: [
        new TableRow({
          children: [
            new TableCell({
              children: [new Paragraph({ text: "姓名：_______________" })],
              width: { size: 25, type: WidthType.PERCENTAGE }
            }),
            new TableCell({
              children: [new Paragraph({ text: "学号：_______________" })],
              width: { size: 25, type: WidthType.PERCENTAGE }
            }),
            new TableCell({
              children: [new Paragraph({ text: "班级：_______________" })],
              width: { size: 25, type: WidthType.PERCENTAGE }
            }),
            new TableCell({
              children: [new Paragraph({ text: "日期：_______________" })],
              width: { size: 25, type: WidthType.PERCENTAGE }
            })
          ]
        })
      ],
      margins: {
        top: 100,
        bottom: 100,
        left: 100,
        right: 100
      }
    })
  }

  /**
   * 创建注意事项
   */
  private static createNoticeSection(): Paragraph[] {
    return [
      new Paragraph({
        text: "注意事项：",
        heading: HeadingLevel.HEADING_3,
        spacing: { before: 400, after: 200 }
      }),
      new Paragraph({
        text: "1. 请在规定时间内完成答题，超时无效。",
        spacing: { after: 100 }
      }),
      new Paragraph({
        text: "2. 请将答案写在指定位置，字迹清楚。",
        spacing: { after: 100 }
      }),
      new Paragraph({
        text: "3. 考试期间不得交头接耳，不得查阅资料。",
        spacing: { after: 300 }
      })
    ]
  }

  /**
   * 按题型分组题目
   */
  private static groupQuestionsByType(questions: Question[]): Record<string, Question[]> {
    const groups: Record<string, Question[]> = {
      single: [],
      multiple: [],
      judge: [],
      fill: [],
      essay: []
    }

    questions.forEach(question => {
      if (groups[question.type]) {
        groups[question.type].push(question)
      }
    })

    return groups
  }

  /**
   * 获取题型标题
   */
  private static getTypeTitle(type: string): string {
    const titles: Record<string, string> = {
      single: '一、单选题',
      multiple: '二、多选题',
      judge: '三、判断题',
      fill: '四、填空题',
      essay: '五、简答题'
    }
    return titles[type] || '题目'
  }

  /**
   * 创建题目段落
   */
  private static createQuestionParagraphs(
    question: Question,
    number: number,
    includeAnswer: boolean,
    includeAnalysis: boolean
  ): Paragraph[] {
    const paragraphs: Paragraph[] = []

    // 题目内容
    paragraphs.push(
      new Paragraph({
        children: [
          new TextRun({ text: `${number}. `, bold: true }),
          new TextRun({ text: question.content })
        ],
        spacing: { before: 200, after: 100 }
      })
    )

    // 选择题选项
    if (question.options && question.options.length > 0) {
      question.options.forEach((option, index) => {
        const label = String.fromCharCode(65 + index) // A, B, C, D
        paragraphs.push(
          new Paragraph({
            text: `${label}. ${option}`,
            spacing: { after: 50 }
          })
        )
      })
    }

    // 答案（如果需要且在题目内显示）
    if (includeAnswer) {
      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({ text: "答案：", bold: true }),
            new TextRun({ text: question.answer, bold: true, color: "FF0000" })
          ],
          spacing: { before: 100, after: 50 }
        })
      )
    }

    // 解析（如果需要且在题目内显示）
    if (includeAnalysis && question.analysis) {
      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({ text: "解析：", bold: true }),
            new TextRun({ text: question.analysis })
          ],
          spacing: { after: 100 }
        })
      )
    }

    // 答题空间（对于主观题）
    if (question.type === 'fill' || question.type === 'essay') {
      paragraphs.push(
        new Paragraph({
          text: "答：_".repeat(question.type === 'essay' ? 100 : 30),
          spacing: { before: 100, after: 200 }
        })
      )
    }

    return paragraphs
  }

  /**
   * 创建答案附录
   */
  private static createAnswerSection(questions: Question[], includeAnalysis: boolean): Paragraph[] {
    const paragraphs: Paragraph[] = []

    paragraphs.push(
      new Paragraph({
        text: "参考答案",
        heading: HeadingLevel.HEADING_1,
        alignment: AlignmentType.CENTER,
        spacing: { before: 600, after: 400 }
      })
    )

    questions.forEach((question, index) => {
      // 答案
      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({ text: `${index + 1}. `, bold: true }),
            new TextRun({ text: question.answer, bold: true, color: "FF0000" })
          ],
          spacing: { after: 100 }
        })
      )

      // 解析
      if (includeAnalysis && question.analysis) {
        paragraphs.push(
          new Paragraph({
            children: [
              new TextRun({ text: "解析：", bold: true }),
              new TextRun({ text: question.analysis })
            ],
            spacing: { after: 200 }
          })
        )
      }
    })

    return paragraphs
  }
}
