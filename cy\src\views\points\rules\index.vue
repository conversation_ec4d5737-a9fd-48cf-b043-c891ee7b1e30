<template>
  <div class="points-rules">
    <div class="page-container">
      <div class="page-toolbar">
        <div class="toolbar-left">
          <h3>积分规则管理</h3>
        </div>
        <div class="toolbar-right">
          <el-button type="primary" :icon="Plus" @click="handleAdd">新增规则</el-button>
          <el-button :icon="Histogram" @click="openStatistics">规则统计</el-button>
          <el-button :icon="Download" @click="handleExport">导出规则</el-button>
          <el-button :icon="Refresh" @click="refreshList">刷新</el-button>
        </div>
      </div>

      <!-- 过滤栏 -->
      <el-card shadow="never" style="margin-top: 20px;">
        <el-form :inline="true" :model="queryParams" size="small">
          <el-form-item label="规则分类">
            <el-select v-model="queryParams.category" placeholder="全部分类" clearable style="width: 180px;">
              <el-option
                v-for="item in categoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="queryParams.status" placeholder="全部状态" clearable style="width: 130px;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input
              v-model="queryParams.keyword"
              placeholder="规则名称/描述"
              style="width: 200px;"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
            <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 数据表格 -->
      <el-card shadow="never" style="margin-top: 20px;">
        <el-table
          v-loading="loading"
          :data="rulesList"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="category" label="分类" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getCategoryTag(row.category)">
                {{ getCategoryName(row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="规则名称" min-width="150" show-overflow-tooltip />
          <el-table-column prop="description" label="规则描述" min-width="250" show-overflow-tooltip />
          <el-table-column prop="code" label="规则标识" width="140" show-overflow-tooltip />
          <el-table-column prop="points" label="积分值" width="80" align="center">
            <template #default="{ row }">
              <span :class="{ 'positive-points': row.points > 0, 'negative-points': row.points < 0 }">
                {{ row.points > 0 ? '+' + row.points : row.points }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="dailyLimit" label="每日上限" width="100" align="center">
            <template #default="{ row }">
              {{ row.dailyLimit === -1 ? '无限制' : row.dailyLimit }}
            </template>
          </el-table-column>
          <el-table-column prop="timesLimit" label="次数上限" width="100" align="center">
            <template #default="{ row }">
              {{ row.timesLimit === -1 ? '无限制' : row.timesLimit }}
            </template>
          </el-table-column>
          <el-table-column prop="usedCount" label="使用次数" width="100" align="center" sortable />
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status ? 'success' : 'danger'">
                {{ row.status ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link :icon="Edit" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button 
                :type="row.status ? 'warning' : 'success'" 
                link 
                :icon="row.status ? Lock : Unlock" 
                @click="handleToggleStatus(row)"
              >
                {{ row.status ? '禁用' : '启用' }}
              </el-button>
              <el-button type="danger" link :icon="Delete" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.page"
            v-model:page-size="queryParams.limit"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 添加/编辑规则弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑规则' : '新增规则'"
      width="550px"
      destroy-on-close
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="规则分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%;">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则标识" prop="code">
          <el-input v-model="form.code" placeholder="请输入规则标识（英文字母和下划线）" />
        </el-form-item>
        <el-form-item label="规则描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入规则描述" />
        </el-form-item>
        <el-form-item label="积分值" prop="points">
          <el-input-number v-model="form.points" :min="-100" :max="100" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="每日上限" prop="dailyLimit">
          <el-input-number 
            v-model="form.dailyLimit" 
            :min="-1" 
            :max="10000" 
            style="width: 100%;" 
            :controls-position="'right'"
          />
          <div class="form-tip">设置为-1表示无限制</div>
        </el-form-item>
        <el-form-item label="次数上限" prop="timesLimit">
          <el-input-number 
            v-model="form.timesLimit" 
            :min="-1" 
            :max="100" 
            style="width: 100%;" 
            :controls-position="'right'"
          />
          <div class="form-tip">设置为-1表示无限制</div>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 规则统计弹窗 -->
    <el-dialog
      v-model="statisticsVisible"
      title="积分规则统计"
      width="800px"
      destroy-on-close
    >
      <div class="statistics-container">
        <div class="statistics-summary">
          <div class="statistic-card">
            <div class="statistic-title">规则总数</div>
            <div class="statistic-value">{{ calculateTotalRules() }}</div>
          </div>
          <div class="statistic-card">
            <div class="statistic-title">启用规则</div>
            <div class="statistic-value">{{ calculateEnabledRules() }}</div>
          </div>
          <div class="statistic-card">
            <div class="statistic-title">已使用次数</div>
            <div class="statistic-value">{{ calculateTotalUsed() }}</div>
          </div>
          <div class="statistic-card">
            <div class="statistic-title">可获取积分</div>
            <div class="statistic-value positive-points">+{{ calculatePositivePoints() }}</div>
          </div>
          <div class="statistic-card">
            <div class="statistic-title">可扣除积分</div>
            <div class="statistic-value negative-points">{{ calculateNegativePoints() }}</div>
          </div>
        </div>

        <div class="statistics-charts">
          <div class="chart-container">
            <div class="chart-title">分类统计</div>
            <div ref="categoryChartRef" class="chart-content"></div>
          </div>
          <div class="chart-container">
            <div class="chart-title">使用频率</div>
            <div ref="usageChartRef" class="chart-content"></div>
          </div>
        </div>

        <div class="statistics-table">
          <div class="table-title">规则使用排行</div>
          <el-table
            :data="topUsedRules"
            border
            style="width: 100%"
          >
            <el-table-column prop="rank" label="排名" width="80" align="center" />
            <el-table-column prop="name" label="规则名称" min-width="150" />
            <el-table-column prop="category" label="分类" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getCategoryTag(row.category)">
                  {{ getCategoryName(row.category) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="usedCount" label="使用次数" width="120" align="center" />
            <el-table-column prop="points" label="积分值" width="100" align="center">
              <template #default="{ row }">
                <span :class="{ 'positive-points': row.points > 0, 'negative-points': row.points < 0 }">
                  {{ row.points > 0 ? '+' + row.points : row.points }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, onUnmounted, watch } from 'vue'
import {
  Plus,
  Edit,
  Delete,
  Search,
  Refresh,
  Download,
  Lock,
  Unlock,
  Histogram
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import {
  getPointsRuleList,
  getPointsRuleDetail,
  createPointsRule,
  updatePointsRule,
  deletePointsRule,
  batchDeletePointsRules,
  togglePointsRuleStatus,
  getPointsRuleStatistics,
  exportPointsRules,
  type PointsRule,
  type PointsRuleQueryParams,
  type PointsRuleStatistics
} from '@/api/points'

// 规则数据类型
interface RuleData extends PointsRule {
  id: string
}

// 分类选项
const categoryOptions = [
  { label: '登录积分', value: 'login' },
  { label: '学习积分', value: 'learning' },
  { label: '论坛积分', value: 'forum' },
  { label: '考试积分', value: 'exam' },
  { label: '其他', value: 'other' }
]

// 查询参数
const queryParams = reactive<PointsRuleQueryParams>({
  category: undefined,
  status: undefined,
  keyword: '',
  page: 1,
  limit: 10
})

// 表单数据
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const form = reactive<Partial<RuleData>>({
  id: undefined,
  category: undefined,
  name: '',
  code: '',
  description: '',
  points: 5,
  dailyLimit: 50,
  timesLimit: 3,
  status: 1
})

// 表单验证规则
const rules = {
  category: [{ required: true, message: '请选择规则分类', trigger: 'change' }],
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  code: [
    { required: true, message: '请输入规则标识', trigger: 'blur' },
    { pattern: /^[a-zA-Z_]+$/, message: '规则标识只能包含英文字母和下划线', trigger: 'blur' }
  ],
  description: [{ required: true, message: '请输入规则描述', trigger: 'blur' }],
  points: [{ required: true, message: '请输入积分值', trigger: 'blur' }]
}

// 统计弹窗
const statisticsVisible = ref(false)

// 数据相关
const loading = ref(false)
const rulesList = ref<RuleData[]>([])
const selectedIds = ref<string[]>([])
const total = ref(0)

// 统计数据
const ruleStatistics = ref<PointsRuleStatistics>({
  totalRules: 0,
  enabledRules: 0,
  totalUsed: 0,
  positivePoints: 0,
  negativePoints: 0,
  categoryDistribution: [],
  usageRanking: []
})

// 图表相关
const categoryChartRef = ref<HTMLElement>()
const usageChartRef = ref<HTMLElement>()
let categoryChart: echarts.ECharts | null = null
let usageChart: echarts.ECharts | null = null

// 初始化分类统计图表
const initCategoryChart = () => {
  if (!categoryChartRef.value) {
    console.log('categoryChartRef 不存在')
    return
  }
  
  // 销毁已存在的图表实例
  if (categoryChart) {
    categoryChart.dispose()
  }
  
  categoryChart = echarts.init(categoryChartRef.value)
  
  const categoryData = ruleStatistics.value.categoryDistribution || []
  console.log('分类统计数据:', categoryData)
  
  if (categoryData.length === 0) {
    // 显示空数据状态
    const option = {
      title: {
        text: '分类分布',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 16,
          fill: '#999'
        }
      }
    }
    categoryChart.setOption(option)
    return
  }
  
  const option = {
    title: {
      text: '分类分布',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: categoryData.map(item => getCategoryName(item.category))
    },
    series: [
      {
        name: '规则分类',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: categoryData.map(item => ({
          value: item.count,
          name: getCategoryName(item.category)
        }))
      }
    ]
  }
  
  categoryChart.setOption(option)
}

// 初始化使用频率图表
const initUsageChart = () => {
  if (!usageChartRef.value) {
    console.log('usageChartRef 不存在')
    return
  }
  
  // 销毁已存在的图表实例
  if (usageChart) {
    usageChart.dispose()
  }
  
  usageChart = echarts.init(usageChartRef.value)
  
  const usageData = ruleStatistics.value.usageRanking?.slice(0, 10) || []
  console.log('使用频率数据:', usageData)
  
  if (usageData.length === 0) {
    // 显示空数据状态
    const option = {
      title: {
        text: '使用频率排行',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 16,
          fill: '#999'
        }
      }
    }
    usageChart.setOption(option)
    return
  }
  
  const option = {
    title: {
      text: '使用频率排行',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: usageData.map(item => item.name).reverse()
    },
    series: [
      {
        name: '使用次数',
        type: 'bar',
        data: usageData.map(item => item.usedCount).reverse(),
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }
  
  usageChart.setOption(option)
}

// 更新图表数据
const updateCharts = () => {
  nextTick(() => {
    initCategoryChart()
    initUsageChart()
  })
}

// 获取规则列表
const fetchRulesList = async () => {
  loading.value = true
  try {
    const data = await getPointsRuleList(queryParams)
    rulesList.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取规则列表失败:', error)
    ElMessage.error('获取规则列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    console.log('开始获取积分规则统计数据...')
    const data = await getPointsRuleStatistics()
    console.log('收到的统计数据:', data)
    
    // 确保数据结构完整，提供默认值
    ruleStatistics.value = {
      totalRules: data?.totalRules || 0,
      enabledRules: data?.enabledRules || 0,
      totalUsed: data?.totalUsed || 0,
      positivePoints: data?.positivePoints || 0,
      negativePoints: data?.negativePoints || 0,
      categoryDistribution: data?.categoryDistribution || [],
      usageRanking: data?.usageRanking || []
    }
    
    console.log('处理后的统计数据:', ruleStatistics.value)
    
    // 更新图表
    updateCharts()
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
    // 保持默认值，不修改 ruleStatistics
  }
}

// 规则使用排行
const topUsedRules = computed(() => {
  // 添加安全检查，确保 usageRanking 存在且为数组
  const usageRanking = ruleStatistics.value?.usageRanking || []
  return usageRanking.slice(0, 5).map((rule, index) => ({
    ...rule,
    rank: index + 1
  }))
})

// 计算统计数据
const calculateTotalRules = () => ruleStatistics.value?.totalRules || 0
const calculateEnabledRules = () => ruleStatistics.value?.enabledRules || 0
const calculateTotalUsed = () => ruleStatistics.value?.totalUsed || 0
const calculatePositivePoints = () => ruleStatistics.value?.positivePoints || 0
const calculateNegativePoints = () => ruleStatistics.value?.negativePoints || 0

// 获取分类名称
const getCategoryName = (category: string) => {
  const found = categoryOptions.find(item => item.value === category)
  return found ? found.label : '未知'
}

// 获取分类标签类型
const getCategoryTag = (category: string) => {
  const typeMap: Record<string, string> = {
    'login': 'primary',
    'learning': 'success',
    'forum': 'warning',
    'exam': 'danger',
    'other': 'info'
  }
  return typeMap[category] || 'default'
}

// 处理查询
const handleQuery = () => {
  queryParams.page = 1
  fetchRulesList()
}

// 重置查询
const resetQuery = () => {
  Object.assign(queryParams, {
    category: undefined,
    status: undefined,
    keyword: '',
    page: 1,
    limit: 10
  })
  fetchRulesList()
}

// 刷新列表
const refreshList = () => {
  fetchRulesList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  queryParams.page = 1
  fetchRulesList()
}

const handleCurrentChange = (val: number) => {
  queryParams.page = val
  fetchRulesList()
}

// 选择处理
const handleSelectionChange = (selection: RuleData[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 添加规则
const handleAdd = () => {
  isEdit.value = false
  Object.assign(form, {
    id: undefined,
    category: undefined,
    name: '',
    code: '',
    description: '',
    points: 5,
    dailyLimit: 50,
    timesLimit: 3,
    status: 1
  })
  dialogVisible.value = true
}

// 编辑规则
const handleEdit = (row: RuleData) => {
  isEdit.value = true
  Object.assign(form, {
    id: row.id,
    category: row.category,
    name: row.name,
    code: row.code,
    description: row.description,
    points: row.points,
    dailyLimit: row.dailyLimit,
    timesLimit: row.timesLimit,
    status: row.status
  })
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await updatePointsRule(form.id!, form)
      ElMessage.success('规则更新成功')
    } else {
      await createPointsRule(form as any)
      ElMessage.success('规则创建成功')
    }
    
    dialogVisible.value = false
    fetchRulesList()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 切换状态
const handleToggleStatus = async (row: RuleData) => {
  const statusText = row.status ? '禁用' : '启用'
  
  try {
    await ElMessageBox.confirm(`确定要${statusText}该规则吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const newStatus = row.status === 1 ? 0 : 1
    await togglePointsRuleStatus(row.id, newStatus)
    
    ElMessage.success(`规则已${statusText}`)
    fetchRulesList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态切换失败:', error)
      ElMessage.error('状态切换失败')
    }
  }
}

// 删除规则
const handleDelete = async (row: RuleData) => {
  try {
    await ElMessageBox.confirm('确定要删除该规则吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deletePointsRule(row.id)
    ElMessage.success('规则删除成功')
    fetchRulesList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 统计相关
const openStatistics = async () => {
  console.log('打开统计对话框...')
  statisticsVisible.value = true
  
  // 获取最新统计数据
  await fetchStatistics()
  
  // 确保对话框和图表容器已经渲染完成
  await nextTick()
  setTimeout(() => {
    console.log('延迟初始化图表...')
    updateCharts()
  }, 100)
}

// 导出规则
const handleExport = async () => {
  try {
    const blob = await exportPointsRules(queryParams)
    
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `积分规则_${new Date().toLocaleDateString()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchRulesList()
  fetchStatistics()
})

// 组件卸载时销毁图表
onUnmounted(() => {
  if (categoryChart) {
    categoryChart.dispose()
    categoryChart = null
  }
  if (usageChart) {
    usageChart.dispose()
    usageChart = null
  }
})

// 监听统计数据变化，更新图表
watch(() => ruleStatistics.value, () => {
  if (statisticsVisible.value) {
    updateCharts()
  }
}, { deep: true })

// 监听统计弹窗显示状态
watch(statisticsVisible, (visible) => {
  if (visible) {
    nextTick(() => {
      updateCharts()
      
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize)
    })
  } else {
    // 移除监听器
    window.removeEventListener('resize', handleResize)
  }
})

// 处理窗口大小变化
const handleResize = () => {
  if (categoryChart) {
    categoryChart.resize()
  }
  if (usageChart) {
    usageChart.resize()
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.positive-points {
  color: #67c23a;
  font-weight: bold;
}

.negative-points {
  color: #f56c6c;
  font-weight: bold;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 统计样式 */
.statistics-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.statistics-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.statistic-card {
  flex: 1;
  min-width: 120px;
  text-align: center;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.statistic-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.statistics-charts {
  display: flex;
  gap: 16px;
}

.chart-container {
  flex: 1;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
}

.chart-content {
  height: 300px;
  width: 100%;
}

.statistics-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
}
</style> 