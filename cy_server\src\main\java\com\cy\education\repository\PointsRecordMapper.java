package com.cy.education.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.PointsRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 积分记录数据访问层
 */
@Mapper
public interface PointsRecordMapper extends BaseMapper<PointsRecord> {

    /**
     * 获取用户当日某规则的使用次数
     *
     * @param userId 用户ID
     * @param type 积分类型
     * @param description 描述（包含规则标识）
     * @param startTime 开始时间（当天凌晨）
     * @param endTime 结束时间（当天结束）
     * @return 使用次数
     */
    @Select("SELECT COUNT(*) FROM points_record WHERE user_id = #{userId} AND type = #{type} " +
            "AND description LIKE CONCAT('%', #{description}, '%') " +
            "AND created_at BETWEEN #{startTime} AND #{endTime}")
    int countDailyUsage(@Param("userId") Integer userId,
                        @Param("type") String type,
                        @Param("description") String description,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取用户最新的积分记录
     *
     * @param userId 用户ID
     * @return 积分记录
     */
    @Select("SELECT * FROM points_record WHERE user_id = #{userId} ORDER BY created_at DESC LIMIT 1")
    PointsRecord getLatestRecord(@Param("userId") Integer userId);

    /**
     * 获取积分统计数据
     *
     * @return 统计数据
     */
    @Select("SELECT " +
            "COUNT(DISTINCT user_id) as totalUsers, " +
            "SUM(CASE WHEN points > 0 THEN points ELSE 0 END) as earnedPoints, " +
            "SUM(CASE WHEN points < 0 THEN ABS(points) ELSE 0 END) as spentPoints " +
            "FROM points_record")
    Map<String, Object> getPointsStatistics();

    /**
     * 分页查询积分记录，并关联学生表获取学生姓名
     *
     * @param page 分页对象
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    @Select("SELECT pr.*, s.name AS userName " +
            "FROM points_record pr " +
            "LEFT JOIN students s ON pr.user_id = s.id " +
            "${ew.customSqlSegment}")
    IPage<PointsRecord> selectPointsRecordPage(
        Page<PointsRecord> page,
        @Param(Constants.WRAPPER) Wrapper<PointsRecord> queryWrapper
    );

    /**
     * 获取积分排名前10的用户
     *
     * @return 用户积分排名列表
     */
    @Select("SELECT pr.user_id as userId, s.name as userName, pr.balance as points " +
           "FROM points_record pr " +
           "LEFT JOIN students s ON pr.user_id = s.id " +
           "WHERE pr.id IN (SELECT MAX(id) FROM points_record GROUP BY user_id) " +
           "ORDER BY pr.balance DESC " +
           "LIMIT 10")
    List<Map<String, Object>> getTopUsersWithPoints();
}
