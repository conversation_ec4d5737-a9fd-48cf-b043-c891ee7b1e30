<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">发布帖子</text>
        <view class="placeholder"/>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-area" scroll-y>
      <!-- 分类选择（up-picker） -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">选择分类</text>
        </view>
        <view class="input-wrapper" @click="showCategoryPicker = true">
          <input
              :value="selectedCategoryName"
              class="title-input"
              placeholder="请选择分类"
              readonly
              style="color: #333; cursor: pointer;"
          />
        </view>
        <up-picker
            ref="uPickerRef"
            :columns="columns"
            :show="showCategoryPicker"
            title="选择分类"
            @change="changeHandler"
            @close="showCategoryPicker = false"
            @confirm="confirm"
        />
      </view>
      <!-- 标题输入 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">帖子标题</text>
          <text class="title-count">{{ postData.title.length }}/50</text>
        </view>
        <view class="input-wrapper">
          <input
              v-model="postData.title"
              class="title-input"
              maxlength="50"
              placeholder="请输入帖子标题..."
          />
        </view>
      </view>
      <!-- 内容输入 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">帖子内容</text>
          <text class="title-count">{{ postData.content.length }}/2000</text>
        </view>
        <view class="input-wrapper">
          <textarea
              v-model="postData.content"
              auto-height
              class="content-textarea"
              maxlength="2000"
              placeholder="请输入帖子内容..."
          />
        </view>
      </view>
      <!-- 图片上传 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">添加图片</text>
          <text class="title-count">{{ postData.images.length }}/9</text>
        </view>
        <view class="image-upload">
          <view
              v-for="(image, index) in postData.images"
              :key="index"
              class="image-item"
          >
            <up-image
                :src="image"
                class="uploaded-image"
                height="100"
                mode="aspectFill"
                width="100"
            ></up-image>
            <view class="image-delete" @click="removeImage(index)">
              <up-icon color="#fff" name="close" size="12"></up-icon>
            </view>
          </view>
          <view
              v-if="postData.images.length < 9"
              class="add-image"
              @click="addImage"
          >
            <up-icon color="#909399" name="plus" size="24"></up-icon>
            <text class="add-text">添加图片</text>
          </view>
        </view>
      </view>
    </scroll-view>
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-buttons">
        <view class="action-btn" @click="saveDraft">
          <up-icon color="#909399" name="checkmark" size="18"></up-icon>
          <text class="action-text">保存</text>
        </view>
        <view class="action-btn" @click="previewPost">
          <up-icon color="#909399" name="eye" size="18"></up-icon>
          <text class="action-text">预览</text>
        </view>
        <view
            :class="{disabled: !canPublish || loading}"
            :style="(canPublish && !loading) ? 'background: linear-gradient(90deg, #667eea 0%, #5a8dee 100%); color: #fff;' : 'background: #f0f0f0; color: #bbb;'"
            class="action-btn publish-btn"
            @click="canPublish && !loading && publishPost()"
        >
          <up-icon :color="(canPublish && !loading) ? '#fff' : '#bbb'" name="checkmark" size="18"></up-icon>
          <text :style="(canPublish && !loading) ? 'color: #fff;' : 'color: #bbb;'" class="action-text">
            {{ loading ? '发布中...' : '发布' }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {getCategoryList, createPost} from '@/api/forum'
import {ref} from 'vue';
import {getImageSignature, uploadToOss} from '@/api/oss'

export default {
  data() {
    return {
      // 表单数据
      postData: {
        title: '',
        content: '',
        categoryId: null,
        images: [],
        tags: []
      },
      // 分类相关
      categoryList: [], // 原始树形数据
      columns: [[], []], // up-picker二维数组
      columnData: [],    // 每个一级分类下的二级分类数组
      categoryMap: {},   // name->id映射
      showCategoryPicker: false,
      selectedCategoryName: '',
      uPickerRef: ref(null),
      // 新标签输入
      newTag: '',
      // 页面状态
      loading: false
    }
  },

  computed: {
    // 是否可以发布
    canPublish() {
      return this.postData.title.trim() &&
          this.postData.content.trim() &&
          this.postData.categoryId !== null;
    }
  },

  onLoad() {
    this.loadCategories();
    this.loadDraft();
  },

  methods: {
    // 页面导航
    goBack() {
      uni.navigateBack();
    },
    // 加载分类列表并处理为二维数组
    async loadCategories() {
      try {
        const categories = await getCategoryList();
        this.categoryList = categories;
        this.processCategoryData(categories);
      } catch (error) {
        console.error('加载分类失败:', error);
        uni.showToast({title: '加载分类失败', icon: 'none'});
      }
    },
    // 处理树形分类为二维数组columns和columnData
    processCategoryData(categories) {
      const firstCol = categories.map(cat => cat.name);
      const secondColArr = categories.map(cat =>
          Array.isArray(cat.children) && cat.children.length > 0
              ? cat.children.map(child => child.name)
              : []
      );
      // 建立name-id映射
      this.categoryMap = {};
      categories.forEach((cat, i) => {
        this.categoryMap[cat.name] = cat.id;
        if (cat.children) {
          cat.children.forEach(child => {
            this.categoryMap[child.name] = child.id;
          });
        }
      });
      this.columns = [firstCol, secondColArr[0] || []];
      this.columnData = secondColArr;
    },
    // up-picker联动
    changeHandler(e) {
      const {columnIndex, index} = e;
      console.log(e)
      if (columnIndex === 0) {
        // 切换一级分类，更新第二列
        this.$refs.uPickerRef.setColumnValues(1, this.columnData[index]);
      }
    },
    // 选择分类后回调
    confirm(e) {
      const names = e.value.filter(Boolean); // 过滤掉 undefined
      const lastName = names[names.length - 1];
      this.selectedCategoryName = lastName;
      this.postData.categoryId = this.categoryMap[lastName];
      this.showCategoryPicker = false;
    },

    // 添加图片
    async addImage() {
      uni.chooseImage({
        count: 9 - this.postData.images.length,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          const isH5 = typeof window !== 'undefined' && !!window.File;
          const files = isH5 ? res.tempFiles : res.tempFilePaths;
          if (!files.length) return;
          uni.showLoading({title: '上传中...'});
          try {
            for (let i = 0; i < files.length; i++) {
              let fileObj;
              if (isH5) {
                // H5端直接用File对象
                fileObj = files[i];
              } else {
                // 小程序端，需转为File对象
                const filePath = files[i];
                const fileName = filePath.split('/').pop();
                // 读取为base64并转为File对象
                fileObj = await new Promise((resolve, reject) => {
                  uni.getFileSystemManager().readFile({
                    filePath,
                    encoding: 'base64',
                    success: (res) => {
                      const byteCharacters = atob(res.data);
                      const byteNumbers = new Array(byteCharacters.length);
                      for (let i = 0; i < byteCharacters.length; i++) {
                        byteNumbers[i] = byteCharacters.charCodeAt(i);
                      }
                      const byteArray = new Uint8Array(byteNumbers);
                      const blob = new Blob([byteArray], {type: 'image/jpeg'});
                      const file = new File([blob], fileName, {type: 'image/jpeg'});
                      resolve(file);
                    },
                    fail: reject
                  });
                });
              }
              const signature = await getImageSignature();
              const {url} = await uploadToOss(fileObj, signature);
              this.postData.images.push(url);
            }
            uni.hideLoading();
            uni.showToast({title: '图片上传成功', icon: 'success'});
          } catch (err) {
            console.error('图片上传失败:', err);
            uni.hideLoading();
            uni.showToast({title: '图片上传失败', icon: 'none'});
          }
        }
      });
    },

    // 移除图片
    removeImage(index) {
      this.postData.images.splice(index, 1);
    },

    // 保存草稿
    saveDraft() {
      if (!this.postData.title.trim()) {
        uni.showToast({
          title: '请输入标题',
          icon: 'none'
        });
        return;
      }

      const draft = {
        ...this.postData,
        saveTime: new Date().toISOString()
      };

      uni.setStorageSync('forum_draft', draft);
      uni.showToast({
        title: '草稿已保存',
        icon: 'success'
      });
    },

    // 加载草稿
    loadDraft() {
      const draft = uni.getStorageSync('forum_draft');
      if (draft) {
        uni.showModal({
          title: '发现草稿',
          content: '是否要恢复上次的草稿？',
          success: (res) => {
            if (res.confirm) {
              this.postData = {...draft};
              this.selectedCategoryValues = [draft.categoryId]; // 确保是数组
              this.selectedCategoryName = draft.categoryName; // 确保是字符串
            } else {
              uni.removeStorageSync('forum_draft');
            }
          }
        });
      }
    },

    // 预览帖子
    previewPost() {
      if (!this.canPublish) {
        uni.showToast({
          title: '请完善帖子内容',
          icon: 'none'
        });
        return;
      }

      // 这里可以实现预览功能，暂时显示提示
      uni.showToast({
        title: '预览功能开发中',
        icon: 'none'
      });
    },

    // 发布帖子
    async publishPost() {
      if (!this.canPublish) {
        uni.showToast({
          title: '请完善帖子内容',
          icon: 'none'
        });
        return;
      }

      try {
        this.loading = true;
        uni.showLoading({title: '发布中...'});

        // 上传图片
        const uploadedImages = [];
        if (this.postData.images.length > 0) {
          for (const image of this.postData.images) {
            try {
              uploadedImages.push(image);
            } catch (error) {
              console.error('图片上传失败:', error);
            }
          }
        }

        // 创建帖子
        const postData = {
          title: this.postData.title.trim(),
          content: this.postData.content.trim(),
          categoryId: this.postData.categoryId,
          images: uploadedImages,
          tags: this.postData.tags
        };

        await createPost(postData);

        // 清除草稿
        uni.removeStorageSync('forum_draft');

        uni.hideLoading();
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        });

        // 返回论坛页面
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);

      } catch (error) {
        console.error('发布帖子失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '发布失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.publish-btn {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;

  &.disabled {
    opacity: 0.5;
  }
}

.publish-text {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
}

.content-area {
  flex: 1;
  padding: 16px;
}

.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.title-count {
  font-size: 12px;
  color: #909399;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  background: #fff;

  &.active {
    border-color: #667eea;
    background: #f0f4ff;
  }
}

.category-icon {
  margin-bottom: 8px;
}

.category-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.input-wrapper {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background: #fff;
}

.title-input {
  width: 100%;
  font-size: 16px;
  color: #333;
  border: none;
  outline: none;
}

.content-textarea {
  width: 100%;
  min-height: 120px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  border: none;
  outline: none;
  resize: none;
}

.image-upload {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.image-item {
  position: relative;
  width: 100px;
  height: 100px;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.image-delete {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-image {
  width: 100px;
  height: 100px;
  border: 2px dashed #d0d0d0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.add-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.tag-input-wrapper {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background: #fff;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background: #f0f4ff;
  border-radius: 12px;
}

.tag-text {
  font-size: 12px;
  color: #667eea;
  margin-right: 4px;
}

.tag-delete {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-input-container {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.tag-input {
  width: 100%;
  font-size: 14px;
  color: #333;
  border: none;
  outline: none;
}

.bottom-bar {
  background: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 16px;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
}

.action-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.publish-btn {
  padding: 8px 16px;
  border-radius: 20px;
  transition: background 0.2s, color 0.2s;

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}
</style>
