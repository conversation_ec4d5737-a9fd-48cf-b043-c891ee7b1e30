package com.cy.education.service;

import java.util.Map;

/**
 * 阿里云OSS服务接口
 */
public interface OssService {

    /**
     * 获取OSS直传Policy签名
     *
     * @param directory 指定上传目录，为空则使用默认目录
     * @param fileType  文件类型限制，如image、video、document等，为空则不限制
     * @return 包含签名信息的Map
     */
    Map<String, String> getPostSignature(String directory, String fileType);

    /**
     * 生成OSS文件访问URL
     *
     * @param objectName OSS对象名称
     * @return 可访问的URL
     */
    String generateFileUrl(String objectName);

    /**
     * 删除OSS文件
     *
     * @param objectName OSS对象名称
     * @return 是否删除成功
     */
    boolean deleteFile(String objectName);
}
