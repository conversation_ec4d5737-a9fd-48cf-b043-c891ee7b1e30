﻿import { defineStore } from 'pinia'

interface AppState {
  sidebar: {
    opened: boolean
    withoutAnimation: boolean
  }
  device: 'desktop' | 'mobile'
  size: 'large' | 'default' | 'small'
  theme: 'light' | 'dark'
  loading: boolean
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    sidebar: {
      opened: localStorage.getItem('sidebarStatus') !== '0',
      withoutAnimation: false
    },
    device: 'desktop',
    size: localStorage.getItem('size') as 'large' | 'default' | 'small' || 'default',
    theme: localStorage.getItem('theme') as 'light' | 'dark' || 'light',
    loading: false
  }),
  
  getters: {
    sidebarOpened: (state) => state.sidebar.opened,
    currentSize: (state) => state.size,
    currentTheme: (state) => state.theme,
    isDarkMode: (state) => state.theme === 'dark'
  },
  
  actions: {
    toggleSidebar(withoutAnimation?: boolean) {
      this.sidebar.opened = !this.sidebar.opened
      this.sidebar.withoutAnimation = withoutAnimation || false
      
      if (this.sidebar.opened) {
        localStorage.setItem('sidebarStatus', '1')
      } else {
        localStorage.setItem('sidebarStatus', '0')
      }
    },
    
    closeSidebar(withoutAnimation?: boolean) {
      this.sidebar.opened = false
      this.sidebar.withoutAnimation = withoutAnimation || false
      localStorage.setItem('sidebarStatus', '0')
    },
    
    openSidebar(withoutAnimation?: boolean) {
      this.sidebar.opened = true
      this.sidebar.withoutAnimation = withoutAnimation || false
      localStorage.setItem('sidebarStatus', '1')
    },
    
    toggleDevice(device: 'desktop' | 'mobile') {
      this.device = device
    },
    
    setSize(size: 'large' | 'default' | 'small') {
      this.size = size
      localStorage.setItem('size', size)
      
      // 动态设置Element Plus的组件大小
      document.documentElement.style.setProperty('--el-component-size', size)
    },
    
    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
      localStorage.setItem('theme', this.theme)
      this.applyTheme()
    },
    
    setTheme(theme: 'light' | 'dark') {
      this.theme = theme
      localStorage.setItem('theme', theme)
      this.applyTheme()
    },
    
    applyTheme() {
      // 设置主题相关的CSS变量或类
      if (this.theme === 'dark') {
        document.documentElement.classList.add('dark')
        document.documentElement.classList.remove('light')
      } else {
        document.documentElement.classList.add('light')
        document.documentElement.classList.remove('dark')
      }
    },
    
    setLoading(status: boolean) {
      this.loading = status
    }
  }
})
