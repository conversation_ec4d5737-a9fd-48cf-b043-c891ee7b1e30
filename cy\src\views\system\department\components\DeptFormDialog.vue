<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑部门' : '新增部门'"
    width="500px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="部门名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入部门名称" />
      </el-form-item>
      <el-form-item label="上级部门" prop="parentId">
        <el-tree-select
          v-model="form.parentId"
          :data="departmentTree"
          :props="treeProps"
          placeholder="请选择上级部门"
          check-strictly
          :render-after-expand="false"
          node-key="id"
          :default-expanded-keys="[form.parentId]"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="负责人" prop="leader">
        <el-input v-model="form.leader" placeholder="请输入负责人姓名" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort" :min="0" :max="999" style="width: 100%;" />
      </el-form-item>
      <el-form-item label="状态">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { FormInstance } from 'element-plus'

interface DepartmentData {
  id: number
  name: string
  parentId: number | null
  leader?: string
  sort: number
  status: number
  createTime?: string
  remark?: string
  children?: DepartmentData[]
}

interface DepartmentForm {
  id?: number
  name: string
  parentId: number | null
  leader: string
  sort: number
  status: number
  remark?: string
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  departmentTree: {
    type: Array as () => DepartmentData[],
    required: true
  },
  departmentData: {
    type: Object as () => Partial<DepartmentData>,
    default: () => ({})
  },
  treeProps: {
    type: Object,
    default: () => ({
      children: 'children',
      label: 'name'
    })
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const formRef = ref<FormInstance>()

const form = reactive<DepartmentForm>({
  id: undefined,
  name: '',
  parentId: null,
  leader: '',
  sort: 0,
  status: 1,
  remark: ''
})

const rules = {
  name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
}

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(() => props.departmentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(form, {
      id: newVal.id,
      name: newVal.name || '',
      parentId: newVal.parentId,
      leader: newVal.leader || '',
      sort: newVal.sort || 0,
      status: newVal.status !== undefined ? newVal.status : 1,
      remark: newVal.remark || ''
    })
  }
}, { deep: true })

const handleSubmit = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      emit('submit', { ...form })
      visible.value = false
    }
  })
}

const handleClosed = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    id: undefined,
    name: '',
    parentId: null,
    leader: '',
    sort: 0,
    status: 1,
    remark: ''
  })
}
</script> 