package com.cy.education.controller;

import com.cy.education.model.params.ExamBankParams;
import com.cy.education.model.params.ExamBankQueryParams;
import com.cy.education.model.params.ExamQuestionParams;
import com.cy.education.model.params.ExamQuestionQueryParams;
import com.cy.education.model.params.ExamPaperParams;
import com.cy.education.model.params.ExamPaperQueryParams;
import com.cy.education.model.params.ExamPaperQuestionParams;
import com.cy.education.model.params.AddQuestionsParams;
import com.cy.education.model.params.ExamParams;
import com.cy.education.model.params.ExamQueryParams;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.ExamBankVO;
import com.cy.education.model.vo.ExamPaperVO;
import com.cy.education.model.vo.ExamQuestionVO;
import com.cy.education.model.vo.ExamVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.security.JwtUserDetails;
import com.cy.education.service.ExamBankService;
import com.cy.education.service.ExamPaperService;
import com.cy.education.service.ExamQuestionService;
import com.cy.education.service.ExamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 试题考试相关控制器
 */
@Api(tags = "试题考试相关接口")
@RestController
@RequestMapping("/exam")
@RequiredArgsConstructor
public class ExamController {

    private final ExamBankService examBankService;
    private final ExamQuestionService examQuestionService;
    private final ExamPaperService examPaperService;
    private final ExamService examService;

    // ==================== 题库相关接口 ====================

    @ApiOperation("获取题库列表")
    @GetMapping("/bank/list")
    public ApiResponse<PageResponse<ExamBankVO>> listBanks(ExamBankQueryParams params) {
        return ApiResponse.success(examBankService.listBanks(params));
    }

    @ApiOperation("获取题库详情")
    @GetMapping("/bank/detail/{id}")
    public ApiResponse<ExamBankVO> getBankDetail(@PathVariable Integer id) {
        return ApiResponse.success(examBankService.getBankDetail(id));
    }

    @ApiOperation("创建题库")
    @PostMapping("/bank/create")
    public ApiResponse<Map<String, Object>> createBank(
            @RequestBody @Validated ExamBankParams params,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        Integer id = examBankService.createBank(params, userDetails.getUsername());
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        return ApiResponse.success(result, "题库创建成功");
    }

    @ApiOperation("更新题库")
    @PutMapping("/bank/update/{id}")
    public ApiResponse<Void> updateBank(
            @PathVariable Integer id,
            @RequestBody @Validated ExamBankParams params) {
        examBankService.updateBank(id, params);
        return ApiResponse.success(null, "题库更新成功");
    }

    @ApiOperation("删除题库")
    @DeleteMapping("/bank/delete/{id}")
    public ApiResponse<Void> deleteBank(@PathVariable Integer id) {
        examBankService.deleteBank(id);
        return ApiResponse.success(null, "题库删除成功");
    }

    // ==================== 题目相关接口 ====================

    @ApiOperation("获取题目列表")
    @GetMapping("/question/list")
    public ApiResponse<PageResponse<ExamQuestionVO>> listQuestions(ExamQuestionQueryParams params) {
        return ApiResponse.success(examQuestionService.listQuestions(params));
    }

    @ApiOperation("获取题目详情")
    @GetMapping("/question/detail/{id}")
    public ApiResponse<ExamQuestionVO> getQuestionDetail(@PathVariable Integer id) {
        return ApiResponse.success(examQuestionService.getQuestionDetail(id));
    }

    @ApiOperation("创建题目")
    @PostMapping("/question/create")
    public ApiResponse<Map<String, Object>> createQuestion(
            @RequestBody @Validated ExamQuestionParams params,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        Integer id = examQuestionService.createQuestion(params, userDetails.getUsername());
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        return ApiResponse.success(result, "题目创建成功");
    }

    @ApiOperation("更新题目")
    @PutMapping("/question/update/{id}")
    public ApiResponse<Void> updateQuestion(
            @PathVariable Integer id,
            @RequestBody @Validated ExamQuestionParams params) {
        examQuestionService.updateQuestion(id, params);
        return ApiResponse.success(null, "题目更新成功");
    }

    @ApiOperation("删除题目")
    @DeleteMapping("/question/delete/{id}")
    public ApiResponse<Void> deleteQuestion(@PathVariable Integer id) {
        examQuestionService.deleteQuestion(id);
        return ApiResponse.success(null, "题目删除成功");
    }

    @ApiOperation("批量删除题目")
    @PostMapping("/question/batch-delete")
    public ApiResponse<Void> batchDeleteQuestions(@RequestBody List<Integer> ids) {
        examQuestionService.batchDeleteQuestions(ids);
        return ApiResponse.success(null, "批量删除成功");
    }

    @ApiOperation("下载题目导入模板")
    @GetMapping("/question/import/template")
    public ResponseEntity<Resource> downloadQuestionTemplate() throws IOException {
        Resource resource = examQuestionService.generateImportTemplate();
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=question_template.xlsx")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    @ApiOperation("批量导入题目")
    @PostMapping("/question/import")
    public ApiResponse<Map<String, Object>> importQuestions(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> result = examQuestionService.importQuestions(file);
            return ApiResponse.success(result, "题目导入完成");
        } catch (Exception e) {
            return ApiResponse.error("导入失败: " + e.getMessage());
        }
    }

    @ApiOperation("导出题目列表")
    @PostMapping("/question/export")
    public void exportQuestions(@RequestBody Map<String, Object> params, HttpServletResponse response) throws IOException {
        examQuestionService.exportQuestions(params, response);
    }

    // ==================== 试卷相关接口 ====================

    @ApiOperation("获取试卷列表")
    @GetMapping("/paper/list")
    public ApiResponse<PageResponse<ExamPaperVO>> listPapers(ExamPaperQueryParams params) {
        return ApiResponse.success(examPaperService.listPapers(params));
    }

    @ApiOperation("获取试卷详情")
    @GetMapping("/paper/detail/{id}")
    public ApiResponse<ExamPaperVO> getPaperDetail(@PathVariable Integer id) {
        return ApiResponse.success(examPaperService.getPaperDetail(id));
    }

    @ApiOperation("创建试卷")
    @PostMapping("/paper/create")
    public ApiResponse<Map<String, Object>> createPaper(
            @RequestBody @Validated ExamPaperParams params,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        Integer id = examPaperService.createPaper(params, userDetails.getUsername());
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        return ApiResponse.success(result, "试卷创建成功");
    }

    @ApiOperation("更新试卷")
    @PutMapping("/paper/update/{id}")
    public ApiResponse<Void> updatePaper(
            @PathVariable Integer id,
            @RequestBody @Validated ExamPaperParams params) {
        examPaperService.updatePaper(id, params);
        return ApiResponse.success(null, "试卷更新成功");
    }

    @ApiOperation("删除试卷")
    @DeleteMapping("/paper/delete/{id}")
    public ApiResponse<Void> deletePaper(@PathVariable Integer id) {
        examPaperService.deletePaper(id);
        return ApiResponse.success(null, "试卷删除成功");
    }

    @ApiOperation("发布/取消发布试卷")
    @PostMapping("/paper/publish/{id}")
    public ApiResponse<Void> publishPaper(
            @PathVariable Integer id,
            @RequestBody Map<String, Boolean> params) {
        Boolean isPublished = params.get("isPublished");
        examPaperService.publishPaper(id, isPublished);
        String message = isPublished ? "试卷发布成功" : "试卷取消发布成功";
        return ApiResponse.success(null, message);
    }

    @ApiOperation("添加题目到试卷")
    @PostMapping("/paper/add-questions/{paperId}")
    public ApiResponse<Void> addQuestionsToPaper(
            @PathVariable Integer paperId,
            @RequestBody @Validated AddQuestionsParams params) {
        examPaperService.addQuestionsToPaper(paperId, params.getQuestions());
        return ApiResponse.success(null, "添加题目成功");
    }

    @ApiOperation("从试卷移除题目")
    @DeleteMapping("/paper/remove-question/{paperId}/{questionId}")
    public ApiResponse<Void> removeQuestionFromPaper(
            @PathVariable Integer paperId,
            @PathVariable Integer questionId) {
        examPaperService.removeQuestionFromPaper(paperId, questionId);
        return ApiResponse.success(null, "移除题目成功");
    }

    @ApiOperation("更新试卷题目列表")
    @PostMapping("/paper/update-questions/{paperId}")
    public ApiResponse<Void> updatePaperQuestions(
            @PathVariable Integer paperId,
            @RequestBody @Validated AddQuestionsParams params) {
        examPaperService.updatePaperQuestions(paperId, params.getQuestions());
        return ApiResponse.success(null, "更新试卷题目成功");
    }

    @ApiOperation("导出试卷")
    @PostMapping("/paper/{paperId}/export")
    public void exportPaper(@PathVariable Integer paperId,
                           @RequestBody Map<String, Object> options,
                           HttpServletResponse response) throws IOException {
        examPaperService.exportPaper(paperId, options, response);
    }

    @ApiOperation("批量导出试卷")
    @PostMapping("/paper/batch-export")
    public void batchExportPapers(@RequestBody Map<String, Object> params,
                                 HttpServletResponse response) throws IOException {
        examPaperService.batchExportPapers(params, response);
    }
    
    // ==================== 考试相关接口 ====================

    @ApiOperation("获取考试列表")
    @GetMapping("/list")
    public ApiResponse<PageResponse<ExamVO>> listExams(ExamQueryParams params) {
        return ApiResponse.success(examService.listExams(params));
    }

    @ApiOperation("获取考试详情")
    @GetMapping("/detail/{id}")
    public ApiResponse<ExamVO> getExamDetail(@PathVariable Integer id) {
        return ApiResponse.success(examService.getExamDetail(id));
    }

    @ApiOperation("创建考试")
    @PostMapping("/create")
    public ApiResponse<Map<String, Object>> createExam(
            @RequestBody @Validated ExamParams params,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        Integer id = examService.createExam(params, userDetails.getUsername());
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        return ApiResponse.success(result, "考试创建成功");
    }

    @ApiOperation("更新考试")
    @PutMapping("/update/{id}")
    public ApiResponse<Void> updateExam(
            @PathVariable Integer id,
            @RequestBody @Validated ExamParams params) {
        examService.updateExam(id, params);
        return ApiResponse.success(null, "考试更新成功");
    }

    @ApiOperation("删除考试")
    @DeleteMapping("/delete/{id}")
    public ApiResponse<Void> deleteExam(@PathVariable Integer id) {
        examService.deleteExam(id);
        return ApiResponse.success(null, "考试删除成功");
    }

    @ApiOperation("发布/取消发布考试")
    @PostMapping("/publish/{id}")
    public ApiResponse<Void> publishExam(
            @PathVariable Integer id,
            @RequestBody Map<String, Boolean> params) {
        Boolean isPublished = params.get("isPublished");
        examService.publishExam(id, isPublished);
        String message = isPublished ? "考试发布成功" : "考试取消发布成功";
        return ApiResponse.success(null, message);
    }

    @ApiOperation("更新考试状态")
    @PostMapping("/update-status/{id}")
    public ApiResponse<Void> updateExamStatus(
            @PathVariable Integer id,
            @RequestBody Map<String, Integer> params) {
        Integer status = params.get("status");
        examService.updateExamStatus(id, status);
        String message = "考试状态更新成功";
        return ApiResponse.success(null, message);
    }
} 