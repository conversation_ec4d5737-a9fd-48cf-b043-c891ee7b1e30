import {
  generateId,
  randomDate,
  randomItem,
  randomItems,
  randomInt,
  mockResponse,
  paginateData,
  getQueryParams
} from './utils'
import type {
  Question,
  Paper,
  Exam,
  ExamRecord,
  QuestionType,
  PaperQuestion,
  ExamAnswer,
  Bank
} from '@/api/exam'
import { departmentData } from './department'

// 题目分类
const questionCategories = [
  '安全法规', '操作规程', '应急处理', '设备维护', '职业健康', '环境保护', '基础知识', '案例分析'
]

// 题目类型选项
const questionTypes: QuestionType[] = ['single', 'multiple', 'judgment', 'fill', 'essay']

// 创建人员列表
const creators = ['张老师', '李老师', '王老师', '赵老师', '刘老师']

// 生成模拟题目数据
function generateQuestions(count: number = 100): Question[] {
  const questions: Question[] = []
  const banks = generateBanks()
  
  for (let i = 0; i < count; i++) {
    const type = randomItem(questionTypes)
    const bank = randomItem(banks) // 随机选择题库
    const category = randomItem(questionCategories) // 用于题目内容生成
    
    let options: string[] | undefined
    let correctAnswer: string | string[]
    
    // 根据题目类型生成选项和答案
    switch (type) {
      case 'single':
        options = [`A. 选项A内容${i + 1}`, `B. 选项B内容${i + 1}`, `C. 选项C内容${i + 1}`, `D. 选项D内容${i + 1}`]
        correctAnswer = randomItem(['A', 'B', 'C', 'D'])
        break
      case 'multiple':
        options = [`A. 选项A内容${i + 1}`, `B. 选项B内容${i + 1}`, `C. 选项C内容${i + 1}`, `D. 选项D内容${i + 1}`]
        correctAnswer = randomItems(['A', 'B', 'C', 'D'], randomInt(2, 3))
        break
      case 'judgment':
        correctAnswer = randomItem(['true', 'false'])
        break
      case 'fill':
        correctAnswer = `标准答案${i + 1}`
        break
      case 'essay':
        correctAnswer = `参考答案：这是一个${category}相关的主观题答案示例${i + 1}。答案应该包含关键要点和详细说明。`
        break
    }
    
    const question: Question = {
      id: generateId(),
      title: `${category}相关题目${i + 1}：请选择或填写正确答案`,
      type,
      bankId: bank.id, // 使用题库ID
      options,
      correctAnswer,
      explanation: `这道题考查的是${category}的相关知识点。正确答案的解析说明...`,
      createdAt: randomDate(new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 6, 1)),
      createdBy: randomItem(creators)
    }
    
    questions.push(question)
  }
  
  return questions
}

// 生成试卷题目关联
function generatePaperQuestions(questions: Question[], count: number = 20): PaperQuestion[] {
  const selectedQuestions = randomItems(questions, Math.min(count, questions.length))
  
  return selectedQuestions.map((question, index) => ({
    id: generateId(),
    questionId: question.id,
    question,
    score: getDefaultScore(question.type),
    order: index + 1
  }))
}

// 根据题型获取默认分数
function getDefaultScore(type: string): number {
  switch (type) {
    case 'essay':
      return randomInt(10, 20)
    case 'multiple':
      return randomInt(3, 5)
    case 'single':
    case 'judgment':
    case 'fill':
      return randomInt(1, 3)
    default:
      return 2
  }
}

// 生成模拟试卷数据
function generatePapers(questions: Question[], count: number = 20): Paper[] {
  const papers: Paper[] = []
  
  for (let i = 0; i < count; i++) {
    // 随机选择一个主要题库
    const banks = generateBanks()
    const mainBank = randomItem(banks)
    
    // 选择该题库下的题目，确保每个试卷有合理的题目组合
    const bankQuestions = questions.filter(q => q.bankId === mainBank.id)
    const otherQuestions = questions.filter(q => q.bankId !== mainBank.id)
    
    // 确保试卷有不同类型的题目
    const singleChoiceQuestions = bankQuestions.filter(q => q.type === 'single').slice(0, randomInt(8, 12))
    const multipleChoiceQuestions = bankQuestions.filter(q => q.type === 'multiple').slice(0, randomInt(3, 5))
    const judgmentQuestions = bankQuestions.filter(q => q.type === 'judgment').slice(0, randomInt(5, 8))
    const fillQuestions = bankQuestions.filter(q => q.type === 'fill').slice(0, randomInt(2, 4))
    const essayQuestions = [...bankQuestions, ...otherQuestions]
      .filter(q => q.type === 'essay')
      .slice(0, randomInt(1, 2))
    
    const selectedQuestions = [
      ...singleChoiceQuestions,
      ...multipleChoiceQuestions,
      ...judgmentQuestions,
      ...fillQuestions,
      ...essayQuestions
    ]
    
    const paperQuestions = selectedQuestions.map((question, index) => ({
      id: generateId(),
      questionId: question.id,
      question,
      score: getDefaultScore(question.type),
      order: index + 1
    }))
    
    const totalScore = paperQuestions.reduce((sum, pq) => sum + pq.score, 0)
    
    const paper: Paper = {
      id: generateId(),
      title: `${mainBank.name.replace('题库', '')}综合测试卷${i + 1}`,
      description: `这是一份关于${mainBank.name}的综合测试试卷，包含单选题、多选题、判断题、填空题和论述题等多种题型，全面考查相关知识点。`,
      totalScore,
      passingScore: Math.floor(totalScore * 0.6), // 60%及格
      duration: randomInt(90, 180), // 90-180分钟
      questionCount: paperQuestions.length,
      questions: paperQuestions,
      isPublished: Math.random() > 0.2, // 80%的试卷已发布
      createdAt: randomDate(new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 6, 1)),
      createdBy: randomItem(creators)
    }
    
    papers.push(paper)
  }
  
  return papers
}

// 生成模拟考试数据
function generateExams(papers: Paper[], count: number = 15): Exam[] {
  const exams: Exam[] = []
  const flatDepartments = flattenDepartments(departmentData)
  
  for (let i = 0; i < count; i++) {
    // 只选择已发布的试卷
    const publishedPapers = papers.filter(p => p.isPublished)
    if (publishedPapers.length === 0) continue
    
    const paper = randomItem(publishedPapers)
    const startTime = randomDate(new Date(2023, 8, 1), new Date(2024, 2, 28))
    const endDate = new Date(startTime)
    endDate.setDate(endDate.getDate() + randomInt(7, 30))
    
    const selectedDepartments = randomItems(flatDepartments, randomInt(1, 4))
    const participantCount = randomInt(30, 150)
    const completedCount = randomInt(Math.floor(participantCount * 0.7), participantCount)
    const passedCount = randomInt(Math.floor(completedCount * 0.6), completedCount)
    
    const exam: Exam = {
      id: generateId(),
      title: `${paper.title.replace('综合测试卷', '正式考试')}`,
      description: `基于《${paper.title}》的正式考试，请认真作答，考试时间${paper.duration}分钟。`,
      paperId: paper.id,
      paper,
      startTime: new Date(startTime).toISOString(),
      endTime: endDate.toISOString(),
      duration: paper.duration,
      passingScore: paper.passingScore,
      departmentIds: selectedDepartments.map(d => d.id.toString()),
      departments: selectedDepartments.map(d => d.name),
      maxAttempts: randomInt(1, 3),
      isPublished: Math.random() > 0.1, // 90%的考试已发布
      participantCount,
      completedCount,
      passedCount,
      createdAt: randomDate(new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 6, 1)),
      createdBy: randomItem(creators)
    }
    
    exams.push(exam)
  }
  
  return exams
}

// 生成考试答案
function generateExamAnswers(paperQuestions: PaperQuestion[]): ExamAnswer[] {
  return paperQuestions.map(pq => {
    let answer: string | string[]
    let isCorrect: boolean
    let score: number
    
    const correctAnswer = pq.question.correctAnswer
    
    // 模拟答题情况
    const accuracy = Math.random() // 0-1 正确率
    
    switch (pq.question.type) {
      case 'single':
        if (accuracy > 0.3) {
          answer = correctAnswer as string
          isCorrect = true
          score = pq.score
        } else {
          const options = ['A', 'B', 'C', 'D']
          answer = randomItem(options.filter(opt => opt !== correctAnswer))
          isCorrect = false
          score = 0
        }
        break
        
      case 'multiple':
        if (accuracy > 0.4) {
          answer = correctAnswer as string[]
          isCorrect = true
          score = pq.score
        } else {
          // 部分正确或错误
          const correct = correctAnswer as string[]
          const wrong = ['A', 'B', 'C', 'D'].filter(opt => !correct.includes(opt))
          answer = accuracy > 0.2 ? 
            randomItems([...correct, ...randomItems(wrong, 1)], randomInt(1, 3)) :
            randomItems(wrong, randomInt(1, 2))
          isCorrect = false
          score = accuracy > 0.2 ? Math.floor(pq.score * 0.5) : 0
        }
        break
        
      case 'judgment':
        if (accuracy > 0.5) {
          answer = correctAnswer as string
          isCorrect = true
          score = pq.score
        } else {
          answer = correctAnswer === 'true' ? 'false' : 'true'
          isCorrect = false
          score = 0
        }
        break
        
      case 'fill':
        if (accuracy > 0.6) {
          answer = correctAnswer as string
          isCorrect = true
          score = pq.score
        } else {
          answer = `错误答案${randomInt(1, 5)}`
          isCorrect = false
          score = 0
        }
        break
        
      case 'essay':
        answer = `学生答案：这是关于题目的回答内容...`
        isCorrect = accuracy > 0.5
        score = Math.floor(pq.score * accuracy)
        break
        
      default:
        answer = ''
        isCorrect = false
        score = 0
    }
    
    return {
      questionId: pq.questionId,
      answer,
      isCorrect,
      score
    }
  })
}

// 生成模拟考试记录数据
function generateExamRecords(exams: Exam[], count: number = 200): ExamRecord[] {
  const records: ExamRecord[] = []
  const flatDepartments = flattenDepartments(departmentData)
  
  for (let i = 0; i < count; i++) {
    const exam = randomItem(exams.filter(e => e.isPublished))
    const department = randomItem(flatDepartments)
    const paperQuestions = exam.paper?.questions || []
    const answers = generateExamAnswers(paperQuestions)
    const totalScore = answers.reduce((sum, answer) => sum + answer.score, 0)
    const passingScore = exam.paper?.passingScore || 60
    const passed = totalScore >= passingScore
    
    const startTime = randomDate(new Date(exam.startTime), new Date(exam.endTime))
    const duration = randomInt(30, exam.duration)
    const endTime = new Date(startTime)
    endTime.setMinutes(endTime.getMinutes() + duration)
    
    const record: ExamRecord = {
      id: generateId(),
      examId: exam.id,
      examTitle: exam.title,
      userId: generateId(6),
      userName: `学员${randomInt(1, 999)}`,
      departmentId: department.id.toString(),
      departmentName: department.name,
      score: totalScore,
      totalScore: exam.paper?.totalScore || 100,
      passed,
      startTime: new Date(startTime).toISOString(),
      endTime: endTime.toISOString(),
      duration,
      answers,
      status: 'completed',
      attemptNumber: randomInt(1, exam.maxAttempts),
      createdAt: new Date(startTime).toISOString(),
      updatedAt: endTime.toISOString()
    }
    
    records.push(record)
  }
  
  return records
}

// 扁平化部门数据
function flattenDepartments(departments: any[], result: any[] = []): any[] {
  departments.forEach(dept => {
    result.push(dept)
    if (dept.children && dept.children.length > 0) {
      flattenDepartments(dept.children, result)
    }
  })
  return result
}

// 生成题库数据
function generateBanks(): Bank[] {
  const banks: Bank[] = [
    {
      id: 'bank_1',
      name: '安全知识题库',
      description: '包含矿山安全知识相关题目',
      scope: '全体员工',
      questionCount: 0,
      createTime: '2023-01-01 10:00:00',
      createdAt: randomDate(new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 6, 1)),
      createdBy: randomItem(creators)
    },
    {
      id: 'bank_2', 
      name: '技术能力题库',
      description: '包含矿山技术相关题目',
      scope: '技术部门',
      questionCount: 0,
      createTime: '2023-01-02 10:00:00',
      createdAt: randomDate(new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 6, 1)),
      createdBy: randomItem(creators)
    },
    {
      id: 'bank_3',
      name: '管理知识题库', 
      description: '包含管理相关题目',
      scope: '管理人员',
      questionCount: 0,
      createTime: '2023-01-03 10:00:00',
      createdAt: randomDate(new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 6, 1)),
      createdBy: randomItem(creators)
    },
    {
      id: 'bank_4',
      name: '应急处理题库',
      description: '包含应急处理相关题目',
      scope: '全体员工',
      questionCount: 0,
      createTime: '2023-01-04 10:00:00',
      createdAt: randomDate(new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 6, 1)),
      createdBy: randomItem(creators)
    },
    {
      id: 'bank_5',
      name: '设备维护题库',
      description: '包含设备维护相关题目',
      scope: '技术部门',
      questionCount: 0,
      createTime: '2023-01-05 10:00:00',
      createdAt: randomDate(new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 6, 1)),
      createdBy: randomItem(creators)
    }
  ]
  
  return banks
}

// 初始化数据
export const questions = generateQuestions(100)
export const papers = generatePapers(questions, 20)
export const exams = generateExams(papers, 15)
export const examRecords = generateExamRecords(exams, 200)

// 题库管理接口模拟
export function mockGetQuestionList(url: string) {
  const params = getQueryParams(url)
  let filteredQuestions = [...questions]
  
  // 关键字搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredQuestions = filteredQuestions.filter(q => 
      q.title.toLowerCase().includes(keyword)
    )
  }
  
  // 题库筛选
  if (params.bankId) {
    filteredQuestions = filteredQuestions.filter(q => q.bankId === params.bankId)
  }
  
  // 类型筛选
  if (params.type) {
    filteredQuestions = filteredQuestions.filter(q => q.type === params.type)
  }
  
  // 排序
  if (params.sortBy) {
    const sortBy = params.sortBy as keyof Question
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1
    
    filteredQuestions.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]
      if (aValue !== undefined && bValue !== undefined) {
        if (aValue < bValue) return -1 * sortOrder
        if (aValue > bValue) return 1 * sortOrder
      }
      return 0
    })
  }
  
  // 分页
  const page = parseInt(params.page || '1')
  const limit = parseInt(params.limit || '10')
  const paginatedData = paginateData(filteredQuestions, page, limit)
  
  return mockResponse(paginatedData)
}

export function mockGetQuestionDetail(url: string) {
  const questionId = url.split('/').pop() || ''
  const question = questions.find(q => q.id === questionId)
  
  if (!question) {
    return mockResponse(null, 404, '题目不存在')
  }
  
  return mockResponse(question)
}

export function mockCreateQuestion(data: Partial<Question>) {
  const newQuestion: Question = {
    id: generateId(),
    title: data.title || '新建题目',
    type: data.type || 'single',
    bankId: data.bankId || 'bank_1',
    options: data.options,
    correctAnswer: data.correctAnswer || 'A',
    explanation: data.explanation,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: data.createdBy || '管理员'
  }
  
  questions.push(newQuestion)
  return mockResponse({ id: newQuestion.id })
}

export function mockUpdateQuestion(url: string, data: Partial<Question>) {
  const questionId = url.split('/').pop() || ''
  const questionIndex = questions.findIndex(q => q.id === questionId)
  
  if (questionIndex === -1) {
    return mockResponse(null, 404, '题目不存在')
  }
  
  questions[questionIndex] = {
    ...questions[questionIndex],
    ...data,
    updatedAt: new Date().toISOString()
  }
  
  return mockResponse({ success: true })
}

export function mockDeleteQuestion(url: string) {
  const questionId = url.split('/').pop() || ''
  const questionIndex = questions.findIndex(q => q.id === questionId)
  
  if (questionIndex === -1) {
    return mockResponse(null, 404, '题目不存在')
  }
  
  questions.splice(questionIndex, 1)
  return mockResponse({ success: true })
}

// 试卷管理接口模拟
export function mockGetPaperList(url: string) {
  const params = getQueryParams(url)
  let filteredPapers = [...papers]
  
  // 关键字搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredPapers = filteredPapers.filter(p => 
      p.title.toLowerCase().includes(keyword) ||
      p.description.toLowerCase().includes(keyword)
    )
  }
  
  // 状态筛选
  if (params.status) {
    const isPublished = params.status === 'published'
    filteredPapers = filteredPapers.filter(p => p.isPublished === isPublished)
  }
  
  // 排序
  if (params.sortBy) {
    const sortBy = params.sortBy as keyof Paper
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1
    
    filteredPapers.sort((a, b) => {
      if (a[sortBy] < b[sortBy]) return -1 * sortOrder
      if (a[sortBy] > b[sortBy]) return 1 * sortOrder
      return 0
    })
  }
  
  // 分页
  const page = parseInt(params.page || '1')
  const limit = parseInt(params.limit || '10')
  const paginatedData = paginateData(filteredPapers, page, limit)
  
  return mockResponse(paginatedData)
}

export function mockGetPaperDetail(url: string) {
  const paperId = url.split('/').pop() || ''
  const paper = papers.find(p => p.id === paperId)
  
  if (!paper) {
    return mockResponse(null, 404, '试卷不存在')
  }
  
  return mockResponse(paper)
}

// 考试管理接口模拟
export function mockGetExamList(url: string) {
  const params = getQueryParams(url)
  let filteredExams = [...exams]
  
  // 关键字搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredExams = filteredExams.filter(e => 
      e.title.toLowerCase().includes(keyword) ||
      e.description.toLowerCase().includes(keyword)
    )
  }
  
  // 状态筛选
  if (params.status) {
    const isPublished = params.status === 'published'
    filteredExams = filteredExams.filter(e => e.isPublished === isPublished)
  }
  
  // 部门筛选
  if (params.departmentId) {
    filteredExams = filteredExams.filter(e => 
      e.departmentIds.includes(params.departmentId!)
    )
  }
  
  // 排序
  if (params.sortBy) {
    const sortBy = params.sortBy as keyof Exam
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1
    
    filteredExams.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]
      if (aValue !== undefined && bValue !== undefined) {
        if (aValue < bValue) return -1 * sortOrder
        if (aValue > bValue) return 1 * sortOrder
      }
      return 0
    })
  }
  
  // 分页
  const page = parseInt(params.page || '1')
  const limit = parseInt(params.limit || '10')
  const paginatedData = paginateData(filteredExams, page, limit)
  
  return mockResponse(paginatedData)
}

// 考试记录接口模拟
export function mockGetExamRecordList(url: string) {
  const params = getQueryParams(url)
  let filteredRecords = [...examRecords]
  
  // 考试筛选
  if (params.examId) {
    filteredRecords = filteredRecords.filter(r => r.examId === params.examId)
  }
  
  // 部门筛选
  if (params.departmentId) {
    filteredRecords = filteredRecords.filter(r => r.departmentId === params.departmentId)
  }
  
  // 状态筛选
  if (params.status) {
    if (params.status === 'passed') {
      filteredRecords = filteredRecords.filter(r => r.passed)
    } else if (params.status === 'failed') {
      filteredRecords = filteredRecords.filter(r => !r.passed)
    }
  }
  
  // 排序
  if (params.sortBy) {
    const sortBy = params.sortBy as keyof ExamRecord
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1
    
    filteredRecords.sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]
      if (aValue !== undefined && bValue !== undefined) {
        if (aValue < bValue) return -1 * sortOrder
        if (aValue > bValue) return 1 * sortOrder
      }
      return 0
    })
  }
  
  // 分页
  const page = parseInt(params.page || '1')
  const limit = parseInt(params.limit || '10')
  const paginatedData = paginateData(filteredRecords, page, limit)
  
  return mockResponse(paginatedData)
}

// 考试统计接口模拟
export function mockGetExamStatistics(url: string) {
  const params = getQueryParams(url)
  const flatDepartments = flattenDepartments(departmentData)
  
  let targetRecords = examRecords
  if (params.examId) {
    targetRecords = examRecords.filter(r => r.examId === params.examId)
  }
  
  const totalExams = exams.length
  const totalParticipants = targetRecords.length
  const averageScore = totalParticipants > 0 ? 
    Math.round(targetRecords.reduce((sum, r) => sum + (r.score / r.totalScore * 100), 0) / totalParticipants) : 0
  const passedCount = targetRecords.filter(r => r.passed).length
  const passRate = totalParticipants > 0 ? Math.round((passedCount / totalParticipants) * 100) : 0
  
  // 部门统计
  const departmentStats = flatDepartments.map(dept => {
    const deptRecords = targetRecords.filter(r => r.departmentId === dept.id.toString())
    const deptParticipants = deptRecords.length
    const deptPassed = deptRecords.filter(r => r.passed).length
    const deptAverageScore = deptParticipants > 0 ? 
      Math.round(deptRecords.reduce((sum, r) => sum + (r.score / r.totalScore * 100), 0) / deptParticipants) : 0
    const deptPassRate = deptParticipants > 0 ? Math.round((deptPassed / deptParticipants) * 100) : 0
    
    return {
      departmentId: dept.id.toString(),
      departmentName: dept.name,
      participantCount: deptParticipants,
      averageScore: deptAverageScore,
      passRate: deptPassRate
    }
  }).filter(stat => stat.participantCount > 0) // 只返回有参与者的部门
  
  return mockResponse({
    totalExams,
    totalParticipants,
    averageScore,
    passRate,
    departmentStats
  })
}

// 题库Mock接口
export function mockGetBankList(url: string) {
  const params = getQueryParams(url)
  const { page = 1, limit = 10, keyword = '', scope = '' } = params
  
  let banks = generateBanks()
  const allQuestions = generateQuestions(100)
  
  // 更新题库题目数量
  banks = banks.map(bank => {
    // 统计该题库下的题目数量
    const count = allQuestions.filter(q => q.bankId === bank.id).length
    return { ...bank, questionCount: count }
  })
  
  // 搜索筛选
  if (keyword) {
    banks = banks.filter(bank => 
      bank.name.includes(keyword) || 
      bank.description.includes(keyword)
    )
  }
  
  if (scope) {
    banks = banks.filter(bank => bank.scope.includes(scope))
  }
  
  const paginatedData = paginateData(banks, Number(page), Number(limit))
  return mockResponse(paginatedData)
}

export function mockGetBankDetail(url: string) {
  const id = url.split('/').pop()
  const banks = generateBanks()
  const bank = banks.find(b => b.id === id)
  
  if (!bank) {
    return mockResponse(null, 404, '题库不存在')
  }
  
  return mockResponse(bank)
}

export function mockCreateBank(data: Partial<Bank>) {
  const newBank: Bank = {
    id: generateId(),
    name: data.name || '',
    description: data.description || '',
    scope: data.scope || '',
    questionCount: 0,
    createTime: new Date().toLocaleString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: data.createdBy || '系统管理员'
  }
  
  return mockResponse(newBank, 200, '创建成功')
}

export function mockUpdateBank(url: string, data: Partial<Bank>) {
  const id = url.split('/').pop()
  const banks = generateBanks()
  const bank = banks.find(b => b.id === id)
  
  if (!bank) {
    return mockResponse(null, 404, '题库不存在')
  }
  
  const updatedBank: Bank = {
    ...bank,
    ...data,
    updatedAt: new Date().toISOString()
  }
  
  return mockResponse(updatedBank, 200, '更新成功')
}

export function mockDeleteBank(url: string) {
  const id = url.split('/').pop()
  const banks = generateBanks()
  const bank = banks.find(b => b.id === id)
  
  if (!bank) {
    return mockResponse(null, 404, '题库不存在')
  }
  
  return mockResponse({ success: true }, 200, '删除成功')
} 