package com.cy.education.model.params;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 积分调整参数
 */
@Data
public class PointsAdjustParam {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Integer userId;
    
    /**
     * 积分变动：正数表示增加，负数表示减少
     */
    @NotNull(message = "积分变动不能为空")
    private Integer points;
    
    /**
     * 变动原因
     */
    private String description;
    
    /**
     * 操作员
     */
    private String operator;
} 