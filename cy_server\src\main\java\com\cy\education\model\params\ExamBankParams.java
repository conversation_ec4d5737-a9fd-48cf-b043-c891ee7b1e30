package com.cy.education.model.params;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 题库添加/修改参数
 */
@Data
public class ExamBankParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题库ID(修改时使用)
     */
    private Integer id;

    /**
     * 题库名称
     */
    @NotBlank(message = "题库名称不能为空")
    @Size(max = 100, message = "题库名称长度不能超过100个字符")
    private String name;

    /**
     * 题库描述
     */
    @Size(max = 500, message = "题库描述长度不能超过500个字符")
    private String description;

    /**
     * 适用范围
     */
    @Size(max = 100, message = "适用范围长度不能超过100个字符")
    private String scope;
} 