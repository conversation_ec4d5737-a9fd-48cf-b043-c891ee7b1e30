package com.cy.education.service.impl.points;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.points.PointsRecord;
import com.cy.education.model.entity.points.PointsRule;
import com.cy.education.model.entity.Student;
import com.cy.education.model.params.PointsAdjustParam;
import com.cy.education.model.params.PointsQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.repository.PointsRecordMapper;
import com.cy.education.service.points.PointsRecordService;
import com.cy.education.service.points.PointsRuleService;
import com.cy.education.service.StudentService;
import com.cy.education.utils.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Map;

/**
 * 积分记录服务实现类
 */
@Service
public class PointsRecordServiceImpl extends ServiceImpl<PointsRecordMapper, PointsRecord> implements PointsRecordService {

    private static final Logger log = LoggerFactory.getLogger(PointsRecordServiceImpl.class);

    @Autowired
    private StudentService studentService;
    @Autowired
    private PointsRuleService pointsRuleService;

    @Override
    public IPage<PointsRecord> page(PointsQueryParam param) {
        // 创建分页对象
        Page<PointsRecord> page = new Page<>(param.getPageNum(), param.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<PointsRecord> wrapper = new LambdaQueryWrapper<>();

        // 用户ID条件
        wrapper.eq(PointsRecord::getUserId, SecurityUtil.getCurrentUserId());

        // 积分类型条件
        if (StringUtils.isNotBlank(param.getType())) {
            wrapper.eq(PointsRecord::getType, param.getType());
        }

        // 日期范围条件
        if (StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate())) {
            LocalDateTime startDateTime = LocalDate.parse(param.getStartDate()).atStartOfDay();
            LocalDateTime endDateTime = LocalDate.parse(param.getEndDate()).atTime(23, 59, 59);
            wrapper.between(PointsRecord::getCreatedAt, startDateTime, endDateTime);
        } else if (StringUtils.isNotBlank(param.getStartDate())) {
            LocalDateTime startDateTime = LocalDate.parse(param.getStartDate()).atStartOfDay();
            wrapper.ge(PointsRecord::getCreatedAt, startDateTime);
        } else if (StringUtils.isNotBlank(param.getEndDate())) {
            LocalDateTime endDateTime = LocalDate.parse(param.getEndDate()).atTime(23, 59, 59);
            wrapper.le(PointsRecord::getCreatedAt, endDateTime);
        }

        // 排序
        if (StringUtils.isNotBlank(param.getSortBy())) {
            boolean isAsc = "asc".equalsIgnoreCase(param.getSortOrder());
            switch (param.getSortBy()) {
                case "createdAt":
                    wrapper.orderBy(true, isAsc, PointsRecord::getCreatedAt);
                    break;
                case "points":
                    wrapper.orderBy(true, isAsc, PointsRecord::getPoints);
                    break;
                default:
                    wrapper.orderBy(true, false, PointsRecord::getCreatedAt);
                    break;
            }
        } else {
            // 默认按创建时间降序
            wrapper.orderByDesc(PointsRecord::getCreatedAt);
        }

        // 使用新的联表查询方法
        try {
            // 如果有userName参数，添加用户名过滤条件（需要在SQL中处理）
            if (StringUtils.isNotBlank(param.getUserName())) {
                wrapper.apply("s.name like concat('%', {0}, '%')", param.getUserName());
            }

            return baseMapper.selectPointsRecordPage(page, wrapper);
        } catch (Exception e) {
            log.error("联表查询积分记录失败", e);
            // 降级使用原方法(修复无限递归问题)
            log.warn("降级使用父类的page方法查询");
            return super.page(page, wrapper);
        }
    }


    @Override
    public Integer getUserBalance(Integer userId) {
        // 获取用户最新的积分记录
        PointsRecord latestRecord = baseMapper.getLatestRecord(userId);

        // 如果没有记录，则积分为0
        return latestRecord != null ? latestRecord.getBalance() : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> adjustPoints(PointsAdjustParam param) {
        // 获取用户当前积分余额
        Integer currentBalance = getUserBalance(param.getUserId());

        // 计算新的积分余额
        Integer newBalance = currentBalance + param.getPoints();

        // 如果新余额小于0，则不允许调整
        if (newBalance < 0) {
            return ApiResponse.error("积分调整后余额不能小于0");
        }

        // 创建积分记录
        String operator = StringUtils.isBlank(param.getOperator()) ? "admin" : param.getOperator();
        String description = StringUtils.isBlank(param.getDescription()) ?
                (param.getPoints() > 0 ? "管理员增加积分" : "管理员减少积分") :
                param.getDescription();

        boolean success = recordPointsChange(
                param.getUserId(),
                param.getPoints(),
                "admin_adjust",
                description,
                null,
                null,
                operator
        );

        if (success) {
            return ApiResponse.success(Map.of(
                    "userId", param.getUserId(),
                    "points", param.getPoints(),
                    "balance", newBalance,
                    "success", true
            ));
        }
        return ApiResponse.error("积分调整失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordPointsChange(Integer userId, Integer points, String type,
                                      String description, Integer relatedId,
                                      String relatedType, String operator) {
        try {
            // 获取学生信息以取得最准确的当前积分值
            Student student = null;
            try {
                // 先尝试从studentService获取学生信息和积分值
                student = studentService.getStudentById(userId);
            } catch (Exception e) {
                log.warn("获取学生信息失败，将使用积分记录表的余额: userId={}", userId, e);
            }

            // 获取用户当前积分余额（优先使用学生表中的积分值）
            Integer currentBalance;
            if (student != null && student.getPoints() != null) {
                currentBalance = student.getPoints();
                log.info("使用学生表中的积分值: userId={}, points={}", userId, currentBalance);
            } else {
                // 降级使用积分记录表的最新余额
                currentBalance = getUserBalance(userId);
                log.info("使用积分记录表的最新余额: userId={}, points={}", userId, currentBalance);
            }

            // 计算新的积分余额
            Integer newBalance = currentBalance + points;

            // 如果新余额小于0，则不允许记录
            if (newBalance < 0) {
                throw new RuntimeException("积分变动后余额不能小于0");
            }

            // 创建积分记录
            PointsRecord record = new PointsRecord();
            record.setUserId(userId);
            record.setPoints(points);
            record.setBalance(newBalance);
            record.setType(type);
            record.setDescription(description);
            record.setRelatedId(relatedId);
            record.setRelatedType(relatedType);
            record.setOperator(operator);
            record.setCreatedAt(LocalDateTime.now());

            // 保存记录
            boolean saveResult = save(record);

            // 更新学员积分余额
            if (saveResult) {
                boolean updateResult = studentService.updateStudentPoints(userId, newBalance);
                if (!updateResult) {
                    log.warn("更新学生表积分失败：userId={}, newBalance={}", userId, newBalance);
                }
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("记录积分变动失败：userId={}, points={}, type={}", userId, points, type, e);
            throw new RuntimeException("记录积分变动失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPointsByRule(Integer userId, String ruleCode, Integer relatedId, String relatedType) {
        try {
            // 获取积分规则
            PointsRule rule = pointsRuleService.getByCode(ruleCode);
            if (rule == null || rule.getStatus() != 1) {
                return false;
            }

            // 检查规则的总次数限制
            if (rule.getTimesLimit() > 0 && rule.getUsedCount() >= rule.getTimesLimit()) {
                return false;
            }

            // 检查规则的每日次数限制
            if (rule.getDailyLimit() > 0) {
                LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
                LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);

                int dailyUsage = baseMapper.countDailyUsage(
                        userId,
                        "rule_based",
                        "规则：" + rule.getCode(),
                        startOfDay,
                        endOfDay
                );

                if (dailyUsage >= rule.getDailyLimit()) {
                    return false;
                }
            }

            // 记录积分变动
            boolean recorded = recordPointsChange(
                    userId,
                    rule.getPoints(),
                    "rule_based",
                    "规则：" + rule.getCode() + " - " + rule.getName(),
                    relatedId,
                    relatedType,
                    "system"
            );

            // 增加规则使用次数
            if (recorded) {
                pointsRuleService.incrementUsedCount(rule.getId());
                return true;
            }

            return false;
        } catch (Exception e) {
            throw new RuntimeException("根据规则添加积分失败", e);
        }
    }
}
