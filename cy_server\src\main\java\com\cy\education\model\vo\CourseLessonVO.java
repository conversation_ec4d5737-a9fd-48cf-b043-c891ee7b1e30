package com.cy.education.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 课程课时VO
 */
@Data
public class CourseLessonVO {
    
    /**
     * 课时ID
     */
    private Integer id;
    
    /**
     * 章节ID
     */
    private Integer sectionId;
    
    /**
     * 课时标题
     */
    private String title;
    
    /**
     * 课时类型：video, file, article
     */
    private String type;
    
    /**
     * 课时时长(分钟)
     */
    private Integer duration;
    
    /**
     * 排序号
     */
    private Integer orderNum;
    
    /**
     * 关联的资源ID
     */
    private Integer resourceId;
    
    /**
     * 关联的资源类型：video, file, article
     */
    private String resourceType;
    
    /**
     * 资源名称（非数据库字段）
     */
    private String resourceName;
    
    /**
     * 资源URL（非数据库字段）
     */
    private String resourceUrl;
    
    /**
     * 资源大小，单位字节（非数据库字段）
     */
    private Long resourceSize;
    
    /**
     * 资源时长，仅视频类型有效（非数据库字段）
     */
    private Integer resourceDuration;
    
    /**
     * 资源封面图（非数据库字段）
     */
    private String coverImage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 