<template>
  <div class="exam-management">
    <div class="page-header">
      <h2>考试管理</h2>
    </div>

    <el-card class="tab-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="考试安排" name="arrangement">
          <ExamArrangement ref="examArrangementRef" @view-records="handleViewRecords" />
        </el-tab-pane>
        <el-tab-pane label="考试记录" name="records">
          <ExamRecords ref="examRecordsRef" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ExamArrangement from './components/ExamArrangement.vue'
// 使用改进版本
// import ExamRecords from './components/ExamRecordsV2.vue'
// import ExamRecords from './components/ExamRecordsSimple.vue'
import ExamRecords from './components/ExamRecords.vue'

const route = useRoute()
const router = useRouter()
const activeTab = ref('arrangement')
const examArrangementRef = ref()
const examRecordsRef = ref()

// 处理标签页切换
const handleTabChange = (tabName: string) => {
  // 更新URL参数而不导航到新页面
  router.replace({
    name: route.name!,
    query: { ...route.query, tab: tabName }
  })
}

// 查看考试记录 - 从考试安排页面跳转到记录页面
const handleViewRecords = (examId: string) => {
  activeTab.value = 'records'
  // 更新URL参数
  router.replace({
    name: route.name!,
    query: { ...route.query, tab: 'records', examId }
  })
  // 通知考试记录组件筛选特定考试
  setTimeout(() => {
    if (examRecordsRef.value && examRecordsRef.value.filterByExam) {
      examRecordsRef.value.filterByExam(examId)
    }
  }, 100)
}

// 监听路由变化
watch(() => route.query.tab, (newTab) => {
  if (newTab && typeof newTab === 'string') {
    activeTab.value = newTab
  }
}, { immediate: true })

// 页面挂载时根据URL参数设置活动标签
onMounted(() => {
  const tab = route.query.tab as string
  if (tab) {
    activeTab.value = tab
  }
  
  // 如果有examId参数，切换到记录页面并筛选
  const examId = route.query.examId as string
  if (examId) {
    activeTab.value = 'records'
    setTimeout(() => {
      if (examRecordsRef.value && examRecordsRef.value.filterByExam) {
        examRecordsRef.value.filterByExam(examId)
      }
    }, 500)
  }
})
</script>

<style scoped>
.exam-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.tab-card {
  margin-bottom: 20px;
}
</style> 