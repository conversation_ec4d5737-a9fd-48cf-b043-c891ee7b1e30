<template>
  <div class="app-container">
    <el-card>
      <el-tabs v-model="activeTab" class="resource-tabs">
        <el-tab-pane label="文件资源" name="file">
          <FileResources v-if="activeTab === 'file'" />
        </el-tab-pane>
        <el-tab-pane label="视频资源" name="video">
          <VideoResources v-if="activeTab === 'video'" />
        </el-tab-pane>
        <el-tab-pane label="文章资源" name="article">
          <ArticleResources v-if="activeTab === 'article'" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import FileResources from './components/FileResources.vue'
import VideoResources from './components/VideoResources.vue'
import ArticleResources from './components/ArticleResources.vue'

const activeTab = ref('file')
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.resource-tabs {
  /* Fixes issue where content flashes on tab switch */
  & .el-tab-pane {
    display: block;
  }
}
</style> 