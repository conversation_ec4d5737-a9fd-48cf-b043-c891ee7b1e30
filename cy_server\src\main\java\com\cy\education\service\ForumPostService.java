package com.cy.education.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.ForumPost;
import com.cy.education.model.vo.ForumPostQueryParam;

import java.util.List;

/**
 * 论坛帖子服务接口
 */
public interface ForumPostService {
    
    /**
     * 获取帖子列表（分页）
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<ForumPost> getPostPage(ForumPostQueryParam param);
    
    /**
     * 获取帖子详情
     * 
     * @param id 帖子ID
     * @return 帖子信息
     */
    ForumPost getPostById(Integer id);
    
    /**
     * 增加帖子浏览量
     * 
     * @param id 帖子ID
     * @return 是否成功
     */
    boolean incrementViewCount(Integer id);
    
    /**
     * 审核帖子
     * 
     * @param id 帖子ID
     * @param status 状态
     * @return 是否成功
     */
    boolean reviewPost(Integer id, Integer status);
    
    /**
     * 设置帖子置顶状态
     * 
     * @param id 帖子ID
     * @param isTop 是否置顶
     * @return 是否成功
     */
    boolean setPostTop(Integer id, Boolean isTop);
    
    /**
     * 设置帖子精华状态
     * 
     * @param id 帖子ID
     * @param isEssence 是否精华
     * @return 是否成功
     */
    boolean setPostEssence(Integer id, Boolean isEssence);
    
    /**
     * 删除帖子（假删除）
     * 
     * @param id 帖子ID
     * @return 是否成功
     */
    boolean deletePost(Integer id);
    
    /**
     * 批量审核帖子
     * 
     * @param ids 帖子ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchReviewPosts(List<Integer> ids, Integer status);
    
    /**
     * 批量删除帖子（假删除）
     * 
     * @param ids 帖子ID列表
     * @return 是否成功
     */
    boolean batchDeletePosts(List<Integer> ids);
    
    /**
     * 更新帖子回复数
     * 
     * @param postId 帖子ID
     * @return 是否成功
     */
    boolean updateReplyCount(Integer postId);
} 