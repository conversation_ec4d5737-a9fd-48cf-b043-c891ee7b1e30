package com.cy.education.controller;

import com.cy.education.model.entity.exam.ExamRecord;
import com.cy.education.model.params.ExamRecordQueryParams;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.ExamRecordVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.exam.ExamRecordService;
import com.cy.education.utils.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 考试记录控制器
 */
@Api(tags = "考试记录接口")
@RestController
@RequestMapping("/exam-record")
@RequiredArgsConstructor
public class ExamRecordController {

    private final ExamRecordService examRecordService;

    @ApiOperation("获取我的考试记录列表")
    @GetMapping("/list")
    public ApiResponse<PageResponse<ExamRecordVO>> listExamRecords(ExamRecordQueryParams params) {
        try {
            params.setUserId(SecurityUtil.getCurrentUserId());
            return ApiResponse.success(examRecordService.listExamRecords(params));
        } catch (Exception e) {
            return ApiResponse.error("获取帖子列表失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取考试记录详情")
    @GetMapping("/detail/{id}")
    public ApiResponse<ExamRecordVO> getExamRecordDetail(@PathVariable Integer id) {
        return ApiResponse.success(examRecordService.getExamRecordDetail(id));
    }

    @ApiOperation("开始考试")
    @PostMapping("/start/{examId}")
    public ApiResponse<Map<String, Object>> startExam(@PathVariable Integer examId) {
        Integer recordId = examRecordService.startExam(examId, SecurityUtil.getCurrentUserId());
        Map<String, Object> result = new HashMap<>();
        result.put("recordId", recordId);
        return ApiResponse.success(result, "考试开始成功");
    }

    @ApiOperation("提交考试")
    @PostMapping("/submit/{recordId}")
    public ApiResponse<Map<String, Object>> submitExam(
            @PathVariable Integer recordId,
            @RequestBody Map<String, String> params) {
        String answers = params.get("answers");
        ExamRecord record = examRecordService.submitExam(recordId, answers);
        Map<String, Object> result = new HashMap<>();
        result.put("score", record.getScore());
        result.put("isPassed", record.getIsPassed());
        return ApiResponse.success(result, "考试提交成功");
    }

    @ApiOperation("获取考试统计数据")
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getExamStatistics(@RequestParam(required = false) Integer examId) {
        return ApiResponse.success(examRecordService.getExamStats(examId));
    }

    @ApiOperation("获取我的考试统计数据")
    @GetMapping("/user/stats")
    public ApiResponse<Map<String, Object>> getUserExamStatistics() {
        return ApiResponse.success(examRecordService.getUserExamStats());
    }

    @ApiOperation("获取考试结果详情")
    @GetMapping("/result/{recordId}")
    public ApiResponse<Map<String, Object>> getExamResult(@PathVariable Integer recordId) {
        return ApiResponse.success(examRecordService.getExamResult(recordId));
    }

}
