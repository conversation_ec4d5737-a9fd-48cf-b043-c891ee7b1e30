package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.Student;
import com.cy.education.model.dto.StudentImportData;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.StudentMapper;
import com.cy.education.service.StudentService;
import com.cy.education.service.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 学员服务实现
 */
@Service
@Slf4j
public class StudentServiceImpl implements StudentService {

    @Autowired
    private StudentMapper studentMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private DepartmentService departmentService;
    
    @Value("${app.default-password:123456}")
    private String defaultPassword;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public PageResponse<Student> listStudents(Long page, Long size, String keyword, Integer departmentId, Integer status) {
        // 创建分页对象
        Page<Student> pageParam = new Page<>(page, size);
        
        // 处理部门筛选
        List<Integer> departmentIds = null;
        if (departmentId != null) {
            // 获取部门及其所有子部门的ID列表
            departmentIds = departmentService.getDepartmentAndChildrenIds(departmentId);
            log.info("查询部门ID: {}, 包含子部门共{}个", departmentId, departmentIds.size());
        }
        
        // 执行分页查询
        IPage<Student> studentPage = studentMapper.selectStudentPage(pageParam, keyword, departmentIds, status);
        
        // 处理结果，填充前端所需字段
        List<Student> students = studentPage.getRecords();
        
        // 添加日志输出status值，帮助调试
        log.info("查询到 {} 个学员", students.size());
        for (Student student : students) {
            // 安全地记录状态值
            try {
                java.lang.reflect.Field statusField = Student.class.getDeclaredField("status");
                statusField.setAccessible(true);
                Object statusValue = statusField.get(student);
                log.info("数据库查询结果 - Student ID: {}, Name: {}, 原始Status值: {}", 
                        student.getId(), student.getName(), statusValue);
            } catch (Exception e) {
                log.error("无法访问status字段", e);
            }
            processStudentFields(student);
        }
        
        // 转换为自定义分页响应对象
        return PageResponse.of(
                students,
                studentPage.getTotal(),
                studentPage.getCurrent(),
                studentPage.getSize()
        );
    }
    
    /**
     * 处理学员字段，填充前端所需字段
     */
    private void processStudentFields(Student student) {
        // 设置注册时间
        if (student.getCreatedAt() != null) {
            student.setRegisterTime(student.getCreatedAt().format(DATE_TIME_FORMATTER));
        }
    }

    @Override
    public Student getStudentById(Integer id) {
        Student student = studentMapper.selectStudentById(id);
        if (student == null) {
            throw new BusinessException("学员不存在");
        }
        
        // 处理学员字段，填充前端所需字段
        processStudentFields(student);
        
        return student;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addStudent(Student student) {
        // 1. 检查手机号是否已存在
        LambdaQueryWrapper<Student> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Student::getPhone, student.getPhone());
        if (studentMapper.selectCount(queryWrapper) > 0) {
            throw new BusinessException("手机号已被使用");
        }
        
        // 2. 设置用户名（如果未提供，则使用手机号作为用户名）
        if (StringUtils.isEmpty(student.getUsername())) {
            student.setUsername(student.getPhone());
        } else {
            // 检查用户名是否已存在
            LambdaQueryWrapper<Student> usernameWrapper = new LambdaQueryWrapper<>();
            usernameWrapper.eq(Student::getUsername, student.getUsername());
            if (studentMapper.selectCount(usernameWrapper) > 0) {
                throw new BusinessException("用户名已被使用");
            }
        }
        
        // 3. 密码加密（如果未提供，则使用默认密码）
        String password = StringUtils.isEmpty(student.getPassword()) ? defaultPassword : student.getPassword();
        student.setPassword(passwordEncoder.encode(password));
        
        
        // 4. 设置默认值
        if (student.getStatus() == null) {
            student.setStatus(1); // 默认启用
        }
        
        if (student.getPoints() == null) {
            student.setPoints(0); // 初始积分为0
        }
        
        // 5. 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        student.setCreatedAt(now);
        student.setUpdatedAt(now);
        
        // 6. 设置默认头像（如果未提供）
        if (StringUtils.isEmpty(student.getAvatar())) {
            student.setAvatar("https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png");
        }
        
        // 7. 插入数据库
        int result = studentMapper.insert(student);
        if (result <= 0) {
            throw new BusinessException("添加学员失败");
        }
        
        return student.getId();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStudent(Student student) {
        // 1. 检查学员是否存在
        Student existingStudent = studentMapper.selectById(student.getId());
        if (existingStudent == null) {
            throw new BusinessException("学员不存在");
        }
        
        // 2. 检查手机号是否已被其他学员使用
        if (!StringUtils.isEmpty(student.getPhone()) && !student.getPhone().equals(existingStudent.getPhone())) {
            LambdaQueryWrapper<Student> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Student::getPhone, student.getPhone())
                    .ne(Student::getId, student.getId());
            if (studentMapper.selectCount(queryWrapper) > 0) {
                throw new BusinessException("手机号已被使用");
            }
        }
        
        // 3. 更新时间
        student.setUpdatedAt(LocalDateTime.now());
        
        // 4. 更新数据库
        return studentMapper.updateById(student) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStudent(Integer id) {
        // 1. 检查学员是否存在
        Student student = studentMapper.selectById(id);
        if (student == null) {
            throw new BusinessException("学员不存在");
        }
        
        // 2. 删除学员
        return studentMapper.deleteById(id) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String resetStudentPassword(Integer id) {
        // 1. 检查学员是否存在
        Student student = studentMapper.selectById(id);
        if (student == null) {
            throw new BusinessException("学员不存在");
        }
        
        // 2. 生成新密码（6位随机数字）
        String newPassword = generateRandomPassword();
        
        // 3. 更新密码
        Student updateStudent = new Student();
        updateStudent.setId(id);
        updateStudent.setPassword(passwordEncoder.encode(newPassword));
        updateStudent.setUpdatedAt(LocalDateTime.now());
        
        int result = studentMapper.updateById(updateStudent);
        if (result <= 0) {
            throw new BusinessException("重置密码失败");
        }
        
        return newPassword;
    }
    
    /**
     * 生成6位随机数字密码
     */
    private String generateRandomPassword() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStudentStatus(Integer id, Integer status) {
        // 1. 检查学员是否存在
        Student student = studentMapper.selectById(id);
        if (student == null) {
            throw new BusinessException("学员不存在");
        }
        
        // 2. 更新状态
        Student updateStudent = new Student();
        updateStudent.setId(id);
        updateStudent.setStatus(status);
        updateStudent.setUpdatedAt(LocalDateTime.now());
        
        return studentMapper.updateById(updateStudent) > 0;
    }
    
    @Override
    public String exportStudentList(String keyword, Integer departmentId, Integer status) {
        // 导出功能暂时只返回成功，实际导出功能可后续实现
        return "学员数据导出成功，请到系统消息中查看下载链接";
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importStudents(InputStream inputStream, String originalFilename) {
        // 导入功能暂时只返回成功，实际导入功能可后续实现
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("count", 0);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStudentPoints(Integer userId, Integer points) {
        if (userId == null || points == null) {
            log.error("更新学生积分参数无效: userId={}, points={}", userId, points);
            return false;
        }
        
        try {
            // 1. 检查学员是否存在
            Student existingStudent = studentMapper.selectById(userId);
            if (existingStudent == null) {
                log.error("更新学生积分失败: 学员不存在，userId={}", userId);
                return false;
            }
            
            // 2. 更新积分
            Student student = new Student();
            student.setId(userId);
            student.setPoints(points);
            student.setUpdatedAt(LocalDateTime.now());
            
            // 3. 更新数据库
            int result = studentMapper.updateById(student);
            if (result <= 0) {
                log.error("更新学生积分失败: 数据库更新返回0行，userId={}, points={}", userId, points);
                return false;
            }
            
            log.info("更新学生积分成功: userId={}, name={}, 新积分={}", userId, existingStudent.getName(), points);
            return true;
        } catch (Exception e) {
            log.error("更新学生积分异常: userId={}, points={}", userId, points, e);
            throw new RuntimeException("更新学生积分失败", e);
        }
    }

    @Override
    public Resource generateImportTemplate() throws IOException {
        // 生成学员导入模板（Excel格式）
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("学员导入模板");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"姓名*", "用户名*", "手机号*", "邮箱", "部门名称", "备注"};

        // 设置标题样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
            // 设置列宽
            sheet.setColumnWidth(i, 4000);
        }

        // 创建示例数据行
        String[][] sampleData = {
            {"张三", "zhangsan", "13800138001", "<EMAIL>", "技术部", "示例学员1"},
            {"李四", "lisi", "13800138002", "<EMAIL>", "市场部", "示例学员2"},
            {"王五", "wangwu", "13800138003", "<EMAIL>", "人事部", "示例学员3"}
        };

        // 创建数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);

        for (int i = 0; i < sampleData.length; i++) {
            Row dataRow = sheet.createRow(i + 1);
            for (int j = 0; j < sampleData[i].length; j++) {
                Cell cell = dataRow.createCell(j);
                cell.setCellValue(sampleData[i][j]);
                cell.setCellStyle(dataStyle);
            }
        }

        // 添加说明信息
        Row noteRow = sheet.createRow(sampleData.length + 2);
        Cell noteCell = noteRow.createCell(0);
        noteCell.setCellValue("说明：带*号的字段为必填项，示例数据可以删除后填入实际数据");

        // 合并说明单元格
        sheet.addMergedRegion(new CellRangeAddress(sampleData.length + 2, sampleData.length + 2, 0, headers.length - 1));

        // 将工作簿写入字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new ByteArrayResource(outputStream.toByteArray());
    }

    @Override
    public Map<String, Object> importStudentsV2(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 读取Excel文件
            List<StudentImportData> importDataList = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), StudentImportData.class, new ReadListener<StudentImportData>() {
                @Override
                public void invoke(StudentImportData data, AnalysisContext context) {
                    importDataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共{}条记录", importDataList.size());
                }
            }).sheet().doRead();

            if (importDataList.isEmpty()) {
                result.put("success", false);
                result.put("message", "文件中没有有效数据");
                return result;
            }

            // 处理导入数据
            List<StudentImportData> failedRecords = new ArrayList<>();
            int successCount = 0;

            for (StudentImportData importData : importDataList) {
                try {
                    // 验证数据
                    if (!validateImportData(importData)) {
                        failedRecords.add(importData);
                        continue;
                    }

                    // 查找部门ID
                    if (StringUtils.hasText(importData.getDepartmentName())) {
                        // 这里应该根据部门名称查找部门ID，暂时设置为1
                        importData.setDepartmentId(1);
                    }

                    // 创建学员对象
                    Student student = new Student();
                    student.setName(importData.getName());
                    student.setUsername(importData.getUsername());
                    student.setPhone(importData.getPhone());
                    student.setEmail(importData.getEmail());
                    student.setDepartmentId(importData.getDepartmentId());
                    student.setRemark(importData.getRemark());
                    student.setStatus(1); // 默认启用
                    student.setPassword(passwordEncoder.encode(defaultPassword)); // 使用默认密码
                    student.setCreatedAt(LocalDateTime.now());
                    student.setUpdatedAt(LocalDateTime.now());

                    // 保存学员
                    studentMapper.insert(student);
                    successCount++;

                } catch (Exception e) {
                    log.error("导入学员失败: {}", importData.getName(), e);
                    importData.setSuccess(false);
                    importData.setErrorMessage("导入失败: " + e.getMessage());
                    failedRecords.add(importData);
                }
            }

            // 生成结果
            result.put("success", true);
            result.put("totalCount", importDataList.size());
            result.put("successCount", successCount);
            result.put("failCount", failedRecords.size());
            result.put("message", String.format("导入完成，成功%d条，失败%d条", successCount, failedRecords.size()));

            // 如果有失败记录，生成错误文件
            if (!failedRecords.isEmpty()) {
                String errorFileUrl = generateErrorFile(failedRecords);
                result.put("errorFileUrl", errorFileUrl);
                result.put("hasErrors", true);
            } else {
                result.put("hasErrors", false);
            }

        } catch (Exception e) {
            log.error("学员导入异常", e);
            result.put("success", false);
            result.put("message", "导入失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public void exportStudentsV2(Map<String, Object> params, HttpServletResponse response) throws IOException {
        log.info("开始导出学员，参数: {}", params);

        // 查询学员列表
        LambdaQueryWrapper<Student> queryWrapper = new LambdaQueryWrapper<>();
        String keyword = (String) params.get("keyword");
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(Student::getName, keyword)
                    .or().like(Student::getUsername, keyword)
                    .or().like(Student::getPhone, keyword));
        }

        // 处理部门筛选
        Integer departmentId = (Integer) params.get("departmentId");
        if (departmentId != null && departmentId > 0) {
            queryWrapper.eq(Student::getDepartmentId, departmentId);
        }

        // 处理状态筛选
        Integer status = (Integer) params.get("status");
        if (status != null) {
            queryWrapper.eq(Student::getStatus, status);
        }

        String range = (String) params.getOrDefault("range", "all");
        @SuppressWarnings("unchecked")
        List<Integer> selectedIds = (List<Integer>) params.get("selectedIds");

        if ("selected".equals(range) && selectedIds != null && !selectedIds.isEmpty()) {
            queryWrapper.in(Student::getId, selectedIds);
        }

        List<Student> students = studentMapper.selectList(queryWrapper);
        log.info("查询到学员数量: {}", students.size());

        // 如果没有数据，记录详细信息
        if (students.isEmpty()) {
            log.warn("没有查询到学员数据，查询条件: {}", params);
            // 查询总数进行对比
            long totalCount = studentMapper.selectCount(null);
            log.info("数据库中学员总数: {}", totalCount);
        }

        // 转换为导出数据
        @SuppressWarnings("unchecked")
        List<String> fields = (List<String>) params.get("fields");

        // 确定要导出的字段
        String[] allFields = {"姓名", "用户名", "手机号", "邮箱", "状态", "创建时间", "备注"};
        String[] selectedFields = fields != null && !fields.isEmpty() ?
            fields.toArray(new String[0]) : allFields;

        // 转换为List<List<Object>>格式
        List<List<Object>> exportData = new ArrayList<>();

        for (Student student : students) {
            List<Object> row = new ArrayList<>();

            for (String field : selectedFields) {
                switch (field) {
                    case "姓名":
                        row.add(student.getName() != null ? student.getName() : "");
                        break;
                    case "用户名":
                        row.add(student.getUsername() != null ? student.getUsername() : "");
                        break;
                    case "手机号":
                        row.add(student.getPhone() != null ? student.getPhone() : "");
                        break;
                    case "邮箱":
                        row.add(student.getEmail() != null ? student.getEmail() : "");
                        break;
                    case "状态":
                        row.add(student.getStatus() == 1 ? "正常" : "禁用");
                        break;
                    case "创建时间":
                        row.add(student.getCreatedAt() != null ? student.getCreatedAt().toString() : "");
                        break;
                    case "备注":
                        row.add(student.getRemark() != null ? student.getRemark() : "");
                        break;
                    default:
                        row.add("");
                        break;
                }
            }

            exportData.add(row);
        }

        log.info("转换后的导出数据数量: {}, 字段数: {}", exportData.size(), selectedFields.length);

        // 设置响应头
        String format = (String) params.getOrDefault("format", "xlsx");
        String fileName = URLEncoder.encode("学员列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + "." + format, StandardCharsets.UTF_8.toString());

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + fileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Cache-Control", "no-cache");

        // 使用EasyExcel导出
        try {
            List<List<String>> headers = createExportHeaders(Arrays.asList(selectedFields));

            log.info("开始导出学员数据，记录数: {}, 字段数: {}", exportData.size(), headers.size());

            // 打印前几行数据用于调试
            if (!exportData.isEmpty()) {
                log.info("第一行数据示例: {}", exportData.get(0));
                if (exportData.size() > 1) {
                    log.info("第二行数据示例: {}", exportData.get(1));
                }
            }

            EasyExcel.write(response.getOutputStream())
                    .head(headers)
                    .sheet("学员列表")
                    .doWrite(exportData);

            log.info("学员数据导出完成");
        } catch (Exception e) {
            log.error("导出学员数据失败", e);
            throw new IOException("导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建导出表头
     */
    private List<List<String>> createExportHeaders(List<String> fields) {
        String[] allHeaders = {"姓名", "用户名", "手机号", "邮箱", "状态", "创建时间", "备注"};
        String[] selectedHeaders = fields != null && !fields.isEmpty() ?
            fields.toArray(new String[0]) : allHeaders;

        List<List<String>> headers = new ArrayList<>();
        for (String header : selectedHeaders) {
            List<String> head = new ArrayList<>();
            head.add(header);
            headers.add(head);
        }
        return headers;
    }

    /**
     * 验证导入数据
     */
    private boolean validateImportData(StudentImportData data) {
        if (!StringUtils.hasText(data.getName())) {
            data.setSuccess(false);
            data.setErrorMessage("姓名不能为空");
            return false;
        }

        if (!StringUtils.hasText(data.getUsername())) {
            data.setSuccess(false);
            data.setErrorMessage("用户名不能为空");
            return false;
        }

        if (!StringUtils.hasText(data.getPhone())) {
            data.setSuccess(false);
            data.setErrorMessage("手机号不能为空");
            return false;
        }

        // 验证手机号格式
        if (!data.getPhone().matches("^1[3-9]\\d{9}$")) {
            data.setSuccess(false);
            data.setErrorMessage("手机号格式不正确");
            return false;
        }

        // 检查用户名是否已存在
        LambdaQueryWrapper<Student> usernameQuery = new LambdaQueryWrapper<>();
        usernameQuery.eq(Student::getUsername, data.getUsername());
        if (studentMapper.selectCount(usernameQuery) > 0) {
            data.setSuccess(false);
            data.setErrorMessage("用户名已存在");
            return false;
        }

        // 检查手机号是否已存在
        LambdaQueryWrapper<Student> phoneQuery = new LambdaQueryWrapper<>();
        phoneQuery.eq(Student::getPhone, data.getPhone());
        if (studentMapper.selectCount(phoneQuery) > 0) {
            data.setSuccess(false);
            data.setErrorMessage("手机号已存在");
            return false;
        }

        return true;
    }

    /**
     * 生成错误文件
     */
    private String generateErrorFile(List<StudentImportData> failedRecords) {
        try {
            // 这里应该生成Excel文件并保存到文件系统或OSS
            // 暂时返回一个占位符URL
            return "/api/student/download-error-file/" + System.currentTimeMillis();
        } catch (Exception e) {
            log.error("生成错误文件失败", e);
            return null;
        }
    }

    @Override
    public long getStudentCount() {
        return studentMapper.selectCount(null);
    }

    @Override
    public List<Student> getStudentListForTest(int limit) {
        LambdaQueryWrapper<Student> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.last("LIMIT " + limit);
        return studentMapper.selectList(queryWrapper);
    }
}