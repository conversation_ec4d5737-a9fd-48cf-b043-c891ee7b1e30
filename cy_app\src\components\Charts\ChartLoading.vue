<template>
  <view class="chart-loading-container">
    <view class="loading-content">
      <view class="loading-spinner">
        <view class="spinner-circle"></view>
      </view>
      <text class="loading-text">{{ text || '图表加载中...' }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Props {
  text?: string
}

defineProps<Props>()
</script>

<style lang="scss" scoped>
.chart-loading-container {
  background: #fff;
  border-radius: 12px;
  padding: 40px 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  position: relative;
}

.spinner-circle {
  width: 100%;
  height: 100%;
  border: 3px solid #f0f2f5;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 14px;
  color: #8e8e93;
}
</style>
