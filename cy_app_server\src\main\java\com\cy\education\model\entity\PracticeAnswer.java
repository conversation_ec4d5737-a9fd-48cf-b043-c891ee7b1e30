package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 练习答案实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("practice_answer")
public class PracticeAnswer implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 练习记录ID
     */
    private Integer recordId;
    
    /**
     * 题目ID
     */
    private Integer questionId;
    
    /**
     * 用户答案
     */
    private String userAnswer;
    
    /**
     * 正确答案
     */
    private String correctAnswer;
    
    /**
     * 是否正确
     */
    private Boolean isCorrect;
    
    /**
     * 答题时间
     */
    private LocalDateTime answerTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 