<template>
    <div class="news-manager">
      <div class="manager-header">
        <div class="header-left">
          <h2 class="section-title">新闻动态</h2>
          <p class="section-desc">管理首页新闻动态内容展示</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="handleAdd">新增新闻</el-button>
        </div>
      </div>
  
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="已发布" :value="1" />
              <el-option label="草稿" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="置顶">
            <el-select v-model="searchForm.isTop" placeholder="请选择" clearable>
              <el-option label="置顶" :value="true" />
              <el-option label="普通" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input v-model="searchForm.keyword" placeholder="标题/内容关键词" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
  
      <div class="news-table-container">
        <NewsTable 
          :loading="loading" 
          :news-list="newsList" 
          :total="total"
          :current-page="searchForm.pageNum"
          :page-size="searchForm.pageSize"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @edit="handleEdit"
          @delete="handleDelete"
          @toggle-status="handleStatusChange"
          @toggle-top="handleTopChange"
          @preview="handlePreview"
        />
      </div>
  
      <!-- 新闻表单对话框 -->
      <NewsForm 
        v-model="dialogVisible"
        :is-edit="formType === 'edit'"
        :news-data="formData"
        @submit="handleFormSubmit"
      />
  
      <!-- 预览对话框 -->
      <ContentPreviewDialog
        v-model="previewVisible"
        :preview-data="previewData"
        content-type="news"
      />
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import NewsTable from './NewsTable.vue'
  import NewsForm from './NewsForm.vue'
  import ContentPreviewDialog from './ContentPreviewDialog.vue'
  import { getNewsList, getNewsById, addNews, updateNews, deleteNews, updateNewsStatus, updateNewsTop, type NewsItem } from '@/api/content'
  
  // 搜索表单
  const searchForm = reactive({
    status: undefined as number | undefined,
    isTop: undefined as boolean | undefined,
    keyword: '',
    pageNum: 1,
    pageSize: 10
  })
  
  // 数据列表
  const newsList = ref<NewsItem[]>([])
  const total = ref(0)
  const loading = ref(false)
  
  // 表单相关
  const dialogVisible = ref(false)
  const formType = ref<'add' | 'edit'>('add')
  const formData = ref<Partial<NewsItem>>({})
  const formRef = ref()
  
  // 预览相关
  const previewVisible = ref(false)
  const previewData = ref<Partial<NewsItem>>({})
  
  // 初始化数据
  onMounted(() => {
    fetchNewsList()
  })
  
  // 获取新闻列表
  const fetchNewsList = async () => {
    loading.value = true
    try {
      const res = await getNewsList(searchForm)
      newsList.value = res.list
      total.value = res.total
    } catch (error) {
      console.error('获取新闻列表失败', error)
      ElMessage.error('获取新闻列表失败')
    } finally {
      loading.value = false
    }
  }
  
  // 搜索
  const handleSearch = () => {
    searchForm.pageNum = 1
    fetchNewsList()
  }
  
  // 重置搜索
  const resetSearch = () => {
    searchForm.status = undefined
    searchForm.isTop = undefined
    searchForm.keyword = ''
    searchForm.pageNum = 1
    fetchNewsList()
  }
  
  // 页码变化
  const handlePageChange = (page: number) => {
    searchForm.pageNum = page
    fetchNewsList()
  }
  
  // 每页条数变化
  const handleSizeChange = (size: number) => {
    searchForm.pageSize = size
    searchForm.pageNum = 1
    fetchNewsList()
  }
  
  // 新增新闻
  const handleAdd = () => {
    formType.value = 'add'
    formData.value = {
      status: 1,
      isTop: false,
      title: '',
      content: '',
      coverUrl: '',
      publishTime: new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].substring(0, 5)
    }
    dialogVisible.value = true
  }
  
  // 编辑新闻
  const handleEdit = async (row: NewsItem | number) => {
    try {
      loading.value = true
      const id = typeof row === 'number' ? row : row.id
      const res = await getNewsById(id)
      if (res) {
        formData.value = res
        formType.value = 'edit'
        dialogVisible.value = true
      } else {
        ElMessage.error('新闻不存在或已被删除')
      }
    } catch (error) {
      console.error('获取新闻详情失败', error)
      ElMessage.error('获取新闻详情失败')
    } finally {
      loading.value = false
    }
  }
  
  // 删除新闻
  const handleDelete = (row: NewsItem | number) => {
    const id = typeof row === 'number' ? row : row.id
    
    ElMessageBox.confirm('确定要删除该新闻吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        loading.value = true
        await deleteNews(id)
        ElMessage.success('删除成功')
        if (newsList.value.length === 1 && searchForm.pageNum > 1) {
          searchForm.pageNum--
        }
        fetchNewsList()
      } catch (error) {
        console.error('删除新闻失败', error)
        ElMessage.error('删除新闻失败')
      } finally {
        loading.value = false
      }
    }).catch(() => {})
  }
  
  // 切换状态
  const handleStatusChange = async (row: NewsItem | number, status?: number) => {
    try {
      loading.value = true
      const id = typeof row === 'number' ? row : row.id
      const newStatus = typeof row === 'number' ? status! : (row.status === 1 ? 0 : 1)
      
      await updateNewsStatus(id, newStatus)
      ElMessage.success('状态更新成功')
      fetchNewsList()
    } catch (error) {
      console.error('更新新闻状态失败', error)
      ElMessage.error('更新新闻状态失败')
    } finally {
      loading.value = false
    }
  }
  
  // 切换置顶状态
  const handleTopChange = async (row: NewsItem | number, isTop?: boolean) => {
    try {
      loading.value = true
      const id = typeof row === 'number' ? row : row.id
      const newIsTop = typeof row === 'number' ? isTop! : !row.isTop
      
      await updateNewsTop(id, newIsTop)
      ElMessage.success(newIsTop ? '置顶成功' : '取消置顶成功')
      fetchNewsList()
    } catch (error) {
      console.error('更新新闻置顶状态失败', error)
      ElMessage.error('更新新闻置顶状态失败')
    } finally {
      loading.value = false
    }
  }
  
  // 预览新闻
  const handlePreview = async (row: NewsItem | number) => {
    try {
      loading.value = true
      const id = typeof row === 'number' ? row : row.id
      const res = await getNewsById(id)
      if (res) {
        previewData.value = res
        previewVisible.value = true
      } else {
        ElMessage.error('新闻不存在或已被删除')
      }
    } catch (error) {
      console.error('获取新闻详情失败', error)
      ElMessage.error('获取新闻详情失败')
    } finally {
      loading.value = false
    }
  }
  
  // 表单提交
  const handleFormSubmit = async (data: Partial<NewsItem>) => {
    try {
      loading.value = true
      if (formType.value === 'add') {
        await addNews(data)
        ElMessage.success('添加成功')
      } else {
        await updateNews(data)
        ElMessage.success('更新成功')
      }
      dialogVisible.value = false
      fetchNewsList()
    } catch (error) {
      console.error('保存新闻失败', error)
      ElMessage.error('保存新闻失败')
    } finally {
      loading.value = false
    }
  }
  </script>
  
  <style scoped>
  .el-select{
  min-width: 100px;
}
  .news-manager {
    height: 100%;
  }
  
  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #1f2937;
  }
  
  .section-desc {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
  }
  
  .search-form {
    margin-bottom: 24px;
    background-color: #fff;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .news-table-container {
    background-color: #fff;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  </style>