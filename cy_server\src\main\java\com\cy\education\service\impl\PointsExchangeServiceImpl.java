package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.PointsExchange;
import com.cy.education.model.entity.PointsProduct;
import com.cy.education.model.params.PointsExchangeQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.repository.PointsExchangeMapper;
import com.cy.education.service.PointsExchangeService;
import com.cy.education.service.PointsProductService;
import com.cy.education.service.PointsRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 积分兑换服务实现类
 */
@Service
public class PointsExchangeServiceImpl extends ServiceImpl<PointsExchangeMapper, PointsExchange> implements PointsExchangeService {

    @Autowired
    private PointsProductService pointsProductService;

    @Autowired
    private PointsRecordService pointsRecordService;

    @Override
    public IPage<PointsExchange> page(PointsExchangeQueryParam param) {
        // 创建分页对象
        Page<PointsExchange> page = new Page<>(param.getPage(), param.getLimit());

        // 调用带详情的分页查询
        return baseMapper.pageWithDetails(page, param.getUserId(), param.getProductId(), param.getStatus());
    }

    @Override
    public PointsExchange getById(Integer id) {
        // 查询基本信息
        PointsExchange exchange = super.getById(id);

        if (exchange != null) {
            // 查询详细信息
            Page<PointsExchange> page = new Page<>(1, 1);
            IPage<PointsExchange> result = baseMapper.pageWithDetails(page, exchange.getUserId(), exchange.getProductId(), null);

            if (result.getRecords().size() > 0) {
                PointsExchange detailExchange = result.getRecords().get(0);
                exchange.setUserName(detailExchange.getUserName());
                exchange.setDepartmentName(detailExchange.getDepartmentName());
                exchange.setProductName(detailExchange.getProductName());
                exchange.setGoodsImage(detailExchange.getGoodsImage());
            }
        }

        return exchange;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> create(PointsExchange exchange) {
        // 获取商品信息
        PointsProduct product = pointsProductService.getById(exchange.getProductId());
        if (product == null) {
            return ApiResponse.error("商品不存在");
        }

        // 检查商品状态
        if (!"active".equals(product.getStatus())) {
            return ApiResponse.error("商品已下架，无法兑换");
        }

        // 检查库存
        if (product.getStock() <= 0) {
            return ApiResponse.error("商品库存不足");
        }

        // 设置初始值
        exchange.setId(null);
        exchange.setPoints(product.getPoints());
        exchange.setQuantity(exchange.getQuantity() != null ? exchange.getQuantity() : 1);
        exchange.setTotalPoints(exchange.getPoints() * exchange.getQuantity());

        // 获取用户积分余额
        Integer userBalance = pointsRecordService.getUserBalance(exchange.getUserId());

        // 检查积分是否足够
        if (userBalance < exchange.getTotalPoints()) {
            return ApiResponse.error("积分不足，无法兑换");
        }
        exchange.setStatus("pending");
        exchange.setCreatedAt(LocalDateTime.now());
        exchange.setUpdatedAt(LocalDateTime.now());

        // 保存兑换记录
        boolean saved = save(exchange);

        if (saved) {
            // 减少商品库存
            boolean decreased = pointsProductService.decreaseStock(product.getId(), exchange.getQuantity());

            if (decreased) {
                // 扣减用户积分
                boolean recorded = pointsRecordService.recordPointsChange(
                    exchange.getUserId(),
                    -exchange.getTotalPoints(),
                    "exchange",
                    "兑换商品：" + product.getName() + " x" + exchange.getQuantity(),
                    product.getId(),
                    "product",
                    "system"
                );

                if (recorded) {
                    return ApiResponse.success(Map.of(
                        "id", exchange.getId(),
                        "success", true
                    ));
                }
            }

            // 如果库存减少或积分记录失败，回滚事务
            throw new RuntimeException("兑换处理失败");
        }

        return ApiResponse.error("创建兑换记录失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> updateStatus(Integer id, String status) {
        // 检查兑换记录是否存在
        PointsExchange exchange = getById(id);
        if (exchange == null) {
            return ApiResponse.error("兑换记录不存在");
        }

        // 检查状态值
        List<String> validStatus = List.of("pending", "approved", "shipped", "delivered", "completed", "cancelled");
        if (!validStatus.contains(status)) {
            return ApiResponse.error("状态值无效");
        }

        // 检查状态流转是否合法
        String currentStatus = exchange.getStatus();
        boolean isValid = false;

        switch (currentStatus) {
            case "pending":
                // 待审核可以变为已审核或已取消
                isValid = "approved".equals(status) || "cancelled".equals(status);
                break;
            case "approved":
                // 已审核可以变为已发货或已取消
                isValid = "shipped".equals(status) || "cancelled".equals(status);
                break;
            case "shipped":
                // 已发货可以变为已送达
                isValid = "delivered".equals(status);
                break;
            case "delivered":
                // 已送达可以变为已完成
                isValid = "completed".equals(status);
                break;
            default:
                // 其他状态不可变更
                isValid = false;
                break;
        }

        if (!isValid) {
            return ApiResponse.error("状态流转不合法");
        }

        // 如果取消兑换，需要恢复库存和积分
        if ("cancelled".equals(status) && !"cancelled".equals(currentStatus)) {
            // 恢复商品库存
            pointsProductService.increaseStock(exchange.getProductId(), 1);

            // 恢复用户积分
            pointsRecordService.recordPointsChange(
                exchange.getUserId(),
                exchange.getPoints(),
                "exchange_cancel",
                "取消兑换，返还积分",
                exchange.getProductId(),
                "product",
                "system"
            );
        }

        // 更新状态和相应的时间字段
        exchange.setStatus(status);

        // 更新状态和时间
        exchange.setStatus(status);
        exchange.setUpdatedAt(LocalDateTime.now());

        // 更新记录
        if (updateById(exchange)) {
            return ApiResponse.success(Map.of("success", true));
        }
        return ApiResponse.error("更新状态失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> ship(Integer id, String company, String trackingNumber, String remark) {
        // 检查兑换记录是否存在
        PointsExchange exchange = getById(id);
        if (exchange == null) {
            return ApiResponse.error("兑换记录不存在");
        }

        // 检查状态是否为已审核
        if (!"approved".equals(exchange.getStatus())) {
            return ApiResponse.error("只有已审核的记录才能发货");
        }

        // 检查物流信息
        if (StringUtils.isBlank(company) || StringUtils.isBlank(trackingNumber)) {
            return ApiResponse.error("物流公司和物流单号不能为空");
        }

        // 更新发货信息
        exchange.setStatus("shipped");

        // 构建物流信息JSON
        String expressInfo = String.format("{\"company\":\"%s\",\"trackingNumber\":\"%s\"}",
                company != null ? company : "", trackingNumber != null ? trackingNumber : "");
        exchange.setExpressInfo(expressInfo);

        if (StringUtils.isNotBlank(remark)) {
            exchange.setRemark(remark);
        }
        exchange.setUpdatedAt(LocalDateTime.now());

        // 更新记录
        if (updateById(exchange)) {
            return ApiResponse.success(Map.of("success", true));
        }
        return ApiResponse.error("发货操作失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> batchShip(List<Map<String, Object>> shipments, String company, String remark) {
        if (shipments == null || shipments.isEmpty()) {
            return ApiResponse.error("没有提供发货信息");
        }

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        List<Map<String, Object>> results = new ArrayList<>();

        for (Map<String, Object> shipment : shipments) {
            Integer id = (Integer) shipment.get("id");
            String trackingNumber = (String) shipment.get("trackingNumber");

            // 使用通用物流公司，除非指定了单独的
            String shipCompany = shipment.get("company") != null ?
                              (String) shipment.get("company") : company;

            // 使用通用备注，除非指定了单独的
            String shipRemark = shipment.get("remark") != null ?
                             (String) shipment.get("remark") : remark;

            ApiResponse<Map<String, Object>> response = ship(id, shipCompany, trackingNumber, shipRemark);

            Map<String, Object> result = new HashMap<>();
            result.put("id", id);
            result.put("success", response.getCode() == 200);
            result.put("message", response.getCode() == 200 ? "发货成功" : response.getMessage());
            results.add(result);

            if (response.getCode() == 200) {
                successCount.incrementAndGet();
            } else {
                failCount.incrementAndGet();
            }
        }

        return ApiResponse.success(Map.of(
            "totalCount", shipments.size(),
            "successCount", successCount.get(),
            "failCount", failCount.get(),
            "results", results
        ));
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> result = new HashMap<>();

        // 获取总兑换记录数
        long totalExchanges = count();
        result.put("totalExchanges", totalExchanges);

        // 获取各状态的数量
        QueryWrapper<PointsExchange> statusWrapper = new QueryWrapper<>();
        statusWrapper.select("status, COUNT(*) as count")
                   .groupBy("status");
        List<Map<String, Object>> statusCounts = listMaps(statusWrapper);
        result.put("statusCounts", statusCounts);

        // 获取总消耗积分
        QueryWrapper<PointsExchange> pointsWrapper = new QueryWrapper<>();
        pointsWrapper.select("SUM(points) as totalPoints");
        Map<String, Object> pointsMap = getMap(pointsWrapper);
        int totalPoints = pointsMap != null && pointsMap.get("totalPoints") != null ?
                        Integer.parseInt(pointsMap.get("totalPoints").toString()) : 0;
        result.put("totalPoints", totalPoints);

        // 获取今日兑换数
        QueryWrapper<PointsExchange> todayWrapper = new QueryWrapper<>();
        todayWrapper.apply("DATE(exchange_time) = CURDATE()");
        long todayExchanges = count(todayWrapper);
        result.put("todayExchanges", todayExchanges);

        // 获取本月兑换趋势
        QueryWrapper<PointsExchange> trendWrapper = new QueryWrapper<>();
        trendWrapper.select("DATE(exchange_time) as date, COUNT(*) as count")
                  .apply("MONTH(exchange_time) = MONTH(CURDATE()) AND YEAR(exchange_time) = YEAR(CURDATE())")
                  .groupBy("date")
                  .orderByAsc("date");
        List<Map<String, Object>> monthlyTrend = listMaps(trendWrapper);
        result.put("monthlyTrend", monthlyTrend);

        // 获取热门商品TOP5
        QueryWrapper<PointsExchange> hotWrapper = new QueryWrapper<>();
        hotWrapper.select("e.product_id as id, p.name as name, COUNT(*) as count")
                .apply("e.product_id = p.id")
                .groupBy("e.product_id")
                .orderByDesc("count")
                .last("LIMIT 5");
        List<Map<String, Object>> hotProducts = baseMapper.selectMaps(hotWrapper);
        result.put("hotProducts", hotProducts);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> batchUpdateStatus(List<Integer> ids, String status, String remark) {
        if (ids == null || ids.isEmpty()) {
            return ApiResponse.error("请选择需要更新的记录");
        }

        // 检查状态值
        List<String> validStatus = List.of("pending", "approved", "shipped", "delivered", "completed", "cancelled", "rejected");
        if (!validStatus.contains(status)) {
            return ApiResponse.error("状态值无效");
        }

        int successCount = 0;
        int failCount = 0;

        for (Integer id : ids) {
            try {
                ApiResponse<Map<String, Object>> response = updateStatus(id, status);
                if (response.getCode() == 200) {
                    // 更新成功后，如果有备注，单独更新备注
                    if (StringUtils.isNotBlank(remark)) {
                        PointsExchange exchange = new PointsExchange();
                        exchange.setId(id);
                        exchange.setRemark(remark);
                        updateById(exchange);
                    }
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.error("批量更新兑换状态失败，ID: " + id, e);
                failCount++;
            }
        }

        return ApiResponse.success(Map.of(
            "success", true,
            "successCount", successCount,
            "failCount", failCount
        ));
    }
}
