package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.home.News;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface NewsMapper extends BaseMapper<News> {
    /**
     * 更新news浏览量
     *
     * @param newsId newsID
     * @return 影响行数
     */
    @Update("UPDATE news SET view_count = view_count + 1 WHERE id = #{newsId}")
    int incrementViewCount(@Param("newsId") Integer newsId);
}
