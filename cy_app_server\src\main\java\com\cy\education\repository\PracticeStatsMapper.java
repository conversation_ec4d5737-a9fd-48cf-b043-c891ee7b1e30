package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.PracticeStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 练习统计Mapper接口
 */
@Mapper
public interface PracticeStatsMapper extends BaseMapper<PracticeStats> {
    
    /**
     * 根据用户ID查询练习统计
     * @param userId 用户ID
     * @return 练习统计列表
     */
    List<PracticeStats> selectByUserId(@Param("userId") Integer userId);
    
    /**
     * 根据用户ID和题库ID查询练习统计
     * @param userId 用户ID
     * @param bankId 题库ID
     * @return 练习统计
     */
    PracticeStats selectByUserIdAndBankId(@Param("userId") Integer userId, @Param("bankId") Integer bankId);
    
    /**
     * 根据题库ID查询练习统计
     * @param bankId 题库ID
     * @return 练习统计列表
     */
    List<PracticeStats> selectByBankId(@Param("bankId") Integer bankId);
} 