<template>
  <div class="import-export-page">
    <div class="page-header">
      <h2>数据导入导出管理</h2>
      <p>统一管理系统中各类数据的导入导出功能</p>
    </div>
    
    <div class="function-grid">
      <!-- 管理员导入导出 -->
      <div class="function-card">
        <div class="card-header">
          <el-icon class="card-icon"><User /></el-icon>
          <h3>管理员管理</h3>
        </div>
        <div class="card-content">
          <p>批量导入导出管理员账户信息</p>
          <div class="card-actions">
            <el-button type="primary" :icon="Upload" @click="openAdminImport">
              导入管理员
            </el-button>
            <el-button type="success" :icon="Download" @click="openAdminExport">
              导出管理员
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 学员导入导出 -->
      <div class="function-card">
        <div class="card-header">
          <el-icon class="card-icon"><UserFilled /></el-icon>
          <h3>学员管理</h3>
        </div>
        <div class="card-content">
          <p>批量导入导出学员账户和基本信息</p>
          <div class="card-actions">
            <el-button type="primary" :icon="Upload" @click="goToStudentPage">
              导入学员
            </el-button>
            <el-button type="success" :icon="Download" @click="goToStudentPage">
              导出学员
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 题库导入导出 -->
      <div class="function-card">
        <div class="card-header">
          <el-icon class="card-icon"><EditPen /></el-icon>
          <h3>题库管理</h3>
        </div>
        <div class="card-content">
          <p>批量导入导出题目，支持多种题型</p>
          <div class="card-actions">
            <el-button type="primary" :icon="Upload" @click="openQuestionImport">
              导入题目
            </el-button>
            <el-button type="success" :icon="Download" @click="openQuestionExport">
              导出题目
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 试卷下载 -->
      <div class="function-card">
        <div class="card-header">
          <el-icon class="card-icon"><Document /></el-icon>
          <h3>试卷下载</h3>
        </div>
        <div class="card-content">
          <p>生成格式良好的Word试卷文档</p>
          <div class="card-actions">
            <el-button type="warning" :icon="DocumentCopy" @click="openPaperDownload">
              下载试卷
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 考试记录导出 -->
      <div class="function-card">
        <div class="card-header">
          <el-icon class="card-icon"><DataAnalysis /></el-icon>
          <h3>考试记录</h3>
        </div>
        <div class="card-content">
          <p>导出考试记录和成绩统计信息</p>
          <div class="card-actions">
            <el-button type="success" :icon="Download" @click="goToExamRecords">
              导出记录
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 学习记录导出 -->
      <div class="function-card">
        <div class="card-header">
          <el-icon class="card-icon"><Reading /></el-icon>
          <h3>学习记录</h3>
        </div>
        <div class="card-content">
          <p>导出学习进度和时长统计信息</p>
          <div class="card-actions">
            <el-button type="success" :icon="Download" @click="goToStudyRecords">
              导出记录
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 管理员导入导出对话框 -->
    <AdminImportExport ref="adminImportExportRef" />
    
    <!-- 题库导入导出对话框 -->
    <QuestionImportExport 
      ref="questionImportExportRef" 
      :bank-list="bankList"
      @import-success="handleQuestionImportSuccess"
      @export-success="handleQuestionExportSuccess"
    />
    
    <!-- 试卷下载对话框 -->
    <PaperDownloadDialog
      ref="paperDownloadRef"
      :paper-list="paperList"
      @download-success="handlePaperDownloadSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  User,
  UserFilled,
  EditPen,
  Document,
  DataAnalysis,
  Reading,
  Upload,
  Download,
  DocumentCopy
} from '@element-plus/icons-vue'
import AdminImportExport from '@/views/system/admin/components/ImportExportDialog.vue'
import QuestionImportExport from '@/views/exam/question-bank/components/QuestionImportExport.vue'
import PaperDownloadDialog from './components/PaperDownloadDialog.vue'

const router = useRouter()

// 组件引用
const adminImportExportRef = ref()
const questionImportExportRef = ref()
const paperDownloadRef = ref()

// 数据
const bankList = ref<any[]>([])
const paperList = ref<any[]>([])

// 管理员操作
const openAdminImport = () => {
  adminImportExportRef.value?.openImport()
}

const openAdminExport = () => {
  adminImportExportRef.value?.openExport()
}

// 学员操作
const goToStudentPage = () => {
  router.push('/student/info')
}

// 题库操作
const openQuestionImport = () => {
  questionImportExportRef.value?.openImport()
}

const openQuestionExport = () => {
  questionImportExportRef.value?.openExport()
}

// 试卷下载
const openPaperDownload = () => {
  paperDownloadRef.value?.open()
}

// 考试记录
const goToExamRecords = () => {
  router.push('/exam/records')
}

// 学习记录
const goToStudyRecords = () => {
  router.push('/study/records')
}

// 事件处理
const handleQuestionImportSuccess = () => {
  ElMessage.success('题目导入成功')
}

const handleQuestionExportSuccess = () => {
  ElMessage.success('题目导出成功')
}

const handlePaperDownloadSuccess = () => {
  ElMessage.success('试卷下载成功')
}

// 加载数据
const loadData = async () => {
  try {
    // 加载题库列表
    // bankList.value = await getBankList()
    bankList.value = [
      { id: 1, name: 'JavaScript基础题库' },
      { id: 2, name: 'Vue.js进阶题库' },
      { id: 3, name: '数据库题库' }
    ]
    
    // 加载试卷列表
    // paperList.value = await getPaperList()
    paperList.value = [
      { id: 1, title: 'JavaScript基础测试', totalScore: 100, duration: 90 },
      { id: 2, title: 'Vue.js进阶考试', totalScore: 120, duration: 120 },
      { id: 3, title: '综合能力测试', totalScore: 150, duration: 150 }
    ]
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.import-export-page {
  padding: 24px;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.function-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.function-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 24px 24px 16px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.card-icon {
  font-size: 24px;
  color: #409eff;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.card-content {
  padding: 24px;
}

.card-content p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
}

.card-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.card-actions .el-button {
  flex: 1;
  min-width: 120px;
}

@media (max-width: 768px) {
  .function-grid {
    grid-template-columns: 1fr;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .card-actions .el-button {
    width: 100%;
  }
}
</style>
