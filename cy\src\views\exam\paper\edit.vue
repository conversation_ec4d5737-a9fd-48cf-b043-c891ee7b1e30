<template>
  <div class="paper-edit">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑试卷' : '新增试卷' }}</h2>
      <div v-if="form.isPublished" class="published-warning">
        <el-alert
          title="试卷已发布，不可编辑"
          type="warning"
          show-icon
          :closable="false"
          style="margin-right: 20px;"
        />
      </div>
      <div class="header-actions">
        <el-button @click="handleCancel">返回列表</el-button>
        <el-button type="primary" @click="handleSave" :disabled="form.isPublished">保存试卷</el-button>
        <el-button type="success" @click="handlePreview">预览试卷</el-button>
      </div>
    </div>
    
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="paper-form">
      <el-card class="basic-info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="试卷名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入试卷名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考试时长(分钟)" prop="duration">
              <el-input-number v-model="form.duration" :min="1" :max="240" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="及格分数" prop="passingScore">
              <el-input-number v-model="form.passingScore" :min="0" :max="form.totalScore || 100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="试卷总分">
              <span>{{ form.totalScore || 0 }}分</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="试卷说明" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3" placeholder="请输入试卷说明" />
        </el-form-item>
      </el-card>
      
      <div class="content-container">
        <el-card class="question-bank-card">
          <template #header>
            <div class="card-header">
              <span>题库题目</span>
              <div class="filter-actions">
                <el-select v-model="selectedBankId" placeholder="选择题库" clearable @change="handleBankChange" style="width: 160px;">
                  <el-option
                    v-for="bank in bankList"
                    :key="bank.id"
                    :label="bank.name"
                    :value="bank.id"
                  />
                </el-select>
                <el-select v-model="filterType" placeholder="题目类型" clearable @change="handleFilterChange" style="margin-left: 10px;">
                  <el-option v-for="type in questionTypes" :key="type.value" :label="type.label" :value="type.value" />
                </el-select>
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索题目"
                  clearable
                  style="width: 180px; margin-left: 10px;"
                  @clear="handleFilterChange"
                  @keyup.enter="handleFilterChange"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </div>
          </template>
          <div class="question-list">
            <div v-for="(question, index) in filteredQuestions" :key="question.id" class="question-item">
              <div class="question-header">
                <span class="question-type-tag" :class="'type-' + question.type">{{ getQuestionTypeName(question.type) }}</span>
                <el-button type="primary" link @click="handleAddQuestion(question)">添加</el-button>
              </div>
              <div class="question-content" v-html="question.title"></div>
            </div>
            <el-empty v-if="filteredQuestions.length === 0" description="没有匹配的题目" />
          </div>
        </el-card>
        
        <el-card class="paper-content-card">
          <template #header>
            <div class="card-header">
              <span>试卷内容</span>
              <div class="action-buttons">
                <el-button type="danger" @click="handleClearPaper">清空试卷</el-button>
              </div>
            </div>
          </template>
          <div class="paper-questions-container">
            <draggable
              v-model="form.questions"
              group="questions"
              item-key="id"
              handle=".question-drag-handle"
              class="question-list"
              ghost-class="ghost-question"
              @end="updateScores"
            >
              <template #item="{ element: question, index: questionIndex }">
                <div class="paper-question-item">
                  <div class="paper-question-header">
                    <el-icon class="question-drag-handle"><DCaret /></el-icon>
                    <span class="question-index">{{ questionIndex + 1 }}.</span>
                    <span class="question-type-tag" :class="'type-' + question.type">{{ getQuestionTypeName(question.type) }}</span>
                    
                    <div class="question-score-setting">
                      <span>分值:</span>
                      <el-input-number 
                        v-model="question.score" 
                        :min="0" 
                        :max="100" 
                        :step="0.5"
                        size="small"
                        controls-position="right"
                        @change="updateScores"
                      />
                    </div>
                    
                    <div class="question-actions">
                      <el-button type="danger" link @click="removeQuestion(questionIndex)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                  <div class="paper-question-content" v-html="question.title"></div>
                </div>
              </template>
              <template #footer>
                <div v-if="form.questions.length === 0" class="empty-paper-tip">
                  <el-empty description="请从左侧添加题目" />
                </div>
              </template>
            </draggable>
          </div>
        </el-card>
      </div>
    </el-form>
    
    <!-- 预览弹窗 -->
    <el-dialog
      v-model="previewVisible"
      title="试卷预览"
      width="800px"
      destroy-on-close
    >
      <div class="preview-container">
        <div class="paper-header">
          <h1>{{ form.title || '未命名试卷' }}</h1>
          <div class="paper-info">
            <span>总分：{{ form.totalScore || 0 }}分</span>
            <span>考试时长：{{ form.duration || 0 }}分钟</span>
            <span>及格分数：{{ form.passingScore || 0 }}分</span>
          </div>
          <div class="paper-description" v-if="form.description">
            {{ form.description }}
          </div>
        </div>
        
        <div class="question-sections" v-if="form.questions && form.questions.length > 0">
          <div v-for="(question, questionIndex) in form.questions" :key="questionIndex" class="question-item">
            <div class="question-header">
              <span class="question-index">{{ questionIndex + 1 }}.</span>
              <span class="question-type">[{{ getQuestionTypeName(question.type) }}]</span>
              <span class="question-score">{{ question.score || 0 }}分</span>
            </div>
            <div class="question-content" v-html="question.title"></div>
            
            <!-- 选择题选项 -->
            <div v-if="['single', 'multiple'].includes(question.type)" class="question-options">
              <div v-for="(option, optionIndex) in question.options" :key="optionIndex" class="option-item">
                <span class="option-label">{{ String.fromCharCode(65 + optionIndex) }}.</span>
                <span class="option-content" v-html="typeof option === 'object' ? option.content : option"></span>
              </div>
            </div>
            
            <!-- 判断题选项 -->
            <div v-if="question.type === 'judgment' || question.type === 'judge'" class="question-options">
              <div class="option-item">
                <span class="option-label">A.</span>
                <span class="option-content">正确</span>
              </div>
              <div class="option-item">
                <span class="option-label">B.</span>
                <span class="option-content">错误</span>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-else description="试卷内容为空" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Search, DCaret, Delete } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { getBankList, getQuestionList, getPaperDetail, createPaper, updatePaper, addQuestionsToPaper, updatePaperQuestions, type Bank, type Question, type Paper } from '@/api/exam'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()

// 判断是编辑还是新增
const isEdit = computed(() => {
  return route.params.id !== '0' && route.params.id !== undefined
})

// 表单数据
const form = reactive<{
  id: string
  title: string
  description: string
  duration: number
  passingScore: number
  totalScore: number
  questions: any[]
  isPublished: boolean
}>({
  id: '',
  title: '',
  description: '',
  duration: 60,
  passingScore: 60,
  totalScore: 0,
  questions: [],
  isPublished: false
})

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入试卷名称', trigger: 'blur' }],
  duration: [{ required: true, message: '请设置考试时长', trigger: 'blur' }],
  passingScore: [{ required: true, message: '请设置及格分数', trigger: 'blur' }]
}

// 题目类型选项
const questionTypes = [
  { value: 'single', label: '单选题' },
  { value: 'multiple', label: '多选题' },
  { value: 'judgment', label: '判断题' },
  { value: 'fill', label: '填空题' },
  { value: 'essay', label: '简答题' }
]

// 筛选条件
const filterType = ref('')
const searchKeyword = ref('')

// 预览弹窗
const previewVisible = ref(false)

// 题库列表
const bankList = ref<Bank[]>([])
const selectedBankId = ref('')

// 题库中的题目列表
const questionBank = ref<Question[]>([])

// 获取题目类型名称
const getQuestionTypeName = (type: string) => {
  // 支持前后端可能不同的题型命名
  const typeMapping: Record<string, string> = {
    'single': '单选题',
    'multiple': '多选题',
    'judgment': '判断题',
    'judge': '判断题',
    'fill': '填空题',
    'essay': '简答题'
  }
  return typeMapping[type] || '未知类型'
}

// 过滤后的题目列表
const filteredQuestions = computed(() => {
  let result = questionBank.value

  if (filterType.value) {
    result = result.filter(q => q.type === filterType.value)
  }
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(q => 
      q.title.toLowerCase().includes(keyword) || 
      (q.options && q.options.some((opt: string) => opt.toLowerCase().includes(keyword)))
    )
  }
  
  return result
})

// 筛选变化处理
const handleFilterChange = () => {
  // 筛选条件变化，不需要额外处理，filteredQuestions会自动更新
}

// 添加题目到试卷
const handleAddQuestion = (question: Question) => {
  console.log('添加题目:', question)
  
  // 复制题目，避免直接引用原题目对象
  const newQuestion: any = {
    id: question.id,
    type: question.type,
    title: question.title,
    score: getDefaultScore(question.type),
    correctAnswer: question.correctAnswer,
    options: []
  }
  
  // 处理选项
  if (question.type === 'single' || question.type === 'multiple') {
    try {
      let parsedOptions = [];
      
      if (typeof question.options === 'string') {
        parsedOptions = JSON.parse(question.options);
      } else if (Array.isArray(question.options)) {
        parsedOptions = question.options;
      } else if (typeof question.options === 'object') {
        parsedOptions = Object.values(question.options);
      }
      
      // 标准化选项格式
      newQuestion.options = parsedOptions.map((opt: any) => {
        if (typeof opt === 'string') {
          return opt;
        } else if (typeof opt === 'object' && opt !== null) {
          return opt.content || JSON.stringify(opt);
        } else {
          return String(opt);
        }
      });
      
      console.log('处理后的选项:', newQuestion.options);
    } catch (e) {
      console.error('解析选项出错:', e)
      newQuestion.options = []
    }
  }
  
  form.questions.push(newQuestion)
  updateScores()
  ElMessage.success('题目添加成功')
}

// 根据题型获取默认分数
const getDefaultScore = (type: string) => {
  const scoreMap: Record<string, number> = {
    'single': 3,
    'multiple': 4,
    'judgment': 2,
    'judge': 2,
    'fill': 2,
    'essay': 10
  }
  return scoreMap[type] || 5
}

// 移除题目
const removeQuestion = (questionIndex: number) => {
  form.questions.splice(questionIndex, 1)
  updateScores()
}

// 更新分数统计
const updateScores = () => {
  // 计算试卷总分
  form.totalScore = form.questions.reduce((total, question) => total + (question.score || 0), 0)
}

// 清空试卷
const handleClearPaper = () => {
  ElMessageBox.confirm(
    '确定要清空试卷内容吗？此操作不可恢复！',
    '清空确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    form.questions = []
    form.totalScore = 0
    ElMessage.success('试卷已清空')
  }).catch(() => {})
}

// 预览试卷
const handlePreview = () => {
  if (form.questions.length === 0) {
    ElMessage.warning('试卷内容为空，无法预览')
    return
  }
  
  console.log('预览试卷中的题目:', form.questions)
  previewVisible.value = true
}

// 保存试卷
const handleSave = () => {
  if (!formRef.value) return
  
  // 检查试卷是否已发布
  if (form.isPublished) {
    ElMessage.error('试卷已发布，不能修改')
    return
  }
  
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      ElMessage.error('请填写必填项')
      return
    }
    
    if (form.questions.length === 0) {
      ElMessage.warning('试卷内容不能为空')
      return
    }
    
    try {
      // 构造保存参数
      const params = {
        title: form.title,
        description: form.description,
        duration: form.duration,
        passingScore: form.passingScore,
        totalScore: form.totalScore,
        questions: form.questions.map((q, index) => ({
          questionId: q.id,
          score: q.score,
          questionOrder: index + 1  // 使用questionOrder字段
        }))
      }
      
      if (isEdit.value) {
        // 更新已有试卷
        await updatePaper(form.id, {
          title: params.title,
          description: params.description,
          duration: params.duration,
          passingScore: params.passingScore,
          totalScore: params.totalScore
        })
        
        // 更新试卷题目
        if (params.questions.length > 0) {
          await updatePaperQuestions(form.id, params.questions)
        } else {
          // 如果没有题目，清空试卷题目
          await updatePaperQuestions(form.id, [])
        }
        
        ElMessage.success('更新成功')
      } else {
        // 创建新试卷
        const result = await createPaper({
          title: params.title,
          description: params.description,
          duration: params.duration,
          passingScore: params.passingScore,
          totalScore: params.totalScore
        })
        
        // 添加题目到试卷
        if (params.questions.length > 0 && result.id) {
          await addQuestionsToPaper(result.id, params.questions)
        }
        
        ElMessage.success('创建成功')
      }
      
      router.push('/exam/paper')
      
    } catch (error) {
      console.error('保存试卷失败:', error)
      ElMessage.error('保存试卷失败')
    }
  })
}

// 取消编辑，返回列表
const handleCancel = () => {
  router.push('/exam/paper')
}

// 获取试卷详情
const fetchPaperDetail = async (id: string) => {
  try {
    // 调用API获取试卷详情
    const paperDetail = await getPaperDetail(id)
    console.log('获取到的试卷详情:', paperDetail)
    
    // 将API返回的数据格式转换为表单所需的格式
      Object.assign(form, {
      id: paperDetail.id,
      title: paperDetail.title,
      description: paperDetail.description,
      duration: paperDetail.duration,
      passingScore: paperDetail.passingScore,
      totalScore: paperDetail.totalScore,
      isPublished: paperDetail.isPublished,
      // 转换试题格式
      questions: paperDetail.questions?.map(item => {
        console.log('处理题目:', item)
        let options = []
        
        // 处理选项 - 尝试多种可能的格式
        if (item.question.type === 'single' || item.question.type === 'multiple') {
          try {
            let parsedOptions = [];
            
            if (typeof item.question.options === 'string') {
              parsedOptions = JSON.parse(item.question.options);
            } else if (Array.isArray(item.question.options)) {
              parsedOptions = item.question.options;
            } else if (typeof item.question.options === 'object') {
              parsedOptions = Object.values(item.question.options);
            }
            
            // 标准化选项格式
            options = parsedOptions.map((opt: any) => {
              if (typeof opt === 'string') {
                return opt;
              } else if (typeof opt === 'object' && opt !== null) {
                return opt.content || JSON.stringify(opt);
              } else {
                return String(opt);
              }
            });
            
            console.log('处理后的选项:', options);
          } catch (e) {
            console.error('解析选项出错:', e)
            options = []
          }
        }
        
        return {
          id: item.questionId, // 使用题目ID作为题目唯一标识
          type: item.question.type,
          title: item.question.title,
          score: item.score,
          options: options,
          correctAnswer: item.question.correctAnswer
        }
      }) || []
    })
    
    // 计算总分
    updateScores()
    console.log('转换后的表单数据:', form)
    
  } catch (error) {
    console.error('获取试卷详情失败:', error)
    ElMessage.error('获取试卷详情失败')
  }
}

// 获取题库列表
const fetchBankList = async () => {
  try {
    const res = await getBankList({
      page: 1,
      limit: 50,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })
    bankList.value = res.list
  } catch (error) {
    console.error('获取题库列表失败:', error)
    ElMessage.error('获取题库列表失败')
  }
}

// 获取题库题目
const fetchQuestionsByBank = async (bankId: string) => {
  try {
    const res = await getQuestionList({
      page: 1,
      limit: 200, // 获取更多题目
      bankId,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })
    console.log('获取到的题目列表:', res.list)
    questionBank.value = res.list
  } catch (error) {
    console.error('获取题目列表失败:', error)
    ElMessage.error('获取题目列表失败')
  }
}

// 题库选择变化处理
const handleBankChange = () => {
  if (selectedBankId.value) {
    fetchQuestionsByBank(selectedBankId.value)
  } else {
    questionBank.value = []
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await fetchBankList()
  
  if (isEdit.value) {
    await fetchPaperDetail(route.params.id as string)
  }
})
</script>

<style scoped>
.paper-edit {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.published-warning {
  flex-grow: 1;
  margin: 0 20px;
}

.basic-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-container {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.question-bank-card {
  flex: 1;
  min-width: 400px;
}

.paper-content-card {
  flex: 1.5;
  min-width: 600px;
}

.filter-actions {
  display: flex;
  align-items: center;
}

.question-list {
  max-height: 600px;
  overflow-y: auto;
  padding: 10px 0;
}

.question-item {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
}

.question-item:hover {
  background-color: #f5f7fa;
}

.question-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.question-type-tag {
  padding: 2px 6px;
  font-size: 12px;
  border-radius: 4px;
  margin-right: 8px;
}

.type-single {
  background-color: #e1f3ff;
  color: #1890ff;
}

.type-multiple {
  background-color: #e8f4d9;
  color: #52c41a;
}

.type-judgment {
  background-color: #ffefd5;
  color: #fa8c16;
}

.type-fill {
  background-color: #f0e6ff;
  color: #722ed1;
}

.type-essay {
  background-color: #ffe6eb;
  color: #eb2f96;
}

.question-content {
  line-height: 1.6;
  margin-bottom: 10px;
  font-size: 14px;
}

.paper-question-item {
  margin-bottom: 16px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.paper-question-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.question-drag-handle {
  cursor: move;
  margin-right: 8px;
  color: #909399;
}

.question-index {
  margin-right: 8px;
  font-weight: bold;
}

.question-score-setting {
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-right: 10px;
}

.question-score-setting span {
  margin-right: 5px;
}

.question-actions {
  display: flex;
  align-items: center;
}

.paper-question-content {
  padding-left: 24px;
  line-height: 1.6;
  font-size: 14px;
}

.empty-paper-tip {
  padding: 40px;
  text-align: center;
}

.ghost-question {
  opacity: 0.5;
  background: #c8ebfb;
}

/* 预览弹窗样式 */
.preview-container {
  padding: 10px;
  max-height: 70vh;
  overflow-y: auto;
}

.paper-header {
  text-align: center;
  margin-bottom: 30px;
}

.paper-header h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

.paper-info {
  display: flex;
  justify-content: center;
  gap: 20px;
  color: #666;
}

.paper-description {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.question-item {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #eee;
}

.question-header {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.question-index {
  font-weight: bold;
  margin-right: 5px;
}

.question-type {
  color: #409eff;
  margin-right: 10px;
  font-size: 14px;
}

.question-score {
  color: #ff9900;
  font-size: 14px;
}

.question-options {
  margin-left: 20px;
  margin-bottom: 15px;
}

.option-item {
  margin-bottom: 8px;
  display: flex;
}

.option-label {
  margin-right: 8px;
  font-weight: 500;
}
</style> 