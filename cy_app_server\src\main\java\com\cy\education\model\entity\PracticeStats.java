package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 练习统计实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("practice_stats")
public class PracticeStats implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 学员ID
     */
    private Integer userId;
    
    /**
     * 题库ID
     */
    private Integer bankId;
    
    /**
     * 题库总题数
     */
    private Integer totalQuestions;
    
    /**
     * 已答题数
     */
    private Integer answeredQuestions;
    
    /**
     * 正确数
     */
    private Integer correctCount;
    
    /**
     * 错误数
     */
    private Integer wrongCount;
    
    /**
     * 正确率
     */
    private BigDecimal accuracyRate;
    
    /**
     * 最后练习时间
     */
    private LocalDateTime lastPracticeTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 