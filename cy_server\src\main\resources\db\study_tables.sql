-- 学习记录相关表结构
-- 创建时间: 2025-06-27

-- --------------------------------------------------------

--
-- 表的结构 `study_records`
-- 学习记录表，记录学员的学习进度和状态
--

CREATE TABLE `study_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `course_id` int NOT NULL COMMENT '课程ID',
  `lesson_id` int DEFAULT NULL COMMENT '课时ID（可选，为空表示整体课程记录）',
  `resource_id` int DEFAULT NULL COMMENT '资源ID',
  `resource_type` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资源类型：video, file, article',
  `progress` int DEFAULT '0' COMMENT '学习进度（百分比）',
  `duration` int DEFAULT '0' COMMENT '学习时长（秒）',
  `completed` tinyint DEFAULT '0' COMMENT '是否完成：0未完成，1已完成',
  `last_position` int DEFAULT '0' COMMENT '最后学习位置（秒）',
  `last_study_time` datetime DEFAULT NULL COMMENT '最后学习时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_course_lesson` (`user_id`, `course_id`, `lesson_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_lesson_id` (`lesson_id`),
  KEY `idx_last_study_time` (`last_study_time`),
  KEY `idx_completed` (`completed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学习记录表';

-- --------------------------------------------------------

--
-- 表的结构 `study_logs`
-- 学习日志表，记录详细的学习行为
--

CREATE TABLE `study_logs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `record_id` int DEFAULT NULL COMMENT '学习记录ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `course_id` int NOT NULL COMMENT '课程ID',
  `lesson_id` int DEFAULT NULL COMMENT '课时ID',
  `resource_id` int DEFAULT NULL COMMENT '资源ID',
  `start_position` int DEFAULT '0' COMMENT '开始位置（秒）',
  `end_position` int DEFAULT '0' COMMENT '结束位置（秒）',
  `duration` int DEFAULT '0' COMMENT '学习时长（秒）',
  `progress` int DEFAULT '0' COMMENT '学习进度（百分比）',
  `completed` tinyint DEFAULT '0' COMMENT '是否完成：0未完成，1已完成',
  `study_time` datetime NOT NULL COMMENT '学习时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_lesson_id` (`lesson_id`),
  KEY `idx_study_time` (`study_time`),
  CONSTRAINT `fk_study_logs_record` FOREIGN KEY (`record_id`) REFERENCES `study_records` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学习日志表';

-- --------------------------------------------------------

--
-- 插入一些示例数据
--

-- 示例学习记录数据
INSERT INTO `study_records` (`user_id`, `course_id`, `lesson_id`, `resource_id`, `resource_type`, `progress`, `duration`, `completed`, `last_position`, `last_study_time`) VALUES
(1, 1, 1, 1, 'video', 75, 1800, 0, 1350, '2025-06-27 10:30:00'),
(1, 1, 2, 2, 'article', 100, 600, 1, 0, '2025-06-27 11:15:00'),
(1, 2, 3, 3, 'video', 45, 900, 0, 405, '2025-06-27 14:20:00'),
(2, 1, 1, 1, 'video', 100, 2400, 1, 2400, '2025-06-27 09:45:00'),
(2, 1, 2, 2, 'article', 80, 480, 0, 0, '2025-06-27 10:30:00'),
(3, 2, 3, 3, 'video', 30, 540, 0, 270, '2025-06-27 16:10:00'),
(3, 3, 4, 4, 'file', 100, 300, 1, 0, '2025-06-27 13:25:00');

-- 示例学习日志数据
INSERT INTO `study_logs` (`record_id`, `user_id`, `course_id`, `lesson_id`, `resource_id`, `start_position`, `end_position`, `duration`, `progress`, `completed`, `study_time`) VALUES
(1, 1, 1, 1, 1, 0, 600, 600, 25, 0, '2025-06-27 10:00:00'),
(1, 1, 1, 1, 1, 600, 1350, 750, 75, 0, '2025-06-27 10:30:00'),
(2, 1, 1, 2, 2, 0, 0, 600, 100, 1, '2025-06-27 11:15:00'),
(3, 1, 2, 3, 3, 0, 405, 405, 45, 0, '2025-06-27 14:20:00'),
(4, 2, 1, 1, 1, 0, 1200, 1200, 50, 0, '2025-06-27 09:15:00'),
(4, 2, 1, 1, 1, 1200, 2400, 1200, 100, 1, '2025-06-27 09:45:00'),
(5, 2, 1, 2, 2, 0, 0, 480, 80, 0, '2025-06-27 10:30:00'),
(6, 3, 2, 3, 3, 0, 270, 270, 30, 0, '2025-06-27 16:10:00'),
(7, 3, 3, 4, 4, 0, 0, 300, 100, 1, '2025-06-27 13:25:00');

-- --------------------------------------------------------

--
-- 创建视图：学习记录详情视图
-- 包含学员姓名、部门名称、课程名称等关联信息
--

CREATE VIEW `v_study_records_detail` AS
SELECT 
    sr.id,
    sr.user_id,
    s.name AS student_name,
    d.name AS department_name,
    sr.course_id,
    c.name AS course_name,
    sr.lesson_id,
    sr.resource_id,
    sr.resource_type,
    sr.progress,
    sr.duration,
    sr.completed,
    sr.last_position,
    sr.last_study_time,
    sr.created_at,
    sr.updated_at
FROM study_records sr
LEFT JOIN students s ON sr.user_id = s.id
LEFT JOIN departments d ON s.department_id = d.id
LEFT JOIN courses c ON sr.course_id = c.id;

-- --------------------------------------------------------

--
-- 创建索引优化查询性能
--

-- 复合索引：用户+课程+更新时间（用于查询用户的课程学习记录）
CREATE INDEX `idx_user_course_updated` ON `study_records` (`user_id`, `course_id`, `updated_at` DESC);

-- 复合索引：课程+完成状态（用于统计课程完成情况）
CREATE INDEX `idx_course_completed` ON `study_records` (`course_id`, `completed`);

-- 复合索引：用户+学习时间（用于查询用户学习趋势）
CREATE INDEX `idx_user_study_time` ON `study_logs` (`user_id`, `study_time` DESC);

-- 复合索引：课程+学习时间（用于课程学习统计）
CREATE INDEX `idx_course_study_time` ON `study_logs` (`course_id`, `study_time` DESC);
