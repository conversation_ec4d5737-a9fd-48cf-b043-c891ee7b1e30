<template>
  <view class="chart-container">
    <view class="chart-header">
      <text class="chart-title">完成时间分布</text>
    </view>
    <view class="chart-content">
      <canvas
          id="durationDistributionChart"
          canvas-id="durationDistributionChart"
          class="chart-canvas"
          @touchend="touchEnd"
          @touchmove="touchMove"
          @touchstart="touchStart"
      ></canvas>
    </view>
    <view class="chart-legend">
      <view
          v-for="(item, index) in legendData"
          :key="index"
          :class="item.class"
          class="legend-item"
      >
        <view :style="{ backgroundColor: item.color }" class="legend-color"></view>
        <text class="legend-label">{{ item.name }}</text>
        <text class="legend-value">{{ item.value }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {ref, onMounted, watch} from 'vue'
import uCharts from '@qiun/ucharts'
import {durationDistributionColors} from './chartConfig'

interface DurationDistribution {
  veryFast: number
  fast: number
  normal: number
  slow: number
  verySlow: number
}

interface Props {
  data: DurationDistribution
}

const props = defineProps<Props>()

const chartInstance = ref<any>(null)
const legendData = ref<any[]>([
  {name: '非常快', value: 0, color: '#10b981', class: 'very-fast'},
  {name: '较快', value: 0, color: '#3b82f6', class: 'fast'},
  {name: '一般', value: 0, color: '#f59e0b', class: 'normal'},
  {name: '较慢', value: 0, color: '#8b5cf6', class: 'slow'},
  {name: '非常慢', value: 0, color: '#ef4444', class: 'very-slow'}
])

// 图表配置 - 改为玫瑰图
const chartConfig = {
  type: 'rose',
  context: null as any,
  width: 300,
  height: 250,
  series: [
    {
      name: '完成时间分布',
      data: [
        {name: '非常快', value: 5},
        {name: '较快', value: 12},
        {name: '一般', value: 18},
        {name: '较慢', value: 15},
        {name: '非常慢', value: 8}
      ]
    }
  ],
  color: durationDistributionColors,
  legend: {
    show: false
  },
  extra: {
    rose: {
      type: 'area',
      minRadius: 60,
      activeOpacity: 0.5,
      activeRadius: 10,
      offsetAngle: -90,
      labelWidth: 15,
      border: true,
      borderWidth: 2,
      borderColor: '#FFFFFF',
      linearType: 'custom',
      customColor: durationDistributionColors
    }
  },
  animation: true,
  background: '#FFFFFF',
  padding: [15, 15, 15, 15],
  dataLabel: true,
  dataPointShape: false
}

// 更新图表数据
const updateChartData = () => {
  if (!props.data) return

  const veryFast = Number(props.data.veryFast) || 0
  const fast = Number(props.data.fast) || 0
  const normal = Number(props.data.normal) || 0
  const slow = Number(props.data.slow) || 0
  const verySlow = Number(props.data.verySlow) || 0

  // 使用玫瑰图的数据格式
  const roseData = [
    {name: '非常快', value: veryFast},
    {name: '较快', value: fast},
    {name: '一般', value: normal},
    {name: '较慢', value: slow},
    {name: '非常慢', value: verySlow}
  ]

  chartConfig.series[0].data = roseData

  // 更新图例数据
  legendData.value = [
    {name: '非常快', value: veryFast, color: '#10b981', class: 'very-fast'},
    {name: '较快', value: fast, color: '#3b82f6', class: 'fast'},
    {name: '一般', value: normal, color: '#f59e0b', class: 'normal'},
    {name: '较慢', value: slow, color: '#8b5cf6', class: 'slow'},
    {name: '非常慢', value: verySlow, color: '#ef4444', class: 'very-slow'}
  ]

  if (chartInstance.value) {
    chartInstance.value.updateData({
      series: chartConfig.series
    })
  }
}

// 初始化图表
const initChart = () => {
  const query = uni.createSelectorQuery()
  query.select('#durationDistributionChart')
      .fields({node: true, size: true})
      .exec((res) => {
        if (res[0] && res[0].node) {
          try {
            const canvas = res[0].node
            const ctx = canvas.getContext('2d')

            if (!ctx) {
              console.error('无法获取Canvas上下文')
              return
            }

            // 设置图表配置
            const config = {
              ...chartConfig,
              context: ctx,
              width: res[0].width || 300,
              height: res[0].height || 250
            }
            console.log(config)
            // 创建图表实例
            chartInstance.value = new uCharts(config)
            // 更新数据
            updateChartData()
          } catch (error) {
            console.error('初始化图表失败:', error)
          }
        } else {
          console.error('Canvas元素未找到')
        }
      })
}

// 触摸事件处理
const touchStart = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.touchLegend(e)
    chartInstance.value.showToolTip(e)
  }
}

const touchMove = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.scroll(e)
  }
}

const touchEnd = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.touchLegend(e)
  }
}

// 监听数据变化
watch(() => props.data, updateChartData, {deep: true})

onMounted(() => {
  setTimeout(() => {
    initChart()
  }, 300)
})
</script>

<style lang="scss" scoped>
.chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.chart-content {
  display: flex;
  justify-content: center;
}

.chart-canvas {
  width: 300px;
  height: 250px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border-radius: 6px;
  background: #f8faff;
  min-width: 80px;

  &.very-fast {
    background: rgba(16, 185, 129, 0.1);
  }

  &.fast {
    background: rgba(59, 130, 246, 0.1);
  }

  &.normal {
    background: rgba(245, 158, 11, 0.1);
  }

  &.slow {
    background: rgba(139, 92, 246, 0.1);
  }

  &.very-slow {
    background: rgba(239, 68, 68, 0.1);
  }
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  flex-shrink: 0;
}

.legend-label {
  font-size: 11px;
  color: #4a5568;
  white-space: nowrap;
}

.legend-value {
  font-size: 11px;
  font-weight: 600;
  color: #1a1d2e;
}
</style>
