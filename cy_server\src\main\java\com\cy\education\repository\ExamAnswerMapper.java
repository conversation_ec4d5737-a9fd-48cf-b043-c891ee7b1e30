package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.ExamAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 答题记录Mapper接口
 */
@Mapper
public interface ExamAnswerMapper extends BaseMapper<ExamAnswer> {

    /**
     * 根据考试记录ID查询答题记录列表
     *
     * @param recordId 考试记录ID
     * @return 答题记录列表
     */
    List<ExamAnswer> selectByRecordId(@Param("recordId") Integer recordId);
    
    /**
     * 批量插入答题记录
     *
     * @param answers 答题记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("answers") List<ExamAnswer> answers);
} 