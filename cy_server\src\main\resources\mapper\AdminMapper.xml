<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.AdminMapper">

    <!-- 分页查询管理员列表，并关联部门信息 -->
    <select id="selectAdminPage" resultType="com.cy.education.model.entity.Admin">
        SELECT a.*, d.name AS department_name
        FROM admins a
        LEFT JOIN departments d ON a.department_id = d.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (a.username LIKE CONCAT('%', #{keyword}, '%')
                     OR a.name LIKE CONCAT('%', #{keyword}, '%')
                     OR a.phone LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="departmentId != null">
                AND a.department_id = #{departmentId}
            </if>
            <if test="status != null">
                AND a.status = #{status}
            </if>
        </where>
        ORDER BY a.created_at DESC
    </select>

    <!-- 根据ID查询管理员详情，并关联部门信息 -->
    <select id="selectAdminById" resultType="com.cy.education.model.entity.Admin">
        SELECT a.*, d.name AS department_name
        FROM admins a
        LEFT JOIN departments d ON a.department_id = d.id
        WHERE a.id = #{id}
    </select>

</mapper> 