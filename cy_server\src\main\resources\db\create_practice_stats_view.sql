-- 创建练习统计视图
DROP VIEW IF EXISTS practice_stats_view;

-- 首先创建一个子查询视图，获取每个用户每道题的最新答题结果
CREATE VIEW latest_practice_answers AS
SELECT
    pr.user_id,
    pa.question_id,
    eq.bank_id,
    pa.is_correct,
    pa.answer_time,
    ROW_NUMBER() OVER (PARTITION BY pr.user_id, pa.question_id ORDER BY pa.answer_time DESC) as rn
FROM practice_answer pa
JOIN practice_record pr ON pa.record_id = pr.id
JOIN exam_question eq ON pa.question_id = eq.id
WHERE eq.deleted = 0;

-- 基于最新答题结果创建统计视图
CREATE VIEW practice_stats_view AS
SELECT
    lpa.user_id,
    lpa.bank_id,
    eb.name as bank_name,
    (SELECT COUNT(*) FROM exam_question eq WHERE eq.bank_id = lpa.bank_id AND eq.deleted = 0) as total_questions,
    COUNT(DISTINCT lpa.question_id) as answered_questions,
    SUM(CASE WHEN lpa.is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
    SUM(CASE WHEN lpa.is_correct = 0 THEN 1 ELSE 0 END) as wrong_count,
    CASE
        WHEN COUNT(DISTINCT lpa.question_id) > 0
        THEN ROUND(SUM(CASE WHEN lpa.is_correct = 1 THEN 1 ELSE 0 END) / COUNT(DISTINCT lpa.question_id), 4)
        ELSE 0
    END as accuracy_rate,
    MAX(lpa.answer_time) as last_practice_time
FROM latest_practice_answers lpa
JOIN exam_bank eb ON lpa.bank_id = eb.id
WHERE lpa.rn = 1 AND eb.is_practice_enabled = 1 AND eb.deleted = 0
GROUP BY lpa.user_id, lpa.bank_id, eb.name;

-- 为没有练习记录的用户和题库组合创建基础统计
DROP VIEW IF EXISTS practice_stats_complete_view;

CREATE VIEW practice_stats_complete_view AS
SELECT
    COALESCE(psv.user_id, s.id) as user_id,
    COALESCE(psv.bank_id, eb.id) as bank_id,
    COALESCE(psv.bank_name, eb.name) as bank_name,
    COALESCE(psv.total_questions, (
        SELECT COUNT(*) FROM exam_question eq2
        WHERE eq2.bank_id = eb.id AND eq2.deleted = 0
    )) as total_questions,
    COALESCE(psv.answered_questions, 0) as answered_questions,
    COALESCE(psv.correct_count, 0) as correct_count,
    COALESCE(psv.wrong_count, 0) as wrong_count,
    COALESCE(psv.accuracy_rate, 0) as accuracy_rate,
    psv.last_practice_time
FROM exam_bank eb
CROSS JOIN students s
LEFT JOIN practice_stats_view psv ON eb.id = psv.bank_id AND s.id = psv.user_id
WHERE eb.is_practice_enabled = 1 AND eb.deleted = 0 AND s.status = 1;
