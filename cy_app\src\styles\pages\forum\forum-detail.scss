// 内容区域
.content-area {
  flex: 1;
  padding-bottom: 80px; // 为底部评论栏留出空间
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-text {
  font-size: 14px;
  color: #8e8e93;
}

.post-card {
  background: #fff;
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

// 作者信息
.author-section {
  padding: 10px;
  border-bottom: 1px solid #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.author-avatar {
  flex-shrink: 0;
}

.author-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.author-title {
  font-size: 12px;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 2px 8px;
  border-radius: 8px;
  align-self: flex-start;
}

.post-time {
  font-size: 12px;
  color: #8e8e93;
}

.follow-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 16px;
  border: 1px solid #e0e6ff;
  background: #f8faff;
  transition: all 0.3s ease;
}

.follow-btn:active {
  background: #f0f4ff;
}

.follow-btn.following {
  background: rgba(67, 233, 123, 0.1);
  border-color: #43e97b;
}

.follow-text {
  font-size: 12px;
  color: #667eea;
  font-weight: 500;
}

.follow-btn.following .follow-text {
  color: #43e97b;
}

// 帖子主体内容
.post-main {
  padding: 10px;
}

.post-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1d2e;
  line-height: 1.4;
  margin-bottom: 12px;
}

.post-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.category-tag {
  padding: 4px 12px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.1);
}

.category-tag.safety {
  background: rgba(245, 108, 108, 0.1);
}

.category-tag.study {
  background: rgba(67, 233, 123, 0.1);
}

.normal-tag {
  padding: 4px 12px;
  border-radius: 12px;
  background: #f0f2f5;
}

.tag-text {
  font-size: 12px;
  font-weight: 500;
}

.category-tag.safety .tag-text {
  color: #f56c6c;
}

.category-tag.study .tag-text {
  color: #43e97b;
}

.category-tag .tag-text {
  color: #667eea;
}

.normal-tag .tag-text {
  color: #8e8e93;
}

.post-content {
  font-size: 16px;
  color: #1a1d2e;
  line-height: 1.6;
  margin-bottom: 16px;
  white-space: pre-wrap;
}

// 帖子图片展示
.post-images {
  margin-bottom: 16px;
  display: grid;
  gap: 8px;
}

// 单张图片
.post-images.single {
  grid-template-columns: 1fr;
  grid-template-rows: 300px;
}

// 两张图片
.post-images.double {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: 200px;
}

// 三张图片
.post-images.triple {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: 150px;
}

// 多张图片网格
.post-images.grid {
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(auto-fit, 120px);
}

.image-item {
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.image-item:active {
  transform: scale(0.98);
}

.post-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 图片网格
.images-grid {
  display: grid;
  gap: 8px;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.images-grid.single {
  grid-template-columns: 1fr;
}

.images-grid.double {
  grid-template-columns: repeat(2, 1fr);
}

.images-grid.triple {
  grid-template-columns: 2fr 1fr;
  grid-template-rows: repeat(2, 1fr);
}

.images-grid.grid {
  grid-template-columns: repeat(3, 1fr);
}

.image-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.image-item:active {
  transform: scale(0.98);
}

.image-item.single {
  height: 200px;
}

.image-item.double {
  height: 150px;
}

.image-item.triple-main {
  grid-row: span 2;
  height: 200px;
}

.image-item.triple-sub {
  height: 96px;
}

.image-item.grid {
  height: 100px;
}

.post-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 互动统计
.post-stats {
  display: flex;
  justify-content: space-around;
  padding: 16px 20px;
  border-top: 1px solid #f0f2f5;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.stat-text {
  font-size: 14px;
  color: #8e8e93;
  font-weight: 500;
}

// 操作按钮
.post-actions {
  display: flex;
  justify-content: space-around;
  padding: 16px 20px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  min-width: 60px;
}

.action-btn:active {
  background: #f0f2f5;
  transform: scale(0.95);
}

.action-btn.active {
  background: rgba(250, 112, 154, 0.1);
}

.action-text {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
}

.action-btn.active .action-text {
  color: #fa709a;
}

// 评论区域
.comments-section {
  background: #fff;
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.comments-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.comments-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.sort-options {
  display: flex;
  gap: 16px;
}

.sort-item {
  padding: 6px 12px;
  border-radius: 12px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  transition: all 0.3s ease;
}

.sort-item:active {
  background: #f0f4ff;
}

.sort-item.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
}

.sort-text {
  font-size: 14px;
  color: #8e8e93;
  font-weight: 500;
}

.sort-item.active .sort-text {
  color: #667eea;
}

.comment-list {
  padding: 0;
}

.comment-item {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  flex-shrink: 0;
}

.avatar-image {
  // 空样式
}

.comment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-author {
  font-size: 14px;
  font-weight: 600;
  color: #1a1d2e;
}

.comment-time {
  font-size: 12px;
  color: #8e8e93;
}

.comment-text {
  font-size: 14px;
  color: #1a1d2e;
  line-height: 1.5;
}

.reply-list {
  background: #f8faff;
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
}

.reply-item {
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 6px;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-author {
  color: #667eea;
  font-weight: 600;
}

.reply-separator {
  color: #8e8e93;
  margin: 0 4px;
}

.reply-content {
  color: #1a1d2e;
}

.comment-actions {
  display: flex;
  gap: 16px;
  margin-top: 4px;
}

.comment-action {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.comment-action:active {
  background: #f0f2f5;
}

.comment-action.active {
  background: rgba(250, 112, 154, 0.1);
}

.action-count,
.action-text {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
}

.comment-action.active .action-count {
  color: #fa709a;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  border-top: 1px solid #f0f2f5;
}

.load-text {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

// 底部评论栏
.bottom-comment-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #f0f2f5;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
  padding: 12px 16px;
  z-index: 999;
}

.comment-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
}

.input-container {
  flex: 1;
  height: 36px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  border-radius: 18px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  transition: all 0.3s ease;
}

.input-container:active {
  background: #f0f4ff;
  border-color: #667eea;
}

.input-placeholder {
  font-size: 14px;
  color: #8e8e93;
}

.quick-actions {
  display: flex;
  gap: 8px;
}

.quick-action {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.quick-action:active {
  background: #f0f4ff;
  transform: scale(0.95);
}

// 评论输入弹窗
.comment-input-popup {
  background: #fff;
  border-radius: 16px 16px 0 0;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.close-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: #f5f6fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #e0e6ff;
}

.reply-target {
  padding: 16px 20px;
  background: #f8faff;
  border-bottom: 1px solid #f0f2f5;
}

.target-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.reply-hint {
  font-size: 12px;
  color: #8e8e93;
}

.target-content {
  font-size: 14px;
  color: #1a1d2e;
  line-height: 1.4;
}

.input-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.comment-textarea {
  flex: 1;
  min-height: 120px;
  border: none;
  outline: none;
  font-size: 16px;
  color: #1a1d2e;
  line-height: 1.5;
  resize: none;
  background: transparent;
}

.comment-textarea::placeholder {
  color: #8e8e93;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
}

.word-count {
  font-size: 12px;
  color: #8e8e93;
}

.send-btn {
  padding: 8px 20px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.send-btn:active {
  transform: scale(0.98);
}

.send-btn.disabled {
  opacity: 0.5;
  background: #f0f2f5;
}

.send-text {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
}

.send-btn.disabled .send-text {
  color: #8e8e93;
}
