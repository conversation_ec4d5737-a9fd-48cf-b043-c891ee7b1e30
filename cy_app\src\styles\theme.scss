/* 现代化主题样式 - 绿意盎然 */
:root {
  /* 主色调 - 清新绿 */
  --primary-color: #10B981; /* Tailwind Green 500 */
  --primary-light: #6EE7B7;
  --primary-dark: #059669;
  
  /* 渐变色 */
  --primary-gradient: linear-gradient(135deg, #6EE7B7 0%, #10B981 100%);
  --secondary-gradient: linear-gradient(135deg, #FBBF24 0%, #F59E0B 100%); /* Amber color */

  /* 辅助色 */
  --success-color: #10B981; 
  --warning-color: #F59E0B; /* Amber 500 */
  --error-color: #EF4444;   /* Red 500 */
  --info-color: #3B82F6;    /* Blue 500 */
  
  /* 中性色 */
  --text-primary: #1F2937;    /* Gray 800 */
  --text-secondary: #4B5563;  /* Gray 600 */
  --text-tertiary: #9CA3AF;   /* Gray 400 */
  --text-disabled: #D1D5DB;   /* Gray 300 */
  
  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;  /* Gray 50 */
  --bg-tertiary: #F3F4F6;   /* Gray 100 */
  --bg-mask: rgba(0, 0, 0, 0.4);
  
  /* 边框颜色 */
  --border-color: #E5E7EB;   /* Gray 200 */
  --border-color-light: #F3F4F6; /* Gray 100 */
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-circle: 50%;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 字体粗细 */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 动画 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  /* 组件尺寸 */
  --navbar-height: 44px;
  --tabbar-height: 50px;
  --input-height: 44px;
  --button-height: 44px;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #7E82FF;
    --primary-light: #9EA1FF;
    --primary-dark: #5B5FEF;
    
    --text-primary: #FFFFFF;
    --text-secondary: #CCCCCC;
    --text-tertiary: #999999;
    --text-disabled: #666666;
    
    --bg-primary: #1C1C1E;
    --bg-secondary: #2C2C2E;
    --bg-tertiary: #3A3A3C;
    
    --border-color: #3A3A3C;
    --border-color-light: #2C2C2E;
  }
} 