package com.cy.education.model.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 试卷参数类
 */
@Data
@ApiModel(description = "试卷参数")
public class ExamPaperParams {

    @ApiModelProperty(value = "试卷标题", required = true)
    @NotBlank(message = "试卷标题不能为空")
    private String title;

    @ApiModelProperty(value = "试卷描述")
    private String description;

    @ApiModelProperty(value = "总分", required = true)
    @NotNull(message = "总分不能为空")
    @Min(value = 1, message = "总分必须大于0")
    private Integer totalScore;

    @ApiModelProperty(value = "及格分数", required = true)
    @NotNull(message = "及格分数不能为空")
    @Min(value = 1, message = "及格分数必须大于0")
    private Integer passingScore;

    @ApiModelProperty(value = "考试时长(分钟)", required = true)
    @NotNull(message = "考试时长不能为空")
    @Min(value = 1, message = "考试时长必须大于0")
    private Integer duration;

    @ApiModelProperty(value = "是否发布")
    private Boolean isPublished = false;
} 