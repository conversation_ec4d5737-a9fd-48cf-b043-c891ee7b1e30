import {get, post, put, upload} from '@/utils/request'

/**
 * 用户接口数据类型
 */
export interface UserInfo {
    id: string
    username: string
    name: string
    avatar: string
    email: string
    phone: string
    department: string
    employeeId: string
}

export interface LoginParams {
    username: string
    password: string
    remember?: boolean
}

export interface ResetPasswordParams {
    username: string
    phone: string
    code: string
    newPassword: string
    confirmPassword: string
}

/**
 * 用户登录
 * @param data 登录参数
 */
export function login(data: LoginParams) {
    return post<{ token: string; user: UserInfo }>('/user/login', data)
        .then(response => {
            return response
        })
        .catch(error => {
            throw error
        })
}

/**
 * 用户登出
 */
export function logout() {
    return post<{ success: boolean }>('/user/logout')
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
    return get<UserInfo>('/user/info')
}

/**
 * 获取验证码
 * @param phone 手机号
 */
export function getVerificationCode(phone: string) {
    return get<{ success: boolean }>('/user/code', {phone})
}

/**
 * 重置密码
 * @param data 重置密码参数
 */
export function resetPassword(data: ResetPasswordParams) {
    return post<{ success: boolean }>('/user/reset-password', data)
}

/**
 * 更新用户信息
 * @param data 用户信息
 */
export function updateUserInfo(data: Partial<UserInfo>) {
    return put<{ success: boolean }>('/user/update', data)
}

/**
 * 更改密码
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 */
export function changePassword(oldPassword: string, newPassword: string) {
    return post<{ success: boolean }>('/user/change-password', {oldPassword, newPassword})
}

/**
 * 上传头像（风格统一，调用upload方法）
 */
export function uploadAvatar(filePath: string): Promise<{ avatarUrl: string }> {
    return upload<{ avatarUrl: string }>('/user/upload-avatar', filePath, 'avatar')
}

// =========================== 论坛相关用户功能 ===========================

/**
 * 获取用户资料（用于论坛展示）
 */
export function getUserProfile(userId: number) {
    return get(`/user/${userId}/profile`)
}

/**
 * 获取用户的粉丝列表
 */
export function getUserFollowers(userId: number, params?: { pageNum?: number; pageSize?: number }) {
    return get(`/user/${userId}/followers`, params)
}

/**
 * 获取用户关注的用户列表
 */
export function getUserFollowing(userId: number, params?: { pageNum?: number; pageSize?: number }) {
    return get(`/user/${userId}/following`, params)
}
