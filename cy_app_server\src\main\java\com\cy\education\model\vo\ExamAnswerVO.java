package com.cy.education.model.vo;

import com.cy.education.model.entity.exam.ExamAnswer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考试答题记录VO类
 */
@Data
@ApiModel("考试答题记录信息")
public class ExamAnswerVO extends ExamAnswer {

    /**
     * 题目内容
     */
    @ApiModelProperty("题目内容")
    private String questionTitle;

    /**
     * 题目类型
     */
    @ApiModelProperty("题目类型")
    private String questionType;

    /**
     * 题目选项
     */
    @ApiModelProperty("题目选项")
    private String options;

    /**
     * 正确答案
     */
    @ApiModelProperty("正确答案")
    private String correctAnswer;

    /**
     * 题目解析
     */
    @ApiModelProperty("题目解析")
    private String explanation;

}
