package com.cy.education.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 练习统计视图对象
 */
@Data
public class PracticeStatisticsVO {
    
    /**
     * 学员ID
     */
    private Integer userId;
    
    /**
     * 学员姓名
     */
    private String studentName;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 题库ID
     */
    private Integer bankId;
    
    /**
     * 题库名称
     */
    private String bankName;
    
    /**
     * 题库总题数
     */
    private Integer totalQuestions;
    
    /**
     * 已答题数
     */
    private Integer answeredQuestions;
    
    /**
     * 正确数
     */
    private Integer correctCount;
    
    /**
     * 错误数
     */
    private Integer wrongCount;
    
    /**
     * 正确率
     */
    private BigDecimal accuracyRate;
    
    /**
     * 最后练习时间
     */
    private LocalDateTime lastPracticeTime;
    
    /**
     * 练习次数
     */
    private Integer practiceCount;
    
    /**
     * 总练习时长(分钟)
     */
    private Integer totalPracticeMinutes;
} 