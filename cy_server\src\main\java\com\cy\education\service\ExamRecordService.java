package com.cy.education.service;

import com.cy.education.model.params.ExamRecordQueryParams;
import com.cy.education.model.vo.ExamRecordVO;
import com.cy.education.model.vo.PageResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 考试记录服务接口
 */
public interface ExamRecordService {

    /**
     * 分页查询考试记录列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResponse<ExamRecordVO> listExamRecords(ExamRecordQueryParams params);

    /**
     * 获取考试记录详情
     *
     * @param id 记录ID
     * @return 考试记录详情
     */
    ExamRecordVO getExamRecordDetail(Integer id);

    /**
     * 开始考试
     *
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 考试记录ID
     */
    Integer startExam(Integer examId, Integer userId);

    /**
     * 提交考试
     *
     * @param recordId 记录ID
     * @param answers 答案列表
     * @return 考试成绩
     */
    ExamRecordVO submitExam(Integer recordId, String answers);

    /**
     * 获取考试统计数据
     *
     * @param examId 考试ID，如果为null则统计所有考试
     * @return 统计数据
     */
    Object getExamStatistics(Integer examId);

    /**
     * 导出考试成绩单
     *
     * @param params 导出参数
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportExamRecords(Map<String, Object> params, HttpServletResponse response) throws IOException;
}