package com.cy.education.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 学员导入数据模型
 */
@Data
public class StudentImportData {
    
    @ExcelProperty("姓名")
    private String name;
    
    @ExcelProperty("用户名")
    private String username;
    
    @ExcelProperty("手机号")
    private String phone;
    
    @ExcelProperty("邮箱")
    private String email;
    
    @ExcelProperty("部门名称")
    private String departmentName;
    
    @ExcelProperty("备注")
    private String remark;
    
    // 导入结果相关字段（不在Excel中）
    private boolean success = true;
    private String errorMessage;
    private Integer departmentId; // 解析后的部门ID
}
