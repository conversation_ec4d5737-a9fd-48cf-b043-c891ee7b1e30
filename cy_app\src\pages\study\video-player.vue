<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :class="{ 'navbar-hidden': isFullscreen }">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="navbar-left">
          <view class="back-btn" @click="goBack">
            <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
          </view>
        </view>
        <view class="navbar-right">
          <view class="back-btn" @click="toggleFavorite">
            <up-icon :name="isFavorite ? 'heart-fill' : 'heart'" color="#fff" size="12"></up-icon>
          </view>
          <view class="back-btn" @click="shareLesson">
            <up-icon color="#fff" name="share" size="12"></up-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 视频播放器区域 -->
    <view class="video-container" :class="{ 'fullscreen': isFullscreen }">
      <video
        id="video-player"
        :src="videoSrc"
        class="video-element"
        :controls="false"
        :show-fullscreen-btn="false"
        :show-play-btn="false"
        :show-center-play-btn="false"
        :show-loading="true"
        :poster="videoPoster"
        @play="onPlay"
        @pause="onPause"
        @timeupdate="onTimeUpdate"
        @loadedmetadata="onLoadedMetadata"
        @fullscreenchange="onFullscreenChange"
        @ended="onVideoEnded"
        @click="toggleControlsVisibility"
        enable-progress-gesture
      />

      <!-- 自定义视频控制层 -->
      <view class="video-controls" :class="{ 'controls-hidden': !showControls }" @click.stop>
        <!-- 顶部控制栏 -->
        <view class="top-controls">
          <view class="video-title">{{ lessonInfo.title }}</view>
          <view class="control-actions">
            <view class="speed-selector" @click="showSpeedMenu = !showSpeedMenu">
              <text class="speed-text">{{ playbackRate }}x</text>
              <up-icon name="arrow-down" size="12" color="#fff"></up-icon>
            </view>
          </view>
        </view>

        <!-- 中央播放按钮 -->
        <view class="center-controls">
          <view class="play-btn-large" @click="togglePlay">
            <up-icon :name="isPlaying ? 'pause' : 'play-right'" color="#fff" size="32"></up-icon>
          </view>
        </view>

        <!-- 底部控制栏 -->
        <view class="bottom-controls">
          <view class="progress-section">
            <view class="progress-time-row-opt">
              <text class="time-current">{{ formatTime(currentTime) }}</text>
              <text class="time-total">{{ formatTime(totalTime) }}</text>
            </view>
            <view class="progress-container" @click="seekTo">
              <view class="progress-bar">
                <view class="progress-buffer" :style="{ width: bufferProgress + '%' }"></view>
                <view :style="{ width: progress + '%' }" class="progress-played"></view>
                <view :style="{ left: progress + '%' }" class="progress-thumb"></view>
              </view>
            </view>
          </view>

          <view class="control-buttons">
            <view class="control-btn" @click="togglePlay">
              <up-icon :name="isPlaying ? 'pause' : 'play-right'" color="#fff" size="20"></up-icon>
            </view>
            <view class="control-btn" @click="toggleMute">
              <up-icon :name="isMuted ? 'volume-off' : 'volume'" color="#fff" size="20"></up-icon>
            </view>
            <view class="control-btn" @click="toggleFullscreen">
              <up-icon :name="isFullscreen ? 'contract' : 'share-square'" color="#fff" size="20"></up-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 倍速选择菜单 -->
      <view v-if="showSpeedMenu" class="speed-menu" @click.stop>
        <view class="speed-menu-header">
          <text class="menu-title">播放速度</text>
          <view class="close-btn" @click="showSpeedMenu = false">
            <up-icon color="#8e8e93" name="close" size="10"></up-icon>
          </view>
        </view>
        <view class="speed-options">
          <view
            v-for="speed in speedOptions"
            :key="speed"
            class="speed-option"
            :class="{ active: playbackRate === speed }"
            @click="setPlaybackRate(speed)"
          >
            <text class="speed-label">{{ speed }}x</text>
            <view v-if="playbackRate === speed" class="speed-check">
              <up-icon name="checkmark" size="16" color="#667eea"></up-icon>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 学习内容区域 -->
    <view class="content-area" v-if="!isFullscreen">
      <!-- 课程信息卡片 -->
      <view class="lesson-card">
        <view class="lesson-header">
          <view class="lesson-details">
            <text class="lesson-title">{{ lessonInfo.title }}</text>
            <view class="lesson-meta-row">
              <text v-if="lessonInfo.duration" class="lesson-meta">时长：{{ lessonInfo.duration }}</text>
              <text class="lesson-meta">学习时长：{{ formatDuration(studyDuration) }}</text>
            </view>
          </view>
          <view class="lesson-stats">
            <view class="stat-item">
              <up-icon name="eye" size="14" color="#8e8e93"></up-icon>
              <text class="stat-text">{{ lessonInfo.views }}</text>
            </view>
            <view class="stat-item">
              <up-icon name="thumb-up" size="14" color="#8e8e93"></up-icon>
              <text class="stat-text">{{ lessonInfo.likes }}</text>
            </view>
          </view>
        </view>
        <!-- 优化tags展示，支持多种格式 -->
        <view v-if="lessonInfo.tags" class="tags-container-opt">
          <view
              v-for="tag in parseTags(lessonInfo.tags)"
              :key="tag"
              class="tag-item-opt"
          >
            {{ tag }}
          </view>
        </view>
        <view class="progress-section">
          <view class="progress-header">
            <text class="progress-label">学习进度</text>
            <text class="progress-value">{{ Math.round(progress) }}%</text>
          </view>
          <view class="progress-bar">
            <view :style="{ width: progress + '%' }" class="progress-fill"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

</template>

<script setup lang="ts">
import {ref, reactive, onMounted, onUnmounted} from 'vue'
import {saveStudyRecord, type StudyRecordSaveParams} from '@/api/course'

// 课程信息
const lessonInfo = reactive({
  id: 0,
  title: '',
  chapterName: '',
  duration: '',
  index: 1,
  views: 1234,
  likes: 89,
  courseId: 0,
  resourceId: 0,
  tags: '',
  content: '',
  type: '',
  progress: 0, // 用于存储上次保存的进度
  lastPosition: 0 // 用于存储上次播放位置
})

// 视频相关状态
const videoSrc = ref('')
const videoPoster = ref('https://picsum.photos/750/420?random=video')
const isPlaying = ref(false)
const isFullscreen = ref(false)
const showControls = ref(true)
const controlsTimer = ref<ReturnType<typeof setTimeout> | null>(null)

// 播放进度
const currentTime = ref(0)
const totalTime = ref(0)
const bufferProgress = ref(0)
const progress = ref(0)

// 播放设置
const playbackRate = ref(1)
const showSpeedMenu = ref(false)
const speedOptions = ref([0.5, 0.75, 1, 1.25, 1.5, 2])
const isMuted = ref(false)

// 学习状态
const isFavorite = ref(false)

// 章节数据
const chapterLessons = ref<any[]>([])
const currentLessonId = ref<string | number>('')

// 学习进度保存相关
const studyDuration = ref(0) // 学习时长（秒）
const startTime = ref(Date.now())
let saveTimer: ReturnType<typeof setInterval> | null = null

onMounted(() => {
  // 获取lesson参数
  const pages = getCurrentPages() as any[]
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  if (options.lesson) {
    const lesson = JSON.parse(decodeURIComponent(options.lesson))
    Object.assign(lessonInfo, lesson)
  }

  if (options.courseId) {
    lessonInfo.courseId = parseInt(options.courseId)
  }

  currentLessonId.value = lessonInfo.id || currentLessonId.value

  // 如果lesson.content是视频地址则赋值videoSrc
  if (lessonInfo.type === 'video' && lessonInfo.content) {
    videoSrc.value = lessonInfo.content
  }

  // 记录开始时间
  startTime.value = Date.now()

  // 初始化视频控制栏自动隐藏
  startControlsTimer()

  // 定时保存学习进度（每30秒保存一次）
  saveTimer = setInterval(() => {
    saveStudyProgress()
  }, 30000)

  // 页面加载时立即保存一次
  setTimeout(() => {
    saveStudyProgress()
  }, 1000)
})

onUnmounted(() => {
  // 清理定时器
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
  if (saveTimer) {
    clearInterval(saveTimer)
  }

  // 页面卸载时保存最终进度
  saveStudyProgress()
})

// 保存学习进度
const saveStudyProgress = async () => {
  try {
    console.log(lessonInfo)
    if (!lessonInfo.courseId || !lessonInfo.id || !lessonInfo.resourceId) {
      console.warn('缺少必要的课程信息，无法保存学习进度')
      return
    }

    const params: StudyRecordSaveParams = {
      courseId: lessonInfo.courseId,
      lessonId: lessonInfo.id,
      resourceId: lessonInfo.resourceId,
      resourceType: 'video', // 视频类型
      progress: Math.round(progress.value),
      duration: studyDuration.value,
      completed: progress.value >= 100 ? 1 : 0,
      lastPosition: Math.floor(currentTime.value)
    }

    const result = await saveStudyRecord(params)
    console.log('学习进度保存成功:', result)
  } catch (error) {
    console.error('保存学习进度失败:', error)
  }
}

const goBack = () => {
  // 返回前保存进度
  saveStudyProgress()
  uni.navigateBack()
}

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value
  uni.showToast({
    title: isFavorite.value ? '已收藏' : '已取消收藏',
    icon: 'success'
  })
}

const shareLesson = () => {
  uni.showActionSheet({
    itemList: ['分享给朋友',],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          uni.showToast({ title: '分享给朋友', icon: 'success' })
          break
      }
    }
  })
}

// 视频控制方法
const toggleControlsVisibility = () => {
  showControls.value = !showControls.value
  if (showControls.value) {
    startControlsTimer()
  }
}

const startControlsTimer = () => {
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
  controlsTimer.value = setTimeout(() => {
    showControls.value = false
  }, 3000)
}

const togglePlay = () => {
  const video = uni.createVideoContext('video-player')
  if (isPlaying.value) {
    video.pause()
  } else {
    video.play()
  }
}

const toggleMute = () => {
  isMuted.value = !isMuted.value
  // 这里可以调用视频API设置静音
}

const toggleFullscreen = () => {
  const video = uni.createVideoContext('video-player')
  if (isFullscreen.value) {
    video.exitFullScreen()
  } else {
    video.requestFullScreen()
  }
}

const onPlay = () => {
  isPlaying.value = true
}

const onPause = () => {
  isPlaying.value = false
}

const onTimeUpdate = (e: any) => {
  currentTime.value = e.detail.currentTime
  progress.value = totalTime.value > 0 ? (currentTime.value / totalTime.value) * 100 : 0
  studyDuration.value = Math.floor((Date.now() - startTime.value) / 1000)
}

const onLoadedMetadata = (e: any) => {
  totalTime.value = e.detail.duration

  // 从上次保存的进度开始播放
  if (lessonInfo.progress && lessonInfo.progress > 0) {
    progress.value = lessonInfo.progress

    // 如果有上次播放位置，跳转到该位置
    if (lessonInfo.lastPosition && lessonInfo.lastPosition > 0) {
      const video = uni.createVideoContext('video-player')
      video.seek(lessonInfo.lastPosition)
      currentTime.value = lessonInfo.lastPosition
    }
  }
}

const onFullscreenChange = (e: any) => {
  isFullscreen.value = e.detail.fullScreen
}

const onVideoEnded = () => {
  isPlaying.value = false
  progress.value = 100
  markLessonCompleted()
  saveStudyProgress()
}

const seekTo = (e: any) => {
  // 进度条点击跳转
  const video = uni.createVideoContext('video-player')
  // 这里需要根据点击位置计算时间
  // 具体实现需要获取点击位置和进度条宽度
}

const setPlaybackRate = (rate: number) => {
  playbackRate.value = rate
  showSpeedMenu.value = false
  const video = uni.createVideoContext('video-player')
  video.playbackRate(rate)
}

const formatTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 格式化学习时长
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 其他功能方法
const markLessonCompleted = () => {
  // 标记当前课程为已完成
  const lesson = chapterLessons.value.find((l: any) => l.id === currentLessonId.value)
  if (lesson) {
    lesson.completed = true
    uni.showToast({
      title: '课程学习完成',
      icon: 'success'
    })
  }
}

const parseTags = (tags: any) => {
  if (!tags) return []
  if (Array.isArray(tags)) return tags
  // 尝试解析JSON数组字符串
  try {
    const arr = JSON.parse(tags)
    if (Array.isArray(arr)) return arr
  } catch (e) {
  }
  // 逗号分隔
  if (typeof tags === 'string') return tags.split(',').map((t: string) => t.trim()).filter(Boolean)
  return []
}
</script>

<style lang="scss" scoped>

.lesson-title {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  line-height: 1.2;
}

.lesson-meta {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

// 视频播放器
.video-container {
  position: relative;
  width: 100%;
  height: 250px;
  background: #000;
}

.video-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  height: 100vh;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

// 视频控制层
.video-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.7) 0%,
    transparent 20%,
    transparent 80%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  transition: opacity 0.3s ease;
}

.controls-hidden {
  opacity: 0;
  pointer-events: none;
}

.top-controls {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.video-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  line-height: 1.3;
}

.control-actions {
  display: flex;
  gap: 12px;
}

.speed-selector {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.speed-text {
  font-size: 14px;
  color: #fff;
  font-weight: 500;
}

.center-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.play-btn-large {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.play-btn-large:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.8);
}

.bottom-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-section {
  margin-top: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.time-current,
.time-total {
  font-size: 12px;
  color: #fff;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.progress-container {
  flex: 1;
  height: 20px;
  display: flex;
  align-items: center;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-buffer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
}

.progress-played {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #667eea;
  border-radius: 2px;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  border: 2px solid #fff;
}

.control-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
}

.control-btn {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.8);
}

// 速度选择菜单
.speed-menu {
  position: absolute;
  top: 80px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  z-index: 1000;
}

.speed-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.menu-title {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
}

.speed-options {
  padding: 8px 0;
}

.speed-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  transition: background 0.3s ease;
}

.speed-option:active {
  background: rgba(255, 255, 255, 0.1);
}

.speed-option.active {
  background: rgba(102, 126, 234, 0.2);
}

.speed-label {
  font-size: 14px;
  color: #fff;
}

.speed-check {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 内容区域
.content-area {
  background: linear-gradient(180deg, #f5f6fa 0%, #ffffff 100%);
  min-height: calc(100vh - 250px);
  padding: 20px;
}

.lesson-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.lesson-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.lesson-details {
  flex: 1;
}

.lesson-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
  line-height: 1.3;
  margin-bottom: 4px;
}

.lesson-meta {
  font-size: 14px;
  color: #8e8e93;
}

.lesson-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-text {
  font-size: 12px;
  color: #8e8e93;
}

.progress-section {
  margin-top: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  color: #1a1d2e;
  font-weight: 500;
}

.progress-value {
  font-size: 14px;
  color: #667eea;
  font-weight: 600;
}

.progress-bar {
  height: 6px;
  background: #f0f2f5;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.chapter-item.active {
  background: rgba(255, 255, 255, 0.9);
}

.chapter-item.active {
  color: #fff;
}

.close-btn {
  width: 20px;
  height: 20px;
  border-radius: 16px;
  background: #f5f6fa;
  margin-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #e0e6ff;
}

.tags-container-opt {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0 0 0;
}

.tag-item-opt {
  padding: 4px 14px;
  background: #f8faff;
  border: 1px solid #e0e6ff;
  border-radius: 16px;
  font-size: 12px;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 4px;
}

.lesson-meta-row {
  display: flex;
  gap: 16px;
  margin-top: 4px;
}

.lesson-meta {
  font-size: 13px;
  color: #8e8e93;
}

.progress-time-row-opt {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}
</style>
