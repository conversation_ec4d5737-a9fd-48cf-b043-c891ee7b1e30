package com.cy.education.model.params;

import lombok.Data;

/**
 * 积分商品查询参数
 */
@Data
public class  PointsProductQueryParam {

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize  = 10;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品分类
     */
    private String category;

    /**
     * 状态：active/inactive
     */
    private String status;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 排序方向：asc/desc
     */
    private String sortOrder;
}
