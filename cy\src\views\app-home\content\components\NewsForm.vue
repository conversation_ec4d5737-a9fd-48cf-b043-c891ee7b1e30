<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑新闻' : '新增新闻'"
    width="700px"
    destroy-on-close
    @closed="handleClosed"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入新闻标题" />
      </el-form-item>
      <el-form-item label="封面图" prop="coverUrl">
        <image-upload
          v-model="form.coverUrl"
          :max-size="5"
          :width="800"
          :height="450"
          tip="请上传比例为16:9的封面图片，推荐尺寸为800×450，支持JPG、PNG格式，最大5MB"
        />
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <div style="border: 1px solid #ccc">
          <Toolbar
            style="border-bottom: 1px solid #ccc"
            :editor="editor"
            :defaultConfig="toolbarConfig"
            mode="default"
          />
          <Editor
            style="height: 300px; overflow-y: hidden;"
            v-model="form.content"
            :defaultConfig="editorConfig"
            mode="default"
            @onCreated="handleEditorCreated"
          />
        </div>
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTime">
        <el-date-picker
          v-model="form.publishTime"
          type="datetime"
          placeholder="选择发布时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :shortcuts="dateShortcuts"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">发布</el-radio>
          <el-radio :label="0">草稿</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否置顶">
        <el-switch
          v-model="form.isTop"
          active-text="置顶"
          inactive-text="不置顶"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onBeforeUnmount } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { type IDomEditor, type IEditorConfig, type IToolbarConfig } from '@wangeditor/editor'
import { createEditorConfig, createToolbarConfig } from '@/utils/editorUpload'
import ImageUpload from '@/components/Upload/ImageUpload.vue'

interface NewsForm {
  id?: number
  title: string
  coverUrl: string
  content: string
  publishTime: string
  status: number
  isTop: boolean
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  newsData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const formRef = ref<FormInstance>()
const editor = ref<IDomEditor | null>(null)

// 编辑器配置
const editorConfig: Partial<IEditorConfig> = createEditorConfig('请输入新闻内容...')

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = createToolbarConfig()

// 日期快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: new Date()
  },
  {
    text: '明天',
    value: () => {
      const date = new Date()
      date.setDate(date.getDate() + 1)
      return date
    }
  },
  {
    text: '一周后',
    value: () => {
      const date = new Date()
      date.setDate(date.getDate() + 7)
      return date
    }
  }
]

// 表单数据
const form = reactive<NewsForm>({
  id: undefined,
  title: '',
  coverUrl: '',
  content: '<p></p>', // 初始化为空内容但有p标签
  publishTime: new Date().toISOString().slice(0, 19).replace('T', ' '), // 当前时间
  status: 1,
  isTop: false
})

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入新闻标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入新闻内容', trigger: 'blur' }],
  publishTime: [{ required: true, message: '请选择发布时间', trigger: 'change' }]
}

// 编辑器创建完成
const handleEditorCreated = (editorInstance: IDomEditor) => {
  editor.value = Object.seal(editorInstance) // 一定要用 Object.seal() ，否则会报错
}

// 监听属性变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(() => props.newsData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(form, {
      id: newVal.id,
      title: newVal.title || '',
      coverUrl: newVal.coverUrl || '',
      content: newVal.content || '<p></p>',
      publishTime: newVal.publishTime || new Date().toISOString().slice(0, 19).replace('T', ' '),
      status: newVal.status !== undefined ? newVal.status : 1,
      isTop: newVal.isTop !== undefined ? newVal.isTop : false
    })
  }
}, { deep: true })



// 提交表单
const submitForm = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      emit('submit', { ...form })
      visible.value = false
    }
  })
}

// 关闭弹窗重置表单
const handleClosed = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    id: undefined,
    title: '',
    coverUrl: '',
    content: '<p></p>',
    publishTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    status: 1,
    isTop: false
  })
}

// 在组件销毁前销毁编辑器实例
onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})
</script>

<style scoped>
/* 新闻表单样式 */
</style> 