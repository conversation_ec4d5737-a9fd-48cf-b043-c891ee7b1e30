package com.cy.education.model.entity.exam;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试部门关联实体类
 */
@Data
@TableName("exam_exam_department")
public class ExamExamDepartment {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 考试ID
     */
    private Integer examId;

    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
