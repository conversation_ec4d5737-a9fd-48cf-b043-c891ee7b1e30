<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.ExamRecordMapper">
    <!-- 分页查询考试记录列表 -->
    <select id="selectExamRecordPage" resultType="com.cy.education.model.vo.ExamRecordVO">
        SELECT er.*, e.title as examTitle
        FROM exam_record er
        LEFT JOIN exam_exam e ON er.exam_id = e.id
        <where>
            <if test="examId != null">
                AND er.exam_id = #{examId}
            </if>
            <if test="departmentId != null">
                AND er.department_id = #{departmentId}
            </if>
            <if test="status != null">
                AND er.status = #{status}
            </if>
            <if test="isPassed != null">
                AND er.is_passed = #{isPassed}
            </if>
            <if test="userId != null">
                AND er.user_id = #{userId}
            </if>
        </where>
        <choose>
            <when test="sortBy != null and sortBy != '' and sortOrder != null and sortOrder != ''">
                ORDER BY er.${sortBy} ${sortOrder}
            </when>
            <otherwise>
                ORDER BY er.created_at DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据考试ID和用户ID查询考试记录 -->
    <select id="selectByExamIdAndUserId" resultType="com.cy.education.model.vo.ExamRecordVO">
        SELECT er.*,
        e.title as examTitle,
        s.name as userName,
        s.department_id as departmentId,
        d.name as departmentName
        FROM exam_record er
        LEFT JOIN exam_exam e ON er.exam_id = e.id
        LEFT JOIN students s ON er.user_id = s.id
        LEFT JOIN departments d ON s.department_id = d.id
        WHERE er.exam_id = #{examId}
        AND er.user_id = #{userId}
        ORDER BY er.id DESC
        LIMIT 1
    </select>

    <!-- 根据考试记录ID查询考试记录 -->
    <select id="selectByRecordId" resultType="com.cy.education.model.vo.ExamRecordVO">
        SELECT er.*,
        e.title as examTitle,
        s.name as userName,
        s.department_id as departmentId,
        d.name as departmentName
        FROM exam_record er
        LEFT JOIN exam_exam e ON er.exam_id = e.id
        LEFT JOIN students s ON er.user_id = s.id
        LEFT JOIN departments d ON s.department_id = d.id
        WHERE er.id = #{recordId}
    </select>
</mapper>
