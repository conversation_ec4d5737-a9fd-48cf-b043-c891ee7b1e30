// 考试记录页面样式

.exam-records {
  padding: 30rpx 30rpx 40rpx;

  .loading-box, .empty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;

    text {
      margin-top: 20rpx;
      color: #8c8c8c;
      font-size: 28rpx;
    }
  }

  .records-list {
    .record-card {
      background: #fff;
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20rpx;

        .exam-title {
          flex: 1;
          font-size: 32rpx;
          font-weight: 600;
          color: #262626;
          line-height: 1.4;
        }

        .status-badge {
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          font-weight: 500;
          margin-left: 20rpx;

          &.status-draft {
            background: #f0f0f0;
            color: #8c8c8c;
          }

          &.status-ongoing {
            background: #e6f7ff;
            color: #1890ff;
          }

          &.status-completed {
            background: #f6ffed;
            color: #52c41a;
          }

          &.status-timeout {
            background: #fff2e8;
            color: #fa8c16;
          }
        }
      }

      .score-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;
        padding: 20rpx;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 16rpx;

        .score-display {
          display: flex;
          align-items: baseline;

          .score-number {
            font-size: 48rpx;
            font-weight: 700;
            line-height: 1;

            &.score-excellent {
              color: #52c41a;
            }

            &.score-good {
              color: #1890ff;
            }

            &.score-moderate {
              color: #fa8c16;
            }

            &.score-pass {
              color: #722ed1;
            }

            &.score-fail {
              color: #ff4d4f;
            }
          }

          .score-divider {
            font-size: 32rpx;
            color: #8c8c8c;
            margin: 0 8rpx;
          }

          .total-score {
            font-size: 28rpx;
            color: #8c8c8c;
          }
        }

        .pass-status {
          display: flex;
          align-items: center;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          font-weight: 500;

          &.passed {
            background: #f6ffed;
            color: #52c41a;
          }

          &.failed {
            background: #fff2f0;
            color: #ff4d4f;
          }

          text {
            margin-left: 8rpx;
          }
        }
      }

      .exam-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;
        margin-bottom: 20rpx;

        .info-item {
          display: flex;
          align-items: center;

          .info-text {
            margin-left: 8rpx;
            font-size: 26rpx;
            color: #595959;
          }
        }
      }

      .detail-hint {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 20rpx;
        border-top: 1rpx solid #f0f0f0;

        text {
          font-size: 24rpx;
          color: #bfbfbf;
          margin-right: 8rpx;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .exam-records {
    padding: 100rpx 20rpx 30rpx;
  }

  .record-card {
    padding: 24rpx !important;

    .exam-title {
      font-size: 28rpx !important;
    }

    .score-number {
      font-size: 40rpx !important;
    }
  }
}



