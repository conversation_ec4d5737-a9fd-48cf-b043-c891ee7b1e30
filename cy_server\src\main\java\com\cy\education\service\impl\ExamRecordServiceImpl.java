package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BadRequestException;
import com.cy.education.exception.NotFoundException;
import com.cy.education.model.entity.ExamAnswer;
import com.cy.education.model.entity.ExamExam;
import com.cy.education.model.entity.ExamQuestion;
import com.cy.education.model.entity.ExamRecord;
import com.cy.education.model.entity.Student;
import com.cy.education.model.entity.Department;
import com.cy.education.model.params.ExamRecordQueryParams;
import com.cy.education.model.vo.ExamAnswerVO;
import com.cy.education.model.vo.ExamRecordVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.ExamAnswerMapper;
import com.cy.education.repository.ExamExamMapper;
import com.cy.education.repository.ExamQuestionMapper;
import com.cy.education.repository.ExamRecordMapper;
import com.cy.education.repository.StudentMapper;
import com.cy.education.service.DepartmentService;
import com.cy.education.service.ExamRecordService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考试记录服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExamRecordServiceImpl implements ExamRecordService {

    private final ExamRecordMapper examRecordMapper;
    private final ExamAnswerMapper examAnswerMapper;
    private final ExamExamMapper examExamMapper;
    private final ExamQuestionMapper examQuestionMapper;
    private final StudentMapper studentMapper;
    private final DepartmentService departmentService;
    private final ObjectMapper objectMapper;

    @Override
    public PageResponse<ExamRecordVO> listExamRecords(ExamRecordQueryParams params) {
        // 构建分页对象
        Page<ExamRecord> page = new Page<>(params.getPage(), params.getSize());
        
        // 分页查询考试记录列表
        IPage<ExamRecord> recordPage = examRecordMapper.selectExamRecordPage(
            page, 
            params.getExamId(),
            params.getDepartmentId(),
            params.getKeyword(),
            params.getStatus(),
            params.getIsPassed(),
            params.getSortBy(),
            params.getSortOrder()
        );
        
        // 转换为VO对象
        List<ExamRecordVO> records = recordPage.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        return new PageResponse<ExamRecordVO>(records, recordPage.getTotal());
    }

    @Override
    public ExamRecordVO getExamRecordDetail(Integer id) {
        // 查询考试记录基本信息
        ExamRecord record = examRecordMapper.selectById(id);
        if (record == null) {
            throw new NotFoundException("考试记录不存在");
        }
        
        // 转换为VO对象
        ExamRecordVO vo = convertToVO(record);
        
        // 查询答题记录
        List<ExamAnswer> answers = examAnswerMapper.selectByRecordId(id);
        
        // 查询考试相关信息
        ExamExam exam = examExamMapper.selectById(record.getExamId());
        if (exam != null) {
            vo.setExamTitle(exam.getTitle());
            // 设置及格分数
            vo.setPassingScore(exam.getPassingScore());
        }
        
        // 查询所有题目详情并关联答题记录
        List<ExamAnswerVO> answerVOs = new ArrayList<>();
        for (ExamAnswer answer : answers) {
            ExamAnswerVO answerVO = convertAnswerToVO(answer);
            
            // 查询题目详情
            ExamQuestion question = examQuestionMapper.selectById(answer.getQuestionId());
            if (question != null) {
                // 设置题目内容
                answerVO.setQuestionContent(question.getTitle());
                // 设置题目类型
                answerVO.setQuestionType(question.getType());
                // 设置题目选项（如果有）
                answerVO.setOptions(question.getOptions());
                // 设置正确答案
                answerVO.setCorrectAnswer(question.getCorrectAnswer());
                // 设置题目解析
                answerVO.setExplanation(question.getExplanation());
                // 设置题目总分
                answerVO.setTotalScore(answer.getScore() != null ? answer.getScore() : 0);
            }
            
            answerVOs.add(answerVO);
        }
        
        vo.setAnswers(answerVOs);
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer startExam(Integer examId, Integer userId) {
        // 查询考试是否存在
        ExamExam exam = examExamMapper.selectById(examId);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        
        // 检查考试是否已发布且正在进行中
        if (!exam.getIsPublished()) {
            throw new BadRequestException("考试未发布，不能参加");
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(exam.getStartTime())) {
            throw new BadRequestException("考试未开始，不能参加");
        }
        
        if (now.isAfter(exam.getEndTime())) {
            throw new BadRequestException("考试已结束，不能参加");
        }
        
        // 检查用户是否存在
        Student student = studentMapper.selectById(userId);
        if (student == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 检查用户是否已经参加过考试
        LambdaQueryWrapper<ExamRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamRecord::getExamId, examId)
                  .eq(ExamRecord::getUserId, userId)
                  .orderByDesc(ExamRecord::getAttemptNumber);
        ExamRecord existingRecord = examRecordMapper.selectOne(queryWrapper);
        
        int attemptNumber = 1;
        if (existingRecord != null) {
            // 检查考试次数是否超过限制
            if (existingRecord.getAttemptNumber() >= exam.getMaxAttempts()) {
                throw new BadRequestException("已达到最大考试次数限制");
            }
            
            // 检查是否有未完成的考试
            if (existingRecord.getStatus() == 1) { // 进行中
                return existingRecord.getId(); // 返回现有的记录ID
            }
            
            attemptNumber = existingRecord.getAttemptNumber() + 1;
        }
        
        // 创建考试记录
        ExamRecord record = new ExamRecord();
        record.setExamId(examId);
        record.setUserId(userId);
        record.setDepartmentId(student.getDepartmentId());
        record.setTotalScore(100); // 默认总分为100
        record.setStartTime(now);
        record.setStatus(1); // 进行中
        record.setAttemptNumber(attemptNumber);
        record.setCreatedAt(now);
        record.setUpdatedAt(now);
        
        examRecordMapper.insert(record);
        
        return record.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamRecordVO submitExam(Integer recordId, String answersJson) {
        // 查询考试记录是否存在
        ExamRecord record = examRecordMapper.selectById(recordId);
        if (record == null) {
            throw new NotFoundException("考试记录不存在");
        }
        
        // 检查考试状态是否为进行中
        if (record.getStatus() != 1) {
            throw new BadRequestException("考试未在进行中，不能提交");
        }
        
        // 查询考试信息
        ExamExam exam = examExamMapper.selectById(record.getExamId());
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        
        // 更新考试记录
        LocalDateTime now = LocalDateTime.now();
        record.setEndTime(now);
        record.setDuration((int) Duration.between(record.getStartTime(), now).toMinutes());
        record.setStatus(2); // 已完成
        
        // 处理答案（简化版，实际情况需要根据题目类型和正确答案进行评分）
        // 这里只是示例，实际项目中需要更复杂的评分逻辑
        try {
            // 解析答案JSON
            List<Map<String, Object>> answers = objectMapper.readValue(answersJson, List.class);
            
            // 计算得分（简化版）
            int totalScore = 0;
            List<ExamAnswer> answerEntities = new ArrayList<>();
            
            for (Map<String, Object> answer : answers) {
                ExamAnswer answerEntity = new ExamAnswer();
                answerEntity.setRecordId(recordId);
                answerEntity.setQuestionId(Integer.valueOf(answer.get("questionId").toString()));
                answerEntity.setAnswer(answer.get("answer").toString());
                
                // 简化的评分逻辑，实际应该根据题目类型和正确答案判断
                boolean isCorrect = Math.random() > 0.3; // 随机判断正确与否
                int score = isCorrect ? 10 : 0; // 随机分数
                
                answerEntity.setIsCorrect(isCorrect);
                answerEntity.setScore(score);
                answerEntity.setCreatedAt(now);
                answerEntity.setUpdatedAt(now);
                
                totalScore += score;
                answerEntities.add(answerEntity);
            }
            
            // 批量插入答题记录
            if (!answerEntities.isEmpty()) {
                examAnswerMapper.batchInsert(answerEntities);
            }
            
            // 更新考试记录的分数和是否通过
            record.setScore(totalScore);
            record.setIsPassed(totalScore >= exam.getPassingScore());
            
        } catch (JsonProcessingException e) {
            log.error("解析答案JSON失败", e);
            throw new BadRequestException("答案格式不正确");
        }
        
        record.setUpdatedAt(now);
        examRecordMapper.updateById(record);
        
        return convertToVO(record);
    }

    @Override
    public Object getExamStatistics(Integer examId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 1. 考试总数
        long totalExams = examExamMapper.selectCount(new LambdaQueryWrapper<ExamExam>()
                .eq(ExamExam::getIsPublished, true));
        statistics.put("totalExams", totalExams);
        
        // 2. 构建查询条件
        LambdaQueryWrapper<ExamRecord> recordQueryWrapper = new LambdaQueryWrapper<>();
        if (examId != null) {
            recordQueryWrapper.eq(ExamRecord::getExamId, examId);
        }
        
        // 3. 参与人数统计
        long totalParticipants = examRecordMapper.selectCount(recordQueryWrapper);
        statistics.put("totalParticipants", totalParticipants);
        
        // 4. 已完成考试的记录
            LambdaQueryWrapper<ExamRecord> completedQueryWrapper = new LambdaQueryWrapper<>();
            completedQueryWrapper.eq(ExamRecord::getStatus, 2); // 已完成
            if (examId != null) {
                completedQueryWrapper.eq(ExamRecord::getExamId, examId);
            }
            List<ExamRecord> completedRecords = examRecordMapper.selectList(completedQueryWrapper);
            int completedCount = completedRecords.size();
        
        // 5. 计算平均分和通过率
        double averageScore = 0;
        double passRate = 0;
            
            if (completedCount > 0) {
                // 计算平均分
                int totalScore = completedRecords.stream()
                    .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                    .sum();
                averageScore = (double) totalScore / completedCount;
                
                // 计算通过率
                long passedCount = completedRecords.stream()
                    .filter(r -> r.getIsPassed() != null && r.getIsPassed())
                    .count();
                passRate = (double) passedCount / completedCount;
        }
        
        statistics.put("averageScore", Math.round(averageScore * 10) / 10.0);
        statistics.put("passRate", Math.round(passRate * 1000) / 10.0); // 转为百分比

        // 计算最高分
        int highestScore = completedRecords.stream()
            .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
            .max()
            .orElse(0);
        statistics.put("highestScore", highestScore);
        
        // 6. 部门统计
        List<Map<String, Object>> departmentStats = new ArrayList<>();
        
        // 获取所有部门
        List<Department> departments = new ArrayList<>();
        List<Department> topDepartments = departmentService.listDepartments(null, true);
        // 展平部门树
        flattenDepartmentTree(topDepartments, departments);
        
        // 创建部门ID到部门名称的映射
        Map<Integer, String> departmentMap = departments.stream()
            .collect(Collectors.toMap(Department::getId, Department::getName, (a, b) -> a));
        
        // 按部门ID分组统计
        Map<Integer, List<ExamRecord>> recordsByDepartment = completedRecords.stream()
            .filter(r -> r.getDepartmentId() != null)
            .collect(Collectors.groupingBy(ExamRecord::getDepartmentId));
        
        // 计算每个部门的统计数据
        for (Map.Entry<Integer, List<ExamRecord>> entry : recordsByDepartment.entrySet()) {
            Integer deptId = entry.getKey();
            List<ExamRecord> deptRecords = entry.getValue();
            
            if (deptRecords.isEmpty()) {
                continue;
            }
            
            // 部门名称
            String departmentName = departmentMap.getOrDefault(deptId, "未知部门");
            
            // 参与人数
            int participantCount = deptRecords.size();
            
            // 平均分
            double deptAvgScore = deptRecords.stream()
                .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                .average()
                .orElse(0);
            
            // 通过率
            long deptPassedCount = deptRecords.stream()
                .filter(r -> r.getIsPassed() != null && r.getIsPassed())
                .count();
            double deptPassRate = (double) deptPassedCount / participantCount;
            
            // 最高分
            int deptHighestScore = deptRecords.stream()
                .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                .max()
                .orElse(0);

            // 最低分
            int deptLowestScore = deptRecords.stream()
                .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                .min()
                .orElse(0);
            
            // 构建部门统计数据
            Map<String, Object> deptStat = new HashMap<>();
            deptStat.put("departmentId", deptId);
            deptStat.put("departmentName", departmentName);
            deptStat.put("participantCount", participantCount);
            deptStat.put("averageScore", Math.round(deptAvgScore * 10) / 10.0);
            deptStat.put("passRate", Math.round(deptPassRate * 1000) / 10.0);
            deptStat.put("highestScore", deptHighestScore);
            deptStat.put("lowestScore", deptLowestScore);
            
            departmentStats.add(deptStat);
        }
        
        // 7. 分数分布统计
        Map<String, Integer> scoreDistribution = new HashMap<>();
        scoreDistribution.put("excellent", 0); // 优秀(90-100)
        scoreDistribution.put("good", 0);      // 良好(80-89)
        scoreDistribution.put("moderate", 0);  // 中等(70-79)
        scoreDistribution.put("pass", 0);      // 及格(60-69)
        scoreDistribution.put("fail", 0);      // 不及格(0-59)
        
        for (ExamRecord record : completedRecords) {
            int score = record.getScore() != null ? record.getScore() : 0;
            if (score >= 90) {
                scoreDistribution.put("excellent", scoreDistribution.get("excellent") + 1);
            } else if (score >= 80) {
                scoreDistribution.put("good", scoreDistribution.get("good") + 1);
            } else if (score >= 70) {
                scoreDistribution.put("moderate", scoreDistribution.get("moderate") + 1);
            } else if (score >= 60) {
                scoreDistribution.put("pass", scoreDistribution.get("pass") + 1);
            } else {
                scoreDistribution.put("fail", scoreDistribution.get("fail") + 1);
            }
        }
        
        statistics.put("scoreDistribution", scoreDistribution);
        
        // 8. 完成时间统计
        Map<String, Integer> durationDistribution = new HashMap<>();
        durationDistribution.put("veryFast", 0);    // 非常快(小于总时长的25%)
        durationDistribution.put("fast", 0);        // 较快(总时长的25%-50%)
        durationDistribution.put("normal", 0);      // 一般(总时长的50%-75%)
        durationDistribution.put("slow", 0);        // 较慢(总时长的75%-100%)
        durationDistribution.put("verySlow", 0);    // 非常慢(超过总时长)
        
        // 获取考试时长
        Integer examDuration = 60; // 默认60分钟
        if (examId != null) {
            ExamExam exam = examExamMapper.selectById(examId);
            if (exam != null) {
                examDuration = exam.getDuration();
            }
        }
        
        for (ExamRecord record : completedRecords) {
            int duration = record.getDuration() != null ? record.getDuration() : 0;
            double durationRatio = (double) duration / examDuration;
            
            if (durationRatio < 0.25) {
                durationDistribution.put("veryFast", durationDistribution.get("veryFast") + 1);
            } else if (durationRatio < 0.5) {
                durationDistribution.put("fast", durationDistribution.get("fast") + 1);
            } else if (durationRatio < 0.75) {
                durationDistribution.put("normal", durationDistribution.get("normal") + 1);
            } else if (durationRatio <= 1.0) {
                durationDistribution.put("slow", durationDistribution.get("slow") + 1);
            } else {
                durationDistribution.put("verySlow", durationDistribution.get("verySlow") + 1);
            }
        }
        
        statistics.put("durationDistribution", durationDistribution);
        statistics.put("departmentStats", departmentStats);
        
        return statistics;
    }
    
    /**
     * 将实体对象转换为VO对象
     */
    private ExamRecordVO convertToVO(ExamRecord record) {
        if (record == null) {
            return null;
        }
        
        ExamRecordVO vo = new ExamRecordVO();
        BeanUtils.copyProperties(record, vo);
        
        // 设置考试标题
        ExamExam exam = examExamMapper.selectById(record.getExamId());
        if (exam != null) {
            vo.setExamTitle(exam.getTitle());
        }
        
        // 设置用户名称
        Student student = studentMapper.selectById(record.getUserId());
        if (student != null) {
            vo.setUserName(student.getName());
        }
        
        // 设置部门名称
        if (record.getDepartmentId() != null) {
            try {
                // 获取真实的部门名称
                Department department = departmentService.getDepartmentById(record.getDepartmentId());
                vo.setDepartmentName(department.getName());
            } catch (Exception e) {
                // 如果部门不存在，设置默认值
                log.warn("获取部门名称失败，部门ID: {}", record.getDepartmentId(), e);
                vo.setDepartmentName("未知部门");
            }
        }
        
        // 转换状态
        switch (record.getStatus()) {
            case 0:
                vo.setStatus("not_started");
                break;
            case 1:
                vo.setStatus("in_progress");
                break;
            case 2:
                vo.setStatus("completed");
                break;
            case 3:
                vo.setStatus("timeout");
                break;
            default:
                vo.setStatus("unknown");
        }
        
        // 是否通过
        vo.setPassed(record.getIsPassed());
        
        return vo;
    }
    
    /**
     * 将答题记录实体对象转换为VO对象
     */
    private ExamAnswerVO convertAnswerToVO(ExamAnswer answer) {
        if (answer == null) {
            return null;
        }
        
        ExamAnswerVO vo = new ExamAnswerVO();
        BeanUtils.copyProperties(answer, vo);
        
        return vo;
    }
    
    /**
     * 递归展平部门树结构
     * 
     * @param departments 部门列表
     * @param result 结果列表
     */
    private void flattenDepartmentTree(List<Department> departments, List<Department> result) {
        if (departments == null || departments.isEmpty()) {
            return;
        }
        
        for (Department department : departments) {
            result.add(department);
            if (department.getChildren() != null && !department.getChildren().isEmpty()) {
                flattenDepartmentTree(department.getChildren(), result);
            }
        }
    }

    @Override
    public void exportExamRecords(Map<String, Object> params, HttpServletResponse response) throws IOException {
        // 获取导出参数
        Integer examId = (Integer) params.get("examId");
        Integer departmentId = (Integer) params.get("departmentId");
        String keyword = (String) params.get("keyword");

        boolean includeStatistics = (Boolean) params.getOrDefault("includeStatistics", true);

        if (examId == null) {
            throw new BadRequestException("考试ID不能为空");
        }

        // 查询考试信息
        ExamExam exam = examExamMapper.selectById(examId);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }

        // 查询考试记录
        ExamRecordQueryParams queryParams = new ExamRecordQueryParams();
        queryParams.setExamId(examId);
        queryParams.setDepartmentId(departmentId);
        queryParams.setKeyword(keyword);
        queryParams.setPage(1L);
        queryParams.setSize(10000L); // 导出所有记录
        queryParams.setSortBy("score");
        queryParams.setSortOrder("desc");

        PageResponse<ExamRecordVO> recordPage = listExamRecords(queryParams);
        List<ExamRecordVO> records = recordPage.getList();

        // 简化版导出，直接生成Excel
        exportRecordsAsExcel(exam, records, includeStatistics, response);
    }

    /**
     * 导出考试记录为Excel格式
     */
    private void exportRecordsAsExcel(ExamExam exam, List<ExamRecordVO> records, boolean includeStatistics, HttpServletResponse response) throws IOException {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("成绩单");

        // 设置标题
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(exam.getTitle() + " - 成绩单");

        // 合并标题单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 7));

        // 创建表头
        Row headerRow = sheet.createRow(2);
        String[] headers = {"序号", "姓名", "部门", "得分", "总分", "得分率", "是否及格", "提交时间"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            sheet.setColumnWidth(i, 3000);
        }

        // 填充数据
        for (int i = 0; i < records.size(); i++) {
            ExamRecordVO record = records.get(i);
            Row dataRow = sheet.createRow(i + 3);

            dataRow.createCell(0).setCellValue(i + 1);
            dataRow.createCell(1).setCellValue(record.getUserName() != null ? record.getUserName() : "");
            dataRow.createCell(2).setCellValue(record.getDepartmentName() != null ? record.getDepartmentName() : "");
            dataRow.createCell(3).setCellValue(record.getScore() != null ? record.getScore() : 0);
            dataRow.createCell(4).setCellValue(record.getTotalScore() != null ? record.getTotalScore() : 0);

            // 计算得分率
            double percentage = record.getTotalScore() != null && record.getTotalScore() > 0 ?
                (double) record.getScore() / record.getTotalScore() * 100 : 0;
            dataRow.createCell(5).setCellValue(Math.round(percentage * 100) / 100.0 + "%");

            dataRow.createCell(6).setCellValue(record.getPassed() ? "是" : "否");
            dataRow.createCell(7).setCellValue(record.getEndTime() != null ? record.getEndTime().toString() : "");
        }

        // 设置响应头
        String fileName = URLEncoder.encode(exam.getTitle() + "_成绩单_" +
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx",
            StandardCharsets.UTF_8.toString());

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        // 写入响应
        workbook.write(response.getOutputStream());
        workbook.close();
    }
}