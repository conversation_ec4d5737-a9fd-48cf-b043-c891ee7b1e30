package com.cy.education.model.params;

import lombok.Data;

/**
 * 内容管理通用查询参数
 */
@Data
public class ContentQueryParam {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 状态：0禁用/草稿，1启用/发布
     */
    private Integer status;

    /**
     * 是否置顶（用于新闻和公告）
     */
    private Boolean isTop;

    /**
     * 重要程度（用于公告）
     */
    private Integer importance;

    /**
     * 关键词（用于标题、内容搜索）
     */
    private String keyword;
}
