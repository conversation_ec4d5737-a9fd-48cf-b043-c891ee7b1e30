<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.DepartmentMapper">

    <!-- 查询部门列表，根据父部门ID -->
    <select id="selectDepartmentList" resultType="com.cy.education.model.entity.Department">
        SELECT d.*
        FROM departments d
        <where>
            <if test="parentId != null">
                AND d.parent_id = #{parentId}
            </if>
            <if test="parentId == null">
                AND d.parent_id IS NULL
            </if>
        </where>
        ORDER BY d.sort ASC, d.id ASC
    </select>

    <!-- 查询部门及其子部门列表 -->
    <select id="selectDepartmentTreeList" resultType="com.cy.education.model.entity.Department">
        WITH RECURSIVE dept_tree AS (
            SELECT d.*
            FROM departments d
            <where>
                <if test="parentId != null">
                    d.parent_id = #{parentId}
                </if>
                <if test="parentId == null">
                    d.parent_id IS NULL
                </if>
            </where>
            UNION ALL
            SELECT d.*
            FROM departments d
            JOIN dept_tree dt ON d.parent_id = dt.id
        )
        SELECT * FROM dept_tree
        ORDER BY sort ASC, id ASC
    </select>

    <!-- 统计部门下的学员数量 -->
    <select id="countStudentsByDepartment" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM students
        WHERE department_id = #{departmentId}
    </select>

    <!-- 查询子部门ID列表 -->
    <select id="selectChildDepartmentIds" resultType="java.lang.Integer">
        WITH RECURSIVE dept_tree AS (
            SELECT id, parent_id
            FROM departments
            WHERE id = #{departmentId}
            UNION ALL
            SELECT d.id, d.parent_id
            FROM departments d
            JOIN dept_tree dt ON d.parent_id = dt.id
        )
        SELECT id FROM dept_tree WHERE id != #{departmentId}
    </select>
</mapper>
