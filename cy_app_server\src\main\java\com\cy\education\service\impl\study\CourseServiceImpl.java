package com.cy.education.service.impl.study;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.study.Course;
import com.cy.education.model.vo.CourseRecordDetailVO;
import com.cy.education.model.vo.CourseVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.CourseMapper;
import com.cy.education.repository.StudyRecordMapper;
import com.cy.education.service.study.CourseService;
import com.cy.education.service.study.StudyRecordService;
import com.cy.education.utils.SecurityUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements CourseService {

    @Autowired
    private StudyRecordService studyRecordService;

    @Autowired
    private StudyRecordMapper studyRecordMapper;

    @Override
    public PageResponse<Course> getCourseList(Integer page, Integer size, String name) {
        QueryWrapper<Course> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "published");
        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }
        queryWrapper.orderByDesc("create_time");
        queryWrapper.select(Course.class, i -> !"structure".equals(i.getColumn()));
        Page<Course> resultPage = baseMapper.selectPage(new Page<>(page, size), queryWrapper);

        // 获取课程列表
        List<Course> courses = resultPage.getRecords();

        // 如果有课程数据，则统计学习人数
        if (!courses.isEmpty()) {
            // 提取课程ID列表
            List<Integer> courseIds = courses.stream()
                    .map(Course::getId)
                    .collect(Collectors.toList());

            // 批量获取学习人数
            List<Map<String, Object>> studyCounts = studyRecordMapper.getCourseStudyCountsByIds(courseIds);

            // 将学习人数数据转换为Map，方便查找
            Map<Integer, Integer> studyCountMap = studyCounts.stream()
                    .collect(Collectors.toMap(
                            map -> (Integer) map.get("course_id"),
                            map -> ((Number) map.get("study_count")).intValue()
                    ));

            // 为每个课程设置学习人数
            courses.forEach(course -> {
                Integer studyCount = studyCountMap.get(course.getId());
                course.setStudyCount(studyCount != null ? studyCount : 0);
            });
        }

        // 转换为自定义分页响应对象
        return PageResponse.of(
                courses,
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    }

    @Override
    public CourseVO getCourseByIdWithStudyRecord(Integer id) {
        Course course = this.getById(id);
        if (course == null) {
            throw new BusinessException("课程不存在或已被删除");
        }
        // 获取课程学习记录详情
        CourseRecordDetailVO studyRecord = studyRecordService.getCourseRecordDetail(SecurityUtil.getCurrentUserId(), id);
        // 构建CourseVO
        CourseVO courseVO = new CourseVO();
        BeanUtils.copyProperties(course, courseVO);
        courseVO.setCurrentUserStudyRecord(studyRecord);
        return courseVO;
    }
}
