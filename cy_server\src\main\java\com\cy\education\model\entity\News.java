package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 新闻实体类
 */
@Data
@TableName("news")
public class News implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 新闻ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 新闻标题
     */
    private String title;

    /**
     * 封面图URL
     */
    private String coverUrl;

    /**
     * 新闻内容
     */
    private String content;

    /**
     * 发布时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 状态：0草稿，1发布
     */
    private Integer status;

    /**
     * 是否置顶
     */
    @TableField("is_top")
    private Boolean top;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 兼容前端的isTop属性
     * @return 是否置顶
     */
    public Boolean getIsTop() {
        return this.top;
    }

    /**
     * 兼容前端的isTop属性
     * @param isTop 是否置顶
     */
    public void setIsTop(Boolean isTop) {
        this.top = isTop;
    }
}
