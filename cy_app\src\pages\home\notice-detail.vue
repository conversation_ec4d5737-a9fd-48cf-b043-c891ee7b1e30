<template>
	<view class="page-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
				</view>
				<text class="navbar-title">公告详情</text>
				<view class="placeholder"></view>
			</view>
		</view>

		<!-- 内容容器 -->
		<view class="content-container with-navbar">
			<view v-if="loading" class="loading-state">
				<uni-load-more status="loading" />
			</view>

			<view v-else-if="!noticeDetail" class="empty-state">
				<text class="empty-text">公告不存在或已被删除</text>
			</view>

			<view v-else class="notice-detail-content">
				<text class="notice-title">{{ noticeDetail.title }}</text>
				<view class="meta-info-row">
                    <view class="importance-tag" :class="'level-' + (noticeDetail.importance || 1)">
						{{ getImportanceText(noticeDetail.importance) }}
					</view>
					<text class="meta-item">发布时间: {{ formatDate(noticeDetail.publishTime) }}</text>
				</view>
        <up-image
            v-if="noticeDetail.coverUrl"
            class="cover-image"
            :src="noticeDetail.coverUrl"
            radius="8"
            mode="aspectFill"
        />

				<view class="notice-body">
					<rich-text :nodes="noticeDetail.content" />
				</view>

				<view class="attachment-section" v-if="noticeDetail.attachments && noticeDetail.attachments.length > 0">
					<view class="section-title">附件下载</view>
					<view class="attachment-list">
						<view
							v-for="(file, index) in noticeDetail.attachments"
							:key="index"
							class="attachment-item"
							@tap="downloadAttachment(file)"
						>
							<view class="file-icon">
								<text class="icon">📄</text>
							</view>
							<view class="file-info">
								<text class="file-name">{{ file.name }}</text>
								<text class="file-size">{{ file.size }}</text>
							</view>
							<view class="download-btn">
								<text class="icon">↓</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getNoticeById, type NoticeItem } from '@/api/content'
import {formatDate} from "@/utils/timeUtil"
import {markNoticeAsRead} from '@/utils/storage'
const loading = ref(true)
const noticeDetail = ref<NoticeItem & { coverUrl?: string; attachments?: any[] } | null>(null)

const navigateBack = () => {
  uni.navigateBack()
}

const downloadAttachment = (file: { name: string; size: string; url: string }) => {
  uni.showToast({
    title: `开始下载: ${file.name}`,
    icon: 'none'
  })
  // 在这里实现真实的下载逻辑
  // uni.downloadFile(...)
}

const getImportanceText = (level?: number) => {
  switch (level) {
    case 1: return '一般';
    case 2: return '重要';
    case 3: return '紧急';
    default: return '一般';
  }
}

onLoad(async (options) => {
  const noticeId = options.id
  loading.value = true
  try {
    noticeDetail.value = await getNoticeById(Number(noticeId))
    // 标记通知为已读
    if (noticeDetail.value) {
      markNoticeAsRead(noticeDetail.value.id)
    }
  } finally {
    loading.value = false
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/pages/home/<USER>';
</style>
