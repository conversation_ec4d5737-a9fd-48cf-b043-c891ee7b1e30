server:
  port: 8090
  servlet:
    context-path: /api

spring:
  application:
    name: cy_education
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************
    username: test
    password: seBRPGJp5ti2xyic
  redis:
    host: **********
    port: 6379
    password: NEUneu123
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 解决Swagger在Spring Boot 2.7.x版本的兼容性问题
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

# MyBatis Plus 配置
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  type-aliases-package: com.cy.education.model.entity,com.example.cy_app_server.model
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_id
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: CY_Education_Secret_Key_2024
  expiration: 315360000000  # Token有效期：10年（永不过期）
  token-header: Authorization
  token-prefix: Bearer

# 文件上传配置
file:
  upload:
    path: ./uploads
    domain: http://localhost:8080
  
# Knife4j配置
knife4j:
  enable: true
  production: false
  basic:
    enable: false

# 阿里云OSS配置
aliyun:
  oss:
    # 以下配置需要替换为实际的值
    bucket: chengyuanxuexi
    region: cn-beijing
    host: https://chengyuanxuexi.oss-cn-beijing.aliyuncs.com
    dir: ${OSS_DIR:upload}
    expireTime: ${OSS_EXPIRE_TIME:3600}
    maxSize: ${OSS_MAX_SIZE:104857600} # 默认100MB
