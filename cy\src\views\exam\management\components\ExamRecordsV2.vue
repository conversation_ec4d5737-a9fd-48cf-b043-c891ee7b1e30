<template>
  <div class="exam-records">
    <!-- 筛选表单 -->
    <div class="filter-form">
      <el-form :inline="true" :model="filterForm" ref="filterFormRef">
        <el-form-item label="考试名称">
          <el-select v-model="filterForm.examId" placeholder="选择考试" clearable style="width: 200px">
            <el-option
              v-for="item in examOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filterForm.departmentId" placeholder="选择部门" clearable style="width: 200px">
            <el-option
              v-for="item in departmentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filterForm.status" placeholder="选择状态" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="已完成" value="completed" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="未开始" value="not_started" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">筛选</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计信息 -->
    <div class="statistics-section" v-if="statistics.totalCount > 0">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalCount }}</div>
              <div class="stat-label">总参与人数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.submittedCount }}</div>
              <div class="stat-label">已提交人数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.avgScore.toFixed(1) }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.passRate.toFixed(1) }}%</div>
              <div class="stat-label">通过率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="recordsList"
        :loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="studentName" label="学员姓名" width="120" />
        <el-table-column prop="studentNumber" label="学员编号" width="120" />
        <el-table-column prop="department" label="部门" width="150" />
        <el-table-column prop="examTitle" label="考试名称" width="200" />
        <el-table-column prop="score" label="得分" width="80" align="center">
          <template #default="{ row }">
            <span :class="getScoreClass(row.score, row.passScore)">
              {{ row.score }}/{{ row.totalScore }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="用时" width="100" align="center">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="提交时间" width="180" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 导出对话框 -->
    <el-dialog v-model="exportDialogVisible" title="导出考试记录" width="600px">
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="xlsx">Excel格式</el-radio>
            <el-radio label="csv">CSV格式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="导出范围">
          <el-radio-group v-model="exportForm.range">
            <el-radio label="all">全部记录</el-radio>
            <el-radio label="current">当前页面</el-radio>
            <el-radio label="selected">选中记录</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="导出字段">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox label="studentName">学员姓名</el-checkbox>
            <el-checkbox label="studentNumber">学员编号</el-checkbox>
            <el-checkbox label="department">部门</el-checkbox>
            <el-checkbox label="examTitle">考试名称</el-checkbox>
            <el-checkbox label="score">得分</el-checkbox>
            <el-checkbox label="status">状态</el-checkbox>
            <el-checkbox label="duration">用时</el-checkbox>
            <el-checkbox label="submitTime">提交时间</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmExport" :loading="exportLoading">
          确认导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getExamRecordList, getExamList, getExamStatistics } from '@/api/exam'
import { getDepartmentTree, type Department } from '@/api/department'

const route = useRoute()

// 表格数据和分页
const loading = ref(false)
const recordsList = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const selectedRecords = ref<any[]>([])

// 筛选表单
const filterForm = reactive({
  examId: '',
  departmentId: '',
  status: ''
})

// 统计数据
const statistics = ref({
  totalCount: 0,
  submittedCount: 0,
  avgScore: 0,
  passRate: 0
})

// 选项数据
const examOptions = ref<{ value: string, label: string }[]>([])
const departmentOptions = ref<{ value: string, label: string }[]>([])

// 导出相关
const exportDialogVisible = ref(false)
const exportLoading = ref(false)
const exportForm = reactive({
  format: 'xlsx',
  range: 'all',
  fields: ['studentName', 'examTitle', 'score', 'status', 'submitTime']
})

// 获取考试记录列表
const fetchRecordsList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      examId: filterForm.examId || undefined,
      departmentId: filterForm.departmentId || undefined,
      status: filterForm.status || undefined
    }
    
    const response = await getExamRecordList(params)
    recordsList.value = response.list || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取考试记录失败:', error)
    ElMessage.error('获取考试记录失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  if (!filterForm.examId) return
  
  try {
    const stats = await getExamStatistics(filterForm.examId)
    statistics.value = stats
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 初始化选项数据
const initOptions = async () => {
  try {
    // 获取考试列表
    const examResponse = await getExamList({ page: 1, size: 100 })
    examOptions.value = examResponse.list.map((exam: any) => ({
      value: exam.id.toString(),
      label: exam.name
    }))
    
    // 获取部门列表
    const deptResponse = await getDepartmentTree()
    departmentOptions.value = flattenDepartments(deptResponse)
  } catch (error) {
    console.error('初始化选项数据失败:', error)
  }
}

// 扁平化部门树
const flattenDepartments = (departments: Department[]): { value: string, label: string }[] => {
  const result: { value: string, label: string }[] = []
  
  const flatten = (depts: Department[], prefix = '') => {
    depts.forEach(dept => {
      result.push({
        value: dept.id.toString(),
        label: prefix + dept.name
      })
      if (dept.children && dept.children.length > 0) {
        flatten(dept.children, prefix + dept.name + ' / ')
      }
    })
  }
  
  flatten(departments)
  return result
}

// 事件处理
const handleFilter = () => {
  currentPage.value = 1
  fetchRecordsList()
  fetchStatistics()
}

const handleReset = () => {
  filterForm.examId = ''
  filterForm.departmentId = ''
  filterForm.status = ''
  currentPage.value = 1
  fetchRecordsList()
  statistics.value = { totalCount: 0, submittedCount: 0, avgScore: 0, passRate: 0 }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchRecordsList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchRecordsList()
}

const handleSelectionChange = (selection: any[]) => {
  selectedRecords.value = selection
}

const handleExport = () => {
  exportDialogVisible.value = true
}

const handleConfirmExport = async () => {
  if (exportForm.range === 'selected' && selectedRecords.value.length === 0) {
    ElMessage.warning('请先选择要导出的记录')
    return
  }
  
  exportLoading.value = true
  try {
    const params = {
      format: exportForm.format,
      range: exportForm.range,
      fields: exportForm.fields,
      examId: filterForm.examId,
      departmentId: filterForm.departmentId,
      status: filterForm.status,
      selectedIds: exportForm.range === 'selected' ? selectedRecords.value.map(r => r.id) : undefined
    }
    
    // 这里调用导出API
    ElMessage.success('导出成功')
    exportDialogVisible.value = false
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleViewDetail = (row: any) => {
  // 查看考试记录详情
  ElMessage.info('查看详情功能待实现')
}

// 工具方法
const getScoreClass = (score: number, passScore: number) => {
  if (score >= passScore) return 'text-success'
  return 'text-danger'
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'in_progress': return 'warning'
    case 'not_started': return 'info'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'in_progress': return '进行中'
    case 'not_started': return '未开始'
    default: return '未知'
  }
}

const formatDuration = (duration: number) => {
  if (!duration) return '-'
  const hours = Math.floor(duration / 60)
  const minutes = duration % 60
  return hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`
}

// 暴露方法给父组件
defineExpose({
  filterByExam: (examId: string) => {
    filterForm.examId = examId
    handleFilter()
  }
})

// 页面加载时获取数据
onMounted(() => {
  initOptions()
  fetchRecordsList()
})
</script>

<style scoped>
.exam-records {
  width: 100%;
}

.filter-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.statistics-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.text-success {
  color: #67C23A;
  font-weight: bold;
}

.text-danger {
  color: #F56C6C;
  font-weight: bold;
}
</style>
