package com.cy.education.service;

import com.cy.education.model.entity.Admin;
import com.cy.education.model.params.*;
import com.cy.education.model.vo.AdminVO;
import com.cy.education.model.vo.PageResponse;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 管理员服务接口
 */
public interface AdminService {

    /**
     * 分页查询管理员列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResponse<AdminVO> getAdminList(AdminQueryParams params);

    /**
     * 根据ID查询管理员详情
     *
     * @param id 管理员ID
     * @return 管理员详情
     */
    AdminVO getAdminById(Integer id);

    /**
     * 新增管理员
     *
     * @param params 管理员信息
     * @return 新增的管理员ID
     */
    Integer addAdmin(AdminAddParams params);

    /**
     * 更新管理员信息
     *
     * @param params 管理员信息
     * @return 是否更新成功
     */
    boolean updateAdmin(AdminUpdateParams params);

    /**
     * 删除管理员
     *
     * @param id 管理员ID
     * @return 是否删除成功
     */
    boolean deleteAdmin(Integer id);

    /**
     * 更新管理员状态
     *
     * @param params 状态参数
     * @return 是否更新成功
     */
    boolean updateAdminStatus(AdminStatusParams params);

    /**
     * 重置管理员密码
     *
     * @param id 管理员ID
     * @return 新密码
     */
    String resetAdminPassword(Integer id);

    /**
     * 获取管理员权限列表
     *
     * @param id 管理员ID
     * @return 权限ID列表
     */
    List<Integer> getAdminPermissions(Integer id);

    /**
     * 设置管理员权限
     *
     * @param params 权限参数
     * @return 是否设置成功
     */
    boolean setAdminPermissions(AdminPermissionParams params);

    /**
     * 检查用户名是否已存在
     *
     * @param username 用户名
     * @param excludeId 排除的管理员ID（用于更新时的检查）
     * @return 是否已存在
     */
    boolean isUsernameExists(String username, Integer excludeId);

    // ==================== 导入导出功能 ====================

    /**
     * 生成管理员导入模板
     *
     * @return 模板文件资源
     */
    Resource generateImportTemplate() throws IOException;

    /**
     * 批量导入管理员
     *
     * @param file Excel文件
     * @return 导入结果
     */
    Map<String, Object> importAdmins(MultipartFile file);

    /**
     * 导出管理员列表
     *
     * @param params 导出参数
     * @param response HTTP响应
     */
    void exportAdmins(Map<String, Object> params, HttpServletResponse response) throws IOException;
}