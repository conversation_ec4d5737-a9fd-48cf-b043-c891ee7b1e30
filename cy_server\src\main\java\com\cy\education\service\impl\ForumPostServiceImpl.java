package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.ForumComment;
import com.cy.education.model.entity.ForumPost;
import com.cy.education.model.vo.ForumPostQueryParam;
import com.cy.education.repository.ForumCommentMapper;
import com.cy.education.repository.ForumPostMapper;
import com.cy.education.service.ForumPostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 论坛帖子服务实现
 */
@Service
public class ForumPostServiceImpl implements ForumPostService {

    @Autowired
    private ForumPostMapper forumPostMapper;
    
    @Autowired
    private ForumCommentMapper forumCommentMapper;
    
    // 状态常量
    private static final int STATUS_PENDING = 0;
    private static final int STATUS_APPROVED = 1;
    private static final int STATUS_REJECTED = 2;
    private static final int STATUS_DELETED = 3;

    @Override
    public IPage<ForumPost> getPostPage(ForumPostQueryParam param) {
        // 创建分页对象
        Page<ForumPost> page = new Page<>(param.getPage(), param.getSize());
        
        // 调用Mapper查询
        return forumPostMapper.selectPostPage(page, param.getCategoryId(), param.getKeyword(), param.getStatus());
    }

    @Override
    public ForumPost getPostById(Integer id) {
        // 查询帖子
        ForumPost post = forumPostMapper.selectPostWithDetailsById(id);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }
        
        return post;
    }

    @Override
    public boolean incrementViewCount(Integer id) {
        return forumPostMapper.incrementViewCount(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewPost(Integer id, Integer status) {
        // 检查状态是否合法
        if (!isValidStatus(status)) {
            throw new BusinessException("无效的状态值，应为1(已通过)或2(已拒绝)");
        }
        
        // 检查帖子是否存在
        ForumPost post = forumPostMapper.selectById(id);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }
        
        // 更新状态
        return forumPostMapper.updateStatus(id, status) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setPostTop(Integer id, Boolean isTop) {
        // 检查帖子是否存在
        ForumPost post = forumPostMapper.selectById(id);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }
        
        // 更新置顶状态
        return forumPostMapper.updateTopStatus(id, isTop) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setPostEssence(Integer id, Boolean isEssence) {
        // 检查帖子是否存在
        ForumPost post = forumPostMapper.selectById(id);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }
        
        // 更新精华状态
        return forumPostMapper.updateEssenceStatus(id, isEssence) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePost(Integer id) {
        // 检查帖子是否存在
        ForumPost post = forumPostMapper.selectById(id);
        if (post == null) {
            throw new BusinessException("帖子不存在");
        }
        
        // 假删除帖子（将状态设为已删除）
        return forumPostMapper.softDeletePost(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchReviewPosts(List<Integer> ids, Integer status) {
        // 检查状态是否合法
        if (!isValidStatus(status)) {
            throw new BusinessException("无效的状态值，应为1(已通过)或2(已拒绝)");
        }
        
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // 遍历批量更新
        boolean success = true;
        for (Integer id : ids) {
            if (forumPostMapper.updateStatus(id, status) <= 0) {
                success = false;
            }
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeletePosts(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // 遍历批量假删除
        boolean success = true;
        for (Integer id : ids) {
            try {
                if (forumPostMapper.softDeletePost(id) <= 0) {
                    success = false;
                }
            } catch (Exception e) {
                success = false;
            }
        }
        
        return success;
    }

    @Override
    public boolean updateReplyCount(Integer postId) {
        return forumPostMapper.updateReplyCount(postId) > 0;
    }
    
    /**
     * 检查状态是否合法
     * 
     * @param status 状态
     * @return 是否合法
     */
    private boolean isValidStatus(Integer status) {
        return status == STATUS_APPROVED || status == STATUS_REJECTED;
    }
} 