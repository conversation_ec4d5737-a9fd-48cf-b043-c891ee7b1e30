package com.example.cy_app_server.service;

import com.example.cy_app_server.mapper.PracticeMapper;
import com.example.cy_app_server.model.PracticeRecord;
import com.example.cy_app_server.model.PracticeStats;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
public class PracticeService {

    @Autowired
    private PracticeMapper practiceMapper;

    public List<Map<String, Object>> getAvailableBanks() {
        return practiceMapper.getAvailableBanks();
    }

    public List<Map<String, Object>> getUserPracticeStats(Integer userId) {
        return practiceMapper.getUserPracticeStats(userId);
    }

    public List<Map<String, Object>> getWrongQuestions(Integer userId, Integer bankId) {
        return practiceMapper.getWrongQuestions(userId, bankId);
    }

    public Map<String, Object> startPractice(Integer userId, Integer bankId, String type) {
        PracticeRecord record = new PracticeRecord();
        record.setUserId(userId);
        record.setBankId(bankId);
        record.setType(type);
        record.setStatus("ongoing");
        record.setStartTime(LocalDateTime.now());

        if ("wrong".equals(type)) {
            record.setTotalQuestions(practiceMapper.getWrongQuestionsCount(userId, bankId));
        } else {
            record.setTotalQuestions(practiceMapper.getBankQuestionsCount(bankId));
        }

        practiceMapper.createPracticeRecord(record);

        return Map.of("recordId", record.getId());
    }

    public Map<String, Object> submitAnswer(Integer recordId, Integer questionId, String userAnswer) {
        practiceMapper.createPracticeAnswer(recordId, questionId, userAnswer);
        PracticeRecord record = practiceMapper.getPracticeRecordById(recordId);
        updatePracticeStats(record.getUserId(), record.getBankId());
        return Map.of("success", true);
    }
    
    public Map<String, Object> getPracticeRecordDetail(Integer recordId) {
        Map<String, Object> recordMap = practiceMapper.getPracticeRecord(recordId);
        List<Map<String, Object>> answers = practiceMapper.getPracticeAnswers(recordId);

        if (recordMap != null && "completed".equals(recordMap.get("status"))) {
            Integer userId = (Integer) recordMap.get("user_id");
            Integer bankId = (Integer) recordMap.get("bank_id");

            if (bankId != null) {
                updatePracticeStats(userId, bankId);
            }
        }

        return Map.of("record", recordMap, "answers", answers);
    }

    public Map<String, Object> getNextQuestion(Integer recordId) {
        // 这里需要实现获取下一道题的逻辑
        // 暂时返回一个简单的响应
        return Map.of("hasNext", false, "message", "功能开发中");
    }

    public Map<String, Object> completePractice(Integer recordId) {
        // 更新练习记录状态为已完成
        PracticeRecord record = practiceMapper.getPracticeRecordById(recordId);
        if (record != null) {
            record.setStatus("completed");
            record.setEndTime(LocalDateTime.now());
            // 这里需要添加更新记录的mapper方法
            updatePracticeStats(record.getUserId(), record.getBankId());
        }
        return Map.of("success", true, "message", "练习已完成");
    }

    public List<Map<String, Object>> getUserPracticeRecords(Integer userId) {
        // 这里需要实现获取用户练习记录的逻辑
        // 暂时返回空列表
        return List.of();
    }

    public Map<String, Object> clearWrongQuestions(Integer userId, Integer bankId) {
        // 这里需要实现清空错题本的逻辑
        // 暂时返回成功响应
        return Map.of("success", true, "message", "错题本已清空");
    }

    private void updatePracticeStats(Integer userId, Integer bankId) {
        PracticeStats stats = practiceMapper.getPracticeStats(userId, bankId);
        if (stats == null) {
            stats = new PracticeStats();
            stats.setUserId(userId);
            stats.setBankId(bankId);
            stats.setTotalQuestions(practiceMapper.getBankQuestionsCount(bankId));
            practiceMapper.createPracticeStats(stats);
        }

        List<Map<String, Object>> userAnswers = practiceMapper.getUserAnswersForBank(userId, bankId);
        long correctCount = userAnswers.stream().filter(a -> (Boolean) a.get("is_correct")).count();
        long wrongCount = userAnswers.size() - correctCount;

        stats.setAnsweredQuestions(userAnswers.size());
        stats.setCorrectCount((int) correctCount);
        stats.setWrongCount((int) wrongCount);
        stats.setAccuracyRate(new BigDecimal(correctCount).divide(new BigDecimal(userAnswers.size()), 2, RoundingMode.HALF_UP));
        stats.setLastPracticeTime(LocalDateTime.now());

        practiceMapper.updatePracticeStats(stats);
    }
}
