package com.cy.education.controller;

import com.cy.education.model.entity.PointsExchange;
import com.cy.education.model.params.PointsAdjustParam;
import com.cy.education.service.PointsExchangeService;
import com.cy.education.service.PointsRecordService;
import com.cy.education.service.PointsRuleService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 积分控制器测试类
 */
@WebMvcTest(PointsController.class)
public class PointsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PointsRuleService pointsRuleService;

    @MockBean
    private PointsRecordService pointsRecordService;

    @MockBean
    private PointsExchangeService pointsExchangeService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testGetRuleList() throws Exception {
        mockMvc.perform(get("/points/rules")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk());
    }

    @Test
    public void testGetRecordList() throws Exception {
        mockMvc.perform(get("/points/records")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk());
    }

    @Test
    public void testGetExchangeList() throws Exception {
        mockMvc.perform(get("/points/exchanges")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk());
    }

    @Test
    public void testGetUserBalance() throws Exception {
        mockMvc.perform(get("/points/balance/1"))
                .andExpect(status().isOk());
    }

    @Test
    public void testAdjustPoints() throws Exception {
        PointsAdjustParam param = new PointsAdjustParam();
        param.setUserId(1);
        param.setPoints(100);
        param.setDescription("测试调整");
        param.setOperator("admin");

        mockMvc.perform(post("/points/adjust")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(param)))
                .andExpect(status().isOk());
    }

    @Test
    public void testCreateExchange() throws Exception {
        PointsExchange exchange = new PointsExchange();
        exchange.setUserId(1);
        exchange.setProductId(1);
        exchange.setQuantity(1);

        mockMvc.perform(post("/points/exchanges")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(exchange)))
                .andExpect(status().isOk());
    }

    @Test
    public void testGetStatistics() throws Exception {
        mockMvc.perform(get("/points/statistics"))
                .andExpect(status().isOk());
    }

    @Test
    public void testGetRuleStatistics() throws Exception {
        mockMvc.perform(get("/points/rules/statistics"))
                .andExpect(status().isOk());
    }

    @Test
    public void testGetExchangeStatistics() throws Exception {
        mockMvc.perform(get("/points/exchanges/statistics"))
                .andExpect(status().isOk());
    }
}
