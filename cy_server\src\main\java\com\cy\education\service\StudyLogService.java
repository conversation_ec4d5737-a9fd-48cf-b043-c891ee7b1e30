package com.cy.education.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.StudyLog;
import com.cy.education.model.params.StudyRecordQueryParams;

import java.util.List;
import java.util.Map;

/**
 * 学习日志服务接口
 */
public interface StudyLogService {
    
    /**
     * 分页查询学习日志
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<StudyLog> getStudyLogList(StudyRecordQueryParams params);
    
    /**
     * 记录学习日志
     * @param studyLog 学习日志
     * @return 记录后的学习日志
     */
    StudyLog recordStudyLog(StudyLog studyLog);
    
    /**
     * 获取学员的学习时间趋势
     * @param studentId 学员ID
     * @param days 天数
     * @return 学习时间趋势数据
     */
    List<Map<String, Object>> getStudyTimeTrend(Integer studentId, Integer days);
    
    /**
     * 获取课程的学习活动记录
     * @param courseId 课程ID
     * @param limit 限制数量
     * @return 学习活动记录
     */
    List<StudyLog> getCourseActivities(Integer courseId, Integer limit);
} 