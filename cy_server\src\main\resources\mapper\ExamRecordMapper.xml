<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.ExamRecordMapper">

    <!-- 分页查询考试记录列表 -->
    <select id="selectExamRecordPage" resultType="com.cy.education.model.entity.ExamRecord">
        SELECT er.*
        FROM exam_record er
        LEFT JOIN students s ON er.user_id = s.id
        <where>
            <if test="examId != null">
                AND er.exam_id = #{examId}
            </if>
            <if test="departmentId != null">
                AND er.department_id = #{departmentId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND s.name LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="status != null">
                AND er.status = #{status}
            </if>
            <if test="isPassed != null">
                AND er.is_passed = #{isPassed}
            </if>
        </where>
        <choose>
            <when test="sortBy != null and sortBy != '' and sortOrder != null and sortOrder != ''">
                ORDER BY er.${sortBy} ${sortOrder}
            </when>
            <otherwise>
                ORDER BY er.created_at DESC
            </otherwise>
        </choose>
    </select>
</mapper>
