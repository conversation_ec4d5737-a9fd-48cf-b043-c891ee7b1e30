<template>
  <view class="data-usage-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">数据使用</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-container">
      <view class="usage-section">
        <view class="section-title">数据使用情况</view>

        <!-- 存储使用 -->
        <view class="usage-card">
          <view class="card-header">
            <up-icon color="#667eea" name="folder" size="20"></up-icon>
            <text class="card-title">存储空间</text>
          </view>
          <view class="usage-info">
            <view class="usage-bar">
              <view :style="{ width: storageUsage.percentage + '%' }" class="usage-progress"></view>
            </view>
            <view class="usage-text">
              <text>已使用 {{ storageUsage.used }}</text>
              <text>总计 {{ storageUsage.total }}</text>
            </view>
          </view>
        </view>

        <!-- 缓存数据 -->
        <view class="usage-card">
          <view class="card-header">
            <up-icon color="#f5576c" name="folder" size="20"></up-icon>
            <text class="card-title">缓存数据</text>
          </view>
          <view class="cache-info">
            <view class="cache-item">
              <text class="cache-name">图片缓存</text>
              <text class="cache-size">{{ cacheData.images }}</text>
            </view>
            <view class="cache-item">
              <text class="cache-name">视频缓存</text>
              <text class="cache-size">{{ cacheData.videos }}</text>
            </view>
            <view class="cache-item">
              <text class="cache-name">文档缓存</text>
              <text class="cache-size">{{ cacheData.documents }}</text>
            </view>
            <view class="cache-item">
              <text class="cache-name">其他缓存</text>
              <text class="cache-size">{{ cacheData.others }}</text>
            </view>
          </view>
          <view class="cache-actions">
            <up-button
                customStyle="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 8px;"
                size="small"
                text="清除缓存"
                type="primary"
                @click="handleClearCache"
            ></up-button>
          </view>
        </view>

        <!-- 网络使用 -->
        <view class="usage-card">
          <view class="card-header">
            <up-icon color="#43e97b" name="wifi" size="20"></up-icon>
            <text class="card-title">网络使用</text>
          </view>
          <view class="network-info">
            <view class="network-item">
              <text class="network-name">本月已用流量</text>
              <text class="network-size">{{ networkUsage.monthly }}</text>
            </view>
            <view class="network-item">
              <text class="network-name">今日已用流量</text>
              <text class="network-size">{{ networkUsage.today }}</text>
            </view>
          </view>
        </view>

        <!-- 数据设置 -->
        <view class="settings-card">
          <view class="card-header">
            <up-icon color="#fa709a" name="setting" size="20"></up-icon>
            <text class="card-title">数据设置</text>
          </view>
          <view class="settings-list">
            <view class="setting-item">
              <view class="setting-left">
                <text class="setting-name">自动清理缓存</text>
                <text class="setting-desc">每周自动清理超过7天的缓存</text>
              </view>
              <view class="setting-right">
                <up-switch
                    v-model="settings.autoClearCache"
                    activeColor="#667eea"
                    @change="handleAutoClearChange"
                ></up-switch>
              </view>
            </view>
            <view class="setting-item">
              <view class="setting-left">
                <text class="setting-name">仅WiFi下载</text>
                <text class="setting-desc">仅在WiFi环境下下载课程资源</text>
              </view>
              <view class="setting-right">
                <up-switch
                    v-model="settings.wifiOnly"
                    activeColor="#667eea"
                    @change="handleWifiOnlyChange"
                ></up-switch>
              </view>
            </view>
            <view class="setting-item">
              <view class="setting-left">
                <text class="setting-name">数据统计</text>
                <text class="setting-desc">允许收集使用数据以改进服务</text>
              </view>
              <view class="setting-right">
                <up-switch
                    v-model="settings.dataCollection"
                    activeColor="#667eea"
                    @change="handleDataCollectionChange"
                ></up-switch>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      storageUsage: {
        used: '156MB',
        total: '1GB',
        percentage: 15.6
      },
      cacheData: {
        images: '45MB',
        videos: '89MB',
        documents: '12MB',
        others: '10MB'
      },
      networkUsage: {
        monthly: '2.3GB',
        today: '156MB'
      },
      settings: {
        autoClearCache: true,
        wifiOnly: true,
        dataCollection: false
      }
    }
  },

  methods: {
    // 清除缓存
    handleClearCache() {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除所有缓存数据吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '清除中...'
            })

            setTimeout(() => {
              uni.hideLoading()
              uni.showToast({
                title: '缓存已清除',
                icon: 'success'
              })

              // 重置缓存数据
              this.cacheData = {
                images: '0MB',
                videos: '0MB',
                documents: '0MB',
                others: '0MB'
              }
            }, 1500)
          }
        }
      })
    },

    // 自动清理缓存开关
    handleAutoClearChange(value) {
      this.settings.autoClearCache = value
      uni.showToast({
        title: value ? '已开启自动清理' : '已关闭自动清理',
        icon: 'none'
      })
    },

    // WiFi下载开关
    handleWifiOnlyChange(value) {
      this.settings.wifiOnly = value
      uni.showToast({
        title: value ? '已开启仅WiFi下载' : '已关闭仅WiFi下载',
        icon: 'none'
      })
    },

    // 数据统计开关
    handleDataCollectionChange(value) {
      this.settings.dataCollection = value
      uni.showToast({
        title: value ? '已开启数据统计' : '已关闭数据统计',
        icon: 'none'
      })
    },

    // 返回上一页
    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.data-usage-container {
  min-height: 100vh;
  background-color: #f5f6fa;
}

.content-container {
  padding: 20px;
}

.usage-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
}

.usage-card,
.settings-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.usage-info {
  margin-top: 12px;
}

.usage-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.usage-progress {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.usage-text {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

.cache-info {
  margin-bottom: 16px;
}

.cache-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.cache-name {
  font-size: 14px;
  color: #333;
}

.cache-size {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.cache-actions {
  display: flex;
  justify-content: center;
}

.network-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.network-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.network-name {
  font-size: 14px;
  color: #333;
}

.network-size {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.setting-left {
  flex: 1;
  margin-right: 16px;
}

.setting-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;
}

.setting-desc {
  font-size: 12px;
  color: #999;
  display: block;
}

.setting-right {
  display: flex;
  align-items: center;
}
</style>
