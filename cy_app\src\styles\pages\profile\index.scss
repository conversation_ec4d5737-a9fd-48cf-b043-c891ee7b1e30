.profile-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f6fa;
}

.header-section {
  background: linear-gradient(135deg, #6a8dff 0%, #a084ee 100%);
  padding-bottom: 32px;
  padding-top: env(safe-area-inset-top, 24px);
  position: relative;
  border-radius: 0 0 32px 32px;
  box-shadow: 0 8px 32px rgba(106, 141, 255, 0.10);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  z-index: 2;
  margin-bottom: 20px;
}

.profile-card {
  margin: 0 20px 0 20px;
  padding: 22px 20px 18px 20px;
  background: rgba(255, 255, 255, 0.13);
  border-radius: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 4px 20px rgba(106, 141, 255, 0.10);
  position: relative;
  top: 18px;
}

.profile-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.avatar {
  margin-right: 16px;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  margin-bottom: 4px;
}

.job-info {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2px;
}

.employee-id {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.points-section {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px 12px 0 0;
}

.points-label {
  display: block;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
}

.points-value {
  display: block;
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}

.content-container {
  padding: 0 16px 100px 16px;
  margin-top: -10px;
}

.stats-section,
.function-section,
.system-section {
  background: #fff;
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-card {
  text-align: center;
  padding: 16px;
  border-radius: 16px;
  background: #f8f9fa;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-number {
  display: block;
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 8px;
  border-radius: 12px;
  transition: background 0.2s;
}

.function-item:hover {
  background: #f8f9fa;
}

.function-icon {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.function-name {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.system-list {
  display: flex;
  flex-direction: column;
}

.system-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 12px;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 12px;
  transition: background 0.2s;
}

.system-item:hover {
  background: #f8f9fa;
}

.system-item:last-child {
  border-bottom: none;
}

.system-left {
  display: flex;
  align-items: center;
}

.system-name {
  font-size: 15px;
  color: #333;
  margin-left: 12px;
}

.system-right {
  display: flex;
  align-items: center;
}

.system-value {
  font-size: 14px;
  color: #999;
  margin-right: 8px;
}
