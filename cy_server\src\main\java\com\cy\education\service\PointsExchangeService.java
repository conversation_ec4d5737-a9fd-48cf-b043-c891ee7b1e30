package com.cy.education.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.PointsExchange;
import com.cy.education.model.params.PointsExchangeQueryParam;
import com.cy.education.model.vo.ApiResponse;

import java.util.List;
import java.util.Map;

/**
 * 积分兑换服务接口
 */
public interface PointsExchangeService {

    /**
     * 分页查询兑换记录
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<PointsExchange> page(PointsExchangeQueryParam param);
    
    /**
     * 根据ID查询兑换记录
     *
     * @param id 兑换记录ID
     * @return 兑换记录对象
     */
    PointsExchange getById(Integer id);
    
    /**
     * 创建兑换记录
     *
     * @param exchange 兑换记录对象
     * @return 创建结果
     */
    ApiResponse<Map<String, Object>> create(PointsExchange exchange);

    /**
     * 更新兑换状态
     *
     * @param id 兑换记录ID
     * @param status 状态
     * @return 更新结果
     */
    ApiResponse<Map<String, Object>> updateStatus(Integer id, String status);

    /**
     * 发货
     *
     * @param id 兑换记录ID
     * @param company 物流公司
     * @param trackingNumber 物流单号
     * @param remark 备注
     * @return 操作结果
     */
    ApiResponse<Map<String, Object>> ship(Integer id, String company, String trackingNumber, String remark);

    /**
     * 批量发货
     *
     * @param shipments 发货信息列表
     * @param company 统一物流公司
     * @param remark 统一备注
     * @return 操作结果
     */
    ApiResponse<Map<String, Object>> batchShip(List<Map<String, Object>> shipments, String company, String remark);
    
    /**
     * 获取兑换统计数据
     *
     * @return 统计数据
     */
    Map<String, Object> getStatistics();
    
    /**
     * 批量更新兑换状态
     *
     * @param ids 兑换记录ID列表
     * @param status 状态
     * @param remark 备注
     * @return 操作结果
     */
    ApiResponse<Map<String, Object>> batchUpdateStatus(List<Integer> ids, String status, String remark);
} 