import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import router from '@/router'

// 导入模拟数据（放在前面的import语句里，避免循环依赖）
// import '../mock'

// 扩展AxiosRequestConfig类型，添加showLoading属性
interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // 从环境变量获取API基础URL
  timeout: 15000 // 请求超时时间
})

// 添加调试日志
console.log('API基础URL:', service.defaults.baseURL)

// loading实例
let loadingInstance: any
let loadingCount = 0

// 显示loading
const showLoading = () => {
  if (loadingCount === 0) {
    loadingInstance = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
  }
  loadingCount++
}

// 隐藏loading
const hideLoading = () => {
  loadingCount--
  if (loadingCount <= 0) {
    loadingCount = 0
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }
}

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求发送前可以进行处理，例如添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    console.log('请求拦截器处理请求:', config)
    // 显示loading
    const customConfig = config as RequestConfig
    if (customConfig.showLoading !== false) {
      showLoading()
    }

    return config
  },
  (error) => {
    // 处理请求错误
    console.error('Request error:', error)
    hideLoading()
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 关闭loading
    hideLoading()

    console.log('响应拦截器处理响应:', response.config.url, response.data)

    try {
      // 特殊处理Blob响应（文件下载）
      if (response.config.responseType === 'blob') {
        console.log('Blob响应处理:', response)
        return response.data
      }

      // 检查是否有真实的响应数据
      if (!response.data) {
        console.error('响应数据为空')
        return Promise.reject(new Error('响应数据为空'))
      }

      const res = response.data

      // 特殊处理登录请求
      if (response.config.url?.includes('/user/login')) {
        console.log('登录请求特殊处理')
        
        // 兼容多种登录响应格式
        if (res && typeof res === 'object') {
          // 情况1: 直接返回登录结果对象 {token, user}
          if (res.token && res.user) {
            console.log('登录成功(直接响应):', res)
            return res
          }
          // 情况2: 标准API响应格式 {code, message, data: {token, user}}
          else if (res.code === 200 && res.data) {
            if (res.data.token && res.data.user) {
              console.log('登录成功(标准响应):', res.data)
              return res.data
            }
          }
          // 登录失败情况
          else if (res.code !== 200) {
            console.error('登录失败:', res.message)
            return Promise.reject(new Error(res.message || '登录失败'))
          }
        }
        
        console.warn('未能识别的登录响应格式:', res)
        // 尝试直接返回响应，让上层处理
        return res
      }

      // 如果是标准API响应格式，直接返回data字段
      if (res && res.code === 200) {
        console.log('成功处理响应数据:', res)
        return res.data
      }

      // 根据自定义错误码处理错误
      if (res.code !== 200 && res.code !== 0) {
        // 特殊处理登出请求，即使失败也视为成功
        if (response.config.url?.includes('/user/logout')) {
          console.log('登出请求特殊处理')
          return { success: true }
        }

        ElMessage({
          message: res.message || '请求失败',
          type: 'error',
          duration: 5 * 1000
        })

        // 特定的错误码处理
        if (res.code === 401) {
          // token过期或未登录
          ElMessage({
            message: '您的登录已过期，请重新登录',
            type: 'error',
            duration: 5 * 1000,
            onClose: () => {
              localStorage.removeItem('token')
              router.push('/login')
            }
          })
        }

        return Promise.reject(new Error(res.message || '请求失败'))
      } else {
        return res.data
      }
    } catch (e) {
      console.error('响应拦截器处理出错:', e)
      return Promise.reject(e)
    }
  },
  (error) => {
    // 关闭loading
    hideLoading()

    console.error('Response error:', error)
    
    // 特殊处理登出请求，即使失败也视为成功
    if (error.config && error.config.url && error.config.url.includes('/user/logout')) {
      console.log('登出请求错误特殊处理')
      return Promise.resolve({ success: true })
    }
    
    // 尝试从错误对象中获取响应数据
    if (error.response?.data) {
      const errorData = error.response.data
      
      // 如果是模拟数据返回的成功响应
      if (errorData.code === 200 && errorData.data !== undefined) {
        console.log('从错误中恢复模拟数据:', errorData)
        return Promise.resolve(errorData.data)
      }
    }
    
    // 网络错误处理
    let message = '网络请求失败'
    if (error.response) {
      // 根据HTTP状态码自定义错误信息
      switch (error.response.status) {
        case 400:
          message = '请求错误'
          break
        case 401:
          message = '未授权，请重新登录'
          localStorage.removeItem('token')
          router.push('/login')
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址出错'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = `未知错误(${error.response.status})`
      }
    } else if (error.message.includes('timeout')) {
      message = '请求超时'
    } else if (error.message.includes('Network Error')) {
      message = '网络错误，请检查您的网络连接'
    }

    ElMessage({
      message,
      type: 'error',
      duration: 5 * 1000
    })

    return Promise.reject(error)
  }
)

// 封装GET请求
export function get<T>(url: string, params?: any, config?: RequestConfig): Promise<T> {
  return service.get(url, { params, ...config })
}

// 封装POST请求
export function post<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
  console.log(`开始发送POST请求: ${url}`, { data: data ? { ...data, password: data.password ? '******' : undefined } : undefined })
  return service.post(url, data, config)
    .then(response => {
      console.log(`POST请求成功: ${url}`, response)
      return response as T
    })
    .catch(error => {
      console.error(`POST请求失败: ${url}`, error)
      throw error
    })
}

// 封装PUT请求
export function put<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
  return service.put(url, data, config)
}

// 封装DELETE请求
export function del<T>(url: string, params?: any, config?: RequestConfig): Promise<T> {
  return service.delete(url, { params, ...config })
}

// 导出axios实例
export default service
