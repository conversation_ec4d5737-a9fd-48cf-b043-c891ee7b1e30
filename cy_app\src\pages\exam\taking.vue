<template>
  <view class="page-container">

    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="navbar-left">
          <view class="back-btn" @click="confirmExit">
            <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
          </view>
          <text class="navbar-title">{{ examInfo.title }}</text>
        </view>
        <view class="navbar-right">
          <view class="timer-display">
            <view class="timer-icon">
              <up-icon color="#fff" name="clock" size="16"></up-icon>
            </view>
            <text class="timer-text">{{ formatTime(remainingTime) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 进度指示器 -->
    <view class="progress-section">
      <view class="progress-info">
        <text class="progress-text">{{ currentQuestionIndex + 1 }}/{{ questions.length }}</text>
        <text class="progress-label">已答题：{{ answeredCount }}/{{ questions.length }}</text>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
      </view>
    </view>

    <!-- 题目内容 -->
    <view class="content-container">
      <view class="question-card">
        <view class="question-header">
          <view class="question-meta">
            <view class="question-info">
              <text class="question-number">第 {{ currentQuestionIndex + 1 }} 题</text>
              <view class="question-type">
                <text class="type-badge">{{ getQuestionTypeName(currentQuestion.type) }}</text>
                <text class="question-score">{{ currentQuestion.score }}分</text>
              </view>
            </view>
            <view class="question-actions">
              <view
                class="mark-btn"
                :class="{ marked: currentQuestion.marked }"
                @click="toggleMark"
              >
                <up-icon
                  :name="currentQuestion.marked ? 'star-fill' : 'star'"
                  :color="currentQuestion.marked ? '#fa709a' : '#8e8e93'"
                  size="18"
                ></up-icon>
                <text class="mark-text">{{ currentQuestion.marked ? '已标记' : '标记' }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="question-body">
          <text class="question-text">{{ currentQuestion.question }}</text>

          <!-- 图片展示 -->
          <view v-if="currentQuestion.images && currentQuestion.images.length > 0" class="question-images">
            <view
              v-for="(img, imgIndex) in currentQuestion.images"
              :key="imgIndex"
              class="image-wrapper"
              @click="previewImage(img, currentQuestion.images)"
            >
              <up-image
                :src="img"
                width="100%"
                height="200"
                mode="aspectFit"
                radius="8"
                class="question-image"
              ></up-image>
            </view>
          </view>

          <!-- 选择题选项 -->
          <view v-if="currentQuestion.type === 'single' || currentQuestion.type === 'multiple'" class="options-container">
            <view
              v-for="option in currentQuestion.options"
              :key="option.key"
              class="option-item"
              :class="{ selected: isOptionSelected(option.key) }"
              @click="selectOption(option.key)"
            >
              <view class="option-indicator">
                <view class="option-circle" :class="{ selected: isOptionSelected(option.key) }">
                  <view v-if="isOptionSelected(option.key)" class="option-check">
                    <up-icon
                      :name="currentQuestion.type === 'single' ? 'checkmark' : 'checkmark'"
                      color="#fff"
                      size="14"
                    ></up-icon>
                  </view>
                </view>
                <text class="option-key">{{ option.key }}</text>
              </view>
              <text class="option-text">{{ option.text }}</text>
            </view>
          </view>

          <!-- 判断题选项 -->
          <view v-if="currentQuestion.type === 'judge'" class="judge-options">
            <view
              class="judge-option"
              :class="{ selected: currentQuestion.answer === 'true' }"
              @click="selectJudge('true')"
            >
              <view class="judge-indicator">
                <view class="judge-circle" :class="{ selected: currentQuestion.answer === 'true' }">
                  <up-icon v-if="currentQuestion.answer === 'true'" color="#fff" name="checkmark" size="14"></up-icon>
                </view>
              </view>
              <view class="judge-content">
                <up-icon color="#43e97b" name="checkmark-circle" size="20"></up-icon>
                <text class="judge-text">正确</text>
              </view>
            </view>
            <view
              class="judge-option"
              :class="{ selected: currentQuestion.answer === 'false' }"
              @click="selectJudge('false')"
            >
              <view class="judge-indicator">
                <view class="judge-circle" :class="{ selected: currentQuestion.answer === 'false' }">
                  <up-icon v-if="currentQuestion.answer === 'false'" color="#fff" name="checkmark" size="14"></up-icon>
                </view>
              </view>
              <view class="judge-content">
                <up-icon color="#f56c6c" name="close-circle" size="20"></up-icon>
                <text class="judge-text">错误</text>
              </view>
            </view>
          </view>

          <!-- 填空题 -->
          <view v-if="currentQuestion.type === 'fill'" class="fill-container">
            <view class="input-wrapper">
              <textarea
                v-model="currentQuestion.answer"
                class="fill-textarea"
                placeholder="请输入答案..."
                @input="onAnswerChange"
                auto-height
              />
            </view>
          </view>

          <!-- 简答题 -->
          <view v-if="currentQuestion.type === 'essay'" class="essay-container">
            <view class="input-wrapper">
              <textarea
                v-model="currentQuestion.answer"
                class="essay-textarea"
                placeholder="请输入您的答案，字数不少于50字..."
                @input="onAnswerChange"
                auto-height
              />
              <view class="word-count">
                <up-icon color="#8e8e93" name="edit" size="14"></up-icon>
                <text class="count-text">字数：{{ getWordCount(currentQuestion.answer || '') }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="navigation-buttons">
        <view
          class="nav-btn prev"
          :class="{ disabled: currentQuestionIndex === 0 }"
          @click="previousQuestion"
        >
          <up-icon color="#8e8e93" name="arrow-left" size="16"></up-icon>
        </view>
        <view
          class="nav-btn next"
          :class="{ disabled: currentQuestionIndex === questions.length - 1 }"
          @click="nextQuestion"
        >
          <up-icon color="#8e8e93" name="arrow-right" size="16"></up-icon>
        </view>
      </view>

      <view class="submit-button" @click="showSubmitDialog">
        <up-icon color="#fff" name="checkmark-circle" size="18"></up-icon>
        <text class="submit-text">交卷</text>
      </view>
      <view class="answer-sheet-btn" @click="showAnswerSheet = true">
        <up-icon color="#4f8cff" name="list" size="18"></up-icon>
      </view>
    </view>

    <!-- 交卷确认弹窗 -->
    <up-popup :show="showSubmitConfirm" @close="closeSubmitDialog" mode="center" round="16">
      <view class="submit-dialog">
        <view class="dialog-header">
          <view class="header-icon">
            <up-icon color="#fa709a" name="question-circle" size="24"></up-icon>
          </view>
          <text class="dialog-title">确认交卷</text>
          <view class="close-btn" @click="closeSubmitDialog">
            <up-icon color="#8e8e93" name="close" size="20"></up-icon>
          </view>
        </view>

        <view class="dialog-content">
          <view class="submit-stats">
            <view class="stats-grid">
              <view class="stat-item">
                <view class="stat-icon">
                  <up-icon color="#667eea" name="list" size="20"></up-icon>
                </view>
                <view class="stat-content">
                  <text class="stat-value">{{ questions.length }}</text>
                  <text class="stat-label">总题数</text>
                </view>
              </view>
              <view class="stat-item">
                <view class="stat-icon">
                  <up-icon color="#43e97b" name="checkmark-circle" size="20"></up-icon>
                </view>
                <view class="stat-content">
                  <text class="stat-value">{{ answeredCount }}</text>
                  <text class="stat-label">已答题</text>
                </view>
              </view>
              <view class="stat-item">
                <view class="stat-icon">
                  <up-icon color="#fa8c16" name="question-circle" size="20"></up-icon>
                </view>
                <view class="stat-content">
                  <text class="stat-value">{{ questions.length - answeredCount }}</text>
                  <text class="stat-label">未答题</text>
                </view>
              </view>
              <view class="stat-item">
                <view class="stat-icon">
                  <up-icon color="#f56c6c" name="clock" size="20"></up-icon>
                </view>
                <view class="stat-content">
                  <text class="stat-value">{{ formatTime(remainingTime) }}</text>
                  <text class="stat-label">剩余时间</text>
                </view>
              </view>
            </view>
          </view>

          <view class="warning-section">
            <view class="warning-icon">
              <up-icon color="#fa8c16" name="warning" size="20"></up-icon>
            </view>
            <text class="warning-text">提交后无法修改答案，请确认是否提交？</text>
          </view>
        </view>

        <view class="dialog-actions">
          <view class="action-btn secondary" @click="closeSubmitDialog">
            <text class="btn-text">再检查一下</text>
          </view>
          <view class="action-btn primary" @click="submitExam">
            <text class="btn-text" style="color: #fff2e8">确认交卷</text>
          </view>
        </view>
      </view>
    </up-popup>

    <!-- 时间到弹窗 -->
    <up-popup :show="showTimeUpDialog" mode="center" round="16" :closeable="false">
      <view class="time-up-dialog">
        <view class="time-up-content">
          <view class="time-up-icon">
            <up-icon color="#f56c6c" name="time" size="48"></up-icon>
          </view>
          <text class="time-up-title">考试时间到</text>
          <text class="time-up-message">系统将自动提交您的答案</text>
          <view class="countdown-display">
            <text class="countdown-text">{{ autoSubmitCountdown }}秒后自动提交</text>
          </view>
        </view>
      </view>
    </up-popup>
    <!-- 答题卡弹窗 -->
    <up-popup :show="showAnswerSheet" mode="bottom" round="16" @close="showAnswerSheet = false">
      <view class="answer-sheet-popup">
        <view class="popup-header">
          <text class="popup-title">答题卡</text>
          <up-icon color="#8e8e93" name="close" size="20" style="position:absolute;right:16rpx;top:16rpx;"
                   @click="showAnswerSheet = false"/>
        </view>
        <view class="answer-sheet-grid">
          <view
              v-for="(question, idx) in questions"
              :key="question.id"
              :class="{
              active: currentQuestionIndex === idx,
              answered: question.answered,
              marked: question.marked
            }"
              class="answer-sheet-item"
              @click="goToQuestion(idx); showAnswerSheet = false"
          >
            <text class="sheet-number">{{ idx + 1 }}</text>
            <up-icon v-if="question.marked" class="sheet-mark" color="#fa709a" name="star-fill" size="12"/>
          </view>
        </view>
        <view class="popup-actions">
          <view class="popup-action-btn" @click="showSubmitDialog">
            <up-icon color="#fff" name="checkmark-circle" size="18"></up-icon>
            <text class="submit-text">交卷</text>
          </view>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script>
import {getExamDetail, startExam, submitExam} from '@/api/exam'

export default {
  data() {
    return {
      // 考试信息
      examInfo: {
        id: '',
        title: '',
        duration: 0, // 秒
        totalScore: 0
      },
      // 考试状态
      questions: [],
      currentQuestionIndex: 0,
      remainingTime: 0, // 秒
      showSubmitConfirm: false,
      showTimeUpDialog: false,
      autoSubmitCountdown: 5,
      timer: null,
      autoSubmitTimer: null,
      showAnswerSheet: false,
      recordId: null,
    }
  },

  computed: {
    currentQuestion() {
      return this.questions[this.currentQuestionIndex] || {};
    },

    answeredCount() {
      return this.questions.filter(q => q.answered).length;
    },

    progressPercent() {
      return this.questions.length > 0 ? (this.answeredCount / this.questions.length) * 100 : 0;
    }
  },

  onLoad: async function (options) {
    if (options.id) {
      this.examInfo.id = options.id;
    }
    // 先调用startExam获取/恢复考试记录ID
    try {
      const res = await startExam(Number(this.examInfo.id));
      if (res && res.recordId) {
        this.recordId = res.recordId;
      }
    } catch (e) {
      uni.showToast({title: '无法开始考试', icon: 'none'});
      uni.navigateBack();
      return;
    }
    await this.loadExamDetail(this.examInfo.id);
    this.startTimer();
  },

  onUnload() {
    // 清理计时器
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.autoSubmitTimer) {
      clearInterval(this.autoSubmitTimer);
    }
  },

  onBackPress() {
    // 确认退出考试
    this.confirmExit();
    return true; // 阻止默认返回行为
  },

  methods: {
    // 加载考试详情并初始化题目
    async loadExamDetail(examId) {
      uni.showLoading({title: '加载中...'});
      try {
        const data = await getExamDetail(Number(examId));
        if (!data || !data.paper || !Array.isArray(data.paper.questions)) {
          uni.showToast({title: '考试信息有误', icon: 'none'});
          return;
        }
        // 初始化考试信息
        this.examInfo = {
          id: data.id,
          title: data.title,
          duration: data.duration * 60, // 若API单位为分钟，需转为秒
          totalScore: data.paper.totalScore
        };
        // 计算剩余时间
        let usedTime = 0;
        if (data.currentUserRecord && data.currentUserRecord.status === 1) {
          usedTime = data.currentUserRecord.duration || 0;
        }
        this.remainingTime = Math.max(this.examInfo.duration - usedTime, 0);
        // 题目适配
        this.questions = data.paper.questions.map((q, idx) => {
          const typeMap = {
            'single': 'single',
            'multiple': 'multiple',
            'judgment': 'judge',
            'judge': 'judge',
            'fill': 'fill',
            'essay': 'essay'
          };
          // 选项适配，支持JSON字符串和数组
          let opts = [];
          if (q.question.options) {
            try {
              const arr = typeof q.question.options === 'string' ? JSON.parse(q.question.options) : q.question.options;
              if (Array.isArray(arr)) {
                opts = arr.map(opt => ({
                  key: opt.id || String.fromCharCode(65 + arr.indexOf(opt)),
                  text: opt.content || opt
                }));
              }
            } catch (e) {
              // 兼容老格式
              opts = (q.question.options || []).map((opt, i) => ({
                key: String.fromCharCode(65 + i),
                text: opt
              }));
            }
          }
          return {
            id: q.question.id,
            type: typeMap[q.question.type] || q.question.type,
            question: q.question.title,
            options: opts,
            score: q.score,
            answered: false,
            marked: false,
            answer: (typeMap[q.question.type] === 'multiple' ? [] : '')
          };
        });

        console.log(this.questions)
      } catch (e) {
        uni.showToast({title: '加载失败', icon: 'none'});
      } finally {
        uni.hideLoading();
      }
    },

    // 计时器相关
    startTimer() {
      this.timer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--;
        } else {
          // 时间到，自动交卷
          this.timeUp();
        }
      }, 1000);
    },

    timeUp() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.showTimeUpDialog = true;

      // 开始倒计时自动提交
      this.autoSubmitCountdown = 5;
      this.autoSubmitTimer = setInterval(() => {
        this.autoSubmitCountdown--;
        if (this.autoSubmitCountdown <= 0) {
          this.submitExam();
        }
      }, 1000);
    },

    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;

      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },

    // 题目导航
    goToQuestion(index) {
      this.currentQuestionIndex = index;
    },

    previousQuestion() {
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--;
      }
    },

    nextQuestion() {
      if (this.currentQuestionIndex < this.questions.length - 1) {
        this.currentQuestionIndex++;
      }
    },

    // 答题功能
    selectOption(optionKey) {
      const question = this.currentQuestion;

      if (question.type === 'single') {
        question.answer = optionKey;
        question.answered = true;
      } else if (question.type === 'multiple') {
        if (!question.answer) {
          question.answer = [];
        }
        const answers = question.answer;
        const index = answers.indexOf(optionKey);
        if (index > -1) {
          answers.splice(index, 1);
        } else {
          answers.push(optionKey);
        }
        question.answered = answers.length > 0;
      }
    },

    selectJudge(value) {
      this.currentQuestion.answer = value;
      this.currentQuestion.answered = true;
    },

    onAnswerChange() {
      const question = this.currentQuestion;
      question.answered = !!(question.answer && question.answer.toString().trim());
    },

    isOptionSelected(optionKey) {
      const answer = this.currentQuestion.answer;
      if (this.currentQuestion.type === 'single') {
        return answer === optionKey;
      } else if (this.currentQuestion.type === 'multiple') {
        return Array.isArray(answer) && answer.includes(optionKey);
      }
      return false;
    },

    toggleMark() {
      this.currentQuestion.marked = !this.currentQuestion.marked;
    },

    getQuestionTypeName(type) {
      const typeMap = {
        single: '单选题',
        multiple: '多选题',
        judge: '判断题',
        fill: '填空题',
        essay: '简答题'
      };
      return typeMap[type] || '未知题型';
    },

    getWordCount(text) {
      return text.replace(/\s/g, '').length;
    },

    previewImage(current, urls) {
      uni.previewImage({
        current,
        urls
      });
    },

    // 交卷功能
    confirmExit() {
      uni.showModal({
        title: '确认退出',
        content: '退出后将丢失当前答题进度，是否确认退出？',
        success: (res) => {
          if (res.confirm) {
            if (this.timer) {
              clearInterval(this.timer);
            }
            if (this.autoSubmitTimer) {
              clearInterval(this.autoSubmitTimer);
            }
            uni.navigateBack();
          }
        }
      });
    },

    showSubmitDialog() {
      this.showSubmitConfirm = true;
    },

    closeSubmitDialog() {
      this.showSubmitConfirm = false;
    },

    async submitExam() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      if (this.autoSubmitTimer) {
        clearInterval(this.autoSubmitTimer);
      }
      // 收集答题数据
      const answers = this.questions.map(q => ({
        questionId: q.id,
        answer: Array.isArray(q.answer) ? q.answer.join(',') : q.answer,
        answered: q.answered
      }));
      uni.showLoading({title: '正在提交...'});
      try {
        await submitExam(this.recordId, answers);
        uni.hideLoading();
        uni.showToast({
          title: '提交成功',
          icon: 'success'
        });
        // 跳转到结果页面
        setTimeout(() => {
          uni.redirectTo({
            url: `/pages/exam/result?recordId=${this.recordId}`
          });
        }, 1500);
      } catch (e) {
        uni.hideLoading();
        uni.showToast({title: '提交失败', icon: 'none'});
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/exam/taking.scss";
// 答题卡弹窗样式
.answer-sheet-btn {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
  background: #f0f4ff;
  border-radius: 20rpx;
  padding: 0 20rpx;
  height: 64rpx;
  font-size: 28rpx;
  color: #4f8cff;
  cursor: pointer;
}

.answer-sheet-text {
  margin-left: 8rpx;
}

.answer-sheet-popup {
  padding: 32rpx 24rpx 24rpx 24rpx;
  background: #fff;
  border-radius: 16rpx 16rpx 0 0;
  min-height: 400rpx;
}

.popup-header {
  position: relative;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}

.popup-title {
  color: #222;
}

.answer-sheet-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: flex-start;
  margin-bottom: 32rpx;
}

.answer-sheet-item {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  cursor: pointer;
  border: 2rpx solid transparent;
}

.answer-sheet-item.answered {
  background: #e6f7ff;
  color: #1890ff;
}

.answer-sheet-item.marked {
  border-color: #fa709a;
}

.answer-sheet-item.active {
  border-color: #4f8cff;
  background: #d6e4ff;
}

.sheet-number {
  z-index: 1;
}

.sheet-mark {
  position: absolute;
  right: 2rpx;
  top: 2rpx;
  z-index: 2;
}

.popup-actions {
  display: flex;
  justify-content: center;
}

.popup-action-btn {
  display: flex;
  align-items: center;
  background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
  color: #fff;
  border-radius: 32rpx;
  padding: 0 40rpx;
  height: 64rpx;
  font-size: 30rpx;
  font-weight: bold;
  cursor: pointer;
}

.popup-action-btn .submit-text {
  margin-left: 8rpx;
}
</style>
