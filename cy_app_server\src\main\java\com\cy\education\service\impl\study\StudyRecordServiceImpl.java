package com.cy.education.service.impl.study;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.study.Course;
import com.cy.education.model.entity.study.StudyLog;
import com.cy.education.model.entity.study.StudyRecord;
import com.cy.education.model.params.PageParams;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.model.vo.StudyStatisticsVO;
import com.cy.education.repository.CourseMapper;
import com.cy.education.repository.StudyLogMapper;
import com.cy.education.repository.StudyRecordMapper;
import com.cy.education.service.study.StudyRecordService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cy.education.model.vo.CourseRecordVO;
import com.cy.education.model.vo.CourseRecordDetailVO;
import com.cy.education.model.vo.ChapterVO;
import com.cy.education.model.vo.LessonRecordVO;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习记录服务实现类
 */
@Service
public class StudyRecordServiceImpl extends ServiceImpl<StudyRecordMapper, StudyRecord> implements StudyRecordService {

    @Autowired
    private StudyLogMapper studyLogMapper;

    @Autowired
    private CourseMapper courseMapper;

    @Override
    public PageResponse<CourseRecordVO> getCourseRecordList(Integer userId, PageParams params) {
        // 查询用户所有学习记录，提取已学过的课程ID
        LambdaQueryWrapper<StudyRecord> recordQuery = new LambdaQueryWrapper<>();
        recordQuery.eq(StudyRecord::getUserId, userId);
        List<StudyRecord> allRecords = baseMapper.selectList(recordQuery);

        // 分页查询用户学过的课程
        Page<Course> page = new Page<>(params.getPageNum(), params.getPageSize());
        IPage<Course> coursePage = courseMapper.selectLearnedCourses(page, userId);
        List<CourseRecordVO> voList = new ArrayList<>();

        for (Course course : coursePage.getRecords()) {
            if (course == null) continue;
            // 该用户在本课程的所有学习记录
            List<StudyRecord> courseRecords = allRecords.stream()
                    .filter(r -> course.getId().equals(r.getCourseId() == null ? null : r.getCourseId()))
                    .filter(r -> r.getLessonId() != null)
                    .collect(Collectors.toList());
            // 计算课程学习统计
            CourseStudyStats stats = calculateCourseStudyStats(course, courseRecords, false);
            CourseRecordVO vo = new CourseRecordVO();
            vo.setCourseId(course.getId());
            vo.setCourseName(course.getName());
            vo.setCoverImageUrl(course.getCoverImageUrl());
            vo.setProgress((int) Math.round(stats.getWeightedProgress()));
            vo.setDuration(stats.getTotalDuration());
            vo.setCompleted((stats.getTotalLessons() > 0 && stats.getCompletedLessons() == stats.getTotalLessons()) ? 1 : 0);
            voList.add(vo);
        }

        return new PageResponse<>(voList, coursePage.getTotal(), coursePage.getCurrent(), coursePage.getSize());
    }

    @Override
    public CourseRecordDetailVO getCourseRecordDetail(Integer userId, Integer courseId) {
        Course course = courseMapper.selectById(courseId);
        if (course == null) {
            throw new RuntimeException("课程不存在");
        }

        LambdaQueryWrapper<StudyRecord> recordQuery = new LambdaQueryWrapper<>();
        recordQuery.eq(StudyRecord::getUserId, userId);
        recordQuery.eq(StudyRecord::getCourseId, courseId);
        List<StudyRecord> records = baseMapper.selectList(recordQuery);

        // 计算课程学习统计，并获取详细结构
        CourseStudyStats stats = calculateCourseStudyStats(course, records, true);
        CourseRecordDetailVO vo = new CourseRecordDetailVO();
        vo.setCourseId(course.getId());
        vo.setCourseName(course.getName());
        vo.setStructure(stats.getStructure());
        vo.setProgress((int) Math.round(stats.getWeightedProgress()));
        vo.setDuration(stats.getTotalDuration());
        vo.setCompleted((stats.getTotalLessons() > 0 && stats.getCompletedLessons() == stats.getTotalLessons()) ? 1 : 0);
        return vo;
    }

    /**
     * 计算课程学习统计信息
     *
     * @param course        课程信息
     * @param records       学习记录列表
     * @param includeDetail 是否包含详细结构信息
     * @return 课程学习统计结果
     */
    private CourseStudyStats calculateCourseStudyStats(Course course, List<StudyRecord> records, boolean includeDetail) {
        CourseStudyStats stats = new CourseStudyStats();

        // 将学习记录转换为Map，便于快速查找
        Map<Integer, StudyRecord> lessonRecordMap = records.stream()
                .filter(r -> r.getLessonId() != null)
                .collect(Collectors.toMap(
                        StudyRecord::getLessonId,
                        record -> record,
                        (existing, replacement) -> existing // 如果有重复，保留第一个
                ));
        stats.setLessonRecordMap(lessonRecordMap);

        // 解析课程结构
        JSONArray arr = JSONArray.parseArray(course.getStructure());

        if (arr == null) {
            return stats;
        }

        List<ChapterVO> structure = new ArrayList<>();
        int totalLessons = 0;
        int completedLessons = 0;
        int totalDuration = 0;
        double weightedProgressSum = 0.0;

        for (Object chapterObj : arr) {
            JSONObject chapterJson = (JSONObject) chapterObj;
            ChapterVO chapter = new ChapterVO();
            chapter.setId(chapterJson.getInteger("id"));
            chapter.setLabel(chapterJson.getString("label"));

            List<LessonRecordVO> lessons = new ArrayList<>();
            JSONArray children = chapterJson.getJSONArray("children");

            if (children != null) {
                for (Object lessonObj : children) {
                    JSONObject lessonJson = (JSONObject) lessonObj;
                    Integer lessonId = lessonJson.getInteger("id");

                    // 获取该课时的学习记录
                    StudyRecord record = lessonRecordMap.get(lessonId);

                    // 创建课时记录VO
                    LessonRecordVO lesson = new LessonRecordVO();
                    lesson.setId(lessonId);
                    lesson.setLabel(lessonJson.getString("label"));
                    lesson.setResourceId(lessonJson.getInteger("resourceId"));

                    // 设置学习进度信息
                    if (record != null) {
                        lesson.setProgress(record.getProgress() == null ? 0 : record.getProgress());
                        lesson.setDuration(record.getDuration() == null ? 0 : record.getDuration());
                        lesson.setCompleted(record.getCompleted() == null ? 0 : record.getCompleted());
                        lesson.setLastPosition(record.getLastPosition() == null ? 0 : record.getLastPosition());
                    } else {
                        lesson.setProgress(0);
                        lesson.setDuration(0);
                        lesson.setCompleted(0);
                        lesson.setLastPosition(0);
                    }

                    lessons.add(lesson);
                    totalLessons++;

                    // 统计学习数据
                    int progress = lesson.getProgress();
                    int duration = lesson.getDuration();
                    int completed = lesson.getCompleted();

                    // 累加学习时长（只计算实际学习的时长）
                    totalDuration += duration;

                    // 计算加权进度（考虑每个课时的权重）
                    weightedProgressSum += progress;

                    // 统计已完成课时
                    if (progress == 100 && completed == 1) {
                        completedLessons++;
                    }
                }
            }

            if (includeDetail) {
                chapter.setChildren(lessons);
                structure.add(chapter);
            }
        }

        // 设置统计结果
        stats.setTotalLessons(totalLessons);
        stats.setCompletedLessons(completedLessons);
        stats.setTotalDuration(totalDuration);
        stats.setWeightedProgress(totalLessons == 0 ? 0.0 : weightedProgressSum / totalLessons);

        if (includeDetail) {
            stats.setStructure(structure);
        }

        return stats;
    }

    /**
     * 记录学习日志
     *
     * @param studyRecord 学习记录
     */
    private void recordStudyLog(StudyRecord studyRecord) {
        StudyLog studyLog = new StudyLog();
        studyLog.setUserId(studyRecord.getUserId());
        studyLog.setCourseId(studyRecord.getCourseId());
        studyLog.setLessonId(studyRecord.getLessonId());
        studyLog.setDuration(studyRecord.getDuration() != null ? studyRecord.getDuration() : 0);
        studyLog.setProgress(studyRecord.getProgress());
        studyLog.setCompleted(studyRecord.getCompleted());
        studyLogMapper.insert(studyLog);
    }

    @Override
    public boolean logResourceAccess(Map<String, Object> accessLog) {
        try {
            // 创建学习日志
            StudyLog studyLog = new StudyLog();
            // 设置学员ID
            if (accessLog.containsKey("userId")) {
                studyLog.setUserId((Integer) accessLog.get("userId"));
            }
            // 设置课程ID
            if (accessLog.containsKey("courseId")) {
                studyLog.setCourseId((Integer) accessLog.get("courseId"));
            }
            // 设置课时ID
            if (accessLog.containsKey("lessonId")) {
                studyLog.setLessonId((Integer) accessLog.get("lessonId"));
            }
            // 设置资源ID
            if (accessLog.containsKey("resourceId")) {
                studyLog.setResourceId((Integer) accessLog.get("resourceId"));
            }
            // 设置资源类型
            if (accessLog.containsKey("resourceType")) {
                studyLog.setResourceType((String) accessLog.get("resourceType"));
            }
            // 设置学习时长
            if (accessLog.containsKey("duration")) {
                studyLog.setDuration((Integer) accessLog.get("duration"));
            }
            // 设置学习进度
            if (accessLog.containsKey("progress")) {
                studyLog.setProgress((Integer) accessLog.get("progress"));
            }
            // 设置是否完成
            if (accessLog.containsKey("completed")) {
                studyLog.setCompleted((Integer) accessLog.get("completed"));
            }
            // 设置学习时间
            studyLog.setStudyTime(LocalDateTime.now());
            // 保存学习日志
            studyLogMapper.insert(studyLog);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    @Transactional
    public StudyRecord saveStudyRecord(StudyRecord studyRecord) {
        // 查询是否存在记录
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getUserId, studyRecord.getUserId());
        if (studyRecord.getLessonId() != null) {
            // 课时学习记录
            queryWrapper.eq(StudyRecord::getLessonId, studyRecord.getLessonId());
        } else {
            // 课程学习记录
            queryWrapper.eq(StudyRecord::getCourseId, studyRecord.getCourseId());
            queryWrapper.isNull(StudyRecord::getLessonId);
        }
        StudyRecord existingRecord = getOne(queryWrapper);
        if (existingRecord != null) {
            // 更新现有记录
            studyRecord.setId(existingRecord.getId());
            // 累加学习时长
            if (studyRecord.getDuration() != null) {
                studyRecord.setDuration(existingRecord.getDuration() + studyRecord.getDuration());
            }
            updateById(studyRecord);
            // 记录学习日志
//            recordStudyLog(studyRecord);
            return studyRecord;
        } else {
            // 创建新记录
            save(studyRecord);
            // 记录学习日志
//            recordStudyLog(studyRecord);
            return studyRecord;
        }
    }

    /**
     * 课程学习统计结果
     */
    @Data
    private static class CourseStudyStats {
        private int totalLessons;
        private int completedLessons;
        private int totalDuration;
        private int totalProgress;
        private double weightedProgress;
        private List<ChapterVO> structure;
        private Map<Integer, StudyRecord> lessonRecordMap;
    }

    @Override
    public StudyStatisticsVO getStudyStatistics(Integer studentId, Integer courseId) {
        StudyStatisticsVO statistics = new StudyStatisticsVO();

        // 查询条件
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getUserId, studentId);

        if (courseId != null) {
            queryWrapper.eq(StudyRecord::getCourseId, courseId);
        }

        // 查询学习记录
        List<StudyRecord> records = list(queryWrapper);

        if (records.isEmpty()) {
            // 没有学习记录
            statistics.setTotalStudyTime(0);
            statistics.setStudyCount(0);
            statistics.setCompletedLessons(new ArrayList<>());
            statistics.setStudyTrend(new ArrayList<>());
            return statistics;
        }

        // 计算总学习时长
        int totalTime = records.stream().mapToInt(StudyRecord::getDuration).sum();
        statistics.setTotalStudyTime(totalTime);

        // 获取最后学习时间
        LocalDateTime lastStudyTime = records.stream()
                .map(StudyRecord::getLastStudyTime)
                .filter(Objects::nonNull)
                .max(LocalDateTime::compareTo)
                .orElse(null);
        statistics.setLastStudyTime(lastStudyTime);

        // 获取已完成课时列表
        List<Integer> completedLessons = records.stream()
                .filter(r -> r.getLessonId() != null && r.getCompleted() != null && r.getCompleted() == 1)
                .map(StudyRecord::getLessonId)
                .collect(Collectors.toList());
        statistics.setCompletedLessons(completedLessons);

        // 统计学习次数
        statistics.setStudyCount(records.size());

        // 获取学习趋势数据
        List<Map<String, Object>> studyTrend = studyLogMapper.getStudyTimeTrend(studentId, 30);
        statistics.setStudyTrend(studyTrend);

        return statistics;
    }

}
