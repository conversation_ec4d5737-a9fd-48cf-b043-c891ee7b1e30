/**
 * 论坛模块状态转换工具类型定义
 */

export interface StatusInfo {
  value: number;
  code: string;
  text: string;
  type: string;
}

export interface StatusOption {
  value: number;
  label: string;
}

// 帖子状态映射
export const postStatusMap: Record<number, string>;
export const commentStatusMap: Record<number, string>;
export const violationStatusMap: Record<number, string>;
export const processTypeMap: Record<number, string>;

// 状态文本映射
export const postStatusTextMap: Record<number, string>;
export const commentStatusTextMap: Record<number, string>;
export const violationStatusTextMap: Record<number, string>;
export const processTypeTextMap: Record<number, string>;

// 状态样式映射
export const postStatusTypeMap: Record<number, string>;
export const commentStatusTypeMap: Record<number, string>;
export const violationStatusTypeMap: Record<number, string>;
export const processTypeStyleMap: Record<number, string>;

// 状态信息获取方法
export function getPostStatusInfo(status: number | string): StatusInfo;
export function getCommentStatusInfo(status: number | string): StatusInfo;
export function getViolationStatusInfo(status: number | string): StatusInfo;
export function getProcessTypeInfo(type: number | string): StatusInfo;

// 选项列表
export const postStatusOptions: StatusOption[];
export const commentStatusOptions: StatusOption[];
export const violationStatusOptions: StatusOption[]; 