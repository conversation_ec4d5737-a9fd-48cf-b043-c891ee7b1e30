<template>
  <view class="home-container">
    <!-- 顶部渐变背景区域 -->
    <view class="header-section">
      <!-- 用户信息区域 -->
      <view class="user-info">
        <view class="left-content">
          <view class="avatar-container">
            <up-image
                :src="userInfo.avatar"
                width="50"
                height="50"
                shape="circle"
                class="avatar"
                @click="goToProfile"
            ></up-image>
          </view>
          <view class="user-text">
            <text class="greeting">{{ getGreeting() }}</text>
            <text class="name">{{ userInfo.name }}</text>
          </view>
        </view>
        <view class="right-content">
          <view class="notification-btn" @click="goToNotifications">
            <up-icon name="bell" color="#fff" size="22"></up-icon>
            <view v-if="unreadCount > 0" class="notification-badge">
              <text class="badge-text">{{ unreadCount > 99 ? '99+' : unreadCount }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="content-container">
      <!-- 轮播图区域 -->
      <view class="carousel-section" v-if="carouselList.length > 0">
        <swiper
            class="carousel-swiper"
            autoplay
            circular
            interval="4000"
            duration="500"
            indicator-dots
            indicator-color="rgba(255,255,255,0.4)"
            indicator-active-color="#fff"
        >
          <swiper-item v-for="(item, index) in carouselList" :key="index">
            <view class="carousel-item" @click="handleCarouselClick(item)">
              <up-image
                  :src="item.imageUrl"
                  width="100%"
                  height="180"
                  mode="aspectFill"
                  radius="16"
                  class="carousel-image"
                  @click="handleCarouselClick(item)"
              ></up-image>
              <view class="carousel-overlay" v-if="item.title">
                <text class="carousel-title">{{ item.title }}</text>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 快捷功能区域 -->
      <view class="function-section">
        <view class="section-header">
          <text class="section-title">快捷功能</text>
        </view>
        <view class="function-grid">
          <view
              v-for="item in functionList"
              :key="item.name"
              class="function-item"
              @click="handleFunctionClick(item)"
          >
            <view class="function-icon-container">
              <view class="function-icon" :style="{ background: item.gradient }">
                <up-icon :name="item.icon" color="#fff" size="26"></up-icon>
              </view>
              <view v-if="item.badge" class="function-badge">
                <text class="badge-text">{{ item.badge }}</text>
              </view>
            </view>
            <text class="function-name">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <!-- 通知公告 -->
      <view class="notice-section">
        <view class="section-header">
          <text class="section-title">通知公告</text>
          <view class="more-btn" @click="goToNoticeList">
            <text class="more-text">更多</text>
            <up-icon name="arrow-right" size="12" color="#999"></up-icon>
          </view>
        </view>
        <view class="notice-list">
          <view
              v-for="notice in noticeList"
              :key="notice.id"
              class="notice-item"
              @click="goToNoticeDetail(notice)"
          >
            <view class="notice-icon">
              <up-icon name="volume" color="#667eea" size="16"></up-icon>
            </view>
            <view class="notice-content">
              <text class="notice-title">{{ notice.title }}</text>
              <text class="notice-time">{{ formatTime(notice.publishTime) }}</text>
            </view>
            <up-icon name="arrow-right" size="12" color="#c0c4cc"></up-icon>
          </view>
        </view>
      </view>

      <!-- 最新动态 -->
      <view class="news-section">
        <view class="section-header">
          <text class="section-title">最新动态</text>
          <view class="more-btn" @click="goToNewsList">
            <text class="more-text">更多</text>
            <up-icon name="arrow-right" size="12" color="#999"></up-icon>
          </view>
        </view>
        <view class="news-list">
          <view
              v-for="news in newsList"
              :key="news.id"
              class="news-item"
              @click="goToNewsDetail(news)"
          >
            <view class="news-cover">
              <up-image
                  :src="news.coverUrl"
                  width="80"
                  height="60"
                  mode="aspectFill"
                  radius="8"
                  class="news-image"
              ></up-image>
            </view>
            <view class="news-content">
              <text class="news-title">{{ news.title }}</text>
              <view class="news-meta">
                <text class="news-time">{{ formatTime(news.publishTime) }}</text>
                <text class="news-views">{{ news.viewCount || Math.floor(Math.random() * 1000) }}次浏览</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <custom-tabbar current="0"></custom-tabbar>
  </view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar/index.vue'
import {getCarouselList, getNewsList, getNoticeList} from '@/api/content'
import {formatTime} from "@/utils/timeUtil"

export default {
  components: {
    CustomTabbar
  },
  data() {
    return {
      unreadCount: 3, // 未读通知数量
      userInfo: {
        name: '',
        avatar: ''
      },
      carouselList: [],
      newsList: [],
      noticeList: [],
      functionList: [
        {
          name: '继续学习',
          icon: 'play-circle',
          gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          path: '/pages/study/index'
        },
        {
          name: '我的考试',
          icon: 'file-text',
          gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          path: '/pages/exam/index',
          // badge: '2'
        },
        {
          name: '积分商城',
          icon: 'gift',
          gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
          path: '/pages/points/mall'
        }
      ]
    }
  },
  async onLoad() {
    const localUserInfo = uni.getStorageSync('userInfo');
    if (localUserInfo) {
      this.userInfo = localUserInfo;
    } else {
      // 导入auth工具函数
      const { checkAuth, redirectToLogin } = await import('@/utils/auth');
      const isLoggedIn = await checkAuth();
      if (!isLoggedIn) {
        uni.showToast({
          title: '请先登录',
          icon: 'none',
          duration: 2000
        });
        redirectToLogin();
        return;
      }
    }
    this.fetchHomeData()
  },
  onShow() {
  },
  methods: {
    formatTime,
    async fetchHomeData() {
      // 轮播图
      const carouselRes = await getCarouselList({pageNum: 1, pageSize: 5})
      this.carouselList = carouselRes.list
      // 新闻
      const newsRes = await getNewsList({pageNum: 1, pageSize: 3})
      this.newsList = newsRes.list
      // 公告
      const noticeRes = await getNoticeList({pageNum: 1, pageSize: 3})
      this.noticeList = noticeRes.list
    },
    // 获取问候语
    getGreeting() {
      const hour = new Date().getHours();
      if (hour < 6) return '夜深了';
      if (hour < 9) return '早上好';
      if (hour < 12) return '上午好';
      if (hour < 14) return '中午好';
      if (hour < 18) return '下午好';
      if (hour < 22) return '晚上好';
      return '夜深了';
    },
    goToProfile() {
      uni.navigateTo({url: '/pages/profile/index'});
    },
    goToNotifications() {
      uni.navigateTo({url: '/pages/profile/messages'});
    },
    goToNewsDetail(news) {
      uni.navigateTo({url: `/pages/home/<USER>
    },
    goToNoticeList() {
      uni.navigateTo({url: '/pages/home/<USER>'});
    },
    goToNoticeDetail(notice) {
      uni.navigateTo({url: `/pages/home/<USER>
    },
    goToNewsList() {
      uni.navigateTo({url: '/pages/home/<USER>'});
    },
    handleFunctionClick(item) {
      if (item.path) {
        uni.navigateTo({
          url: item.path
        });
      }
    },
    handleCarouselClick(item) {
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/pages/home/<USER>';
</style>
