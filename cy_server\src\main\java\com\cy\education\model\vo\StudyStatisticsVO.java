package com.cy.education.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 学习统计VO
 */
@Data
public class StudyStatisticsVO {
    
    /**
     * 总学习时长(分钟)
     */
    private Integer totalStudyTime;
    
    /**
     * 最后学习时间
     */
    private LocalDateTime lastStudyTime;
    
    /**
     * 已完成课时列表
     */
    private List<Integer> completedLessons;
    
    /**
     * 学习次数
     */
    private Integer studyCount;
    
    /**
     * 学习趋势数据
     */
    private List<Map<String, Object>> studyTrend;
} 