package com.cy.education.service.impl.exam;

import com.cy.education.exception.NotFoundException;
import com.cy.education.model.entity.exam.ExamPaper;
import com.cy.education.model.vo.ExamPaperQuestionVO;
import com.cy.education.model.vo.ExamPaperVO;
import com.cy.education.repository.ExamPaperMapper;
import com.cy.education.repository.ExamPaperQuestionMapper;
import com.cy.education.service.exam.ExamPaperService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 试卷服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExamPaperServiceImpl implements ExamPaperService {

    private final ExamPaperMapper examPaperMapper;
    private final ExamPaperQuestionMapper examPaperQuestionMapper;

    @Override
    public ExamPaperVO getPaperDetailWithAnswer(Integer id) {
        ExamPaper paper = examPaperMapper.selectById(id);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        ExamPaperVO vo = new ExamPaperVO();
        BeanUtils.copyProperties(paper, vo);
        List<ExamPaperQuestionVO> questions = examPaperQuestionMapper.selectQuestionsByPaperId(id);
        vo.setQuestions(questions);
        vo.setQuestionCount(questions != null ? questions.size() : 0);
        return vo;
    }

    @Override
    public ExamPaperVO getPaperDetailWithoutAnswer(Integer id) {
        ExamPaper paper = examPaperMapper.selectById(id);
        if (paper == null) {
            throw new NotFoundException("试卷不存在");
        }
        ExamPaperVO vo = new ExamPaperVO();
        BeanUtils.copyProperties(paper, vo);
        List<ExamPaperQuestionVO> questions = examPaperQuestionMapper.selectQuestionsByPaperId(id);
        // 移除每道题的正确答案
        if (questions != null) {
            for (ExamPaperQuestionVO q : questions) {
                if (q.getQuestion() != null) {
                    q.getQuestion().setCorrectAnswer(null);
                    q.getQuestion().setExplanation(null);
                }
            }
        }
        vo.setQuestions(questions);
        vo.setQuestionCount(questions != null ? questions.size() : 0);
        return vo;
    }
}
