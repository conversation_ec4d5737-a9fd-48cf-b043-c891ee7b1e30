<template>
  <el-dialog
    v-model="visible"
    title="导出试卷"
    width="500px"
    destroy-on-close
    @closed="handleClosed"
  >
    <div class="export-container">
      <el-form :model="exportForm" label-width="120px">
        <!-- 导出格式 -->
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="docx">Word文档 (.docx)</el-radio>
            <el-radio label="pdf">PDF文档 (.pdf)</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 答案选项 -->
        <el-form-item label="答案设置">
          <el-checkbox v-model="exportForm.includeAnswers">包含答案</el-checkbox>
        </el-form-item>

        <!-- 解析选项 -->
        <el-form-item label="解析设置">
          <el-checkbox 
            v-model="exportForm.includeExplanations"
            :disabled="!exportForm.includeAnswers"
          >
            包含解析
          </el-checkbox>
        </el-form-item>

        <!-- 答案位置 -->
        <el-form-item label="答案位置" v-if="exportForm.includeAnswers">
          <el-radio-group v-model="exportForm.answerPosition">
            <el-radio label="inline">题目内显示</el-radio>
            <el-radio label="appendix">附录中显示</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 页面设置 -->
        <el-form-item label="页面设置">
          <el-checkbox v-model="exportForm.showHeader">显示页眉</el-checkbox>
          <el-checkbox v-model="exportForm.showFooter">显示页脚</el-checkbox>
          <el-checkbox v-model="exportForm.showPageNumber">显示页码</el-checkbox>
        </el-form-item>

        <!-- 字体设置 -->
        <el-form-item label="字体大小">
          <el-select v-model="exportForm.fontSize" style="width: 120px">
            <el-option label="小四号" value="12" />
            <el-option label="四号" value="14" />
            <el-option label="小三号" value="15" />
            <el-option label="三号" value="16" />
          </el-select>
        </el-form-item>

        <!-- 试卷信息 -->
        <el-form-item label="试卷信息">
          <div class="paper-info">
            <p><strong>试卷名称：</strong>{{ paperInfo.title }}</p>
            <p><strong>题目数量：</strong>{{ paperInfo.questionCount }} 题</p>
            <p><strong>总分：</strong>{{ paperInfo.totalScore }} 分</p>
          </div>
        </el-form-item>

        <!-- 导出说明 -->
        <el-form-item>
          <el-alert
            title="导出说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. Word格式支持更好的编辑和格式调整</p>
              <p>2. PDF格式适合直接打印和分发</p>
              <p>3. 答案和解析可选择在题目内显示或附录中显示</p>
            </template>
          </el-alert>
        </el-form-item>
      </el-form>

      <!-- 导出进度 -->
      <div v-if="exporting" class="export-progress">
        <el-progress :percentage="exportProgress" />
        <div class="progress-text">正在生成试卷文档...</div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="startExport" 
          :loading="exporting"
        >
          开始导出
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { exportExamPaper } from '@/api/importExport'

interface PaperInfo {
  id: number
  title: string
  questionCount: number
  totalScore: number
}

interface Props {
  modelValue: boolean
  paperInfo: PaperInfo
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const exporting = ref(false)
const exportProgress = ref(0)

const exportForm = ref({
  format: 'docx',
  includeAnswers: true,
  includeExplanations: true,
  answerPosition: 'appendix',
  showHeader: true,
  showFooter: true,
  showPageNumber: true,
  fontSize: '14'
})

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const startExport = async () => {
  exporting.value = true
  exportProgress.value = 0

  try {
    const options = {
      includeAnswers: exportForm.value.includeAnswers,
      includeExplanations: exportForm.value.includeExplanations,
      answerPosition: exportForm.value.answerPosition as 'inline' | 'appendix',
      format: exportForm.value.format as 'docx' | 'pdf',
      showHeader: exportForm.value.showHeader,
      showFooter: exportForm.value.showFooter,
      showPageNumber: exportForm.value.showPageNumber,
      fontSize: parseInt(exportForm.value.fontSize)
    }

    // 模拟进度
    const progressInterval = setInterval(() => {
      if (exportProgress.value < 90) {
        exportProgress.value += 10
      }
    }, 200)

    const response = await exportExamPaper(props.paperInfo.id, options)
    
    clearInterval(progressInterval)
    exportProgress.value = 100

    // 处理文件下载
    const blob = new Blob([response], { 
      type: options.format === 'docx' 
        ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        : 'application/pdf'
    })
    
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    const extension = options.format
    const answerSuffix = options.includeAnswers 
      ? (options.includeExplanations ? '_含答案解析' : '_含答案') 
      : ''
    
    link.download = `${props.paperInfo.title}${answerSuffix}.${extension}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('试卷导出成功')
    emit('success')
    visible.value = false
    
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const handleClosed = () => {
  exporting.value = false
  exportProgress.value = 0
  exportForm.value = {
    format: 'docx',
    includeAnswers: true,
    includeExplanations: true,
    answerPosition: 'appendix',
    showHeader: true,
    showFooter: true,
    showPageNumber: true,
    fontSize: '14'
  }
}
</script>

<style scoped>
.export-container {
  padding: 20px 0;
}

.paper-info {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.paper-info p {
  margin: 4px 0;
  color: #606266;
}

.export-progress {
  margin-top: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
