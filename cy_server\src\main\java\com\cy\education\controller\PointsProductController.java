package com.cy.education.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.PointsProduct;
import com.cy.education.model.params.PointsProductQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.PointsProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 积分商品控制器
 */
@Api(tags = "积分商品")
@RestController
@RequestMapping("/product")
public class PointsProductController {

    @Autowired
    private PointsProductService pointsProductService;
    
    /**
     * 获取积分商品列表
     */
    @ApiOperation("获取积分商品列表")
    @GetMapping("/list")
    public ApiResponse<PageResponse<PointsProduct>> getProductList(PointsProductQueryParam param) {
        IPage<PointsProduct> page = pointsProductService.page(param);
        return ApiResponse.success(PageResponse.from(page));
    }

    /**
     * 获取积分商品详情
     */
    @ApiOperation("获取积分商品详情")
    @GetMapping("/{id}")
    public ApiResponse<PointsProduct> getProductDetail(@PathVariable Integer id) {
        PointsProduct product = pointsProductService.getById(id);
        return ApiResponse.success(product);
    }

    /**
     * 创建积分商品
     */
    @ApiOperation("创建积分商品")
    @PostMapping("")
    public ApiResponse<Map<String, Object>> createProduct(@RequestBody @Valid PointsProduct product) {
        return pointsProductService.create(product);
    }

    /**
     * 更新积分商品
     */
    @ApiOperation("更新积分商品")
    @PutMapping("/{id}")
    public ApiResponse<Map<String, Object>> updateProduct(@PathVariable Integer id, @RequestBody PointsProduct product) {
        return pointsProductService.update(id, product);
    }

    /**
     * 删除积分商品
     */
    @ApiOperation("删除积分商品")
    @DeleteMapping("/{id}")
    public ApiResponse<Map<String, Object>> deleteProduct(@PathVariable Integer id) {
        return pointsProductService.delete(id);
    }
    
    /**
     * 更新商品状态
     */
    @ApiOperation("更新商品状态")
    @PutMapping("/{id}/status")
    public ApiResponse<Map<String, Object>> updateProductStatus(@PathVariable Integer id, @RequestBody Map<String, String> param) {
        String status = param.get("status");
        return pointsProductService.updateStatus(id, status);
    }

    /**
     * 更新商品状态（POST兼容接口）
     */
    @ApiOperation("更新商品状态（POST兼容接口）")
    @PostMapping("/{id}/status")
    public ApiResponse<Map<String, Object>> updateProductStatusPost(@PathVariable Integer id, @RequestBody Map<String, String> param) {
        String status = param.get("status");
        return pointsProductService.updateStatus(id, status);
    }

    /**
     * 补充商品库存
     */
    @ApiOperation("补充商品库存")
    @PutMapping("/{id}/stock")
    public ApiResponse<Map<String, Object>> restockProduct(@PathVariable Integer id, @RequestBody Map<String, Integer> param) {
        Integer count = param.get("count");
        if (count == null) {
            // 兼容前端直接传数字的情况
            return ApiResponse.error("缺少count参数");
        }
        return pointsProductService.restock(id, count);
    }

    /**
     * 获取积分商品统计数据
     */
    @ApiOperation("获取积分商品统计数据")
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getProductStatistics() {
        Map<String, Object> statistics = pointsProductService.getStatistics();
        return ApiResponse.success(statistics);
    }
} 