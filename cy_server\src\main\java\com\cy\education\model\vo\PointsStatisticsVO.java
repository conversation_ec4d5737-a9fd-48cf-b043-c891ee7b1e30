package com.cy.education.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 积分统计VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointsStatisticsVO {
    
    /**
     * 总用户数
     */
    private Integer totalUsers;
    
    /**
     * 总积分
     */
    private Integer totalPoints;
    
    /**
     * 平均积分
     */
    private Double averagePoints;
    
    /**
     * 月度积分变化
     */
    private List<Map<String, Object>> monthlyPointsChange;
    
    /**
     * 积分排名前10的用户
     */
    private List<Map<String, Object>> topUsers;
    
    /**
     * 积分分布
     */
    private List<Map<String, Object>> pointsDistribution;
} 