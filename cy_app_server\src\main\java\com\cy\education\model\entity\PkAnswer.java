package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PK答题记录实体
 */
@Data
@TableName("pk_answer")
public class PkAnswer {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 房间ID
     */
    private Long roomId;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 题目ID
     */
    private Integer questionId;
    
    /**
     * 题目顺序
     */
    private Integer questionOrder;
    
    /**
     * 用户答案
     */
    private String userAnswer;
    
    /**
     * 是否正确: 0-错误, 1-正确
     */
    private Boolean isCorrect;
    
    /**
     * 答题时间(毫秒)
     */
    private Integer answerTime;
    
    /**
     * 得分
     */
    private Integer score;
    
    /**
     * 答题时间
     */
    private LocalDateTime answeredAt;
}
