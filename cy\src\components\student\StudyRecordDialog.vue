<template>
  <el-dialog v-model="visible" title="学习记录" width="1000px" destroy-on-close @close="handleClose">
    <div class="dialog-header">
      <span>学员：{{ student?.name }}</span>
      <el-select 
        v-model="courseFilter" 
        placeholder="课程筛选" 
        style="width: 200px; margin-left: 20px;" 
        @change="loadData"
      >
        <el-option label="全部课程" value="" />
        <el-option 
          v-for="course in courseOptions" 
          :key="course.id" 
          :label="course.name" 
          :value="course.id" 
        />
      </el-select>
    </div>
    
    <el-table :data="records" v-loading="loading" max-height="400">
      <el-table-column prop="courseName" label="课程名称" min-width="150" />
      <el-table-column prop="lessonName" label="课时名称" min-width="150" />
      <el-table-column prop="progress" label="学习进度" width="120">
        <template #default="{ row }">
          <el-progress :percentage="row.progress" :stroke-width="8" />
        </template>
      </el-table-column>
      <el-table-column prop="studyTime" label="学习时长" width="100">
        <template #default="{ row }">
          {{ formatDuration(row.studyTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="lastStudyTime" label="最后学习" width="150" />
      <el-table-column prop="status" label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status === 'completed' ? 'success' : 'info'">
            {{ row.status === 'completed' ? '已完成' : '学习中' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="dialog-pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadData"
        @current-change="loadData"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { getCourseList } from '@/api/course'
import { getUserStudyRecords, type StudyRecordVO } from '@/api/study'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  student: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const records = ref<StudyRecordVO[]>([])
const courseFilter = ref('')
const courseOptions = ref<any[]>([])

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 监听props变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.student) {
    loadCourseOptions()
    loadData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadCourseOptions = async () => {
  try {
    const response = await getCourseList({ page: 1, limit: 100 })
    courseOptions.value = response.list || []
  } catch (error) {
    console.error('加载课程选项失败:', error)
  }
}

const loadData = async () => {
  if (!props.student) return

  loading.value = true
  try {
    const response = await getUserStudyRecords(
      props.student.id,
      courseFilter.value ? Number(courseFilter.value) : undefined
    )

    // 转换数据格式以匹配表格显示
    const transformedRecords = response.map(record => ({
      ...record,
      progress: record.progress || 0,
      studyTime: record.duration || 0,
      status: record.completed ? 'completed' : 'in_progress'
    }))

    records.value = transformedRecords
    // 注意：getUserStudyRecords 返回的是数组，不是分页对象
    // 如果需要分页，需要使用其他API或在前端处理
    pagination.total = transformedRecords.length
  } catch (error) {
    console.error('加载学习记录失败:', error)
    ElMessage.error('加载学习记录失败')
  } finally {
    loading.value = false
  }
}

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  if (hours > 0) {
    return `${hours}h${minutes}m`
  }
  return `${minutes}m`
}

const handleClose = () => {
  visible.value = false
  records.value = []
  courseFilter.value = ''
  pagination.page = 1
}
</script>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  font-weight: 600;
}

.dialog-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}
</style>
