package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.study.Course;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CourseMapper extends BaseMapper<Course> {
    /**
     * 分页查询用户学过的课程
     * @param page 分页对象
     * @param userId 用户ID
     * @return 课程列表
     */
    @Select("SELECT DISTINCT c.* FROM courses c " +
            "JOIN study_records r ON c.id = r.course_id " +
            "WHERE r.user_id = #{userId}")
    IPage<Course> selectLearnedCourses(Page<Course> page, @Param("userId") Integer userId);
}
