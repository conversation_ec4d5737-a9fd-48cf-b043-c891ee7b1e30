package com.cy.education.model.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页响应对象
 *
 * @param <T> 数据类型
 */
@Data
@ApiModel("分页响应")
public class PageResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 数据列表
     */
    @ApiModelProperty("数据列表")
    private List<T> list;

    /**
     * 总记录数
     */
    @ApiModelProperty("总记录数")
    private long total;

    /**
     * 当前页码
     */
    private long page;

    /**
     * 每页记录数
     */
    private long size;

    /**
     * 总页数
     */
    private long totalPages;

    /**
     * 默认构造函数
     */
    public PageResponse() {
    }

    /**
     * 构造函数
     *
     * @param list  数据列表
     * @param total 总记录数
     */
    public PageResponse(List<T> list, long total) {
        this.list = list;
        this.total = total;
    }

    /**
     * 构造函数
     *
     * @param list  数据列表
     * @param total 总记录数
     * @param page  当前页码
     * @param size  每页大小
     */
    public PageResponse(List<T> list, long total, long page, long size) {
        this.list = list;
        this.total = total;
        this.page = page;
        this.size = size;
        this.totalPages = (total + size - 1) / size;
    }

    /**
     * 将分页数据封装为分页响应对象
     */
    public static <T> PageResponse<T> of(List<T> list, long total, long page, long size) {
        return new PageResponse<>(list, total, page, size);
    }

    /**
     * 从IPage对象创建PageResponse
     *
     * @param page IPage对象
     * @param <T> 数据类型
     * @return PageResponse对象
     */
    public static <T> PageResponse<T> from(IPage<T> page) {
        PageResponse<T> response = new PageResponse<>();
        response.setPage(page.getCurrent());
        response.setSize(page.getSize());
        response.setTotal(page.getTotal());
        response.setTotalPages(page.getPages());
        response.setList(page.getRecords());
        return response;
    }
}
