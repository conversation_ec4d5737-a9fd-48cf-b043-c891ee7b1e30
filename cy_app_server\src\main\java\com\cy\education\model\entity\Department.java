package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门实体类
 */
@Data
@TableName("departments")
public class Department implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 父部门ID
     */
    private Integer parentId;

    /**
     * 部门负责人
     */
    private String leader;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态：0禁用，1启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建时间（前端显示用）
     */
    @TableField(exist = false)
    private String createTime;

    /**
     * 子部门列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<Department> children;

    /**
     * 部门学员数量（非数据库字段）
     */
    @TableField(exist = false)
    private Integer studentCount;
} 