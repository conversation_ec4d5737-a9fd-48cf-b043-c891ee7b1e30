package com.cy.education.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.PointsExchange;
import com.cy.education.model.entity.PointsRecord;
import com.cy.education.model.entity.PointsRule;
import com.cy.education.model.params.PointsAdjustParam;
import com.cy.education.model.params.PointsExchangeQueryParam;
import com.cy.education.model.params.PointsQueryParam;
import com.cy.education.model.params.PointsRuleQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.model.vo.PointsStatisticsVO;
import com.cy.education.service.PointsExchangeService;
import com.cy.education.service.PointsRecordService;
import com.cy.education.service.PointsRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 积分管理控制器
 */
@Api(tags = "积分管理")
@RestController
@RequestMapping("/points")
public class PointsController {

    @Autowired
    private PointsRuleService pointsRuleService;
    
    @Autowired
    private PointsRecordService pointsRecordService;
    
    @Autowired
    private PointsExchangeService pointsExchangeService;
    
    /**
     * 获取积分规则列表
     */
    @ApiOperation("获取积分规则列表")
    @GetMapping("/rules")
    public ApiResponse<PageResponse<PointsRule>> getRuleList(PointsRuleQueryParam param) {
        IPage<PointsRule> page = pointsRuleService.page(param);
        return ApiResponse.success(PageResponse.from(page));
    }

    /**
     * 获取积分规则详情
     */
    @ApiOperation("获取积分规则详情")
    @GetMapping("/rules/{id}")
    public ApiResponse<PointsRule> getRuleDetail(@PathVariable Integer id) {
        PointsRule rule = pointsRuleService.getById(id);
        return ApiResponse.success(rule);
    }

    /**
     * 创建积分规则
     */
    @ApiOperation("创建积分规则")
    @PostMapping("/rules")
    public ApiResponse<Map<String, Object>> createRule(@RequestBody @Valid PointsRule rule) {
        return pointsRuleService.create(rule);
    }

    /**
     * 更新积分规则
     */
    @ApiOperation("更新积分规则")
    @PutMapping("/rules/{id}")
    public ApiResponse<Map<String, Object>> updateRule(@PathVariable Integer id, @RequestBody PointsRule rule) {
        return pointsRuleService.update(id, rule);
    }

    /**
     * 删除积分规则
     */
    @ApiOperation("删除积分规则")
    @DeleteMapping("/rules/{id}")
    public ApiResponse<Map<String, Object>> deleteRule(@PathVariable Integer id) {
        return pointsRuleService.delete(id);
    }
    
    /**
     * 批量删除积分规则
     */
    @ApiOperation("批量删除积分规则")
    @PostMapping("/rules/batch-delete")
    public ApiResponse<Map<String, Object>> batchDeleteRules(@RequestBody List<Integer> ids) {
        return pointsRuleService.batchDelete(ids);
    }

    /**
     * 切换积分规则状态
     */
    @ApiOperation("切换积分规则状态")
    @PutMapping("/rules/{id}/status")
    public ApiResponse<Map<String, Object>> toggleRuleStatus(@PathVariable Integer id, @RequestBody Map<String, Integer> param) {
        Integer status = param.get("status");
        return pointsRuleService.toggleStatus(id, status);
    }

    /**
     * 获取积分规则统计数据
     */
    @ApiOperation("获取积分规则统计数据")
    @GetMapping("/rules/statistics")
    public ApiResponse<Map<String, Object>> getRuleStatistics() {
        Map<String, Object> statistics = pointsRuleService.getStatistics();
        return ApiResponse.success(statistics);
    }
    
    /**
     * 获取积分记录列表
     */
    @ApiOperation("获取积分记录列表")
    @GetMapping("/records")
    public ApiResponse<PageResponse<PointsRecord>> getRecordList(PointsQueryParam param) {
        IPage<PointsRecord> page = pointsRecordService.page(param);
        return ApiResponse.success(PageResponse.from(page));
    }

    /**
     * 获取用户积分余额
     */
    @ApiOperation("获取用户积分余额")
    @GetMapping("/balance/{userId}")
    public ApiResponse<Map<String, Object>> getUserBalance(@PathVariable Integer userId) {
        Integer balance = pointsRecordService.getUserBalance(userId);
        return ApiResponse.success(Map.of("userId", userId, "balance", balance));
    }

    /**
     * 积分调整
     */
    @ApiOperation("积分调整")
    @PostMapping("/adjust")
    public ApiResponse<Map<String, Object>> adjustPoints(@RequestBody @Valid PointsAdjustParam param) {
        return pointsRecordService.adjustPoints(param);
    }

    /**
     * 批量调整积分
     */
    @ApiOperation("批量调整积分")
    @PostMapping("/batch-adjust")
    public ApiResponse<Map<String, Object>> batchAdjustPoints(@RequestBody @Valid List<PointsAdjustParam> params) {
        return pointsRecordService.batchAdjustPoints(params);
    }
    
    /**
     * 获取积分统计数据
     */
    @ApiOperation("获取积分统计数据")
    @GetMapping("/statistics")
    public ApiResponse<PointsStatisticsVO> getPointsStatistics() {
        PointsStatisticsVO statistics = pointsRecordService.getStatistics();
        return ApiResponse.success(statistics);
    }

    /**
     * 获取用户积分趋势
     */
    @ApiOperation("获取用户积分趋势")
    @GetMapping("/trend/{userId}")
    public ApiResponse<Map<String, Object>> getUserPointsTrend(@PathVariable Integer userId, @RequestParam String period) {
        Map<String, Object> trend = pointsRecordService.getUserPointsTrend(userId, period);
        return ApiResponse.success(trend);
    }
    
    /**
     * 获取兑换记录列表
     */
    @ApiOperation("获取兑换记录列表")
    @GetMapping("/exchanges")
    public ApiResponse<PageResponse<PointsExchange>> getExchangeList(PointsExchangeQueryParam param) {
        IPage<PointsExchange> page = pointsExchangeService.page(param);
        return ApiResponse.success(PageResponse.from(page));
    }

    /**
     * 获取兑换记录详情
     */
    @ApiOperation("获取兑换记录详情")
    @GetMapping("/exchanges/{id}")
    public ApiResponse<PointsExchange> getExchangeDetail(@PathVariable Integer id) {
        PointsExchange exchange = pointsExchangeService.getById(id);
        return ApiResponse.success(exchange);
    }

    /**
     * 创建兑换记录
     */
    @ApiOperation("创建兑换记录")
    @PostMapping("/exchanges")
    public ApiResponse<Map<String, Object>> createExchange(@RequestBody @Valid PointsExchange exchange) {
        return pointsExchangeService.create(exchange);
    }
    
    /**
     * 更新兑换状态
     */
    @ApiOperation("更新兑换状态")
    @PutMapping("/exchanges/{id}/status")
    public ApiResponse<Map<String, Object>> updateExchangeStatus(@PathVariable Integer id, @RequestBody Map<String, String> param) {
        String status = param.get("status");
        return pointsExchangeService.updateStatus(id, status);
    }

    /**
     * 发货
     */
    @ApiOperation("发货")
    @PutMapping("/exchanges/{id}/ship")
    public ApiResponse<Map<String, Object>> shipExchange(@PathVariable Integer id, @RequestBody Map<String, String> param) {
        String company = param.get("company");
        String trackingNumber = param.get("trackingNumber");
        String remark = param.get("remark");
        return pointsExchangeService.ship(id, company, trackingNumber, remark);
    }

    /**
     * 批量发货
     */
    @ApiOperation("批量发货")
    @PostMapping("/exchanges/batch-ship")
    public ApiResponse<Map<String, Object>> batchShipExchange(@RequestBody Map<String, Object> param) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> shipments = (List<Map<String, Object>>) param.get("shipments");
        String company = (String) param.get("company");
        String remark = (String) param.get("remark");
        return pointsExchangeService.batchShip(shipments, company, remark);
    }
    
    /**
     * 获取兑换统计数据
     */
    @ApiOperation("获取兑换统计数据")
    @GetMapping("/exchanges/statistics")
    public ApiResponse<Map<String, Object>> getExchangeStatistics() {
        Map<String, Object> statistics = pointsExchangeService.getStatistics();
        return ApiResponse.success(statistics);
    }

    /**
     * 批量更新兑换状态
     */
    @ApiOperation("批量更新兑换状态")
    @PostMapping("/exchanges/batch-status")
    public ApiResponse<Map<String, Object>> batchUpdateExchangeStatus(@RequestBody Map<String, Object> param) {
        @SuppressWarnings("unchecked")
        List<Integer> ids = (List<Integer>) param.get("ids");
        String status = (String) param.get("status");
        String remark = (String) param.get("remark");

        return pointsExchangeService.batchUpdateStatus(ids, status, remark);
    }
    
    /**
     * 导出兑换记录
     */
    @ApiOperation("导出兑换记录")
    @GetMapping("/exchanges/export")
    public void exportExchanges(
            PointsExchangeQueryParam param, 
            HttpServletResponse response) throws IOException {
        // 查询数据
        param.setLimit(10000); // 设置较大的限制以获取更多数据
        IPage<PointsExchange> page = pointsExchangeService.page(param);
        List<PointsExchange> exchanges = page.getRecords();
        
        // 创建Excel工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("兑换记录");
        
        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"ID", "用户ID", "用户名", "商品ID", "商品名称", "积分", "数量", "总积分", 
                           "状态", "收货人", "联系电话", "收货地址", "物流公司", "物流单号", "备注", "创建时间", "更新时间"};
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }
        
        // 填充数据
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        int rowNum = 1;
        for (PointsExchange exchange : exchanges) {
            Row row = sheet.createRow(rowNum++);
            
            row.createCell(0).setCellValue(exchange.getId() != null ? exchange.getId().toString() : "");
            row.createCell(1).setCellValue(exchange.getUserId() != null ? exchange.getUserId().toString() : "");
            row.createCell(2).setCellValue(exchange.getUserName() != null ? exchange.getUserName() : "");
            row.createCell(3).setCellValue(exchange.getProductId() != null ? exchange.getProductId().toString() : "");
            row.createCell(4).setCellValue(exchange.getProductName() != null ? exchange.getProductName() : "");
            row.createCell(5).setCellValue(exchange.getPoints() != null ? exchange.getPoints().doubleValue() : 0.0);
            row.createCell(6).setCellValue(exchange.getQuantity() != null ? exchange.getQuantity().doubleValue() : 0.0);
            row.createCell(7).setCellValue(exchange.getTotalPoints() != null ? exchange.getTotalPoints().doubleValue() : 0.0);
            row.createCell(8).setCellValue(exchange.getStatus() != null ? exchange.getStatus() : "");
            
            // 地址信息
            String recipientName = "";
            String phone = "";
            String address = "";
            
            if (exchange.getAddress() != null) {
                recipientName = exchange.getAddress().contains("name") ? 
                    exchange.getAddress().substring(exchange.getAddress().indexOf("name") + 5) : "";
                if (recipientName.contains(",")) {
                    recipientName = recipientName.substring(0, recipientName.indexOf(","));
                }
                
                phone = exchange.getAddress().contains("phone") ? 
                    exchange.getAddress().substring(exchange.getAddress().indexOf("phone") + 6) : "";
                if (phone.contains(",")) {
                    phone = phone.substring(0, phone.indexOf(","));
                }
                
                address = exchange.getAddress();
            }
            
            row.createCell(9).setCellValue(recipientName);
            row.createCell(10).setCellValue(phone);
            row.createCell(11).setCellValue(address);
            
            // 物流信息
            String company = "";
            String trackingNumber = "";
            
            if (exchange.getExpressInfo() != null) {
                company = exchange.getExpressInfo().contains("company") ? 
                    exchange.getExpressInfo().substring(exchange.getExpressInfo().indexOf("company") + 8) : "";
                if (company.contains(",")) {
                    company = company.substring(0, company.indexOf(","));
                }
                
                trackingNumber = exchange.getExpressInfo().contains("trackingNumber") ? 
                    exchange.getExpressInfo().substring(exchange.getExpressInfo().indexOf("trackingNumber") + 15) : "";
                if (trackingNumber.contains(",")) {
                    trackingNumber = trackingNumber.substring(0, trackingNumber.indexOf(","));
                }
            }
            
            row.createCell(12).setCellValue(company);
            row.createCell(13).setCellValue(trackingNumber);
            row.createCell(14).setCellValue(exchange.getRemark() != null ? exchange.getRemark() : "");
            row.createCell(15).setCellValue(exchange.getCreatedAt() != null ? 
                exchange.getCreatedAt().format(formatter) : "");
            row.createCell(16).setCellValue(exchange.getUpdatedAt() != null ? 
                exchange.getUpdatedAt().format(formatter) : "");
        }
        
        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
        
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        String fileName = URLEncoder.encode("兑换记录_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), 
                StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
        
        // 输出Excel文件
        try (OutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
        } finally {
            workbook.close();
        }
    }
} 