package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.study.StudyRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 学习记录Mapper接口
 */
@Mapper
public interface StudyRecordMapper extends BaseMapper<StudyRecord> {

    /**
     * 统计每个课程的学习人数
     *
     * @return 课程ID和学习人数的映射
     */
    @Select("SELECT course_id, COUNT(DISTINCT user_id) as study_count " +
            "FROM study_records " +
            "WHERE course_id IS NOT NULL " +
            "GROUP BY course_id")
    List<Map<String, Object>> getCourseStudyCounts();

    /**
     * 根据课程ID列表批量获取学习人数
     *
     * @param courseIds 课程ID列表
     * @return 课程ID和学习人数的映射
     */
    @Select("<script>" +
            "SELECT course_id, COUNT(DISTINCT user_id) as study_count " +
            "FROM study_records " +
            "WHERE course_id IN " +
            "<foreach collection='courseIds' item='courseId' open='(' separator=',' close=')'>" +
            "  #{courseId}" +
            "</foreach>" +
            "GROUP BY course_id" +
            "</script>")
    List<Map<String, Object>> getCourseStudyCountsByIds(@Param("courseIds") List<Integer> courseIds);
}
