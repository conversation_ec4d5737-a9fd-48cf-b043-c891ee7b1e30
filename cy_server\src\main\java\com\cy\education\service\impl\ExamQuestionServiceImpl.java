package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BadRequestException;
import com.cy.education.exception.BusinessException;
import com.cy.education.exception.NotFoundException;
import com.cy.education.model.entity.ExamBank;
import com.cy.education.model.entity.ExamQuestion;
import com.cy.education.model.params.ExamQuestionParams;
import com.cy.education.model.params.ExamQuestionQueryParams;
import com.cy.education.model.vo.ExamQuestionVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.ExamBankMapper;
import com.cy.education.repository.ExamQuestionMapper;
import com.cy.education.service.ExamQuestionService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 题目服务实现类
 */
@Service
@RequiredArgsConstructor
public class ExamQuestionServiceImpl implements ExamQuestionService {

    private final ExamQuestionMapper examQuestionMapper;
    private final ExamBankMapper examBankMapper;
    private final ObjectMapper objectMapper;

    @Override
    public PageResponse<ExamQuestionVO> listQuestions(ExamQuestionQueryParams params) {
        // 构建分页对象
        Page<ExamQuestionVO> page = new Page<>(params.getPage(), params.getSize());
        
        // 调用Mapper进行分页查询
        IPage<ExamQuestionVO> resultPage = examQuestionMapper.pageQuestions(
                page,
                params.getKeyword(),
                params.getBankId(),
                params.getType()
        );
        
        // 处理查询结果
        List<ExamQuestionVO> records = resultPage.getRecords();
        records.forEach(this::processQuestionOptions);
        
        return PageResponse.of(records, resultPage.getTotal(), params.getPage(), params.getSize());
    }

    @Override
    public ExamQuestionVO getQuestionDetail(Integer id) {
        ExamQuestionVO question = examQuestionMapper.getQuestionDetail(id);
        if (question == null) {
            throw new NotFoundException("题目不存在");
        }
        
        processQuestionOptions(question);
        return question;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createQuestion(ExamQuestionParams params, String createdBy) {
        // 校验题库是否存在
        ExamBank bank = examBankMapper.selectById(params.getBankId());
        if (bank == null) {
            throw new BadRequestException("所选题库不存在");
        }
        
        ExamQuestion question = new ExamQuestion();
        BeanUtils.copyProperties(params, question);
        
        // 处理选项
        if (params.getOptions() != null && !params.getOptions().isEmpty()) {
            try {
                question.setOptions(objectMapper.writeValueAsString(params.getOptions()));
            } catch (JsonProcessingException e) {
                throw new BadRequestException("选项格式错误");
            }
        }
        
        question.setCreatedBy(createdBy);
        question.setCreatedAt(LocalDateTime.now());
        question.setUpdatedAt(LocalDateTime.now());
        
        examQuestionMapper.insert(question);
        return question.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestion(Integer id, ExamQuestionParams params) {
        ExamQuestion question = examQuestionMapper.selectById(id);
        if (question == null) {
            throw new NotFoundException("题目不存在");
        }
        
        // 校验题库是否存在
        if (!question.getBankId().equals(params.getBankId())) {
            ExamBank bank = examBankMapper.selectById(params.getBankId());
            if (bank == null) {
                throw new BadRequestException("所选题库不存在");
            }
        }
        
        BeanUtils.copyProperties(params, question);
        
        // 处理选项
        if (params.getOptions() != null && !params.getOptions().isEmpty()) {
            try {
                question.setOptions(objectMapper.writeValueAsString(params.getOptions()));
            } catch (JsonProcessingException e) {
                throw new BadRequestException("选项格式错误");
            }
        }
        
        question.setUpdatedAt(LocalDateTime.now());
        
        return examQuestionMapper.updateById(question) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteQuestion(Integer id) {
        ExamQuestion question = examQuestionMapper.selectById(id);
        if (question == null) {
            throw new NotFoundException("题目不存在");
        }
        
        // TODO: 检查题目是否已被试卷使用，如果已使用则不允许删除
        
        return examQuestionMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteQuestions(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // TODO: 检查题目是否已被试卷使用，如果已使用则不允许删除
        
        return examQuestionMapper.deleteBatchIds(ids) > 0;
    }
    
    /**
     * 处理题目选项，将JSON字符串转换为List
     */
    @SuppressWarnings("unchecked")
    private void processQuestionOptions(ExamQuestionVO question) {
        try {
            // 由于查询结果是通过SQL直接映射到VO对象
            // MyBatis会自动将options字段映射为String类型而不是List类型
            // 这里需要手动处理一下字段类型转换
            Object optionsObj = question.getOptions();
            
            if (optionsObj != null && optionsObj instanceof String) {
                String optionsJson = (String) optionsObj;
                if (StringUtils.hasText(optionsJson)) {
                    // 直接解析为List<Map>而不是String数组
                    // 这样可以正确处理复杂的JSON结构
                    List<Object> options = objectMapper.readValue(optionsJson, List.class);
                    question.setOptions(options);
                } else {
                    question.setOptions(Collections.emptyList());
                }
            }
        } catch (Exception e) {
            // 记录错误日志
            e.printStackTrace();
            // 如果解析失败，设置为空列表
            question.setOptions(Collections.emptyList());
        }
    }

    // ==================== 导入导出功能 ====================

    @Override
    public Resource generateImportTemplate() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("题目导入模板");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"题库名称*", "题目类型*", "题目标题*", "选项A", "选项B", "选项C", "选项D", "正确答案*", "解析", "难度", "分值"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 创建示例数据行
        Row dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue("Java基础");
        dataRow.createCell(1).setCellValue("single");
        dataRow.createCell(2).setCellValue("Java是什么类型的语言？");
        dataRow.createCell(3).setCellValue("编译型语言");
        dataRow.createCell(4).setCellValue("解释型语言");
        dataRow.createCell(5).setCellValue("混合型语言");
        dataRow.createCell(6).setCellValue("脚本语言");
        dataRow.createCell(7).setCellValue("C");
        dataRow.createCell(8).setCellValue("Java既需要编译又需要解释执行，属于混合型语言");
        dataRow.createCell(9).setCellValue("medium");
        dataRow.createCell(10).setCellValue(5);

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new ByteArrayResource(outputStream.toByteArray());
    }

    @Override
    @Transactional
    public Map<String, Object> importQuestions(MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        List<Map<String, String>> dataList = new ArrayList<>();

        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            // 跳过标题行，从第二行开始读取数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                Map<String, String> data = new HashMap<>();
                data.put("bankName", getCellStringValue(row.getCell(0)));
                data.put("type", getCellStringValue(row.getCell(1)));
                data.put("title", getCellStringValue(row.getCell(2)));
                data.put("optionA", getCellStringValue(row.getCell(3)));
                data.put("optionB", getCellStringValue(row.getCell(4)));
                data.put("optionC", getCellStringValue(row.getCell(5)));
                data.put("optionD", getCellStringValue(row.getCell(6)));
                data.put("answer", getCellStringValue(row.getCell(7)));
                data.put("explanation", getCellStringValue(row.getCell(8)));
                data.put("difficulty", getCellStringValue(row.getCell(9)));
                data.put("score", getCellStringValue(row.getCell(10)));

                dataList.add(data);
            }
        } catch (IOException e) {
            throw new BusinessException("文件读取失败: " + e.getMessage());
        }

        int successCount = 0;
        int failureCount = 0;
        List<Map<String, Object>> failures = new ArrayList<>();

        for (int i = 0; i < dataList.size(); i++) {
            Map<String, String> data = dataList.get(i);
            try {
                // 验证数据
                validateQuestionData(data, i + 2); // Excel行号从2开始

                // 查找题库
                LambdaQueryWrapper<ExamBank> bankQuery = new LambdaQueryWrapper<>();
                bankQuery.eq(ExamBank::getName, data.get("bankName"));
                ExamBank bank = examBankMapper.selectOne(bankQuery);
                if (bank == null) {
                    throw new RuntimeException("题库不存在: " + data.get("bankName"));
                }

                // 创建题目
                ExamQuestion question = new ExamQuestion();
                question.setBankId(bank.getId());
                question.setType(data.get("type"));
                question.setTitle(data.get("title"));
                question.setCorrectAnswer(data.get("answer"));
                question.setExplanation(data.get("explanation"));
                question.setCreatedBy("system"); // 设置创建人

                // 处理选项
                List<String> options = new ArrayList<>();
                if (StringUtils.hasText(data.get("optionA"))) options.add(data.get("optionA"));
                if (StringUtils.hasText(data.get("optionB"))) options.add(data.get("optionB"));
                if (StringUtils.hasText(data.get("optionC"))) options.add(data.get("optionC"));
                if (StringUtils.hasText(data.get("optionD"))) options.add(data.get("optionD"));

                if (!options.isEmpty()) {
                    question.setOptions(objectMapper.writeValueAsString(options));
                }

                question.setCreatedAt(LocalDateTime.now());
                question.setUpdatedAt(LocalDateTime.now());

                examQuestionMapper.insert(question);
                successCount++;

            } catch (Exception e) {
                failureCount++;
                Map<String, Object> failure = new HashMap<>();
                failure.put("row", i + 2);
                failure.put("data", data.toString());
                failure.put("reason", e.getMessage());
                failures.add(failure);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);
        result.put("failures", failures);

        return result;
    }

    @Override
    public void exportQuestions(Map<String, Object> params, HttpServletResponse response) throws IOException {
        // 构建查询条件
        String keyword = (String) params.get("keyword");
        Integer bankId = null;
        Object bankIdObj = params.get("bankId");
        if (bankIdObj != null) {
            if (bankIdObj instanceof Integer) {
                bankId = (Integer) bankIdObj;
            } else if (bankIdObj instanceof String && !((String) bankIdObj).isEmpty()) {
                try {
                    bankId = Integer.parseInt((String) bankIdObj);
                } catch (NumberFormatException e) {
                    // 忽略无效的bankId
                }
            }
        }
        String type = (String) params.get("type");
        String format = (String) params.getOrDefault("format", "xlsx");
        @SuppressWarnings("unchecked")
        List<String> fields = (List<String>) params.get("fields");
        String range = (String) params.getOrDefault("range", "all");
        @SuppressWarnings("unchecked")
        List<Integer> selectedIds = (List<Integer>) params.get("selectedIds");

        // 查询题目数据
        LambdaQueryWrapper<ExamQuestion> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(ExamQuestion::getTitle, keyword);
        }
        if (bankId != null) {
            queryWrapper.eq(ExamQuestion::getBankId, bankId);
        }
        if (StringUtils.hasText(type)) {
            queryWrapper.eq(ExamQuestion::getType, type);
        }
        if ("selected".equals(range) && selectedIds != null && !selectedIds.isEmpty()) {
            queryWrapper.in(ExamQuestion::getId, selectedIds);
        }

        List<ExamQuestion> questions = examQuestionMapper.selectList(queryWrapper);

        // 设置响应头
        String fileName = URLEncoder.encode("题目列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + "." + format, StandardCharsets.UTF_8.toString());
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        // 使用Apache POI写入Excel
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("题目列表");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] allHeaders = {"题库名称", "题目类型", "题目标题", "选项", "正确答案", "解析", "创建时间"};
            String[] selectedHeaders = fields != null && !fields.isEmpty() ?
                fields.toArray(new String[0]) : allHeaders;

            for (int i = 0; i < selectedHeaders.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(selectedHeaders[i]);
            }

            // 写入数据
            for (int i = 0; i < questions.size(); i++) {
                ExamQuestion question = questions.get(i);
                Row row = sheet.createRow(i + 1);

                // 获取题库名称
                ExamBank bank = examBankMapper.selectById(question.getBankId());
                String bankName = bank != null ? bank.getName() : "";

                int colIndex = 0;
                for (String header : selectedHeaders) {
                    Cell cell = row.createCell(colIndex++);
                    switch (header) {
                        case "题库名称":
                        case "bankName":
                            cell.setCellValue(bankName);
                            break;
                        case "题目类型":
                        case "type":
                            cell.setCellValue(question.getType());
                            break;
                        case "题目标题":
                        case "title":
                            cell.setCellValue(question.getTitle());
                            break;
                        case "选项":
                        case "options":
                            cell.setCellValue(question.getOptions() != null ? question.getOptions() : "");
                            break;
                        case "正确答案":
                        case "answer":
                            cell.setCellValue(question.getCorrectAnswer() != null ? question.getCorrectAnswer() : "");
                            break;
                        case "解析":
                        case "explanation":
                            cell.setCellValue(question.getExplanation() != null ? question.getExplanation() : "");
                            break;
                        case "创建时间":
                        case "createTime":
                        case "createdAt":
                            cell.setCellValue(question.getCreatedAt() != null ? question.getCreatedAt().toString() : "");
                            break;
                    }
                }
            }

            // 自动调整列宽
            for (int i = 0; i < selectedHeaders.length; i++) {
                sheet.autoSizeColumn(i);
            }

            workbook.write(response.getOutputStream());
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 验证题目数据
     */
    private void validateQuestionData(Map<String, String> data, int rowNum) {
        if (!StringUtils.hasText(data.get("bankName"))) {
            throw new RuntimeException("第" + rowNum + "行：题库名称不能为空");
        }
        if (!StringUtils.hasText(data.get("type"))) {
            throw new RuntimeException("第" + rowNum + "行：题目类型不能为空");
        }
        if (!StringUtils.hasText(data.get("title"))) {
            throw new RuntimeException("第" + rowNum + "行：题目标题不能为空");
        }
        if (!StringUtils.hasText(data.get("answer"))) {
            throw new RuntimeException("第" + rowNum + "行：正确答案不能为空");
        }
        // 可以添加更多验证逻辑
    }
}