<template>
  <view class="page-container">
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">兑换记录</text>
        <view class="placeholder"></view>
      </view>
    </view>
    <!-- 加载更多 -->
    <view v-if="loading" class="loading-more">
      <up-loading-icon color="#667eea"></up-loading-icon>
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <up-empty
        v-if="exchangeRecords.length === 0 && !loading"
        mode="list"
        text="空空如也"
        textColor="#909399"
        textSize="14"
    >
    </up-empty>
    <view v-else class="records-list">
      <view v-for="record in exchangeRecords" :key="record.id" class="record-card">
        <!-- 商品图片 -->
        <image :src="record.goodsImage" class="goods-image" mode="aspectFill" />
        <view class="record-info">
          <text class="record-desc">{{ record.productName }}</text>
          <text class="record-address">收货地址：{{ record.address }}</text>
          <text class="record-date">兑换时间：{{ record.createdAt }}</text>
          <view class="points-status-row">
            <text class="record-points">{{ record.points }}积分</text>
            <view class="record-status-tag">
              <text :class="['status-tag', statusTagClass(record.status)]">{{ record.status }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 加载更多 -->
      <view class="load-more" v-if="exchangeRecords.length > 0">
        <view v-if="loadMoreStatus === 'loading'" class="loading-text">
          <text>加载中...</text>
        </view>
        <view v-else-if="loadMoreStatus === 'noMore'" class="nomore-text">
          <text>没有更多了</text>
        </view>
        <view v-else class="loadmore-text">
          <text>加载更多</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import { getPointsExchanges } from '@/api/points'

// 加载状态
const loading = ref(true)
// 兑换记录数据
const exchangeRecords = ref<any[]>([])
// 加载更多状态：more-可加载，loading-加载中，noMore-没有更多
const loadMoreStatus = ref<'more' | 'loading' | 'noMore'>('more')
// 分页参数
const pageNum = ref(1)
const pageSize = ref(4)
const total = ref(0)
const isLoadingMore = ref(false)
const hasMore = ref(true)

// 状态tag颜色映射
const statusTagClass = (status: string) => {
  switch (status) {
    case '待审核':
      return 'tag-pending'
    case '已发货':
      return 'tag-shipped'
    case '已完成':
      return 'tag-completed'
    case '已拒绝':
      return 'tag-rejected'
    default:
      return 'tag-default'
  }
}

// 返回上一页
const navigateBack = () => {
  uni.navigateBack()
}

// 下拉刷新
onPullDownRefresh(() => {
  pageNum.value = 1
  exchangeRecords.value = []
  hasMore.value = true
  fetchExchanges()
})

// 上拉加载更多
onReachBottom(() => {
  if (!hasMore.value || isLoadingMore.value) return
  pageNum.value += 1
  fetchExchanges(true)
})

// 获取兑换记录数据，isLoadMore=true为加载更多，否则为刷新
const fetchExchanges = async (isLoadMore = false) => {
  if (isLoadMore) {
    isLoadingMore.value = true
    loadMoreStatus.value = 'loading'
  } else {
    loading.value = true
    pageNum.value = 1
    exchangeRecords.value = []
    hasMore.value = true
  }
  try {
    const res = await getPointsExchanges({ pageNum: pageNum.value, pageSize: pageSize.value})
    total.value = res.total || 0
    if (isLoadMore) {
      exchangeRecords.value = exchangeRecords.value.concat(res.list)
    } else {
      exchangeRecords.value = res.list
    }
    if (exchangeRecords.value.length >= total.value) {
      hasMore.value = false
      loadMoreStatus.value = 'noMore'
    } else {
      hasMore.value = true
      loadMoreStatus.value = 'more'
    }
  } finally {
    loading.value = false
    isLoadingMore.value = false
    uni.stopPullDownRefresh();
  }
}

onMounted(() => {
  fetchExchanges()
})
</script>

<style lang="scss" scoped>
.page-container {
  background: #f5f6fa;
  min-height: 100vh;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px 16px 32px 16px;
}

.record-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  padding: 18px 20px;
  transition: box-shadow 0.2s;
  gap: 16px;

  &:active {
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
  }
}

.goods-image {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  object-fit: cover;
  background: #f0f0f0;
  margin-right: 12px;
}

.record-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.record-desc {
  font-size: 15px;
  color: #1a1d2e;
  font-weight: 500;
}

.record-address {
  font-size: 13px;
  color: #8e8e93;
}

.record-date {
  font-size: 12px;
  color: #8e8e93;
}

.points-status-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 2px;
}

.record-points {
  font-size: 15px;
  font-weight: 600;
  color: var(--primary-color);
}

.record-status-tag {
  margin-left: 8px;
}

.status-tag {
  display: inline-block;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
}
.tag-pending {
  background: #f7b500;
}
.tag-shipped {
  background: #409eff;
}
.tag-completed {
  background: #67c23a;
}
.tag-rejected {
  background: #ff4d4f;
}
.tag-default {
  background: #bfbfbf;
}
</style>
