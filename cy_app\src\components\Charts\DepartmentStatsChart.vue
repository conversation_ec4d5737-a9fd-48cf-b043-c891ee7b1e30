<template>
  <view class="chart-container">
    <view class="chart-header">
      <text class="chart-title">部门统计对比</text>
      <view class="chart-controls">
        <view class="metric-selector">
          <text
              v-for="(metric, index) in metrics"
              :key="index"
              :class="['metric-option', { active: selectedMetric === metric.key }]"
              @click="selectMetric(metric.key)"
          >
            {{ metric.label }}
          </text>
        </view>
      </view>
    </view>
    <view class="chart-content">
      <canvas
          id="departmentStatsChart"
          canvas-id="departmentStatsChart"
          class="chart-canvas"
          @touchend="touchEnd"
          @touchmove="touchMove"
          @touchstart="touchStart"
      ></canvas>
    </view>
    <view v-if="showLegend" class="chart-legend">
      <view
          v-for="(item, index) in legendData"
          :key="index"
          class="legend-item"
      >
        <view :style="{ backgroundColor: item.color }" class="legend-color"></view>
        <text class="legend-label">{{ item.name }}</text>
        <text class="legend-value">{{ item.value }}</text>
      </view>
    </view>
    <view v-if="chartInfo" class="chart-info">
      <text class="info-text">{{ chartInfo }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {ref, onMounted, watch, computed} from 'vue'
import uCharts from '@qiun/ucharts'
import {pieChartConfig, departmentColors} from './chartConfig'

interface DepartmentStat {
  departmentId: number
  departmentName: string
  participantCount: number
  averageScore: number
  passRate: number
  highestScore: number
  averageDuration: number
}

interface Props {
  data: DepartmentStat[]
}

const props = defineProps<Props>()

const chartInstance = ref<any>(null)
const selectedMetric = ref('participantCount')
const legendData = ref<any[]>([])
const showLegend = ref(false)
const chartInfo = ref('')

// 指标配置
const metrics = [
  {key: 'participantCount', label: '参与人数', unit: '人'},
  {key: 'averageScore', label: '平均分', unit: '分'},
  {key: 'passRate', label: '通过率', unit: '%'},
  {key: 'highestScore', label: '最高分', unit: '分'},
  {key: 'averageDuration', label: '平均用时', unit: '分钟'}
]

// 图表配置 - 改为饼图
const chartConfig = {
  type: 'pie',
  context: null as any,
  width: 350,
  height: 300,
  series: [
    {
      name: '部门数据',
      data: []
    }
  ],
  color: departmentColors,
  legend: {
    show: false
  },
  extra: {
    pie: {
      activeOpacity: 0.5,
      activeRadius: 10,
      offsetAngle: 0,
      labelWidth: 15,
      border: true,
      borderWidth: 3,
      borderColor: '#FFFFFF'
    }
  },
  animation: true,
  background: '#FFFFFF',
  padding: [15, 15, 15, 15],
  dataLabel: true,
  dataPointShape: false
}

// 选择指标
const selectMetric = (metricKey: string) => {
  selectedMetric.value = metricKey
  updateChartData()
}

// 更新图表数据
const updateChartData = () => {
  if (!props.data || props.data.length === 0) return

  // 根据选择的指标获取数据
  const metric = metrics.find(m => m.key === selectedMetric.value)
  if (!metric) return

  // 获取对应指标的数据
  const pieData = props.data.map((dept, index) => {
    let value = dept[selectedMetric.value as keyof DepartmentStat] as number
    if (selectedMetric.value === 'averageDuration') {
      // 平均用时转换为分钟
      value = Math.round(value / 60)
    } else if (selectedMetric.value === 'passRate') {
      // 通过率保持百分比
      value = Math.round(value)
    } else {
      // 其他数值四舍五入
      value = Math.round(value)
    }

    // 如果部门名称太长，进行截断
    const name = dept.departmentName.length > 8
        ? dept.departmentName.substring(0, 8) + '...'
        : dept.departmentName

    return {
      name: name,
      value: value
    }
  }).filter(item => item.value > 0) // 过滤掉值为0的数据

  // 更新图表配置
  chartConfig.series[0].data = pieData
  chartConfig.series[0].name = metric.label

  // 更新图例数据
  legendData.value = pieData.map((item, index) => ({
    name: item.name,
    value: `${item.value}${metric.unit}`,
    color: departmentColors[index % departmentColors.length]
  }))

  showLegend.value = pieData.length > 0

  // 更新图表信息
  chartInfo.value = `共${pieData.length}个部门`

  // 更新图表
  if (chartInstance.value) {
    chartInstance.value.updateData({
      series: chartConfig.series
    })
  }
}

// 初始化图表
const initChart = () => {
  const query = uni.createSelectorQuery()
  query.select('#departmentStatsChart')
      .fields({node: true, size: true})
      .exec((res) => {
        if (res[0] && res[0].node) {
          try {
            const canvas = res[0].node
            const ctx = canvas.getContext('2d')

            if (!ctx) {
              console.error('无法获取Canvas上下文')
              return
            }

            // 设置图表配置
            const config = {
              ...chartConfig,
              context: ctx,
              width: res[0].width || 350,
              height: res[0].height || 300
            }

            // 创建图表实例
            chartInstance.value = new uCharts(config)

            // 更新数据
            updateChartData()
          } catch (error) {
            console.error('初始化图表失败:', error)
          }
        } else {
          console.error('Canvas元素未找到')
        }
      })
}

// 触摸事件处理
const touchStart = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.touchLegend(e)
    chartInstance.value.showToolTip(e)
  }
}

const touchMove = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.scroll(e)
  }
}

const touchEnd = (e: any) => {
  if (chartInstance.value) {
    chartInstance.value.touchLegend(e)
  }
}

// 监听数据变化
watch(() => props.data, updateChartData, {deep: true})

onMounted(() => {
  setTimeout(() => {
    initChart()
  }, 300)
})
</script>

<style lang="scss" scoped>
.chart-container {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 12px;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-selector {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 5px;
}

.metric-option {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;

  &.active {
    background: #3b82f6;
    color: #ffffff;
  }

  &:hover {
    background: #e2e8f0;
  }
}

.chart-content {
  display: flex;
  justify-content: center;
}

.chart-canvas {
  width: 350px;
  height: 300px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border-radius: 6px;
  background: #f8faff;
  min-width: 80px;
  max-width: 120px;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  flex-shrink: 0;
}

.legend-label {
  font-size: 11px;
  color: #4a5568;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.legend-value {
  font-size: 11px;
  font-weight: 600;
  color: #1a1d2e;
}

.chart-info {
  margin-top: 8px;
  text-align: center;
}

.info-text {
  font-size: 12px;
  color: #64748b;
}
</style>
