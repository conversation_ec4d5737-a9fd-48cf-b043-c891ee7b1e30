<template>
  <el-dialog v-model="visible" title="帖子记录" width="900px" destroy-on-close @close="handleClose">
    <div class="dialog-header">
      <span>学员：{{ student?.name }}</span>
      <el-select 
        v-model="statusFilter" 
        placeholder="状态筛选" 
        style="width: 120px; margin-left: 20px;" 
        @change="loadData"
      >
        <el-option label="全部" value="" />
        <el-option label="已发布" value="published" />
        <el-option label="待审核" value="pending" />
        <el-option label="已删除" value="deleted" />
      </el-select>
    </div>
    
    <el-table :data="posts" v-loading="loading" max-height="400">
      <el-table-column prop="title" label="标题" min-width="200" />
      <el-table-column prop="categoryName" label="分类" width="100" />
      <el-table-column prop="status" label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="viewCount" label="浏览" width="80" />
      <el-table-column prop="replyCount" label="回复" width="80" />
      <el-table-column prop="createTime" label="发布时间" width="150" />
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button text type="primary" @click="viewPost(row)">查看</el-button>
          <el-button text type="danger" @click="deletePost(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="dialog-pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadData"
        @current-change="loadData"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { getPostList, deletePost as deletePostApi, type Post, type PostQueryParams } from '@/api/forum'
import { ElMessage, ElMessageBox } from 'element-plus'

interface Props {
  modelValue: boolean
  student: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const posts = ref<Post[]>([])
const statusFilter = ref('')

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 监听props变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.student) {
    loadData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const loadData = async () => {
  if (!props.student) return

  loading.value = true
  try {
    const params: PostQueryParams = {
      page: pagination.page,
      size: pagination.limit,
      authorId: props.student.id
    }

    if (statusFilter.value) {
      // 转换状态值：published->1, pending->0, deleted->3
      const statusMap: Record<string, number> = {
        'published': 1,
        'pending': 0,
        'deleted': 3
      }
      params.status = statusMap[statusFilter.value]
    }

    const response = await getPostList(params)

    // 转换数据格式以匹配表格显示
    const transformedPosts = response.list.map(post => ({
      ...post,
      categoryName: post.category,
      status: getStatusFromNumber(post.status)
    }))

    posts.value = transformedPosts
    pagination.total = response.total
  } catch (error) {
    console.error('加载帖子记录失败:', error)
    ElMessage.error('加载帖子记录失败')
  } finally {
    loading.value = false
  }
}

// 将数字状态转换为字符串状态
const getStatusFromNumber = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: 'pending',
    1: 'published',
    2: 'rejected',
    3: 'deleted'
  }
  return statusMap[status] || 'pending'
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'published': 'success',
    'pending': 'warning',
    'deleted': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'published': '已发布',
    'pending': '待审核',
    'deleted': '已删除'
  }
  return textMap[status] || status
}

const viewPost = (row: Post) => {
  // TODO: 实现查看帖子详情，可以跳转到帖子详情页
  window.open(`/forum/post/${row.id}`, '_blank')
}

const deletePost = (row: Post) => {
  ElMessageBox.confirm('确定要删除这个帖子吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deletePostApi(row.id)
      ElMessage.success('删除成功')
      loadData()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

const handleClose = () => {
  visible.value = false
  posts.value = []
  statusFilter.value = ''
  pagination.page = 1
}
</script>

<style scoped>
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  font-weight: 600;
}

.dialog-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}
</style>
