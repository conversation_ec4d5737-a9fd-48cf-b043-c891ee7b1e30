<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <up-icon name="arrow-left" size="20" color="#1a1d2e"></up-icon>
        </view>
        <view class="navbar-center">
          <text class="navbar-title">练习中心</text>
        </view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 题库选择 -->
      <view class="bank-section">
        <view class="section-title">
          <text class="title-text">选择题库</text>
          <text class="title-desc">请选择要练习的题库</text>
        </view>
        
        <view class="bank-list">
          <!-- 全部题库选项 -->
          <view 
            :class="['bank-card', { active: selectedBankId === null }]" 
            @click="selectBank(null)"
          >
            <view class="bank-left">
              <view class="bank-icon all">
                <up-icon name="grid" size="24" color="#667eea"></up-icon>
              </view>
              <view class="bank-content">
                <text class="bank-name">全部题库</text>
                <text class="bank-desc">练习所有可用题库的题目</text>
              </view>
            </view>
            <view class="bank-right">
              <view v-if="allStats" class="bank-stats">
                <text class="stats-text">
                  已答 {{ allStats.answered }}/{{ allStats.total }} 题
                </text>
                <text class="stats-text">
                  正确率 {{ allStats.accuracy }}%
                </text>
              </view>
              <up-icon name="checkmark-circle-fill" size="20" color="#667eea" v-if="selectedBankId === null"></up-icon>
            </view>
          </view>

          <!-- 具体题库选项 -->
          <view 
            v-for="bank in bankList" 
            :key="bank.id"
            :class="['bank-card', { active: selectedBankId === bank.id }]" 
            @click="selectBank(bank.id)"
          >
            <view class="bank-left">
              <view class="bank-icon">
                <up-icon name="folder" size="24" color="#667eea"></up-icon>
              </view>
              <view class="bank-content">
                <text class="bank-name">{{ bank.name }}</text>
                <text class="bank-desc">{{ bank.description || '暂无描述' }}</text>
              </view>
            </view>
            <view class="bank-right">
              <view v-if="bank.stats" class="bank-stats">
                <text class="stats-text">
                  已答 {{ bank.stats.answered }}/{{ bank.stats.total }} 题
                </text>
                <text class="stats-text">
                  正确率 {{ bank.stats.accuracy }}%
                </text>
              </view>
              <up-icon name="checkmark-circle-fill" size="20" color="#667eea" v-if="selectedBankId === bank.id"></up-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 练习统计 -->
      <view v-if="selectedStats" class="stats-section">
        <view class="section-title">
          <text class="title-text">练习统计</text>
        </view>
        
        <view class="stats-card">
          <view class="stats-grid">
            <view class="stat-item">
              <text class="stat-number">{{ selectedStats.total || 0 }}</text>
              <text class="stat-label">总题数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number answered">{{ selectedStats.answered || 0 }}</text>
              <text class="stat-label">已答题</text>
            </view>
            <view class="stat-item">
              <text class="stat-number correct">{{ selectedStats.correct || 0 }}</text>
              <text class="stat-label">答对题</text>
            </view>
            <view class="stat-item">
              <text class="stat-number wrong">{{ selectedStats.wrong || 0 }}</text>
              <text class="stat-label">答错题</text>
            </view>
          </view>
          <view class="accuracy-bar">
            <view class="accuracy-label">
              <text>正确率</text>
              <text class="accuracy-value">{{ selectedStats.accuracy || 0 }}%</text>
            </view>
            <view class="progress-bar">
              <view 
                class="progress-fill" 
                :style="{ width: (selectedStats.accuracy || 0) + '%' }"
              ></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 练习模式选择 -->
      <view class="mode-section">
        <view class="section-title">
          <text class="title-text">练习模式</text>
        </view>
        
        <view class="mode-list">
          <view 
            :class="['mode-card', { active: practiceMode === 'normal' }]"
            @click="practiceMode = 'normal'"
          >
            <view class="mode-icon">
              <up-icon name="edit-pen" size="24" color="#667eea"></up-icon>
            </view>
            <view class="mode-content">
              <text class="mode-name">顺序练习</text>
              <text class="mode-desc">按顺序练习题目</text>
            </view>
            <up-icon name="checkmark-circle-fill" size="20" color="#667eea" v-if="practiceMode === 'normal'"></up-icon>
          </view>
          
          <view 
            :class="['mode-card', { active: practiceMode === 'wrong', disabled: !hasWrongQuestions }]"
            @click="selectWrongMode"
          >
            <view class="mode-icon wrong">
              <up-icon name="close-circle" size="24" color="#ef4444"></up-icon>
            </view>
            <view class="mode-content">
              <text class="mode-name">错题练习</text>
              <text class="mode-desc">
                {{ hasWrongQuestions ? '练习做错的题目' : '暂无错题' }}
              </text>
            </view>
            <up-icon name="checkmark-circle-fill" size="20" color="#667eea" v-if="practiceMode === 'wrong'"></up-icon>
          </view>
        </view>
      </view>

      <!-- 开始练习按钮 -->
      <view class="action-section">
        <view 
          :class="['start-btn', { disabled: !canStartPractice }]"
          @click="startPractice"
        >
          <text class="btn-text">开始练习</text>
        </view>
      </view>
    </view>

    <!-- 加载遮罩 -->
    <up-loading-page :loading="loading" loading-text="加载中..."></up-loading-page>
  </view>
</template>

<script>
import { getAvailableBanks, getUserPracticeStats, getWrongQuestions, startPractice } from '@/api/practice'

export default {
  data() {
    return {
      loading: true,
      bankList: [],
      selectedBankId: null,
      practiceMode: 'normal',
      allStats: null,
      selectedStats: null,
      hasWrongQuestions: false
    }
  },
  computed: {
    canStartPractice() {
      if (this.practiceMode === 'wrong' && !this.hasWrongQuestions) {
        return false
      }
      return true
    }
  },
  onLoad() {
    this.loadData()
  },
  onShow() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      
      await this.loadBanks()
      await this.loadStats()
      await this.checkWrongQuestions()
      
      this.loading = false
    },
    
    async loadBanks() {
      try {
        const response = await getAvailableBanks()
        this.bankList = response || []
      } catch (error) {
        console.error('获取题库列表失败:', error)
        this.bankList = []
        uni.showToast({
          title: '获取题库失败',
          icon: 'none'
        })
      }
    },
    
    async loadStats() {
      const userId = uni.getStorageSync('userId')
      if (!userId) {
        this.initDefaultStats()
        return
      }
      
      try {
        const stats = await getUserPracticeStats(userId)
        
        if (stats && stats.length > 0) {
          const total = stats.reduce((sum, item) => sum + (item.totalQuestions || 0), 0)
          const answered = stats.reduce((sum, item) => sum + (item.answeredQuestions || 0), 0)
          const correct = stats.reduce((sum, item) => sum + (item.correctCount || 0), 0)
          const wrong = stats.reduce((sum, item) => sum + (item.wrongCount || 0), 0)
          
          this.allStats = {
            total,
            answered,
            correct,
            wrong,
            accuracy: answered > 0 ? Math.round((correct / answered) * 100) : 0
          }
          
          this.bankList.forEach(bank => {
            const bankStat = stats.find(s => s.bankId === bank.id)
            if (bankStat) {
              bank.stats = {
                total: bankStat.totalQuestions || 0,
                answered: bankStat.answeredQuestions || 0,
                correct: bankStat.correctCount || 0,
                wrong: bankStat.wrongCount || 0,
                accuracy: bankStat.answeredQuestions > 0 ? 
                  Math.round((bankStat.correctCount / bankStat.answeredQuestions) * 100) : 0
              }
            } else {
              bank.stats = { total: 0, answered: 0, correct: 0, wrong: 0, accuracy: 0 }
            }
          })
        } else {
          this.initDefaultStats()
        }
        
        this.selectedStats = this.allStats
      } catch (error) {
        console.error('获取练习统计失败:', error)
        this.initDefaultStats()
      }
    },
    
    initDefaultStats() {
      this.allStats = { total: 0, answered: 0, correct: 0, wrong: 0, accuracy: 0 }
      this.bankList.forEach(bank => {
        bank.stats = { total: 0, answered: 0, correct: 0, wrong: 0, accuracy: 0 }
      })
      this.selectedStats = this.allStats
    },
    
    async checkWrongQuestions() {
      const userId = uni.getStorageSync('userId')
      if (!userId) {
        this.hasWrongQuestions = false
        return
      }
      
      try {
        const wrongQuestions = await getWrongQuestions(userId, this.selectedBankId)
        this.hasWrongQuestions = wrongQuestions && wrongQuestions.length > 0
      } catch (error) {
        console.error('检查错题失败:', error)
        this.hasWrongQuestions = false
      }
    },
    
    async selectBank(bankId) {
      this.selectedBankId = bankId
      
      if (bankId === null) {
        this.selectedStats = this.allStats
      } else {
        const bank = this.bankList.find(b => b.id === bankId)
        this.selectedStats = bank ? bank.stats : null
      }
      
      await this.checkWrongQuestions()
      
      if (this.practiceMode === 'wrong' && !this.hasWrongQuestions) {
        this.practiceMode = 'normal'
        uni.showToast({
          title: '当前选择暂无错题，已切换为正常练习',
          icon: 'none'
        })
      }
    },
    
    selectWrongMode() {
      if (!this.hasWrongQuestions) {
        uni.showToast({
          title: '当前选择暂无错题',
          icon: 'none'
        })
        return
      }
      this.practiceMode = 'wrong'
    },
    
    async startPractice() {
      if (!this.canStartPractice) return
      
      const userId = uni.getStorageSync('userId')
      if (!userId) {
        uni.showToast({ title: '请先登录', icon: 'none' })
        return
      }
      
      try {
        uni.showLoading({ title: '准备练习...' })
        
        const result = await startPractice({
          userId: parseInt(userId),
          bankId: this.selectedBankId,
          type: this.practiceMode
        })
        
        uni.hideLoading()
        
        if (result && result.recordId) {
          uni.navigateTo({
            url: `/pages/practice/doing?recordId=${result.recordId}`
          })
        } else {
          uni.showToast({ title: '开始练习失败', icon: 'none' })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('开始练习失败:', error)
        uni.showToast({ title: error.message || '开始练习失败', icon: 'none' })
      }
    },
    
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/practice/index.scss";
</style>