package com.cy.education.security;

import com.cy.education.utils.JwtTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;

/**
 * JWT认证过滤器
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Value("${jwt.token-header}")
    private String tokenHeader;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {

        // 获取请求头中的token
        String authHeader = request.getHeader(tokenHeader);

        // 判断是否有token
        if (authHeader != null && authHeader.startsWith(tokenPrefix)) {
            // 去除Bearer前缀
            String authToken = authHeader.substring(tokenPrefix.length()).trim();

            // 从token中获取用户名
            String username = jwtTokenUtil.getUsernameFromToken(authToken);

            // 判断用户名是否存在，且Spring Security认证上下文中没有认证信息
            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 从token中直接提取用户ID，避免重复查询数据库
                Integer userId = jwtTokenUtil.getClaimFromToken(authToken, claims -> {
                    Object idClaim = claims.get("id");
                    if (idClaim != null) {
                        if (idClaim instanceof Integer) {
                            return (Integer) idClaim;
                        } else if (idClaim instanceof String) {
                            return Integer.parseInt((String) idClaim);
                        }
                    }
                    return null;
                });

                // 从token中提取真实姓名
                String realName = jwtTokenUtil.getClaimFromToken(authToken, claims -> {
                    Object realNameClaim = claims.get("realName");
                    return realNameClaim != null ? realNameClaim.toString() : null;
                });

                // 创建JwtUserDetails对象，直接从token中获取信息
                JwtUserDetails userDetails = new JwtUserDetails(
                        username,
                        "", // 密码为空，因为JWT已经验证过了
                        Collections.emptyList());

                // 设置用户ID和真实姓名
                if (userId != null) {
                    userDetails.setUserId(userId.toString());
                }
                if (realName != null) {
                    userDetails.setRealName(realName);
                }

                // 验证token是否有效（只验证签名和过期时间）
                if (jwtTokenUtil.validateToken(authToken, userDetails)) {
                    // 创建认证信息
                    UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(
                            userDetails, null, userDetails.getAuthorities());

                    // 设置认证详情
                    authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    // 设置认证信息到安全上下文
                    SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                }
            }
        }

        // 继续过滤器链
        chain.doFilter(request, response);
    }
}
