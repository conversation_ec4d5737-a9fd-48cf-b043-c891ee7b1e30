package com.cy.education.repository;

import com.cy.education.model.entity.exam.ExamAnswer;
import com.cy.education.model.vo.ExamAnswerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 考试答题Mapper接口
 */
@Mapper
public interface ExamAnswerMapper {

    /**
     * 根据记录ID查询答题详情列表
     * @param recordId 考试记录ID
     * @return 答题详情列表
     */
    List<ExamAnswerVO> selectByRecordId(@Param("recordId") Integer recordId);

    void batchInsert(@Param("answers") List<ExamAnswer> answers);
}
