package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 论坛违规举报实体类
 */
@Data
@TableName("forum_violations")
public class ForumViolation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 举报ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 内容类型：post帖子，comment评论
     */
    private String contentType;

    /**
     * 内容ID
     */
    private Integer contentId;

    /**
     * 举报人ID
     */
    private Integer reporterId;

    /**
     * 举报原因
     */
    private String reportReason;

    /**
     * 状态：0待处理，1已处理，2已忽略
     */
    private Integer status;

    /**
     * 处理类型：0无操作，1删除，2警告
     */
    private Integer processType;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 处理时间
     */
    private LocalDateTime processTime;

    /**
     * 处理人ID
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 举报人名称（非数据库字段）
     */
    @TableField(exist = false)
    private String reporter;

    /**
     * 处理人名称（非数据库字段）
     */
    @TableField(exist = false)
    private String operator;

    /**
     * 被举报内容（非数据库字段）
     */
    @TableField(exist = false)
    private String content;
} 