<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑题目' : '新增题目'"
    width="800px"
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="所属题库" prop="bankId">
        <el-select v-model="formData.bankId" placeholder="请选择题库" style="width: 100%">
          <el-option
            v-for="bank in bankList"
            :key="bank.id"
            :label="bank.name"
            :value="bank.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="题目类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择题目类型" @change="handleTypeChange">
          <el-option
            v-for="type in questionTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="题目标题" prop="title">
        <el-input
          v-model="formData.title"
          type="textarea"
          :rows="3"
          placeholder="请输入题目标题"
        />
      </el-form-item>

      <!-- 选择题选项 -->
      <el-form-item 
        v-if="formData.type === 'single' || formData.type === 'multiple'" 
        label="题目选项" 
        prop="options"
      >
        <div class="options-container">
          <div
            v-for="(option, index) in formData.options"
            :key="index"
            class="option-item"
          >
            <div class="option-label">{{ String.fromCharCode(65 + index) }}.</div>
            <el-input
              v-model="formData.options[index]"
              placeholder="请输入选项内容"
              style="flex: 1"
            />
            <el-button
              v-if="formData.options.length > 2"
              type="danger"
              size="small"
              :icon="Minus"
              @click="removeOption(index)"
            />
          </div>
          <el-button
            v-if="formData.options.length < 6"
            type="primary"
            size="small"
            :icon="Plus"
            @click="addOption"
          >
            添加选项
          </el-button>
        </div>
      </el-form-item>

      <!-- 正确答案 -->
      <el-form-item label="正确答案" prop="correctAnswer">
        <!-- 单选题答案 -->
        <el-radio-group v-if="formData.type === 'single'" v-model="formData.correctAnswer">
          <el-radio
            v-for="(option, index) in formData.options"
            :key="index"
            :value="String.fromCharCode(65 + index)"
          >
            {{ String.fromCharCode(65 + index) }}
          </el-radio>
        </el-radio-group>

        <!-- 多选题答案 -->
        <el-checkbox-group v-else-if="formData.type === 'multiple'" v-model="formData.correctAnswer">
          <el-checkbox
            v-for="(option, index) in formData.options"
            :key="index"
            :value="String.fromCharCode(65 + index)"
          >
            {{ String.fromCharCode(65 + index) }}
          </el-checkbox>
        </el-checkbox-group>

        <!-- 判断题答案 -->
        <el-radio-group v-else-if="formData.type === 'judgment'" v-model="formData.correctAnswer">
          <el-radio value="true">正确</el-radio>
          <el-radio value="false">错误</el-radio>
        </el-radio-group>

        <!-- 填空题和简答题答案 -->
        <el-input
          v-else
          v-model="formData.correctAnswer"
          type="textarea"
          :rows="3"
          placeholder="请输入正确答案"
        />
      </el-form-item>

      <el-form-item label="题目解析" prop="explanation">
        <el-input
          v-model="formData.explanation"
          type="textarea"
          :rows="3"
          placeholder="请输入题目解析（可选）"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { Plus, Minus } from '@element-plus/icons-vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { Question, Bank } from '@/api/exam'

interface QuestionType {
  value: string
  label: string
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  questionData: {
    type: Object as () => Question | null,
    default: null
  },
  bankList: {
    type: Array as () => Bank[],
    default: () => []
  },
  questionTypes: {
    type: Array as () => QuestionType[],
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const loading = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive<Partial<Question> & { options: string[] }>({
  bankId: '',
  type: 'single',
  title: '',
  options: ['', '', '', ''],
  correctAnswer: 'A',
  explanation: ''
})

const rules: FormRules = {
  bankId: [
    { required: true, message: '请选择题库', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择题目类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入题目标题', trigger: 'blur' },
    { min: 5, message: '题目标题至少5个字符', trigger: 'blur' }
  ],
  options: [
    {
      validator: (rule, value, callback) => {
        if (formData.type === 'single' || formData.type === 'multiple') {
          const validOptions = formData.options.filter((opt: string) => opt.trim())
          if (validOptions.length < 2) {
            callback(new Error('至少需要2个选项'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  correctAnswer: [
    { required: true, message: '请设置正确答案', trigger: 'change' }
  ]
}

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.questionData) {
    // 编辑模式，填充数据
    const questionData = {...props.questionData};
    console.log('加载题目数据:', questionData);
    
    // 处理选项数据
    let formattedOptions = ['', '', '', ''];
    if (questionData.options) {
      if (Array.isArray(questionData.options)) {
        console.log('原始选项数据:', questionData.options);
        
        // 处理两种情况:
        // 1. 后端返回的是字符串数组
        // 2. 后端返回的是对象数组
        // 使用类型断言处理选项
        formattedOptions = (questionData.options as any[]).map((opt: any) => {
          if (opt && typeof opt === 'object' && 'content' in opt) {
            return opt.content;
          }
          return String(opt);
        });
        
        console.log('处理后的选项数据:', formattedOptions);
      }
    }
    
    // 处理正确答案
    let correctAnswer = questionData.correctAnswer;
    if (questionData.type === 'multiple' && typeof correctAnswer === 'string') {
      correctAnswer = correctAnswer.split(',');
      console.log('处理后的多选答案:', correctAnswer);
    }
    
    // 合并数据到表单
    Object.assign(formData, {
      ...questionData,
      options: formattedOptions,
      correctAnswer: correctAnswer
    });
  }
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetForm()
  }
})

const handleTypeChange = () => {
  // 重置答案和选项
  if (formData.type === 'single' || formData.type === 'multiple') {
    if (!formData.options || formData.options.length < 4) {
      formData.options = ['', '', '', '']
    }
    formData.correctAnswer = formData.type === 'single' ? 'A' : ['A']
  } else if (formData.type === 'judgment') {
    formData.options = []
    formData.correctAnswer = 'true'
  } else {
    formData.options = []
    formData.correctAnswer = ''
  }
}

const addOption = () => {
  if (formData.options.length < 6) {
    formData.options.push('')
  }
}

const removeOption = (index: number) => {
  if (formData.options.length > 2) {
    formData.options.splice(index, 1)
    // 重新调整答案
    if (formData.type === 'single') {
      const currentAnswer = formData.correctAnswer as string
      const answerIndex = currentAnswer.charCodeAt(0) - 65
      if (answerIndex >= index) {
        const newIndex = Math.max(0, answerIndex - 1)
        formData.correctAnswer = String.fromCharCode(65 + newIndex)
      }
    } else if (formData.type === 'multiple') {
      const currentAnswers = formData.correctAnswer as string[]
      formData.correctAnswer = currentAnswers.filter(answer => {
        const answerIndex = answer.charCodeAt(0) - 65
        return answerIndex < index
      }).map(answer => {
        const answerIndex = answer.charCodeAt(0) - 65
        return answerIndex > index ? String.fromCharCode(answer.charCodeAt(0) - 1) : answer
      })
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 创建提交数据的副本，避免修改原始表单数据
    const submitData: any = { ...formData };
    console.log('提交前数据检查:', submitData);
    
    // 根据题目类型处理选项和答案
    if (formData.type === 'single' || formData.type === 'multiple') {
      // 过滤空选项
      const validOptions = formData.options.filter((opt: string) => opt.trim());
      
      // 注意：后端期望的是字符串数组，不是对象数组
      submitData.options = validOptions;
      
      // 处理多选题答案
      if (formData.type === 'multiple' && Array.isArray(formData.correctAnswer)) {
        submitData.correctAnswer = formData.correctAnswer.join(',');
      }
      
      console.log('处理后的选项和答案:', {
        options: submitData.options,
        correctAnswer: submitData.correctAnswer
      });
    } else if (formData.type === 'judgment') {
      // 判断题不需要选项
      submitData.options = [];
    } else {
      // 填空题和简答题不需要选项
      submitData.options = [];
    }

    emit('submit', submitData);
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

const resetForm = () => {
  Object.assign(formData, {
    bankId: '',
    type: 'single',
    title: '',
    options: ['', '', '', ''],
    correctAnswer: 'A',
    explanation: ''
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}
</script>

<style scoped>
.options-container {
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.option-label {
  min-width: 24px;
  font-weight: bold;
}
</style>