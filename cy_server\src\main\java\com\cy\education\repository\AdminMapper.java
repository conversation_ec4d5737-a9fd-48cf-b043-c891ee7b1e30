package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.model.entity.Admin;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 管理员Mapper接口
 */
@Repository
public interface AdminMapper extends BaseMapper<Admin> {
    
    /**
     * 分页查询管理员列表，并关联部门信息
     *
     * @param page 分页对象
     * @param keyword 关键词（用户名、真实姓名）
     * @param departmentId 部门ID
     * @param status 状态
     * @return 分页结果
     */
    IPage<Admin> selectAdminPage(Page<Admin> page, 
                               @Param("keyword") String keyword, 
                               @Param("departmentId") Integer departmentId, 
                               @Param("status") Integer status);
    
    /**
     * 根据ID查询管理员详情，并关联部门信息
     *
     * @param id 管理员ID
     * @return 管理员详情
     */
    Admin selectAdminById(@Param("id") Integer id);
} 