<template>
  <view class="bind-email-container">
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">绑定邮箱</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <view class="content-container">
      <view class="form-section">
        <view class="form-title">绑定邮箱</view>

        <view class="form-item">
          <view class="form-label">邮箱地址</view>
          <up-input
              v-model="formData.email"
              :border="false"
              customStyle="background: #f8f9fa; border-radius: 12px; padding: 16px;"
              placeholder="请输入邮箱地址"
              type="text"
          ></up-input>
        </view>

        <view class="submit-section">
          <up-button
              :disabled="!isFormValid"
              customStyle="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; border-radius: 12px; height: 48px;"
              text="确认绑定"
              type="primary"
              @click="handleSubmit"
          ></up-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {getVerificationCode, updateUserInfo} from '@/api/user'

export default {
  data() {
    return {
      formData: {
        email: '',
        code: ''
      },
      countdown: 0,
      timer: null
    }
  },

  computed: {
    isEmailValid() {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(this.formData.email)
    },

    isCodeValid() {
      return true
    },

    canSendCode() {
      return this.isEmailValid
    },

    isFormValid() {
      return this.isEmailValid && this.isCodeValid
    }
  },

  onUnload() {
  },

  methods: {
    async handleSubmit() {
      if (!this.isFormValid) {
        uni.showToast({
          title: '请完善信息',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({
          title: '绑定中...'
        })

        await updateUserInfo({
          id: uni.getStorageSync('userInfo').id,
          email: this.formData.email
        })

        uni.hideLoading()
        uni.showToast({
          title: '绑定成功',
          icon: 'success'
        })

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: error.message || '绑定失败',
          icon: 'none'
        })
      }
    },

    navigateBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/study/bind.scss";
</style>
