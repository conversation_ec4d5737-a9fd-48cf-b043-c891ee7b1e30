package com.cy.education.service.impl.exam;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BadRequestException;
import com.cy.education.exception.NotFoundException;
import com.cy.education.model.entity.*;
import com.cy.education.model.entity.exam.ExamAnswer;
import com.cy.education.model.entity.exam.ExamExam;
import com.cy.education.model.entity.exam.ExamRecord;
import com.cy.education.model.params.ExamRecordQueryParams;
import com.cy.education.model.vo.*;
import com.cy.education.repository.*;
import com.cy.education.service.DepartmentService;
import com.cy.education.service.exam.ExamRecordService;
import com.cy.education.utils.SecurityUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.cy.education.repository.ExamPaperMapper;
import com.cy.education.repository.ExamPaperQuestionMapper;
import com.cy.education.model.entity.exam.ExamPaper;

/**
 * 考试记录服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExamRecordServiceImpl implements ExamRecordService {

    private final ExamRecordMapper examRecordMapper;
    private final ExamAnswerMapper examAnswerMapper;
    private final ExamExamMapper examExamMapper;
    private final StudentMapper studentMapper;
    private final DepartmentService departmentService;
    private final ObjectMapper objectMapper;
    private final ExamPaperMapper examPaperMapper;
    private final ExamPaperQuestionMapper examPaperQuestionMapper;

    @Override
    public PageResponse<ExamRecordVO> listExamRecords(ExamRecordQueryParams params) {
        Page<ExamRecordVO> page = new Page<>(params.getPageNum(), params.getPageSize());
        IPage<ExamRecordVO> resultPage = examRecordMapper.selectExamRecordPage(
                page,
                params.getUserId(),
                params.getExamId(),
                params.getDepartmentId(),
                params.getKeyword(),
                params.getStatus(),
                params.getIsPassed(),
                params.getSortBy(),
                params.getSortOrder()
        );
        return PageResponse.of(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    }

    @Override
    public ExamRecordVO getExamRecordDetail(Integer id) {
        ExamRecordVO record = examRecordMapper.selectByRecordId(id);
        if (record == null) {
            throw new NotFoundException("考试记录不存在");
        }
        return record;
    }

    @Override
    public ExamRecordVO selectByExamIdAndUserId(Integer examId, Integer userId) {
        return examRecordMapper.selectByExamIdAndUserId(examId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer startExam(Integer examId, Integer userId) {
        // 查询考试是否存在
        ExamExam exam = examExamMapper.selectById(examId);
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        // 检查考试是否已发布且正在进行中
        if (!exam.getIsPublished()) {
            throw new BadRequestException("考试未发布，不能参加");
        }
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(exam.getStartTime())) {
            throw new BadRequestException("考试未开始，不能参加");
        }
        if (now.isAfter(exam.getEndTime())) {
            throw new BadRequestException("考试已结束，不能参加");
        }
        // 检查用户是否存在
        Student student = studentMapper.selectById(userId);
        if (student == null) {
            throw new NotFoundException("用户不存在");
        }
        // 检查用户是否已经参加过考试
        LambdaQueryWrapper<ExamRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamRecord::getExamId, examId)
                .eq(ExamRecord::getUserId, userId)
                .orderByDesc(ExamRecord::getAttemptNumber);
        ExamRecord existingRecord = examRecordMapper.selectOne(queryWrapper);
        int attemptNumber = 1;
        if (existingRecord != null) {
            if (existingRecord.getAttemptNumber() >= exam.getMaxAttempts()) {
                throw new BadRequestException("已达到最大考试次数限制");
            }
            if (existingRecord.getStatus() == 1) { // 进行中
                return existingRecord.getId(); // 返回现有的记录ID
            }
            attemptNumber = existingRecord.getAttemptNumber() + 1;
        }
        // 获取试卷总分
        ExamPaper paper = examPaperMapper.selectById(exam.getPaperId());
        int totalScore = paper != null && paper.getTotalScore() != null ? paper.getTotalScore() : 100;
        // 创建考试记录
        ExamRecord record = new ExamRecord();
        record.setExamId(examId);
        record.setUserId(userId);
        record.setDepartmentId(student.getDepartmentId());
        record.setTotalScore(totalScore);
        record.setStartTime(now);
        record.setStatus(1); // 进行中
        record.setAttemptNumber(attemptNumber);
        examRecordMapper.insert(record);
        return record.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamRecord submitExam(Integer recordId, String answersJson) {
        // 查询考试记录是否存在
        ExamRecord record = examRecordMapper.selectById(recordId);
        if (record == null) {
            throw new NotFoundException("考试记录不存在");
        }
        // 检查考试状态是否为进行中
        if (record.getStatus() != 1) {
            throw new BadRequestException("考试未在进行中，不能提交");
        }
        // 查询考试信息
        ExamExam exam = examExamMapper.selectById(record.getExamId());
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        // 获取试卷题目及分值
        List<ExamPaperQuestionVO> paperQuestions = examPaperQuestionMapper.selectQuestionsByPaperId(exam.getPaperId());
        Map<Integer, ExamPaperQuestionVO> questionMap = paperQuestions.stream().collect(Collectors.toMap(q -> q.getQuestion().getId(), q -> q));
        // 更新考试记录
        LocalDateTime now = LocalDateTime.now();
        record.setEndTime(now);
        record.setDuration((int) Duration.between(record.getStartTime(), now).toMinutes());
        record.setStatus(2); // 已完成
        try {
            // 解析答案JSON
            List<Map<String, Object>> answers = objectMapper.readValue(answersJson, List.class);
            int totalScore = 0;
            List<ExamAnswer> answerEntities = new ArrayList<>();
            for (Map<String, Object> answer : answers) {
                ExamAnswer answerEntity = new ExamAnswer();
                answerEntity.setRecordId(recordId);
                Integer questionId = Integer.valueOf(answer.get("questionId").toString());
                answerEntity.setQuestionId(questionId);
                String userAnswer = answer.get("answer").toString();
                answerEntity.setAnswer(userAnswer);
                // 判分逻辑
                ExamPaperQuestionVO paperQuestion = questionMap.get(questionId);
                int score = 0;
                boolean isCorrect = false;
                if (paperQuestion != null && paperQuestion.getQuestion() != null) {
                    ExamQuestionVO q = paperQuestion.getQuestion();
                    String correct = q.getCorrectAnswer();
                    String type = q.getType();
                    int qScore = paperQuestion.getScore() != null ? paperQuestion.getScore() : 0;
                    if ("single".equals(type) || "judgment".equals(type)) {
                        isCorrect = userAnswer.equalsIgnoreCase(correct);
                        score = isCorrect ? qScore : 0;
                    } else if ("multiple".equals(type)) {
                        // 多选题答案用逗号分隔，顺序无关
                        List<String> userAnsList = List.of(userAnswer.split(","));
                        List<String> correctList = List.of(correct.split(","));
                        isCorrect = userAnsList.size() == correctList.size() && userAnsList.containsAll(correctList);
                        score = isCorrect ? qScore : 0;
                    } else if ("fill".equals(type)) {
                        // 填空题可用trim后比对，或后续人工批阅
                        isCorrect = userAnswer.trim().equalsIgnoreCase(correct.trim());
                        score = isCorrect ? qScore : 0;
                    } else if ("essay".equals(type)) {
                        // 简答题默认0分，后续人工批阅
                        isCorrect = false;
                        score = 0;
                    }
                }
                answerEntity.setIsCorrect(isCorrect);
                answerEntity.setScore(score);
                totalScore += score;
                answerEntities.add(answerEntity);
            }
            // 批量插入答题记录
            if (!answerEntities.isEmpty()) {
                examAnswerMapper.batchInsert(answerEntities);
            }
            // 更新考试记录的分数和是否通过
            record.setScore(totalScore);
            record.setIsPassed(totalScore >= exam.getPassingScore());
        } catch (JsonProcessingException e) {
            log.error("解析答案JSON失败", e);
            throw new BadRequestException("答案格式不正确");
        }
        examRecordMapper.updateById(record);
        return record;
    }

    /**
     * 获取考试统计信息
     *
     * @param examId 考试ID，如果为null则统计所有考试
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getExamStats(Integer examId) {
        Map<String, Object> statistics = new HashMap<>();

        // 1. 基础统计信息
        ExamExam exam = null;
        if (examId != null) {
            exam = examExamMapper.selectById(examId);
            if (exam == null) {
                throw new NotFoundException("考试不存在");
            }
        }

        // 2. 构建查询条件
        LambdaQueryWrapper<ExamRecord> recordQueryWrapper = new LambdaQueryWrapper<>();
        if (examId != null) {
            recordQueryWrapper.eq(ExamRecord::getExamId, examId);
        }

        // 3. 参与人数统计
        long totalParticipants = examRecordMapper.selectCount(recordQueryWrapper);
        statistics.put("totalParticipants", totalParticipants);

        // 4. 已完成考试的记录
        LambdaQueryWrapper<ExamRecord> completedQueryWrapper = new LambdaQueryWrapper<>();
        completedQueryWrapper.eq(ExamRecord::getStatus, 2); // 已完成
        if (examId != null) {
            completedQueryWrapper.eq(ExamRecord::getExamId, examId);
        }
        List<ExamRecord> completedRecords = examRecordMapper.selectList(completedQueryWrapper);
        int completedCount = completedRecords.size();
        statistics.put("completedCount", completedCount);

        // 5. 进行中的考试记录
        LambdaQueryWrapper<ExamRecord> ongoingQueryWrapper = new LambdaQueryWrapper<>();
        ongoingQueryWrapper.eq(ExamRecord::getStatus, 1); // 进行中
        if (examId != null) {
            ongoingQueryWrapper.eq(ExamRecord::getExamId, examId);
        }
        long ongoingCount = examRecordMapper.selectCount(ongoingQueryWrapper);
        statistics.put("ongoingCount", ongoingCount);

        // 6. 计算平均分、通过率等统计指标
        double averageScore = 0;
        double passRate = 0;
        int highestScore = 0;
        int lowestScore = 0;
        double medianScore = 0;

        if (completedCount > 0) {
            // 计算平均分
            int totalScore = completedRecords.stream()
                    .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                    .sum();
            averageScore = (double) totalScore / completedCount;

            // 计算通过率
            long passedCount = completedRecords.stream()
                    .filter(r -> r.getIsPassed() != null && r.getIsPassed())
                    .count();
            passRate = (double) passedCount / completedCount;

            // 计算最高分和最低分
            highestScore = completedRecords.stream()
                    .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                    .max()
                    .orElse(0);

            lowestScore = completedRecords.stream()
                    .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                    .min()
                    .orElse(0);

            // 计算中位数
            List<Integer> scores = completedRecords.stream()
                    .map(r -> r.getScore() != null ? r.getScore() : 0)
                    .sorted()
                    .collect(Collectors.toList());
            int size = scores.size();
            if (size % 2 == 0) {
                medianScore = (scores.get(size / 2 - 1) + scores.get(size / 2)) / 2.0;
            } else {
                medianScore = scores.get(size / 2);
            }
        }

        statistics.put("averageScore", Math.round(averageScore * 10) / 10.0);
        statistics.put("passRate", Math.round(passRate * 1000) / 10.0); // 转为百分比
        statistics.put("highestScore", highestScore);
        statistics.put("lowestScore", lowestScore);
        statistics.put("medianScore", Math.round(medianScore * 10) / 10.0);

        // 7. 部门统计
        List<Map<String, Object>> departmentStats = getDepartmentStatistics(completedRecords);
        statistics.put("departmentStats", departmentStats);

        // 8. 分数分布统计
        Map<String, Object> scoreDistribution = getScoreDistribution(completedRecords);
        statistics.put("scoreDistribution", scoreDistribution);

        // 9. 完成时间统计
        Map<String, Object> durationDistribution = getDurationDistribution(completedRecords, exam);
        statistics.put("durationDistribution", durationDistribution);

        // 10. 考试次数统计
        Map<String, Object> attemptStats = getAttemptStatistics(examId);
        statistics.put("attemptStats", attemptStats);

        // 11. 时间趋势统计（最近7天的考试情况）
//        Map<String, Object> timeTrendStats = getTimeTrendStatistics(examId);
//        statistics.put("timeTrendStats", timeTrendStats);

        return statistics;
    }

    /**
     * 获取用户考试统计信息
     *
     * @return 用户考试统计信息
     */
    @Override
    public Map<String, Object> getUserExamStats() {
        Integer currentUserId = SecurityUtil.getCurrentUserId();
        Integer userId = SecurityUtil.getCurrentUserId();
        Student student = studentMapper.selectById(userId);
        if (student == null) {
            throw new NotFoundException("用户不存在");
        }

        Map<String, Object> stats = new HashMap<>();

        // 1. 基础统计 - 从exam表获取总考试数，从record表获取参与情况

        int totalExams = examExamMapper.countExamsByDepartmentId(student.getDepartmentId());

        LambdaQueryWrapper<ExamRecord> userRecordWrapper = new LambdaQueryWrapper<>();
        userRecordWrapper.eq(ExamRecord::getUserId, currentUserId);
        List<ExamRecord> userRecords = examRecordMapper.selectList(userRecordWrapper);

        int participatedCount = userRecords.size();
        int completedCount = (int) userRecords.stream().filter(r -> r.getStatus() == 2).count();
        int ongoingCount = (int) userRecords.stream().filter(r -> r.getStatus() == 1).count();
        int passedCount = (int) userRecords.stream().filter(r -> r.getIsPassed() != null && r.getIsPassed()).count();
        int notParticipatedCount = totalExams - participatedCount;

        stats.put("totalExams", totalExams);
        stats.put("participatedExams", participatedCount);
        stats.put("notParticipatedExams", notParticipatedCount);
        stats.put("completedExams", completedCount);
        stats.put("ongoingExams", ongoingCount);
        stats.put("passedExams", passedCount);

        // 2. 分数统计
        if (completedCount > 0) {
            double averageScore = userRecords.stream()
                    .filter(r -> r.getScore() != null)
                    .mapToInt(ExamRecord::getScore)
                    .average()
                    .orElse(0);

            int highestScore = userRecords.stream()
                    .filter(r -> r.getScore() != null)
                    .mapToInt(ExamRecord::getScore)
                    .max()
                    .orElse(0);

            int lowestScore = userRecords.stream()
                    .filter(r -> r.getScore() != null)
                    .mapToInt(ExamRecord::getScore)
                    .min()
                    .orElse(0);

            double passRate = (double) passedCount / completedCount;

            stats.put("averageScore", Math.round(averageScore * 10) / 10.0);
            stats.put("highestScore", highestScore);
            stats.put("lowestScore", lowestScore);
            stats.put("passRate", Math.round(passRate * 1000) / 10.0);
        } else {
            stats.put("averageScore", 0);
            stats.put("highestScore", 0);
            stats.put("lowestScore", 0);
            stats.put("passRate", 0);
        }

        return stats;
    }

    @Override
    public Map<String, Object> getExamResult(Integer recordId) {
        ExamRecord record = examRecordMapper.selectById(recordId);
        if (record == null) throw new NotFoundException("考试记录不存在");
        // 查询考试信息
        ExamExam exam = examExamMapper.selectById(record.getExamId());
        if (exam == null) {
            throw new NotFoundException("考试不存在");
        }
        // 查询答题详情
        List<ExamAnswerVO> answers = examAnswerMapper.selectByRecordId(recordId);
        // 查询试卷题目
        List<ExamPaperQuestionVO> paperQuestions = examPaperQuestionMapper.selectQuestionsByPaperId(exam.getPaperId());
        Map<Integer, ExamAnswerVO> answerMap = answers.stream().collect(Collectors.toMap(ExamAnswerVO::getQuestionId, a -> a));
        List<Map<String, Object>> questions = new ArrayList<>();
        int correctCount = 0, wrongCount = 0, unansweredCount = 0;
        int idx = 1;
        for (ExamPaperQuestionVO pq : paperQuestions) {
            ExamQuestionVO q = pq.getQuestion();
            ExamAnswerVO ans = answerMap.get(q.getId());
            String userAnswer = ans != null ? ans.getAnswer() : null;
            Boolean isCorrect = ans != null ? ans.getIsCorrect() : null;
            if (isCorrect != null && isCorrect) correctCount++;
            else if (isCorrect != null && !isCorrect) wrongCount++;
            else unansweredCount++;
            // 选项处理
            List<Map<String, String>> options = new ArrayList<>();
            if (q.getOptions() != null) {
                try {
                    String optStr = q.getOptions().toString();
                    List<Map<String, Object>> opts = objectMapper.readValue(optStr, List.class);
                    for (Map<String, Object> opt : opts) {
                        Map<String, String> optionMap = new HashMap<>();
                        optionMap.put("key", String.valueOf(opt.getOrDefault("id", "")));
                        optionMap.put("text", String.valueOf(opt.getOrDefault("content", "")));
                        options.add(optionMap);
                    }
                } catch (Exception e) {
                    log.error("解析选项失败", e);
                    throw new BadRequestException("题目选项格式不正确");
                }
            }
            Map<String, Object> questionMap = new HashMap<>();
            questionMap.put("questionId", q.getId());
            questionMap.put("questionNumber", idx);
            questionMap.put("type", q.getType());
            questionMap.put("typeName", getTypeName(q.getType()));
            questionMap.put("question", q.getTitle());
            questionMap.put("options", options);
            questionMap.put("userAnswer", userAnswer);
            questionMap.put("correctAnswer", q.getCorrectAnswer());
            questionMap.put("isCorrect", isCorrect);
            questionMap.put("explanation", q.getExplanation());
            questions.add(questionMap);
            idx++;
        }
        int totalCount = paperQuestions.size();
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", totalCount);
        result.put("correctCount", correctCount);
        result.put("wrongCount", wrongCount);
        result.put("unansweredCount", unansweredCount);
        result.put("usedTime", record.getDuration());
        result.put("questions", questions);
        return result;
    }

    private String getTypeName(String type) {
        switch (type) {
            case "single":
                return "单选题";
            case "multiple":
                return "多选题";
            case "judgment":
            case "judge":
                return "判断题";
            case "fill":
                return "填空题";
            case "essay":
                return "简答题";
            default:
                return "未知题型";
        }
    }

    /**
     * 递归展平部门树结构
     *
     * @param departments 部门列表
     * @param result      结果列表
     */
    private void flattenDepartmentTree(List<Department> departments, List<Department> result) {
        if (departments == null || departments.isEmpty()) {
            return;
        }

        for (Department department : departments) {
            result.add(department);
            if (department.getChildren() != null && !department.getChildren().isEmpty()) {
                flattenDepartmentTree(department.getChildren(), result);
            }
        }
    }

    /**
     * 获取部门统计信息
     */
    private List<Map<String, Object>> getDepartmentStatistics(List<ExamRecord> completedRecords) {
        List<Map<String, Object>> departmentStats = new ArrayList<>();

        // 获取所有部门
        List<Department> departments = new ArrayList<>();
        List<Department> topDepartments = departmentService.listDepartments(null, true);
        flattenDepartmentTree(topDepartments, departments);

        // 创建部门ID到部门名称的映射
        Map<Integer, String> departmentMap = departments.stream()
                .collect(Collectors.toMap(Department::getId, Department::getName, (a, b) -> a));

        // 按部门ID分组统计
        Map<Integer, List<ExamRecord>> recordsByDepartment = completedRecords.stream()
                .filter(r -> r.getDepartmentId() != null)
                .collect(Collectors.groupingBy(ExamRecord::getDepartmentId));

        // 计算每个部门的统计数据
        for (Map.Entry<Integer, List<ExamRecord>> entry : recordsByDepartment.entrySet()) {
            Integer deptId = entry.getKey();
            List<ExamRecord> deptRecords = entry.getValue();

            if (deptRecords.isEmpty()) {
                continue;
            }

            String departmentName = departmentMap.getOrDefault(deptId, "未知部门");
            int participantCount = deptRecords.size();

            // 平均分
            double deptAvgScore = deptRecords.stream()
                    .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                    .average()
                    .orElse(0);

            // 通过率
            long deptPassedCount = deptRecords.stream()
                    .filter(r -> r.getIsPassed() != null && r.getIsPassed())
                    .count();
            double deptPassRate = (double) deptPassedCount / participantCount;

            // 最高分和最低分
            int deptHighestScore = deptRecords.stream()
                    .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                    .max()
                    .orElse(0);

            int deptLowestScore = deptRecords.stream()
                    .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                    .min()
                    .orElse(0);

            // 平均用时
            double deptAvgDuration = deptRecords.stream()
                    .filter(r -> r.getDuration() != null)
                    .mapToInt(ExamRecord::getDuration)
                    .average()
                    .orElse(0);

            Map<String, Object> deptStat = new HashMap<>();
            deptStat.put("departmentId", deptId);
            deptStat.put("departmentName", departmentName);
            deptStat.put("participantCount", participantCount);
            deptStat.put("averageScore", Math.round(deptAvgScore * 10) / 10.0);
            deptStat.put("passRate", Math.round(deptPassRate * 1000) / 10.0);
            deptStat.put("highestScore", deptHighestScore);
            deptStat.put("lowestScore", deptLowestScore);
            deptStat.put("averageDuration", Math.round(deptAvgDuration * 10) / 10.0);

            departmentStats.add(deptStat);
        }

        return departmentStats;
    }

    /**
     * 获取分数分布统计
     */
    private Map<String, Object> getScoreDistribution(List<ExamRecord> completedRecords) {
        Map<String, Object> scoreDistribution = new HashMap<>();
        scoreDistribution.put("excellent", 0); // 优秀(90-100)
        scoreDistribution.put("good", 0);      // 良好(80-89)
        scoreDistribution.put("moderate", 0);  // 中等(70-79)
        scoreDistribution.put("pass", 0);      // 及格(60-69)
        scoreDistribution.put("fail", 0);      // 不及格(0-59)

        for (ExamRecord record : completedRecords) {
            int score = record.getScore() != null ? record.getScore() : 0;
            if (score >= 90) {
                scoreDistribution.put("excellent", (Integer) scoreDistribution.get("excellent") + 1);
            } else if (score >= 80) {
                scoreDistribution.put("good", (Integer) scoreDistribution.get("good") + 1);
            } else if (score >= 70) {
                scoreDistribution.put("moderate", (Integer) scoreDistribution.get("moderate") + 1);
            } else if (score >= 60) {
                scoreDistribution.put("pass", (Integer) scoreDistribution.get("pass") + 1);
            } else {
                scoreDistribution.put("fail", (Integer) scoreDistribution.get("fail") + 1);
            }
        }

        return scoreDistribution;
    }

    /**
     * 获取完成时间分布统计
     */
    private Map<String, Object> getDurationDistribution(List<ExamRecord> completedRecords, ExamExam exam) {
        Map<String, Object> durationDistribution = new HashMap<>();
        durationDistribution.put("veryFast", 0);    // 非常快(小于总时长的25%)
        durationDistribution.put("fast", 0);        // 较快(总时长的25%-50%)
        durationDistribution.put("normal", 0);      // 一般(总时长的50%-75%)
        durationDistribution.put("slow", 0);        // 较慢(总时长的75%-100%)
        durationDistribution.put("verySlow", 0);    // 非常慢(超过总时长)

        // 获取考试时长
        Integer examDuration = 60; // 默认60分钟
        if (exam != null && exam.getDuration() != null) {
            examDuration = exam.getDuration();
        }

        for (ExamRecord record : completedRecords) {
            if (record.getDuration() == null) continue;

            int duration = record.getDuration();
            double durationRatio = (double) duration / examDuration;

            if (durationRatio < 0.25) {
                durationDistribution.put("veryFast", (Integer) durationDistribution.get("veryFast") + 1);
            } else if (durationRatio < 0.5) {
                durationDistribution.put("fast", (Integer) durationDistribution.get("fast") + 1);
            } else if (durationRatio < 0.75) {
                durationDistribution.put("normal", (Integer) durationDistribution.get("normal") + 1);
            } else if (durationRatio <= 1.0) {
                durationDistribution.put("slow", (Integer) durationDistribution.get("slow") + 1);
            } else {
                durationDistribution.put("verySlow", (Integer) durationDistribution.get("verySlow") + 1);
            }
        }

        return durationDistribution;
    }

    /**
     * 获取考试次数统计
     */
    private Map<String, Object> getAttemptStatistics(Integer examId) {
        Map<String, Object> attemptStats = new HashMap<>();

        LambdaQueryWrapper<ExamRecord> queryWrapper = new LambdaQueryWrapper<>();
        if (examId != null) {
            queryWrapper.eq(ExamRecord::getExamId, examId);
        }

        List<ExamRecord> allRecords = examRecordMapper.selectList(queryWrapper);

        // 按用户分组统计考试次数
        Map<Integer, Long> userAttempts = allRecords.stream()
                .collect(Collectors.groupingBy(ExamRecord::getUserId, Collectors.counting()));

        double averageAttempts = userAttempts.values().stream()
                .mapToLong(Long::longValue)
                .average()
                .orElse(0);

        int maxAttempts = userAttempts.values().stream()
                .mapToInt(Long::intValue)
                .max()
                .orElse(0);

        int minAttempts = userAttempts.values().stream()
                .mapToInt(Long::intValue)
                .min()
                .orElse(0);

        attemptStats.put("averageAttempts", Math.round(averageAttempts * 10) / 10.0);
        attemptStats.put("maxAttempts", maxAttempts);
        attemptStats.put("minAttempts", minAttempts);
        attemptStats.put("totalUsers", userAttempts.size());

        return attemptStats;
    }

    /**
     * 获取时间趋势统计
     */
    private Map<String, Object> getTimeTrendStatistics(Integer examId) {
        Map<String, Object> timeTrendStats = new HashMap<>();

        // 最近7天的考试完成情况
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);

        LambdaQueryWrapper<ExamRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamRecord::getStatus, 2) // 已完成的考试
                .ge(ExamRecord::getEndTime, sevenDaysAgo);
        if (examId != null) {
            queryWrapper.eq(ExamRecord::getExamId, examId);
        }

        List<ExamRecord> recentRecords = examRecordMapper.selectList(queryWrapper);

        // 按日期分组统计
        Map<String, Integer> dailyCompletions = new HashMap<>();
        Map<String, Integer> dailyPasses = new HashMap<>();

        for (ExamRecord record : recentRecords) {
            String dateKey = record.getEndTime().toLocalDate().toString();
            dailyCompletions.put(dateKey, dailyCompletions.getOrDefault(dateKey, 0) + 1);

            if (record.getIsPassed() != null && record.getIsPassed()) {
                dailyPasses.put(dateKey, dailyPasses.getOrDefault(dateKey, 0) + 1);
            }
        }

        timeTrendStats.put("dailyCompletions", dailyCompletions);
        timeTrendStats.put("dailyPasses", dailyPasses);

        return timeTrendStats;
    }

}
