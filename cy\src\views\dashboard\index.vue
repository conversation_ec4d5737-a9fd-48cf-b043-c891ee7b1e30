<template>
  <div class="dashboard" v-loading="loading">
    <!-- 顶部统计卡片 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-card-header">
          <div>
            <div class="stats-card-title">总学员数</div>
            <div class="stats-card-value">{{ dashboardData.totalStudents || 0 }}</div>
            <div class="stats-card-desc">
              <span style="color: #909399; margin-left: 8px;">注册学员总数</span>
            </div>
          </div>
          <div class="stats-card-icon primary">
            <el-icon><User /></el-icon>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-card-header">
          <div>
            <div class="stats-card-title">课程总数</div>
            <div class="stats-card-value">{{ dashboardData.totalCourses || 0 }}</div>
            <div class="stats-card-desc">
              <span style="color: #909399; margin-left: 8px;">已发布课程数量</span>
            </div>
          </div>
          <div class="stats-card-icon success">
            <el-icon><Document /></el-icon>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-card-header">
          <div>
            <div class="stats-card-title">考试总数</div>
            <div class="stats-card-value">{{ dashboardData.totalExams || 0 }}</div>
            <div class="stats-card-desc">
              <span style="color: #909399; margin-left: 8px;">已发布考试数量</span>
            </div>
          </div>
          <div class="stats-card-icon warning">
            <el-icon><EditPen /></el-icon>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-card-header">
          <div>
            <div class="stats-card-title">论坛帖子</div>
            <div class="stats-card-value">{{ dashboardData.totalPosts || 0 }}</div>
            <div class="stats-card-desc">
              <span style="color: #909399; margin-left: 8px;">论坛帖子总数</span>
            </div>
          </div>
          <div class="stats-card-icon info">
            <el-icon><ChatDotRound /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="page-toolbar">
      <div class="toolbar-left">
        <h3>数据概览</h3>
      </div>
      <div class="toolbar-right">
        <el-select v-model="timeRange" placeholder="选择时间范围" style="width: 120px;">
          <el-option label="今天" value="today" />
          <el-option label="本周" value="week" />
          <el-option label="本月" value="month" />
          <el-option label="自定义" value="custom" />
        </el-select>
        <el-button type="primary" :icon="Refresh" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <el-row :gutter="24">
      <el-col :span="12">
        <div class="chart-container">
          <div class="chart-header">
            <h4>最新学员</h4>
          </div>
          <div class="chart-content">
            <el-table :data="recentStudents" style="width: 100%" max-height="300">
              <el-table-column prop="name" label="姓名" width="100" />
              <el-table-column prop="departmentName" label="部门" width="120" />
              <el-table-column prop="createTime" label="注册时间" />
            </el-table>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-container">
          <div class="chart-header">
            <h4>最新课程</h4>
          </div>
          <div class="chart-content">
            <el-table :data="recentCourses" style="width: 100%" max-height="300">
              <el-table-column prop="name" label="课程名称" />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'published' ? 'success' : 'warning'">
                    {{ row.status === 'published' ? '已发布' : '草稿' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="120" />
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="24" style="margin-top: 24px;">
      <el-col :span="12">
        <div class="chart-container">
          <div class="chart-header">
            <h4>最新考试</h4>
          </div>
          <div class="chart-content">
            <el-table :data="recentExams" style="width: 100%" max-height="300">
              <el-table-column prop="title" label="考试名称" />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="getExamStatusType(row.status)">
                    {{ getExamStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="startTime" label="开始时间" width="120" />
            </el-table>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-container">
          <div class="chart-header">
            <h4>最新帖子</h4>
          </div>
          <div class="chart-content">
            <el-table :data="recentPosts" style="width: 100%" max-height="300">
              <el-table-column prop="title" label="帖子标题" />
              <el-table-column prop="author" label="作者" width="100" />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="getPostStatusType(row.status)">
                    {{ getPostStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="发布时间" width="120" />
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <div class="quick-actions-header">
        <h4>快捷操作</h4>
      </div>
      <div class="quick-actions-content">
        <el-button type="primary" :icon="Plus" size="large" @click="goToPage('/student/info')">
          学员管理
        </el-button>
        <el-button type="success" :icon="DocumentAdd" size="large" @click="goToPage('/course/management')">
          课程管理
        </el-button>
        <el-button type="warning" :icon="EditPen" size="large" @click="goToPage('/exam/management')">
          考试管理
        </el-button>
        <el-button type="info" :icon="ChatDotRound" size="large" @click="goToPage('/forum/post')">
          论坛管理
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  User,
  Document,
  Refresh,
  EditPen,
  DocumentAdd,
  ChatDotRound,
  Plus
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getDashboardStatistics, getRecentData } from '@/api/dashboard'

const router = useRouter()
const loading = ref(false)
const timeRange = ref('month')

// Dashboard数据
const dashboardData = ref({
  totalStudents: 0,
  totalCourses: 0,
  totalExams: 0,
  totalPosts: 0
})

// 最新数据
const recentStudents = ref<any[]>([])
const recentCourses = ref<any[]>([])
const recentExams = ref<any[]>([])
const recentPosts = ref<any[]>([])

// 页面跳转
const goToPage = (path: string) => {
  router.push(path)
}

// 状态转换方法
const getExamStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',    // 草稿
    1: 'warning', // 未开始
    2: 'success', // 进行中
    3: 'info'     // 已结束
  }
  return typeMap[status] || 'info'
}

const getExamStatusText = (status: number) => {
  const textMap: Record<number, string> = {
    0: '草稿',
    1: '未开始',
    2: '进行中',
    3: '已结束'
  }
  return textMap[status] || '未知'
}

const getPostStatusType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'warning', // 待审核
    1: 'success', // 已发布
    2: 'danger',  // 已拒绝
    3: 'info'     // 已删除
  }
  return typeMap[status] || 'info'
}

const getPostStatusText = (status: number) => {
  const textMap: Record<number, string> = {
    0: '待审核',
    1: '已发布',
    2: '已拒绝',
    3: '已删除'
  }
  return textMap[status] || '未知'
}

// 加载Dashboard数据
const loadDashboardData = async () => {
  loading.value = true
  try {
    // 并行加载统计数据和最新数据
    const [statisticsRes, recentDataRes] = await Promise.all([
      getDashboardStatistics(),
      getRecentData()
    ])

    // 更新统计数据
    dashboardData.value = statisticsRes

    // 更新最新数据
    recentStudents.value = (recentDataRes.recentStudents || []).map(student => ({
      ...student,
      createTime: student.createTime?.substring(0, 10) || ''
    }))

    recentCourses.value = (recentDataRes.recentCourses || []).map(course => ({
      ...course,
      createTime: course.createTime?.substring(0, 10) || ''
    }))

    recentExams.value = (recentDataRes.recentExams || []).map(exam => ({
      ...exam,
      startTime: exam.startTime?.substring(0, 10) || ''
    }))

    recentPosts.value = (recentDataRes.recentPosts || []).map(post => ({
      ...post,
      createTime: post.createTime?.substring(0, 10) || ''
    }))

  } catch (error) {
    console.error('加载Dashboard数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadDashboardData()
  ElMessage.success('数据已刷新')
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  background: transparent;
}

.chart-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.chart-content {
  min-height: 300px;
}

.quick-actions {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-top: 24px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}

.quick-actions-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.quick-actions-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.quick-actions-content {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-progress-bar__outer) {
  background-color: #f5f5f5;
}
</style> 