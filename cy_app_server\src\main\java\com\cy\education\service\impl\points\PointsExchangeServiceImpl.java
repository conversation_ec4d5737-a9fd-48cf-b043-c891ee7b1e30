package com.cy.education.service.impl.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.points.PointsExchange;
import com.cy.education.model.entity.points.PointsProduct;
import com.cy.education.model.params.PointsExchangeQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.repository.PointsExchangeMapper;
import com.cy.education.service.points.PointsExchangeService;
import com.cy.education.service.points.PointsProductService;
import com.cy.education.service.points.PointsRecordService;
import com.cy.education.utils.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 积分兑换服务实现类
 */
@Service
public class PointsExchangeServiceImpl extends ServiceImpl<PointsExchangeMapper, PointsExchange> implements PointsExchangeService {

    @Autowired
    private PointsProductService pointsProductService;

    @Autowired
    private PointsRecordService pointsRecordService;

    @Override
    public IPage<PointsExchange> page(PointsExchangeQueryParam param) {
        // 创建分页对象
        Page<PointsExchange> page = new Page<>(param.getPageNum(), param.getPageSize());
        // 调用带详情的分页查询
        return baseMapper.pageWithDetails(page, SecurityUtil.getCurrentUserId(), param.getProductId(), param.getStatus());
    }

    @Override
    public PointsExchange getById(Integer id) {
        // 查询基本信息
        PointsExchange exchange = super.getById(id);

        if (exchange != null) {
            // 查询详细信息
            Page<PointsExchange> page = new Page<>(1, 1);
            IPage<PointsExchange> result = baseMapper.pageWithDetails(page, exchange.getUserId(), exchange.getProductId(), null);

            if (result.getRecords().size() > 0) {
                PointsExchange detailExchange = result.getRecords().get(0);
                exchange.setUserName(detailExchange.getUserName());
                exchange.setDepartmentName(detailExchange.getDepartmentName());
                exchange.setProductName(detailExchange.getProductName());
                exchange.setGoodsImage(detailExchange.getGoodsImage());
            }
        }

        return exchange;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> create(PointsExchange exchange) {
        exchange.setUserId(SecurityUtil.getCurrentUserId());
        // 获取商品信息
        PointsProduct product = pointsProductService.getById(exchange.getProductId());
        if (product == null) {
            return ApiResponse.error("商品不存在");
        }

        // 检查商品状态
        if (!"active".equals(product.getStatus())) {
            return ApiResponse.error("商品已下架，无法兑换");
        }

        // 检查库存
        if (product.getStock() <= 0) {
            return ApiResponse.error("商品库存不足");
        }

        exchange.setPoints(product.getPoints());

        // 获取用户积分余额
        Integer userBalance = pointsRecordService.getUserBalance(exchange.getUserId());

        // 检查积分是否足够
        if (userBalance < exchange.getPoints()) {
            return ApiResponse.error("积分不足，无法兑换");
        }
        exchange.setStatus("待审核");

        // 保存兑换记录
        boolean saved = save(exchange);

        if (saved) {
            // 减少商品库存
            boolean decreased = pointsProductService.decreaseStock(product.getId(), 1);

            if (decreased) {
                // 扣减用户积分
                boolean recorded = pointsRecordService.recordPointsChange(
                        exchange.getUserId(),
                        -exchange.getPoints(),
                        "exchange",
                        "兑换商品：" + product.getName() + " x" + 1,
                        product.getId(),
                        "product",
                        "system"
                );

                if (recorded) {
                    return ApiResponse.success(Map.of(
                            "id", exchange.getId(),
                            "success", true
                    ));
                }
            }

            // 如果库存减少或积分记录失败，回滚事务
            throw new RuntimeException("兑换处理失败");
        }

        return ApiResponse.error("创建兑换记录失败");
    }
}
