declare module '@wangeditor/editor-for-vue' {
  import { ComponentOptions } from 'vue'
  
  export const Editor: ComponentOptions
  export const Toolbar: ComponentOptions
}

declare module '@wangeditor/editor' {
  export interface IDomEditor {
    destroy: () => void
  }
  
  export interface IEditorConfig {
    placeholder?: string
    readOnly?: boolean
    autoFocus?: boolean
    scroll?: boolean
    maxLength?: number
    MENU_CONF?: Record<string, any>
    customPaste?: (editor: IDomEditor, event: ClipboardEvent) => boolean | void
    onCreated?: (editor: IDomEditor) => void
    onChange?: (editor: IDomEditor) => void
    onDestroyed?: (editor: IDomEditor) => void
    onFocus?: (editor: IDomEditor) => void
    onBlur?: (editor: IDomEditor) => void
    onMaxLength?: (editor: IDomEditor) => void
  }
  
  export interface IToolbarConfig {
    toolbarKeys?: string[]
    excludeKeys?: string[]
    insertKeys?: {
      index: number | undefined
      keys: string[]
    }
    modalAppendToBody?: boolean
  }
} 