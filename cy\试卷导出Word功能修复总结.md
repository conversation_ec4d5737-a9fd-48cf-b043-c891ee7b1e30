# 试卷导出Word功能修复总结

## 问题描述

### 1. 答案位置选项无效
**现象**: 无论在前端选择"题目内"还是"附录"，答案都显示在题目下方。
**原因**: 后端没有处理`answerPosition`参数，始终使用内联模式。

### 2. 导出的试卷没有题目选项
**现象**: 选择题导出时只有题目标题，没有A、B、C、D选项。
**原因**: 题目选项存储为JSON格式，导出时没有正确解析和格式化。

## 修复方案

### ✅ 1. 修复答案位置选项功能

#### 后端参数处理
```java
// 获取导出选项
String format = (String) options.getOrDefault("format", "docx");
boolean includeAnswers = (Boolean) options.getOrDefault("includeAnswers", true);
boolean includeExplanations = (Boolean) options.getOrDefault("includeExplanations", true);
String answerPosition = (String) options.getOrDefault("answerPosition", "inline"); // 新增

if ("docx".equals(format)) {
    exportPaperAsWord(paper, paperId, includeAnswers, includeExplanations, answerPosition, response);
} else {
    exportPaperAsText(paper, paperId, includeAnswers, includeExplanations, answerPosition, response);
}
```

#### 内联模式（inline）
答案和解析显示在每个题目下方：
```java
if ("inline".equals(answerPosition)) {
    // 答案（如果需要）
    if (includeAnswers && question.getQuestion() != null && question.getQuestion().getCorrectAnswer() != null) {
        XWPFParagraph answerParagraph = document.createParagraph();
        XWPFRun answerRun = answerParagraph.createRun();
        answerRun.setText("【参考答案】" + question.getQuestion().getCorrectAnswer());
        answerRun.setFontFamily("宋体");
        answerRun.setColor("0000FF"); // 蓝色
    }
    
    // 解析（如果需要）
    if (includeExplanations && question.getQuestion() != null && question.getQuestion().getExplanation() != null) {
        XWPFParagraph analysisParagraph = document.createParagraph();
        XWPFRun analysisRun = analysisParagraph.createRun();
        analysisRun.setText("【解析】" + question.getQuestion().getExplanation());
        analysisRun.setFontFamily("宋体");
        analysisRun.setColor("008000"); // 绿色
    }
}
```

#### 附录模式（appendix）
答案和解析统一显示在文档末尾：
```java
if ("appendix".equals(answerPosition)) {
    // 添加答案部分
    if (includeAnswers && !answerList.isEmpty()) {
        // 添加分页符
        XWPFParagraph pageBreak = document.createParagraph();
        pageBreak.setPageBreak(true);
        
        // 答案标题
        XWPFParagraph answerTitleParagraph = document.createParagraph();
        answerTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun answerTitleRun = answerTitleParagraph.createRun();
        answerTitleRun.setText("参考答案");
        answerTitleRun.setBold(true);
        answerTitleRun.setFontSize(16);
        
        // 添加答案列表
        for (String answer : answerList) {
            XWPFParagraph answerParagraph = document.createParagraph();
            XWPFRun answerRun = answerParagraph.createRun();
            answerRun.setText(answer);
            answerRun.setColor("0000FF"); // 蓝色
        }
    }
    
    // 添加解析部分（类似处理）
}
```

### ✅ 2. 修复题目选项显示

#### 题目选项数据格式
数据库中存储的选项有两种JSON格式：
1. **简单字符串数组**: `["选项A", "选项B", "选项C", "选项D"]`
2. **对象数组**: `[{"id":"A","content":"<video>"},{"id":"B","content":"<media>"}]`

#### 选项解析方法
```java
@SuppressWarnings("unchecked")
private List<String> parseQuestionOptions(Object options) {
    List<String> optionTexts = new ArrayList<>();
    
    try {
        if (options instanceof List) {
            List<Object> optionList = (List<Object>) options;
            for (Object option : optionList) {
                if (option instanceof String) {
                    optionTexts.add((String) option);
                } else if (option instanceof Map) {
                    Map<String, Object> optionMap = (Map<String, Object>) option;
                    // 处理 {"id":"A","content":"选项内容"} 格式
                    if (optionMap.containsKey("content")) {
                        optionTexts.add(String.valueOf(optionMap.get("content")));
                    } else if (optionMap.containsKey("text")) {
                        optionTexts.add(String.valueOf(optionMap.get("text")));
                    } else {
                        optionTexts.add(option.toString());
                    }
                } else {
                    optionTexts.add(String.valueOf(option));
                }
            }
        } else if (options instanceof String) {
            String optionsStr = (String) options;
            // 如果是JSON字符串，使用ObjectMapper解析
            if (optionsStr.trim().startsWith("[") && optionsStr.trim().endsWith("]")) {
                ObjectMapper objectMapper = new ObjectMapper();
                List<Object> parsedOptions = objectMapper.readValue(optionsStr, List.class);
                // 递归处理解析后的选项
                return parseQuestionOptions(parsedOptions);
            } else {
                optionTexts.add(optionsStr);
            }
        }
    } catch (Exception e) {
        log.error("解析题目选项失败", e);
    }
    
    return optionTexts;
}
```

#### 选项格式化显示
```java
// Word格式
List<String> optionTexts = parseQuestionOptions(question.getQuestion().getOptions());
if (!optionTexts.isEmpty()) {
    String[] optionLabels = {"A", "B", "C", "D", "E", "F", "G", "H"};
    for (int j = 0; j < optionTexts.size() && j < optionLabels.length; j++) {
        XWPFParagraph optionParagraph = document.createParagraph();
        XWPFRun optionRun = optionParagraph.createRun();
        optionRun.setText(optionLabels[j] + ". " + optionTexts.get(j));
        optionRun.setFontFamily("宋体");
    }
}

// 文本格式
String[] optionLabels = {"A", "B", "C", "D", "E", "F", "G", "H"};
for (int j = 0; j < optionTexts.size() && j < optionLabels.length; j++) {
    content.append(optionLabels[j]).append(". ").append(optionTexts.get(j)).append("\n");
}
```

## 技术要点

### 1. 答案位置控制
- **内联模式**: 答案紧跟在每个题目后面
- **附录模式**: 所有答案统一放在文档末尾，使用分页符分隔
- **收集机制**: 在题目遍历时收集答案和解析，附录模式时统一输出

### 2. 选项解析策略
- **类型检测**: 判断选项是List、Map还是String
- **JSON解析**: 使用ObjectMapper处理JSON字符串
- **容错处理**: 解析失败时使用简单字符串分割
- **格式统一**: 最终都转换为List<String>格式

### 3. Word文档结构
```
试卷标题（居中，18号字体，加粗）
试卷信息（总分、时长）

题目部分：
1. 题目标题 (5分)（加粗）
A. 选项A
B. 选项B
C. 选项C
D. 选项D
【参考答案】A（蓝色，仅内联模式）
【解析】解析内容（绿色，仅内联模式）

（如果是附录模式）
=== 分页符 ===
参考答案（居中，16号字体，加粗）
第1题：A
第2题：B
...

题目解析（居中，16号字体，加粗）
第1题：解析内容
第2题：解析内容
...
```

### 4. 文本格式结构
```
试卷标题

总分：100分    考试时长：120分钟

1. 题目标题 (5分)
A. 选项A
B. 选项B
C. 选项C
D. 选项D
【参考答案】A（仅内联模式）
【解析】解析内容（仅内联模式）

（如果是附录模式）
==================================================
参考答案
==================================================

第1题：A
第2题：B

==================================================
题目解析
==================================================

第1题：解析内容
第2题：解析内容
```

## 测试步骤

### 1. 重启后端服务
```bash
cd cy_server
mvn clean install
mvn spring-boot:run
```

### 2. 测试答案位置功能
1. **访问试卷管理页面**: `http://localhost:3000/exam/paper`
2. **点击"导出Word"**: 选择一个有题目的试卷
3. **选择"题目内"**: 导出后检查答案是否在每个题目下方
4. **选择"附录"**: 导出后检查答案是否在文档末尾

### 3. 测试题目选项显示
1. **选择有选择题的试卷**: 确保试卷包含单选或多选题
2. **导出Word文档**: 检查是否显示A、B、C、D选项
3. **验证选项内容**: 确保选项内容正确显示

### 4. 测试不同导出选项组合
- ✅ 包含答案 + 题目内
- ✅ 包含答案 + 附录
- ✅ 包含答案和解析 + 题目内
- ✅ 包含答案和解析 + 附录
- ✅ 不包含答案
- ✅ 只包含解析

## 预期结果

### ✅ 答案位置功能
- **题目内模式**: 答案和解析显示在每个题目下方，用蓝色和绿色区分
- **附录模式**: 题目部分只显示题目和选项，答案和解析统一显示在文档末尾
- **分页效果**: 附录模式时答案部分另起一页

### ✅ 题目选项显示
- **选择题**: 显示完整的A、B、C、D选项
- **选项格式**: 每个选项前有标识符（A. B. C. D.）
- **内容完整**: 选项内容正确显示，支持HTML标签等复杂内容

### ✅ 文档质量
- **格式专业**: 标题居中加粗，选项缩进对齐
- **颜色区分**: 答案蓝色，解析绿色，便于区分
- **字体统一**: 全文使用宋体，确保中文显示效果

现在请重启后端服务，然后按照测试步骤验证修复效果！
