package com.cy.education.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 登录响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginResponseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * JWT令牌
     */
    private String token;

    /**
     * 用户信息
     */
    private UserInfoVO user;

    /**
     * 用户信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfoVO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 用户ID
         */
        private Integer id;

        /**
         * 用户名
         */
        private String username;

        /**
         * 真实姓名
         */
        private String name;

        /**
         * 头像
         */
        private String avatar;

        /**
         * 权限列表
         */
        private String[] permissions;
    }
} 