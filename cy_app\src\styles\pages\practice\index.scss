// 页面容器
.page-container {
  min-height: 100vh;
  background: #f5f7ff;
}

// 页面内容
.page-content {
  padding: 20px;
  padding-bottom: 40px;
}

// 分区标题
.section-title {
  margin-bottom: 16px;
}

.title-text {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #1a1d2e;
  margin-bottom: 4px;
}

.title-desc {
  font-size: 13px;
  color: #8e8e93;
  font-weight: 500;
}

// 题库选择
.bank-section {
  margin-bottom: 24px;
}

.bank-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bank-card {
  background: #fff;
  border-radius: 16px;
  padding: 16px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;

  &.active {
    border-color: #667eea;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
  }

  &:active {
    transform: scale(0.98);
  }
}

.bank-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.bank-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;

  &.all {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
}

.bank-content {
  flex: 1;
}

.bank-name {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 4px;
}

.bank-desc {
  font-size: 13px;
  color: #8e8e93;
  font-weight: 500;
}

.bank-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 12px;
}

.bank-stats {
  margin-bottom: 8px;
}

.stats-text {
  display: block;
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
  text-align: right;

  &:not(:last-child) {
    margin-bottom: 2px;
  }
}

// 练习统计
.stats-section {
  margin-bottom: 24px;
}

.stats-card {
  background: #fff;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 6px;
  line-height: 1;

  &.answered {
    color: #3b82f6;
  }

  &.correct {
    color: #10b981;
  }

  &.wrong {
    color: #ef4444;
  }
}

.stat-label {
  font-size: 13px;
  color: #8e8e93;
  font-weight: 500;
}

.accuracy-bar {
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
}

.accuracy-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #1a1d2e;
}

.accuracy-value {
  color: #667eea;
}

.progress-bar {
  height: 8px;
  background: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

// 练习模式
.mode-section {
  margin-bottom: 32px;
}

.mode-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mode-card {
  background: #fff;
  border-radius: 16px;
  padding: 16px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;

  &.active {
    border-color: #667eea;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
  }

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  &:active:not(.disabled) {
    transform: scale(0.98);
  }
}

.mode-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;

  &.wrong {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  }
}

.mode-content {
  flex: 1;
}

.mode-name {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
  margin-bottom: 4px;
}

.mode-desc {
  font-size: 13px;
  color: #8e8e93;
  font-weight: 500;
}

// 操作按钮
.action-section {
  margin-top: 32px;
}

.start-btn {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  &:active:not(.disabled) {
    transform: scale(0.98);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
} 