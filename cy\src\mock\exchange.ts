import { 
  generateId, 
  randomDate, 
  randomItem, 
  randomInt, 
  mockResponse,
  paginateData,
  getQueryParams
} from './utils'
import type { Exchange, ExchangeStatistics } from '@/api/exchange'
import { users } from './user'
import { products } from './product'

// 兑换状态
const exchangeStatus = ['pending', 'approved', 'shipped', 'delivered', 'cancelled', 'rejected']

// 物流公司
const expressCompanies = ['顺丰速运', '中通快递', '圆通速递', '申通快递', '韵达快递', '邮政EMS']

// 省份
const provinces = ['北京市', '上海市', '广东省', '山东省', '四川省', '湖北省', '河北省', '江苏省']

// 城市
const cities = ['北京市', '上海市', '广州市', '深圳市', '成都市', '武汉市', '石家庄市', '南京市']

// 区县
const districts = ['朝阳区', '浦东新区', '天河区', '南山区', '武侯区', '江岸区', '长安区', '鼓楼区']

// 生成随机地址
function generateAddress() {
  const province = randomItem(provinces)
  const city = randomItem(cities)
  const district = randomItem(districts)
  
  return {
    name: `${randomItem(['张', '李', '王', '赵', '刘'])}${randomItem(['先生', '女士'])}`,
    phone: `1${randomInt(3, 9)}${randomInt(1000000, 9999999)}`,
    province,
    city,
    district,
    address: `${randomItem(['幸福', '阳光', '文化', '科技', '和平'])}路${randomInt(1, 100)}号${randomItem(['幸福', '阳光', '文化', '科技', '和平'])}小区${randomInt(1, 20)}栋${randomInt(101, 2099)}室`,
    postalCode: `${randomInt(100000, 999999)}`
  }
}

// 生成物流信息
function generateExpressInfo() {
  return {
    company: randomItem(expressCompanies),
    trackingNumber: `${randomItem(['SF', 'ZT', 'YT', 'ST', 'YD', 'EMS'])}${randomInt(1000000000, 9999999999)}`,
    shippedAt: randomDate(new Date(2023, 0, 1))
  }
}

// 生成兑换记录
function generateExchanges(count: number = 100): Exchange[] {
  const exchanges: Exchange[] = []
  
  for (let i = 0; i < count; i++) {
    const user = randomItem(users)
    const product = randomItem(products)
    const quantity = randomInt(1, 3)
    const totalPoints = product.points * quantity
    const status = randomItem(exchangeStatus) as 'pending' | 'approved' | 'shipped' | 'delivered' | 'cancelled' | 'rejected'
    
    const exchange: Exchange = {
      id: generateId(),
      userId: user.id,
      userName: user.name,
      productId: product.id,
      productName: product.name,
      productImage: product.images[0],
      points: product.points,
      quantity,
      totalPoints,
      status,
      address: generateAddress(),
      remark: Math.random() > 0.7 ? `备注：${randomItem(['请尽快发货', '工作日送货', '周末送货', '需要发票', ''])}` : undefined,
      createdAt: randomDate(new Date(2023, 0, 1)),
      updatedAt: randomDate(new Date(2023, 3, 1))
    }
    
    // 根据状态添加物流信息
    if (['shipped', 'delivered'].includes(status)) {
      exchange.expressInfo = generateExpressInfo()
    }
    
    exchanges.push(exchange)
  }
  
  return exchanges
}

// 生成兑换统计数据
function generateExchangeStatistics(exchanges: Exchange[]): ExchangeStatistics {
  // 总兑换数
  const totalExchanges = exchanges.length
  
  // 待处理兑换数
  const pendingExchanges = exchanges.filter(exchange => exchange.status === 'pending').length
  
  // 总积分使用
  const totalPointsUsed = exchanges.reduce((total, exchange) => total + exchange.totalPoints, 0)
  
  // 状态分布
  const statusMap = new Map<string, number>()
  exchanges.forEach(exchange => {
    const count = statusMap.get(exchange.status) || 0
    statusMap.set(exchange.status, count + 1)
  })
  
  const statusDistribution = Array.from(statusMap.entries()).map(([status, count]) => ({
    status,
    count
  }))
  
  // 商品分布
  const productMap = new Map<string, { productId: string; productName: string; count: number }>()
  exchanges.forEach(exchange => {
    const product = productMap.get(exchange.productId) || {
      productId: exchange.productId,
      productName: exchange.productName,
      count: 0
    }
    product.count += exchange.quantity
    productMap.set(exchange.productId, product)
  })
  
  const productDistribution = Array.from(productMap.values())
    .sort((a, b) => b.count - a.count)
    .slice(0, 10)
  
  // 月度兑换趋势
  const monthlyMap = new Map<string, { count: number; points: number }>()
  exchanges.forEach(exchange => {
    const date = new Date(exchange.createdAt)
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    
    const monthData = monthlyMap.get(monthKey) || { count: 0, points: 0 }
    monthData.count += 1
    monthData.points += exchange.totalPoints
    
    monthlyMap.set(monthKey, monthData)
  })
  
  const monthlyExchanges = Array.from(monthlyMap.entries())
    .map(([month, data]) => ({
      month,
      count: data.count,
      points: data.points
    }))
    .sort((a, b) => a.month.localeCompare(b.month))
  
  return {
    totalExchanges,
    pendingExchanges,
    totalPointsUsed,
    statusDistribution,
    productDistribution,
    monthlyExchanges
  }
}

// 兑换记录数据集
export const exchanges = generateExchanges(100)

// 模拟获取兑换记录列表接口
export function mockGetExchangeList(url: string) {
  const params = getQueryParams(url)
  let filteredExchanges = [...exchanges]
  
  // 用户ID筛选
  if (params.userId) {
    filteredExchanges = filteredExchanges.filter(exchange => exchange.userId === params.userId)
  }
  
  // 用户名筛选
  if (params.userName) {
    const userName = params.userName.toLowerCase()
    filteredExchanges = filteredExchanges.filter(exchange => 
      exchange.userName.toLowerCase().includes(userName)
    )
  }
  
  // 商品ID筛选
  if (params.productId) {
    filteredExchanges = filteredExchanges.filter(exchange => exchange.productId === params.productId)
  }
  
  // 商品名称筛选
  if (params.productName) {
    const productName = params.productName.toLowerCase()
    filteredExchanges = filteredExchanges.filter(exchange => 
      exchange.productName.toLowerCase().includes(productName)
    )
  }
  
  // 状态筛选
  if (params.status) {
    filteredExchanges = filteredExchanges.filter(exchange => exchange.status === params.status)
  }
  
  // 日期筛选
  if (params.startDate) {
    const startDate = new Date(params.startDate)
    filteredExchanges = filteredExchanges.filter(exchange => new Date(exchange.createdAt) >= startDate)
  }
  
  if (params.endDate) {
    const endDate = new Date(params.endDate)
    filteredExchanges = filteredExchanges.filter(exchange => new Date(exchange.createdAt) <= endDate)
  }
  
  // 排序
  if (params.sortBy) {
    const sortBy = params.sortBy as keyof Exchange
    const sortOrder = params.sortOrder === 'desc' ? -1 : 1
    
    filteredExchanges.sort((a, b) => {
      if (a[sortBy] < b[sortBy]) return -1 * sortOrder
      if (a[sortBy] > b[sortBy]) return 1 * sortOrder
      return 0
    })
  } else {
    // 默认按创建时间倒序
    filteredExchanges.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }
  
  // 分页
  const page = parseInt(params.page || '1')
  const limit = parseInt(params.limit || '10')
  const paginatedData = paginateData(filteredExchanges, page, limit)
  
  return mockResponse(paginatedData)
}

// 模拟获取兑换记录详情接口
export function mockGetExchangeDetail(url: string) {
  const exchangeId = url.split('/').pop() || ''
  const exchange = exchanges.find(e => e.id === exchangeId)
  
  if (!exchange) {
    return mockResponse(null, 404, '兑换记录不存在')
  }
  
  return mockResponse(exchange)
}

// 模拟审核兑换申请接口
export function mockReviewExchange(url: string, data: { status: 'approved' | 'rejected'; remark?: string }) {
  const exchangeId = url.split('/').pop() || ''
  const exchangeIndex = exchanges.findIndex(e => e.id === exchangeId)
  
  if (exchangeIndex === -1) {
    return mockResponse(null, 404, '兑换记录不存在')
  }
  
  // 只能审核待处理的兑换
  if (exchanges[exchangeIndex].status !== 'pending') {
    return mockResponse(null, 400, '只能审核待处理的兑换记录')
  }
  
  // 更新状态
  exchanges[exchangeIndex].status = data.status
  exchanges[exchangeIndex].updatedAt = new Date().toISOString()
  
  // 添加备注
  if (data.remark) {
    exchanges[exchangeIndex].remark = data.remark
  }
  
  return mockResponse({ success: true })
}

// 模拟更新物流信息接口
export function mockUpdateExchangeExpress(url: string, data: { company: string; trackingNumber: string }) {
  const exchangeId = url.split('/').pop() || ''
  const exchangeIndex = exchanges.findIndex(e => e.id === exchangeId)
  
  if (exchangeIndex === -1) {
    return mockResponse(null, 404, '兑换记录不存在')
  }
  
  // 只能更新已审核通过的兑换
  if (exchanges[exchangeIndex].status !== 'approved') {
    return mockResponse(null, 400, '只能更新已审核通过的兑换记录')
  }
  
  // 更新物流信息
  exchanges[exchangeIndex].expressInfo = {
    ...data,
    shippedAt: new Date().toISOString()
  }
  
  // 更新状态为已发货
  exchanges[exchangeIndex].status = 'shipped'
  exchanges[exchangeIndex].updatedAt = new Date().toISOString()
  
  return mockResponse({ success: true })
}

// 模拟确认收货接口
export function mockConfirmExchangeDelivery(url: string) {
  const exchangeId = url.split('/').pop() || ''
  const exchangeIndex = exchanges.findIndex(e => e.id === exchangeId)
  
  if (exchangeIndex === -1) {
    return mockResponse(null, 404, '兑换记录不存在')
  }
  
  // 只能确认已发货的兑换
  if (exchanges[exchangeIndex].status !== 'shipped') {
    return mockResponse(null, 400, '只能确认已发货的兑换记录')
  }
  
  // 更新状态为已收货
  exchanges[exchangeIndex].status = 'delivered'
  exchanges[exchangeIndex].updatedAt = new Date().toISOString()
  
  return mockResponse({ success: true })
}

// 模拟取消兑换接口
export function mockCancelExchange(url: string, data: { reason: string }) {
  const exchangeId = url.split('/').pop() || ''
  const exchangeIndex = exchanges.findIndex(e => e.id === exchangeId)
  
  if (exchangeIndex === -1) {
    return mockResponse(null, 404, '兑换记录不存在')
  }
  
  // 只能取消待处理或已审核的兑换
  if (!['pending', 'approved'].includes(exchanges[exchangeIndex].status)) {
    return mockResponse(null, 400, '只能取消待处理或已审核的兑换记录')
  }
  
  // 更新状态为已取消
  exchanges[exchangeIndex].status = 'cancelled'
  exchanges[exchangeIndex].updatedAt = new Date().toISOString()
  
  // 添加取消原因
  exchanges[exchangeIndex].remark = data.reason
  
  return mockResponse({ success: true })
}

// 模拟批量更新兑换状态接口
export function mockBatchUpdateExchangeStatus(data: { 
  ids: string[]; 
  status: 'approved' | 'shipped' | 'rejected' | 'cancelled';
  remark?: string
}) {
  const { ids, status, remark } = data
  let failedCount = 0
  
  ids.forEach(id => {
    const exchangeIndex = exchanges.findIndex(e => e.id === id)
    if (exchangeIndex === -1) {
      failedCount++
      return
    }
    
    // 根据状态检查是否可以更新
    const currentStatus = exchanges[exchangeIndex].status
    let canUpdate = false
    
    switch (status) {
      case 'approved':
      case 'rejected':
        canUpdate = currentStatus === 'pending'
        break
      case 'shipped':
        canUpdate = currentStatus === 'approved'
        break
      case 'cancelled':
        canUpdate = ['pending', 'approved'].includes(currentStatus)
        break
    }
    
    if (!canUpdate) {
      failedCount++
      return
    }
    
    // 更新状态
    exchanges[exchangeIndex].status = status
    exchanges[exchangeIndex].updatedAt = new Date().toISOString()
    
    // 添加备注
    if (remark) {
      exchanges[exchangeIndex].remark = remark
    }
    
    // 如果是发货状态，添加物流信息
    if (status === 'shipped') {
      exchanges[exchangeIndex].expressInfo = generateExpressInfo()
    }
  })
  
  return mockResponse({ success: true, failedCount })
}

// 模拟获取兑换统计数据接口
export function mockGetExchangeStatistics() {
  return mockResponse(generateExchangeStatistics(exchanges))
}

// 模拟导出兑换记录接口
export function mockExportExchanges() {
  // 实际应用中应返回文件流，这里简化处理
  return mockResponse(new Blob())
} 