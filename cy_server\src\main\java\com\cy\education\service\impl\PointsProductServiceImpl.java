package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.PointsProduct;
import com.cy.education.model.params.PointsProductQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.repository.PointsProductMapper;
import com.cy.education.service.PointsProductService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 积分商品服务实现类
 */
@Service
public class PointsProductServiceImpl extends ServiceImpl<PointsProductMapper, PointsProduct> implements PointsProductService {

    @Override
    public IPage<PointsProduct> page(PointsProductQueryParam param) {
        // 创建分页对象
        Page<PointsProduct> page = new Page<>(param.getPage(), param.getLimit());

        // 构建查询条件
        LambdaQueryWrapper<PointsProduct> wrapper = new LambdaQueryWrapper<>();

        // 商品名称条件
        if (StringUtils.isNotBlank(param.getName())) {
            wrapper.like(PointsProduct::getName, param.getName());
        }

        // 商品分类条件
        if (StringUtils.isNotBlank(param.getCategory())) {
            wrapper.eq(PointsProduct::getCategory, param.getCategory());
        }

        // 状态条件
        if (StringUtils.isNotBlank(param.getStatus())) {
            wrapper.eq(PointsProduct::getStatus, param.getStatus());
        }

        // 排序
        if (StringUtils.isNotBlank(param.getSortBy())) {
            boolean isAsc = "asc".equalsIgnoreCase(param.getSortOrder());
            switch (param.getSortBy()) {
                case "points":
                    wrapper.orderBy(true, isAsc, PointsProduct::getPoints);
                    break;
                case "stock":
                    wrapper.orderBy(true, isAsc, PointsProduct::getStock);
                    break;
                case "exchangeCount":
                    wrapper.orderBy(true, isAsc, PointsProduct::getExchangeCount);
                    break;
                case "createdAt":
                    wrapper.orderBy(true, isAsc, PointsProduct::getCreatedAt);
                    break;
                default:
                    wrapper.orderBy(true, false, PointsProduct::getCreatedAt);
                    break;
            }
        } else {
            // 默认按创建时间降序
            wrapper.orderByDesc(PointsProduct::getCreatedAt);
        }

        // 执行查询
        return page(page, wrapper);
    }

    @Override
    public PointsProduct getById(Integer id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> create(PointsProduct product) {
        // 设置初始值
        product.setId(null);
        product.setExchangeCount(0);
        product.setCreatedAt(LocalDateTime.now());

        // 保存商品
        if (save(product)) {
            return ApiResponse.success(Map.of("id", product.getId(), "success", true));
        }
        return ApiResponse.error("创建商品失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> update(Integer id, PointsProduct product) {
        // 检查商品是否存在
        PointsProduct existingProduct = getById(id);
        if (existingProduct == null) {
            return ApiResponse.error("商品不存在");
        }

        // 设置不可修改的字段
        product.setId(id);
        product.setExchangeCount(existingProduct.getExchangeCount());
        product.setCreatedAt(existingProduct.getCreatedAt());
        product.setUpdatedAt(LocalDateTime.now());

        // 更新商品
        if (updateById(product)) {
            return ApiResponse.success(Map.of("success", true));
        }
        return ApiResponse.error("更新商品失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> delete(Integer id) {
        // 检查商品是否存在
        PointsProduct product = getById(id);
        if (product == null) {
            return ApiResponse.error("商品不存在");
        }

        // 检查是否已经有兑换记录
        if (product.getExchangeCount() > 0) {
            return ApiResponse.error("商品已有兑换记录，不能删除");
        }

        // 删除商品
        if (removeById(id)) {
            return ApiResponse.success(Map.of("success", true));
        }
        return ApiResponse.error("删除商品失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> updateStatus(Integer id, String status) {
        // 检查商品是否存在
        PointsProduct product = getById(id);
        if (product == null) {
            return ApiResponse.error("商品不存在");
        }

        // 检查状态值
        if (!"active".equals(status) && !"inactive".equals(status)) {
            return ApiResponse.error("状态值无效");
        }

        // 如果状态已经相同，无需更新
        if (product.getStatus().equals(status)) {
            return ApiResponse.success(Map.of("success", true));
        }

        // 更新状态
        product.setStatus(status);
        product.setUpdatedAt(LocalDateTime.now());

        if (updateById(product)) {
            return ApiResponse.success(Map.of("success", true));
        }
        return ApiResponse.error("更新状态失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> restock(Integer id, Integer count) {
        // 检查商品是否存在
        PointsProduct product = getById(id);
        if (product == null) {
            return ApiResponse.error("商品不存在");
        }

        // 检查补充数量
        if (count == null || count <= 0) {
            return ApiResponse.error("补充数量必须大于0");
        }

        // 增加库存
        if (baseMapper.increaseStock(id, count) > 0) {
            // 获取更新后的商品信息
            product = getById(id);
            return ApiResponse.success(Map.of(
                "success", true,
                "id", id,
                "newStock", product.getStock()
            ));
        }
        return ApiResponse.error("补充库存失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean decreaseStock(Integer id, Integer count) {
        // 减少库存
        return baseMapper.decreaseStock(id, count) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean increaseStock(Integer id, Integer count) {
        // 增加库存
        return baseMapper.increaseStock(id, count) > 0;
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> result = new HashMap<>();

        // 获取商品总数
        long totalProducts = count();
        result.put("totalProducts", totalProducts);

        // 获取上架商品数
        LambdaQueryWrapper<PointsProduct> activeWrapper = new LambdaQueryWrapper<>();
        activeWrapper.eq(PointsProduct::getStatus, "active");
        long activeProducts = count(activeWrapper);
        result.put("activeProducts", activeProducts);

        // 获取库存不足商品数
        QueryWrapper<PointsProduct> lowStockWrapper = new QueryWrapper<>();
        lowStockWrapper.apply("stock <= low_stock_threshold")
                     .eq("status", "active");
        long lowStockProducts = count(lowStockWrapper);
        result.put("lowStockProducts", lowStockProducts);

        // 获取总兑换次数
        QueryWrapper<PointsProduct> exchangeWrapper = new QueryWrapper<>();
        exchangeWrapper.select("SUM(exchange_count) as totalExchanges");
        Map<String, Object> exchangeMap = getMap(exchangeWrapper);
        int totalExchanges = exchangeMap != null && exchangeMap.get("totalExchanges") != null ?
                          Integer.parseInt(exchangeMap.get("totalExchanges").toString()) : 0;
        result.put("totalExchanges", totalExchanges);

        // 获取分类分布
        QueryWrapper<PointsProduct> categoryWrapper = new QueryWrapper<>();
        categoryWrapper.select("category, COUNT(*) as count")
                     .groupBy("category");
        result.put("categoryDistribution", listMaps(categoryWrapper));

        // 获取热门商品TOP5
        QueryWrapper<PointsProduct> hotWrapper = new QueryWrapper<>();
        hotWrapper.select("id", "name", "points", "exchange_count", "stock", "status")
                .orderByDesc("exchange_count")
                .last("LIMIT 5");
        result.put("hotProducts", listMaps(hotWrapper));

        return result;
    }
}
