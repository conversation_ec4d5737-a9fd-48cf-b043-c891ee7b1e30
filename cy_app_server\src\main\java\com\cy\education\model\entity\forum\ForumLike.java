package com.cy.education.model.entity.forum;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 论坛点赞实体类
 */
@Data
@TableName("forum_likes")
public class ForumLike implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 点赞ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 帖子ID
     */
    private Integer postId;

    /**
     * 评论ID（如果是对评论点赞）
     */
    private Integer commentId;

    /**
     * 点赞类型：post-帖子点赞, comment-评论点赞
     */
    private String type;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}