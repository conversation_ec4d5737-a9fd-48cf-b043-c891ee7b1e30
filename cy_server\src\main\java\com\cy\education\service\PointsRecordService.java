package com.cy.education.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.PointsRecord;
import com.cy.education.model.params.PointsAdjustParam;
import com.cy.education.model.params.PointsQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PointsStatisticsVO;

import java.util.List;
import java.util.Map;

/**
 * 积分记录服务接口
 */
public interface PointsRecordService {

    /**
     * 分页查询积分记录
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<PointsRecord> page(PointsQueryParam param);
    
    /**
     * 获取用户积分余额
     *
     * @param userId 用户ID
     * @return 积分余额
     */
    Integer getUserBalance(Integer userId);
    
    /**
     * 积分调整
     *
     * @param param 调整参数
     * @return 操作结果
     */
    ApiResponse<Map<String, Object>> adjustPoints(PointsAdjustParam param);

    /**
     * 批量调整积分
     *
     * @param params 调整参数列表
     * @return 操作结果
     */
    ApiResponse<Map<String, Object>> batchAdjustPoints(List<PointsAdjustParam> params);
    
    /**
     * 获取积分统计数据
     *
     * @return 统计数据
     */
    PointsStatisticsVO getStatistics();
    
    /**
     * 获取用户积分趋势
     *
     * @param userId 用户ID
     * @param period 时间段：week/month/year/all
     * @return 趋势数据
     */
    Map<String, Object> getUserPointsTrend(Integer userId, String period);
    
    /**
     * 记录积分变动
     *
     * @param userId 用户ID
     * @param points 积分变动值
     * @param type 变动类型
     * @param description 描述
     * @param relatedId 关联ID
     * @param relatedType 关联类型
     * @param operator 操作员
     * @return 是否成功
     */
    boolean recordPointsChange(Integer userId, Integer points, String type, 
                              String description, Integer relatedId, 
                              String relatedType, String operator);
    
    /**
     * 根据规则增加积分
     *
     * @param userId 用户ID
     * @param ruleCode 规则编码
     * @param relatedId 关联ID
     * @param relatedType 关联类型
     * @return 是否成功
     */
    boolean addPointsByRule(Integer userId, String ruleCode, Integer relatedId, String relatedType);
} 