import * as XLSX from 'xlsx'
import { ElMessage } from 'element-plus'

/**
 * Excel工具类
 */
export class ExcelUtils {
  /**
   * 导出数据到Excel
   * @param data 数据数组
   * @param headers 表头配置
   * @param filename 文件名
   * @param sheetName 工作表名称
   */
  static exportToExcel(
    data: any[],
    headers: { key: string; title: string; width?: number }[],
    filename: string,
    sheetName: string = 'Sheet1'
  ) {
    try {
      // 创建工作簿
      const workbook = XLSX.utils.book_new()
      
      // 准备数据
      const worksheetData = [
        // 表头
        headers.map(h => h.title),
        // 数据行
        ...data.map(row => headers.map(h => row[h.key] || ''))
      ]
      
      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)
      
      // 设置列宽
      const colWidths = headers.map(h => ({ wch: h.width || 15 }))
      worksheet['!cols'] = colWidths
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
      
      // 导出文件
      XLSX.writeFile(workbook, filename)
      
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出Excel失败:', error)
      ElMessage.error('导出失败')
    }
  }

  /**
   * 读取Excel文件
   * @param file 文件对象
   * @param sheetIndex 工作表索引，默认0
   * @returns Promise<any[]>
   */
  static readExcelFile(file: File, sheetIndex: number = 0): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          
          // 获取第一个工作表
          const sheetName = workbook.SheetNames[sheetIndex]
          const worksheet = workbook.Sheets[sheetName]
          
          // 转换为JSON数据
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
          
          resolve(jsonData as any[])
        } catch (error) {
          reject(error)
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * 创建Excel模板
   * @param headers 表头配置
   * @param sampleData 示例数据
   * @param filename 文件名
   * @param sheetName 工作表名称
   */
  static createTemplate(
    headers: { key: string; title: string; width?: number; required?: boolean }[],
    sampleData: any[] = [],
    filename: string,
    sheetName: string = 'Template'
  ) {
    try {
      const workbook = XLSX.utils.book_new()
      
      // 准备模板数据
      const templateData = [
        // 表头（带必填标识）
        headers.map(h => h.required ? `${h.title}*` : h.title),
        // 示例数据
        ...sampleData.map(row => headers.map(h => row[h.key] || ''))
      ]
      
      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(templateData)
      
      // 设置列宽
      const colWidths = headers.map(h => ({ wch: h.width || 15 }))
      worksheet['!cols'] = colWidths
      
      // 添加工作表
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
      
      // 导出模板
      XLSX.writeFile(workbook, filename)
      
      ElMessage.success('模板下载成功')
    } catch (error) {
      console.error('创建模板失败:', error)
      ElMessage.error('模板创建失败')
    }
  }

  /**
   * 验证Excel数据
   * @param data Excel数据
   * @param headers 表头配置
   * @returns 验证结果
   */
  static validateExcelData(
    data: any[][],
    headers: { key: string; title: string; required?: boolean; validator?: (value: any) => boolean }[]
  ): { valid: boolean; errors: string[]; validData: any[] } {
    const errors: string[] = []
    const validData: any[] = []
    
    if (data.length < 2) {
      errors.push('Excel文件至少需要包含表头和一行数据')
      return { valid: false, errors, validData }
    }
    
    // 验证表头
    const headerRow = data[0]
    const expectedHeaders = headers.map(h => h.required ? `${h.title}*` : h.title)
    
    for (let i = 0; i < expectedHeaders.length; i++) {
      if (headerRow[i] !== expectedHeaders[i]) {
        errors.push(`第${i + 1}列表头应为"${expectedHeaders[i]}"，实际为"${headerRow[i] || ''}"`)
      }
    }
    
    if (errors.length > 0) {
      return { valid: false, errors, validData }
    }
    
    // 验证数据行
    for (let rowIndex = 1; rowIndex < data.length; rowIndex++) {
      const row = data[rowIndex]
      const rowData: any = {}
      let rowValid = true
      
      for (let colIndex = 0; colIndex < headers.length; colIndex++) {
        const header = headers[colIndex]
        const cellValue = row[colIndex]
        
        // 检查必填字段
        if (header.required && (!cellValue || cellValue.toString().trim() === '')) {
          errors.push(`第${rowIndex + 1}行第${colIndex + 1}列"${header.title}"为必填项`)
          rowValid = false
          continue
        }
        
        // 自定义验证
        if (header.validator && cellValue && !header.validator(cellValue)) {
          errors.push(`第${rowIndex + 1}行第${colIndex + 1}列"${header.title}"格式不正确`)
          rowValid = false
          continue
        }
        
        rowData[header.key] = cellValue
      }
      
      if (rowValid) {
        validData.push(rowData)
      }
    }
    
    return {
      valid: errors.length === 0,
      errors,
      validData
    }
  }

  /**
   * 下载文件
   * @param blob Blob对象
   * @param filename 文件名
   */
  static downloadFile(blob: Blob, filename: string) {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

/**
 * 格式化日期为Excel可识别的格式
 */
export function formatDateForExcel(date: Date | string | null): string {
  if (!date) return ''
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleDateString('zh-CN') + ' ' + d.toLocaleTimeString('zh-CN')
}

/**
 * 格式化状态为中文
 */
export function formatStatus(status: number | string, statusMap: Record<string, string>): string {
  return statusMap[status.toString()] || '未知'
}
