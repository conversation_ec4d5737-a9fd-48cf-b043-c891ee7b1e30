package com.cy.education.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程章节VO
 */
@Data
public class CourseSectionVO {
    
    /**
     * 章节ID
     */
    private Integer id;
    
    /**
     * 课程ID
     */
    private Integer courseId;
    
    /**
     * 章节标题
     */
    private String title;
    
    /**
     * 章节时长(分钟)
     */
    private Integer duration;
    
    /**
     * 排序号
     */
    private Integer orderNum;
    
    /**
     * 课时列表
     */
    private List<CourseLessonVO> lessons;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 