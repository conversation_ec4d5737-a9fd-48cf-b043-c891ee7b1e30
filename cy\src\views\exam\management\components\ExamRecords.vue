<template>
  <div class="exam-records">
    <div class="filter-form">
      <el-form :inline="true" :model="filterForm" ref="filterFormRef">
        <el-form-item label="考试名称">
          <el-select v-model="filterForm.examId" placeholder="选择考试" clearable style="width: 200px">
            <el-option
              v-for="item in examOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filterForm.departmentId" placeholder="选择部门" clearable style="width: 180px">
            <el-option
              v-for="item in departmentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考生姓名">
          <el-input v-model="filterForm.keyword" placeholder="搜索考生姓名" clearable style="width: 150px" />
        </el-form-item>
        <el-form-item label="排序方式">
          <el-select v-model="filterForm.sortBy" placeholder="选择排序" clearable style="width: 140px">
            <el-option label="提交时间" value="created_at" />
            <el-option label="成绩高到低" value="score_desc" />
            <el-option label="成绩低到高" value="score_asc" />
            <el-option label="答题时长" value="duration" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="success" @click="handleExportResults">导出成绩</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card class="statistics-card" v-if="filterForm.examId">
      <div class="statistics-header">
        <h3>{{ currentExam.name }} - 成绩统计</h3>
      </div>
      <div class="statistics-content">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="statistics-item">
              <div class="statistics-value">{{ statistics.totalParticipants || 0 }}</div>
              <div class="statistics-label">考试人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="statistics-item">
              <div class="statistics-value">{{ statistics.totalParticipants || 0 }}</div>
              <div class="statistics-label">已交卷人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="statistics-item">
              <div class="statistics-value">{{ statistics.averageScore || 0 }}</div>
              <div class="statistics-label">平均分</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="statistics-item">
              <div class="statistics-value">{{ statistics.passRate || 0 }}%</div>
              <div class="statistics-label">及格率</div>
            </div>
          </el-col>
        </el-row>

        <!-- 部门统计表格 -->
        <div class="department-stats" v-if="statistics.departmentStats && statistics.departmentStats.length > 0">
          <h4 style="margin: 20px 0 10px 0;">各部门成绩统计</h4>
          <el-table :data="statistics.departmentStats" size="small" style="margin-top: 10px;">
            <el-table-column prop="departmentName" label="部门" width="150" />
            <el-table-column prop="participantCount" label="参考人数" width="100" align="center" />
            <el-table-column prop="averageScore" label="平均分" width="100" align="center">
              <template #default="{ row }">
                {{ Math.round(row.averageScore * 100) / 100 }}
              </template>
            </el-table-column>
            <el-table-column prop="passRate" label="及格率" width="100" align="center">
              <template #default="{ row }">
                {{ Math.round(row.passRate * 100) / 100 }}%
              </template>
            </el-table-column>
            <el-table-column prop="highestScore" label="最高分" width="100" align="center" />
            <el-table-column prop="lowestScore" label="最低分" width="100" align="center" />
          </el-table>
        </div>

        <!-- 部门统计图表 -->
        <div class="charts-container" v-if="statistics.departmentStats && statistics.departmentStats.length > 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="chart-wrapper">
                <h4>部门参与统计</h4>
                <div ref="deptStatsChartRef" class="chart" style="height: 300px;"></div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="chart-wrapper">
                <h4>成绩分布</h4>
                <div ref="scoreDistChartRef" class="chart" style="height: 300px;"></div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="chart-wrapper">
                <h4>答题时长分布</h4>
                <div ref="durationDistChartRef" class="chart" style="height: 300px;"></div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <el-table
      v-loading="loading"
      :data="recordsList"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="userName" label="考生姓名" min-width="100" />
      <el-table-column prop="departmentName" label="部门" min-width="120" />
      <el-table-column prop="examTitle" label="考试名称" min-width="150" />
      <el-table-column prop="score" label="得分" width="100" align="center">
        <template #default="{ row }">
          <span :class="{ 'pass-score': row.passed, 'fail-score': !row.passed }">
            {{ row.score }}/{{ row.totalScore }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="percentage" label="得分率" width="100" align="center">
        <template #default="{ row }">
          {{ row.percentage }}%
        </template>
      </el-table-column>
      <el-table-column prop="duration" label="答题时长" width="100" align="center">
        <template #default="{ row }">
          {{ formatDuration(row.duration) }}
        </template>
      </el-table-column>
      <el-table-column prop="endTime" label="提交时间" width="160" align="center" />
      <el-table-column prop="status" label="考试状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 'completed' ? 'success' : 'info'">
            {{ row.status === 'completed' ? '已完成' : '进行中' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="180" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleViewDetail(row)">查看详情</el-button>
          <el-button type="success" link @click="handleViewResults(row)">查看成绩</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 导出对话框 - 临时注释掉 -->
    <!--
    <ExportDialog
      v-model="exportDialogVisible"
      title="导出考试记录"
      export-url="/api/exam-record/export"
      :support-csv="true"
      :support-range="true"
      :support-time-range="true"
      :fields="[
        { key: 'studentName', label: '学员姓名', required: true },
        { key: 'studentNumber', label: '学员编号' },
        { key: 'department', label: '部门' },
        { key: 'examTitle', label: '考试名称', required: true },
        { key: 'score', label: '得分', required: true },
        { key: 'totalScore', label: '总分' },
        { key: 'passScore', label: '及格分' },
        { key: 'status', label: '状态' },
        { key: 'startTime', label: '开始时间' },
        { key: 'endTime', label: '结束时间' },
        { key: 'duration', label: '用时' },
        { key: 'submitTime', label: '提交时间' }
      ]"
      :default-params="{
        examId: route.params.id,
        keyword: searchForm.keyword,
        departmentId: searchForm.departmentId,
        status: searchForm.status
      }"
      description="可选择导出全部记录、当前页面记录，支持按时间范围筛选"
      @success="handleExportSuccess"
    />
    -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// ECharts导入
import * as echarts from 'echarts/core'
import { BarChart, PieChart } from 'echarts/charts'
import { TooltipComponent, LegendComponent, TitleComponent, GridComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { getExamRecordList, getExamList, getExamStatistics } from '@/api/exam'
// 临时注释掉ExportDialog导入
// import ExportDialog from '@/components/ImportExport/ExportDialog.vue'
import { getDepartmentTree, type Department } from '@/api/department'

// ECharts注册
echarts.use([BarChart, PieChart, TooltipComponent, LegendComponent, TitleComponent, GridComponent, CanvasRenderer])

const route = useRoute()
const router = useRouter()

// 表格数据和分页
const loading = ref(false)
const recordsList = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 筛选表单
const filterFormRef = ref(null)
const filterForm = reactive({
  examId: '',
  departmentId: '',
  status: '',
  keyword: '',
  sortBy: 'created_at'
})

// 选项数据
const examOptions = ref<{ value: string, label: string }[]>([])
const departmentOptions = ref<{ value: string, label: string }[]>([])

// 图表相关
const deptStatsChartRef = ref<HTMLElement>()
const scoreDistChartRef = ref<HTMLElement>()
const durationDistChartRef = ref<HTMLElement>()
let deptStatsChart: any = null
let scoreDistChart: any = null
let durationDistChart: any = null

// 当前选中的考试统计信息
const currentExam = ref({
  name: '',
  totalCount: 0,
  submittedCount: 0,
  avgScore: 0,
  passRate: 0
})

// 导出相关
const exportDialogVisible = ref(false)

// 统计数据类型定义
interface DepartmentStat {
  departmentId: string;
  departmentName: string;
  participantCount: number;
  averageScore: number;
  passRate: number;
  highestScore?: number;
  lowestScore?: number;
}

interface ScoreDistribution {
  excellent: number;
  good: number;
  moderate: number;
  pass: number;
  fail: number;
}

interface DurationDistribution {
  veryFast: number;
  fast: number;
  normal: number;
  slow: number;
  verySlow: number;
}

interface Statistics {
  totalExams: number;
  totalParticipants: number;
  averageScore: number;
  passRate: number;
  departmentStats: DepartmentStat[];
  scoreDistribution?: ScoreDistribution;
  durationDistribution?: DurationDistribution;
}

// 统计数据
const statistics = ref<Statistics>({
  totalExams: 0,
  totalParticipants: 0,
  averageScore: 0,
  passRate: 0,
  departmentStats: []
})

// 初始化考试选项
const initExamOptions = async () => {
  try {
    const res = await getExamList({
      page: 1,
      limit: 100,
      sortBy: 'created_at',
      sortOrder: 'desc'
    })
    examOptions.value = res.list.map(exam => ({
      value: exam.id,
      label: exam.title
    }))
  } catch (error) {
    console.error('获取考试列表失败:', error)
  }
}

// 初始化部门选项
const initDepartmentOptions = async () => {
  try {
    const res = await getDepartmentTree()
    departmentOptions.value = convertDepartmentTreeToOptions(res)
  } catch (error) {
    console.error('获取部门树失败:', error)
    ElMessage.error('获取部门树失败')
  }
}

// 将部门树转换为扁平的选项数组
const convertDepartmentTreeToOptions = (departments: Department[], prefix = ''): { value: string, label: string }[] => {
  let options: { value: string, label: string }[] = []
  
  departments.forEach(dept => {
    const label = prefix ? `${prefix} / ${dept.name}` : dept.name
    options.push({ value: dept.id.toString(), label })
    
    if (dept.children && dept.children.length > 0) {
      options = options.concat(convertDepartmentTreeToOptions(dept.children, label))
    }
  })
  
  return options
}

// 获取考试记录列表
const fetchRecordsList = async () => {
  loading.value = true
  try {
    // 处理排序参数
    let sortBy = 'created_at'
    let sortOrder: 'desc' | 'asc' = 'desc'

    if (filterForm.sortBy) {
      switch (filterForm.sortBy) {
        case 'score_desc':
          sortBy = 'score'
          sortOrder = 'desc'
          break
        case 'score_asc':
          sortBy = 'score'
          sortOrder = 'asc'
          break
        case 'duration':
          sortBy = 'duration'
          sortOrder = 'asc'
          break
        case 'created_at':
        default:
          sortBy = 'created_at'
          sortOrder = 'desc'
          break
      }
    }

    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      examId: filterForm.examId || undefined,
      departmentId: filterForm.departmentId || undefined,
      status: filterForm.status || undefined,
      keyword: filterForm.keyword || undefined,
      sortBy,
      sortOrder
    }
    
    const res = await getExamRecordList(params)
    recordsList.value = res.list.map(record => ({
      id: record.id,
      examId: record.examId,
      examTitle: record.examTitle,
      userName: record.userName,
      departmentName: record.departmentName,
      score: record.score,
      totalScore: record.totalScore,
      percentage: record.totalScore > 0 ? Math.round((record.score / record.totalScore) * 100) : 0,
      passed: record.passed,
      duration: record.duration,
      startTime: record.startTime ? formatDateTime(record.startTime) : '-',
      endTime: record.endTime ? formatDateTime(record.endTime) : '-',
      status: record.status,
      attemptNumber: record.attemptNumber
    }))
    
    total.value = res.total
  } catch (error) {
    console.error('获取考试记录失败:', error)
    ElMessage.error('获取考试记录失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const examId = filterForm.examId || undefined
    const stats = await getExamStatistics(examId)
    statistics.value = stats
    
    // 更新图表
    await nextTick()
    updateCharts()
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化考试时长
const formatDuration = (minutes: number) => {
  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours}小时${remainingMinutes}分钟`
  }
}

// 更新图表
const updateCharts = () => {
  // 确保在下一个tick中执行，保证DOM已经更新
  nextTick(() => {
    updateDeptStatsChart()
    updateScoreDistChart()
    updateDurationDistChart()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新部门统计图表
const updateDeptStatsChart = () => {
  if (!deptStatsChartRef.value) return
  
  if (!deptStatsChart) {
    deptStatsChart = echarts.init(deptStatsChartRef.value)
  }
  
  const deptStats = statistics.value.departmentStats || []
  
  const option = {
    title: {
      text: '部门考试统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const data = params[0]
        const deptStat = deptStats[data.dataIndex]
        return `${data.name}<br/>
          参与人数: ${data.value}人<br/>
          平均分: ${deptStat?.averageScore || 0}分<br/>
          通过率: ${deptStat?.passRate || 0}%<br/>
          最高分: ${deptStat?.highestScore || 0}分<br/>
          最低分: ${deptStat?.lowestScore || 0}分`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: deptStats.map(item => item.departmentName),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '参与人数'
    },
    series: [
      {
        name: '参与人数',
        type: 'bar',
        data: deptStats.map(item => item.participantCount),
        itemStyle: {
          color: '#409EFF'
        },
        label: {
          show: true,
          position: 'top'
        }
      }
    ]
  }
  
  deptStatsChart.setOption(option)
}

// 更新分数分布图表
const updateScoreDistChart = () => {
  if (!scoreDistChartRef.value) return
  
  if (!scoreDistChart) {
    scoreDistChart = echarts.init(scoreDistChartRef.value)
  }
  
  // 使用后端返回的分数分布数据
  const scoreDistribution = statistics.value.scoreDistribution || {
    excellent: 0,
    good: 0,
    moderate: 0,
    pass: 0,
    fail: 0
  }
  
  const scoreData = [
    { value: scoreDistribution.excellent, name: '优秀(90-100)', itemStyle: { color: '#67C23A' } },
    { value: scoreDistribution.good, name: '良好(80-89)', itemStyle: { color: '#95D475' } },
    { value: scoreDistribution.moderate, name: '中等(70-79)', itemStyle: { color: '#E6A23C' } },
    { value: scoreDistribution.pass, name: '及格(60-69)', itemStyle: { color: '#F56C6C' } },
    { value: scoreDistribution.fail, name: '不及格(0-59)', itemStyle: { color: '#909399' } }
  ]
  
  const option = {
    title: {
      text: '成绩分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}人 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: scoreData.map(item => item.name)
    },
    series: [
      {
        name: '成绩分布',
        type: 'pie',
        radius: '50%',
        data: scoreData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  scoreDistChart.setOption(option)
}

// 更新答题时长分布图表
const updateDurationDistChart = () => {
  if (!durationDistChartRef.value) return

  if (!durationDistChart) {
    durationDistChart = echarts.init(durationDistChartRef.value)
  }

  const durationDistribution = statistics.value.durationDistribution || {
    veryFast: 0,
    fast: 0,
    normal: 0,
    slow: 0,
    verySlow: 0
  }

  const durationOption = {
    title: {
      text: '答题时长分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}人 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['非常快', '较快', '一般', '较慢', '超时']
    },
    series: [
      {
        name: '答题时长',
        type: 'pie',
        radius: '50%',
        data: [
          { value: durationDistribution.veryFast, name: '非常快', itemStyle: { color: '#67C23A' } },
          { value: durationDistribution.fast, name: '较快', itemStyle: { color: '#95D475' } },
          { value: durationDistribution.normal, name: '一般', itemStyle: { color: '#E6A23C' } },
          { value: durationDistribution.slow, name: '较慢', itemStyle: { color: '#F56C6C' } },
          { value: durationDistribution.verySlow, name: '超时', itemStyle: { color: '#909399' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  durationDistChart.setOption(durationOption)
}

// 筛选
const handleFilter = () => {
  currentPage.value = 1
  fetchRecordsList()
  fetchStatistics()
}

// 重置筛选
const resetFilter = () => {
  Object.assign(filterForm, {
    examId: '',
    departmentId: '',
    status: '',
    keyword: ''
  })
  handleFilter()
}

// 分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchRecordsList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchRecordsList()
}

// 导出记录
const handleExportRecords = () => {
  exportDialogVisible.value = true
}

// 导出成功回调
const handleExportSuccess = () => {
  ElMessage.success('考试记录导出成功')
}

// 过滤特定考试（供父组件调用）
const filterByExam = (examId: string) => {
  if (!examId) return;
  
  filterForm.examId = examId;
  currentPage.value = 1;
  fetchRecordsList();
  fetchStatistics();
  
  // 设置当前考试名称
  const exam = examOptions.value.find(e => e.value === examId);
  if (exam) {
    currentExam.value.name = exam.label;
  }
}

// 暴露给父组件的方法
defineExpose({
  filterByExam
})

// 窗口大小变化时重绘图表
const handleResize = () => {
  deptStatsChart?.resize()
  scoreDistChart?.resize()
  durationDistChart?.resize()
}

// 页面加载时获取数据
onMounted(() => {
  fetchRecordsList()
  fetchStatistics()
  initExamOptions()
  initDepartmentOptions()
  
  // 初始化图表
  nextTick(() => {
    updateCharts()
  })
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  deptStatsChart?.dispose()
  scoreDistChart?.dispose()
  durationDistChart?.dispose()
})

// 获取阅卷状态显示文本
const getMarkingStatusText = (status: string) => {
  const statusMap: { [key: string]: string } = {
    'pending': '待阅卷',
    'marking': '阅卷中',
    'marked': '已阅卷'
  }
  return statusMap[status] || '未知状态'
}

// 获取阅卷状态标签类型
const getMarkingStatusType = (status: string) => {
  const typeMap: { [key: string]: string } = {
    'pending': 'warning',
    'marking': 'info',
    'marked': 'success'
  }
  return typeMap[status] || 'info'
}

// 查看详情
const handleViewDetail = (row: any) => {
  router.push(`/exam/record/detail/${row.id}`)
}

// 进行阅卷
const handleMarking = (row: any) => {
  router.push(`/exam/marking/${row.id}`)
}

// 查看成绩
const handleViewResults = (row: any) => {
  router.push(`/exam/record/result/${row.id}`)
}

// 导出成绩
const handleExportResults = async () => {
  if (!filterForm.examId) {
    ElMessage.warning('请先选择考试')
    return
  }

  try {
    ElMessage.info('正在生成成绩单，请稍候...')

    // 构建导出参数
    const exportParams = {
      examId: filterForm.examId,
      departmentId: filterForm.departmentId || undefined,
      keyword: filterForm.keyword || undefined,
      format: 'excel',
      includeStatistics: true
    }

    // 调用导出API
    const response = await fetch('/api/exam-record/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(exportParams)
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url

      // 获取考试名称作为文件名
      const examName = examOptions.value.find(e => e.value === filterForm.examId)?.label || '考试'
      a.download = `${examName}_成绩单_${new Date().toISOString().slice(0, 10)}.xlsx`

      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)

      ElMessage.success('成绩单导出成功')
    } else {
      const errorText = await response.text()
      ElMessage.error(`导出失败: ${errorText}`)
    }
  } catch (error) {
    console.error('导出成绩单失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 监听路由参数变化，用于从考试安排切换过来时设置筛选条件
watch(() => route.query, (query) => {
  if (query.tab === 'records' && query.examId) {
    const examId = query.examId.toString();
    
    // 确保选项已加载
    if (examOptions.value.length === 0) {
      // 先初始化选项数据，然后再过滤
      initExamOptions().then(() => {
        filterByExam(examId);
      });
    } else {
      filterByExam(examId);
    }
  }
}, { immediate: true })
</script>

<style scoped>
.exam-records {
  width: 100%;
}

.filter-form {
  margin-bottom: 20px;
}

.statistics-card {
  margin-bottom: 20px;
}

.statistics-header {
  margin-bottom: 20px;
}

.statistics-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.statistics-content {
  padding: 10px 0;
}

.statistics-item {
  text-align: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.statistics-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.statistics-label {
  font-size: 14px;
  color: #606266;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.pass-score {
  color: #67c23a;
  font-weight: bold;
}

.fail-score {
  color: #f56c6c;
  font-weight: bold;
}

.charts-container {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.chart-wrapper {
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  padding: 15px;
}

.chart-wrapper h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  text-align: center;
}

.chart {
  width: 100%;
}
</style> 