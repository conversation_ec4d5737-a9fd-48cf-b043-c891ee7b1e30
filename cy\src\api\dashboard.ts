import { get } from '@/utils/request'

/**
 * Dashboard统计数据接口
 */
export interface DashboardStatistics {
  totalStudents: number
  totalCourses: number
  totalExams: number
  totalPosts: number
}

/**
 * 最新数据接口
 */
export interface RecentData {
  recentStudents: any[]
  recentCourses: any[]
  recentExams: any[]
  recentPosts: any[]
}

/**
 * 获取Dashboard统计数据
 */
export function getDashboardStatistics() {
  return get<DashboardStatistics>('/dashboard/statistics')
}

/**
 * 获取最新数据
 */
export function getRecentData() {
  return get<RecentData>('/dashboard/recent')
}
