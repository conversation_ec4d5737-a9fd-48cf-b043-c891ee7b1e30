package com.cy.education.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 考试VO类
 */
@Data
@ApiModel("考试信息")
public class ExamVO {
    
    /**
     * 考试ID
     */
    @ApiModelProperty("考试ID")
    private Integer id;
    
    /**
     * 考试标题
     */
    @ApiModelProperty("考试标题")
    private String title;
    
    /**
     * 考试描述
     */
    @ApiModelProperty("考试描述")
    private String description;
    
    /**
     * 试卷ID
     */
    @ApiModelProperty("试卷ID")
    private Integer paperId;
    
    /**
     * 试卷信息
     */
    @ApiModelProperty("试卷信息")
    private ExamPaperVO paper;
    
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;
    
    /**
     * 考试时长(分钟)
     */
    @ApiModelProperty("考试时长(分钟)")
    private Integer duration;
    
    /**
     * 及格分数
     */
    @ApiModelProperty("及格分数")
    private Integer passingScore;
    
    /**
     * 最大考试次数
     */
    @ApiModelProperty("最大考试次数")
    private Integer maxAttempts;
    
    /**
     * 是否发布
     */
    @ApiModelProperty("是否发布")
    private Boolean isPublished;
    
    /**
     * 状态(0-草稿,1-未开始,2-进行中,3-已结束)
     */
    @ApiModelProperty("状态(0-草稿,1-未开始,2-进行中,3-已结束)")
    private Integer status;
    
    /**
     * 参考部门ID列表
     */
    @ApiModelProperty("参考部门ID列表")
    private List<Integer> departmentIds;
    
    /**
     * 参考部门名称列表
     */
    @ApiModelProperty("参考部门名称列表")
    private List<String> departments;
    
    /**
     * 参与人数
     */
    @ApiModelProperty("参与人数")
    private Integer participantCount;
    
    /**
     * 完成人数
     */
    @ApiModelProperty("完成人数")
    private Integer completedCount;
    
    /**
     * 通过人数
     */
    @ApiModelProperty("通过人数")
    private Integer passedCount;
    
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;
    
    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    private String createdBy;
} 