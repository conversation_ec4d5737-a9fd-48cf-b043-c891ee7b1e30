<template>
  <div class="points-goods">
    <div class="page-header">
      <h2>商品管理</h2>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" ref="filterFormRef" :inline="true">
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="filterForm.name" placeholder="请输入商品名称" clearable />
        </el-form-item>
        <el-form-item label="商品分类" prop="category">
          <el-select v-model="filterForm.category" placeholder="请选择商品分类" clearable>
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
            <el-option label="上架" value="active" />
            <el-option label="下架" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleAddGoods">新增商品</el-button>
      <el-button type="primary" @click="handleShowStats">商品统计</el-button>
      <el-button @click="fetchGoodsList">刷新</el-button>
    </div>

    <!-- 商品列表 -->
    <el-table
      v-loading="loading"
      :data="goodsList"
      border
      style="width: 100%; margin-top: 16px"
    >
      <el-table-column type="index" width="50" align="center" />
      <el-table-column label="商品图片" width="100" align="center">
        <template #default="{ row }">
          <el-image
            style="width: 60px; height: 60px"
            :src="row.image"
            :preview-src-list="[row.image]"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="name" label="商品名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="category" label="分类" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getCategoryTagType(row.category)">
            {{ getCategoryLabel(row.category) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="points" label="所需积分" width="100" align="center" />
      <el-table-column prop="stock" label="库存" width="100" align="center">
        <template #default="{ row }">
          <el-tag v-if="row.stock <= row.lowStockThreshold" type="danger">{{ row.stock }}</el-tag>
          <span v-else>{{ row.stock }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="exchangeCount" label="兑换次数" width="100" align="center" />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '上架' : '下架' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="160" align="center" />
      <el-table-column label="操作" width="250" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleRestock(row)">补货</el-button>
          <el-button
            :type="row.status === 'active' ? 'warning' : 'success'"
            link
            @click="handleToggleStatus(row)"
          >
            {{ row.status === 'active' ? '下架' : '上架' }}
          </el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 商品表单弹窗 -->
    <el-dialog
      v-model="goodsDialogVisible"
      :title="isEdit ? '编辑商品' : '新增商品'"
      width="600px"
      destroy-on-close
    >
      <el-form ref="goodsFormRef" :model="goodsForm" :rules="goodsRules" label-width="100px">
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="goodsForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品分类" prop="category">
          <el-select v-model="goodsForm.category" placeholder="请选择商品分类" style="width: 100%">
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所需积分" prop="points">
          <el-input-number v-model="goodsForm.points" :min="1" :max="10000" style="width: 100%" />
        </el-form-item>
        <el-form-item label="库存数量" prop="stock">
          <el-input-number v-model="goodsForm.stock" :min="0" :max="10000" style="width: 100%" />
        </el-form-item>
        <el-form-item label="库存预警" prop="lowStockThreshold">
          <el-input-number
            v-model="goodsForm.lowStockThreshold"
            :min="0"
            :max="1000"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="上架状态" prop="status">
          <el-switch v-model="goodsForm.status" active-value="active" inactive-value="inactive" />
        </el-form-item>
        <el-form-item label="商品图片" prop="image">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :http-request="customUploadRequest"
          >
            <img v-if="goodsForm.image" :src="goodsForm.image" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="goodsForm.description"
            type="textarea"
            rows="4"
            placeholder="请输入商品描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="goodsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitGoodsForm" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 补货弹窗 -->
    <el-dialog
      v-model="restockDialogVisible"
      title="商品补货"
      width="400px"
      destroy-on-close
    >
      <el-form ref="restockFormRef" :model="restockForm" :rules="restockRules" label-width="100px">
        <div class="restock-header">
          <span>当前库存: {{ currentGoods.stock }}</span>
          <span v-if="currentGoods.stock <= (currentGoods.lowStockThreshold || 0)" class="stock-warning">
            (库存不足)
          </span>
        </div>
        <el-form-item label="补货数量" prop="amount">
          <el-input-number
            v-model="restockForm.amount"
            :min="1"
            :max="1000"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="restockForm.remark"
            placeholder="请输入补货备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="restockDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitRestock" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 商品统计弹窗 -->
    <el-dialog
      v-model="statsDialogVisible"
      title="商品统计"
      width="800px"
      destroy-on-close
    >
      <div class="stats-container">
        <div class="stats-summary">
          <div class="stats-item">
            <div class="stats-label">商品总数</div>
            <div class="stats-value">{{ goodsStats.totalProducts }}</div>
          </div>
          <div class="stats-item">
            <div class="stats-label">上架商品</div>
            <div class="stats-value">{{ goodsStats.activeProducts }}</div>
          </div>
          <div class="stats-item">
            <div class="stats-label">总兑换次数</div>
            <div class="stats-value">{{ goodsStats.totalExchanges }}</div>
          </div>
          <div class="stats-item">
            <div class="stats-label">低库存商品</div>
            <div class="stats-value">{{ goodsStats.lowStockProducts }}</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, UploadProps } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { 
  getProductList,
  getProductCategories,
  createProduct,
  updateProduct,
  deleteProduct,
  changeProductStatus,
  updateProductStock,
  getProductStatistics,
  type Product,
  type ProductQueryParams,
  type ProductCategory,
  type ProductStatistics
} from '@/api/product'

// 表格数据和分页
const loading = ref(false)
const goodsList = ref<Product[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 筛选表单
const filterFormRef = ref()
const filterForm = reactive<ProductQueryParams>({
  name: '',
  category: '',
  status: ''
})

// 商品分类选项
const categoryOptions = ref([
  { value: 'physical', label: '实物商品' },
  { value: 'virtual', label: '虚拟物品' },
  { value: 'service', label: '服务类' },
  { value: 'other', label: '其他' }
])

// 商品表单相关
const goodsDialogVisible = ref(false)
const goodsFormRef = ref()
const submitting = ref(false)
const isEdit = ref(false)
const goodsForm = reactive<Partial<Product>>({
  name: '',
  category: '',
  points: 0,
  stock: 0,
  lowStockThreshold: 10,
  status: 'active',
  image: '',
  description: ''
})

const goodsRules = {
  name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  points: [{ required: true, message: '请输入所需积分', trigger: 'blur' }],
  stock: [{ required: true, message: '请输入库存数量', trigger: 'blur' }],
  lowStockThreshold: [{ required: true, message: '请输入库存预警值', trigger: 'blur' }]
}

// 补货相关
const restockDialogVisible = ref(false)
const restockFormRef = ref()
const currentGoods = ref<Product>({
  id: '',
  name: '',
  description: '',
  image: '',
  points: 0,
  category: '',
  stock: 0,
  lowStockThreshold: 10,
  limitPerUser: 0,
  exchangeCount: 0,
  status: 'active',
  tags: [],
  createdAt: '',
  updatedAt: ''
})
const restockForm = reactive({
  amount: 0,
  remark: ''
})

const restockRules = {
  amount: [{ required: true, message: '请输入补货数量', trigger: 'blur' }]
}

// 统计相关
const statsDialogVisible = ref(false)
const goodsStats = ref<ProductStatistics>({
  totalProducts: 0,
  activeProducts: 0,
  totalExchanges: 0,
  outOfStockCount: 0,
  lowStockProducts: 0,
  categoryDistribution: [],
  popularProducts: []
})

// 获取商品列表
const fetchGoodsList = async () => {
  loading.value = true
  try {
    const params: ProductQueryParams = {
      page: currentPage.value,
      limit: pageSize.value,
      ...filterForm
    }
    
    const data = await getProductList(params)
    goodsList.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 获取商品分类
// 注释掉获取商品分类的方法，直接使用硬编码的分类
// const fetchCategories = async () => {
//   try {
//     const data = await getProductCategories()
//     categoryOptions.value = data.map(category => ({
//       ...category,
//       label: category.name,
//       value: category.id
//     }))
//   } catch (error) {
//     console.error('获取商品分类失败:', error)
//   }
// }

// 分类标签
const getCategoryLabel = (category: string) => {
  const categoryMap: Record<string, string> = {
    'physical': '实物商品',
    'virtual': '虚拟物品',
    'service': '服务类',
    'other': '其他'
  }
  return categoryMap[category] || category
}

const getCategoryTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    'physical': 'primary',
    'virtual': 'success',
    'service': 'warning',
    'other': 'info'
  }
  return typeMap[category] || ''
}

// 筛选和重置
const handleFilter = () => {
  currentPage.value = 1
  fetchGoodsList()
}

const resetFilter = () => {
  filterFormRef.value?.resetFields()
  Object.keys(filterForm).forEach(key => {
    filterForm[key as keyof ProductQueryParams] = '' as any
  })
  currentPage.value = 1
  fetchGoodsList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  fetchGoodsList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchGoodsList()
}

// 新增商品
const handleAddGoods = () => {
  isEdit.value = false
  goodsDialogVisible.value = true
  // 重置表单
  Object.assign(goodsForm, {
    name: '',
    category: '',
    points: 0,
    stock: 0,
    lowStockThreshold: 10,
    status: 'active',
    image: '',
    description: ''
  })
}

// 编辑商品
const handleEdit = (row: Product) => {
  isEdit.value = true
  goodsDialogVisible.value = true
  Object.assign(goodsForm, { ...row })
}

// 提交商品表单
const submitGoodsForm = async () => {
  if (!goodsFormRef.value) return
  
  try {
    await goodsFormRef.value.validate()
    submitting.value = true
    
    if (isEdit.value) {
      await updateProduct(goodsForm.id!, goodsForm)
      ElMessage.success('商品更新成功')
    } else {
      await createProduct(goodsForm)
      ElMessage.success('商品创建成功')
    }
    
    goodsDialogVisible.value = false
    fetchGoodsList()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 删除商品
const handleDelete = async (row: Product) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该商品吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteProduct(row.id)
    ElMessage.success('删除成功')
    fetchGoodsList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 切换状态
const handleToggleStatus = async (row: Product) => {
  try {
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    await changeProductStatus(row.id, newStatus)
    
    ElMessage.success(`商品已${newStatus === 'active' ? '上架' : '下架'}`)
    fetchGoodsList()
  } catch (error) {
    console.error('状态切换失败:', error)
    ElMessage.error('状态切换失败')
  }
}

// 补货
const handleRestock = (row: Product) => {
  currentGoods.value = row
  restockDialogVisible.value = true
  restockForm.amount = 0
  restockForm.remark = ''
}

const submitRestock = async () => {
  if (!restockFormRef.value) return
  
  try {
    await restockFormRef.value.validate()
    submitting.value = true
    
    await updateProductStock(currentGoods.value.id, restockForm.amount)
    
    ElMessage.success('补货成功')
    restockDialogVisible.value = false
    fetchGoodsList()
  } catch (error) {
    console.error('补货失败:', error)
    ElMessage.error('补货失败')
  } finally {
    submitting.value = false
  }
}

// 商品统计
const handleShowStats = async () => {
  try {
    const data = await getProductStatistics()
    goodsStats.value = data
    statsDialogVisible.value = true
  } catch (error) {
    console.error('获取商品统计失败:', error)
    ElMessage.error('获取商品统计失败')
  }
}

// 文件上传
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
    ElMessage.error('只能上传 JPG/PNG 格式的图片!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 1) {
    ElMessage.error('图片大小不能超过 1MB!')
    return false
  }
  return true
}

const customUploadRequest = (params: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    // 压缩图片
    const img = new Image()
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      // 计算压缩后的尺寸，最大宽度或高度为300px
      let width = img.width
      let height = img.height
      const maxSize = 300
      
      if (width > height && width > maxSize) {
        height = Math.round((height * maxSize) / width)
        width = maxSize
      } else if (height > maxSize) {
        width = Math.round((width * maxSize) / height)
        height = maxSize
      }
      
      canvas.width = width
      canvas.height = height
      
      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height)
      
      // 转换为base64，使用较低的质量
      const compressedImage = canvas.toDataURL('image/jpeg', 0.7)
      
      // 设置图片URL
      goodsForm.image = compressedImage
    }
    img.src = e.target?.result as string
  }
  reader.readAsDataURL(params.file)
}

// 页面加载时获取数据
onMounted(() => {
  // 注释掉这行，不再获取商品分类
  // fetchCategories()
  fetchGoodsList()
})
</script>

<style scoped>
.points-goods {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.filter-card {
  margin-bottom: 20px;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 头像上传 */
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
}

/* 补货弹窗 */
.restock-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
}

.stock-warning {
  color: #f56c6c;
  margin-left: 10px;
}

/* 统计弹窗样式 */
.stats-container {
  padding: 10px;
}

.stats-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stats-item {
  text-align: center;
  padding: 10px 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  flex: 1;
  margin: 0 5px;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #67C23A;
}

.chart-container {
  display: flex;
  margin-top: 20px;
}

.category-chart, .exchange-chart {
  flex: 1;
  height: 300px;
}
</style> 