// 加载状态
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

// 错误状态
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-text {
  font-size: 16px;
  color: #8e8e93;
  margin: 16px 0 24px 0;
}

.retry-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.retry-text {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
}

.exam-info-card,
.section-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.exam-header {
  margin-bottom: 20px;
}

.exam-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.exam-title {
  flex: 1;
  font-size: 20px;
  font-weight: 600;
  color: #1a1d2e;
  line-height: 1.3;
}

.exam-status {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;

  &.upcoming {
    background: #fff2e8;
    color: #fa8c16;
  }

  &.ongoing {
    background: #f6ffed;
    color: #52c41a;
  }

  &.completed {
    background: #f0f0f0;
    color: #666;
  }
}

.exam-meta-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 16px;
  padding: 10px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.meta-divider {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.2), transparent);
}

// 考试时间区域
.exam-time-section {
  margin-top: 8px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 12px;
  border: 1px solid rgba(79, 172, 254, 0.1);
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.1);
}

.time-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(79, 172, 254, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.time-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-label {
  font-size: 12px;
  color: #8e8e93;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

@media (max-width: 375px) {
  .exam-meta-row {
    gap: 4px;
    padding: 8px;
  }

  .meta-icon {
    width: 28px;
    height: 28px;
    border-radius: 14px;
  }

  .meta-label {
    font-size: 10px;
  }

  .meta-value {
    font-size: 12px;
  }

  .time-range {
    gap: 8px;
  }

  .time-block {
    min-width: 75px;
    padding: 6px;
  }

  .time-date {
    font-size: 11px;
  }

  .time-hour {
    font-size: 13px;
  }
}

.time-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.15) 0%, rgba(79, 172, 254, 0.05) 100%);
  padding: 3px;
  border-radius: 10px;
  min-width: 85px;
  border: 1px solid rgba(79, 172, 254, 0.2);
  box-shadow: 0 2px 6px rgba(79, 172, 254, 0.1);
  transition: all 0.3s ease;
}

.time-block:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.2);
}

.time-date {
  font-size: 12px;
  color: #4facfe;
  font-weight: 600;
}

.time-hour {
  font-size: 14px;
  color: #1a1d2e;
  font-weight: 600;
}

.time-separator {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
  padding: 0 4px;
}

.time-tip {
  font-size: 11px;
  color: #8e8e93;
  margin-top: 8px;
  text-align: center;
  font-style: italic;
  padding: 4px 8px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
  text-align: center;
  transition: all 0.3s ease;
}

.meta-item:hover {
  transform: scale(1.05);
}

.meta-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.meta-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: center;
}

.meta-label {
  font-size: 11px;
  color: #8e8e93;
  font-weight: 500;
}

.meta-value {
  font-size: 13px;
  color: #1a1d2e;
  font-weight: 600;
}

// 区域样式
.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.section-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.description-text {
  font-size: 14px;
  line-height: 1.6;
  color: #4a5568;
}

// 题型分布
.type-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.type-item {
  padding: 16px;
  background: #f8faff;
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.type-name {
  font-size: 14px;
  color: #1a1d2e;
  font-weight: 600;
}

.type-score {
  font-size: 16px;
  color: #667eea;
  font-weight: 600;
}

.type-count {
  font-size: 12px;
  color: #8e8e93;
}

// 考试规则
.rules-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #fff8f0;
  border-radius: 8px;
  border-left: 3px solid #fa709a;
}

.rule-number {
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background: #fa709a;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.rule-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
  color: #4a5568;
}

// 统计数据
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8faff;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

.stat-label {
  font-size: 12px;
  color: #8e8e93;
}

// 分数统计
.score-stats-section {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.score-stats-header {
  margin-bottom: 12px;
}

.score-stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.score-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.score-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
}

.score-stat-label {
  font-size: 12px;
  color: #8e8e93;
}

.score-stat-value {
  font-size: 16px;
  font-weight: 600;

  &.pass-rate {
    color: #10b981;
  }

  &.highest {
    color: #f59e0b;
  }

  &.lowest {
    color: #ef4444;
  }

  &.median {
    color: #3b82f6;
  }
}

// 分数分布
.score-distribution-section,
.duration-distribution-section {
  margin-bottom: 20px;
}

.distribution-header {
  margin-bottom: 12px;
}

.distribution-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.distribution-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
}

.distribution-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  border-radius: 8px;
  text-align: center;

  &.excellent {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  &.good {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  &.moderate {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
  }

  &.pass {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.2);
  }

  &.fail {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
}

.distribution-label {
  font-size: 12px;
  font-weight: 600;

  .excellent & {
    color: #10b981;
  }

  .good & {
    color: #3b82f6;
  }

  .moderate & {
    color: #f59e0b;
  }

  .pass & {
    color: #8b5cf6;
  }

  .fail & {
    color: #ef4444;
  }
}

.distribution-value {
  font-size: 16px;
  font-weight: 700;
  color: #1a1d2e;
}

.distribution-range {
  font-size: 10px;
  color: #8e8e93;
}

// 完成时间分布
.duration-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
}

.duration-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  border-radius: 8px;
  text-align: center;

  &.very-fast {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  &.fast {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  &.normal {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
  }

  &.slow {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.2);
  }

  &.very-slow {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
}

.duration-label {
  font-size: 12px;
  font-weight: 600;

  .very-fast & {
    color: #10b981;
  }

  .fast & {
    color: #3b82f6;
  }

  .normal & {
    color: #f59e0b;
  }

  .slow & {
    color: #8b5cf6;
  }

  .very-slow & {
    color: #ef4444;
  }
}

.duration-value {
  font-size: 16px;
  font-weight: 700;
  color: #1a1d2e;
}

.duration-desc {
  font-size: 10px;
  color: #8e8e93;
}

// 统计概览
.stats-overview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 16px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.overview-label {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
}

.overview-value {
  font-size: 18px;
  font-weight: 600;
  color: #1a1d2e;
}

// 统计操作
.stats-action {
  margin-top: 16px;
}

.stats-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.stats-btn:active {
  transform: scale(0.98);
}

.stats-btn-text {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
}

// 考试次数统计
.attempt-stats-section {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #fff8f0 0%, #fef3c7 100%);
  border-radius: 12px;
  border: 1px solid rgba(245, 158, 11, 0.1);
}

.attempt-stats-header {
  margin-bottom: 12px;
}

.attempt-stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.attempt-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.attempt-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
}

.attempt-stat-label {
  font-size: 12px;
  color: #8e8e93;
}

.attempt-stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #f59e0b;
}

// 进度条统计
.progress-stats-section {
  margin-bottom: 20px;
}

.progress-stats-header {
  margin-bottom: 12px;
}

.progress-stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

// 部门统计
.department-stats-section {
  margin-bottom: 20px;
}

.department-stats-header {
  margin-bottom: 12px;
}

.department-stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.department-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.department-item {
  padding: 16px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.department-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.department-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1d2e;
}

.department-count {
  font-size: 12px;
  color: #8e8e93;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}

.department-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.dept-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
}

.dept-stat-label {
  font-size: 12px;
  color: #8e8e93;
}

.dept-stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1d2e;
}

// 操作按钮
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #f0f2f5;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
  z-index: 999;
}

.action-btn {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;

  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  }

  &.primary:active {
    transform: scale(0.98);
  }

  &.secondary {
    background: #f8faff;
    color: #667eea;
    border: 1px solid #667eea;
  }

  &.secondary:active {
    background: #f0f4ff;
  }

  &.disabled {
    background: #f5f6fa;
    color: #8e8e93;
    cursor: not-allowed;
  }
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
  color: #fff
}
