package com.cy.education.controller;

import com.cy.education.service.PkService;
import com.cy.education.websocket.PkWebSocketServer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * PK功能测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/pk/test")
@Api(tags = "PK功能测试")
public class PkTestController {
    
    @Autowired
    private PkService pkService;
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @ApiOperation("健康检查")
    public Map<String, Object> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "ok");
        result.put("timestamp", System.currentTimeMillis());
        result.put("service", "PK Service");
        return result;
    }

    /**
     * 测试PK服务是否正常
     */
    @GetMapping("/service")
    @ApiOperation("测试PK服务")
    public Map<String, Object> testPkService() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试服务是否注入成功
            boolean serviceAvailable = pkService != null;
            result.put("pkServiceAvailable", serviceAvailable);
            
            // 测试WebSocket服务中的依赖注入
            boolean webSocketServiceAvailable = PkWebSocketServer.getPkService() != null;
            result.put("webSocketServiceAvailable", webSocketServiceAvailable);
            
            // 测试在线用户数
            int onlineCount = PkWebSocketServer.getOnlineCount();
            result.put("onlineCount", onlineCount);
            
            result.put("success", true);
            result.put("message", "PK服务测试完成");
            
            log.info("PK服务测试结果: {}", result);
            
        } catch (Exception e) {
            log.error("PK服务测试失败", e);
            result.put("success", false);
            result.put("message", "PK服务测试失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 测试数据库连接
     */
    @GetMapping("/database")
    @ApiOperation("测试数据库连接")
    public Map<String, Object> testDatabase() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里可以添加数据库连接测试逻辑
            // 例如查询一个简单的表
            
            result.put("success", true);
            result.put("message", "数据库连接正常");
            
        } catch (Exception e) {
            log.error("数据库测试失败", e);
            result.put("success", false);
            result.put("message", "数据库测试失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    @ApiOperation("获取系统状态")
    public Map<String, Object> getSystemStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 系统信息
            result.put("javaVersion", System.getProperty("java.version"));
            result.put("osName", System.getProperty("os.name"));
            result.put("osVersion", System.getProperty("os.version"));
            
            // 内存信息
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            Map<String, Object> memoryInfo = new HashMap<>();
            memoryInfo.put("total", totalMemory / 1024 / 1024 + " MB");
            memoryInfo.put("used", usedMemory / 1024 / 1024 + " MB");
            memoryInfo.put("free", freeMemory / 1024 / 1024 + " MB");
            
            result.put("memory", memoryInfo);
            
            // WebSocket状态
            result.put("webSocketOnlineCount", PkWebSocketServer.getOnlineCount());
            
            result.put("success", true);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            result.put("success", false);
            result.put("message", "获取系统状态失败: " + e.getMessage());
        }
        
        return result;
    }
}
