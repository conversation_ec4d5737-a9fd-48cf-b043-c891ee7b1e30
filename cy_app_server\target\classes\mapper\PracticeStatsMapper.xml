<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.PracticeStatsMapper">

    <!-- 根据用户ID查询练习统计 -->
    <select id="selectByUserId" resultType="com.cy.education.model.entity.PracticeStats">
        SELECT
            user_id as userId,
            bank_id as bankId,
            bank_name as bankName,
            total_questions as totalQuestions,
            answered_questions as answeredQuestions,
            correct_count as correctCount,
            wrong_count as wrongCount,
            accuracy_rate as accuracyRate,
            last_practice_time as lastPracticeTime
        FROM practice_stats_complete_view
        WHERE user_id = #{userId}
        ORDER BY last_practice_time DESC
    </select>

    <!-- 根据用户ID和题库ID查询练习统计 -->
    <select id="selectByUserIdAndBankId" resultType="com.cy.education.model.entity.PracticeStats">
        SELECT
            user_id as userId,
            bank_id as bankId,
            bank_name as bankName,
            total_questions as totalQuestions,
            answered_questions as answeredQuestions,
            correct_count as correctCount,
            wrong_count as wrongCount,
            accuracy_rate as accuracyRate,
            last_practice_time as lastPracticeTime
        FROM practice_stats_complete_view
        WHERE user_id = #{userId} AND bank_id = #{bankId}
    </select>

    <!-- 根据题库ID查询练习统计 -->
    <select id="selectByBankId" resultType="com.cy.education.model.entity.PracticeStats">
        SELECT ps.*, s.name as student_name FROM practice_stats ps
        LEFT JOIN students s ON ps.user_id = s.id
        WHERE ps.bank_id = #{bankId}
        ORDER BY ps.last_practice_time DESC
    </select>

</mapper> 