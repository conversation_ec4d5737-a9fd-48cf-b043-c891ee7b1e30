<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="navbar-left">
          <text class="navbar-title">学习中心</text>
        </view>
        <view class="navbar-right">
          <view class="navbar-search">
            <view class="search-input-wrapper">
              <up-icon name="search" size="16" color="#8e8e93"></up-icon>
              <input
                v-model="searchKeyword"
                placeholder="搜索课程..."
                class="search-input"
                @input="performSearch"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 课程列表 -->
      <view class="course-section">
        <view class="section-header">
          <text class="section-title">全部</text>
          <view class="view-toggle">
            <view
              class="toggle-btn"
              :class="{ active: viewMode === 'grid' }"
              @click="setViewMode('grid')"
            >
              <up-icon name="grid" size="18" :color="viewMode === 'grid' ? '#fff' : '#8e8e93'"></up-icon>
            </view>
            <view
              class="toggle-btn"
              :class="{ active: viewMode === 'list' }"
              @click="setViewMode('list')"
            >
              <up-icon name="list" size="18" :color="viewMode === 'list' ? '#fff' : '#8e8e93'"></up-icon>
            </view>
          </view>
        </view>

        <!-- 网格视图 -->
        <view v-if="viewMode === 'grid'" class="course-grid">
          <view
              v-for="(course, index) in courses"
            :key="index"
            class="course-card"
            @click="handleCourseDetail(course)"
          >
            <view class="course-cover-container">
              <up-image
                  :src="course.coverImageUrl || 'https://picsum.photos/200/120?random=' + index"
                width="100%"
                height="120"
                mode="aspectFill"
                radius="12"
                class="course-cover"
              ></up-image>
              <view class="course-overlay">
                <view class="play-btn">
                  <up-icon name="play-circle-fill" color="#fff" size="32"></up-icon>
                </view>
              </view>
              <view v-if="course.isNew" class="course-badge new">新</view>
              <view v-if="course.isHot" class="course-badge hot">热</view>
            </view>

            <view class="course-info">
              <text class="course-title">{{ course.name }}</text>

              <view class="course-stats">
                <view class="stat-item">
                  <up-icon name="account" size="12" color="#8e8e93"></up-icon>
                  <text class="stat-text">{{ course.studyCount || 0 }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 列表视图 -->
        <view v-if="viewMode === 'list'" class="course-list">
          <view
              v-for="(course, index) in courses"
            :key="index"
            class="course-item"
            @click="handleCourseDetail(course)"
          >
            <view class="item-cover">
              <up-image
                  :src="course.coverImageUrl || 'https://picsum.photos/100/80?random=' + index"
                width="100"
                height="80"
                mode="aspectFill"
                radius="8"
                class="cover-image"
              ></up-image>
            </view>

            <view class="item-info">
              <text class="item-title">{{ course.name }}</text>

              <view class="item-stats">
                <view class="stat-item">
                  <up-icon name="account" size="12" color="#8e8e93"></up-icon>
                  <text class="stat-text">{{ course.studyCount || 0 }}人学习</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="courses.length > 0" class="load-more">
          <view v-if="loading" class="loading-text">
            <text>加载中...</text>
          </view>
          <view v-else-if="loadStatus === 'nomore'" class="nomore-text">
            <text>没有更多了</text>
          </view>
          <view v-else class="loadmore-text">
            <text>加载更多</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义底部导航 -->
    <CustomTabbar :current="1" />
  </view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar/index.vue'
import {getCourseList} from '@/api/course'

export default {
  components: {
    CustomTabbar
  },
  data() {
    return {
      viewMode: 'grid',
      courses: [],
      searchKeyword: '',
      loadStatus: 'loadmore',
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false
    }
  },
  computed: {},
  onLoad() {
    this.pageNum = 1;
    this.courses = [];
    this.fetchCourses();
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.pageNum = 1;
    this.courses = [];
    this.fetchCourses();
    uni.stopPullDownRefresh();
  },
  // 上拉加载更多
  onReachBottom() {
    if (this.loadStatus === 'nomore' || this.loading) return;
    this.pageNum += 1;
    this.fetchCourses(true);
  },
  methods: {
    async fetchCourses(isLoadMore = false) {
      if (this.loading) return;
      this.loading = true;
      if (!isLoadMore) {
        this.pageNum = 1;
        this.courses = [];
        this.loadStatus = 'loadmore';
      }
      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          name: this.searchKeyword || undefined
        };
        const res = await getCourseList(params);
        this.total = res.total || 0;
        if (isLoadMore) {
          this.courses = this.courses.concat(res.list || []);
        } else {
          this.courses = res.list || [];
        }
        if (this.courses.length >= this.total) {
          this.loadStatus = 'nomore';
        } else {
          this.loadStatus = 'loadmore';
        }
      } catch (e) {
        this.loadStatus = 'nomore';
      } finally {
        this.loading = false;
      }
    },
    setViewMode(mode) {
      this.viewMode = mode;
    },
    performSearch() {
      this.pageNum = 1;
      this.courses = [];
      this.fetchCourses();
    },
    handleCourseDetail(course) {
      uni.navigateTo({url: `/pages/study/course-detail?id=${course.id}`})
    }
  },
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/pages/study/study.scss';
</style>
