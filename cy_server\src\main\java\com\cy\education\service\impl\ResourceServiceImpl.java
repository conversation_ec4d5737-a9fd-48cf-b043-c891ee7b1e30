package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.Resource;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.ResourceMapper;
import com.cy.education.service.ResourceService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class ResourceServiceImpl extends ServiceImpl<ResourceMapper, Resource> implements ResourceService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public PageResponse<Resource> getResourceList(Integer page, Integer size, String type, String name) {
        QueryWrapper<Resource> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasText(type)) {
            queryWrapper.eq("type", type);
        }
        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }
        queryWrapper.orderByDesc("create_time");

        Page<Resource> resourcePage = baseMapper.selectPage(new Page<>(page, size), queryWrapper);
        return new PageResponse<>(resourcePage.getRecords(), resourcePage.getTotal());
    }

    @Override
    public List<String> getAllTags() {
        // This is not the most efficient way for very large datasets,
        // but it's simple and avoids complex SQL for now.
        // For production with huge data, a dedicated query or a separate tags table would be better.
        List<Resource> resources = baseMapper.selectList(new QueryWrapper<Resource>().select("tags"));
        return resources.stream()
                .map(Resource::getTags)
                .filter(StringUtils::hasText)
                .flatMap(tagsJson -> {
                    try {
                        List<String> tags = objectMapper.readValue(tagsJson, new TypeReference<List<String>>() {});
                        return tags.stream();
                    } catch (IOException e) {
                        // Log the error or handle it as needed
                        return Stream.empty();
                    }
                })
                .distinct()
                .collect(Collectors.toList());
    }
}
