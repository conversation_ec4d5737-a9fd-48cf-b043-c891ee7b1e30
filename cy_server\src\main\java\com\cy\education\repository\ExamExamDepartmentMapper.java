package com.cy.education.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cy.education.model.entity.ExamExamDepartment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 考试部门关联Mapper接口
 */
@Mapper
public interface ExamExamDepartmentMapper extends BaseMapper<ExamExamDepartment> {

    /**
     * 批量插入考试部门关联
     *
     * @param examId 考试ID
     * @param departmentIds 部门ID列表
     * @return 影响行数
     */
    int batchInsert(@Param("examId") Integer examId, @Param("departmentIds") List<Integer> departmentIds);

    /**
     * 根据考试ID删除所有部门关联
     *
     * @param examId 考试ID
     * @return 影响行数
     */
    int deleteByExamId(@Param("examId") Integer examId);

    /**
     * 根据考试ID查询部门ID列表
     *
     * @param examId 考试ID
     * @return 部门ID列表
     */
    @Select("SELECT department_id FROM exam_exam_department WHERE exam_id = #{examId}")
    List<Integer> selectDepartmentIdsByExamId(@Param("examId") Integer examId);
} 