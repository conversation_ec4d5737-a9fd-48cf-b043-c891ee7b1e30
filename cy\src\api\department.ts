import { get, post, del } from '@/utils/request'

/**
 * 部门接口数据类型
 */
export interface Department {
  id: number
  name: string
  parentId: number | null
  leader?: string
  sort: number
  status: number
  createTime: string
  remark?: string
  children?: Department[]
}

/**
 * 获取部门树
 */
export function getDepartmentTree() {
  return get<Department[]>('/department/tree')
}

/**
 * 获取部门列表（扁平化）
 */
export function getDepartmentList() {
  return get<Department[]>('/department/list')
}

/**
 * 获取部门详情
 * @param id 部门ID
 */
export function getDepartmentById(id: number) {
  return get<Department>(`/department/${id}`)
}

/**
 * 添加部门
 * @param data 部门信息
 */
export function addDepartment(data: Partial<Department>) {
  return post<{ id: number }>('/department/add', data)
}

/**
 * 更新部门
 * @param data 部门信息
 */
export function updateDepartment(data: Partial<Department>) {
  return post<{ success: boolean }>('/department/update', data)
}

/**
 * 删除部门
 * @param id 部门ID
 */
export function deleteDepartment(id: number) {
  return del<{ success: boolean }>(`/department/${id}`)
}

/**
 * 更新部门排序
 * @param data 排序数据
 */
export function updateDepartmentSort(data: { id: number, parentId: number | null, sort: number }[]) {
  return post<{ success: boolean }>('/department/sort', data)
}

/**
 * 获取部门及其所有子部门的ID列表
 * @param departmentId 部门ID
 */
export function getDepartmentAndChildrenIds(departmentId: number) {
  return get<number[]>(`/department/children/${departmentId}`)
} 