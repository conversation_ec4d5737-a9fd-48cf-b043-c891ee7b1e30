﻿// API接口模块统一导出
import type { CourseQueryParams } from './course'
import type { PointsQueryParams } from './points'
import type { ProductQueryParams } from './product'
import type { ExchangeQueryParams } from './exchange'

export * from './user'
export * from './course'
export * from './points'
export * from './product'
export * from './exchange'

// 重新导出不同的查询参数类型，避免命名冲突
export type {
  CourseQueryParams,
  PointsQueryParams,
  ProductQueryParams,
  ExchangeQueryParams
}
