.loading-box, .empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #888;
}
.course-detail {
  padding: 32rpx 24rpx;
}
.course-info {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx 24rpx 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}
.course-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.progress-section {
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin-top: 8rpx;
}
.progress-number {
  color: #667eea;
  font-weight: bold;
}
.duration {
  color: #888;
}
.completed-status {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;

  &.completed {
    background: #f6ffed;
    color: #52c41a;
  }

  &.incomplete {
    background: #fff2f0;
    color: #ff4d4f;
  }

  text {
    margin-left: 8rpx;
  }
}
.completed-status.completed {
  color: #52c41a;
}
.completed-status.incomplete {
  color: #ff4d4f;
}
.structure-list {
  margin-top: 16rpx;
}
.chapter-block {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.chapter-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #333;
}
.lesson-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  &:last-child {
    border-bottom: none;
  }
}
.lesson-label {
  font-size: 26rpx;
  color: #444;
}
.lesson-progress {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
