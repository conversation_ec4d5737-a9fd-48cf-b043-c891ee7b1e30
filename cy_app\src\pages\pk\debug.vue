<template>
  <view class="debug-page">
    <up-navbar title="PK调试页面" :border="false">
      <template #left>
        <up-icon name="arrow-left" size="20" @click="goBack"></up-icon>
      </template>
    </up-navbar>

    <view class="debug-content">
      <!-- 用户信息 -->
      <view class="debug-section">
        <text class="section-title">用户信息</text>
        <view class="info-item">
          <text class="info-label">用户ID:</text>
          <text class="info-value">{{ userId || '未获取到' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">用户信息:</text>
          <text class="info-value">{{ JSON.stringify(userInfo) }}</text>
        </view>
      </view>

      <!-- API测试 -->
      <view class="debug-section">
        <text class="section-title">API测试</text>
        
        <up-button text="测试获取题库" @click="testGetBanks" :loading="loading.banks" size="small"></up-button>
        <view class="result-box" v-if="results.banks">
          <text class="result-text">{{ JSON.stringify(results.banks, null, 2) }}</text>
        </view>
        
        <up-button text="测试获取历史记录" @click="testGetHistory" :loading="loading.history" size="small"></up-button>
        <view class="result-box" v-if="results.history">
          <text class="result-text">{{ JSON.stringify(results.history, null, 2) }}</text>
        </view>

        <up-button text="测试后端健康状态" @click="testBackendHealth" :loading="loading.health" size="small"></up-button>
        <view class="result-box" v-if="results.health">
          <text class="result-text">{{ JSON.stringify(results.health, null, 2) }}</text>
        </view>
      </view>

      <!-- WebSocket测试 -->
      <view class="debug-section">
        <text class="section-title">WebSocket测试</text>
        
        <view class="info-item">
          <text class="info-label">连接状态:</text>
          <text class="info-value" :class="wsStatusClass">{{ wsStatus }}</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">连接地址:</text>
          <text class="info-value">{{ wsUrl }}</text>
        </view>
        
        <up-button text="重新连接WebSocket" @click="reconnectWebSocket" size="small"></up-button>
        <up-button text="发送心跳" @click="sendHeartbeat" size="small"></up-button>
        
        <view class="log-box">
          <text class="log-title">WebSocket日志:</text>
          <view class="log-content">
            <text class="log-item" v-for="(log, index) in wsLogs" :key="index">
              {{ log }}
            </text>
          </view>
        </view>
      </view>

      <!-- 系统信息 -->
      <view class="debug-section">
        <text class="section-title">系统信息</text>
        <view class="info-item">
          <text class="info-label">平台:</text>
          <text class="info-value">{{ systemInfo.platform }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">版本:</text>
          <text class="info-value">{{ systemInfo.version }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">网络类型:</text>
          <text class="info-value">{{ networkType }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getAvailableBanks } from '@/api/practice'
import { getUserPkHistory } from '@/api/pk'

export default {
  data() {
    return {
      userId: null,
      userInfo: null,
      loading: {
        banks: false,
        history: false,
        health: false
      },
      results: {
        banks: null,
        history: null,
        health: null
      },
      websocket: null,
      wsStatus: '未连接',
      wsUrl: '',
      wsLogs: [],
      systemInfo: {},
      networkType: '未知'
    }
  },
  
  computed: {
    wsStatusClass() {
      return {
        'status-connected': this.wsStatus === '已连接',
        'status-connecting': this.wsStatus === '连接中',
        'status-disconnected': this.wsStatus === '未连接' || this.wsStatus === '连接失败'
      }
    }
  },
  
  onLoad() {
    this.initDebugInfo()
  },
  
  onUnload() {
    if (this.websocket) {
      this.websocket.close()
    }
  },
  
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    initDebugInfo() {
      // 获取用户信息
      this.userInfo = uni.getStorageSync('userInfo')
      this.userId = this.userInfo?.id
      
      // 获取系统信息
      uni.getSystemInfo({
        success: (res) => {
          this.systemInfo = res
        }
      })
      
      // 获取网络类型
      uni.getNetworkType({
        success: (res) => {
          this.networkType = res.networkType
        }
      })
      
      // 初始化WebSocket
      this.initWebSocket()
    },
    
    async testGetBanks() {
      this.loading.banks = true
      try {
        const res = await getAvailableBanks()
        this.results.banks = res
        this.addLog('获取题库成功: ' + JSON.stringify(res))
      } catch (error) {
        this.results.banks = { error: error.message }
        this.addLog('获取题库失败: ' + error.message)
      } finally {
        this.loading.banks = false
      }
    },
    
    async testGetHistory() {
      this.loading.history = true
      try {
        const res = await getUserPkHistory(this.userId, 1, 5)
        this.results.history = res

        // 详细分析响应
        if (res && res.success === true) {
          const count = res.list ? res.list.length : 0
          this.addLog(`✅ 获取历史记录成功: 共${count}条记录`)
          this.addLog('📄 响应详情: ' + JSON.stringify(res, null, 2))
        } else {
          this.addLog('⚠️ 获取历史记录响应异常: ' + JSON.stringify(res))
        }
      } catch (error) {
        this.results.history = { error: error.message || '未知错误' }
        this.addLog('❌ 获取历史记录失败: ' + (error.message || JSON.stringify(error)))
      } finally {
        this.loading.history = false
      }
    },
    
    initWebSocket() {
      if (!this.userId) {
        this.addLog('用户ID不存在，无法建立WebSocket连接')
        return
      }

      // 先测试后端健康状态
      this.testBackendHealth().then((isHealthy) => {
        if (isHealthy) {
          this.connectWebSocket()
        } else {
          this.addLog('后端服务不可用，跳过WebSocket连接')
          this.wsStatus = '后端不可用'
        }
      })
    },

    async testBackendHealth() {
      // 如果是从按钮调用，设置loading状态
      if (arguments.length === 0) {
        this.loading.health = true
      }

      try {
        const response = await new Promise((resolve, reject) => {
          uni.request({
            url: 'http://localhost:8090/api/pk/test/health',
            method: 'GET',
            timeout: 3000,
            success: (res) => {
              resolve(res)
            },
            fail: (error) => {
              reject(error)
            }
          })
        })

        this.addLog('后端健康检查: ' + (response.statusCode === 200 ? '正常' : '异常'))

        // 如果是从按钮调用，保存结果
        if (arguments.length === 0) {
          this.results.health = response
        }

        return response.statusCode === 200
      } catch (error) {
        this.addLog('后端健康检查失败: ' + JSON.stringify(error))

        // 如果是从按钮调用，保存错误结果
        if (arguments.length === 0) {
          this.results.health = { error: error.message || '连接失败' }
        }

        return false
      } finally {
        // 如果是从按钮调用，清除loading状态
        if (arguments.length === 0) {
          this.loading.health = false
        }
      }
    },

    connectWebSocket() {
      const baseUrl = process.env.NODE_ENV === 'development' ? 'localhost:8090' : 'your-server-domain.com'
      this.wsUrl = `ws://${baseUrl}/websocket/pk/${this.userId}`

      this.addLog('尝试连接WebSocket: ' + this.wsUrl)
      this.wsStatus = '连接中'

      this.websocket = uni.connectSocket({
        url: this.wsUrl,
        success: () => {
          this.addLog('WebSocket连接请求发送成功')
        },
        fail: (error) => {
          this.addLog('WebSocket连接请求失败: ' + JSON.stringify(error))
          this.wsStatus = '连接失败'
        }
      })

      this.websocket.onOpen(() => {
        this.addLog('WebSocket连接已建立')
        this.wsStatus = '已连接'
      })

      this.websocket.onMessage((res) => {
        this.addLog('收到消息: ' + res.data)
      })

      this.websocket.onError((error) => {
        this.addLog('WebSocket错误: ' + JSON.stringify(error))
        this.wsStatus = '连接失败'
      })

      this.websocket.onClose((res) => {
        this.addLog('WebSocket连接关闭: ' + JSON.stringify(res))
        this.wsStatus = '未连接'
      })
    },
    
    reconnectWebSocket() {
      if (this.websocket) {
        this.websocket.close()
      }
      this.wsLogs = []
      this.wsStatus = '未连接'
      this.initWebSocket()
    },
    
    sendHeartbeat() {
      if (this.websocket && this.wsStatus === '已连接') {
        const message = {
          type: 'heartbeat',
          userId: this.userId,
          timestamp: Date.now()
        }
        
        this.websocket.send({
          data: JSON.stringify(message),
          success: () => {
            this.addLog('心跳发送成功')
          },
          fail: (error) => {
            this.addLog('心跳发送失败: ' + JSON.stringify(error))
          }
        })
      } else {
        this.addLog('WebSocket未连接，无法发送心跳')
      }
    },
    
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.wsLogs.unshift(`[${timestamp}] ${message}`)
      if (this.wsLogs.length > 20) {
        this.wsLogs.pop()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.debug-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.debug-content {
  padding: 20px;
}

.debug-section {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
  display: block;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.info-label {
  font-size: 14px;
  color: #4a5568;
  width: 80px;
  flex-shrink: 0;
}

.info-value {
  font-size: 14px;
  color: #2d3748;
  flex: 1;
  word-break: break-all;
  
  &.status-connected {
    color: #48bb78;
  }
  
  &.status-connecting {
    color: #ed8936;
  }
  
  &.status-disconnected {
    color: #f56565;
  }
}

.result-box, .log-box {
  margin-top: 12px;
  padding: 12px;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.result-text, .log-item {
  font-size: 12px;
  color: #2d3748;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-title {
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  display: block;
  margin-bottom: 8px;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: block;
  margin-bottom: 4px;
  padding: 4px 0;
  border-bottom: 1px solid #e2e8f0;
}
</style>
