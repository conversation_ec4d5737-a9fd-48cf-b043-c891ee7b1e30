package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cy.education.exception.BusinessException;
import com.cy.education.model.entity.Notice;
import com.cy.education.model.vo.ContentQueryParam;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.NoticeMapper;
import com.cy.education.service.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 公告服务实现
 */
@Service
public class NoticeServiceImpl implements NoticeService {

    @Autowired
    private NoticeMapper noticeMapper;

    @Override
    public PageResponse<Notice> listNotices(ContentQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<Notice> queryWrapper = new LambdaQueryWrapper<>();
        
        // 状态条件
        if (param.getStatus() != null) {
            queryWrapper.eq(Notice::getStatus, param.getStatus());
        }
        
        // 置顶条件
        if (param.getIsTop() != null) {
            queryWrapper.eq(Notice::getIsTop, param.getIsTop());
        }
        
        // 重要程度条件
        if (param.getImportance() != null) {
            queryWrapper.eq(Notice::getImportance, param.getImportance());
        }
        
        // 关键词搜索（标题和内容）
        if (!StringUtils.isEmpty(param.getKeyword())) {
            queryWrapper.and(wrapper -> 
                wrapper.like(Notice::getTitle, param.getKeyword())
                        .or()
                        .like(Notice::getContent, param.getKeyword())
            );
        }
        
        // 排序（置顶优先，然后按重要程度降序，最后按发布时间降序）
        queryWrapper.orderByDesc(Notice::getIsTop, Notice::getImportance, Notice::getPublishTime);
        
        // 分页查询
        Page<Notice> page = new Page<>(param.getPageNum(), param.getPageSize());
        Page<Notice> resultPage = noticeMapper.selectPage(page, queryWrapper);
        
        // 转换为自定义分页响应对象
        return PageResponse.of(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    }

    @Override
    public Notice getNoticeById(Integer id) {
        Notice notice = noticeMapper.selectById(id);
        if (notice == null) {
            throw new BusinessException("公告不存在");
        }
        return notice;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addNotice(Notice notice) {
        // 设置默认值
        if (notice.getStatus() == null) {
            notice.setStatus(1); // 默认发布状态
        }
        if (notice.getIsTop() == null) {
            notice.setIsTop(false); // 默认不置顶
        }
        if (notice.getImportance() == null) {
            notice.setImportance(1); // 默认一般重要程度
        }
        
        // 设置发布时间
        if (notice.getPublishTime() == null) {
            notice.setPublishTime(LocalDateTime.now());
        }
        
        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        notice.setCreatedAt(now);
        notice.setUpdatedAt(now);
        
        int result = noticeMapper.insert(notice);
        if (result <= 0) {
            throw new BusinessException("添加公告失败");
        }
        
        return notice.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotice(Notice notice) {
        // 检查公告是否存在
        Notice existingNotice = noticeMapper.selectById(notice.getId());
        if (existingNotice == null) {
            throw new BusinessException("公告不存在");
        }
        
        // 设置更新时间
        notice.setUpdatedAt(LocalDateTime.now());
        
        return noticeMapper.updateById(notice) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNotice(Integer id) {
        // 检查公告是否存在
        Notice notice = noticeMapper.selectById(id);
        if (notice == null) {
            throw new BusinessException("公告不存在");
        }
        
        return noticeMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNoticeStatus(Integer id, Integer status) {
        // 检查公告是否存在
        Notice notice = noticeMapper.selectById(id);
        if (notice == null) {
            throw new BusinessException("公告不存在");
        }
        
        // 检查状态是否合法
        if (status != 0 && status != 1) {
            throw new BusinessException("状态值不合法，应为0或1");
        }
        
        // 创建更新对象
        Notice updateNotice = new Notice();
        updateNotice.setId(id);
        updateNotice.setStatus(status);
        updateNotice.setUpdatedAt(LocalDateTime.now());
        
        return noticeMapper.updateById(updateNotice) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNoticeTopStatus(Integer id, Boolean isTop) {
        // 检查公告是否存在
        Notice notice = noticeMapper.selectById(id);
        if (notice == null) {
            throw new BusinessException("公告不存在");
        }
        
        // 创建更新对象
        Notice updateNotice = new Notice();
        updateNotice.setId(id);
        updateNotice.setIsTop(isTop);
        updateNotice.setUpdatedAt(LocalDateTime.now());
        
        return noticeMapper.updateById(updateNotice) > 0;
    }
} 