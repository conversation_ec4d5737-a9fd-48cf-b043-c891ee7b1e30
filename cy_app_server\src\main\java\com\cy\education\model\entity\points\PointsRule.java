package com.cy.education.model.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分规则实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("points_rule")
public class PointsRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 规则类别：login(登录)、learning(学习)、forum(论坛)、exam(考试)、other(其他)
     */
    private String category;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 规则标识码
     */
    private String code;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 积分值：正数表示增加，负数表示减少
     */
    private Integer points;

    /**
     * 每日使用上限：-1表示无上限
     */
    private Integer dailyLimit;

    /**
     * 总次数上限：-1表示无上限
     */
    private Integer timesLimit;

    /**
     * 已使用次数
     */
    private Integer usedCount;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
