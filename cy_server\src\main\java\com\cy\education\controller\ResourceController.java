package com.cy.education.controller;

import com.cy.education.model.entity.Resource;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.ResourceService;
import com.cy.education.utils.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/resources")
public class ResourceController {

    @Autowired
    private ResourceService resourceService;

    @GetMapping
    public ApiResponse<PageResponse<Resource>> getResources(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String name) {
        PageResponse<Resource> resourceList = resourceService.getResourceList(page, size, type, name);
        return ApiResponse.success(resourceList);
    }

    @PostMapping
    public ApiResponse<Resource> createResource(@RequestBody Resource resource) {
        // In a real app, you'd get the user ID from the security context
        // Long uploaderId = SecurityUtil.getCurrentUserId();
        // resource.setUploaderId(uploaderId);
        resource.setCreateTime(new Date());
        resource.setUpdateTime(new Date());
        resourceService.save(resource);
        return ApiResponse.success(resource);
    }

    @GetMapping("/{id}")
    public ApiResponse<Resource> getResourceById(@PathVariable Long id) {
        Resource resource = resourceService.getById(id);
        if (resource == null) {
            return ApiResponse.error("Resource not found");
        }
        return ApiResponse.success(resource);
    }

    @PutMapping("/{id}")
    public ApiResponse<Resource> updateResource(@PathVariable Long id, @RequestBody Resource resourceDetails) {
        Resource resource = resourceService.getById(id);
        if (resource == null) {
            return ApiResponse.error("Resource not found");
        }
        resource.setName(resourceDetails.getName());
        resource.setContent(resourceDetails.getContent());
        resource.setTags(resourceDetails.getTags());
        // Type is generally not updatable, but you can add it if needed
        // resource.setType(resourceDetails.getType());
        resource.setUpdateTime(new Date());

        resourceService.updateById(resource);
        return ApiResponse.success(resource);
    }

    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteResource(@PathVariable Long id) {
        boolean removed = resourceService.removeById(id);
        if (!removed) {
            return ApiResponse.error("Resource not found or could not be deleted");
        }
        return ApiResponse.success(null);
    }

    @PostMapping("/batch")
    public ApiResponse<List<Resource>> createResourcesBatch(@RequestBody List<Resource> resources) {
        Date now = new Date();
        for (Resource resource : resources) {
            // In a real app, you'd get the user ID from the security context
            // resource.setUploaderId(SecurityUtil.getCurrentUserId());
            resource.setCreateTime(now);
            resource.setUpdateTime(now);
        }
        resourceService.saveBatch(resources);
        return ApiResponse.success(resources);
    }

    @GetMapping("/tags")
    public ApiResponse<List<String>> getAllTags() {
        List<String> tags = resourceService.getAllTags();
        return ApiResponse.success(tags);
    }
} 