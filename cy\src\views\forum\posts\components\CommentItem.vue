<template>
  <div class="comment-item" :class="{ 'child-comment': isChild }">
    <div class="comment-header">
      <div class="user-info">
        <el-avatar :size="32" :src="comment.authorAvatar" />
        <span class="author-name">{{ comment.author }}</span>
      </div>
      <div class="comment-time">{{ comment.createTime }}</div>
    </div>
    <div class="comment-content">
      {{ comment.content }}
    </div>
    <div v-if="comment.children && comment.children.length > 0" class="comment-replies">
      <CommentItem
        v-for="child in comment.children"
        :key="child.id"
        :comment="child"
        :is-child="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { type Comment } from '@/api/forum'

defineProps<{
  comment: Comment
  isChild?: boolean
}>()
</script>

<style scoped>
.comment-item {
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.child-comment {
  margin-left: 30px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: #f2f6fc;
  border-left: 3px solid #409eff;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  align-items: center;
}

.author-name {
  margin-left: 8px;
  font-weight: 500;
}

.comment-time {
  color: #909399;
  font-size: 12px;
}

.comment-content {
  line-height: 1.6;
  color: #303133;
  word-break: break-word;
}

.comment-replies {
  margin-top: 16px;
}
</style> 