import request from '@/utils/request'

export interface Resource {
  id?: number
  name: string
  type: 'file' | 'video' | 'article'
  content: string
  tags?: string // JSON string
  uploaderId?: number
  createTime?: string
  updateTime?: string
}

export interface ResourceQuery {
  page: number
  size: number
  type?: 'file' | 'video' | 'article'
  name?: string
}

// Get resource list
export function getResourceList(params: ResourceQuery) {
  return request({
    url: '/resources',
    method: 'get',
    params
  })
}

// Create a new resource
export function createResource(data: Resource) {
  return request({
    url: '/resources',
    method: 'post',
    data
  })
}

// Create multiple resources at once
export function createResourcesBatch(data: Resource[]) {
  return request({
    url: '/resources/batch',
    method: 'post',
    data
  })
}

// Get resource details by ID
export function getResourceById(id: number) {
  return request({
    url: `/resources/${id}`,
    method: 'get'
  })
}

// Update a resource
export function updateResource(id: number, data: Resource) {
  return request({
    url: `/resources/${id}`,
    method: 'put',
    data
  })
}

// Delete a resource by ID
export function deleteResource(id: number) {
  return request({
    url: `/resources/${id}`,
    method: 'delete'
  })
}

// Get all tags
export function getAllTags() {
  return request({
    url: '/resources/tags',
    method: 'get'
  })
} 