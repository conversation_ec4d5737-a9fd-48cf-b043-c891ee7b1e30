<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="600px"
    destroy-on-close
    @closed="handleClosed"
  >
    <div class="import-container">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="下载模板" />
        <el-step title="上传文件" />
        <el-step title="导入结果" />
      </el-steps>

      <!-- 步骤1: 下载模板 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="template-info">
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. 请先下载导入模板</p>
              <p>2. 按照模板格式填写数据</p>
              <p>3. 保存为Excel文件后上传</p>
              <p v-if="importRules">4. {{ importRules }}</p>
            </template>
          </el-alert>
        </div>
        
        <div class="template-download">
          <el-button 
            type="primary" 
            :icon="Download" 
            @click="downloadTemplate"
            :loading="downloadLoading"
          >
            下载{{ templateName }}模板
          </el-button>
        </div>
      </div>

      <!-- 步骤2: 上传文件 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :show-file-list="false"
          accept=".xlsx,.xls"
          drag
        >
          <div class="upload-area">
            <el-icon class="upload-icon"><UploadFilled /></el-icon>
            <div class="upload-text">将Excel文件拖到此处，或<em>点击上传</em></div>
            <div class="upload-tip">只支持 .xlsx 和 .xls 格式文件</div>
          </div>
        </el-upload>

        <div v-if="uploadFile" class="upload-file-info">
          <el-icon><Document /></el-icon>
          <span>{{ uploadFile.name }}</span>
          <el-button type="text" @click="removeFile">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>

        <div v-if="uploading" class="upload-progress">
          <el-progress :percentage="uploadProgress" />
          <div class="progress-text">正在导入数据...</div>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="import-result">
          <el-result
            :icon="importResult.success ? 'success' : 'warning'"
            :title="importResult.title"
            :sub-title="importResult.message"
          >
            <template #extra>
              <div class="result-stats">
                <el-statistic
                  title="成功导入"
                  :value="importResult.successCount"
                  suffix="条"
                  class="success-stat"
                />
                <el-statistic
                  title="失败记录"
                  :value="importResult.failureCount"
                  suffix="条"
                  class="failure-stat"
                />
              </div>
            </template>
          </el-result>

          <!-- 失败记录详情 -->
          <div v-if="importResult.failures && importResult.failures.length > 0" class="failure-details">
            <el-collapse>
              <el-collapse-item title="查看失败记录详情" name="failures">
                <el-table :data="importResult.failures" max-height="300">
                  <el-table-column prop="row" label="行号" width="80" />
                  <el-table-column prop="data" label="数据" />
                  <el-table-column prop="reason" label="失败原因" />
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button v-if="currentStep === 0" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-if="currentStep === 1" type="primary" @click="startImport" :disabled="!uploadFile" :loading="uploading">
          开始导入
        </el-button>
        <el-button v-if="currentStep === 2" type="primary" @click="visible = false">完成</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, UploadFilled, Document, Close } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  title: string
  templateName: string
  templateUrl: string
  uploadAction: string
  importRules?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

interface ImportResult {
  success: boolean
  title: string
  message: string
  successCount: number
  failureCount: number
  failures?: Array<{
    row: number
    data: string
    reason: string
  }>
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const currentStep = ref(0)
const downloadLoading = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadFile = ref<File | null>(null)
const uploadRef = ref()

const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}

const importResult = ref<ImportResult>({
  success: false,
  title: '',
  message: '',
  successCount: 0,
  failureCount: 0,
  failures: []
})

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const downloadTemplate = async () => {
  downloadLoading.value = true
  try {
    const response = await fetch(props.templateUrl, {
      headers: uploadHeaders
    })
    
    if (!response.ok) {
      throw new Error('下载失败')
    }
    
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${props.templateName}导入模板.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  } finally {
    downloadLoading.value = false
  }
}

const nextStep = () => {
  currentStep.value++
}

const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }

  uploadFile.value = file
  return false // 阻止自动上传
}

const removeFile = () => {
  uploadFile.value = null
}

const startImport = async () => {
  if (!uploadFile.value) {
    ElMessage.error('请先选择文件')
    return
  }

  uploading.value = true
  uploadProgress.value = 0

  const formData = new FormData()
  formData.append('file', uploadFile.value)

  try {
    const response = await fetch(props.uploadAction, {
      method: 'POST',
      headers: uploadHeaders,
      body: formData
    })

    const result = await response.json()
    
    if (response.ok) {
      importResult.value = {
        success: result.successCount > 0,
        title: result.successCount > 0 ? '导入完成' : '导入失败',
        message: `成功导入 ${result.successCount} 条记录，失败 ${result.failureCount} 条记录`,
        successCount: result.successCount || 0,
        failureCount: result.failureCount || 0,
        failures: result.failures || []
      }
      
      currentStep.value = 2
      
      if (result.successCount > 0) {
        emit('success')
      }
    } else {
      throw new Error(result.message || '导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    uploading.value = false
  }
}

const handleUploadSuccess = () => {
  // 这个方法不会被调用，因为我们阻止了自动上传
}

const handleUploadError = () => {
  ElMessage.error('上传失败')
  uploading.value = false
}

const handleClosed = () => {
  currentStep.value = 0
  uploadFile.value = null
  uploading.value = false
  uploadProgress.value = 0
  importResult.value = {
    success: false,
    title: '',
    message: '',
    successCount: 0,
    failureCount: 0,
    failures: []
  }
}
</script>

<style scoped>
.import-container {
  padding: 20px 0;
}

.step-content {
  margin-top: 30px;
  min-height: 200px;
}

.template-info {
  margin-bottom: 20px;
}

.template-download {
  text-align: center;
  margin-top: 30px;
}

.upload-area {
  text-align: center;
  padding: 40px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

.upload-file-info {
  display: flex;
  align-items: center;
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.upload-file-info span {
  flex: 1;
  margin-left: 8px;
}

.upload-progress {
  margin-top: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #909399;
}

.result-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin: 20px 0;
}

.success-stat :deep(.el-statistic__number) {
  color: #67c23a;
}

.failure-stat :deep(.el-statistic__number) {
  color: #f56c6c;
}

.failure-details {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
