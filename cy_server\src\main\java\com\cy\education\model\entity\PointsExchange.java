package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分兑换记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("points_exchange")
public class PointsExchange implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 兑换记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 学员ID
     */
    private Integer userId;
    
    /**
     * 商品ID
     */
    private Integer productId;
    
    /**
     * 消耗积分
     */
    private Integer points;

    /**
     * 兑换数量
     */
    private Integer quantity;

    /**
     * 总积分（积分 * 数量）
     */
    private Integer totalPoints;

    /**
     * 兑换状态：pending(待审核)、approved(已审核)、shipped(已发货)、
     * delivered(已送达)、completed(已完成)、cancelled(已取消)
     */
    private String status;
    
    /**
     * 收货地址信息（JSON格式）
     */
    private String address;
    
    /**
     * 物流信息（JSON格式）
     */
    private String expressInfo;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 用户名(非数据库字段)
     */
    @TableField(exist = false)
    private String userName;
    
    /**
     * 部门名称(非数据库字段)
     */
    @TableField(exist = false)
    private String departmentName;
    
    /**
     * 商品名称(非数据库字段)
     */
    @TableField(exist = false)
    private String productName;
    
    /**
     * 商品图片(非数据库字段)
     */
    @TableField(exist = false)
    private String goodsImage;
} 