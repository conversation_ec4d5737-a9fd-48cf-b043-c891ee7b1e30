<template>
  <div class="points-records">
    <div class="page-header">
      <h2>积分记录</h2>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" ref="filterFormRef" :inline="true">
        <el-form-item label="学员姓名" prop="userName">
          <el-input v-model="filterForm.userName" placeholder="请输入学员姓名" clearable />
        </el-form-item>
        <el-form-item label="积分类型" prop="type">
          <el-select v-model="filterForm.type" placeholder="请选择积分类型" clearable>
            <el-option label="课程完成" value="course_complete" />
            <el-option label="签到" value="sign_in" />
            <el-option label="兑换" value="exchange" />
            <el-option label="管理员调整" value="admin_adjust" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="timeRange">
          <el-date-picker
            v-model="timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button type="primary" @click="handleManualAdjust">手动加减积分</el-button>
      <el-button type="primary" @click="handleExport">导出记录</el-button>
      <el-button type="primary" @click="handleShowStats">积分统计</el-button>
      <el-button @click="fetchRecordsList">刷新</el-button>
    </div>

    <!-- 积分记录表格 -->
    <el-table
      v-loading="loading"
      :data="recordsList"
      border
      style="width: 100%; margin-top: 16px"
    >
      <el-table-column type="index" width="50" align="center" />
      <el-table-column prop="userName" label="学员姓名" min-width="120" align="center" />
      <el-table-column prop="description" label="操作描述" min-width="180" show-overflow-tooltip />
      <el-table-column prop="type" label="积分类型" min-width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="getTypeTagType(row.type)">
            {{ getTypeLabel(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="points" label="积分变动" min-width="100" align="center">
        <template #default="{ row }">
          <span :class="row.points >= 0 ? 'points-increase' : 'points-decrease'">
            {{ row.points >= 0 ? '+' : '' }}{{ row.points }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="balance" label="积分余额" min-width="100" align="center" />
      <el-table-column prop="createdAt" label="变动时间" min-width="160" align="center" />
      <el-table-column prop="operator" label="操作员" min-width="100" align="center">
        <template #default="{ row }">
          {{ row.operator || '系统' }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 手动调整积分弹窗 -->
    <el-dialog
      v-model="adjustDialogVisible"
      title="手动调整积分"
      width="500px"
      destroy-on-close
    >
      <el-form ref="adjustFormRef" :model="adjustForm" :rules="adjustRules" label-width="100px">
        <el-form-item label="选择学员" prop="userId">
          <el-select
            v-model="adjustForm.userId"
            placeholder="请输入学员姓名搜索"
            filterable
            remote
            :remote-method="searchStudents"
            :loading="studentSearchLoading"
            style="width: 100%"
            @change="handleStudentChange"
          >
            <el-option
              v-for="student in studentOptions"
              :key="student.id"
              :label="`${student.name} - ${student.department}`"
              :value="student.id.toString()"
            >
              <span style="float: left">{{ student.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ student.department }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前积分" v-if="selectedStudent">
          <el-input :value="selectedStudent.points" readonly />
        </el-form-item>
        <el-form-item label="积分变动" prop="points">
          <el-input-number
            v-model="adjustForm.points"
            :min="-1000"
            :max="1000"
            style="width: 100%"
          />
          <div class="form-tip">正数表示增加积分，负数表示减少积分</div>
        </el-form-item>
        <el-form-item label="变动原因" prop="description">
          <el-input
            v-model="adjustForm.description"
            type="textarea"
            rows="3"
            placeholder="请输入变动原因"
          />
        </el-form-item>
        <el-form-item label="操作员" prop="operator">
          <el-input v-model="adjustForm.operator" placeholder="请输入操作员姓名" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="adjustDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAdjust" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 积分统计弹窗 -->
    <el-dialog
      v-model="statsDialogVisible"
      title="积分统计"
      width="800px"
      destroy-on-close
    >
      <div class="stats-container">
        <div class="stats-summary">
          <div class="stats-item">
            <div class="stats-label">总用户数</div>
            <div class="stats-value">{{ pointsStats.totalUsers }}</div>
          </div>
          <div class="stats-item">
            <div class="stats-label">总积分</div>
            <div class="stats-value">{{ pointsStats.totalPoints }}</div>
          </div>
          <div class="stats-item">
            <div class="stats-label">平均积分</div>
            <div class="stats-value">{{ pointsStats.averagePoints }}</div>
          </div>
        </div>

        <div class="chart-container">
          <div ref="pieChartRef" class="pie-chart"></div>
          <div ref="barChartRef" class="bar-chart"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { 
  getPointsRecordList, 
  adjustUserPoints, 
  getPointsStatistics,
  exportPointsRecords,
  type PointsRecord,
  type PointsStatistics,
  type PointsAdjustment,
  type PointsQueryParams
} from '@/api/points'
import {
  getStudentList,
  type Student
} from '@/api/student'

// 表格数据和分页
const loading = ref(false)
const recordsList = ref<PointsRecord[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 筛选表单
const filterFormRef = ref()
const filterForm = reactive<PointsQueryParams>({
  userName: '',
  type: '',
  startDate: '',
  endDate: ''
})

// 计算属性：处理时间范围
const timeRange = computed({
  get: () => {
    if (filterForm.startDate && filterForm.endDate) {
      return [filterForm.startDate, filterForm.endDate]
    }
    return []
  },
  set: (val: string[]) => {
    if (val && val.length === 2) {
      filterForm.startDate = val[0]
      filterForm.endDate = val[1]
    } else {
      filterForm.startDate = ''
      filterForm.endDate = ''
    }
  }
})

// 手动调整积分相关
const adjustDialogVisible = ref(false)
const adjustFormRef = ref()
const submitting = ref(false)
const adjustForm = reactive<PointsAdjustment>({
  userId: '',
  points: 0,
  description: '',
  operator: ''
})

// 学员搜索相关
const studentSearchLoading = ref(false)
const studentOptions = ref<Student[]>([])
const selectedStudent = ref<Student | null>(null)

const adjustRules = {
  userId: [{ required: true, message: '请选择学员', trigger: 'change' }],
  points: [{ required: true, message: '请输入积分变动值', trigger: 'blur' }],
  description: [{ required: true, message: '请输入变动原因', trigger: 'blur' }],
  operator: [{ required: true, message: '请输入操作员姓名', trigger: 'blur' }]
}

// 积分统计相关
const statsDialogVisible = ref(false)
const pieChartRef = ref()
const barChartRef = ref()
let pieChart: any = null
let barChart: any = null

// 积分统计数据
const pointsStats = ref<PointsStatistics>({
  totalUsers: 0,
  totalPoints: 0,
  averagePoints: 0,
  monthlyPointsChange: [],
  topUsers: [],
  pointsDistribution: []
})

// 获取积分记录列表
const fetchRecordsList = async () => {
  loading.value = true
  try {
    const params: PointsQueryParams = {
      page: currentPage.value,
      limit: pageSize.value,
      ...filterForm
    }
    
    const data = await getPointsRecordList(params)
    recordsList.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取积分记录失败:', error)
    ElMessage.error('获取积分记录失败')
  } finally {
    loading.value = false
  }
}

// 积分类型标签
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'course_complete': '课程完成',
    'sign_in': '签到',
    'exchange': '兑换',
    'admin_adjust': '管理员调整',
    'other': '其他'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'course_complete': 'success',
    'sign_in': 'primary',
    'exchange': 'warning',
    'admin_adjust': 'info',
    'other': ''
  }
  return typeMap[type] || ''
}

// 筛选和重置
const handleFilter = () => {
  currentPage.value = 1
  fetchRecordsList()
}

const resetFilter = () => {
  filterFormRef.value?.resetFields()
  Object.keys(filterForm).forEach(key => {
    filterForm[key as keyof PointsQueryParams] = '' as any
  })
  currentPage.value = 1
  fetchRecordsList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  fetchRecordsList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchRecordsList()
}

// 手动调整积分
const handleManualAdjust = () => {
  adjustDialogVisible.value = true
  // 重置表单
  Object.assign(adjustForm, {
    userId: '',
    points: 0,
    description: '',
    operator: ''
  })
  // 重置学员选择相关状态
  selectedStudent.value = null
  studentOptions.value = []
}

const submitAdjust = async () => {
  if (!adjustFormRef.value) return
  
  try {
    await adjustFormRef.value.validate()
    submitting.value = true
    
    await adjustUserPoints(adjustForm)
    
    ElMessage.success('积分调整成功')
    adjustDialogVisible.value = false
    fetchRecordsList()
  } catch (error) {
    console.error('积分调整失败:', error)
    ElMessage.error('积分调整失败')
  } finally {
    submitting.value = false
  }
}

// 导出记录
const handleExport = async () => {
  try {
    const params: PointsQueryParams = {
      ...filterForm
    }
    
    const blob = await exportPointsRecords(params)
    
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `积分记录_${new Date().toLocaleDateString()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 显示积分统计
const handleShowStats = async () => {
  try {
    const data = await getPointsStatistics()
    pointsStats.value = data
    statsDialogVisible.value = true
    
    // 等待弹窗渲染完成后初始化图表
    setTimeout(() => {
      initCharts()
    }, 100)
  } catch (error) {
    console.error('获取积分统计失败:', error)
    ElMessage.error('获取积分统计失败')
  }
}

// 初始化图表
const initCharts = () => {
  if (pieChartRef.value) {
    pieChart = echarts.init(pieChartRef.value)
    const pieOption = {
      title: {
        text: '积分分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '积分分布',
          type: 'pie',
          radius: '50%',
          data: pointsStats.value.pointsDistribution.map(item => ({
            name: item.range,
            value: item.count
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    pieChart.setOption(pieOption)
  }
  
  if (barChartRef.value) {
    barChart = echarts.init(barChartRef.value)
    const barOption = {
      title: {
        text: '积分排行TOP10',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: pointsStats.value.topUsers.map(user => user.userName)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '积分',
          type: 'bar',
          data: pointsStats.value.topUsers.map(user => user.points),
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    }
    barChart.setOption(barOption)
  }
}

// 组件卸载时销毁图表
onUnmounted(() => {
  if (pieChart) {
    pieChart.dispose()
  }
  if (barChart) {
    barChart.dispose()
  }
})

// 页面加载时获取数据
onMounted(() => {
  fetchRecordsList()
})

// 搜索学员
const searchStudents = async (query: string) => {
  if (!query) {
    studentOptions.value = []
    return
  }
  
  studentSearchLoading.value = true
  try {
    const data = await getStudentList({
      keyword: query,
      status: 1,
      page: 1,
      size: 20
    })
    studentOptions.value = data.list
  } catch (error) {
    console.error('搜索学员失败:', error)
    ElMessage.error('搜索学员失败')
  } finally {
    studentSearchLoading.value = false
  }
}

// 选择学员
const handleStudentChange = (studentId: string) => {
  const student = studentOptions.value.find(s => s.id.toString() === studentId)
  selectedStudent.value = student || null
}
</script>

<style scoped>
.points-records {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-weight: 500;
}

.filter-card {
  margin-bottom: 20px;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.points-increase {
  color: #67c23a;
  font-weight: bold;
}

.points-decrease {
  color: #f56c6c;
  font-weight: bold;
}

/* 统计弹窗样式 */
.stats-container {
  padding: 10px;
}

.stats-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stats-item {
  text-align: center;
  padding: 10px 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  flex: 1;
  margin: 0 5px;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.chart-container {
  display: flex;
  margin-top: 20px;
}

.pie-chart, .bar-chart {
  flex: 1;
  height: 300px;
}

.bar-chart {
  width: 100%;
  height: 300px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style> 