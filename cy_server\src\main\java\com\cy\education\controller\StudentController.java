package com.cy.education.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cy.education.model.entity.Student;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.repository.StudentMapper;
import com.cy.education.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * 学员管理控制器
 */
@Api(tags = "学员管理接口")
@RestController
@RequestMapping("/student")
@PreAuthorize("hasAuthority('user:manage')")
@Slf4j
public class StudentController {

    @Autowired
    private StudentService studentService;

    @Autowired
    private StudentMapper studentMapper;

    /**
     * 分页查询学员列表
     */
    @ApiOperation("分页查询学员列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, dataType = "Long", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "size", value = "每页大小", required = true, dataType = "Long", paramType = "query", defaultValue = "10"),
            @ApiImplicitParam(name = "keyword", value = "关键词（用户名、真实姓名、手机号）", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "departmentId", value = "部门ID", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态（0禁用，1启用）", dataType = "Integer", paramType = "query")
    })
    @GetMapping("/list")
    public ApiResponse<PageResponse<Student>> list(
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer departmentId,
            @RequestParam(required = false) Integer status) {
        try {
            PageResponse<Student> pageResponse = studentService.listStudents(page, size, keyword, departmentId, status);
            return ApiResponse.success(pageResponse);
        } catch (Exception e) {
            log.error("查询学员列表失败", e);
            return ApiResponse.error("查询学员列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询学员详情
     */
    @ApiOperation("根据ID查询学员详情")
    @ApiImplicitParam(name = "id", value = "学员ID", required = true, dataType = "Integer", paramType = "path")
    @GetMapping("/{id}")
    public ApiResponse<Student> getById(@PathVariable Integer id) {
        try {
            Student student = studentService.getStudentById(id);
            return ApiResponse.success(student);
        } catch (Exception e) {
            log.error("查询学员详情失败", e);
            return ApiResponse.error("查询学员详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加学员
     */
    @ApiOperation("添加学员")
    @PostMapping("/add")
    public ApiResponse<Map<String, Object>> add(@Validated @RequestBody Student student) {
        try {
            Integer id = studentService.addStudent(student);
            Map<String, Object> result = new HashMap<>();
            result.put("id", id);
            return ApiResponse.success(result, "添加学员成功");
        } catch (Exception e) {
            log.error("添加学员失败", e);
            return ApiResponse.error("添加学员失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新学员信息
     */
    @ApiOperation("更新学员信息")
    @PutMapping("/update")
    public ApiResponse<Map<String, Boolean>> update(@Validated @RequestBody Student student) {
        try {
            boolean success = studentService.updateStudent(student);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新学员成功");
        } catch (Exception e) {
            log.error("更新学员失败", e);
            return ApiResponse.error("更新学员失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除学员
     */
    @ApiOperation("删除学员")
    @DeleteMapping("/{id}")
    public ApiResponse<Map<String, Boolean>> delete(@PathVariable Integer id) {
        try {
            boolean success = studentService.deleteStudent(id);
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "删除学员成功");
        } catch (Exception e) {
            log.error("删除学员失败", e);
            return ApiResponse.error("删除学员失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置学员密码
     */
    @ApiOperation("重置学员密码")
    @PostMapping("/{id}/reset-password")
    public ApiResponse<Map<String, String>> resetPassword(@PathVariable Integer id) {
        try {
            String newPassword = studentService.resetStudentPassword(id);
            Map<String, String> result = new HashMap<>();
            result.put("password", newPassword);
            return ApiResponse.success(result, "重置密码成功");
        } catch (Exception e) {
            log.error("重置密码失败", e);
            return ApiResponse.error("重置密码失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新学员状态
     */
    @ApiOperation("更新学员状态")
    @PutMapping("/{id}/status")
    public ApiResponse<Map<String, Boolean>> updateStatus(
            @PathVariable Integer id,
            @RequestBody Map<String, Object> params) {
        try {
            Object statusObj = params.get("status");
            if (statusObj == null) {
                return ApiResponse.validateFailed("状态不能为空");
            }
            
            // 将status转换为Integer类型
            Integer statusCode;
            if (statusObj instanceof Integer) {
                statusCode = (Integer) statusObj;
            } else if (statusObj instanceof String) {
                try {
                    statusCode = Integer.parseInt((String) statusObj);
                } catch (NumberFormatException e) {
                    return ApiResponse.validateFailed("状态值格式不正确，应为0或1");
                }
            } else {
                return ApiResponse.validateFailed("状态值格式不正确，应为0或1");
            }
            
            // 验证状态值是否合法
            if (statusCode != 0 && statusCode != 1) {
                return ApiResponse.validateFailed("状态值不合法，应为0或1");
            }
            
            boolean success = studentService.updateStudentStatus(id, statusCode);
            
            Map<String, Boolean> result = new HashMap<>();
            result.put("success", success);
            return ApiResponse.success(result, "更新状态成功");
        } catch (Exception e) {
            log.error("更新状态失败", e);
            return ApiResponse.error("更新状态失败: " + e.getMessage());
        }
    }
    
    // ==================== 新版导入导出功能 ====================

    /**
     * 下载学员导入模板
     */
    @ApiOperation("下载学员导入模板")
    @GetMapping("/import/template")
    public ResponseEntity<Resource> downloadImportTemplate() throws IOException {
        Resource resource = studentService.generateImportTemplate();
        String fileName = URLEncoder.encode("学员导入模板.xlsx", StandardCharsets.UTF_8.toString());
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"student_template.xlsx\"; filename*=UTF-8''" + fileName)
                .header(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .header(HttpHeaders.CACHE_CONTROL, "no-cache")
                .body(resource);
    }

    /**
     * 批量导入学员（新版本）
     */
    @ApiOperation("批量导入学员（新版本）")
    @PostMapping("/import/v2")
    public ApiResponse<Map<String, Object>> importStudentsV2(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> result = studentService.importStudentsV2(file);
            return ApiResponse.success(result, "学员导入完成");
        } catch (Exception e) {
            log.error("导入学员失败", e);
            return ApiResponse.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出学员列表（新版本）
     */
    @ApiOperation("导出学员列表（新版本）")
    @PostMapping("/export/v2")
    public void exportStudentsV2(@RequestBody Map<String, Object> params, HttpServletResponse response) throws IOException {
        try {
            log.info("开始导出学员列表，参数: {}", params);
            studentService.exportStudentsV2(params, response);
            log.info("学员列表导出完成");
        } catch (Exception e) {
            log.error("导出学员列表失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("导出失败: " + e.getMessage());
        }
    }

    /**
     * 测试导出（简化版本）
     */
    @ApiOperation("测试导出")
    @GetMapping("/export/test")
    public void testExport(HttpServletResponse response) throws IOException {
        try {
            // 设置响应头
            String fileName = URLEncoder.encode("测试导出.txt", StandardCharsets.UTF_8.toString());
            response.setContentType("text/plain;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + fileName);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Cache-Control", "no-cache");

            // 查询学员数据进行测试
            String content = "这是一个测试导出文件\n";
            content += "创建时间: " + LocalDateTime.now() + "\n";
            content += "测试内容: 学员导出功能测试\n\n";

            // 查询学员总数
            try {
                long totalCount = studentService.getStudentCount();
                content += "数据库中学员总数: " + totalCount + "\n";

                if (totalCount > 0) {
                    // 获取前5个学员作为示例
                    List<Student> students = studentService.getStudentListForTest(5);
                    content += "前5个学员示例:\n";
                    for (int i = 0; i < students.size(); i++) {
                        Student student = students.get(i);
                        content += (i + 1) + ". " + student.getName() + " (" + student.getUsername() + ")\n";
                    }
                } else {
                    content += "数据库中没有学员数据\n";
                }
            } catch (Exception e) {
                content += "查询学员数据时出错: " + e.getMessage() + "\n";
                log.error("查询学员数据失败", e);
            }

            response.getWriter().write(content);
            response.getWriter().flush();

            log.info("测试导出完成");
        } catch (Exception e) {
            log.error("测试导出失败", e);
            throw e;
        }
    }

    /**
     * 创建测试学员数据
     */
    @ApiOperation("创建测试学员数据")
    @PostMapping("/create-test-data")
    public ApiResponse<String> createTestData() {
        try {
            // 创建几个测试学员
            for (int i = 1; i <= 5; i++) {
                Student student = new Student();
                student.setName("测试学员" + i);
                student.setUsername("test" + i);
                student.setPhone("1380013800" + i);
                student.setEmail("test" + i + "@example.com");
                student.setDepartmentId(1);
                student.setStatus(1);
                student.setRemark("测试数据" + i);

                // 检查是否已存在
                LambdaQueryWrapper<Student> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Student::getUsername, student.getUsername());
                if (studentMapper.selectCount(queryWrapper) == 0) {
                    studentService.addStudent(student);
                    log.info("创建测试学员: {}", student.getName());
                }
            }

            return ApiResponse.success("测试数据创建成功");
        } catch (Exception e) {
            log.error("创建测试数据失败", e);
            return ApiResponse.error("创建测试数据失败: " + e.getMessage());
        }
    }

    // ==================== 原有导入导出功能（保持兼容） ====================

    /**
     * 导出学员列表
     */
    @ApiOperation("导出学员列表")
    @GetMapping("/export")
    public ApiResponse<Map<String, String>> export(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer departmentId,
            @RequestParam(required = false) Integer status) {
        try {
            String exportUrl = studentService.exportStudentList(keyword, departmentId, status);
            Map<String, String> result = new HashMap<>();
            result.put("url", exportUrl);
            return ApiResponse.success(result, "导出成功");
        } catch (Exception e) {
            log.error("导出学员列表失败", e);
            return ApiResponse.error("导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量导入学员
     */
    @ApiOperation("批量导入学员")
    @PostMapping("/import")
    public ApiResponse<Map<String, Object>> importStudents(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ApiResponse.validateFailed("请选择要导入的文件");
            }
            
            Map<String, Object> result = studentService.importStudents(file.getInputStream(), file.getOriginalFilename());
            return ApiResponse.success(result, "导入成功");
        } catch (Exception e) {
            log.error("导入学员失败", e);
            return ApiResponse.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 测试导出功能（简化版）
     */
    @ApiOperation("测试导出功能")
    @GetMapping("/test-export-simple")
    public void testExportSimple(HttpServletResponse response) throws IOException {
        try {
            // 查询前3个学员
            LambdaQueryWrapper<Student> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.last("LIMIT 3");
            List<Student> students = studentMapper.selectList(queryWrapper);

            log.info("测试导出，查询到学员数量: {}", students.size());

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("测试学员列表", StandardCharsets.UTF_8.toString());
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 创建表头
            List<List<String>> headers = new ArrayList<>();
            headers.add(Arrays.asList("姓名"));
            headers.add(Arrays.asList("用户名"));
            headers.add(Arrays.asList("手机号"));
            headers.add(Arrays.asList("状态"));

            // 创建数据
            List<List<Object>> data = new ArrayList<>();
            for (Student student : students) {
                List<Object> row = new ArrayList<>();
                row.add(student.getName() != null ? student.getName() : "");
                row.add(student.getUsername() != null ? student.getUsername() : "");
                row.add(student.getPhone() != null ? student.getPhone() : "");
                row.add(student.getStatus() == 1 ? "正常" : "禁用");
                data.add(row);

                log.info("添加学员数据: {}", row);
            }

            log.info("开始写入Excel，数据行数: {}", data.size());

            // 写入Excel
            EasyExcel.write(response.getOutputStream())
                    .head(headers)
                    .sheet("学员列表")
                    .doWrite(data);

            log.info("Excel写入完成");

        } catch (Exception e) {
            log.error("测试导出失败", e);
            throw new IOException("测试导出失败: " + e.getMessage(), e);
        }
    }
}