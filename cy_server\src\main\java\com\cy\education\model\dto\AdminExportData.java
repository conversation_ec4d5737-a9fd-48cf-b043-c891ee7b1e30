package com.cy.education.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 管理员导出数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminExportData {

    @ExcelProperty("姓名")
    private String realName;

    @ExcelProperty("用户名")
    private String username;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("邮箱")
    private String email;

    @ExcelProperty("部门")
    private String departmentName;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("最后登录时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
}
