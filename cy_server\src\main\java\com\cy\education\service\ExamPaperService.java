package com.cy.education.service;

import com.cy.education.model.params.ExamPaperParams;
import com.cy.education.model.params.ExamPaperQuestionParams;
import com.cy.education.model.params.ExamPaperQueryParams;
import com.cy.education.model.vo.ExamPaperVO;
import com.cy.education.model.vo.PageResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 试卷服务接口
 */
public interface ExamPaperService {

    /**
     * 分页查询试卷列表
     *
     * @param params 查询参数
     * @return 分页结果
     */
    PageResponse<ExamPaperVO> listPapers(ExamPaperQueryParams params);

    /**
     * 获取试卷详情
     *
     * @param id 试卷ID
     * @return 试卷详情
     */
    ExamPaperVO getPaperDetail(Integer id);

    /**
     * 创建试卷
     *
     * @param params 试卷参数
     * @param createdBy 创建人ID
     * @return 试卷ID
     */
    Integer createPaper(ExamPaperParams params, String createdBy);

    /**
     * 更新试卷
     *
     * @param id 试卷ID
     * @param params 试卷参数
     * @return 是否成功
     */
    boolean updatePaper(Integer id, ExamPaperParams params);

    /**
     * 删除试卷
     *
     * @param id 试卷ID
     * @return 是否成功
     */
    boolean deletePaper(Integer id);

    /**
     * 发布/取消发布试卷
     *
     * @param id 试卷ID
     * @param isPublished 是否发布
     * @return 是否成功
     */
    boolean publishPaper(Integer id, Boolean isPublished);

    /**
     * 添加题目到试卷
     *
     * @param paperId 试卷ID
     * @param questions 题目参数列表
     * @return 是否成功
     */
    boolean addQuestionsToPaper(Integer paperId, List<ExamPaperQuestionParams> questions);

    /**
     * 更新试卷题目
     *
     * @param paperId 试卷ID
     * @param questions 题目参数列表
     * @return 是否成功
     */
    boolean updatePaperQuestions(Integer paperId, List<ExamPaperQuestionParams> questions);

    /**
     * 从试卷移除题目
     *
     * @param paperId 试卷ID
     * @param questionId 题目ID
     * @return 是否成功
     */
    boolean removeQuestionFromPaper(Integer paperId, Integer questionId);

    // ==================== 导出功能 ====================

    /**
     * 导出试卷
     *
     * @param paperId 试卷ID
     * @param options 导出选项
     * @param response HTTP响应
     */
    void exportPaper(Integer paperId, Map<String, Object> options, HttpServletResponse response) throws IOException;

    /**
     * 批量导出试卷
     *
     * @param params 导出参数
     * @param response HTTP响应
     */
    void batchExportPapers(Map<String, Object> params, HttpServletResponse response) throws IOException;
}