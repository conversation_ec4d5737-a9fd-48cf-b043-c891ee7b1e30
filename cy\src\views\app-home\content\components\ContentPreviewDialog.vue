<template>
  <el-dialog
    v-model="visible"
    :title="getPreviewTitle"
    width="800px"
    destroy-on-close
  >
    <div v-if="contentType === 'carousel'" class="carousel-preview">
      <div class="carousel-image-container">
        <el-image
          :src="previewData.imageUrl"
          fit="contain"
          :preview-src-list="[previewData.imageUrl]"
          style="width: 100%;"
        />
      </div>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="标题">{{ previewData.title }}</el-descriptions-item>
        <el-descriptions-item label="跳转链接">
          <a :href="previewData.link" target="_blank" v-if="previewData.link">{{ previewData.link }}</a>
          <span v-else>无</span>
        </el-descriptions-item>
        <el-descriptions-item label="排序">{{ previewData.sort }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="previewData.status ? 'success' : 'info'">
            {{ previewData.status ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-else-if="contentType === 'news'" class="news-preview">
      <h2 class="news-title">{{ previewData.title }}</h2>
      <div class="news-meta">
        <span>发布时间：{{ previewData.publishTime }}</span>
        <span>阅读量：{{ previewData.viewCount }}</span>
        <span>
          <el-tag :type="previewData.isTop ? 'danger' : 'info'" size="small">
            {{ previewData.isTop ? '置顶' : '普通' }}
          </el-tag>
        </span>
      </div>
      <div v-if="previewData.coverUrl" class="news-cover">
        <el-image
          :src="previewData.coverUrl"
          fit="cover"
          :preview-src-list="[previewData.coverUrl]"
        />
      </div>
      <div class="news-content" v-html="previewData.content"></div>
    </div>

    <div v-else-if="contentType === 'notice'" class="notice-preview">
      <h2 class="notice-title">{{ previewData.title }}</h2>
      <div class="notice-meta">
        <span>发布时间：{{ previewData.publishTime }}</span>
        <span>
          <el-tag :type="getImportanceType(previewData.importance)" effect="dark" size="small">
            {{ getImportanceName(previewData.importance) }}
          </el-tag>
        </span>
        <span>
          <el-tag :type="previewData.isTop ? 'danger' : 'info'" size="small">
            {{ previewData.isTop ? '置顶' : '普通' }}
          </el-tag>
        </span>
      </div>
      <div class="notice-content" v-html="previewData.content"></div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  previewData: {
    type: Object,
    default: () => ({})
  },
  contentType: {
    type: String,
    validator: (value: string) => ['carousel', 'news', 'notice'].includes(value),
    required: true
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(props.modelValue)

// 监听属性变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取预览标题
const getPreviewTitle = computed(() => {
  const typeMap: Record<string, string> = {
    'carousel': '轮播图预览',
    'news': '新闻预览',
    'notice': '公告预览'
  }
  return typeMap[props.contentType] || '内容预览'
})

// 获取重要程度对应的名称
const getImportanceName = (importance: number) => {
  const map: Record<number, string> = {
    1: '普通',
    2: '重要',
    3: '紧急'
  }
  return map[importance] || '未知'
}

// 获取重要程度对应的标签类型
const getImportanceType = (importance: number) => {
  const map: Record<number, string> = {
    1: 'info',
    2: 'warning',
    3: 'danger'
  }
  return map[importance] || 'info'
}
</script>

<style scoped>
.carousel-image-container {
  margin-bottom: 20px;
  text-align: center;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
}

.news-title,
.notice-title {
  margin-top: 0;
  margin-bottom: 16px;
  text-align: center;
}

.news-meta,
.notice-meta {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
  color: #606266;
  font-size: 14px;
}

.news-cover {
  margin-bottom: 20px;
  text-align: center;
}

.news-cover .el-image {
  max-width: 100%;
  max-height: 300px;
}

.news-content,
.notice-content {
  line-height: 1.8;
  margin-bottom: 20px;
  padding: 10px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

:deep(.news-content img),
:deep(.notice-content img) {
  max-width: 100%;
  height: auto;
}

:deep(.news-content table),
:deep(.notice-content table) {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
}

:deep(.news-content table td),
:deep(.notice-content table td),
:deep(.news-content table th),
:deep(.notice-content table th) {
  border: 1px solid #ddd;
  padding: 8px;
}
</style> 