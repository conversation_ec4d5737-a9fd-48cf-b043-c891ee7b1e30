﻿// 文件内容将通过编辑器填充

import { defineStore } from 'pinia'
import { RouteRecordRaw } from 'vue-router'
import { useUserStore } from './user'

// 定义路由类型
export interface AppRouteRecordRaw extends Omit<RouteRecordRaw, 'children'> {
  hidden?: boolean
  children?: AppRouteRecordRaw[]
  meta?: {
    title?: string
    icon?: string
    roles?: string[]
    permissions?: string[]
    keepAlive?: boolean
    breadcrumb?: boolean
  }
}

// 定义权限状态
interface PermissionState {
  routes: AppRouteRecordRaw[]
  addRoutes: AppRouteRecordRaw[]
  menus: any[] // 菜单数据结构
}

/**
 * 根据路由的meta.roles和用户角色过滤路由
 * @param routes 路由配置
 * @param roles 用户角色
 */
function filterAsyncRoutes(routes: AppRouteRecordRaw[], roles: string[]): AppRouteRecordRaw[] {
  const res: AppRouteRecordRaw[] = []
  
  routes.forEach(route => {
    const tmp = { ...route }
    
    // 检查用户是否有权限访问该路由
    if (hasPermission(roles, tmp)) {
      // 如果有子路由，递归过滤
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })
  
  return res
}

/**
 * 判断用户是否有权限访问路由
 * @param roles 用户角色
 * @param route 路由
 */
function hasPermission(roles: string[], route: AppRouteRecordRaw): boolean {
  if (route.meta && route.meta.roles) {
    // 如果路由定义了roles，则用户必须拥有其中的一个角色才能访问
    return roles.some(role => route.meta?.roles?.includes(role))
  } else {
    // 如果路由没有定义roles，则所有用户都可以访问
    return true
  }
}

/**
 * 将路由转换为菜单
 * @param routes 路由配置
 */
function routesToMenus(routes: AppRouteRecordRaw[]): any[] {
  const menus: any[] = []
  
  routes.forEach(route => {
    // 跳过隐藏路由
    if (route.hidden) {
      return
    }
    
    const menu: any = {
      path: route.path,
      name: route.name,
      meta: route.meta || {},
      component: null // 菜单不需要组件信息
    }
    
    // 处理子菜单
    if (route.children && route.children.length > 0) {
      // 如果只有一个子路由且不是隐藏的，则合并到父级
      if (
        route.children.length === 1 &&
        !route.children[0].hidden &&
        !route.children[0].children
      ) {
        menu.path = route.path + '/' + route.children[0].path
        menu.meta = { ...menu.meta, ...route.children[0].meta }
      } else {
        // 否则作为子菜单
        menu.children = routesToMenus(route.children)
      }
    }
    
    menus.push(menu)
  })
  
  return menus
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routes: [], // 所有路由
    addRoutes: [], // 动态添加的路由
    menus: [] // 菜单数据
  }),
  
  getters: {
    // 获取所有路由
    getRoutes: (state) => state.routes,
    // 获取菜单
    getMenus: (state) => state.menus
  },
  
  actions: {
    /**
     * 根据用户角色生成可访问的路由
     * @param asyncRoutes 异步路由配置
     */
    generateRoutes(asyncRoutes: AppRouteRecordRaw[]) {
      const userStore = useUserStore()
      const { roles } = userStore
      
      let accessedRoutes
      
      // 如果用户是管理员，直接返回所有异步路由
      if (roles.includes('admin')) {
        accessedRoutes = asyncRoutes
      } else {
        // 否则根据角色过滤路由
        accessedRoutes = filterAsyncRoutes(asyncRoutes, roles)
      }
      
      this.addRoutes = accessedRoutes
      this.routes = accessedRoutes
      this.menus = routesToMenus(accessedRoutes)
      
      return accessedRoutes
    },
    
    /**
     * 重置权限路由
     */
    resetRoutes() {
      this.addRoutes = []
      this.routes = []
      this.menus = []
    }
  }
})
