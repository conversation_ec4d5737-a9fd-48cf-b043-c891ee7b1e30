/**
 * 生成随机ID
 * @param length ID长度
 * @returns 随机ID字符串
 */
export function generateId(length: number = 8): string {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成随机日期
 * @param start 开始日期
 * @param end 结束日期
 * @returns 随机日期
 */
export function randomDate(start: Date = new Date(2022, 0, 1), end: Date = new Date()): string {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  return date.toISOString()
}

/**
 * 从数组中随机选择一个元素
 * @param array 数组
 * @returns 随机元素
 */
export function randomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]
}

/**
 * 从数组中随机选择多个元素
 * @param array 数组
 * @param count 选择数量
 * @returns 随机元素数组
 */
export function randomItems<T>(array: T[], count: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

/**
 * 生成随机整数
 * @param min 最小值
 * @param max 最大值
 * @returns 随机整数
 */
export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 生成随机浮点数
 * @param min 最小值
 * @param max 最大值
 * @param decimals 小数位数
 * @returns 随机浮点数
 */
export function randomFloat(min: number, max: number, decimals: number = 2): number {
  const value = Math.random() * (max - min) + min
  return Number(value.toFixed(decimals))
}

/**
 * 返回模拟响应数据
 * @param data 响应数据
 * @param code 响应码
 * @param message 响应消息
 * @returns 响应对象
 */
export function mockResponse<T>(data: T, code: number = 200, message: string = 'success'): {
  code: number
  message: string
  data: T
} {
  return {
    code,
    message,
    data
  }
}

/**
 * 延迟执行
 * @param ms 延迟毫秒数
 * @returns Promise
 */
export function delay(ms: number = 100): Promise<void> {
  // 在开发环境中，使用更短的延迟时间，以加快响应速度
  const actualDelay = import.meta.env.DEV ? Math.min(ms, 100) : ms
  console.log(`模拟延迟: ${actualDelay}ms`)
  return new Promise(resolve => setTimeout(resolve, actualDelay))
}

/**
 * 分页数据
 * @param list 数据列表
 * @param page 页码
 * @param limit 每页条数
 * @returns 分页结果
 */
export function paginateData<T>(list: T[], page: number = 1, limit: number = 10): {
  list: T[]
  total: number
} {
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit
  const paginatedList = list.slice(startIndex, endIndex)

  return {
    list: paginatedList,
    total: list.length
  }
}

/**
 * 获取查询参数
 * @param url URL字符串
 * @returns 查询参数对象
 */
export function getQueryParams(url: string): Record<string, string> {
  const params: Record<string, string> = {}
  const queryString = url.split('?')[1]
  
  if (!queryString) return params
  
  const pairs = queryString.split('&')
  pairs.forEach(pair => {
    const [key, value] = pair.split('=')
    if (key && value) {
      params[key] = decodeURIComponent(value)
    }
  })
  
  return params
} 