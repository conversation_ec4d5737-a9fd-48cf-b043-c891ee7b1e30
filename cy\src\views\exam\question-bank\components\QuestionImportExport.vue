<template>
  <div>
    <!-- 导入对话框 -->
    <el-dialog v-model="importVisible" title="批量导入题目" width="700px" destroy-on-close>
      <div class="import-steps">
        <div class="step">
          <div class="step-title">第一步：选择题库</div>
          <div class="step-content">
            <el-select v-model="selectedBankId" placeholder="请选择题库" style="width: 300px;">
              <el-option
                v-for="bank in bankList"
                :key="bank.id"
                :label="bank.name"
                :value="bank.id"
              />
            </el-select>
          </div>
        </div>
        
        <div class="step">
          <div class="step-title">第二步：下载模板</div>
          <div class="step-content">
            <el-button type="primary" :icon="Download" @click="downloadTemplate">
              下载Excel模板
            </el-button>
            <div class="step-tip">请按照模板格式填写题目信息，支持单选题、多选题、判断题、填空题、简答题</div>
          </div>
        </div>
        
        <div class="step">
          <div class="step-title">第三步：上传文件</div>
          <div class="step-content">
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              drag
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              accept=".xlsx,.xls"
              :limit="1"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">
                将Excel文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件，且不超过10MB
                </div>
              </template>
            </el-upload>
          </div>
        </div>
        
        <div v-if="previewData.length > 0" class="step">
          <div class="step-title">第四步：数据预览</div>
          <div class="step-content">
            <div class="preview-info">
              <span>共 {{ previewData.length }} 道题目</span>
              <span v-if="validationErrors.length > 0" class="error-count">
                {{ validationErrors.length }} 个错误
              </span>
            </div>
            
            <el-table :data="previewData.slice(0, 3)" border size="small" max-height="300">
              <el-table-column prop="type" label="题型" width="80">
                <template #default="{ row }">
                  <el-tag :type="getQuestionTypeTag(row.type)">
                    {{ getQuestionTypeText(row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="content" label="题目内容" min-width="200" show-overflow-tooltip />
              <el-table-column prop="difficulty" label="难度" width="80">
                <template #default="{ row }">
                  <el-rate v-model="row.difficulty" disabled size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="score" label="分值" width="60" />
            </el-table>
            
            <div v-if="previewData.length > 3" class="more-tip">
              还有 {{ previewData.length - 3 }} 道题目...
            </div>
            
            <div v-if="validationErrors.length > 0" class="validation-errors">
              <div class="error-title">数据验证错误：</div>
              <ul>
                <li v-for="error in validationErrors.slice(0, 10)" :key="error" class="error-item">
                  {{ error }}
                </li>
              </ul>
              <div v-if="validationErrors.length > 10" class="more-errors">
                还有 {{ validationErrors.length - 10 }} 个错误...
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="importVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="submitImport" 
          :loading="importing"
          :disabled="!selectedBankId || previewData.length === 0 || validationErrors.length > 0"
        >
          确认导入
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 导出对话框 -->
    <el-dialog v-model="exportVisible" title="导出题目" width="500px">
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="选择题库">
          <el-select v-model="exportForm.bankId" placeholder="请选择题库" style="width: 100%;">
            <el-option label="全部题库" :value="0" />
            <el-option
              v-for="bank in bankList"
              :key="bank.id"
              :label="bank.name"
              :value="bank.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="题目类型">
          <el-checkbox-group v-model="exportForm.types">
            <el-checkbox label="single">单选题</el-checkbox>
            <el-checkbox label="multiple">多选题</el-checkbox>
            <el-checkbox label="judge">判断题</el-checkbox>
            <el-checkbox label="fill">填空题</el-checkbox>
            <el-checkbox label="essay">简答题</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="难度等级">
          <el-checkbox-group v-model="exportForm.difficulties">
            <el-checkbox :label="1">简单</el-checkbox>
            <el-checkbox :label="2">一般</el-checkbox>
            <el-checkbox :label="3">困难</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="导出选项">
          <el-checkbox-group v-model="exportForm.options">
            <el-checkbox label="includeAnswer">包含答案</el-checkbox>
            <el-checkbox label="includeAnalysis">包含解析</el-checkbox>
            <el-checkbox label="includeScore">包含分值</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="exportVisible = false">取消</el-button>
        <el-button type="primary" @click="submitExport" :loading="exporting">
          确认导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import { ExcelUtils } from '@/utils/excel'

interface Props {
  bankList: any[]
}

interface Emits {
  (e: 'import-success'): void
  (e: 'export-success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 导入相关
const importVisible = ref(false)
const importing = ref(false)
const selectedBankId = ref<number>()
const uploadRef = ref()
const previewData = ref<any[]>([])
const validationErrors = ref<string[]>([])

// 导出相关
const exportVisible = ref(false)
const exporting = ref(false)
const exportForm = reactive({
  bankId: 0,
  types: ['single', 'multiple', 'judge', 'fill', 'essay'],
  difficulties: [1, 2, 3],
  options: ['includeAnswer', 'includeAnalysis', 'includeScore']
})

// 题目模板配置
const questionHeaders = [
  { key: 'type', title: '题型', width: 10, required: true },
  { key: 'content', title: '题目内容', width: 50, required: true },
  { key: 'optionA', title: '选项A', width: 20 },
  { key: 'optionB', title: '选项B', width: 20 },
  { key: 'optionC', title: '选项C', width: 20 },
  { key: 'optionD', title: '选项D', width: 20 },
  { key: 'answer', title: '正确答案', width: 15, required: true },
  { key: 'analysis', title: '答案解析', width: 30 },
  { key: 'difficulty', title: '难度等级', width: 10, required: true },
  { key: 'score', title: '分值', width: 10, required: true }
]

// 示例数据
const sampleData = [
  {
    type: '单选题',
    content: '以下哪个是JavaScript的数据类型？',
    optionA: 'String',
    optionB: 'Integer',
    optionC: 'Float',
    optionD: 'Character',
    answer: 'A',
    analysis: 'JavaScript的基本数据类型包括String、Number、Boolean等',
    difficulty: 2,
    score: 2
  },
  {
    type: '多选题',
    content: '以下哪些是前端框架？',
    optionA: 'Vue.js',
    optionB: 'React',
    optionC: 'Angular',
    optionD: 'Spring',
    answer: 'ABC',
    analysis: 'Vue.js、React、Angular都是前端框架，Spring是后端框架',
    difficulty: 1,
    score: 3
  },
  {
    type: '判断题',
    content: 'HTML是一种编程语言',
    optionA: '',
    optionB: '',
    optionC: '',
    optionD: '',
    answer: '错误',
    analysis: 'HTML是标记语言，不是编程语言',
    difficulty: 1,
    score: 1
  }
]

// 题型映射
const questionTypeMap = {
  '单选题': 'single',
  '多选题': 'multiple',
  '判断题': 'judge',
  '填空题': 'fill',
  '简答题': 'essay'
}

// 获取题型标签类型
const getQuestionTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    '单选题': 'primary',
    '多选题': 'success',
    '判断题': 'warning',
    '填空题': 'info',
    '简答题': 'danger'
  }
  return typeMap[type] || 'default'
}

// 获取题型文本
const getQuestionTypeText = (type: string) => {
  return type
}

// 打开导入对话框
const openImport = () => {
  importVisible.value = true
  resetImportData()
}

// 打开导出对话框
const openExport = () => {
  exportVisible.value = true
}

// 重置导入数据
const resetImportData = () => {
  selectedBankId.value = undefined
  previewData.value = []
  validationErrors.value = []
  uploadRef.value?.clearFiles()
}

// 下载模板
const downloadTemplate = () => {
  ExcelUtils.createTemplate(
    questionHeaders,
    sampleData,
    '题目导入模板.xlsx',
    '题目模板'
  )
}

// 文件变化处理
const handleFileChange = async (file: any) => {
  try {
    const rawData = await ExcelUtils.readExcelFile(file.raw)
    
    // 验证数据
    const validation = ExcelUtils.validateExcelData(rawData, [
      ...questionHeaders,
      {
        key: 'type',
        title: '题型',
        required: true,
        validator: (value: string) => Object.keys(questionTypeMap).includes(value)
      },
      {
        key: 'difficulty',
        title: '难度等级',
        required: true,
        validator: (value: number) => [1, 2, 3].includes(Number(value))
      },
      {
        key: 'score',
        title: '分值',
        required: true,
        validator: (value: number) => Number(value) > 0
      }
    ])
    
    previewData.value = validation.validData
    validationErrors.value = validation.errors
    
    if (validation.valid) {
      ElMessage.success(`数据验证通过，共 ${validation.validData.length} 道题目`)
    } else {
      ElMessage.warning(`数据验证失败，发现 ${validation.errors.length} 个错误`)
    }
  } catch (error) {
    console.error('文件解析失败:', error)
    ElMessage.error('文件解析失败，请检查文件格式')
  }
}

// 文件移除处理
const handleFileRemove = () => {
  resetImportData()
}

// 提交导入
const submitImport = async () => {
  if (!selectedBankId.value) {
    ElMessage.warning('请先选择题库')
    return
  }
  
  if (previewData.value.length === 0) {
    ElMessage.warning('请先上传文件')
    return
  }
  
  if (validationErrors.value.length > 0) {
    ElMessage.error('请先修复数据错误')
    return
  }
  
  importing.value = true
  try {
    // 这里调用实际的导入API
    // await importQuestions(selectedBankId.value, previewData.value)
    
    // 模拟导入
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`成功导入 ${previewData.value.length} 道题目`)
    importVisible.value = false
    emit('import-success')
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

// 提交导出
const submitExport = async () => {
  if (exportForm.types.length === 0) {
    ElMessage.warning('请至少选择一种题目类型')
    return
  }
  
  exporting.value = true
  try {
    // 这里调用实际的导出API获取数据
    // const data = await exportQuestions(exportForm)
    
    // 模拟数据
    const mockData = sampleData.filter(item => 
      exportForm.types.includes(questionTypeMap[item.type as keyof typeof questionTypeMap])
    )
    
    // 根据选项过滤字段
    let headers = [...questionHeaders]
    if (!exportForm.options.includes('includeAnswer')) {
      headers = headers.filter(h => h.key !== 'answer')
    }
    if (!exportForm.options.includes('includeAnalysis')) {
      headers = headers.filter(h => h.key !== 'analysis')
    }
    if (!exportForm.options.includes('includeScore')) {
      headers = headers.filter(h => h.key !== 'score')
    }
    
    // 导出
    const filename = `题库导出_${new Date().toISOString().slice(0, 10)}.xlsx`
    ExcelUtils.exportToExcel(mockData, headers, filename, '题目列表')
    
    exportVisible.value = false
    emit('export-success')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 暴露方法
defineExpose({
  openImport,
  openExport
})
</script>

<style scoped>
.import-steps {
  margin: 20px 0;
}

.step {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.step:last-child {
  border-bottom: none;
}

.step-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #303133;
}

.step-content {
  margin-left: 16px;
}

.step-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.preview-info {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-count {
  color: #f56c6c;
  font-weight: 600;
}

.more-tip {
  margin-top: 8px;
  text-align: center;
  color: #909399;
  font-size: 12px;
}

.validation-errors {
  margin-top: 16px;
  padding: 12px;
  background-color: #fef0f0;
  border-radius: 4px;
  border: 1px solid #fbc4c4;
}

.error-title {
  font-weight: 600;
  color: #f56c6c;
  margin-bottom: 8px;
}

.error-item {
  color: #f56c6c;
  font-size: 12px;
  margin-bottom: 4px;
}

.more-errors {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 8px;
}
</style>
