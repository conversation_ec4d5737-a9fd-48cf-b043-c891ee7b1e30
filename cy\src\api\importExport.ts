import { get, post } from '@/utils/request'

/**
 * 导入结果接口
 */
export interface ImportResult {
  successCount: number
  failureCount: number
  failures?: Array<{
    row: number
    data: string
    reason: string
  }>
  message?: string
}

/**
 * 导出参数接口
 */
export interface ExportParams {
  format: 'xlsx' | 'csv' | 'pdf'
  range?: 'all' | 'current' | 'selected'
  fields?: string[]
  options?: string[]
  startTime?: string
  endTime?: string
  [key: string]: any
}

// ==================== 管理员导入导出 ====================

/**
 * 下载管理员导入模板
 */
export function downloadAdminTemplate() {
  return get('/api/admin/import/template', {}, { responseType: 'blob' })
}

/**
 * 导入管理员
 */
export function importAdmins(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return post<ImportResult>('/admin/import', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 * 导出管理员
 */
export function exportAdmins(params: ExportParams) {
  return post('/admin/export', params, { responseType: 'blob' })
}

// 注意：学员导入导出功能已在 student.ts 中实现

// ==================== 题库导入导出 ====================

/**
 * 下载题目导入模板
 */
export function downloadQuestionTemplate() {
  return get('/exam/question/import/template', {}, { responseType: 'blob' })
}

/**
 * 导入题目
 */
export function importQuestions(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return post<ImportResult>('/exam/question/import', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

/**
 * 导出题目
 */
export function exportQuestions(params: ExportParams) {
  return post('/exam/question/export', params, { responseType: 'blob' })
}

// ==================== 试卷导出 ====================

/**
 * 试卷导出选项
 */
export interface ExamPaperExportOptions {
  includeAnswers: boolean // 是否包含答案
  includeExplanations: boolean // 是否包含解析
  answerPosition: 'inline' | 'appendix' // 答案位置：题目内 | 附录
  format: 'docx' | 'pdf'
}

/**
 * 导出试卷
 */
export function exportExamPaper(examId: number, options: ExamPaperExportOptions) {
  return post(`/exam/paper/${examId}/export`, options, { responseType: 'blob' })
}

/**
 * 批量导出试卷
 */
export function batchExportExamPapers(examIds: number[], options: ExamPaperExportOptions) {
  return post('/exam/paper/batch-export', { examIds, ...options }, { responseType: 'blob' })
}

// ==================== 考试记录导出 ====================

/**
 * 导出考试记录
 */
export function exportExamRecords(params: ExportParams & {
  examId?: number
  studentId?: number
  status?: string
}) {
  return post('/exam-record/export', params, { responseType: 'blob' })
}

/**
 * 导出考试统计报告
 */
export function exportExamStatistics(examId: number, params: ExportParams) {
  return post(`/api/exam/${examId}/statistics/export`, params, { responseType: 'blob' })
}

// ==================== 学习记录导出 ====================

/**
 * 导出学习记录
 */
export function exportLearningRecords(params: ExportParams & {
  courseId?: number
  studentId?: number
  status?: string
}) {
  return post('/api/learning-record/export', params, { responseType: 'blob' })
}

/**
 * 导出学习统计报告
 */
export function exportLearningStatistics(params: ExportParams & {
  courseId?: number
  departmentId?: number
}) {
  return post('/api/learning-statistics/export', params, { responseType: 'blob' })
}
