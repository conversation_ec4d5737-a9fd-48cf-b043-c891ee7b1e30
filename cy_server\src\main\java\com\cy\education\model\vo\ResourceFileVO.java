package com.cy.education.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件资源VO
 */
@Data
public class ResourceFileVO {
    
    /**
     * 文件ID
     */
    private Integer id;
    
    /**
     * 文件名称
     */
    private String name;
    
    /**
     * 文件大小(字节)
     */
    private Long size;
    
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * 下载次数
     */
    private Integer downloadCount;
    
    /**
     * 文件URL
     */
    private String url;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件扩展名
     */
    private String extension;
    
    /**
     * 文件描述
     */
    private String description;
    
    /**
     * 上传用户ID
     */
    private Integer uploadUserId;
    
    /**
     * 上传用户名称
     */
    private String uploadUserName;
    
    /**
     * 标签列表
     */
    private List<String> tags;
} 