package com.cy.education.controller;

import com.cy.education.model.vo.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 首页控制器
 */
@Api(tags = "系统接口")
@RestController
public class IndexController {

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 系统首页
     */
    @ApiOperation("系统首页")
    @GetMapping("/")
    public ApiResponse<Map<String, Object>> index() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", applicationName);
        data.put("version", "1.0.0");
        data.put("status", "running");
        data.put("timestamp", System.currentTimeMillis());
        return ApiResponse.success(data, "系统运行正常");
    }

    /**
     * 健康检查
     */
    @ApiOperation("健康检查")
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", System.currentTimeMillis());
        return ApiResponse.success(data);
    }
} 