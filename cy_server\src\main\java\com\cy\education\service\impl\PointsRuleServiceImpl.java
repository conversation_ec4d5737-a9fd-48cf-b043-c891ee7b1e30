package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.PointsRule;
import com.cy.education.model.params.PointsRuleQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.repository.PointsRuleMapper;
import com.cy.education.service.PointsRuleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 积分规则服务实现类
 */
@Service
public class PointsRuleServiceImpl extends ServiceImpl<PointsRuleMapper, PointsRule> implements PointsRuleService {

    @Override
    public IPage<PointsRule> page(PointsRuleQueryParam param) {
        // 创建分页对象
        Page<PointsRule> page = new Page<>(param.getPage(), param.getLimit());

        // 构建查询条件
        LambdaQueryWrapper<PointsRule> wrapper = new LambdaQueryWrapper<>();

        // 分类条件
        if (StringUtils.isNotBlank(param.getCategory())) {
            wrapper.eq(PointsRule::getCategory, param.getCategory());
        }

        // 状态条件
        if (param.getStatus() != null) {
            wrapper.eq(PointsRule::getStatus, param.getStatus());
        }

        // 关键词搜索（名称或描述）
        if (StringUtils.isNotBlank(param.getKeyword())) {
            wrapper.and(w -> w.like(PointsRule::getName, param.getKeyword())
                    .or()
                    .like(PointsRule::getDescription, param.getKeyword()));
        }

        // 排序
        if (StringUtils.isNotBlank(param.getSortBy())) {
            boolean isAsc = "asc".equalsIgnoreCase(param.getSortOrder());
            switch (param.getSortBy()) {
                case "createTime":
                    wrapper.orderBy(true, isAsc, PointsRule::getCreateTime);
                    break;
                case "points":
                    wrapper.orderBy(true, isAsc, PointsRule::getPoints);
                    break;
                case "usedCount":
                    wrapper.orderBy(true, isAsc, PointsRule::getUsedCount);
                    break;
                default:
                    wrapper.orderBy(true, false, PointsRule::getCreateTime);
                    break;
            }
        } else {
            // 默认按创建时间降序
            wrapper.orderByDesc(PointsRule::getCreateTime);
        }

        // 执行查询
        return page(page, wrapper);
    }

    @Override
    public PointsRule getById(Integer id) {
        return super.getById(id);
    }

    @Override
    public PointsRule getByCode(String code) {
        return baseMapper.findByCode(code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> create(PointsRule rule) {
        // 检查规则编码是否已存在
        LambdaQueryWrapper<PointsRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointsRule::getCode, rule.getCode());
        if (count(wrapper) > 0) {
            return ApiResponse.error("规则编码已存在");
        }

        // 设置初始值
        rule.setId(null);
        rule.setUsedCount(0);
        rule.setCreateTime(LocalDateTime.now());

        // 保存规则
        if (save(rule)) {
            return ApiResponse.success(Map.of("id", rule.getId(), "success", true));
        }
        return ApiResponse.error("创建规则失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> update(Integer id, PointsRule rule) {
        // 检查规则是否存在
        PointsRule existingRule = getById(id);
        if (existingRule == null) {
            return ApiResponse.error("规则不存在");
        }

        // 检查规则编码是否已被其他规则使用
        if (!existingRule.getCode().equals(rule.getCode())) {
            LambdaQueryWrapper<PointsRule> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PointsRule::getCode, rule.getCode());
            if (count(wrapper) > 0) {
                return ApiResponse.error("规则编码已存在");
            }
        }

        // 设置不可修改的字段
        rule.setId(id);
        rule.setUsedCount(existingRule.getUsedCount());
        rule.setCreateTime(existingRule.getCreateTime());
        rule.setUpdateTime(LocalDateTime.now());

        // 更新规则
        if (updateById(rule)) {
            return ApiResponse.success(Map.of("success", true));
        }
        return ApiResponse.error("更新规则失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> delete(Integer id) {
        // 检查规则是否存在
        if (getById(id) == null) {
            return ApiResponse.error("规则不存在");
        }

        // 删除规则
        if (removeById(id)) {
            return ApiResponse.success(Map.of("success", true));
        }
        return ApiResponse.error("删除规则失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> batchDelete(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return ApiResponse.error("未选择要删除的规则");
        }

        // 删除规则
        if (removeByIds(ids)) {
            return ApiResponse.success(Map.of("success", true, "failedCount", 0));
        }
        return ApiResponse.error("批量删除规则失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> toggleStatus(Integer id, Integer status) {
        // 检查规则是否存在
        PointsRule rule = getById(id);
        if (rule == null) {
            return ApiResponse.error("规则不存在");
        }

        // 检查状态值
        if (status != 0 && status != 1) {
            return ApiResponse.error("状态值无效");
        }

        // 如果状态已经相同，无需更新
        if (rule.getStatus().equals(status)) {
            return ApiResponse.success(Map.of("success", true));
        }

        // 更新状态
        rule.setStatus(status);
        rule.setUpdateTime(LocalDateTime.now());

        if (updateById(rule)) {
            return ApiResponse.success(Map.of("success", true));
        }
        return ApiResponse.error("更新状态失败");
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> result = new HashMap<>();

        // 获取总规则数
        long totalRules = count();
        result.put("totalRules", totalRules);

        // 获取启用的规则数
        LambdaQueryWrapper<PointsRule> enabledWrapper = new LambdaQueryWrapper<>();
        enabledWrapper.eq(PointsRule::getStatus, 1);
        long enabledRules = count(enabledWrapper);
        result.put("enabledRules", enabledRules);

        // 获取总使用次数
        QueryWrapper<PointsRule> usedWrapper = new QueryWrapper<>();
        usedWrapper.select("SUM(used_count) as totalUsed");
        Map<String, Object> usedMap = getMap(usedWrapper);
        int totalUsed = usedMap != null && usedMap.get("totalUsed") != null ?
                          Integer.parseInt(usedMap.get("totalUsed").toString()) : 0;
        result.put("totalUsed", totalUsed);

        // 获取正积分规则总和
        QueryWrapper<PointsRule> positiveWrapper = new QueryWrapper<>();
        positiveWrapper.select("SUM(points) as positivePoints")
                     .gt("points", 0)
                     .eq("status", 1);
        Map<String, Object> positiveMap = getMap(positiveWrapper);
        int positivePoints = positiveMap != null && positiveMap.get("positivePoints") != null ?
                               Integer.parseInt(positiveMap.get("positivePoints").toString()) : 0;
        result.put("positivePoints", positivePoints);

        // 获取负积分规则总和
        QueryWrapper<PointsRule> negativeWrapper = new QueryWrapper<>();
        negativeWrapper.select("SUM(points) as negativePoints")
                     .lt("points", 0)
                     .eq("status", 1);
        Map<String, Object> negativeMap = getMap(negativeWrapper);
        int negativePoints = negativeMap != null && negativeMap.get("negativePoints") != null ?
                               Integer.parseInt(negativeMap.get("negativePoints").toString()) : 0;
        result.put("negativePoints", negativePoints);

        // 获取分类分布
        QueryWrapper<PointsRule> categoryWrapper = new QueryWrapper<>();
        categoryWrapper.select("category, COUNT(*) as count")
                     .groupBy("category");
        List<Map<String, Object>> categoryDistribution = listMaps(categoryWrapper);
        result.put("categoryDistribution", categoryDistribution);

        // 获取使用排行
        QueryWrapper<PointsRule> rankingWrapper = new QueryWrapper<>();
        rankingWrapper.orderByDesc("used_count")
                    .last("LIMIT 10");
        List<PointsRule> usageRanking = list(rankingWrapper);

        List<Map<String, Object>> rankingList = new ArrayList<>();
        for (int i = 0; i < usageRanking.size(); i++) {
            PointsRule r = usageRanking.get(i);
            Map<String, Object> item = new HashMap<>();
            item.put("id", r.getId());
            item.put("name", r.getName());
            item.put("category", r.getCategory());
            item.put("usedCount", r.getUsedCount());
            item.put("points", r.getPoints());
            rankingList.add(item);
        }
        result.put("usageRanking", rankingList);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementUsedCount(Integer id) {
        try {
            return baseMapper.incrementUsedCount(id) > 0;
        } catch (Exception e) {
            throw new RuntimeException("增加规则使用次数失败", e);
        }
    }
}
