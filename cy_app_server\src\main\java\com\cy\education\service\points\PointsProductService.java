package com.cy.education.service.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.points.PointsProduct;
import com.cy.education.model.params.PointsProductQueryParam;

/**
 * 积分商品服务接口
 */
public interface PointsProductService {

    /**
     * 分页查询积分商品
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<PointsProduct> page(PointsProductQueryParam param);

    /**
     * 根据ID查询商品
     *
     * @param id 商品ID
     * @return 商品对象
     */
    PointsProduct getById(Integer id);

    /**
     * 减少商品库存
     *
     * @param id 商品ID
     * @param count 减少数量
     * @return 是否成功
     */
    boolean decreaseStock(Integer id, Integer count);
}
