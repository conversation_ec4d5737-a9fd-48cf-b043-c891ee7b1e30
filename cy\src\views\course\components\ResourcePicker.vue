<template>
  <el-dialog title="选择资源" :model-value="visible" @update:model-value="$emit('update:visible', $event)" width="60%">
    <div class="filter-container">
      <el-input v-model="query.name" placeholder="资源名称" style="width: 200px;" class="filter-item" @keyup.enter="handleFilter" />
      <el-select v-model="query.type" placeholder="资源类型" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in resourceTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
    </div>

    <el-table :data="list" v-loading="loading" border fit highlight-current-row style="width: 100%;">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="资源名称" />
      <el-table-column prop="type" label="类型" width="100">
          <template #default="{row}">
              <span>{{ resourceTypeMap[row.type] }}</span>
          </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template #default="{row}">
          <el-button type="success" size="mini" @click="handleSelect(row)">
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        v-show="total > 0"
        v-model:current-page="query.page"
        v-model:page-size="query.size"
        :total="total"
        :page-sizes="[5, 10, 20]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="getList"
        @current-change="getList"
      />

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { getResourceList, Resource, ResourceQuery } from '@/api/resource';
import { ElMessage } from 'element-plus';

const props = defineProps<{ visible: boolean }>();
const emit = defineEmits(['update:visible', 'select']);

const list = ref<Resource[]>([]);
const total = ref(0);
const loading = ref(true);

const query = reactive<ResourceQuery>({
  page: 1,
  size: 5,
  name: undefined,
  type: undefined,
});

const resourceTypes = ref([
  { label: '文件', value: 'file' },
  { label: '视频', value: 'video' },
  { label: '文章', value: 'article' },
]);

const resourceTypeMap: { [key: string]: string } = {
    file: '文件',
    video: '视频',
    article: '文章',
}

const getList = async () => {
  loading.value = true;
  try {
    const response: any = await getResourceList(query);
    list.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('获取资源列表失败');
  } finally {
    loading.value = false;
  }
};

const handleFilter = () => {
  query.page = 1;
  getList();
};

const handleSelect = (resource: Resource) => {
  emit('select', resource);
  emit('update:visible', false);
};

watch(() => props.visible, (newVal) => {
  if (newVal) {
    getList();
  }
});
</script> 