-- 练习功能相关数据库表

-- 1. 修改exam_bank表，添加is_practice_enabled字段
ALTER TABLE `exam_bank` ADD COLUMN `is_practice_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否允许学员刷题(0-不允许，1-允许)' AFTER `scope`;

-- 2. 练习记录表
CREATE TABLE `practice_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '练习记录ID',
  `user_id` int NOT NULL COMMENT '学员ID',
  `bank_id` int DEFAULT NULL COMMENT '题库ID(为空表示全部题库)',
  `type` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '练习类型(normal-正常练习，wrong-错题练习)',
  `total_questions` int NOT NULL DEFAULT '0' COMMENT '总题数',
  `answered_questions` int NOT NULL DEFAULT '0' COMMENT '已答题数',
  `correct_count` int NOT NULL DEFAULT '0' COMMENT '正确数',
  `wrong_count` int NOT NULL DEFAULT '0' COMMENT '错误数',
  `score` int DEFAULT NULL COMMENT '得分',
  `status` varchar(20) NOT NULL DEFAULT 'ongoing' COMMENT '状态(ongoing-进行中,completed-已完成,abandoned-已放弃)',
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习记录表';

-- 3. 练习答案表
CREATE TABLE `practice_answer` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `record_id` int NOT NULL COMMENT '练习记录ID',
  `question_id` int NOT NULL COMMENT '题目ID',
  `user_answer` text COMMENT '用户答案',
  `correct_answer` text COMMENT '正确答案',
  `is_correct` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否正确(0-错误，1-正确)',
  `answer_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习答案表';

-- 4. 练习统计表（用于快速查询学员在各题库的统计信息）
CREATE TABLE `practice_stats` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` int NOT NULL COMMENT '学员ID',
  `bank_id` int NOT NULL COMMENT '题库ID',
  `total_questions` int NOT NULL DEFAULT '0' COMMENT '题库总题数',
  `answered_questions` int NOT NULL DEFAULT '0' COMMENT '已答题数',
  `correct_count` int NOT NULL DEFAULT '0' COMMENT '正确数',
  `wrong_count` int NOT NULL DEFAULT '0' COMMENT '错误数',
  `accuracy_rate` decimal(5,2) DEFAULT '0.00' COMMENT '正确率',
  `last_practice_time` datetime DEFAULT NULL COMMENT '最后练习时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习统计表';

-- 添加索引
ALTER TABLE `practice_record`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_bank_id` (`bank_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_start_time` (`start_time`);

ALTER TABLE `practice_answer`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_record_id` (`record_id`),
  ADD KEY `idx_question_id` (`question_id`),
  ADD KEY `idx_is_correct` (`is_correct`),
  ADD UNIQUE KEY `uk_record_question` (`record_id`, `question_id`);

ALTER TABLE `practice_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_user_bank` (`user_id`, `bank_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_bank_id` (`bank_id`);

-- 添加外键约束
ALTER TABLE `practice_record`
  ADD CONSTRAINT `fk_practice_record_user` FOREIGN KEY (`user_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_practice_record_bank` FOREIGN KEY (`bank_id`) REFERENCES `exam_bank` (`id`) ON DELETE CASCADE;

ALTER TABLE `practice_answer`
  ADD CONSTRAINT `fk_practice_answer_record` FOREIGN KEY (`record_id`) REFERENCES `practice_record` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_practice_answer_question` FOREIGN KEY (`question_id`) REFERENCES `exam_question` (`id`) ON DELETE CASCADE;

ALTER TABLE `practice_stats`
  ADD CONSTRAINT `fk_practice_stats_user` FOREIGN KEY (`user_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_practice_stats_bank` FOREIGN KEY (`bank_id`) REFERENCES `exam_bank` (`id`) ON DELETE CASCADE; 