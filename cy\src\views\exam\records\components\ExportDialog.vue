<template>
  <el-dialog v-model="visible" title="导出考试记录" width="600px">
    <el-form :model="exportForm" label-width="120px">
      <el-form-item label="导出范围">
        <el-radio-group v-model="exportForm.scope">
          <el-radio label="all">全部记录</el-radio>
          <el-radio label="filtered">当前筛选结果</el-radio>
          <el-radio label="selected">选中记录</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="exportForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%;"
        />
      </el-form-item>
      
      <el-form-item label="考试状态">
        <el-checkbox-group v-model="exportForm.statuses">
          <el-checkbox label="0">未开始</el-checkbox>
          <el-checkbox label="1">进行中</el-checkbox>
          <el-checkbox label="2">已完成</el-checkbox>
          <el-checkbox label="3">已超时</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="导出字段">
        <el-checkbox-group v-model="exportForm.fields">
          <el-checkbox label="studentName">学员姓名</el-checkbox>
          <el-checkbox label="studentId">学员ID</el-checkbox>
          <el-checkbox label="examTitle">考试名称</el-checkbox>
          <el-checkbox label="score">得分</el-checkbox>
          <el-checkbox label="totalScore">总分</el-checkbox>
          <el-checkbox label="percentage">得分率</el-checkbox>
          <el-checkbox label="status">考试状态</el-checkbox>
          <el-checkbox label="startTime">开始时间</el-checkbox>
          <el-checkbox label="endTime">结束时间</el-checkbox>
          <el-checkbox label="duration">用时</el-checkbox>
          <el-checkbox label="submitTime">提交时间</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="导出格式">
        <el-radio-group v-model="exportForm.format">
          <el-radio label="xlsx">Excel (.xlsx)</el-radio>
          <el-radio label="csv">CSV (.csv)</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="详细选项">
        <el-checkbox-group v-model="exportForm.options">
          <el-checkbox label="includeAnswerDetails">包含答题详情</el-checkbox>
          <el-checkbox label="includeStatistics">包含统计信息</el-checkbox>
          <el-checkbox label="groupByExam">按考试分组</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleExport" :loading="exporting">
        确认导出
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ExcelUtils, formatDateForExcel, formatStatus } from '@/utils/excel'

interface Props {
  modelValue: boolean
  currentFilters?: any
  selectedRecords?: any[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'export-success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const exporting = ref(false)

const exportForm = reactive({
  scope: 'all',
  dateRange: [] as string[],
  statuses: ['0', '1', '2', '3'],
  fields: [
    'studentName',
    'examTitle', 
    'score',
    'totalScore',
    'percentage',
    'status',
    'startTime',
    'endTime',
    'duration'
  ],
  format: 'xlsx',
  options: ['includeStatistics']
})

// 状态映射
const statusMap = {
  '0': '未开始',
  '1': '进行中', 
  '2': '已完成',
  '3': '已超时'
}

// 字段配置
const fieldHeaders = [
  { key: 'studentName', title: '学员姓名', width: 15 },
  { key: 'studentId', title: '学员ID', width: 12 },
  { key: 'examTitle', title: '考试名称', width: 25 },
  { key: 'score', title: '得分', width: 10 },
  { key: 'totalScore', title: '总分', width: 10 },
  { key: 'percentage', title: '得分率(%)', width: 12 },
  { key: 'status', title: '考试状态', width: 12 },
  { key: 'startTime', title: '开始时间', width: 20 },
  { key: 'endTime', title: '结束时间', width: 20 },
  { key: 'duration', title: '用时(分钟)', width: 12 },
  { key: 'submitTime', title: '提交时间', width: 20 }
]

watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 处理导出
const handleExport = async () => {
  if (exportForm.fields.length === 0) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }
  
  exporting.value = true
  try {
    // 构建查询参数
    const queryParams = {
      scope: exportForm.scope,
      dateRange: exportForm.dateRange,
      statuses: exportForm.statuses,
      fields: exportForm.fields,
      options: exportForm.options,
      currentFilters: props.currentFilters,
      selectedIds: exportForm.scope === 'selected' ? props.selectedRecords?.map(r => r.id) : undefined
    }
    
    // 调用导出API获取数据
    // const data = await exportExamRecords(queryParams)
    
    // 模拟数据
    const mockData = [
      {
        studentName: '张三',
        studentId: 'S001',
        examTitle: 'JavaScript基础测试',
        score: 85,
        totalScore: 100,
        percentage: 85,
        status: 2,
        startTime: '2023-12-01 09:00:00',
        endTime: '2023-12-01 10:30:00',
        duration: 90,
        submitTime: '2023-12-01 10:25:00'
      },
      {
        studentName: '李四',
        studentId: 'S002', 
        examTitle: 'JavaScript基础测试',
        score: 92,
        totalScore: 100,
        percentage: 92,
        status: 2,
        startTime: '2023-12-01 09:00:00',
        endTime: '2023-12-01 10:20:00',
        duration: 80,
        submitTime: '2023-12-01 10:20:00'
      }
    ]
    
    // 处理数据
    const processedData = mockData.map(record => {
      const processed: any = {}
      
      exportForm.fields.forEach(field => {
        switch (field) {
          case 'status':
            processed[field] = formatStatus(record.status, statusMap)
            break
          case 'startTime':
          case 'endTime':
          case 'submitTime':
            processed[field] = formatDateForExcel(record[field as keyof typeof record] as string)
            break
          case 'percentage':
            processed[field] = `${record.percentage}%`
            break
          default:
            processed[field] = record[field as keyof typeof record]
        }
      })
      
      return processed
    })
    
    // 过滤表头
    const filteredHeaders = fieldHeaders.filter(h => exportForm.fields.includes(h.key))
    
    // 如果包含统计信息，添加统计行
    if (exportForm.options.includes('includeStatistics')) {
      const stats = calculateStatistics(mockData)
      processedData.push({}, stats) // 空行 + 统计行
    }
    
    // 导出文件
    const filename = `考试记录_${new Date().toISOString().slice(0, 10)}.${exportForm.format}`
    ExcelUtils.exportToExcel(processedData, filteredHeaders, filename, '考试记录')
    
    visible.value = false
    emit('export-success')
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 计算统计信息
const calculateStatistics = (data: any[]) => {
  const completedRecords = data.filter(r => r.status === 2)
  const totalScore = completedRecords.reduce((sum, r) => sum + r.score, 0)
  const avgScore = completedRecords.length > 0 ? (totalScore / completedRecords.length).toFixed(1) : 0
  const maxScore = completedRecords.length > 0 ? Math.max(...completedRecords.map(r => r.score)) : 0
  const minScore = completedRecords.length > 0 ? Math.min(...completedRecords.map(r => r.score)) : 0
  
  return {
    studentName: '统计信息',
    examTitle: `完成人数: ${completedRecords.length}`,
    score: `平均分: ${avgScore}`,
    totalScore: `最高分: ${maxScore}`,
    percentage: `最低分: ${minScore}`,
    status: '已完成',
    startTime: '',
    endTime: '',
    duration: '',
    submitTime: ''
  }
}
</script>

<style scoped>
:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}
</style>
