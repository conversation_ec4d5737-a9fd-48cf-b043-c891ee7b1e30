import {get, post} from '@/utils/request'

export const getPointsBalance = (userId: number) =>
  get<{ points: number }>(`/points/balance/${userId}`)

export const getPointsRecords = (params: { pageNum: number; pageSize: number}) =>
  get<{ list: any[]; total: number }>('/points/records', params)

export const getPointsExchanges = (params: { pageNum: number; pageSize: number}) =>
  get<{ list: any[]; total: number }>('/points/exchanges', params)

// 兑换商品API
export const exchangeGoodsApi = (data: { productId: string }) => post<any>('/points/exchanges', data)
