package com.cy.education.controller;

import com.cy.education.model.params.PracticeStatisticsQueryParams;
import com.cy.education.model.vo.PracticeStatisticsVO;
import com.cy.education.service.PracticeStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 练习统计管理控制器
 */
@RestController
@RequestMapping("/api/admin/practice-statistics")
@Api(tags = "练习统计管理")
public class PracticeStatisticsController {
    
    @Autowired
    private PracticeStatisticsService practiceStatisticsService;
    
    /**
     * 获取学员练习统计列表
     */
    @GetMapping("/list")
    @ApiOperation("获取学员练习统计列表")
    public List<PracticeStatisticsVO> getPracticeStatistics(PracticeStatisticsQueryParams params) {
        return practiceStatisticsService.getPracticeStatistics(params);
    }
    
    /**
     * 获取学员练习统计详情
     */
    @GetMapping("/detail/{userId}/{bankId}")
    @ApiOperation("获取学员练习统计详情")
    public Map<String, Object> getPracticeStatisticsDetail(
            @ApiParam("学员ID") @PathVariable Integer userId,
            @ApiParam("题库ID") @PathVariable Integer bankId) {
        return practiceStatisticsService.getPracticeStatisticsDetail(userId, bankId);
    }
    
    /**
     * 获取题库练习统计概览
     */
    @GetMapping("/bank-overview/{bankId}")
    @ApiOperation("获取题库练习统计概览")
    public Map<String, Object> getBankPracticeOverview(
            @ApiParam("题库ID") @PathVariable Integer bankId) {
        return practiceStatisticsService.getBankPracticeOverview(bankId);
    }
    
    /**
     * 获取全部练习统计概览
     */
    @GetMapping("/overview")
    @ApiOperation("获取全部练习统计概览")
    public Map<String, Object> getAllPracticeOverview() {
        return practiceStatisticsService.getAllPracticeOverview();
    }
    
    /**
     * 获取学员练习记录
     */
    @GetMapping("/records/{userId}")
    @ApiOperation("获取学员练习记录")
    public List<Map<String, Object>> getStudentPracticeRecords(
            @ApiParam("学员ID") @PathVariable Integer userId,
            @ApiParam("题库ID") @RequestParam(required = false) Integer bankId) {
        return practiceStatisticsService.getStudentPracticeRecords(userId, bankId);
    }
} 