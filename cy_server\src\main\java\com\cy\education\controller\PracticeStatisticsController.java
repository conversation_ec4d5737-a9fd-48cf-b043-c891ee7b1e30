package com.cy.education.controller;

import com.cy.education.model.params.PracticeStatisticsQueryParams;
import com.cy.education.model.vo.PracticeStatisticsVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.service.PracticeStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 练习统计管理控制器
 */
@RestController
@RequestMapping("/api/admin/practice-statistics")
@Api(tags = "练习统计管理")
@Slf4j
public class PracticeStatisticsController {
    
    @Autowired
    private PracticeStatisticsService practiceStatisticsService;
    
    /**
     * 获取学员练习统计列表
     */
    @GetMapping("/list")
    @ApiOperation("获取学员练习统计列表")
    public ApiResponse<PageResponse<PracticeStatisticsVO>> getPracticeStatistics(PracticeStatisticsQueryParams params) {
        try {
            PageResponse<PracticeStatisticsVO> result = practiceStatisticsService.getPracticeStatistics(params);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取学员练习统计列表失败", e);
            return ApiResponse.error("获取学员练习统计列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取学员练习统计详情
     */
    @GetMapping("/detail/{userId}/{bankId}")
    @ApiOperation("获取学员练习统计详情")
    public ApiResponse<Map<String, Object>> getPracticeStatisticsDetail(
            @ApiParam("学员ID") @PathVariable Integer userId,
            @ApiParam("题库ID") @PathVariable Integer bankId) {
        try {
            Map<String, Object> result = practiceStatisticsService.getPracticeStatisticsDetail(userId, bankId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取学员练习统计详情失败", e);
            return ApiResponse.error("获取学员练习统计详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取题库练习统计概览
     */
    @GetMapping("/bank-overview/{bankId}")
    @ApiOperation("获取题库练习统计概览")
    public ApiResponse<Map<String, Object>> getBankPracticeOverview(
            @ApiParam("题库ID") @PathVariable Integer bankId) {
        try {
            Map<String, Object> result = practiceStatisticsService.getBankPracticeOverview(bankId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取题库练习统计概览失败", e);
            return ApiResponse.error("获取题库练习统计概览失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取全部练习统计概览
     */
    @GetMapping("/overview")
    @ApiOperation("获取全部练习统计概览")
    public ApiResponse<Map<String, Object>> getAllPracticeOverview() {
        try {
            Map<String, Object> result = practiceStatisticsService.getAllPracticeOverview();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取全部练习统计概览失败", e);
            return ApiResponse.error("获取全部练习统计概览失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取学员练习记录
     */
    @GetMapping("/records/{userId}")
    @ApiOperation("获取学员练习记录")
    public ApiResponse<List<Map<String, Object>>> getStudentPracticeRecords(
            @ApiParam("学员ID") @PathVariable Integer userId,
            @ApiParam("题库ID") @RequestParam(required = false) Integer bankId) {
        try {
            List<Map<String, Object>> result = practiceStatisticsService.getStudentPracticeRecords(userId, bankId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取学员练习记录失败", e);
            return ApiResponse.error("获取学员练习记录失败: " + e.getMessage());
        }
    }

    /**
     * 导出练习统计数据
     */
    @PostMapping("/export")
    @ApiOperation("导出练习统计数据")
    public ApiResponse<Void> exportPracticeStatistics(@RequestBody PracticeStatisticsQueryParams params) {
        try {
            practiceStatisticsService.exportPracticeStatistics(params);
            return ApiResponse.success();
        } catch (Exception e) {
            log.error("导出练习统计数据失败", e);
            return ApiResponse.error("导出练习统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取练习统计图表数据
     */
    @GetMapping("/chart-data")
    @ApiOperation("获取练习统计图表数据")
    public ApiResponse<Map<String, Object>> getPracticeStatisticsChartData(PracticeStatisticsQueryParams params) {
        try {
            Map<String, Object> result = practiceStatisticsService.getPracticeStatisticsChartData(params);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取练习统计图表数据失败", e);
            return ApiResponse.error("获取练习统计图表数据失败: " + e.getMessage());
        }
    }
}