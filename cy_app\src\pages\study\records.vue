<template>
  <view class="page-container">
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">学习记录</text>
        <view class="placeholder"/>
      </view>
    </view>

    <view class="study-records">
      <view v-if="loading" class="loading-box">
        <up-loading-icon color="#667eea" size="20"/>
        <text>加载中...</text>
      </view>
      <view v-else-if="courseProgressList.length === 0" class="empty-box">
        <up-empty text="空空如也"/>
      </view>
      <view v-else class="records-list">
        <view
          v-for="course in courseProgressList"
          :key="course.courseId"
          class="record-card"
          @click="viewCourseDetail(course)"
        >
          <!-- 课程信息 -->
          <view class="card-header">
            <text class="course-title">{{ course.courseName }}</text>
          </view>

          <!-- 学习进度与时长 -->
          <view class="progress-section">
            <view class="progress-display">
              <text class="progress-number">{{ course.progress }}%</text>
              <text class="duration">学习时长：{{ formatDuration(course.duration) }}</text>
            </view>
            <view class="completed-status" :class="course.completed === 1 ? 'completed' : 'incomplete'">
              <up-icon
                :name="course.completed === 1 ? 'checkmark-circle' : 'close-circle'"
                :color="course.completed === 1 ? '#52c41a' : '#ff4d4f'"
                size="16"
              />
              <text>{{ course.completed === 1 ? '已完成' : '未完成' }}</text>
            </view>
          </view>

          <!-- 点击提示 -->
          <view class="detail-hint">
            <text>点击查看详情</text>
            <up-icon name="arrow-right" color="#bfbfbf" size="12"/>
          </view>
        </view>
        <!-- 分页加载更多提示 -->
        <view class="load-more" v-if="courseProgressList.length > 0">
          <view v-if="loadMoreStatus === 'loading'" class="loading-text">
            <text>加载中...</text>
          </view>
          <view v-else-if="loadMoreStatus === 'noMore'" class="nomore-text">
            <text>没有更多了</text>
          </view>
          <view v-else class="loadmore-text">
            <text>加载更多</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {getCourseProgressList, type CourseRecordVO} from '@/api/course'
import {onPullDownRefresh, onReachBottom} from "@dcloudio/uni-app";

const courseProgressList = ref<CourseRecordVO[]>([])
const loading = ref(true)
const loadMoreStatus = ref('more') // 'more' | 'loading' | 'noMore'
const pageNum = ref(1)
const pageSize = ref(5)
const total = ref(0)
const isLoadingMore = ref(false)
const hasMore = ref(true)

const goBack = () => {
  uni.navigateBack()
}

// 格式化日期
function formatDate(dateStr: string) {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    const y = date.getFullYear()
    const m = (date.getMonth() + 1).toString().padStart(2, '0')
    const d = date.getDate().toString().padStart(2, '0')
    const h = date.getHours().toString().padStart(2, '0')
    const min = date.getMinutes().toString().padStart(2, '0')
    return `${y}-${m}-${d} ${h}:${min}`
  } catch {
    return ''
  }
}

// 查看课程详情
function viewCourseDetail(course: CourseRecordVO) {
  uni.navigateTo({
    url: `/pages/study/record-detail?courseId=${course.courseId}`
  })
}

// 分页加载课程进度
async function loadCourseProgress(isLoadMore = false) {
  if (isLoadMore) {
    isLoadingMore.value = true
    loadMoreStatus.value = 'loading'
  } else {
    loading.value = true
    pageNum.value = 1
    courseProgressList.value = []
    hasMore.value = true
  }
  try {
    const res = await getCourseProgressList({pageNum: pageNum.value, pageSize: pageSize.value})
    total.value = res.total || 0
    if (isLoadMore) {
      courseProgressList.value = courseProgressList.value.concat(res.list || [])
    } else {
      courseProgressList.value = res.list || []
    }
    if (courseProgressList.value.length >= total.value) {
      hasMore.value = false
      loadMoreStatus.value = 'noMore'
    } else {
      hasMore.value = true
      loadMoreStatus.value = 'more'
    }
  } catch (e) {
    uni.showToast({title: '加载失败', icon: 'none'})
    if (!isLoadMore) courseProgressList.value = []
  } finally {
    loading.value = false
    isLoadingMore.value = false
    uni.stopPullDownRefresh();
  }
}

// 下拉刷新
onPullDownRefresh(() => {
  pageNum.value = 1
  courseProgressList.value = []
  hasMore.value = true
  loadCourseProgress()
})
// 上拉加载更多
onReachBottom(() => {
  if (!hasMore.value || isLoadingMore.value) return
  pageNum.value += 1
  loadCourseProgress(true)
})

onMounted(() => {
  loadCourseProgress()
})

// 格式化学习时长（秒转分钟/小时）
function formatDuration(seconds: number) {
  if (!seconds) return ''
  const mins = Math.floor(seconds / 60)
  const hours = Math.floor(mins / 60)
  const remainMins = mins % 60
  if (hours > 0) {
    return `${hours}小时${remainMins}分钟`
  }
  return `${mins}分钟`
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/study/records.scss";
</style>
