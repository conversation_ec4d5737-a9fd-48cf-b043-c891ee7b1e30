package com.cy.education.model.params;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 更新管理员参数
 */
@Data
public class AdminUpdateParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 管理员ID
     */
    @NotNull(message = "管理员ID不能为空")
    private Integer id;

    /**
     * 真实姓名 - 与前端匹配的字段名
     */
    @NotBlank(message = "姓名不能为空")
    private String realName;

    /**
     * 真实姓名 - 兼容原有字段名
     * 该getter用于与实体类对应
     */
    public String getName() {
        return this.realName;
    }

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_]{3,20}$", message = "用户名只能包含字母、数字、下划线，长度3-20位")
    private String username;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 部门ID
     */
    @NotNull(message = "部门不能为空")
    private Integer departmentId;

    /**
     * 状态：0禁用，1启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 备注
     */
    private String remark;
} 