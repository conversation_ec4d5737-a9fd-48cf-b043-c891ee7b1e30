<template>
  <div class="login-container">
    <div class="login-background"></div>
    <div class="login-box">
      <div class="login-logo">
        <img src="@/assets/logo.svg" alt="矿山学习社区系统" class="logo-img">
        <h1 class="logo-title">矿山学习社区系统</h1>
      </div>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        autocomplete="on"
        label-position="top"
      >
        <h2 class="form-title">管理员登录</h2>
        
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            type="text"
            tabindex="1"
            autocomplete="on"
            :prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            :type="passwordVisible ? 'text' : 'password'"
            placeholder="请输入密码"
            tabindex="2"
            autocomplete="on"
            :prefix-icon="Lock"
            show-password
          />
        </el-form-item>
        
        <el-form-item prop="captcha">
          <div class="captcha-container">
            <el-input
              v-model="loginForm.captcha"
              placeholder="请输入验证码"
              tabindex="3"
              autocomplete="off"
              maxlength="4"
              style="flex: 1"
            />
            <div class="captcha-image" @click="refreshCaptcha">
              <img 
                v-if="captchaImage" 
                :src="captchaImage" 
                alt="验证码"
                title="点击刷新验证码"
              />
              <div v-else class="captcha-loading">
                <el-icon><Loading /></el-icon>
                <span>加载中...</span>
              </div>
            </div>
          </div>
        </el-form-item>
        
        <div class="login-options">
          <el-checkbox v-model="loginForm.remember">记住密码</el-checkbox>
          <el-link type="primary" @click="handleForgotPassword">忘记密码？</el-link>
        </div>
        
        <el-button
          :loading="loading"
          type="primary"
          class="login-button"
          @click.prevent="handleLogin"
        >
          登录
        </el-button>
      </el-form>
      
      <div class="login-footer">
        <p>© 2023 矿山学习社区系统 - 版权所有</p>
      </div>
    </div>
    
    <!-- 忘记密码弹窗 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="忘记密码"
      width="400px"
      destroy-on-close
    >
      <el-form :model="forgotForm" :rules="forgotRules" ref="forgotFormRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="forgotForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="forgotForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <div class="verification-code">
            <el-input v-model="forgotForm.code" placeholder="请输入验证码" />
            <el-button
              type="primary"
              :disabled="countdown > 0"
              @click="handleSendCode"
            >
              {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="forgotForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="forgotForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="forgotPasswordVisible = false">取消</el-button>
        <el-button type="primary" @click="handleResetPassword" :loading="resetting">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { User, Lock, Loading } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { login, getVerificationCode, resetPassword } from '@/api/user'
import { getCaptcha, type CaptchaResponse } from '@/api/captcha'

// 路由实例
const router = useRouter()
// 用户状态
const userStore = useUserStore()

// 登录表单引用
const loginFormRef = ref<FormInstance>()
const passwordVisible = ref(false)
const loading = ref(false)

// 验证码相关
const captchaImage = ref('')
const captchaLoading = ref(false)
const loginForm = reactive({
  username: '',
  password: '',
  remember: false,
  captcha: '',
  captchaId: ''
})

// 登录表单校验规则
const loginRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
}

// 忘记密码相关
const forgotFormRef = ref<FormInstance>()
const forgotPasswordVisible = ref(false)
const resetting = ref(false)
const countdown = ref(0)
const forgotForm = reactive({
  username: '',
  phone: '',
  code: '',
  newPassword: '',
  confirmPassword: ''
})

// 忘记密码表单校验规则
const forgotRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== forgotForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取验证码
const loadCaptcha = async () => {
  try {
    captchaLoading.value = true
    const response = await getCaptcha()
    captchaImage.value = response.captchaImage
    loginForm.captchaId = response.captchaId
    loginForm.captcha = ''
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败，请重试')
  } finally {
    captchaLoading.value = false
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  loadCaptcha()
}

// 登录方法
const handleLogin = () => {
  if (!loginFormRef.value) return
  
  // 先重置任何可能的错误状态
  loading.value = false
  
  console.log('开始登录流程...')
  loginFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      console.log('表单验证失败，停止登录')
      return
    }
    
    console.log('表单验证通过，准备登录')
    loading.value = true
    console.log('开始验证登录表单...')
    
    try {
      console.log('调用登录API，参数:', {
        username: loginForm.username,
        password: '******', // 不打印实际密码
        remember: loginForm.remember
      })
      
      // 尝试使用直接API调用而不是通过store
      try {
        // 直接调用登录API
        console.log('直接调用登录API')
        const loginResponse = await login({
          username: loginForm.username,
          password: loginForm.password,
          remember: loginForm.remember,
          captchaId: loginForm.captchaId,
          captcha: loginForm.captcha
        })
        console.log('登录API直接调用结果:', loginResponse)
        
        if (loginResponse && loginResponse.token && loginResponse.user) {
          // 手动设置用户状态
          userStore.setToken(loginResponse.token)
          userStore.setUserInfo(loginResponse.user)
          
          // 记住密码逻辑
          if (loginForm.remember) {
            localStorage.setItem('username', loginForm.username)
            localStorage.setItem('password', window.btoa(loginForm.password))
          } else {
            localStorage.removeItem('username')
            localStorage.removeItem('password')
          }
          
          ElMessage.success('登录成功')
          
          // 确保loading状态关闭
          loading.value = false
          
          // 获取重定向地址
          const redirect = router.currentRoute.value.query.redirect as string
          const targetPath = redirect || '/'
          console.log('登录成功，准备跳转到:', targetPath)
          
          // 直接跳转，不使用延时
          console.log('执行路由跳转...')
          router.push(targetPath)
          console.log('路由跳转指令已发出')
          return
        }
      } catch (directError) {
        console.error('直接调用登录API失败:', directError)
        // 失败后继续尝试使用store方法
      }
      
      // 调用store登录方法作为备选
      console.log('尝试使用store登录方法')
      const success = await userStore.loginAction(
        loginForm.username, 
        loginForm.password, 
        loginForm.remember,
        loginForm.captchaId,
        loginForm.captcha
      )
      
      console.log('登录API调用结果:', success)
      
      if (success) {
        // 记住密码逻辑
        if (loginForm.remember) {
          localStorage.setItem('username', loginForm.username)
          localStorage.setItem('password', window.btoa(loginForm.password))
        } else {
          localStorage.removeItem('username')
          localStorage.removeItem('password')
        }
        
        ElMessage.success('登录成功')
        
        // 确保loading状态关闭
        loading.value = false
        
        // 获取重定向地址
        const redirect = router.currentRoute.value.query.redirect as string
        const targetPath = redirect || '/'
        console.log('登录成功，准备跳转到:', targetPath)
        
        // 直接跳转，不使用延时
        console.log('执行路由跳转...')
        router.push(targetPath)
        console.log('路由跳转指令已发出')
      } else {
        console.error('登录失败，用户名或密码错误')
        ElMessage.error('登录失败，请检查用户名和密码')
        loading.value = false
        // 登录失败时刷新验证码
        refreshCaptcha()
      }
    } catch (error) {
      console.error('登录过程中发生错误:', error)
      ElMessage.error(`登录失败: ${error instanceof Error ? error.message : '未知错误'}`)
      loading.value = false
      // 登录失败时刷新验证码
      refreshCaptcha()
    }
  })
}

// 忘记密码方法
const handleForgotPassword = () => {
  forgotPasswordVisible.value = true
  forgotForm.username = loginForm.username
}

// 发送验证码
const handleSendCode = async () => {
  if (!forgotForm.phone) {
    ElMessage.warning('请先输入手机号码')
    return
  }
  
  if (!/^1[3-9]\d{9}$/.test(forgotForm.phone)) {
    ElMessage.warning('请输入正确的手机号码')
    return
  }
  
  try {
    // 调用获取验证码API
    const result = await getVerificationCode(forgotForm.phone)
    
    if (result.success) {
      // 开始倒计时
      countdown.value = 60
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
      
      ElMessage.success(`验证码已发送至手机：${forgotForm.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}`)
    } else {
      ElMessage.error('验证码发送失败，请稍后重试')
    }
  } catch (error) {
    console.error('Send code error:', error)
    ElMessage.error('验证码发送失败，请稍后重试')
  }
}

// 重置密码
const handleResetPassword = async () => {
  if (!forgotFormRef.value) return
  
  forgotFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      resetting.value = true
      
      try {
        // 调用重置密码API
        const result = await resetPassword({
          username: forgotForm.username,
          phone: forgotForm.phone,
          code: forgotForm.code,
          newPassword: forgotForm.newPassword,
          confirmPassword: forgotForm.confirmPassword
        })
        
        if (result.success) {
          ElMessage.success('密码重置成功，请使用新密码登录')
          forgotPasswordVisible.value = false
          
          // 更新登录表单的用户名
          loginForm.username = forgotForm.username
          loginForm.password = ''
        } else {
          ElMessage.error('密码重置失败，请检查信息是否正确')
        }
      } catch (error) {
        console.error('Reset password error:', error)
        ElMessage.error('密码重置失败，请稍后重试')
      } finally {
        resetting.value = false
      }
    }
  })
}

// 页面加载时检查是否有记住的账号密码
onMounted(() => {
  const savedUsername = localStorage.getItem('username')
  const savedPassword = localStorage.getItem('password')
  
  if (savedUsername && savedPassword) {
    loginForm.username = savedUsername
    loginForm.password = window.atob(savedPassword)
    loginForm.remember = true
  }
  
  // 加载验证码
  loadCaptcha()
})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.freepik.com/free-photo/mining-site-with-machinery-trucks-excavators-quarry_342744-1274.jpg');
  background-size: cover;
  background-position: center;
  filter: blur(5px);
  opacity: 0.8;
}

.login-box {
  position: relative;
  width: 420px;
  padding: 30px 35px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  z-index: 1;
}

.login-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.logo-img {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.logo-title {
  margin-top: 15px;
  font-size: 22px;
  font-weight: 600;
  color: #333;
}

.form-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 25px;
}

.login-form {
  width: 100%;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  padding: 12px 20px;
  font-size: 16px;
}

.login-footer {
  margin-top: 30px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.verification-code {
  display: flex;
  gap: 10px;
}

.verification-code .el-input {
  flex: 1;
}

.verification-code .el-button {
  flex-shrink: 0;
  width: 130px;
}

.captcha-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.captcha-image {
  width: 120px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  transition: border-color 0.2s;
}

.captcha-image:hover {
  border-color: #409eff;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.captcha-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #909399;
}

.captcha-loading .el-icon {
  font-size: 16px;
  margin-bottom: 2px;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 