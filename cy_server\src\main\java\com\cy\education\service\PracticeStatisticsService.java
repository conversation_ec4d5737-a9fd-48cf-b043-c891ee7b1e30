package com.cy.education.service;

import com.cy.education.model.params.PracticeStatisticsQueryParams;
import com.cy.education.model.vo.PracticeStatisticsVO;

import java.util.List;
import java.util.Map;

/**
 * 练习统计Service接口
 */
public interface PracticeStatisticsService {
    
    /**
     * 获取学员练习统计列表
     * @param params 查询参数
     * @return 练习统计列表
     */
    List<PracticeStatisticsVO> getPracticeStatistics(PracticeStatisticsQueryParams params);
    
    /**
     * 获取学员练习统计详情
     * @param userId 学员ID
     * @param bankId 题库ID
     * @return 练习统计详情
     */
    Map<String, Object> getPracticeStatisticsDetail(Integer userId, Integer bankId);
    
    /**
     * 获取题库练习统计概览
     * @param bankId 题库ID
     * @return 题库练习统计概览
     */
    Map<String, Object> getBankPracticeOverview(Integer bankId);
    
    /**
     * 获取全部练习统计概览
     * @return 全部练习统计概览
     */
    Map<String, Object> getAllPracticeOverview();
    
    /**
     * 获取学员练习记录
     * @param userId 学员ID
     * @param bankId 题库ID
     * @return 练习记录列表
     */
    List<Map<String, Object>> getStudentPracticeRecords(Integer userId, Integer bankId);
} 