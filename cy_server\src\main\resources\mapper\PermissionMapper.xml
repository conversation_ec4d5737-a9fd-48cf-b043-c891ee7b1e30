<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.PermissionMapper">

    <!-- 根据管理员ID查询权限列表 -->
    <select id="selectPermissionsByAdminId" resultType="com.cy.education.model.entity.Permission">
        SELECT p.*
        FROM permissions p
        INNER JOIN admin_permissions ap ON p.id = ap.permission_id
        WHERE ap.admin_id = #{adminId}
        ORDER BY p.id ASC
    </select>

    <!-- 根据模块查询权限列表 -->
    <select id="selectPermissionsByModule" resultType="com.cy.education.model.entity.Permission">
        SELECT *
        FROM permissions
        WHERE module = #{module}
        ORDER BY id ASC
    </select>

</mapper> 