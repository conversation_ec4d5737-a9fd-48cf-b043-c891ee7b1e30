import request from '@/utils/request'

export interface Course {
  id?: number
  name: string
  description?: string
  coverImageUrl?: string
  status?: 'draft' | 'published'
  structure?: string // JSON string for course structure
  creatorId?: number
  createTime?: string
  updateTime?: string
}

export interface CourseQuery {
  page: number
  size: number
  name?: string
}

// Get course list
export function getCourseList(params: CourseQuery) {
  return request({
    url: '/courses',
    method: 'get',
    params
  })
}

// Create a new course
export function createCourse(data: Course) {
  return request({
    url: '/courses',
    method: 'post',
    data
  })
}

// Get course details for editing
export function getCourseDetails(id: number) {
  return request({
    url: `/courses/${id}`,
    method: 'get'
  })
}

// Get course details for preview
export function getCourseForPreview(id: number) {
  return request({
    url: `/courses/${id}/preview`,
    method: 'get'
  })
}

// Update a course
export function updateCourse(id: number, data: Course) {
  return request({
    url: `/courses/${id}`,
    method: 'put',
    data
  })
}

// Delete a course by ID
export function deleteCourse(id: number) {
  return request({
    url: `/courses/${id}`,
    method: 'delete'
  })
} 