# 试卷管理功能修复总结

## 问题描述

### 1. 题目总数显示错误，都为0
**原因**: 前端使用`paper.questions?.length || 0`计算题目数量，但后端在列表查询时没有返回`questions`字段，导致计算结果为0。

### 2. 导出Word功能404错误
**原因**: 前端API调用中存在重复的`/api`前缀，导致请求URL变成`/api/api/exam/paper/2/export`。

## 修复方案

### ✅ 1. 修复题目总数统计

#### 后端修复
1. **ExamPaperVO添加题目数量字段**:
```java
/**
 * 题目数量
 */
private Integer questionCount;
```

2. **列表查询时统计题目数量**:
```java
// 转换为VO对象
List<ExamPaperVO> voList = paperPage.getRecords().stream().map(paper -> {
    ExamPaperVO vo = new ExamPaperVO();
    BeanUtils.copyProperties(paper, vo);
    
    // 统计题目数量
    LambdaQueryWrapper<ExamPaperQuestion> questionQueryWrapper = new LambdaQueryWrapper<>();
    questionQueryWrapper.eq(ExamPaperQuestion::getPaperId, paper.getId());
    long questionCount = examPaperQuestionMapper.selectCount(questionQueryWrapper);
    vo.setQuestionCount((int) questionCount);
    
    return vo;
}).collect(Collectors.toList());
```

3. **详情查询时设置题目数量**:
```java
// 查询试卷题目列表
List<ExamPaperQuestionVO> questions = examPaperQuestionMapper.selectQuestionsByPaperId(id);
vo.setQuestions(questions);
vo.setQuestionCount(questions != null ? questions.size() : 0);
```

#### 前端修复
```typescript
const res = await getPaperList(params)
paperList.value = res.list.map(paper => ({
  ...paper,
  questionCount: paper.questionCount || 0, // 使用后端返回的题目数量
  createTime: formatDateTime(paper.createdAt),
  usageCount: Math.floor(Math.random() * 50),
  isPublished: paper.isPublished
}))
```

### ✅ 2. 修复导出Word功能URL问题

#### 前端API修复
移除重复的`/api`前缀：

```typescript
// 修复前：
export function exportExamPaper(examId: number, options: ExamPaperExportOptions) {
  return post(`/api/exam/paper/${examId}/export`, options, { responseType: 'blob' })
}

// 修复后：
export function exportExamPaper(examId: number, options: ExamPaperExportOptions) {
  return post(`/exam/paper/${examId}/export`, options, { responseType: 'blob' })
}
```

同时修复了其他API的重复前缀问题：
- `/api/admin/import` → `/admin/import`
- `/api/admin/export` → `/admin/export`
- `/api/exam/question/import/template` → `/exam/question/import/template`
- `/api/exam/question/import` → `/exam/question/import`
- `/api/exam/question/export` → `/exam/question/export`
- `/api/exam-record/export` → `/exam-record/export`

### ✅ 3. 增强导出Word功能

#### 后端导出功能升级
1. **支持多种格式**:
```java
@Override
public void exportPaper(Integer paperId, Map<String, Object> options, HttpServletResponse response) throws IOException {
    // 获取导出选项
    String format = (String) options.getOrDefault("format", "docx");
    boolean includeAnswers = (Boolean) options.getOrDefault("includeAnswers", true);
    boolean includeExplanations = (Boolean) options.getOrDefault("includeExplanations", true);

    if ("docx".equals(format)) {
        exportPaperAsWord(paper, paperId, includeAnswers, includeExplanations, response);
    } else {
        exportPaperAsText(paper, paperId, includeAnswers, includeExplanations, response);
    }
}
```

2. **Word格式导出**:
- 使用Apache POI创建专业的Word文档
- 支持标题居中、字体设置
- 支持答案和解析的颜色区分
- 支持选择题选项格式化

3. **导出选项支持**:
- `format`: "docx" | "txt" - 导出格式
- `includeAnswers`: boolean - 是否包含答案
- `includeExplanations`: boolean - 是否包含解析

#### Word文档特性
```java
// 设置标题
XWPFParagraph titleParagraph = document.createParagraph();
titleParagraph.setAlignment(ParagraphAlignment.CENTER);
XWPFRun titleRun = titleParagraph.createRun();
titleRun.setText(paper.getTitle());
titleRun.setBold(true);
titleRun.setFontSize(18);
titleRun.setFontFamily("宋体");

// 答案用蓝色显示
answerRun.setColor("0000FF");

// 解析用绿色显示
analysisRun.setColor("008000");
```

## 技术要点

### 1. 题目数量统计优化
- **列表查询**: 使用`selectCount`统计，避免查询完整题目数据
- **详情查询**: 直接使用已查询的题目列表长度
- **前端显示**: 使用后端返回的`questionCount`字段

### 2. API路径规范
- **baseURL配置**: `/api` (在request.ts中配置)
- **API调用**: 不包含`/api`前缀，由baseURL自动添加
- **避免重复**: 确保不会出现`/api/api/`的情况

### 3. Word文档生成
- **依赖**: Apache POI XWPF
- **字体**: 统一使用"宋体"确保中文显示
- **样式**: 标题居中加粗，答案蓝色，解析绿色
- **结构**: 题目 → 选项 → 答案 → 解析

### 4. 数据结构处理
```java
// ExamPaperQuestionVO结构
{
  id: Integer,
  paperId: Integer,
  questionId: Integer,
  question: ExamQuestionVO,  // 嵌套的题目详情
  score: Integer,
  questionOrder: Integer
}

// ExamQuestionVO结构
{
  id: Integer,
  title: String,           // 题目标题
  type: String,           // 题目类型
  options: Object,        // 选项(List<String>)
  correctAnswer: String,  // 正确答案
  explanation: String     // 题目解析
}
```

## 测试步骤

### 1. 题目数量显示测试
1. **重启后端服务**
2. **访问试卷管理页面**: `http://localhost:3000/exam/paper`
3. **检查题目数量列**: 应显示实际的题目数量，不再是0
4. **创建新试卷**: 添加题目后检查数量是否正确更新

### 2. 导出Word功能测试
1. **点击"导出Word"按钮**: 应弹出导出选项对话框
2. **选择导出选项**: 
   - 格式：Word文档(.docx)
   - 包含答案：是/否
   - 包含解析：是/否
3. **执行导出**: 应成功下载Word文件
4. **检查Word内容**: 
   - 标题居中加粗
   - 题目格式正确
   - 答案和解析颜色区分
   - 中文显示正常

### 3. URL路径测试
1. **打开浏览器开发者工具**
2. **执行导出操作**
3. **检查Network面板**: 请求URL应为`/api/exam/paper/{id}/export`，不应出现`/api/api/`

## 预期结果

### ✅ 题目数量显示
- 试卷列表中"题目数量"列显示正确的数字
- 新创建的试卷添加题目后数量实时更新
- 详情页面显示准确的题目统计

### ✅ 导出Word功能
- 点击"导出Word"按钮正常弹出选项对话框
- 选择选项后成功下载Word文档
- Word文档格式专业，包含完整的试卷内容
- 支持答案和解析的可选导出

### ✅ API路径规范
- 所有API请求路径正确，无404错误
- 导出功能正常工作
- 其他相关功能不受影响

## 后续优化建议

### 1. 性能优化
- 考虑在试卷表中添加`question_count`字段，避免每次查询时统计
- 使用缓存机制减少重复查询

### 2. 功能增强
- 支持PDF格式导出
- 支持批量导出多个试卷
- 支持自定义Word模板

### 3. 用户体验
- 添加导出进度提示
- 支持大文件导出的异步处理
- 提供导出历史记录

现在请重启后端服务，然后按照测试步骤验证修复效果！
