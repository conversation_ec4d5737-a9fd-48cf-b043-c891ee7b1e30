package com.cy.education.controller;

import com.cy.education.model.entity.study.StudyRecord;
import com.cy.education.model.params.PageParams;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.model.vo.StudyStatisticsVO;
import com.cy.education.model.vo.CourseRecordVO;
import com.cy.education.model.vo.CourseRecordDetailVO;
import com.cy.education.service.study.StudyRecordService;
import com.cy.education.utils.SecurityUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 学习记录控制器
 */
@RestController
@RequestMapping("/study/records")
public class StudyRecordController {

    @Autowired
    private StudyRecordService studyRecordService;

    /**
     * 获取课程学习记录分页列表
     */
    @GetMapping("/course-progress")
    public ApiResponse<PageResponse<CourseRecordVO>> getCourseRecordList(PageParams params) {
        Integer userId = SecurityUtil.getCurrentUserId();
        return ApiResponse.success(studyRecordService.getCourseRecordList(userId, params));
    }

    /**
     * 获取课程详情学习记录（章节）
     */
    @GetMapping("/course-detail")
    public ApiResponse<CourseRecordDetailVO> getCourseRecordDetail(@RequestParam Integer courseId) {
        Integer userId = SecurityUtil.getCurrentUserId();
        return ApiResponse.success(studyRecordService.getCourseRecordDetail(userId, courseId));
    }

    /**
     * 创建或更新学习记录
     */
    @PostMapping("/save")
    public ApiResponse<Map<String, Object>> saveStudyRecord(@RequestBody StudyRecord studyRecord) {
        studyRecord.setUserId(SecurityUtil.getCurrentUserId());
        StudyRecord savedRecord = studyRecordService.saveStudyRecord(studyRecord);
        Map<String, Object> result = new HashMap<>();
        result.put("id", savedRecord.getId());
        return ApiResponse.success(result);
    }

    /**
     * 记录资源访问日志
     */
    @PostMapping("/access/log")
    public ApiResponse<Boolean> logResourceAccess(@RequestBody Map<String, Object> accessLog) {
        boolean success = studyRecordService.logResourceAccess(accessLog);
        return ApiResponse.success(success);
    }

    /**
     * 获取学习统计数据
     */
    @GetMapping("/statistics")
    public ApiResponse<StudyStatisticsVO> getStudyStatistics(
            @RequestParam(required = false) Integer courseId
    ) {
        Integer studentId = SecurityUtil.getCurrentUserId();
        StudyStatisticsVO statistics = studyRecordService.getStudyStatistics(studentId, courseId);
        return ApiResponse.success(statistics);
    }


}
