<template>
  <view class="battle-page">
    <!-- 状态栏 -->
    <view class="status-bar">
      <view class="timer">
        <up-icon name="clock" size="16" color="#fff"></up-icon>
        <text class="timer-text">{{ formatTime(remainingTime) }}</text>
      </view>
      <view class="progress">
        <text class="progress-text">{{ currentQuestionIndex }}/{{ totalQuestions }}</text>
      </view>
    </view>

    <!-- 对战区域 -->
    <view class="battle-area">
      <!-- 左侧玩家 -->
      <view class="player-area left">
        <view class="player-info">
          <view class="player-avatar">
            <up-icon name="account" size="24" color="#667eea"></up-icon>
          </view>
          <text class="player-name">{{ leftPlayer.name }}</text>
          <text class="player-score">{{ leftPlayer.score }}分</text>
        </view>
        <view class="player-progress">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: leftPlayer.progress + '%' }"></view>
          </view>
          <text class="progress-label">{{ leftPlayer.answeredCount }}/{{ totalQuestions }}</text>
        </view>
      </view>

      <!-- VS标识 -->
      <view class="vs-indicator">
        <text class="vs-text">VS</text>
      </view>

      <!-- 右侧玩家 -->
      <view class="player-area right">
        <view class="player-info">
          <view class="player-avatar">
            <up-icon name="account" size="24" color="#f59e0b"></up-icon>
          </view>
          <text class="player-name">{{ rightPlayer.name }}</text>
          <text class="player-score">{{ rightPlayer.score }}分</text>
        </view>
        <view class="player-progress">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: rightPlayer.progress + '%' }"></view>
          </view>
          <text class="progress-label">{{ rightPlayer.answeredCount }}/{{ totalQuestions }}</text>
        </view>
      </view>
    </view>

    <!-- 题目区域 -->
    <view class="question-area" v-if="currentQuestion">
      <view class="question-header">
        <text class="question-title">第{{ currentQuestionIndex }}题</text>
        <text class="question-type">{{ getQuestionTypeText(currentQuestion.type) }}</text>
      </view>
      
      <view class="question-content">
        <text class="question-text">{{ currentQuestion.content }}</text>
        <image v-if="currentQuestion.image" :src="currentQuestion.image" class="question-image" mode="widthFix"></image>
      </view>

      <!-- 选择题选项 -->
      <view class="options-area" v-if="currentQuestion.type === 'single' || currentQuestion.type === 'multiple'">
        <view 
          class="option-item" 
          v-for="(option, index) in currentQuestion.options" 
          :key="index"
          :class="{ 'selected': isOptionSelected(index) }"
          @click="selectOption(index)"
        >
          <view class="option-label">{{ String.fromCharCode(65 + index) }}</view>
          <text class="option-text">{{ option }}</text>
        </view>
      </view>

      <!-- 判断题选项 -->
      <view class="judge-area" v-if="currentQuestion.type === 'judge'">
        <view 
          class="judge-option" 
          :class="{ 'selected': userAnswer === 'true' }"
          @click="selectJudge('true')"
        >
          <up-icon name="checkmark-circle" size="24" color="#10b981"></up-icon>
          <text>正确</text>
        </view>
        <view 
          class="judge-option" 
          :class="{ 'selected': userAnswer === 'false' }"
          @click="selectJudge('false')"
        >
          <up-icon name="close-circle" size="24" color="#ef4444"></up-icon>
          <text>错误</text>
        </view>
      </view>

      <!-- 填空题输入 -->
      <view class="fill-area" v-if="currentQuestion.type === 'fill'">
        <up-input 
          v-model="userAnswer" 
          placeholder="请输入答案"
          :border="true"
          :clearable="true"
        ></up-input>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-area">
        <up-button 
          type="primary" 
          text="提交答案" 
          :disabled="!userAnswer"
          @click="submitAnswer"
          size="large"
        ></up-button>
      </view>
    </view>

    <!-- 等待对手 -->
    <view class="waiting-area" v-if="isWaiting">
      <up-loading-icon mode="circle" size="48"></up-loading-icon>
      <text class="waiting-text">等待对手答题...</text>
    </view>

    <!-- 游戏结束对话框 -->
    <up-popup v-model="showResultDialog" mode="center" border-radius="16" :closeable="false">
      <view class="result-dialog">
        <view class="result-header">
          <view class="result-icon" :class="resultClass">
            <up-icon :name="resultIcon" size="48" :color="resultIconColor"></up-icon>
          </view>
          <text class="result-title">{{ resultTitle }}</text>
        </view>
        
        <view class="result-content">
          <view class="result-scores">
            <view class="score-item">
              <text class="score-label">我的得分</text>
              <text class="score-value">{{ myScore }}分</text>
            </view>
            <view class="score-item">
              <text class="score-label">对手得分</text>
              <text class="score-value">{{ opponentScore }}分</text>
            </view>
          </view>
          
          <view class="result-stats">
            <view class="stat-item">
              <text class="stat-label">正确率</text>
              <text class="stat-value">{{ accuracy }}%</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">用时</text>
              <text class="stat-value">{{ formatTime(totalTime) }}</text>
            </view>
          </view>
        </view>
        
        <view class="result-actions">
          <up-button text="再来一局" @click="playAgain"></up-button>
          <up-button type="primary" text="返回" @click="goBack"></up-button>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script>
import { submitAnswer, getGameResult } from '@/api/pk'

export default {
  data() {
    return {
      roomId: null,
      userId: null,
      
      // 游戏状态
      gameStatus: 'playing', // playing, finished
      remainingTime: 300, // 剩余时间（秒）
      currentQuestionIndex: 1,
      totalQuestions: 10,
      
      // 当前题目
      currentQuestion: null,
      userAnswer: '',
      selectedOptions: [],
      
      // 玩家信息
      leftPlayer: {
        id: null,
        name: '我',
        score: 0,
        progress: 0,
        answeredCount: 0
      },
      rightPlayer: {
        id: null,
        name: '对手',
        score: 0,
        progress: 0,
        answeredCount: 0
      },
      
      // 状态
      isWaiting: false,
      showResultDialog: false,
      
      // 结果
      gameResult: null,
      
      // WebSocket
      websocket: null,
      
      // 计时器
      timer: null,
      startTime: null
    }
  },
  
  computed: {
    resultClass() {
      if (!this.gameResult) return ''
      const isWin = this.gameResult.winner?.userId === this.userId
      return isWin ? 'win' : 'lose'
    },
    
    resultIcon() {
      if (!this.gameResult) return 'trophy'
      const isWin = this.gameResult.winner?.userId === this.userId
      return isWin ? 'trophy' : 'close-circle'
    },
    
    resultIconColor() {
      if (!this.gameResult) return '#f59e0b'
      const isWin = this.gameResult.winner?.userId === this.userId
      return isWin ? '#f59e0b' : '#ef4444'
    },
    
    resultTitle() {
      if (!this.gameResult) return '游戏结束'
      const isWin = this.gameResult.winner?.userId === this.userId
      if (!this.gameResult.winner) return '平局'
      return isWin ? '胜利！' : '失败'
    },
    
    myScore() {
      if (!this.gameResult) return 0
      const myParticipant = this.gameResult.participants?.find(p => p.userId === this.userId)
      return myParticipant?.score || 0
    },
    
    opponentScore() {
      if (!this.gameResult) return 0
      const opponent = this.gameResult.participants?.find(p => p.userId !== this.userId)
      return opponent?.score || 0
    },
    
    accuracy() {
      if (!this.gameResult) return 0
      const myParticipant = this.gameResult.participants?.find(p => p.userId === this.userId)
      if (!myParticipant || !myParticipant.answeredQuestions) return 0
      return Math.round((myParticipant.correctCount / myParticipant.answeredQuestions) * 100)
    },
    
    totalTime() {
      if (!this.gameResult) return 0
      const myParticipant = this.gameResult.participants?.find(p => p.userId === this.userId)
      return Math.floor((myParticipant?.answerTime || 0) / 1000)
    }
  },
  
  onLoad(options) {
    this.roomId = options.roomId
    this.userId = uni.getStorageSync('userInfo')?.id
    this.initWebSocket()
    this.startTimer()
    this.startTime = Date.now()
  },
  
  onUnload() {
    this.closeWebSocket()
    this.stopTimer()
  },
  
  methods: {
    // 格式化时间
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },
    
    // 获取题目类型文本
    getQuestionTypeText(type) {
      const typeMap = {
        'single': '单选题',
        'multiple': '多选题',
        'judge': '判断题',
        'fill': '填空题'
      }
      return typeMap[type] || '未知题型'
    },
    
    // 选择选项
    selectOption(index) {
      if (this.currentQuestion.type === 'single') {
        this.selectedOptions = [index]
        this.userAnswer = String.fromCharCode(65 + index)
      } else if (this.currentQuestion.type === 'multiple') {
        const optionIndex = this.selectedOptions.indexOf(index)
        if (optionIndex > -1) {
          this.selectedOptions.splice(optionIndex, 1)
        } else {
          this.selectedOptions.push(index)
        }
        this.userAnswer = this.selectedOptions.map(i => String.fromCharCode(65 + i)).sort().join('')
      }
    },
    
    // 判断选项是否被选中
    isOptionSelected(index) {
      return this.selectedOptions.includes(index)
    },
    
    // 选择判断题答案
    selectJudge(value) {
      this.userAnswer = value
    },
    
    // 提交答案
    async submitAnswer() {
      if (!this.userAnswer) {
        uni.showToast({ title: '请选择答案', icon: 'none' })
        return
      }
      
      const answerTime = Date.now() - this.startTime
      
      try {
        await submitAnswer({
          roomId: this.roomId,
          userId: this.userId,
          questionId: this.currentQuestion.id,
          questionOrder: this.currentQuestionIndex,
          userAnswer: this.userAnswer,
          answerTime: answerTime
        })
        
        // 重置答案
        this.userAnswer = ''
        this.selectedOptions = []
        this.isWaiting = true
        
      } catch (error) {
        console.error('提交答案失败:', error)
        uni.showToast({ title: '提交失败', icon: 'none' })
      }
    },
    
    // 开始计时器
    startTimer() {
      this.timer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--
        } else {
          this.timeUp()
        }
      }, 1000)
    },
    
    // 停止计时器
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    
    // 时间到
    timeUp() {
      this.stopTimer()
      uni.showToast({ title: '时间到！', icon: 'none' })
      // 自动提交当前答案或跳过
      if (this.userAnswer) {
        this.submitAnswer()
      }
    },
    
    // 再来一局
    playAgain() {
      uni.navigateBack()
    },
    
    // 返回
    goBack() {
      uni.navigateBack()
    },
    
    // WebSocket相关方法
    initWebSocket() {
      if (!this.userId) return
      
      // 根据环境配置WebSocket地址
      const baseUrl = process.env.NODE_ENV === 'development' ? 'localhost:8080' : 'your-server-domain.com'
      const wsUrl = `ws://${baseUrl}/websocket/pk/${this.userId}`
      this.websocket = uni.connectSocket({
        url: wsUrl,
        success: () => {
          console.log('WebSocket连接成功')
          // 加入房间
          this.sendWebSocketMessage({
            type: 'room_join',
            roomId: this.roomId
          })
        }
      })
      
      this.websocket.onMessage((res) => {
        try {
          const message = JSON.parse(res.data)
          this.handleWebSocketMessage(message)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      })
    },
    
    closeWebSocket() {
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
    },
    
    sendWebSocketMessage(message) {
      if (this.websocket) {
        message.userId = this.userId
        message.timestamp = Date.now()
        this.websocket.send({
          data: JSON.stringify(message)
        })
      }
    },
    
    handleWebSocketMessage(message) {
      console.log('收到WebSocket消息:', message)
      
      switch (message.type) {
        case 'game_question':
          this.onReceiveQuestion(message.data)
          break
        case 'game_progress':
          this.onGameProgress(message.data)
          break
        case 'game_finish':
          this.onGameFinish(message.data)
          break
      }
    },
    
    onReceiveQuestion(data) {
      this.currentQuestion = data.question
      this.currentQuestionIndex = data.questionOrder
      this.totalQuestions = data.totalQuestions
      this.isWaiting = false
      this.startTime = Date.now()
    },
    
    onGameProgress(data) {
      // 更新对手进度
      if (data.userId !== this.userId) {
        this.rightPlayer.score = data.score || 0
        this.rightPlayer.answeredCount = data.questionOrder || 0
        this.rightPlayer.progress = (this.rightPlayer.answeredCount / this.totalQuestions) * 100
      } else {
        this.leftPlayer.score = data.score || 0
        this.leftPlayer.answeredCount = data.questionOrder || 0
        this.leftPlayer.progress = (this.leftPlayer.answeredCount / this.totalQuestions) * 100
      }
      
      // 如果有下一题，显示下一题
      if (data.nextQuestion && data.userId === this.userId) {
        this.currentQuestion = data.nextQuestion
        this.currentQuestionIndex = data.nextQuestionOrder
        this.isWaiting = false
        this.startTime = Date.now()
      }
    },
    
    onGameFinish(data) {
      this.gameStatus = 'finished'
      this.gameResult = data
      this.showResultDialog = true
      this.stopTimer()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/pages/pk/battle.scss";
</style>
