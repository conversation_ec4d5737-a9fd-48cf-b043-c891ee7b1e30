<template>
  <el-dialog v-model="visible" title="导出学习记录" width="600px">
    <el-form :model="exportForm" label-width="120px">
      <el-form-item label="导出范围">
        <el-radio-group v-model="exportForm.scope">
          <el-radio label="all">全部记录</el-radio>
          <el-radio label="filtered">当前筛选结果</el-radio>
          <el-radio label="selected">选中记录</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="exportForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%;"
        />
      </el-form-item>
      
      <el-form-item label="学习状态">
        <el-checkbox-group v-model="exportForm.statuses">
          <el-checkbox label="0">未开始</el-checkbox>
          <el-checkbox label="1">学习中</el-checkbox>
          <el-checkbox label="2">已完成</el-checkbox>
          <el-checkbox label="3">已暂停</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="课程类型">
        <el-checkbox-group v-model="exportForm.courseTypes">
          <el-checkbox label="video">视频课程</el-checkbox>
          <el-checkbox label="document">文档课程</el-checkbox>
          <el-checkbox label="live">直播课程</el-checkbox>
          <el-checkbox label="mixed">混合课程</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="导出字段">
        <el-checkbox-group v-model="exportForm.fields">
          <el-checkbox label="studentName">学员姓名</el-checkbox>
          <el-checkbox label="studentId">学员ID</el-checkbox>
          <el-checkbox label="departmentName">部门</el-checkbox>
          <el-checkbox label="courseName">课程名称</el-checkbox>
          <el-checkbox label="courseType">课程类型</el-checkbox>
          <el-checkbox label="progress">学习进度</el-checkbox>
          <el-checkbox label="studyTime">学习时长</el-checkbox>
          <el-checkbox label="status">学习状态</el-checkbox>
          <el-checkbox label="startTime">开始时间</el-checkbox>
          <el-checkbox label="lastStudyTime">最后学习时间</el-checkbox>
          <el-checkbox label="completionTime">完成时间</el-checkbox>
          <el-checkbox label="score">课程得分</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="导出格式">
        <el-radio-group v-model="exportForm.format">
          <el-radio label="xlsx">Excel (.xlsx)</el-radio>
          <el-radio label="csv">CSV (.csv)</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="详细选项">
        <el-checkbox-group v-model="exportForm.options">
          <el-checkbox label="includeStatistics">包含统计信息</el-checkbox>
          <el-checkbox label="groupByStudent">按学员分组</el-checkbox>
          <el-checkbox label="groupByCourse">按课程分组</el-checkbox>
          <el-checkbox label="groupByDepartment">按部门分组</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleExport" :loading="exporting">
        确认导出
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ExcelUtils, formatDateForExcel, formatStatus } from '@/utils/excel'

interface Props {
  modelValue: boolean
  currentFilters?: any
  selectedRecords?: any[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'export-success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const exporting = ref(false)

const exportForm = reactive({
  scope: 'all',
  dateRange: [] as string[],
  statuses: ['0', '1', '2', '3'],
  courseTypes: ['video', 'document', 'live', 'mixed'],
  fields: [
    'studentName',
    'departmentName',
    'courseName',
    'courseType',
    'progress',
    'studyTime',
    'status',
    'startTime',
    'lastStudyTime'
  ],
  format: 'xlsx',
  options: ['includeStatistics']
})

// 状态映射
const statusMap = {
  '0': '未开始',
  '1': '学习中',
  '2': '已完成',
  '3': '已暂停'
}

// 课程类型映射
const courseTypeMap = {
  'video': '视频课程',
  'document': '文档课程',
  'live': '直播课程',
  'mixed': '混合课程'
}

// 字段配置
const fieldHeaders = [
  { key: 'studentName', title: '学员姓名', width: 15 },
  { key: 'studentId', title: '学员ID', width: 12 },
  { key: 'departmentName', title: '部门', width: 15 },
  { key: 'courseName', title: '课程名称', width: 25 },
  { key: 'courseType', title: '课程类型', width: 12 },
  { key: 'progress', title: '学习进度(%)', width: 12 },
  { key: 'studyTime', title: '学习时长(分钟)', width: 15 },
  { key: 'status', title: '学习状态', width: 12 },
  { key: 'startTime', title: '开始时间', width: 20 },
  { key: 'lastStudyTime', title: '最后学习时间', width: 20 },
  { key: 'completionTime', title: '完成时间', width: 20 },
  { key: 'score', title: '课程得分', width: 10 }
]

watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 处理导出
const handleExport = async () => {
  if (exportForm.fields.length === 0) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }
  
  exporting.value = true
  try {
    // 构建查询参数
    const queryParams = {
      scope: exportForm.scope,
      dateRange: exportForm.dateRange,
      statuses: exportForm.statuses,
      courseTypes: exportForm.courseTypes,
      fields: exportForm.fields,
      options: exportForm.options,
      currentFilters: props.currentFilters,
      selectedIds: exportForm.scope === 'selected' ? props.selectedRecords?.map(r => r.id) : undefined
    }
    
    // 调用导出API获取数据
    // const data = await exportStudyRecords(queryParams)
    
    // 模拟数据
    const mockData = [
      {
        studentName: '张三',
        studentId: 'S001',
        departmentName: '技术部',
        courseName: 'JavaScript基础教程',
        courseType: 'video',
        progress: 85,
        studyTime: 120,
        status: 1,
        startTime: '2023-12-01 09:00:00',
        lastStudyTime: '2023-12-05 14:30:00',
        completionTime: null,
        score: null
      },
      {
        studentName: '李四',
        studentId: 'S002',
        departmentName: '市场部',
        courseName: 'Vue.js进阶课程',
        courseType: 'video',
        progress: 100,
        studyTime: 180,
        status: 2,
        startTime: '2023-11-28 10:00:00',
        lastStudyTime: '2023-12-03 16:45:00',
        completionTime: '2023-12-03 16:45:00',
        score: 95
      }
    ]
    
    // 处理数据
    let processedData = mockData.map(record => {
      const processed: any = {}
      
      exportForm.fields.forEach(field => {
        switch (field) {
          case 'status':
            processed[field] = formatStatus(record.status, statusMap)
            break
          case 'courseType':
            processed[field] = courseTypeMap[record.courseType as keyof typeof courseTypeMap]
            break
          case 'startTime':
          case 'lastStudyTime':
          case 'completionTime':
            processed[field] = record[field as keyof typeof record] 
              ? formatDateForExcel(record[field as keyof typeof record] as string)
              : ''
            break
          case 'progress':
            processed[field] = `${record.progress}%`
            break
          default:
            processed[field] = record[field as keyof typeof record] || ''
        }
      })
      
      return processed
    })
    
    // 按选项分组数据
    if (exportForm.options.includes('groupByStudent')) {
      processedData = groupDataBy(processedData, 'studentName')
    } else if (exportForm.options.includes('groupByCourse')) {
      processedData = groupDataBy(processedData, 'courseName')
    } else if (exportForm.options.includes('groupByDepartment')) {
      processedData = groupDataBy(processedData, 'departmentName')
    }
    
    // 过滤表头
    const filteredHeaders = fieldHeaders.filter(h => exportForm.fields.includes(h.key))
    
    // 如果包含统计信息，添加统计行
    if (exportForm.options.includes('includeStatistics')) {
      const stats = calculateStatistics(mockData)
      processedData.push({}, stats) // 空行 + 统计行
    }
    
    // 导出文件
    const filename = `学习记录_${new Date().toISOString().slice(0, 10)}.${exportForm.format}`
    ExcelUtils.exportToExcel(processedData, filteredHeaders, filename, '学习记录')
    
    visible.value = false
    emit('export-success')
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 按字段分组数据
const groupDataBy = (data: any[], groupField: string) => {
  const grouped: any[] = []
  const groups = data.reduce((acc, item) => {
    const key = item[groupField]
    if (!acc[key]) {
      acc[key] = []
    }
    acc[key].push(item)
    return acc
  }, {})
  
  Object.entries(groups).forEach(([groupName, items]) => {
    // 添加分组标题
    const groupTitle: any = {}
    exportForm.fields.forEach(field => {
      groupTitle[field] = field === groupField ? `【${groupName}】` : ''
    })
    grouped.push(groupTitle)
    
    // 添加分组数据
    grouped.push(...(items as any[]))
    
    // 添加空行
    grouped.push({})
  })
  
  return grouped
}

// 计算统计信息
const calculateStatistics = (data: any[]) => {
  const completedRecords = data.filter(r => r.status === 2)
  const totalStudyTime = data.reduce((sum, r) => sum + r.studyTime, 0)
  const avgProgress = data.length > 0 ? (data.reduce((sum, r) => sum + r.progress, 0) / data.length).toFixed(1) : 0
  const completionRate = data.length > 0 ? ((completedRecords.length / data.length) * 100).toFixed(1) : 0
  
  return {
    studentName: '统计信息',
    departmentName: `总记录数: ${data.length}`,
    courseName: `完成数: ${completedRecords.length}`,
    courseType: `完成率: ${completionRate}%`,
    progress: `平均进度: ${avgProgress}%`,
    studyTime: `总时长: ${totalStudyTime}分钟`,
    status: '统计',
    startTime: '',
    lastStudyTime: '',
    completionTime: '',
    score: ''
  }
}
</script>

<style scoped>
:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}
</style>
