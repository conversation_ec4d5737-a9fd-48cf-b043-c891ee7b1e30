<template>
	<view class="notice-detail">
		<view class="notice-header">
			<text class="notice-title">{{ notice.title }}</text>
			<text class="notice-meta">发布时间：{{ notice.publishTime }}</text>
			<text class="notice-meta">发布人：{{ notice.publisher }}</text>
		</view>
		
		<view class="notice-content">
			<rich-text :nodes="notice.content"></rich-text>
		</view>
		
		<view class="notice-attachments" v-if="notice.attachments && notice.attachments.length">
			<text class="attachment-title">附件：</text>
			<view v-for="file in notice.attachments" :key="file.id" class="attachment-item" @tap="downloadFile(file)">
				<text class="file-name">{{ file.name }}</text>
				<text class="download-btn">下载</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Notice {
	id: string
	title: string
	content: string
	publishTime: string
	publisher: string
	attachments?: Array<{
		id: string
		name: string
		url: string
	}>
}

const notice = ref<Notice>({
	id: '1',
	title: '关于开展安全生产培训的通知',
	content: `
		<p>各部门、各位员工：</p>
		<p>为了进一步提高全体员工的安全生产意识，确保公司安全生产形势持续稳定，经公司研究决定，开展安全生产培训活动。</p>
		<p><strong>培训安排：</strong></p>
		<p>1. 培训时间：2024年1月20日-2月20日</p>
		<p>2. 培训方式：线上学习 + 线下考试</p>
		<p>3. 培训内容：安全生产法律法规、操作规程、应急处置等</p>
		<p>请各部门高度重视，认真组织员工参与培训。</p>
		<p style="text-align: right;">人力资源部<br/>2024年1月15日</p>
	`,
	publishTime: '2024-01-15 10:30',
	publisher: '人力资源部',
	attachments: [
		{
			id: '1',
			name: '安全生产培训资料.pdf',
			url: '/files/safety_training.pdf'
		}
	]
})

onMounted(() => {
	// 获取页面参数
	const pages = getCurrentPages() as any[]
	const currentPage = pages[pages.length - 1]
	const noticeId = currentPage.options.id
	
	if (noticeId) {
		// TODO: 根据ID加载公告详情
		console.log('Loading notice:', noticeId)
	}
})

const downloadFile = (file: any) => {
	uni.showToast({
		title: '下载功能开发中',
		icon: 'none'
	})
}
</script>

<style lang="scss" scoped>
.notice-detail {
	padding: var(--spacing-md);
	background: var(--bg-secondary);
	min-height: 100vh;
}

.notice-header {
	background: var(--bg-primary);
	padding: var(--spacing-lg);
	border-radius: var(--radius-lg);
	margin-bottom: var(--spacing-md);
}

.notice-title {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: var(--text-primary);
	line-height: 1.5;
	margin-bottom: var(--spacing-md);
}

.notice-meta {
	display: block;
	font-size: 24rpx;
	color: var(--text-secondary);
	margin-bottom: 8rpx;
}

.notice-content {
	background: var(--bg-primary);
	padding: var(--spacing-lg);
	border-radius: var(--radius-lg);
	margin-bottom: var(--spacing-md);
	line-height: 1.6;
}

.notice-attachments {
	background: var(--bg-primary);
	padding: var(--spacing-lg);
	border-radius: var(--radius-lg);
}

.attachment-title {
	font-size: 28rpx;
	font-weight: 600;
	color: var(--text-primary);
	margin-bottom: var(--spacing-md);
}

.attachment-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: var(--spacing-sm) 0;
	border-bottom: 1px solid var(--border-color);
	
	&:last-child {
		border-bottom: none;
	}
}

.file-name {
	font-size: 26rpx;
	color: var(--text-primary);
}

.download-btn {
	font-size: 24rpx;
	color: var(--primary-color);
}
</style> 