<template>
  <view class="page-container">
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">考试记录</text>
        <view class="placeholder"/>
      </view>
    </view>

    <view class="exam-records">
      <view v-if="loading" class="loading-box">
        <up-loading-icon color="#667eea" size="20"/>
        <text>加载中...</text>
      </view>
      <view v-else-if="examRecords.length === 0" class="empty-box">
        <up-empty text="空空如也"/>
      </view>
      <view v-else class="records-list">
        <view
          v-for="record in examRecords"
          :key="record.id"
          class="record-card"
          @click="viewRecordDetail(record)"
        >
          <!-- 考试标题和状态 -->
          <view class="card-header">
            <text class="exam-title">{{ record.examTitle }}</text>
            <view class="status-badge" :class="getStatusClass(record.status)">
              {{ getStatusText(record.status) }}
            </view>
          </view>

          <!-- 分数信息 -->
          <view class="score-section">
            <view class="score-display">
              <text class="score-number" :class="getScoreClass(record.score, record.totalScore)">
                {{ record.score }}
              </text>
              <text class="score-divider">/</text>
              <text class="total-score">{{ record.totalScore }}</text>
            </view>
            <view class="pass-status" :class="record.isPassed ? 'passed' : 'failed'">
              <up-icon
                :name="record.isPassed ? 'checkmark-circle' : 'close-circle'"
                :color="record.isPassed ? '#52c41a' : '#ff4d4f'"
                size="16"
              />
              <text>{{ record.isPassed ? '及格' : '不及格' }}</text>
            </view>
          </view>

          <!-- 考试信息 -->
          <view class="exam-info">
            <view class="info-item">
              <up-icon name="clock" color="#8c8c8c" size="14"/>
              <text class="info-text">{{ formatDate(record.endTime) }}</text>
            </view>
            <view class="info-item" v-if="record.duration">
              <up-icon name="clock" color="#8c8c8c" size="14"/>
              <text class="info-text">{{ formatDuration(record.duration) }}</text>
            </view>
          </view>

          <!-- 点击提示 -->
          <view class="detail-hint">
            <text>点击查看详情</text>
            <up-icon name="arrow-right" color="#bfbfbf" size="12"/>
          </view>
        </view>
        <!-- 分页加载更多提示 -->
        <view class="load-more" v-if="examRecords.length > 0">
          <view v-if="loadMoreStatus === 'loading'" class="loading-text">
            <text>加载中...</text>
          </view>
          <view v-else-if="loadMoreStatus === 'noMore'" class="nomore-text">
            <text>没有更多了</text>
          </view>
          <view v-else class="loadmore-text">
            <text>加载更多</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {getExamRecords, type ExamRecordVO} from '@/api/exam'
import {onPullDownRefresh, onReachBottom} from "@dcloudio/uni-app";

const examRecords = ref<ExamRecordVO[]>([])
const loading = ref(true)
const loadMoreStatus = ref('more') // 'more' | 'loading' | 'noMore'
const pageNum = ref(1)
const pageSize = ref(5)
const total = ref(0)
const isLoadingMore = ref(false)
const hasMore = ref(true)

const goBack = () => {
  uni.navigateBack()
}

// 格式化日期
function formatDate(dateStr: string) {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    const y = date.getFullYear()
    const m = (date.getMonth() + 1).toString().padStart(2, '0')
    const d = date.getDate().toString().padStart(2, '0')
    const h = date.getHours().toString().padStart(2, '0')
    const min = date.getMinutes().toString().padStart(2, '0')
    return `${y}-${m}-${d} ${h}:${min}`
  } catch {
    return ''
  }
}

// 格式化考试时长
function formatDuration(minutes: number) {
  if (!minutes) return ''
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  }
  return `${mins}分钟`
}

// 获取状态样式类
function getStatusClass(status: number) {
  switch (status) {
    case 0: return 'status-draft'
    case 1: return 'status-ongoing'
    case 2: return 'status-completed'
    case 3: return 'status-timeout'
    default: return 'status-draft'
  }
}

// 获取状态文本
function getStatusText(status: number) {
  switch (status) {
    case 0: return '未开始'
    case 1: return '进行中'
    case 2: return '已完成'
    case 3: return '已超时'
    default: return '未知'
  }
}

// 获取分数样式类
function getScoreClass(score: number, totalScore: number) {
  const percentage = (score / totalScore) * 100
  if (percentage >= 90) return 'score-excellent'
  if (percentage >= 80) return 'score-good'
  if (percentage >= 70) return 'score-moderate'
  if (percentage >= 60) return 'score-pass'
  return 'score-fail'
}

// 查看记录详情
function viewRecordDetail(record: ExamRecordVO) {
  // 跳转到考试详情页面
  uni.navigateTo({
    url: `/pages/exam/result?recordId=${record.id}`
  })
}

// 分页加载考试记录
async function loadExamRecords(isLoadMore = false) {
  if (isLoadMore) {
    isLoadingMore.value = true
    loadMoreStatus.value = 'loading'
  } else {
    loading.value = true
    pageNum.value = 1
    examRecords.value = []
    hasMore.value = true
  }
  try {
    const res = await getExamRecords({pageNum: pageNum.value, pageSize: pageSize.value})
    total.value = res.total || 0
    if (isLoadMore) {
      examRecords.value = examRecords.value.concat(res.list || [])
    } else {
      examRecords.value = res.list || []
    }
    if (examRecords.value.length >= total.value) {
      hasMore.value = false
      loadMoreStatus.value = 'noMore'
    } else {
      hasMore.value = true
      loadMoreStatus.value = 'more'
    }
  } catch (e) {
    uni.showToast({title: '加载失败', icon: 'none'})
    if (!isLoadMore) examRecords.value = []
  } finally {
    loading.value = false
    isLoadingMore.value = false
    uni.stopPullDownRefresh();
  }
}

// 下拉刷新
onPullDownRefresh(() => {
  pageNum.value = 1
  examRecords.value = []
  hasMore.value = true
  loadExamRecords()
})
// 上拉加载更多
onReachBottom(() => {
  if (!hasMore.value || isLoadingMore.value) return
  pageNum.value += 1
  loadExamRecords(true)
})

onMounted(() => {
  loadExamRecords()
})
</script>

<style lang="scss" scoped>
@import "@/styles/pages/exam/records.scss";
</style>
