package com.cy.education.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cy.education.model.entity.PointsRule;
import com.cy.education.model.params.PointsRuleQueryParam;
import com.cy.education.model.vo.ApiResponse;

import java.util.List;
import java.util.Map;

/**
 * 积分规则服务接口
 */
public interface PointsRuleService {

    /**
     * 分页查询积分规则
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<PointsRule> page(PointsRuleQueryParam param);
    
    /**
     * 根据ID查询规则
     *
     * @param id 规则ID
     * @return 规则对象
     */
    PointsRule getById(Integer id);
    
    /**
     * 根据规则编码查询规则
     *
     * @param code 规则编码
     * @return 规则对象
     */
    PointsRule getByCode(String code);
    
    /**
     * 创建规则
     *
     * @param rule 规则对象
     * @return 创建结果
     */
    ApiResponse<Map<String, Object>> create(PointsRule rule);

    /**
     * 更新规则
     *
     * @param id 规则ID
     * @param rule 规则对象
     * @return 更新结果
     */
    ApiResponse<Map<String, Object>> update(Integer id, PointsRule rule);

    /**
     * 删除规则
     *
     * @param id 规则ID
     * @return 删除结果
     */
    ApiResponse<Map<String, Object>> delete(Integer id);

    /**
     * 批量删除规则
     *
     * @param ids ID列表
     * @return 删除结果
     */
    ApiResponse<Map<String, Object>> batchDelete(List<Integer> ids);

    /**
     * 切换规则状态
     *
     * @param id 规则ID
     * @param status 状态
     * @return 操作结果
     */
    ApiResponse<Map<String, Object>> toggleStatus(Integer id, Integer status);
    
    /**
     * 获取规则统计数据
     *
     * @return 统计数据
     */
    Map<String, Object> getStatistics();
    
    /**
     * 增加规则使用次数
     *
     * @param id 规则ID
     * @return 是否成功
     */
    boolean incrementUsedCount(Integer id);
} 