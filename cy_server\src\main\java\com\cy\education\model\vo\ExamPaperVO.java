package com.cy.education.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 试卷视图对象
 */
@Data
public class ExamPaperVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 试卷ID
     */
    private Integer id;

    /**
     * 试卷标题
     */
    private String title;

    /**
     * 试卷描述
     */
    private String description;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 及格分数
     */
    private Integer passingScore;

    /**
     * 考试时长(分钟)
     */
    private Integer duration;

    /**
     * 是否发布
     */
    private Boolean isPublished;

    /**
     * 题目列表
     */
    private List<ExamPaperQuestionVO> questions;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人ID
     */
    private String createdBy;
} 