package com.cy.education.service.impl.points;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.points.PointsRule;
import com.cy.education.repository.PointsRuleMapper;
import com.cy.education.service.points.PointsRuleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 积分规则服务实现类
 */
@Service
public class PointsRuleServiceImpl extends ServiceImpl<PointsRuleMapper, PointsRule> implements PointsRuleService {

    @Override
    public PointsRule getById(Integer id) {
        return super.getById(id);
    }

    @Override
    public PointsRule getByCode(String code) {
        return baseMapper.findByCode(code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementUsedCount(Integer id) {
        try {
            return baseMapper.incrementUsedCount(id) > 0;
        } catch (Exception e) {
            throw new RuntimeException("增加规则使用次数失败", e);
        }
    }
}
