package com.cy.education.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * 数据库迁移工具类，用于执行SQL脚本进行数据库结构更新
 */
@Component
public class DatabaseMigrationUtil {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseMigrationUtil.class);
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 应用启动时自动执行数据库更新
     */
    @PostConstruct
    public void migrate() {
        try {
            logger.info("开始执行数据库更新...");
            
            // 执行db_update.sql脚本
            executeSqlScript("db_update.sql");
            
            logger.info("数据库更新完成");
        } catch (Exception e) {
            logger.error("数据库更新失败", e);
        }
    }
    
    /**
     * 执行SQL脚本文件
     * @param scriptName 脚本文件名
     */
    private void executeSqlScript(String scriptName) {
        try {
            ClassPathResource resource = new ClassPathResource(scriptName);
            if (!resource.exists()) {
                logger.info("脚本文件 {} 不存在，跳过执行", scriptName);
                return;
            }
            
            // 读取脚本文件内容
            String sql;
            try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
                sql = FileCopyUtils.copyToString(reader);
            }
            
            // 分割SQL语句并执行
            List<String> statements = Arrays.asList(sql.split(";"));
            for (String statement : statements) {
                String trimmedStatement = statement.trim();
                if (!trimmedStatement.isEmpty()) {
                    try {
                        logger.info("执行SQL: {}", trimmedStatement);
                        jdbcTemplate.execute(trimmedStatement);
                    } catch (Exception e) {
                        // 忽略字段已存在等可预期的错误
                        logger.warn("SQL执行警告: {}", e.getMessage());
                    }
                }
            }
        } catch (IOException e) {
            logger.error("读取SQL脚本文件失败", e);
            throw new RuntimeException("执行SQL脚本失败", e);
        }
    }
} 