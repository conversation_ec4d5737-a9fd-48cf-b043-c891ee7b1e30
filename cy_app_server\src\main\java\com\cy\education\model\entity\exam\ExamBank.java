package com.cy.education.model.entity.exam;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 题库实体类
 */
@Data
@TableName("exam_bank")
public class ExamBank implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题库ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 题库名称
     */
    private String name;

    /**
     * 题库描述
     */
    private String description;

    /**
     * 适用范围
     */
    private String scope;

    /**
     * 是否允许学员刷题(0-不允许，1-允许)
     */
    private Boolean isPracticeEnabled;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人ID
     */
    private String createdBy;

    /**
     * 是否删除(0-未删除，1-已删除)
     */
    @TableLogic
    private Integer deleted;

    /**
     * 题目总数（非数据库字段，用于前端显示）
     */
    @TableField(exist = false)
    private Integer totalQuestions;
}
