package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.exception.NotFoundException;
import com.cy.education.model.entity.StudyLog;
import com.cy.education.model.entity.StudyRecord;
import com.cy.education.model.entity.Student;

import com.cy.education.model.params.StudyRecordQueryParams;
import com.cy.education.model.vo.StudyRecordVO;
import com.cy.education.model.vo.StudyStatisticsVO;
import com.cy.education.repository.StudyLogMapper;
import com.cy.education.repository.StudyRecordMapper;
import com.cy.education.repository.StudentMapper;
import com.cy.education.service.StudyRecordService;

import com.alibaba.excel.EasyExcel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习记录服务实现类
 */
@Service
public class StudyRecordServiceImpl extends ServiceImpl<StudyRecordMapper, StudyRecord> implements StudyRecordService {
    
    @Autowired
    private StudyLogMapper studyLogMapper;

    @Autowired
    private StudentMapper studentMapper;
    
    @Override
    public List<StudyRecordVO> getUserStudyRecords(Integer userId, Integer courseId) {
        // 查询条件
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getUserId, userId);
        
        if (courseId != null) {
            queryWrapper.eq(StudyRecord::getCourseId, courseId);
        }
        
        // 按更新时间降序排序
        queryWrapper.orderByDesc(StudyRecord::getUpdatedAt);
        
        // 查询学习记录
        List<StudyRecord> records = list(queryWrapper);
        
        // 转换为VO
        return records.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<StudyRecordVO> getCourseStudyRecords(Integer courseId) {
        // 查询条件
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getCourseId, courseId);
        
        // 按更新时间降序排序
        queryWrapper.orderByDesc(StudyRecord::getUpdatedAt);
        
        // 查询学习记录
        List<StudyRecord> records = list(queryWrapper);
        
        // 转换为VO
        return records.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public List<StudyRecordVO> getLessonStudyRecords(Integer lessonId) {
        // 查询条件
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getLessonId, lessonId);
        
        // 按更新时间降序排序
        queryWrapper.orderByDesc(StudyRecord::getUpdatedAt);
        
        // 查询学习记录
        List<StudyRecord> records = list(queryWrapper);
        
        // 转换为VO
        return records.stream().map(this::convertToVO).collect(Collectors.toList());
    }
    
    @Override
    public Map<String, Object> getUserCourseProgress(Integer userId, Integer courseId) {
        // 查询用户的课程学习记录
        StudyRecordVO record = getStudentCourseRecord(userId, courseId);
        
        Map<String, Object> result = new HashMap<>();
        if (record != null) {
            result.put("progress", record.getProgress());
            result.put("completed", record.getCompleted());
            result.put("duration", record.getDuration());
            result.put("lastStudyTime", record.getLastStudyTime());
        } else {
            result.put("progress", 0);
            result.put("completed", 0);
            result.put("duration", 0);
            result.put("lastStudyTime", null);
        }
        
        // 查询已完成的课时
        List<Integer> completedLessons = new ArrayList<>();
        
        // 查询条件
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getUserId, userId);
        queryWrapper.eq(StudyRecord::getCourseId, courseId);
        queryWrapper.eq(StudyRecord::getCompleted, 1);
        queryWrapper.isNotNull(StudyRecord::getLessonId);
        
        // 查询学习记录
        List<StudyRecord> records = list(queryWrapper);
        
        if (!records.isEmpty()) {
            completedLessons = records.stream()
                    .map(StudyRecord::getLessonId)
                    .collect(Collectors.toList());
        }
        
        result.put("completedLessons", completedLessons);
        
        return result;
    }
    
    @Override
    public boolean logResourceAccess(Map<String, Object> accessLog) {
        try {
            // 创建学习日志
            StudyLog studyLog = new StudyLog();
            
            // 设置学员ID
            if (accessLog.containsKey("userId")) {
                studyLog.setUserId((Integer) accessLog.get("userId"));
            }
            
            // 设置课程ID
            if (accessLog.containsKey("courseId")) {
                studyLog.setCourseId((Integer) accessLog.get("courseId"));
            }
            
            // 设置课时ID
            if (accessLog.containsKey("lessonId")) {
                studyLog.setLessonId((Integer) accessLog.get("lessonId"));
            }
            
            // 设置资源ID
            if (accessLog.containsKey("resourceId")) {
                studyLog.setResourceId((Integer) accessLog.get("resourceId"));
            }
            
            // 设置资源类型
            if (accessLog.containsKey("resourceType")) {
                studyLog.setResourceType((String) accessLog.get("resourceType"));
            }
            
            // 设置学习时长
            if (accessLog.containsKey("duration")) {
                studyLog.setDuration((Integer) accessLog.get("duration"));
            }
            
            // 设置学习进度
            if (accessLog.containsKey("progress")) {
                studyLog.setProgress((Integer) accessLog.get("progress"));
            }
            
            // 设置是否完成
            if (accessLog.containsKey("completed")) {
                studyLog.setCompleted((Integer) accessLog.get("completed"));
            }
            
            // 设置学习时间
            studyLog.setStudyTime(LocalDateTime.now());
            
            // 设置创建时间
            studyLog.setCreatedAt(LocalDateTime.now());
            
            // 保存学习日志
            studyLogMapper.insert(studyLog);
            
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public IPage<StudyRecordVO> getStudyRecordList(StudyRecordQueryParams params) {
        // 创建分页对象
        Page<StudyRecord> page = new Page<>(params.getPage(), params.getSize());

        // 构建查询条件
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(params.getStudentId() != null, StudyRecord::getUserId, params.getStudentId())
                   .eq(params.getCourseId() != null, StudyRecord::getCourseId, params.getCourseId())
                   .eq(params.getCompleted() != null, StudyRecord::getCompleted, params.getCompleted())
                   .ge(params.getStartTime() != null, StudyRecord::getCreatedAt, params.getStartTime())
                   .le(params.getEndTime() != null, StudyRecord::getCreatedAt, params.getEndTime())
                   .orderByDesc(StudyRecord::getUpdatedAt);

        // 执行分页查询
        IPage<StudyRecord> recordIPage = baseMapper.selectPage(page, queryWrapper);

        // 转换为VO
        IPage<StudyRecordVO> voIPage = recordIPage.convert(this::convertToVO);

        return voIPage;
    }
    
    @Override
    public StudyRecordVO getStudyRecordById(Integer id) {
        // 查询学习记录
        StudyRecord studyRecord = getById(id);
        if (studyRecord == null) {
            throw new NotFoundException("学习记录不存在");
        }
        
        // 转换为VO
        return convertToVO(studyRecord);
    }
    
    @Override
    @Transactional
    public StudyRecord saveStudyRecord(StudyRecord studyRecord) {
        // 查询是否存在记录
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getUserId, studyRecord.getUserId());
        
        if (studyRecord.getLessonId() != null) {
            // 课时学习记录
            queryWrapper.eq(StudyRecord::getLessonId, studyRecord.getLessonId());
        } else {
            // 课程学习记录
            queryWrapper.eq(StudyRecord::getCourseId, studyRecord.getCourseId());
            queryWrapper.isNull(StudyRecord::getLessonId);
        }
        
        StudyRecord existingRecord = getOne(queryWrapper);
        
        if (existingRecord != null) {
            // 更新现有记录
            studyRecord.setId(existingRecord.getId());
            studyRecord.setCreatedAt(existingRecord.getCreatedAt());
            studyRecord.setUpdatedAt(LocalDateTime.now());
            
            // 累加学习时长
            if (studyRecord.getDuration() != null) {
                studyRecord.setDuration(existingRecord.getDuration() + studyRecord.getDuration());
            }
            
            updateById(studyRecord);
            
            // 记录学习日志
            recordStudyLog(studyRecord);
            
            return studyRecord;
        } else {
            // 创建新记录
            LocalDateTime now = LocalDateTime.now();
            studyRecord.setCreatedAt(now);
            studyRecord.setUpdatedAt(now);
            
            save(studyRecord);
            
            // 记录学习日志
            recordStudyLog(studyRecord);
            
            return studyRecord;
        }
    }
    
    @Override
    @Transactional
    public boolean deleteStudyRecord(Integer id) {
        // 检查学习记录是否存在
        StudyRecord studyRecord = getById(id);
        if (studyRecord == null) {
            throw new NotFoundException("学习记录不存在");
        }
        
        // 删除学习记录
        return removeById(id);
    }
    
    @Override
    public StudyRecordVO getStudentCourseRecord(Integer studentId, Integer courseId) {
        // 查询学员的课程学习记录
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getUserId, studentId);
        queryWrapper.eq(StudyRecord::getCourseId, courseId);
        queryWrapper.isNull(StudyRecord::getLessonId);
        
        StudyRecord studyRecord = getOne(queryWrapper);
        if (studyRecord == null) {
            return null;
        }
        
        // 转换为VO
        return convertToVO(studyRecord);
    }
    
    @Override
    public StudyRecordVO getStudentLessonRecord(Integer studentId, Integer lessonId) {
        // 查询学员的课时学习记录
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getUserId, studentId);
        queryWrapper.eq(StudyRecord::getLessonId, lessonId);
        
        StudyRecord studyRecord = getOne(queryWrapper);
        if (studyRecord == null) {
            return null;
        }
        
        // 转换为VO
        return convertToVO(studyRecord);
    }
    
    @Override
    public StudyStatisticsVO getStudyStatistics(Integer studentId, Integer courseId) {
        StudyStatisticsVO statistics = new StudyStatisticsVO();
        
        // 查询条件
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getUserId, studentId);
        
        if (courseId != null) {
            queryWrapper.eq(StudyRecord::getCourseId, courseId);
        }
        
        // 查询学习记录
        List<StudyRecord> records = list(queryWrapper);
        
        if (records.isEmpty()) {
            // 没有学习记录
            statistics.setTotalStudyTime(0);
            statistics.setStudyCount(0);
            statistics.setCompletedLessons(new ArrayList<>());
            statistics.setStudyTrend(new ArrayList<>());
            return statistics;
        }
        
        // 计算总学习时长
        int totalTime = records.stream().mapToInt(StudyRecord::getDuration).sum();
        statistics.setTotalStudyTime(totalTime);
        
        // 获取最后学习时间
        LocalDateTime lastStudyTime = records.stream()
                .map(StudyRecord::getLastStudyTime)
                .filter(Objects::nonNull)
                .max(LocalDateTime::compareTo)
                .orElse(null);
        statistics.setLastStudyTime(lastStudyTime);
        
        // 获取已完成课时列表
        List<Integer> completedLessons = records.stream()
                .filter(r -> r.getLessonId() != null && r.getCompleted() != null && r.getCompleted() == 1)
                .map(StudyRecord::getLessonId)
                .collect(Collectors.toList());
        statistics.setCompletedLessons(completedLessons);
        
        // 统计学习次数
        statistics.setStudyCount(records.size());
        
        // 获取学习趋势数据
        List<Map<String, Object>> studyTrend = studyLogMapper.getStudyTimeTrend(studentId, 30);
        statistics.setStudyTrend(studyTrend);
        
        return statistics;
    }
    
    @Override
    public List<Map<String, Object>> getDepartmentStatistics() {
        return baseMapper.getDepartmentStatistics();
    }
    
    @Override
    public List<Map<String, Object>> getActiveStudents(Integer limit) {
        return baseMapper.getActiveStudents(limit);
    }

    @Override
    public byte[] exportStudyRecords(StudyRecordQueryParams params) {
        // 查询学习记录数据
        IPage<StudyRecordVO> page = getStudyRecordList(params);
        List<StudyRecordVO> records = page.getRecords();

        // 这里应该使用Excel导出工具类，暂时返回空字节数组
        // 实际项目中需要集成Apache POI或EasyExcel来生成Excel文件
        try {
            // 模拟Excel导出
            StringBuilder csvContent = new StringBuilder();
            csvContent.append("学员姓名,部门,课程名称,课时名称,学习进度,学习时长,完成状态,最后学习时间\n");

            for (StudyRecordVO record : records) {
                csvContent.append(record.getStudentName()).append(",")
                         .append(record.getDepartmentName()).append(",")
                         .append(record.getCourseName()).append(",")
                         .append(record.getLessonName() != null ? record.getLessonName() : "整体课程").append(",")
                         .append(record.getProgress()).append("%,")
                         .append(record.getDuration()).append("秒,")
                         .append(record.getCompleted() == 1 ? "已完成" : "学习中").append(",")
                         .append(record.getLastStudyTime() != null ? record.getLastStudyTime().toString() : "").append("\n");
            }

            return csvContent.toString().getBytes("UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
            return new byte[0];
        }
    }
    
    /**
     * 记录学习日志
     * @param studyRecord 学习记录
     */
    private void recordStudyLog(StudyRecord studyRecord) {
        StudyLog studyLog = new StudyLog();
        studyLog.setUserId(studyRecord.getUserId());
        studyLog.setCourseId(studyRecord.getCourseId());
        studyLog.setLessonId(studyRecord.getLessonId());
        studyLog.setDuration(studyRecord.getDuration() != null ? studyRecord.getDuration() : 0);
        studyLog.setProgress(studyRecord.getProgress());
        studyLog.setCompleted(studyRecord.getCompleted());
        studyLog.setStudyTime(LocalDateTime.now());
        studyLog.setCreatedAt(LocalDateTime.now());
        
        studyLogMapper.insert(studyLog);
    }
    
    /**
     * 将实体转换为VO
     * @param studyRecord 学习记录实体
     * @return 学习记录VO
     */
    private StudyRecordVO convertToVO(StudyRecord studyRecord) {
        StudyRecordVO vo = new StudyRecordVO();
        BeanUtils.copyProperties(studyRecord, vo);

        // 查询学员信息
        if (studyRecord.getUserId() != null) {
            Student student = studentMapper.selectById(studyRecord.getUserId());
            if (student != null) {
                vo.setStudentName(student.getName());

                // 设置部门信息（暂时使用ID，后续完善）
                if (student.getDepartmentId() != null) {
                    vo.setDepartmentName("部门" + student.getDepartmentId());
                } else {
                    vo.setDepartmentName("未分配部门");
                }
            } else {
                vo.setStudentName("未知学员");
                vo.setDepartmentName("未知部门");
            }
        } else {
            vo.setStudentName("未知学员");
            vo.setDepartmentName("未知部门");
        }

        // 设置课程名称（暂时使用ID，后续可以添加课程查询）
        vo.setCourseName("课程" + studyRecord.getCourseId());

        return vo;
    }

    @Override
    public void exportStudyRecords(Map<String, Object> params, HttpServletResponse response) throws IOException {
        // 构建查询条件
        String keyword = (String) params.get("keyword");
        Integer courseId = null;
        Object courseIdObj = params.get("courseId");
        if (courseIdObj != null) {
            if (courseIdObj instanceof Integer) {
                courseId = (Integer) courseIdObj;
            } else if (courseIdObj instanceof String && !((String) courseIdObj).isEmpty()) {
                try {
                    courseId = Integer.parseInt((String) courseIdObj);
                } catch (NumberFormatException e) {
                    // 忽略无效的courseId
                }
            }
        }

        String format = (String) params.getOrDefault("format", "xlsx");
        @SuppressWarnings("unchecked")
        List<String> fields = (List<String>) params.get("fields");
        String range = (String) params.getOrDefault("range", "all");
        @SuppressWarnings("unchecked")
        List<Integer> selectedIds = (List<Integer>) params.get("selectedIds");

        // 查询学习记录数据
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(keyword)) {
            // 这里可以根据需要添加关键词搜索逻辑
        }
        if (courseId != null) {
            queryWrapper.eq(StudyRecord::getCourseId, courseId);
        }
        if ("selected".equals(range) && selectedIds != null && !selectedIds.isEmpty()) {
            queryWrapper.in(StudyRecord::getId, selectedIds);
        }

        List<StudyRecord> records = list(queryWrapper);

        // 转换为导出数据
        List<Map<String, Object>> exportData = new ArrayList<>();
        for (StudyRecord record : records) {
            Map<String, Object> data = new HashMap<>();
            data.put("学员姓名", "学员" + record.getUserId()); // 简化处理
            data.put("课程名称", "课程" + record.getCourseId()); // 简化处理
            data.put("学习进度", record.getProgress() != null ? record.getProgress() + "%" : "0%");
            data.put("学习时长", record.getDuration() != null ? record.getDuration() + "分钟" : "0分钟");
            data.put("完成状态", record.getCompleted() != null && record.getCompleted() == 1 ? "已完成" : "未完成");
            data.put("开始时间", record.getCreatedAt() != null ? record.getCreatedAt().toString() : "");
            data.put("最后学习时间", record.getUpdatedAt() != null ? record.getUpdatedAt().toString() : "");
            exportData.add(data);
        }

        // 设置响应头
        String fileName = URLEncoder.encode("学习记录_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + "." + format, StandardCharsets.UTF_8.toString());
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        // 使用EasyExcel写入
        try {
            EasyExcel.write(response.getOutputStream())
                    .head(createHeaders(fields))
                    .sheet("学习记录")
                    .doWrite(exportData);
        } catch (Exception e) {
            throw new IOException("导出失败", e);
        }
    }

    private List<List<String>> createHeaders(List<String> fields) {
        String[] allHeaders = {"学员姓名", "课程名称", "学习进度", "学习时长", "完成状态", "开始时间", "最后学习时间"};
        String[] selectedHeaders = fields != null && !fields.isEmpty() ?
            fields.toArray(new String[0]) : allHeaders;

        List<List<String>> headers = new ArrayList<>();
        for (String header : selectedHeaders) {
            List<String> head = new ArrayList<>();
            head.add(header);
            headers.add(head);
        }
        return headers;
    }

    @Override
    public List<Map<String, Object>> getDepartmentStudyStats() {
        List<Map<String, Object>> stats = new ArrayList<>();

        // 查询所有学习记录，按部门分组统计
        List<StudyRecord> allRecords = list();

        // 按用户ID分组，获取每个用户的学习情况
        Map<Integer, List<StudyRecord>> recordsByUser = allRecords.stream()
            .collect(Collectors.groupingBy(StudyRecord::getUserId));

        // 按部门统计
        Map<String, Map<String, Object>> departmentStatsMap = new HashMap<>();

        for (Map.Entry<Integer, List<StudyRecord>> entry : recordsByUser.entrySet()) {
            Integer userId = entry.getKey();
            List<StudyRecord> userRecords = entry.getValue();

            // 查询用户信息
            Student student = studentMapper.selectById(userId);
            if (student == null) continue;

            String departmentName = "部门" + (student.getDepartmentId() != null ? student.getDepartmentId() : "未知");

            // 初始化部门统计
            departmentStatsMap.computeIfAbsent(departmentName, k -> {
                Map<String, Object> deptStats = new HashMap<>();
                deptStats.put("name", k);
                deptStats.put("totalStudents", 0);
                deptStats.put("activeStudents", 0);
                deptStats.put("totalStudyTime", 0L);
                deptStats.put("completionRate", 0.0);
                return deptStats;
            });

            Map<String, Object> deptStats = departmentStatsMap.get(departmentName);

            // 更新统计数据
            deptStats.put("totalStudents", (Integer) deptStats.get("totalStudents") + 1);

            // 计算用户学习时长
            long userStudyTime = userRecords.stream()
                .mapToLong(r -> r.getDuration() != null ? r.getDuration() : 0)
                .sum();
            deptStats.put("totalStudyTime", (Long) deptStats.get("totalStudyTime") + userStudyTime);

            // 判断是否为活跃用户（最近7天有学习记录）
            boolean isActive = userRecords.stream()
                .anyMatch(r -> r.getUpdatedAt() != null &&
                    r.getUpdatedAt().isAfter(LocalDateTime.now().minusDays(7)));
            if (isActive) {
                deptStats.put("activeStudents", (Integer) deptStats.get("activeStudents") + 1);
            }

            // 计算完成率（已完成的记录数 / 总记录数）
            long completedCount = userRecords.stream()
                .filter(r -> r.getCompleted() != null && r.getCompleted() == 1)
                .count();
            double completionRate = userRecords.size() > 0 ?
                (double) completedCount / userRecords.size() * 100 : 0;

            // 更新部门完成率（取平均值）
            int totalStudents = (Integer) deptStats.get("totalStudents");
            double currentRate = (Double) deptStats.get("completionRate");
            double newRate = (currentRate * (totalStudents - 1) + completionRate) / totalStudents;
            deptStats.put("completionRate", Math.round(newRate * 10) / 10.0);
        }

        stats.addAll(departmentStatsMap.values());
        return stats;
    }
}