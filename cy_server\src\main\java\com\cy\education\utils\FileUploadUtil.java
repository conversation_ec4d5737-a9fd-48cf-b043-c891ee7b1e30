package com.cy.education.utils;

import com.cy.education.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传工具类
 */
@Slf4j
@Component
public class FileUploadUtil {
    
    @Value("${file.upload.path:/tmp/uploads}")
    private String uploadPath;
    
    @Value("${file.upload.domain:http://localhost:8080}")
    private String domain;
    
    // 允许的图片格式
    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );
    
    // 最大文件大小 2MB
    private static final long MAX_FILE_SIZE = 2 * 1024 * 1024;
    
    /**
     * 上传头像
     *
     * @param file 文件
     * @return 文件访问URL
     */
    public String uploadAvatar(MultipartFile file) {
        log.info("开始上传头像，文件名: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());
        log.info("配置的上传路径: {}", uploadPath);
        
        // 验证文件
        validateFile(file);
        
        // 创建上传目录
        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String avatarDir = uploadPath + "/avatar/" + datePath;
        log.info("目标上传目录: {}", avatarDir);
        
        createDirectoryIfNotExists(avatarDir);
        
        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String fileName = UUID.randomUUID().toString() + extension;
        
        // 保存文件
        try {
            Path filePath = Paths.get(avatarDir, fileName);
            log.info("完整文件路径: {}", filePath.toAbsolutePath());
            
            Files.copy(file.getInputStream(), filePath);
            
            // 验证文件是否保存成功
            File savedFile = filePath.toFile();
            if (savedFile.exists()) {
                log.info("文件保存成功，文件大小: {} bytes", savedFile.length());
            } else {
                log.error("文件保存失败，文件不存在");
                throw new BusinessException("文件保存失败");
            }
            
            // 返回访问URL
            String fileUrl = "/uploads/avatar/" + datePath + "/" + fileName;
            log.info("文件上传成功，返回URL: {}", fileUrl);
            return fileUrl;
            
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }
        
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("文件大小不能超过2MB");
        }
        
        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_IMAGE_TYPES.contains(contentType.toLowerCase())) {
            throw new BusinessException("只支持上传图片文件(jpg, png, gif, webp)");
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }
    
    /**
     * 创建目录
     */
    private void createDirectoryIfNotExists(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (!created) {
                throw new BusinessException("创建上传目录失败");
            }
        }
    }
} 