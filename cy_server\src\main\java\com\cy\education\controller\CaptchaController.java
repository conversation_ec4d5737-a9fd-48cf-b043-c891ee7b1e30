package com.cy.education.controller;

import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.CaptchaResponseVO;
import com.cy.education.service.CaptchaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 验证码控制器
 */
@Api(tags = "验证码接口")
@RestController
@RequestMapping("/captcha")
public class CaptchaController {

    @Autowired
    private CaptchaService captchaService;

    /**
     * 生成验证码
     */
    @ApiOperation("生成验证码")
    @GetMapping("/generate")
    public ApiResponse<CaptchaResponseVO> generateCaptcha() {
        CaptchaResponseVO captcha = captchaService.generateCaptcha();
        return ApiResponse.success(captcha);
    }
} 