<template>
  <div class="comment-tree">
    <div v-if="loading" class="comment-loading">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else-if="comments.length === 0" class="no-comments">
      暂无评论
    </div>
    <div v-else class="comment-list">
      <template v-for="comment in comments" :key="comment.id">
        <CommentItem :comment="comment" />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getPostComments, type Comment } from '@/api/forum'
import CommentItem from './CommentItem.vue'

const props = defineProps<{
  postId: number
}>()

const loading = ref(false)
const comments = ref<Comment[]>([])

// 加载评论列表
const loadComments = async () => {
  loading.value = true
  try {
    const res = await getPostComments(props.postId)
    comments.value = res
  } catch (error) {
    console.error('获取评论失败:', error)
    ElMessage.error('获取评论失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadComments()
})
</script>

<style scoped>
.comment-tree {
  width: 100%;
  padding: 16px;
}

.comment-loading {
  padding: 20px 0;
}

.no-comments {
  text-align: center;
  color: #909399;
  padding: 40px 0;
  font-size: 14px;
}

.comment-list {
  margin-top: 16px;
}
</style> 