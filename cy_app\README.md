# chengyuan 前端说明书

## 学习中心（study）

### 功能简介

- 展示课程列表，支持分页加载、下拉加载更多
- 支持课程名称关键词搜索
- 课程无分类，所有课程统一展示
- 点击课程可进入课程详情页

### 主要API

#### 1. 获取课程分页列表

- 方法：`getCourseList(params)`
- 路径：`/courses`
- 参数：
    - `pageNum`（number，必填）：页码，从1开始
    - `pageSize`（number，必填）：每页条数
    - `name`（string，可选）：课程名称关键词（用于搜索）
- 返回值：
    - `list`：课程数组
    - `total`：总课程数
    - `pageNum`：当前页码
    - `pageSize`：每页条数

#### 2. 获取课程详情

- 方法：`getCourseById(id)`
- 路径：`/courses/{id}`
- 参数：
    - `id`（number，必填）：课程ID
- 返回值：课程对象

#### 课程对象字段

- `id`：课程ID
- `name`：课程名称
- `description`：课程简介
- `coverImageUrl`：课程封面图片
- `status`：课程状态
- `structure`：课程结构（JSON字符串）
- `creatorId`：创建人ID
- `createTime`：创建时间
- `updateTime`：更新时间

### 页面交互说明

- 页面加载时自动请求第一页课程数据
- 向下滚动点击“加载更多”按钮，分页获取后续课程
- 在顶部搜索框输入关键词，自动重置为第一页并搜索
- 课程卡片展示课程封面、名称，点击进入详情
- 无课程时显示空状态

### 代码结构

- 课程API封装：`src/api/course.ts`
- 页面逻辑：`src/pages/study/index.vue`

---

如需扩展课程详情、学习进度等功能，请参考本文件继续补充说明。 
