<template>
  <div>
    <!-- Search and Action Bar -->
    <div class="filter-container">
      <el-input v-model="query.name" placeholder="文章标题" style="width: 200px;" class="filter-item" @keyup.enter="handleFilter" />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-edit" @click="handleCreate">新增文章</el-button>
    </div>

    <!-- Table -->
    <el-table :data="list" v-loading="loading" border fit highlight-current-row style="width: 100%;">
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="文章标题" prop="name" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template #default="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-show="total>0" :total="total" v-model:current-page="query.page" v-model:page-size="query.size" @size-change="getList" @current-change="getList" />

    <!-- Dialog for Create/Update -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="80%" @closed="handleDialogClosed">
      <el-form :model="tempResource" label-width="120px">
        <el-form-item label="文章标题"><el-input v-model="tempResource.name" /></el-form-item>
        <el-form-item label="标签">
            <el-tag
              v-for="tag in dynamicTags"
              :key="tag"
              class="mx-1"
              closable
              :disable-transitions="false"
              @close="handleTagClose(tag)"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="InputRef"
              v-model="inputValue"
              class="ml-1 w-20"
              size="small"
              @keyup.enter="handleInputConfirm"
              @blur="handleInputConfirm"
            />
            <el-button v-else class="button-new-tag ml-1" size="small" @click="showInput">
              + New Tag
            </el-button>
        </el-form-item>
        <el-form-item label="文章内容">
          <div v-if="dialogVisible" style="border: 1px solid #ccc">
              <Toolbar
                style="border-bottom: 1px solid #ccc"
                :editor="editorRef"
                :defaultConfig="toolbarConfig"
                mode="default"
              />
              <Editor
                style="height: 500px; overflow-y: hidden;"
                v-model="tempResource.content"
                :defaultConfig="editorConfig"
                mode="default"
                @onCreated="handleCreated"
              />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, shallowRef, onBeforeUnmount, nextTick } from 'vue';
import { ElMessage, ElMessageBox, ElInput } from 'element-plus';
import { getResourceList, deleteResource, createResource, updateResource, Resource, ResourceQuery } from '@/api/resource';
import '@wangeditor/editor/dist/css/style.css' // import css
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import { createEditorConfig, createToolbarConfig } from '@/utils/editorUpload'

const list = ref<Resource[]>([]);
const total = ref(0);
const loading = ref(true);
const query = reactive<ResourceQuery>({ page: 1, size: 10, type: 'article', name: undefined });

const dialogVisible = ref(false);
const dialogStatus = ref<'create' | 'update'>('create');
const dialogTitle = computed(() => dialogStatus.value === 'create' ? '新增文章' : '编辑文章');
const tempResource = ref<Resource>({ name: '', type: 'article', content: '<p></p>', tags: '' });

// editor instance
const editorRef = shallowRef<IDomEditor | null>(null)
const toolbarConfig: Partial<IToolbarConfig> = createToolbarConfig()
const editorConfig: Partial<IEditorConfig> = createEditorConfig('请输入文章内容...')

const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor 
}

const handleDialogClosed = () => {
    const editor = editorRef.value;
    if (editor == null) return;
    editor.destroy();
    editorRef.value = null;
};

onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})

const getList = async () => {
  loading.value = true;
  try {
    const response: any = await getResourceList(query);
    list.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('获取列表失败');
  } finally {
    loading.value = false;
  }
};

const handleFilter = () => {
  query.page = 1;
  getList();
};

const resetTemp = () => {
  tempResource.value = { name: '', type: 'article', content: '<p></p>', tags: '' };
};

const handleCreate = () => {
  resetTemp();
  dialogStatus.value = 'create';
  dialogVisible.value = true;
};

const handleUpdate = (row: Resource) => {
  tempResource.value = { ...row };
  try {
    dynamicTags.value = row.tags ? JSON.parse(row.tags) : [];
  } catch(e) {
    dynamicTags.value = [];
  }
  dialogStatus.value = 'update';
  dialogVisible.value = true;
};

const createData = async () => {
  tempResource.value.tags = JSON.stringify(dynamicTags.value);
  try {
    await createResource(tempResource.value);
    dialogVisible.value = false;
    ElMessage.success('创建成功');
    getList();
  } catch (error) {
    ElMessage.error('创建失败');
  }
};

const updateData = async () => {
  if (!tempResource.value.id) return;
  tempResource.value.tags = JSON.stringify(dynamicTags.value);
  try {
    await updateResource(tempResource.value.id, tempResource.value);
    dialogVisible.value = false;
    ElMessage.success('更新成功');
    getList();
  } catch (error) {
    ElMessage.error('更新失败');
  }
};

const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个资源吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    await deleteResource(id);
    ElMessage.success('删除成功');
    getList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

// --- Tag Editing Logic ---
const inputVisible = ref(false)
const InputRef = ref<InstanceType<typeof ElInput>>()
const inputValue = ref('')
const dynamicTags = ref<string[]>([])

const handleTagClose = (tag: string) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1)
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value) {
    dynamicTags.value.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}
// --- End Tag Editing Logic ---

onMounted(() => {
  getList();
});
</script>

<style scoped>
.mx-1 {
  margin-right: 8px;
}
.w-20 {
    width: 80px;
}
.button-new-tag {
    margin-left: 8px;
}
</style> 