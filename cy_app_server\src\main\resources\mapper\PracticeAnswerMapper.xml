<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.PracticeAnswerMapper">

    <!-- 根据练习记录ID查询答案 -->
    <select id="selectByRecordId" resultType="com.cy.education.model.entity.PracticeAnswer">
        SELECT * FROM practice_answer
        WHERE record_id = #{recordId}
        ORDER BY answer_time ASC
    </select>

    <!-- 根据用户ID和题目ID查询错题记录 -->
    <select id="selectWrongAnswersByUserIdAndQuestionId" resultType="com.cy.education.model.entity.PracticeAnswer">
        SELECT pa.* FROM practice_answer pa
        INNER JOIN practice_record pr ON pa.record_id = pr.id
        WHERE pr.user_id = #{userId} AND pa.question_id = #{questionId}
        AND pa.is_correct = 0
        ORDER BY pa.answer_time DESC
    </select>

    <!-- 根据用户ID查询错题记录 -->
    <select id="selectWrongAnswersByUserId" resultType="com.cy.education.model.entity.PracticeAnswer">
        SELECT pa.* FROM practice_answer pa
        INNER JOIN practice_record pr ON pa.record_id = pr.id
        INNER JOIN (
            SELECT
                pa2.question_id,
                pr2.user_id,
                MAX(pa2.answer_time) as latest_time
            FROM practice_answer pa2
            INNER JOIN practice_record pr2 ON pa2.record_id = pr2.id
            WHERE pr2.user_id = #{userId}
            GROUP BY pa2.question_id, pr2.user_id
        ) latest ON pa.question_id = latest.question_id
                AND pr.user_id = latest.user_id
                AND pa.answer_time = latest.latest_time
        WHERE pr.user_id = #{userId} AND pa.is_correct = 0
        ORDER BY pa.answer_time DESC
    </select>

    <!-- 根据用户ID和题库ID查询错题记录 -->
    <select id="selectWrongAnswersByUserIdAndBankId" resultType="com.cy.education.model.entity.PracticeAnswer">
        SELECT pa.* FROM practice_answer pa
        INNER JOIN practice_record pr ON pa.record_id = pr.id
        INNER JOIN exam_question eq ON pa.question_id = eq.id
        INNER JOIN (
            SELECT
                pa2.question_id,
                pr2.user_id,
                MAX(pa2.answer_time) as latest_time
            FROM practice_answer pa2
            INNER JOIN practice_record pr2 ON pa2.record_id = pr2.id
            INNER JOIN exam_question eq2 ON pa2.question_id = eq2.id
            WHERE pr2.user_id = #{userId} AND eq2.bank_id = #{bankId}
            GROUP BY pa2.question_id, pr2.user_id
        ) latest ON pa.question_id = latest.question_id
                AND pr.user_id = latest.user_id
                AND pa.answer_time = latest.latest_time
        WHERE pr.user_id = #{userId} AND eq.bank_id = #{bankId} AND pa.is_correct = 0
        ORDER BY pa.answer_time DESC
    </select>

    <!-- 删除用户的错题记录 -->
    <delete id="deleteWrongAnswersByUserId">
        DELETE pa FROM practice_answer pa
        INNER JOIN practice_record pr ON pa.record_id = pr.id
        WHERE pr.user_id = #{userId} AND pa.is_correct = 0
    </delete>

    <!-- 删除用户在指定题库的错题记录 -->
    <delete id="deleteWrongAnswersByUserIdAndBankId">
        DELETE pa FROM practice_answer pa
        INNER JOIN practice_record pr ON pa.record_id = pr.id
        INNER JOIN exam_question eq ON pa.question_id = eq.id
        WHERE pr.user_id = #{userId} AND eq.bank_id = #{bankId} AND pa.is_correct = 0
    </delete>

</mapper>