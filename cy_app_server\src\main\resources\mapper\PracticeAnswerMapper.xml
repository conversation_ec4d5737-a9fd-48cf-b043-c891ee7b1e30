<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cy.education.repository.PracticeAnswerMapper">

    <!-- 根据练习记录ID查询答案 -->
    <select id="selectByRecordId" resultType="com.cy.education.model.entity.PracticeAnswer">
        SELECT * FROM practice_answer
        WHERE record_id = #{recordId}
        ORDER BY answer_time ASC
    </select>

    <!-- 根据用户ID和题目ID查询错题记录 -->
    <select id="selectWrongAnswersByUserIdAndQuestionId" resultType="com.cy.education.model.entity.PracticeAnswer">
        SELECT pa.* FROM practice_answer pa
        INNER JOIN practice_record pr ON pa.record_id = pr.id
        WHERE pr.user_id = #{userId} AND pa.question_id = #{questionId}
        AND pa.is_correct = 0
        ORDER BY pa.answer_time DESC
    </select>

    <!-- 根据用户ID查询错题记录 -->
    <select id="selectWrongAnswersByUserId" resultType="com.cy.education.model.entity.PracticeAnswer">
        SELECT DISTINCT pa.* FROM practice_answer pa
        INNER JOIN practice_record pr ON pa.record_id = pr.id
        WHERE pr.user_id = #{userId} AND pa.is_correct = 0
        ORDER BY pa.answer_time DESC
    </select>

    <!-- 根据用户ID和题库ID查询错题记录 -->
    <select id="selectWrongAnswersByUserIdAndBankId" resultType="com.cy.education.model.entity.PracticeAnswer">
        SELECT DISTINCT pa.* FROM practice_answer pa
        INNER JOIN practice_record pr ON pa.record_id = pr.id
        INNER JOIN exam_question eq ON pa.question_id = eq.id
        WHERE pr.user_id = #{userId} AND eq.bank_id = #{bankId}
        AND pa.is_correct = 0
        ORDER BY pa.answer_time DESC
    </select>

</mapper> 