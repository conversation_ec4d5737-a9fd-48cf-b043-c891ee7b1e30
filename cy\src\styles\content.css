/* Content styles for article display */
.article-content {
  line-height: 1.6;
  color: #333;
  font-size: 14px;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
  margin: 20px 0 10px 0;
  font-weight: 600;
  color: #2c3e50;
}

.article-content h1 {
  font-size: 24px;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
}

.article-content h2 {
  font-size: 20px;
}

.article-content h3 {
  font-size: 18px;
}

.article-content p {
  margin: 10px 0;
  text-align: justify;
}

.article-content ul,
.article-content ol {
  margin: 10px 0;
  padding-left: 20px;
}

.article-content li {
  margin: 5px 0;
}

.article-content blockquote {
  margin: 15px 0;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-left: 4px solid #007bff;
  color: #6c757d;
}

.article-content code {
  background-color: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.article-content pre {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  margin: 15px 0;
}

.article-content pre code {
  background: none;
  padding: 0;
}

.article-content img {
  max-width: 100%;
  height: auto;
  margin: 10px 0;
  border-radius: 5px;
}

.article-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
}

.article-content table th,
.article-content table td {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.article-content table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.article-content a {
  color: #007bff;
  text-decoration: none;
}

.article-content a:hover {
  text-decoration: underline;
}

.article-content hr {
  border: none;
  border-top: 1px solid #eee;
  margin: 20px 0;
}
