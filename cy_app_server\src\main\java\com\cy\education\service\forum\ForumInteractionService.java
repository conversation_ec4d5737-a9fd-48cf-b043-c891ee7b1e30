package com.cy.education.service.forum;

/**
 * 论坛用户交互服务接口
 */
public interface ForumInteractionService {

    /**
     * 点赞帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean likePost(Integer postId, Integer userId);

    /**
     * 取消点赞帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unlikePost(Integer postId, Integer userId);

    /**
     * 收藏帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean collectPost(Integer postId, Integer userId);

    /**
     * 取消收藏帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean uncollectPost(Integer postId, Integer userId);

    /**
     * 关注用户
     *
     * @param targetUserId 目标用户ID
     * @param userId       当前用户ID
     * @return 是否成功
     */
    boolean followUser(Integer targetUserId, Integer userId);

    /**
     * 取消关注用户
     *
     * @param targetUserId 目标用户ID
     * @param userId       当前用户ID
     * @return 是否成功
     */
    boolean unfollowUser(Integer targetUserId, Integer userId);

    /**
     * 点赞评论
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 是否成功
     */
    boolean likeComment(Integer commentId, Integer userId);

    /**
     * 取消点赞评论
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 是否成功
     */
    boolean unlikeComment(Integer commentId, Integer userId);

    /**
     * 检查用户是否点赞了帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否点赞
     */
    boolean isPostLiked(Integer postId, Integer userId);

    /**
     * 检查用户是否收藏了帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否收藏
     */
    boolean isPostCollected(Integer postId, Integer userId);

    /**
     * 检查用户是否关注了目标用户
     *
     * @param targetUserId 目标用户ID
     * @param userId       当前用户ID
     * @return 是否关注
     */
    boolean isUserFollowed(Integer targetUserId, Integer userId);

    /**
     * 检查用户是否点赞了评论
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 是否点赞
     */
    boolean isCommentLiked(Integer commentId, Integer userId);
}
