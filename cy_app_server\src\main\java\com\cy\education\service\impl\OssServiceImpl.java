package com.cy.education.service.impl;

import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.sts20150401.models.AssumeRoleResponse;
import com.aliyun.sts20150401.models.AssumeRoleResponseBody;
import com.aliyun.tea.TeaException;
import com.cy.education.config.OssConfig;
import com.cy.education.service.OssService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 阿里云OSS服务实现类
 */
@Service
public class OssServiceImpl implements OssService {

    private static final Logger logger = LoggerFactory.getLogger(OssServiceImpl.class);

    @Autowired
    private OssConfig ossConfig;

    /**
     * 初始化STS Client
     */
    private com.aliyun.sts20150401.Client createStsClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId("LTAI5t6ZP1zhKoeqX4YiASTp")
                .setAccessKeySecret("******************************");
        // Endpoint设置
        config.endpoint = "sts." + ossConfig.getRegion() + ".aliyuncs.com";
        return new com.aliyun.sts20150401.Client(config);
    }

    /**
     * 获取STS临时凭证
     */
    private AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials getCredential() throws Exception {
        com.aliyun.sts20150401.Client client = createStsClient();
        com.aliyun.sts20150401.models.AssumeRoleRequest assumeRoleRequest = new com.aliyun.sts20150401.models.AssumeRoleRequest()
                .setRoleArn("acs:ram::1312187725738307:role/chengyuanxuexi")
                .setRoleSessionName("educationSystemSession");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            AssumeRoleResponse response = client.assumeRoleWithOptions(assumeRoleRequest, runtime);
            return response.body.credentials;
        } catch (TeaException error) {
            logger.error("获取STS临时凭证失败: {}", error.getMessage());
            logger.error("诊断信息: {}", error.getData().get("Recommend"));
            throw new RuntimeException("获取STS临时凭证失败", error);
        } catch (Exception error) {
            logger.error("获取STS临时凭证异常: {}", error.getMessage());
            throw new RuntimeException("获取STS临时凭证异常", error);
        }
    }

    /**
     * 生成过期时间
     */
    private String generateExpiration(long seconds) {
        // 获取当前时间戳（以秒为单位）
        long now = Instant.now().getEpochSecond();
        // 计算过期时间的时间戳
        long expirationTime = now + seconds;
        // 将时间戳转换为Instant对象，并格式化为ISO8601格式
        Instant instant = Instant.ofEpochSecond(expirationTime);
        // 定义时区为UTC
        ZoneId zone = ZoneId.of("UTC");
        // 将 Instant 转换为 ZonedDateTime
        ZonedDateTime zonedDateTime = instant.atZone(zone);
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        // 格式化日期时间
        return zonedDateTime.format(formatter);
    }

    /**
     * HMAC-SHA256计算
     */
    private byte[] hmacsha256(byte[] key, String data) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKeySpec);
            return mac.doFinal(data.getBytes());
        } catch (Exception e) {
            throw new RuntimeException("计算HMAC-SHA256失败", e);
        }
    }

    @Override
    public Map<String, String> getPostSignature(String directory, String fileType) {
        try {
            // 获取STS临时凭证
            AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials stsCredential = getCredential();

            String accessKeyId = stsCredential.accessKeyId;
            String accessKeySecret = stsCredential.accessKeySecret;
            String securityToken = stsCredential.securityToken;
            String region = ossConfig.getRegion();
            String bucket = ossConfig.getBucket();

            // 确定上传目录
            String uploadDir = StringUtils.isNotBlank(directory) ? directory : ossConfig.getUploadDir();
            // 确保目录以/结尾
            if (!uploadDir.endsWith("/")) {
                uploadDir = uploadDir + "/";
            }

            // 获取当前日期，格式为yyyyMMdd
            LocalDate today = LocalDate.now();
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String date = today.format(dateFormatter);

            // 获取x-oss-date
            ZonedDateTime now = ZonedDateTime.now(ZoneId.of("UTC"));
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");
            String xOssDate = now.format(dateTimeFormatter);

            // 创建x-oss-credential
            String xOssCredential = accessKeyId + "/" + date + "/" + region + "/oss/aliyun_v4_request";

            // 构建policy
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> policy = new HashMap<>();
            policy.put("expiration", generateExpiration(ossConfig.getExpireTime()));

            List<Object> conditions = new ArrayList<>();

            // 添加各种条件
            conditions.add(Collections.singletonMap("bucket", bucket));

            // 添加文件大小限制
            conditions.add(Arrays.asList("content-length-range", 1, ossConfig.getMaxSize()));

            // 添加目录前缀条件
            conditions.add(Arrays.asList("starts-with", "$key", uploadDir));

            // 添加成功响应状态码
            conditions.add(Arrays.asList("eq", "$success_action_status", "200"));

            // STS Token相关
            conditions.add(Collections.singletonMap("x-oss-security-token", securityToken));

            // 签名版本和凭证
            conditions.add(Collections.singletonMap("x-oss-signature-version", "OSS4-HMAC-SHA256"));
            conditions.add(Collections.singletonMap("x-oss-date", xOssDate));
            conditions.add(Collections.singletonMap("x-oss-credential", xOssCredential));

            // 添加文件类型限制（如果指定）
            if (StringUtils.isNotBlank(fileType)) {
                conditions.add(Arrays.asList("starts-with", "$Content-Type", fileType + "/"));
            }

            policy.put("conditions", conditions);

            // 序列化为JSON并Base64编码
            String jsonPolicy = mapper.writeValueAsString(policy);
            logger.debug("Policy JSON: {}", jsonPolicy);
            String base64Policy = new String(Base64.encodeBase64(jsonPolicy.getBytes()));

            // 计算签名
            byte[] dateKey = hmacsha256(("aliyun_v4" + accessKeySecret).getBytes(), date);
            byte[] dateRegionKey = hmacsha256(dateKey, region);
            byte[] dateRegionServiceKey = hmacsha256(dateRegionKey, "oss");
            byte[] signingKey = hmacsha256(dateRegionServiceKey, "aliyun_v4_request");

            byte[] signatureBytes = hmacsha256(signingKey, base64Policy);
            String signature = BinaryUtil.toHex(signatureBytes);

            // 构建响应
            Map<String, String> response = new HashMap<>();
            response.put("accessId", accessKeyId);
            response.put("host", ossConfig.getHost());
            response.put("policy", base64Policy);
            response.put("signature", signature);
            response.put("x-oss-signature", signature);
            response.put("x_oss_date", xOssDate);
            response.put("x_oss_credential", xOssCredential);
            response.put("dir", uploadDir);
            response.put("expire", String.valueOf(ossConfig.getExpireTime()));
            response.put("security_token", securityToken);
            response.put("success_action_status", "200");
            response.put("x_oss_signature_version", "OSS4-HMAC-SHA256");
            response.put("bucket", bucket);

            return response;
        } catch (Exception e) {
            logger.error("获取OSS签名失败", e);
            throw new RuntimeException("获取OSS签名失败", e);
        }
    }

    @Override
    public String generateFileUrl(String objectName) {
        if (StringUtils.isBlank(objectName)) {
            return null;
        }

        String host = ossConfig.getHost();
        // 确保host以/结尾
        if (!host.endsWith("/")) {
            host = host + "/";
        }

        // 去掉objectName开头的/（如果有）
        if (objectName.startsWith("/")) {
            objectName = objectName.substring(1);
        }

        return host + objectName;
    }

    @Override
    public boolean deleteFile(String objectName) {
        // 这里可以实现通过OSS SDK删除文件的逻辑
        // 由于我们使用的是STS临时凭证直传模式，可以考虑使用STS临时凭证来操作删除
        // 为简化实现，这里暂不实现具体删除逻辑
        logger.warn("删除OSS文件功能未实现: {}", objectName);
        return false;
    }
}
