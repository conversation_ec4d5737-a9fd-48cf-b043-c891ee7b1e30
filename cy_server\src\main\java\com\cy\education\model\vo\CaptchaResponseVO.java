package com.cy.education.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 验证码响应VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaptchaResponseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 验证码ID
     */
    private String captchaId;

    /**
     * 验证码图片（Base64格式）
     */
    private String captchaImage;

    /**
     * 过期时间（毫秒）
     */
    private Long expireTime;
} 