package com.cy.education.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.PointsRecord;
import com.cy.education.model.entity.PointsRule;
import com.cy.education.model.entity.Student;
import com.cy.education.model.params.PointsAdjustParam;
import com.cy.education.model.params.PointsQueryParam;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.PointsStatisticsVO;
import com.cy.education.repository.PointsRecordMapper;
import com.cy.education.service.PointsRecordService;
import com.cy.education.service.PointsRuleService;
import com.cy.education.service.StudentService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 积分记录服务实现类
 */
@Service
public class PointsRecordServiceImpl extends ServiceImpl<PointsRecordMapper, PointsRecord> implements PointsRecordService {

    private static final Logger log = LoggerFactory.getLogger(PointsRecordServiceImpl.class);

    @Autowired
    private PointsRuleService pointsRuleService;

    @Autowired
    private StudentService studentService;

    @Override
    public IPage<PointsRecord> page(PointsQueryParam param) {
        // 创建分页对象
        Page<PointsRecord> page = new Page<>(param.getPage(), param.getLimit());

        // 构建查询条件
        LambdaQueryWrapper<PointsRecord> wrapper = new LambdaQueryWrapper<>();

        // 用户ID条件
        if (param.getUserId() != null) {
            wrapper.eq(PointsRecord::getUserId, param.getUserId());
        }

        // 用户名条件（这个条件由新的联表查询处理，这里不再需要）

        // 积分类型条件
        if (StringUtils.isNotBlank(param.getType())) {
            wrapper.eq(PointsRecord::getType, param.getType());
        }

        // 日期范围条件
        if (StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate())) {
            LocalDateTime startDateTime = LocalDate.parse(param.getStartDate()).atStartOfDay();
            LocalDateTime endDateTime = LocalDate.parse(param.getEndDate()).atTime(23, 59, 59);
            wrapper.between(PointsRecord::getCreatedAt, startDateTime, endDateTime);
        } else if (StringUtils.isNotBlank(param.getStartDate())) {
            LocalDateTime startDateTime = LocalDate.parse(param.getStartDate()).atStartOfDay();
            wrapper.ge(PointsRecord::getCreatedAt, startDateTime);
        } else if (StringUtils.isNotBlank(param.getEndDate())) {
            LocalDateTime endDateTime = LocalDate.parse(param.getEndDate()).atTime(23, 59, 59);
            wrapper.le(PointsRecord::getCreatedAt, endDateTime);
        }

        // 排序
        if (StringUtils.isNotBlank(param.getSortBy())) {
            boolean isAsc = "asc".equalsIgnoreCase(param.getSortOrder());
            switch (param.getSortBy()) {
                case "createdAt":
                    wrapper.orderBy(true, isAsc, PointsRecord::getCreatedAt);
                    break;
                case "points":
                    wrapper.orderBy(true, isAsc, PointsRecord::getPoints);
                    break;
                default:
                    wrapper.orderBy(true, false, PointsRecord::getCreatedAt);
                    break;
            }
        } else {
            // 默认按创建时间降序
            wrapper.orderByDesc(PointsRecord::getCreatedAt);
        }

        // 使用新的联表查询方法
        try {
            // 如果有userName参数，添加用户名过滤条件（需要在SQL中处理）
            if (StringUtils.isNotBlank(param.getUserName())) {
                wrapper.apply("s.name like concat('%', {0}, '%')", param.getUserName());
            }

            return baseMapper.selectPointsRecordPage(page, wrapper);
        } catch (Exception e) {
            log.error("联表查询积分记录失败", e);
            // 降级使用原方法(修复无限递归问题)
            log.warn("降级使用父类的page方法查询");
            return super.page(page, wrapper);
        }
    }

    @Override
    public Integer getUserBalance(Integer userId) {
        // 获取用户最新的积分记录
        PointsRecord latestRecord = baseMapper.getLatestRecord(userId);

        // 如果没有记录，则积分为0
        return latestRecord != null ? latestRecord.getBalance() : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> adjustPoints(PointsAdjustParam param) {
        // 获取用户当前积分余额
        Integer currentBalance = getUserBalance(param.getUserId());

        // 计算新的积分余额
        Integer newBalance = currentBalance + param.getPoints();

        // 如果新余额小于0，则不允许调整
        if (newBalance < 0) {
            return ApiResponse.error("积分调整后余额不能小于0");
        }

        // 创建积分记录
        String operator = StringUtils.isBlank(param.getOperator()) ? "admin" : param.getOperator();
        String description = StringUtils.isBlank(param.getDescription()) ?
                             (param.getPoints() > 0 ? "管理员增加积分" : "管理员减少积分") :
                             param.getDescription();

        boolean success = recordPointsChange(
            param.getUserId(),
            param.getPoints(),
            "admin_adjust",
            description,
            null,
            null,
            operator
        );

        if (success) {
            return ApiResponse.success(Map.of(
                "userId", param.getUserId(),
                "points", param.getPoints(),
                "balance", newBalance,
                "success", true
            ));
        }
        return ApiResponse.error("积分调整失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse<Map<String, Object>> batchAdjustPoints(List<PointsAdjustParam> params) {
        if (params == null || params.isEmpty()) {
            return ApiResponse.error("没有提供调整参数");
        }

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        params.forEach(param -> {
            ApiResponse response = adjustPoints(param);
            if (response.getCode() == 200) {
                successCount.incrementAndGet();
            } else {
                failCount.incrementAndGet();
            }
        });

        return ApiResponse.success(Map.of(
            "totalCount", params.size(),
            "successCount", successCount.get(),
            "failCount", failCount.get()
        ));
    }

    @Override
    public PointsStatisticsVO getStatistics() {
        try {
            // 获取积分统计数据
            Map<String, Object> statistics = baseMapper.getPointsStatistics();

            // 构建统计VO
            PointsStatisticsVO vo = new PointsStatisticsVO();

            // 设置基本统计信息
            vo.setTotalUsers(statistics.get("totalUsers") != null ?
                            Integer.parseInt(statistics.get("totalUsers").toString()) : 0);

            Integer earnedPoints = statistics.get("earnedPoints") != null ?
                                Integer.parseInt(statistics.get("earnedPoints").toString()) : 0;

            Integer spentPoints = statistics.get("spentPoints") != null ?
                                Integer.parseInt(statistics.get("spentPoints").toString()) : 0;

            vo.setTotalPoints(earnedPoints - spentPoints);

            // 计算平均积分
            vo.setAveragePoints(vo.getTotalUsers() > 0 ?
                            (double) vo.getTotalPoints() / vo.getTotalUsers() : 0.0);

            // 查询月度积分变化
            QueryWrapper<PointsRecord> monthlyWrapper = new QueryWrapper<>();
            monthlyWrapper.select(
                    "DATE_FORMAT(created_at, '%Y-%m') as month",
                    "SUM(CASE WHEN points > 0 THEN points ELSE 0 END) as earned",
                    "SUM(CASE WHEN points < 0 THEN ABS(points) ELSE 0 END) as spent"
                )
                .groupBy("month")
                .orderByAsc("month")
                .last("LIMIT 12");

            List<Map<String, Object>> monthlyData = listMaps(monthlyWrapper);

            // 确保每条记录中有所有必要的字段，符合前端期望的结构
            List<Map<String, Object>> processedMonthlyData = new ArrayList<>();
            for (Map<String, Object> data : monthlyData) {
                Map<String, Object> item = new HashMap<>();
                item.put("month", data.get("month"));
                item.put("earned", data.get("earned") != null ? data.get("earned") : 0);
                // 注意将spent字段映射为consumed，符合前端接口期望
                item.put("consumed", data.get("spent") != null ? data.get("spent") : 0);
                processedMonthlyData.add(item);
            }
            vo.setMonthlyPointsChange(processedMonthlyData);

            // 查询积分排名前10的用户
            List<Map<String, Object>> topUsersData;
            try {
                topUsersData = baseMapper.getTopUsersWithPoints();
                log.info("成功获取到积分排名前10的用户，数量：{}", topUsersData.size());
            } catch (Exception e) {
                log.error("获取积分排名失败，使用原始查询", e);
                // 降级使用简单SQL
                topUsersData = baseMapper.selectMaps(new QueryWrapper<PointsRecord>()
                    .select("user_id as userId", "balance as points")
                    .groupBy("user_id")
                    .orderByDesc("balance")
                    .last("LIMIT 10"));
            }

            // 标准化输出格式
            List<Map<String, Object>> processedTopUsers = new ArrayList<>();
            for (Map<String, Object> data : topUsersData) {
                Map<String, Object> item = new HashMap<>();
                item.put("userId", data.get("userId").toString());
                item.put("userName", data.get("userName") != null ? data.get("userName").toString() : "未知用户");
                item.put("points", data.get("points") != null ? Integer.parseInt(data.get("points").toString()) : 0);
                processedTopUsers.add(item);
            }
            vo.setTopUsers(processedTopUsers);

            // 查询积分分布
            QueryWrapper<PointsRecord> distributionWrapper = new QueryWrapper<>();
            distributionWrapper.select(
                    "CASE " +
                    "WHEN balance < 100 THEN '0-99' " +
                    "WHEN balance BETWEEN 100 AND 499 THEN '100-499' " +
                    "WHEN balance BETWEEN 500 AND 999 THEN '500-999' " +
                    "WHEN balance BETWEEN 1000 AND 4999 THEN '1000-4999' " +
                    "ELSE '5000+' END as range",
                    "COUNT(*) as count"
                )
                .apply("id IN (SELECT MAX(id) FROM points_record GROUP BY user_id)")
                .groupBy("range")
                .orderByAsc("range");

            List<Map<String, Object>> distributionData = listMaps(distributionWrapper);

            // 确保distribution数据正确
            List<Map<String, Object>> processedDistribution = new ArrayList<>();
            for (Map<String, Object> data : distributionData) {
                Map<String, Object> item = new HashMap<>();
                item.put("range", data.get("range"));
                item.put("count", data.get("count") != null ? Integer.parseInt(data.get("count").toString()) : 0);
                processedDistribution.add(item);
            }
            vo.setPointsDistribution(processedDistribution);

            log.info("积分统计数据生成成功");
            return vo;
        } catch (Exception e) {
            log.error("获取积分统计数据失败", e);
            // 返回默认值，避免前端显示错误
            PointsStatisticsVO defaultVO = new PointsStatisticsVO();
            defaultVO.setTotalUsers(0);
            defaultVO.setTotalPoints(0);
            defaultVO.setAveragePoints(0.0);
            defaultVO.setMonthlyPointsChange(new ArrayList<>());
            defaultVO.setTopUsers(new ArrayList<>());
            defaultVO.setPointsDistribution(new ArrayList<>());
            return defaultVO;
        }
    }

    @Override
    public Map<String, Object> getUserPointsTrend(Integer userId, String period) {
        Map<String, Object> result = new HashMap<>();

        // 根据时间段确定日期格式和SQL查询
        String format;
        String sql;
        LocalDateTime startTime;
        LocalDateTime endTime = LocalDateTime.now();

        switch (period.toLowerCase()) {
            case "week":
                // 最近7天，按天统计
                format = "%Y-%m-%d";
                startTime = endTime.minusDays(6);
                sql = "DATE_FORMAT(created_at, '" + format + "') as date";
                break;
            case "month":
                // 最近30天，按天统计
                format = "%Y-%m-%d";
                startTime = endTime.minusDays(29);
                sql = "DATE_FORMAT(created_at, '" + format + "') as date";
                break;
            case "year":
                // 最近12个月，按月统计
                format = "%Y-%m";
                startTime = endTime.minusMonths(11);
                sql = "DATE_FORMAT(created_at, '" + format + "') as date";
                break;
            default:
                // 所有时间，按月统计
                format = "%Y-%m";
                startTime = endTime.minusYears(5); // 默认查询5年内的数据
                sql = "DATE_FORMAT(created_at, '" + format + "') as date";
                break;
        }

        // 执行查询
        QueryWrapper<PointsRecord> wrapper = new QueryWrapper<>();
        wrapper.select(
                sql,
                "SUM(CASE WHEN points > 0 THEN points ELSE 0 END) as earned",
                "SUM(CASE WHEN points < 0 THEN ABS(points) ELSE 0 END) as spent",
                "SUM(points) as net"
            )
            .eq("user_id", userId)
            .between("created_at", startTime, endTime)
            .groupBy("date")
            .orderByAsc("date");

        List<Map<String, Object>> trendData = listMaps(wrapper);

        // 填充缺失的日期
        List<Map<String, Object>> filledData = new ArrayList<>();

        if ("week".equalsIgnoreCase(period) || "month".equalsIgnoreCase(period)) {
            // 按天填充
            LocalDate current = startTime.toLocalDate();
            LocalDate end = endTime.toLocalDate();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            while (!current.isAfter(end)) {
                String dateStr = current.format(formatter);
                boolean found = false;

                for (Map<String, Object> data : trendData) {
                    if (dateStr.equals(data.get("date"))) {
                        filledData.add(data);
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    Map<String, Object> emptyData = new HashMap<>();
                    emptyData.put("date", dateStr);
                    emptyData.put("earned", 0);
                    emptyData.put("spent", 0);
                    emptyData.put("net", 0);
                    filledData.add(emptyData);
                }

                current = current.plusDays(1);
            }
        } else {
            // 按月填充
            LocalDate current = startTime.toLocalDate().withDayOfMonth(1);
            LocalDate end = endTime.toLocalDate().withDayOfMonth(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

            while (!current.isAfter(end)) {
                String dateStr = current.format(formatter);
                boolean found = false;

                for (Map<String, Object> data : trendData) {
                    if (dateStr.equals(data.get("date"))) {
                        filledData.add(data);
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    Map<String, Object> emptyData = new HashMap<>();
                    emptyData.put("date", dateStr);
                    emptyData.put("earned", 0);
                    emptyData.put("spent", 0);
                    emptyData.put("net", 0);
                    filledData.add(emptyData);
                }

                current = current.plusMonths(1);
            }
        }

        result.put("userId", userId);
        result.put("period", period);
        result.put("data", filledData);

        // 计算总计和当前积分
        int totalEarned = 0;
        int totalSpent = 0;
        int currentBalance = getUserBalance(userId);

        for (Map<String, Object> data : trendData) {
            totalEarned += data.get("earned") != null ?
                          Integer.parseInt(data.get("earned").toString()) : 0;
            totalSpent += data.get("spent") != null ?
                         Integer.parseInt(data.get("spent").toString()) : 0;
        }

        result.put("totalEarned", totalEarned);
        result.put("totalSpent", totalSpent);
        result.put("currentBalance", currentBalance);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordPointsChange(Integer userId, Integer points, String type,
                                    String description, Integer relatedId,
                                    String relatedType, String operator) {
        try {
            // 获取学生信息以取得最准确的当前积分值
            Student student = null;
            try {
                // 先尝试从studentService获取学生信息和积分值
                student = studentService.getStudentById(userId);
            } catch (Exception e) {
                log.warn("获取学生信息失败，将使用积分记录表的余额: userId={}", userId, e);
            }

            // 获取用户当前积分余额（优先使用学生表中的积分值）
            Integer currentBalance;
            if (student != null && student.getPoints() != null) {
                currentBalance = student.getPoints();
                log.info("使用学生表中的积分值: userId={}, points={}", userId, currentBalance);
            } else {
                // 降级使用积分记录表的最新余额
                currentBalance = getUserBalance(userId);
                log.info("使用积分记录表的最新余额: userId={}, points={}", userId, currentBalance);
            }

            // 计算新的积分余额
            Integer newBalance = currentBalance + points;

            // 如果新余额小于0，则不允许记录
            if (newBalance < 0) {
                throw new RuntimeException("积分变动后余额不能小于0");
            }

            // 创建积分记录
            PointsRecord record = new PointsRecord();
            record.setUserId(userId);
            record.setPoints(points);
            record.setBalance(newBalance);
            record.setType(type);
            record.setDescription(description);
            record.setRelatedId(relatedId);
            record.setRelatedType(relatedType);
            record.setOperator(operator);
            record.setCreatedAt(LocalDateTime.now());

            // 保存记录
            boolean saveResult = save(record);

            // 更新学员积分余额
            if (saveResult) {
                boolean updateResult = studentService.updateStudentPoints(userId, newBalance);
                if (!updateResult) {
                    log.warn("更新学生表积分失败：userId={}, newBalance={}", userId, newBalance);
                }
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("记录积分变动失败：userId={}, points={}, type={}", userId, points, type, e);
            throw new RuntimeException("记录积分变动失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPointsByRule(Integer userId, String ruleCode, Integer relatedId, String relatedType) {
        try {
            // 获取积分规则
            PointsRule rule = pointsRuleService.getByCode(ruleCode);
            if (rule == null || rule.getStatus() != 1) {
                return false;
            }

            // 检查规则的总次数限制
            if (rule.getTimesLimit() > 0 && rule.getUsedCount() >= rule.getTimesLimit()) {
                return false;
            }

            // 检查规则的每日次数限制
            if (rule.getDailyLimit() > 0) {
                LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
                LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);

                int dailyUsage = baseMapper.countDailyUsage(
                    userId,
                    "rule_based",
                    "规则：" + rule.getCode(),
                    startOfDay,
                    endOfDay
                );

                if (dailyUsage >= rule.getDailyLimit()) {
                    return false;
                }
            }

            // 记录积分变动
            boolean recorded = recordPointsChange(
                userId,
                rule.getPoints(),
                "rule_based",
                "规则：" + rule.getCode() + " - " + rule.getName(),
                relatedId,
                relatedType,
                "system"
            );

            // 增加规则使用次数
            if (recorded) {
                pointsRuleService.incrementUsedCount(rule.getId());
                return true;
            }

            return false;
        } catch (Exception e) {
            throw new RuntimeException("根据规则添加积分失败", e);
        }
    }
}
