import { get } from '@/utils/request'

export interface CarouselItem {
  id: number
  title: string
  imageUrl: string
  link: string
  sort: number
  status: number
  createTime: string
  updateTime?: string
}

export interface NewsItem {
  id: number
  title: string
  coverUrl?: string
  content: string
  publishTime: string
  viewCount: number
  status: number
  isTop: boolean
  createTime: string
  updateTime?: string
}

export interface NoticeItem {
  id: number
  title: string
  content: string
  publishTime: string
  importance: number
  status: number
  isTop: boolean
  createTime: string
  updateTime?: string
}

export interface PageQuery {
  pageNum?: number;
  pageSize?: number;
  status?: number;
  keyword?: string;
}

export interface PageResponse<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

export const getCarouselList = (params: PageQuery) => get<PageResponse<CarouselItem>>('/content/carousel/list', params)
export const getNewsList = (params: PageQuery) => get<PageResponse<NewsItem>>('/content/news/list', params)
export const getNoticeList = (params: PageQuery) => get<PageResponse<NoticeItem>>('/content/notice/list', params)
export const getNewsById = (id: number) => get<NewsItem>(`/content/news/${id}`)
export const getNoticeById = (id: number) => get<NoticeItem>(`/content/notice/${id}`)
