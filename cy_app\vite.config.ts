import {defineConfig} from "vite";
import uni from "@dcloudio/vite-plugin-uni";

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        uni(),
    ],
    css: {
        preprocessorOptions: {
            scss: {
                // 取消sass废弃API的报警
                silenceDeprecations: ['legacy-js-api', 'color-functions', 'import'],
            },
        },
    },
    server: {
        port: 5173,
        open: true,
        proxy: {
            '/api': {
                target: 'http://127.0.0.1:8090',
                changeOrigin: true,
                secure: false,
                rewrite: (path) => path.replace(/^\/api/, '/api')
            }
        }
    },
});


