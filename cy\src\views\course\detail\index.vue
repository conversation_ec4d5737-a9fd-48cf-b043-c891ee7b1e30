<template>
  <div class="app-container">
    <el-card v-if="course" v-loading="loading">
      <el-form :model="course" label-width="120px">
        <el-form-item label="课程名称">
          <el-input v-model="course.name" />
        </el-form-item>
        <el-form-item label="课程描述">
          <el-input v-model="course.description" type="textarea" />
        </el-form-item>
        <el-form-item label="课程封面URL">
          <el-input v-model="course.coverImageUrl" />
          <!-- TODO: Add upload component for cover image -->
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSave">保存课程</el-button>
          <el-button @click="goBack">返回列表</el-button>
        </el-form-item>
      </el-form>

      <el-divider />

      <div class="structure-header">
        <h2>课程结构</h2>
        <el-button type="success" @click="addChapter">添加章节</el-button>
      </div>

      <el-tree
        :data="courseStructure"
        node-key="id"
        default-expand-all
        draggable
        :allow-drop="allowDrop"
        @node-drop="handleDrop"
        class="course-tree"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <span>{{ node.label }}</span>
            <span>
              <el-button v-if="data.type === 'chapter'" type="text" size="mini" @click.stop="addLesson(data)">
                添加课时
              </el-button>
              <el-button type="text" size="mini" @click.stop="renameNode(node, data)">
                重命名
              </el-button>
              <el-button type="text" size="mini" class="delete-btn" @click.stop="removeNode(node, data)">
                删除
              </el-button>
            </span>
          </span>
        </template>
      </el-tree>

    </el-card>
    <div v-else>
      <p>正在加载课程信息...</p>
    </div>

    <ResourcePicker
      :visible="isPickerVisible"
      @update:visible="isPickerVisible = false"
      @select="handleResourceSelect"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCourseDetails, updateCourse, Course } from '@/api/course'
import ResourcePicker from '../components/ResourcePicker.vue'

const route = useRoute()
const router = useRouter()

const course = ref<Course | null>(null)
const loading = ref(false)
const courseStructure = ref<any[]>([])
const isPickerVisible = ref(false)
const currentChapter = ref<any>(null)

const courseId = Number(route.params.id)

const fetchCourse = async () => {
  if (isNaN(courseId)) {
    ElMessage.error('无效的课程ID')
    router.push('/course/list')
    return
  }
  loading.value = true
  try {
    const response: any = await getCourseDetails(courseId)
    course.value = response

    // Parse structure JSON string into an object
    if (response.structure) {
      try {
        courseStructure.value = JSON.parse(response.structure);
      } catch (e) {
        console.error("Failed to parse course structure", e);
        courseStructure.value = []; // Fallback to empty array on parsing error
      }
    } else {
      courseStructure.value = [];
    }

  } catch (error) {
    ElMessage.error('获取课程详情失败')
  } finally {
    loading.value = false
  }
}

const handleSave = async () => {
  if (!course.value) return
  loading.value = true
  try {
    // Stringify the structure back to JSON before saving
    course.value.structure = JSON.stringify(courseStructure.value)
    await updateCourse(courseId, course.value)
    ElMessage.success('课程保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push('/course/list')
}

const addChapter = () => {
  ElMessageBox.prompt('请输入新章节的名称', '添加章节', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(({ value }) => {
      if (!value) {
        ElMessage.warning('章节名称不能为空');
        return;
      }
      const newChapter = {
        id: Math.floor(new Date().getTime() / 1000), // 使用整数类型的ID
        label: value,
        type: 'chapter',
        children: [],
      };
      courseStructure.value.push(newChapter);
      ElMessage.success(`章节 "${value}" 添加成功`);
    })
    .catch(() => { /* User cancelled */ });
};

const addLesson = (chapterData: any) => {
  currentChapter.value = chapterData;
  isPickerVisible.value = true;
};

const handleResourceSelect = (resource: any) => {
  if (!currentChapter.value) return;

  const newLesson = {
    id: Math.floor(new Date().getTime() / 1000) + Math.floor(Math.random() * 1000), // 使用整数类型的ID，添加随机数避免冲突
    label: resource.name, // Default lesson name to resource name
    type: 'lesson',
    resourceId: resource.id,
  };

  if (!currentChapter.value.children) {
    currentChapter.value.children = [];
  }
  currentChapter.value.children.push(newLesson);
  ElMessage.success(`课时 "${resource.name}" 已添加到章节 "${currentChapter.value.label}"`);
};

const renameNode = (node: any, data: any) => {
  ElMessageBox.prompt('请输入新的名称', '重命名', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: data.label,
  })
    .then(({ value }) => {
      if (!value) {
        ElMessage.warning('名称不能为空');
        return;
      }
      data.label = value;
      ElMessage.success('重命名成功');
    })
    .catch(() => { /* User cancelled */ });
};

const removeNode = (node: any, data: any) => {
  ElMessageBox.confirm(`确定要删除 "${data.label}" 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex((d: any) => d.id === data.id);
      children.splice(index, 1);
      ElMessage.success('删除成功');
    })
    .catch(() => { /* User cancelled */ });
};

const allowDrop = (draggingNode: any, dropNode: any, type: any) => {
  // For now, allow any drop. Logic can be refined later.
  // e.g., prevent dropping a chapter inside another chapter.
  return true;
};

const handleDrop = (draggingNode: any, dropNode: any, dropType: any, ev: any) => {
    // el-tree handles the visual update, this event is just for logging or fine-tuning if needed.
    // The data (`courseStructure`) is already updated by the component.
    console.log('tree drop: ', draggingNode.label, dropNode.label, dropType);
    ElMessage.success('排序已更新');
};

onMounted(() => {
  fetchCourse()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.structure-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.course-tree {
  margin-top: 20px;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.delete-btn {
  color: #F56C6C;
}
</style> 