<template>
	<component :is="iconComponent" :size="size" :color="color" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import HomeIcon from './HomeIcon.vue'
import StudyIcon from './StudyIcon.vue'
import ExamIcon from './ExamIcon.vue'
import ForumIcon from './ForumIcon.vue'
import ProfileIcon from './ProfileIcon.vue'

interface Props {
	name: 'home' | 'study' | 'exam' | 'forum' | 'profile'
	size?: string | number
	color?: string
}

const props = withDefaults(defineProps<Props>(), {
	size: 24,
	color: 'currentColor'
})

const iconComponent = computed(() => {
	const iconMap = {
		home: HomeIcon,
		study: StudyIcon,
		exam: ExamIcon,
		forum: ForumIcon,
		profile: ProfileIcon
	}
	return iconMap[props.name]
})
</script> 