package com.cy.education.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 试卷题目关联实体类
 */
@Data
@TableName("exam_paper_question")
public class ExamPaperQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 试卷ID
     */
    private Integer paperId;

    /**
     * 题目ID
     */
    private Integer questionId;

    /**
     * 分值
     */
    private Integer score;

    /**
     * 排序号
     */
    @TableField("question_order")
    private Integer questionOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 