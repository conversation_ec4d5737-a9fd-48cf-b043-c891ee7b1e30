<template>
  <div class="course-preview-container">
    <!-- 左侧课程目录 -->
    <div class="sidebar">
      <div class="course-header">
        <div class="course-info">
          <h2 class="course-title">{{ course?.name || '课程预览' }}</h2>
          <p class="course-description">{{ course?.description || '暂无描述' }}</p>
        </div>
        <div class="course-meta">
          <el-tag v-if="(course as any)?.level" :type="getLevelType((course as any).level)">
            {{ getLevelText((course as any).level) }}
          </el-tag>
          <span v-if="(course as any)?.duration" class="duration">
            <el-icon><Clock /></el-icon>
            {{ (course as any).duration }}分钟
          </span>
        </div>
      </div>

      <div class="course-content">
        <h3 class="content-title">
          <el-icon><Menu /></el-icon>
          课程目录
        </h3>
        <el-tree
          v-if="courseStructure.length > 0"
          :data="courseStructure"
          @node-click="handleNodeClick"
          :props="{ children: 'children', label: 'label' }"
          :expand-on-click-node="false"
          default-expand-all
          class="course-tree"
          :current-node-key="currentNodeKey"
          highlight-current
        >
          <template #default="{ node, data }">
            <div class="tree-node" :class="{ 'is-lesson': data.type === 'lesson', 'is-active': currentNodeKey === data.key }">
              <div class="node-content">
                <el-icon class="node-icon" :class="getNodeIconClass(data.type)">
                  <component :is="getNodeIcon(data.type)" />
                </el-icon>
                <span class="node-label">{{ node.label }}</span>
              </div>
              <div v-if="data.type === 'lesson'" class="lesson-meta">
                <el-tag v-if="data.duration" size="small" type="info">{{ data.duration }}min</el-tag>
                <el-icon v-if="data.completed" class="completed-icon" color="#67c23a"><Check /></el-icon>
              </div>
            </div>
          </template>
        </el-tree>
        <el-empty v-else description="暂无课程目录" :image-size="80" />
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="main-content">
      <div v-if="!selectedResource" class="welcome-section">
        <div class="welcome-content">
          <el-icon class="welcome-icon"><VideoPlay /></el-icon>
          <h1 class="welcome-title">开始学习之旅</h1>
          <p class="welcome-description">请从左侧目录中选择一个课时开始学习</p>
          <div class="course-stats" v-if="course">
            <div class="stat-item">
              <span class="stat-number">{{ getTotalLessons() }}</span>
              <span class="stat-label">课时</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ (course as any)?.duration || 0 }}</span>
              <span class="stat-label">分钟</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="content-section" v-loading="resourceLoading">
        <div class="content-header">
          <h2 class="content-title">{{ selectedResource.name }}</h2>
          <div class="content-actions">
            <el-button v-if="selectedResource.type === 'file'" type="primary" @click="downloadResource">
              <el-icon><Download /></el-icon>
              下载资源
            </el-button>
          </div>
        </div>

        <div class="content-body">
          <!-- 视频内容 -->
          <div v-if="selectedResource.type === 'video'" class="video-container">
            <video
              ref="videoPlayer"
              :src="selectedResource.content"
              controls
              class="video-player"
              @loadstart="onVideoLoadStart"
              @loadeddata="onVideoLoaded"
              @error="onVideoError"
            >
              您的浏览器不支持视频播放
            </video>
          </div>

          <!-- 文件内容 -->
          <div v-else-if="selectedResource.type === 'file'" class="file-container">
            <div class="file-preview">
              <el-icon class="file-icon"><Document /></el-icon>
              <div class="file-info">
                <h3>{{ selectedResource.name }}</h3>
                <p class="file-description">点击下载按钮获取文件资源</p>
              </div>
            </div>
          </div>

          <!-- 文章内容 -->
          <div v-else-if="selectedResource.type === 'article'" class="article-container">
            <div class="article-content" v-html="selectedResource.content"></div>
          </div>

          <!-- 其他类型内容 -->
          <div v-else class="unknown-content">
            <el-empty description="不支持的资源类型" :image-size="100" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { getCourseForPreview, Course } from '@/api/course'
import { getResourceById, Resource } from '@/api/resource'
import { ElMessage } from 'element-plus'
import {
  Document,
  Clock,
  Menu,
  Check,
  VideoPlay,
  Download,
  Folder,
  FolderOpened
} from '@element-plus/icons-vue'
import '@/styles/content.css'

const route = useRoute()
const course = ref<Course | null>(null)
const courseStructure = ref<any[]>([])
const selectedResource = ref<Resource | null>(null)
const resourceLoading = ref(false)
const currentNodeKey = ref<string>('')
const videoPlayer = ref<HTMLVideoElement>()

const courseId = Number(route.params.id)

// 工具方法
const getLevelType = (level: string) => {
  const levelMap: Record<string, string> = {
    'beginner': 'success',
    'intermediate': 'warning',
    'advanced': 'danger'
  }
  return levelMap[level] || 'info'
}

const getLevelText = (level: string) => {
  const levelMap: Record<string, string> = {
    'beginner': '初级',
    'intermediate': '中级',
    'advanced': '高级'
  }
  return levelMap[level] || level
}

const getNodeIcon = (type: string) => {
  return type === 'lesson' ? Document : Menu
}

const getNodeIconClass = (type: string) => {
  return type === 'lesson' ? 'lesson-icon' : 'chapter-icon'
}

const getTotalLessons = () => {
  let count = 0
  const countLessons = (items: any[]) => {
    items.forEach(item => {
      if (item.type === 'lesson') {
        count++
      }
      if (item.children) {
        countLessons(item.children)
      }
    })
  }
  countLessons(courseStructure.value)
  return count
}

const downloadResource = () => {
  if (selectedResource.value?.content) {
    window.open(selectedResource.value.content, '_blank')
  }
}

// 视频事件处理
const onVideoLoadStart = () => {
  console.log('Video loading started')
}

const onVideoLoaded = () => {
  console.log('Video loaded')
}

const onVideoError = (error: Event) => {
  console.error('Video error:', error)
  ElMessage.error('视频加载失败')
}

const fetchCourseForPreview = async () => {
  if (isNaN(courseId)) {
    ElMessage.error('无效的课程ID')
    return
  }
  try {
    const response = await getCourseForPreview(courseId)
    console.log('Course preview response:', response)

    // 处理不同的响应结构
    const courseData = response.data || response;
    course.value = courseData;

    if (courseData && courseData.structure) {
      try {
        const structure = JSON.parse(courseData.structure)
        // 为每个节点添加唯一key
        const addKeys = (items: any[], parentKey = '') => {
          items.forEach((item, index) => {
            item.key = parentKey ? `${parentKey}-${index}` : `${index}`
            if (item.children) {
              addKeys(item.children, item.key)
            }
          })
        }
        addKeys(structure)
        courseStructure.value = structure
      } catch (parseError) {
        console.error('Failed to parse course structure:', parseError)
        courseStructure.value = []
      }
    } else {
      courseStructure.value = []
    }
  } catch (error) {
    console.error('获取课程预览信息失败:', error)
    ElMessage.error('获取课程预览信息失败')
  }
}

const handleNodeClick = async (data: any) => {
  if (data.type !== 'lesson') {
    return
  }

  // 设置当前选中的节点
  currentNodeKey.value = data.key

  if (!data.resourceId) {
    ElMessage.warning('该课时未关联任何资源')
    return
  }

  resourceLoading.value = true
  try {
    // 如果是同一个资源，重新播放视频
    if (selectedResource.value && selectedResource.value.id === data.resourceId) {
      if (selectedResource.value.type === 'video' && videoPlayer.value) {
        videoPlayer.value.currentTime = 0;
        videoPlayer.value.play();
      }
      resourceLoading.value = false;
      return;
    }

    const response = await getResourceById(data.resourceId)
    selectedResource.value = response.data || response

    // 如果是视频，自动播放
    if (selectedResource.value?.type === 'video') {
      await nextTick()
      if (videoPlayer.value) {
        videoPlayer.value.play()
      }
    }
  } catch (error) {
    console.error('加载资源失败:', error)
    ElMessage.error('加载资源失败')
    selectedResource.value = null
  } finally {
    resourceLoading.value = false
  }
}

onMounted(() => {
  fetchCourseForPreview()
})
</script>

<style scoped>
.course-preview-container {
  display: flex;
  height: calc(100vh - 50px);
  background-color: #f5f7fa;
}

/* 左侧边栏样式 */
.sidebar {
  width: 350px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.course-header {
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.course-info {
  margin-bottom: 16px;
}

.course-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.course-description {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.5;
}

.course-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.duration {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  opacity: 0.9;
}

.course-content {
  padding: 20px;
}

.content-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: white;
}

.course-tree {
  background: transparent;
}

.course-tree :deep(.el-tree-node__content) {
  background: transparent;
  border-radius: 8px;
  margin-bottom: 4px;
  padding: 8px 12px;
  transition: all 0.3s ease;
}

.course-tree :deep(.el-tree-node__content:hover) {
  background: rgba(255, 255, 255, 0.1);
}

.course-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background: rgba(255, 255, 255, 0.2);
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: white;
}

.tree-node.is-lesson {
  cursor: pointer;
}

.tree-node.is-active {
  font-weight: 600;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
}

.node-icon.lesson-icon {
  color: #a8e6cf;
}

.node-icon.chapter-icon {
  color: #ffd93d;
}

.node-label {
  font-size: 14px;
  line-height: 1.4;
}

.lesson-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.completed-icon {
  font-size: 16px;
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  background: white;
  overflow-y: auto;
}

.welcome-section {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
}

.welcome-content {
  text-align: center;
  max-width: 500px;
}

.welcome-icon {
  font-size: 80px;
  color: #667eea;
  margin-bottom: 24px;
}

.welcome-title {
  font-size: 32px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 16px 0;
}

.welcome-description {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.course-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #95a5a6;
}

.content-section {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.content-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.content-actions {
  display: flex;
  gap: 12px;
}

.content-body {
  flex: 1;
  overflow-y: auto;
}

/* 视频容器样式 */
.video-container {
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.video-player {
  width: 100%;
  height: auto;
  min-height: 400px;
  display: block;
}

/* 文件容器样式 */
.file-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px;
  border: 2px dashed #ddd;
  border-radius: 12px;
  background: #fafafa;
}

.file-icon {
  font-size: 64px;
  color: #667eea;
  margin-bottom: 16px;
}

.file-info h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.file-description {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

/* 文章容器样式 */
.article-container {
  max-width: 800px;
  margin: 0 auto;
}

.article-content {
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  line-height: 1.8;
  font-size: 16px;
  color: #2c3e50;
}

.article-content :deep(h1),
.article-content :deep(h2),
.article-content :deep(h3) {
  color: #2c3e50;
  margin-top: 24px;
  margin-bottom: 16px;
}

.article-content :deep(p) {
  margin-bottom: 16px;
}

.article-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 16px 0;
}

.unknown-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-preview-container {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 300px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .course-stats {
    gap: 20px;
  }
}
</style>