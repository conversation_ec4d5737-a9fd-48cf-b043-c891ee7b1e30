import { 
  generateId, 
  randomDate, 
  randomItem, 
  randomItems, 
  randomInt, 
  randomFloat,
  mockResponse,
  paginateData,
  getQueryParams
} from './utils'
import type { 
  Course, 
  CourseDetail, 
  CourseSection, 
  CourseLesson,
  StudyRecord
} from '@/api/course'

// 课程标签
const tags = [
  '入门', '进阶', '专业', '管理', '技术', '实操', '理论', '案例', '考核', '必修', '选修',
  '安全管理', '安全技术', '应急管理', '职业健康', '环境保护', '机电设备', '矿山开采', '新工艺新技术'
]

// 课程级别
const levels = ['beginner', 'intermediate', 'advanced']

// 课时类型
const lessonTypes = ['video', 'document', 'quiz']

// 生成模拟课程数据
function generateCourses(count: number = 50): Course[] {
  const courses: Course[] = []
  
  for (let i = 0; i < count; i++) {
    const isPublished = Math.random() > 0.2 ? 1 : 0
    const courseTags = randomItems(tags, randomInt(2, 5))
    const courseId = parseInt(generateId(8), 10) % 100000 // 转换为较小的数字ID
    const course: Course = {
      id: courseId,
      title: `培训课程 ${i + 1}`,
      cover: `https://picsum.photos/300/200?random=${i}`,
      description: `这是一门培训课程，适合${randomItem(['初学者', '有一定基础的学员', '高级技术人员'])}学习。`,
      tags: courseTags,
      level: randomItem(levels) as 'beginner' | 'intermediate' | 'advanced',
      duration: randomInt(30, 300),
      studentsCount: randomInt(0, 1000), // 模拟后端统计的学员数量
      isPublished,
      createdAt: new Date(randomDate(new Date(2022, 0, 1), new Date(2023, 0, 1))).toISOString(),
      updatedAt: new Date(randomDate(new Date(2023, 0, 1), new Date())).toISOString(),
      visibleDepartments: randomItems([1, 2, 3, 4, 5, 6, 7, 8], randomInt(1, 3)), // 随机可见部门
      allowFastForward: Math.random() > 0.3 ? 1 : 0 // 70%概率允许快进
    }
    
    courses.push(course)
  }
  
  return courses
}

// 生成模拟课时数据
function generateLessons(count: number = 5, sectionId: number): CourseLesson[] {
  const lessons: CourseLesson[] = []
  
  for (let i = 0; i < count; i++) {
    const type = randomItem(lessonTypes) as 'video' | 'document' | 'quiz'
    const lessonId = parseInt(generateId(8), 10) % 100000
    
    const lesson: CourseLesson = {
      id: lessonId,
      title: `课时 ${i + 1}`,
      type,
      duration: randomInt(5, 45),
      orderNum: i + 1,
      content: `这是第 ${i + 1} 课时的内容描述`,
      videoUrl: type === 'video' ? `https://example.com/videos/${generateId()}.mp4` : undefined,
      fileUrl: type === 'document' ? `https://example.com/docs/${generateId()}.pdf` : undefined
    }
    
    lessons.push(lesson)
  }
  
  return lessons
}

// 生成模拟章节数据
function generateSections(count: number = 4, courseId: number): CourseSection[] {
  const sections: CourseSection[] = []
  
  for (let i = 0; i < count; i++) {
    const sectionId = parseInt(generateId(8), 10) % 100000
    const lessonsCount = randomInt(3, 8)
    
    const section: CourseSection = {
      id: sectionId,
      title: `第 ${i + 1} 章: ${randomItem(['基础知识', '核心概念', '实战案例', '综合应用', '拓展提高'])}`,
      duration: 0, // 后面计算
      orderNum: i + 1,
      lessons: generateLessons(lessonsCount, sectionId)
    }
    
    // 计算章节总时长
    section.duration = section.lessons.reduce((total, lesson) => total + lesson.duration, 0)
    
    sections.push(section)
  }
  
  return sections
}

// 生成课程详情
function generateCourseDetail(course: Course): CourseDetail {
  const sectionsCount = randomInt(3, 6)
  const sections = generateSections(sectionsCount, course.id)
  
  return {
    ...course,
    objectives: [
      '掌握安全生产基本理论和方法',
      '了解最新安全生产法律法规',
      '提高安全意识和应急处理能力',
      '学习先进工作经验和案例分析'
    ],
    requirements: [
      '具备基本的矿山作业知识',
      '有一定的实际工作经验',
      '能够按时完成课程学习和作业'
    ],
    sections
  }
}

// 生成学习记录
function generateStudyRecords(count: number = 100, courses: Course[]): StudyRecord[] {
  const records: StudyRecord[] = []
  
  for (let i = 0; i < count; i++) {
    const course = randomItem(courses)
    const progress = randomInt(0, 100)
    const status = progress === 0 ? 'not_started' : (progress === 100 ? 'completed' : 'in_progress')
    const recordId = parseInt(generateId(8), 10) % 100000
    const studentId = parseInt(generateId(8), 10) % 100000
    const lessonId = Math.random() > 0.5 ? parseInt(generateId(8), 10) % 100000 : undefined
    
    const record: StudyRecord = {
      id: recordId,
      studentId: studentId,
      studentName: `学员${randomInt(1, 999)}`,
      courseId: course.id,
      courseName: course.title,
      lessonId,
      studyTime: randomInt(10, 120), // 本次学习时长(分钟)
      progress,
      status: status as 'not_started' | 'in_progress' | 'completed',
      score: status === 'completed' ? randomInt(60, 100) : undefined,
      createdAt: new Date(randomDate(new Date(2022, 0, 1), new Date(2023, 0, 1))).toISOString(),
      updatedAt: new Date(randomDate(new Date(2023, 0, 1))).toISOString()
    }
    
    records.push(record)
  }
  
  return records
}

// 生成统计数据
function generateStatistics(courses: Course[]) {
  const totalCourses = courses.length
  const publishedCourses = courses.filter(course => course.isPublished === 1).length
  const totalStudents = courses.reduce((total, course) => total + course.studentsCount, 0)
  
  // 级别分布
  const levelDistribution = levels.map(level => {
    const count = courses.filter(course => course.level === level).length
    return { level, count }
  })
  
  // 热门课程
  const popularCourses = [...courses]
    .sort((a, b) => b.studentsCount - a.studentsCount)
    .slice(0, 5)
    .map(course => ({
      id: course.id,
      title: course.title,
      studentsCount: course.studentsCount
    }))
  
  return {
    totalCourses,
    publishedCourses,
    totalStudents,
    levelDistribution,
    popularCourses
  }
}

// 课程数据集
export const courses = generateCourses(50)

// 学习记录数据集
export const studyRecords = generateStudyRecords(200, courses)

// 模拟获取课程列表接口
export function mockGetCourseList(url: string) {
  const { page = 1, limit = 10, keyword, level, status } = getQueryParams(url)
  
  let filteredCourses = [...courses]
  
  // 按关键词过滤
  if (keyword) {
    filteredCourses = filteredCourses.filter(course => 
      course.title.toLowerCase().includes(keyword.toLowerCase()) || 
      course.description.toLowerCase().includes(keyword.toLowerCase())
    )
  }
  
  // 按级别过滤
  if (level) {
    filteredCourses = filteredCourses.filter(course => course.level === level)
  }
  
  // 按状态过滤
  if (status !== undefined) {
    const statusNumber = parseInt(status, 10)
    filteredCourses = filteredCourses.filter(course => course.isPublished === statusNumber)
  }
  
  // 分页
  const { list, total } = paginateData(filteredCourses, page, limit)
  
  return mockResponse({ list, total })
}

// 模拟获取课程详情接口
export function mockGetCourseDetail(url: string) {
  const id = url.split('/').pop()
  if (!id) return mockResponse(null, 400, 'Invalid ID')
  
  const course = courses.find(course => course.id.toString() === id)
  if (!course) return mockResponse(null, 404, 'Course not found')
  
  const courseDetail = generateCourseDetail(course)
  return mockResponse(courseDetail)
}

// 模拟创建课程接口
export function mockCreateCourse(data: Partial<Course>) {
  const courseId = parseInt(generateId(8), 10) % 100000
  const newCourse: Course = {
    id: courseId,
    title: data.title || '新课程',
    cover: data.cover || 'https://picsum.photos/300/200',
    description: data.description || '课程描述',
    tags: data.tags || randomItems(tags, 3),
    level: data.level || 'beginner',
    duration: data.duration || 60,
    studentsCount: 0,
    isPublished: data.isPublished ? 1 : 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  courses.push(newCourse)
  
  return mockResponse({ id: newCourse.id })
}

// 模拟更新课程接口
export function mockUpdateCourse(url: string, data: Partial<Course>) {
  const id = url.split('/').pop()
  if (!id) return mockResponse(null, 400, 'Invalid ID')
  
  const index = courses.findIndex(course => course.id.toString() === id)
  if (index === -1) return mockResponse(null, 404, 'Course not found')
  
  const course = courses[index]
  
  // 更新课程信息
  courses[index] = {
    ...course,
    ...data,
    updatedAt: new Date().toISOString()
  }
  
  return mockResponse({ success: true })
}

// 模拟删除课程接口
export function mockDeleteCourse(url: string) {
  const id = url.split('/').pop()
  if (!id) return mockResponse(null, 400, 'Invalid ID')
  
  const index = courses.findIndex(course => course.id.toString() === id)
  if (index === -1) return mockResponse(null, 404, 'Course not found')
  
  courses.splice(index, 1)
  
  return mockResponse({ success: true })
}

// 模拟发布/下架课程接口
export function mockPublishCourse(url: string, data: { isPublished: boolean }) {
  const id = url.split('/').pop()
  if (!id) return mockResponse(null, 400, 'Invalid ID')
  
  const index = courses.findIndex(course => course.id.toString() === id)
  if (index === -1) return mockResponse(null, 404, 'Course not found')
  
  courses[index].isPublished = data.isPublished ? 1 : 0
  
  return mockResponse({ success: true })
}

// 模拟获取学习记录列表接口
export function mockGetStudyRecordList(url: string) {
  const { page = 1, limit = 10, courseId, studentId, status } = getQueryParams(url)
  
  let filteredRecords = [...studyRecords]
  
  // 按课程ID过滤
  if (courseId) {
    filteredRecords = filteredRecords.filter(record => record.courseId.toString() === courseId)
  }
  
  // 按学员ID过滤
  if (studentId) {
    filteredRecords = filteredRecords.filter(record => record.studentId.toString() === studentId)
  }
  
  // 按状态过滤
  if (status) {
    filteredRecords = filteredRecords.filter(record => record.status === status)
  }
  
  // 分页
  const { list, total } = paginateData(filteredRecords, page, limit)
  
  return mockResponse({ list, total })
}

// 模拟获取学习记录详情接口
export function mockGetStudyRecordDetail(url: string) {
  const id = url.split('/').pop()
  if (!id) return mockResponse(null, 400, 'Invalid ID')
  
  const record = studyRecords.find(record => record.id.toString() === id)
  if (!record) return mockResponse(null, 404, 'Study record not found')
  
  return mockResponse(record)
}

// 模拟获取课程统计数据接口
export function mockGetCourseStatistics() {
  return mockResponse(generateStatistics(courses))
} 