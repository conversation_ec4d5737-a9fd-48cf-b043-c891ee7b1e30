<template>
  <div class="download-test">
    <el-card>
      <template #header>
        <h3>文件下载测试</h3>
      </template>
      
      <div class="test-section">
        <h4>学员模板下载测试</h4>
        <el-button type="primary" @click="testTemplateDownload" :loading="loading1">
          测试模板下载
        </el-button>
        <p class="test-result">{{ result1 }}</p>
      </div>
      
      <div class="test-section">
        <h4>学员导出测试</h4>
        <el-button type="success" @click="testExportDownload" :loading="loading2">
          测试导出下载
        </el-button>
        <p class="test-result">{{ result2 }}</p>
      </div>
      
      <div class="test-section">
        <h4>直接链接测试</h4>
        <el-button type="info" @click="testDirectDownload">
          直接下载链接
        </el-button>
        <p class="test-result">{{ result3 }}</p>
      </div>
      
      <div class="test-section">
        <h4>API响应测试</h4>
        <el-button type="warning" @click="testApiResponse" :loading="loading4">
          测试API响应
        </el-button>
        <p class="test-result">{{ result4 }}</p>
        <pre v-if="apiResponse" class="api-response">{{ apiResponse }}</pre>
      </div>

      <div class="test-section">
        <h4>简化导出测试</h4>
        <el-button type="danger" @click="testSimpleExport" :loading="loading5">
          测试简化导出
        </el-button>
        <p class="test-result">{{ result5 }}</p>
      </div>

      <div class="test-section">
        <h4>创建测试数据</h4>
        <el-button type="success" @click="createTestData" :loading="loading6">
          创建测试学员数据
        </el-button>
        <p class="test-result">{{ result6 }}</p>
      </div>

      <div class="test-section">
        <h4>简化导出测试（直接下载）</h4>
        <el-button type="primary" @click="testDirectExport" :loading="loading7">
          测试直接导出
        </el-button>
        <p class="test-result">{{ result7 }}</p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { downloadStudentTemplate, exportStudentsV2 } from '@/api/student'
import { get } from '@/utils/request'

const loading1 = ref(false)
const loading2 = ref(false)
const loading4 = ref(false)
const loading5 = ref(false)
const loading6 = ref(false)
const loading7 = ref(false)
const result1 = ref('')
const result2 = ref('')
const result3 = ref('')
const result4 = ref('')
const result5 = ref('')
const result6 = ref('')
const result7 = ref('')
const apiResponse = ref('')

// 测试模板下载
const testTemplateDownload = async () => {
  loading1.value = true
  result1.value = '开始测试Excel模板下载...'

  try {
    await downloadStudentTemplate()
    result1.value = '✅ Excel模板下载成功，请检查下载文件夹中的"学员导入模板.xlsx"文件'
    ElMessage.success('Excel模板下载成功')
  } catch (error) {
    console.error('模板下载失败:', error)
    result1.value = `❌ 模板下载失败: ${error}`
    ElMessage.error('模板下载失败')
  } finally {
    loading1.value = false
  }
}

// 测试导出下载
const testExportDownload = async () => {
  loading2.value = true
  result2.value = '开始测试...'
  
  try {
    await exportStudentsV2({
      keyword: '',
      fields: ['姓名', '用户名', '手机号'],
      range: 'all',
      format: 'xlsx'
    })
    result2.value = '✅ 导出下载成功'
    ElMessage.success('导出下载成功')
  } catch (error) {
    console.error('导出下载失败:', error)
    result2.value = `❌ 导出下载失败: ${error}`
    ElMessage.error('导出下载失败')
  } finally {
    loading2.value = false
  }
}

// 测试直接下载链接
const testDirectDownload = () => {
  result3.value = '开始测试...'
  
  try {
    const link = document.createElement('a')
    link.href = '/api/student/import/template'
    link.download = '学员导入模板.csv'
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    result3.value = '✅ 直接下载链接已触发'
    ElMessage.success('直接下载链接已触发')
  } catch (error) {
    console.error('直接下载失败:', error)
    result3.value = `❌ 直接下载失败: ${error}`
    ElMessage.error('直接下载失败')
  }
}

// 测试API响应
const testApiResponse = async () => {
  loading4.value = true
  result4.value = '开始测试...'
  
  try {
    const response = await get('/student/import/template', {}, {
      responseType: 'blob'
    })
    
    result4.value = '✅ API响应成功'
    apiResponse.value = `
响应类型: ${typeof response}
响应大小: ${response.size || response.length || 'unknown'} bytes
响应类型: ${response.type || 'unknown'}
    `.trim()
    
    ElMessage.success('API响应成功')
  } catch (error) {
    console.error('API响应失败:', error)
    result4.value = `❌ API响应失败: ${error}`
    apiResponse.value = JSON.stringify(error, null, 2)
    ElMessage.error('API响应失败')
  } finally {
    loading4.value = false
  }
}

// 测试简化导出
const testSimpleExport = async () => {
  loading5.value = true
  result5.value = '开始测试...'

  try {
    const link = document.createElement('a')
    link.href = '/api/student/export/test'
    link.download = '测试导出.txt'
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    result5.value = '✅ 简化导出测试完成'
    ElMessage.success('简化导出测试完成')
  } catch (error) {
    console.error('简化导出测试失败:', error)
    result5.value = `❌ 简化导出测试失败: ${error}`
    ElMessage.error('简化导出测试失败')
  } finally {
    loading5.value = false
  }
}

// 创建测试数据
const createTestData = async () => {
  loading6.value = true
  result6.value = '开始创建测试数据...'

  try {
    const response = await post('/student/create-test-data', {})
    result6.value = '✅ 测试数据创建成功: ' + response.message
    ElMessage.success('测试数据创建成功')
  } catch (error) {
    console.error('创建测试数据失败:', error)
    result6.value = `❌ 创建测试数据失败: ${error}`
    ElMessage.error('创建测试数据失败')
  } finally {
    loading6.value = false
  }
}

// 测试直接导出
const testDirectExport = async () => {
  loading7.value = true
  result7.value = '开始测试直接导出...'

  try {
    const response = await fetch('/api/student/test-export-simple', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = '测试学员列表.xlsx'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)

      result7.value = '✅ 直接导出成功，文件大小: ' + (blob.size / 1024).toFixed(2) + ' KB'
      ElMessage.success('导出成功')
    } else {
      const errorText = await response.text()
      result7.value = `❌ 导出失败: ${response.status} - ${errorText}`
      ElMessage.error('导出失败')
    }
  } catch (error) {
    console.error('直接导出失败:', error)
    result7.value = `❌ 直接导出失败: ${error}`
    ElMessage.error('导出失败')
  } finally {
    loading7.value = false
  }
}
</script>

<style scoped>
.download-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.test-section h4 {
  margin-bottom: 15px;
  color: #333;
}

.test-result {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-family: monospace;
}

.api-response {
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
