import {get, post} from '@/utils/request'

// =========================== 通用接口 ===========================
export interface PageQuery {
    pageNum?: number;
    pageSize?: number;
    name?: string;
}

export interface PageResponse<T> {
    list: T[];
    total: number;
    pageNum: number;
    pageSize: number;
}

// =========================== 课程实体 ===========================
export interface Course {
    id: number;
    name: string;
    description: string;
    coverImageUrl: string;
    status: string;
    structure?: string;
    creatorId?: number;
    createTime?: string;
    updateTime?: string;
}

export interface CourseVO {
    id: number;
    name: string;
    description: string;
    coverImageUrl: string;
    status: string;
    structure?: string;
    creatorId?: number;
    createTime?: string;
    updateTime?: string;
    currentUserStudyRecord: CourseDetailRecordVO
}

// =========================== 课程学习记录VO（课程聚合） ===========================
export interface CourseRecordVO {
    courseId: number;
    courseName: string;
    coverImageUrl?: string;
    progress: number; // 总进度百分比
    duration: number; // 总学习时长（秒）
    completed: number; // 是否完成 1=完成 0=未完成
}

// =========================== 课程学习记录详情VO ===========================
export interface LessonRecordVO {
  id: number;
  label: string;
  resourceId?: number;
  progress: number;
  duration: number;
  completed: number;
    lastPosition?: number; // 上次播放位置（秒）
}

export interface ChapterVO {
  id: number;
  label: string;
    children: LessonRecordVO[];
}

export interface CourseDetailRecordVO {
  courseId: number;
  courseName: string;
  progress: number;
  duration: number;
  completed: number;
  structure: ChapterVO[];
}

// =========================== 学习记录保存接口 ===========================
export interface StudyRecordSaveParams {
    courseId: number;
    lessonId: number;
    resourceId: number;
    resourceType: string;
    progress: number;
    duration: number;
    completed: number;
    lastPosition?: number;
}

// =========================== API 函数 ===========================

// 获取课程分页列表
export const getCourseList = (params: PageQuery) =>
    get<PageResponse<Course>>('/courses', params)

// 获取课程详情（包含学习记录）
export const getCourseById = (id: number) =>
    get<CourseVO>(`/courses/${id}`)

// 获取所有课程的进度列表
export const getCourseProgressList = (params: PageQuery) =>
    get<PageResponse<CourseRecordVO>>('/study/records/course-progress', params)

// 获取课程（包含章节）的进度列表
export const getCourseDetail = (courseId: number) =>
    get<CourseDetailRecordVO>(`/study/records/course-detail`, {courseId});

// 保存学习记录
export const saveStudyRecord = (params: StudyRecordSaveParams) =>
  post<{id: number}>(`/study/records/save`, params);
