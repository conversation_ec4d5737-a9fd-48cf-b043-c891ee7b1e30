// 图表配置工具

// 颜色主题配置
export const chartColors = {
    primary: '#667eea',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#3b82f6',
    purple: '#8b5cf6',
    pink: '#ec4899',
    gray: '#6b7280'
}

// 分数分布颜色
export const scoreDistributionColors = [
    chartColors.success, // 优秀 - 绿色
    chartColors.info,    // 良好 - 蓝色
    chartColors.warning, // 中等 - 橙色
    chartColors.purple,  // 及格 - 紫色
    chartColors.danger   // 不及格 - 红色
]

// 完成时间分布颜色
export const durationDistributionColors = [
    chartColors.success, // 非常快 - 绿色
    chartColors.info,    // 较快 - 蓝色
    chartColors.warning, // 一般 - 橙色
    chartColors.purple,  // 较慢 - 紫色
    chartColors.danger   // 非常慢 - 红色
]

// 部门统计颜色
export const departmentColors = [
    chartColors.primary,
    chartColors.pink,
    chartColors.success,
    chartColors.warning,
    chartColors.info
]

// 通用图表配置
export const commonChartConfig = {
    animation: true,
    background: '#FFFFFF',
    padding: [15, 15, 15, 15],
    legend: {
        show: true,
        position: 'bottom',
        float: 'center',
        padding: 5,
        margin: 5,
        backgroundColor: 'rgba(255,255,255,0.8)',
        borderRadius: 3,
        borderColor: '#CCCCCC',
        borderWidth: 1,
        fontSize: 12,
        hiddenColor: '#CECECE',
        itemGap: 10
    },
    dataLabel: false,
    dataPointShape: true
}

// 饼图配置
export const pieChartConfig = {
    ...commonChartConfig,
    legend: {
        ...commonChartConfig.legend,
        show: false
    },
    extra: {
        pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF'
        }
    }
}

// 柱状图配置
export const columnChartConfig = {
    ...commonChartConfig,
    legend: {
        ...commonChartConfig.legend,
        show: false
    },
    dataLabel: true,
    dataPointShape: false,
    extra: {
        column: {
            width: 20,
            activeBgColor: '#000000',
            activeBgOpacity: 0.08
        }
    },
    yAxis: {
        gridType: 'dash',
        dashLength: 2,
        data: []
    },
    xAxis: {
        disableGrid: true
    }
}

// 雷达图配置
export const radarChartConfig = {
    ...commonChartConfig,
    extra: {
        radar: {
            gridType: 'polygon',
            gridColor: '#CCCCCC',
            gridCount: 5,
            opacity: 0.2,
            max: 100,
            labelColor: '#666666'
        }
    }
}

// 折线图配置
export const lineChartConfig = {
    ...commonChartConfig,
    extra: {
        line: {
            type: 'straight',
            width: 2
        }
    },
    yAxis: {
        gridType: 'dash',
        dashLength: 2,
        data: []
    },
    xAxis: {
        disableGrid: true,
        fontColor: '#666666'
    }
}

// 进度条颜色配置
export const progressColors = {
    success: {
        color: '#10b981',
        colorLight: '#34d399'
    },
    info: {
        color: '#3b82f6',
        colorLight: '#60a5fa'
    },
    warning: {
        color: '#f59e0b',
        colorLight: '#fbbf24'
    },
    danger: {
        color: '#ef4444',
        colorLight: '#f87171'
    }
}
