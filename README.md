# 成远教育平台

## 项目概述

成远教育平台是一个综合性的在线教育管理系统，包含管理端、学员端App和移动端App三个子系统。

## 项目结构

```
chengyuan/
├── cy/                    # 管理端 (Vue3 + Element Plus)
├── cy_app/               # 学员端App (uni-app)
├── cy_app_server/        # 学员端App后端服务 (Spring Boot)
└── cy_server/            # 管理端后端服务 (Spring Boot)
```

## 功能模块

### 管理端 (cy/)
- 用户管理
- 课程管理
- 考试管理
- 内容管理
- 系统设置

### 学员端App (cy_app/)
- 课程学习
- 在线考试
- 学习论坛
- 积分系统
- 个人中心

#### 课程详情页面功能

- **课程信息展示**: 显示课程封面、标题、描述、统计信息
- **学习进度跟踪**:
  - 课程总体进度显示（封面区域进度条）
  - 章节进度显示（每个章节的完成百分比）
  - 课时进度显示（每个课时的完成状态和进度条）
- **进度状态标识**:
  - 已完成：绿色对勾图标，背景高亮
  - 学习中：蓝色播放图标，进度条显示
  - 未开始：灰色锁定图标
- **学习统计**: 显示章节数、课时数、已完成课时数、总学习时长
- **交互功能**: 点击课时可跳转到对应的学习页面
- **响应式设计**: 支持不同屏幕尺寸，美观的UI设计

#### 考试记录页面功能
- **考试记录展示**: 显示考试名称、分数、总分、及格状态、考试时长、完成时间
- **状态标识**: 通过颜色和图标区分不同分数等级（优秀、良好、中等、及格、不及格）
- **考试状态**: 显示考试完成状态（未开始、进行中、已完成、已超时）
- **交互功能**: 点击记录可跳转到考试详情页面
- **响应式设计**: 支持不同屏幕尺寸和深色模式

#### 文档阅读页面功能
- **学习进度跟踪**: 自动记录用户阅读进度，支持实时保存
- **学习时长统计**: 显示用户学习时长，格式化为小时/分钟/秒
- **进度保存机制**: 
  - 页面加载时自动保存初始进度
  - 每30秒自动保存当前进度
  - 用户滚动/点击时更新进度
  - 页面卸载时保存最终进度
- **进度可视化**: 显示阅读进度条和百分比
- **交互反馈**: 用户操作时实时更新进度
- **数据持久化**: 通过API将学习记录保存到后端数据库

#### 视频播放器功能
- **视频播放控制**: 支持播放/暂停、全屏、倍速播放、静音等功能
- **学习进度跟踪**: 基于视频播放时间自动计算学习进度
- **学习时长统计**: 实时显示用户学习时长
- **进度保存机制**:
  - 页面加载时自动保存初始进度
  - 每30秒自动保存当前播放进度
  - 视频播放时实时更新进度
  - 视频结束时自动标记完成
  - 页面卸载时保存最终进度
- **进度可视化**: 显示视频播放进度条和学习进度条
- **断点续播**: 记录最后播放位置，支持断点续播
- **数据持久化**: 通过API将视频学习记录保存到后端数据库

### 后端服务
- 用户认证（支持验证码）
- 数据管理
- 文件上传
- API接口

## 技术栈

### 前端
- Vue 3
- Element Plus (管理端)
- uni-app (移动端)
- TypeScript
- SCSS

### 后端
- Spring Boot
- MyBatis Plus
- MySQL
- Redis
- JWT认证

## 开发环境

### 环境要求
- Node.js 16+
- Java 8+
- MySQL 5.7+
- Redis 6+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd chengyuan
```

2. **安装依赖**
```bash
# 管理端
cd cy
npm install

# 学员端App
cd ../cy_app
npm install
```

3. **配置环境变量**
```bash
# 复制环境变量文件
cp .env.example .env
# 修改配置
```

4. **启动服务**
```bash
# 启动后端服务
cd cy_server
mvn spring-boot:run

# 启动管理端
cd cy
npm run dev

# 启动学员端App
cd cy_app
npm run dev:mp-weixin
```

## 最新更新

### 2024-12-19 更新内容

#### 1. 登录功能完善
- **永不过期Token**: 修改JWT配置，将token过期时间设置为10年（实际永不过期）
- **智能登录检查**: 
  - 访问登录页面时自动检查token有效性
  - 如果已登录且token有效，自动跳转到首页
  - 优化App启动时的登录状态检查逻辑
- **认证工具函数**: 新增`cy_app/src/utils/auth.ts`，提供统一的认证管理功能
  - `hasToken()`: 检查token是否存在
  - `validateToken()`: 验证token有效性
  - `checkAuth()`: 检查用户登录状态
  - `redirectToLogin()`: 跳转到登录页
  - `redirectToHome()`: 跳转到首页

#### 2. 课程结构ID格式修复
- **问题**: 课程结构中的章节和课时ID使用字符串格式（如"chapter_1752321198862"），导致后端处理时出现NumberFormatException
- **解决方案**: 修改管理端课程详情页面，将章节和课时ID改为整数格式
  - 章节ID: `Math.floor(new Date().getTime() / 1000)`
  - 课时ID: `Math.floor(new Date().getTime() / 1000) + Math.floor(Math.random() * 1000)`
- **影响**: 解决了移动端获取课程详情时的后端报错问题

#### 3. 技术改进
- **后端JWT配置**: 设置token过期时间为315360000000毫秒（10年）
- **前端状态管理**: 统一使用auth工具函数管理登录状态
- **错误处理**: 改进token验证失败时的处理逻辑

## API文档

### 课程相关API

#### 获取课程详情（包含学习进度）

```
GET /courses/{id}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "课程名称",
    "description": "课程描述",
    "coverImageUrl": "封面图片URL",
    "status": "active",
    "structure": "课程结构JSON",
    "currentUserStudyRecord": {
      "courseId": 1,
      "courseName": "课程名称",
      "progress": 75,
      "duration": 3600,
      "completed": 0,
      "structure": [
        {
          "id": 1,
          "label": "第一章",
          "children": [
            {
              "id": 1,
              "label": "第一课时",
              "resourceId": 1,
              "progress": 100,
              "duration": 1800,
              "completed": 1
            }
          ]
        }
      ]
    }
  }
}
```

### 论坛相关API

#### 获取分类列表
```
GET /forum/category/list
```

#### 获取帖子列表
```
GET /forum/post/list?pageNum=1&pageSize=10&keyword=xxx&sortBy=latest
```

#### 获取帖子详情
```
GET /forum/post/{id}
```

#### 获取帖子评论
```
GET /forum/post/{postId}/comments
```

### 学习记录相关API

#### 保存学习记录
```
POST /study/records/save
Content-Type: application/json

{
  "courseId": 1,
  "lessonId": 1,
  "resourceId": 1,
  "resourceType": "article",
  "progress": 85,
  "duration": 1800,
  "completed": 0,
  "lastPosition": 1800
}
```

#### 获取学习记录列表
```
GET /study/records/list?pageNum=1&pageSize=10
```

#### 获取课程进度列表
```
GET /study/records/course-progress?pageNum=1&pageSize=10
```

#### 获取课程详情（包含进度）
```
GET /study/records/course-detail?courseId=1
```

## 部署说明

### 生产环境部署
1. 构建前端项目
2. 配置生产环境变量
3. 部署后端服务
4. 配置Nginx反向代理

### Docker部署
```bash
# 构建镜像
docker build -t chengyuan-app .

# 运行容器
docker run -d -p 8080:8080 chengyuan-app
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 最新更新

### 登录页面现代化优化 (2024-12-19)

对学员端App登录页面进行了全面的现代化优化，使其与应用整体设计风格保持一致：

#### 视觉设计优化
- **渐变背景**: 采用与应用主色调一致的紫蓝色渐变背景（#667eea 到 #764ba2）
- **动态装饰元素**: 添加浮动的半透明圆形装饰，增强视觉层次感
- **Logo区域优化**: 
  - Logo尺寸调整至70px，确保页面布局紧凑
  - 添加旋转光晕效果，提升品牌视觉冲击力
  - 优化文字层次，添加阴影效果

#### 交互体验提升
- **卡片式表单设计**: 采用毛玻璃效果的卡片容器，提升现代感
- **优化输入框样式**: 
  - 重新设计输入框，采用圆角设计和聚焦状态动效
  - 添加彩色图标，提升视觉识别度
  - 优化间距和字体大小
- **增强按钮设计**: 
  - 登录按钮采用渐变背景和阴影效果
  - 添加按压动效，提升交互反馈

#### 布局优化
- **一屏显示**: 精心调整各元素间距，确保页面在一屏内完整显示，无需滚动
- **欢迎信息优化**: 将欢迎文字移至表单卡片内，优化信息层次
- **精简功能**: 移除其他登录方式，专注核心登录功能
- **响应式设计**: 针对不同屏幕高度进行适配优化

#### 技术实现
- 使用CSS3渐变、阴影、动画等现代特性
- 采用flexbox布局确保兼容性和一屏显示
- 毛玻璃效果使用backdrop-filter实现
- 动画效果采用transform和keyframes
- 响应式媒体查询适配小屏设备

这次优化在保持现代化视觉效果的同时，确保了良好的用户体验和页面的紧凑性，完美适配各种设备屏幕。

## 联系方式

- 项目维护者: [维护者姓名]
- 邮箱: [邮箱地址]
- 项目地址: [项目地址]

---

**注意**: 本项目仅供学习和研究使用，请勿用于商业用途。


