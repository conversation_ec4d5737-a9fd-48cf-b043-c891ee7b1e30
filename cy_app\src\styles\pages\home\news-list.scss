// 新闻动态列表页样式

.empty-hint {
  font-size: 14px;
  color: var(--text-tertiary);
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.news-card {
  display: flex;
  gap: 16px;
  padding: 10px;
  background: var(--gradient-card);
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-fast);

  &:active {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
}

.card-left {
  width: 100px;
  height: 60px;
  flex-shrink: 0;
}

.news-thumbnail {
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.card-right {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.news-title {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-summary {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.5;
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 8px;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: var(--text-tertiary);
}

.meta-right {
  display: flex;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;

  .icon {
    font-size: 12px;
  }
}