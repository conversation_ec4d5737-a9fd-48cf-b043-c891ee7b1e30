package com.cy.education.service.forum;

import com.cy.education.model.entity.forum.ForumComment;

import java.util.List;

/**
 * 论坛评论服务接口
 */
public interface ForumCommentService {

    /**
     * 获取帖子的评论列表（树形结构）
     *
     * @param postId 帖子ID
     * @param sortBy 排序方式：latest-最新，hot-最热
     * @return 评论列表
     */
    List<ForumComment> getPostComments(Integer postId, String sortBy);

    /**
     * 获取评论详情
     *
     * @param id 评论ID
     * @return 评论信息
     */
    ForumComment getCommentById(Integer id);

    /**
     * 创建评论
     *
     * @param postId    帖子ID
     * @param content   评论内容
     * @param authorId  作者ID
     * @param parentId  父评论ID（可选）
     * @param replyToId 回复目标用户ID（可选）
     * @return 评论信息
     */
    ForumComment createComment(Integer postId, String content, Integer authorId, Integer parentId, Integer replyToId);

    /**
     * 删除评论（用户删除自己的评论）
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 是否成功
     */
    boolean deleteCommentByUser(Integer commentId, Integer userId);
}
