<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar-placeholder"></view>
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">通知公告</text>
        <!-- 占位符，保持标题居中 -->
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 内容容器 -->
    <view class="content-container with-navbar">
      <!-- 操作栏 -->
      <view v-if="noticeList.length > 0" class="action-bar">
        <view class="unread-info">
          <text class="unread-count">未读 {{ unreadCount }}</text>
        </view>
        <view v-if="unreadCount > 0" class="mark-all-read" @tap="markAllRead">
          <text class="mark-all-text">全部已读</text>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading" class="loading-more">
        <up-loading-icon color="#667eea"></up-loading-icon>
        <text>加载中...</text>
      </view>

      <!-- 空状态 -->
      <up-empty
          v-if="noticeList.length === 0 && !loading"
          mode="list"
          text="空空如也"
          textColor="#909399"
          textSize="14"
      >
      </up-empty>

      <view v-else class="notice-list">
        <view
            v-for="(notice, index) in noticeList"
            :key="index"
            class="notice-card"
            @tap="goToNoticeDetail(notice)"
        >
          <view class="notice-icon">
            <up-icon
                :name="notice.importance == 3 ? 'warning-fill' : 'volume-fill'"
                :color="notice.importance == 3 ? '#fa709a' : '#667eea'"
                size="24"
            ></up-icon>
            <view v-if="!notice.isRead" class="unread-dot"></view>
          </view>

          <view class="notice-content">
            <view class="notice-header">
              <text class="notice-title">{{ notice.title }}</text>
              <view class="notice-tags">
                <view v-if="notice.importance" class="tag" :class="'level-' + (notice.importance || 1)">
                  <text class="tag-text"> {{ getImportanceText(notice.importance) }}</text>
                </view>
                <view v-if="!notice.isRead" class="tag unread">
                  <text class="tag-text">未读</text>
                </view>
              </view>
            </view>
            <view class="notice-meta">
              <text class="notice-time">{{ formatTime(notice.publishTime) }}</text>
              <text class="notice-author">{{ notice.author || '系统管理员' }}</text>
            </view>
          </view>

          <view class="notice-arrow">
            <up-icon name="arrow-right" size="16" color="#c0c4cc"></up-icon>
          </view>
        </view>
        <uni-load-more :status="loadMoreStatus"/>
        <!-- 加载更多 -->
        <view class="load-more" v-if="noticeList.length > 0">
          <view v-if="loadMoreStatus === 'loading'" class="loading-text">
            <text>加载中...</text>
          </view>
          <view v-else-if="loadMoreStatus === 'noMore'" class="nomore-text">
            <text>没有更多了</text>
          </view>
          <view v-else class="loadmore-text">
            <text>加载更多</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, onMounted, computed} from 'vue'
import {getNoticeList, type NoticeItem as RawNoticeItem} from '@/api/content'
import {onPullDownRefresh, onReachBottom, onShow} from "@dcloudio/uni-app";
import {formatTime} from "@/utils/timeUtil";
import {isNoticeRead, markNoticeAsRead, markNoticesAsRead, getUnreadCount} from '@/utils/storage'

type NoticeItem = RawNoticeItem & { isRead?: boolean; author?: string }
const loading = ref(true)
const noticeList = ref<NoticeItem[]>([])
const loadMoreStatus = ref('more')
const pageNum = ref(1)
const pageSize = ref(4)
const total = ref(0)
const isLoadingMore = ref(false)
const hasMore = ref(true)

// 计算未读通知数量
const unreadCount = computed(() => getUnreadCount(noticeList.value))

// 为通知列表添加已读状态
const addReadStatusToNotices = (notices: RawNoticeItem[]): NoticeItem[] => {
  return notices.map(notice => ({
    ...notice,
    isRead: isNoticeRead(notice.id),
    author: '系统管理员'
  }))
}

// 刷新noticeList的isRead状态
const refreshNoticeReadStatus = () => {
  noticeList.value = addReadStatusToNotices(noticeList.value)
}

// 下拉刷新
onPullDownRefresh(() => {
  pageNum.value = 1
  noticeList.value = []
  hasMore.value = true
  fetchNotices()
})

// 上拉加载更多
onReachBottom(() => {
  if (!hasMore.value || isLoadingMore.value) return
  pageNum.value += 1
  fetchNotices(true)
})

// 页面显示时刷新已读状态
onShow(() => {
  refreshNoticeReadStatus()
})

const fetchNotices = async (isLoadMore = false) => {
  if (isLoadMore) {
    isLoadingMore.value = true
    loadMoreStatus.value = 'loading'
  } else {
    loading.value = true
    pageNum.value = 1
    noticeList.value = []
    hasMore.value = true
  }
  try {
    const res = await getNoticeList({pageNum: pageNum.value, pageSize: pageSize.value})
    total.value = res.total || 0

    // 为通知添加已读状态
    const noticesWithReadStatus = addReadStatusToNotices(res.list)

    if (isLoadMore) {
      noticeList.value = noticeList.value.concat(noticesWithReadStatus)
    } else {
      noticeList.value = noticesWithReadStatus
    }
    if (noticeList.value.length >= total.value) {
      hasMore.value = false
      loadMoreStatus.value = 'noMore'
    } else {
      hasMore.value = true
      loadMoreStatus.value = 'more'
    }
  } finally {
    loading.value = false
    isLoadingMore.value = false
    uni.stopPullDownRefresh();
  }
}

const goBack = () => {
  uni.navigateBack()
}

const markAllRead = () => {
  // 获取所有未读通知的ID
  const unreadIds = noticeList.value
      .filter(notice => !notice.isRead)
      .map(notice => notice.id)

  if (unreadIds.length > 0) {
    // 批量标记为已读
    markNoticesAsRead(unreadIds)

    // 更新本地状态
    noticeList.value.forEach(notice => {
      notice.isRead = true
    })

    uni.showToast({
      title: '已全部标记为已读',
      icon: 'success'
    })
    refreshNoticeReadStatus()
  } else {
    uni.showToast({
      title: '没有未读通知',
      icon: 'none'
    })
  }
}

const goToNoticeDetail = (notice: NoticeItem) => {
  // 如果通知未读，标记为已读
  if (!notice.isRead) {
    markNoticeAsRead(notice.id)
    notice.isRead = true
    refreshNoticeReadStatus()
  }
  uni.navigateTo({url: `/pages/home/<USER>
}

const getImportanceText = (level?: number) => {
  switch (level) {
    case 1:
      return '一般';
    case 2:
      return '重要';
    case 3:
      return '紧急';
    default:
      return '一般';
  }
}

onMounted(() => {
  fetchNotices()
})
</script>

<style lang="scss" scoped>
@import '@/styles/pages/home/<USER>';
</style>
