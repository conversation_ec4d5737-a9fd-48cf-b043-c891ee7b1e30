<template>
  <div class="single-file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :show-file-list="false"
      :accept="accept"
      class="upload-demo"
    >
      <el-button type="primary" :loading="uploading">
        <el-icon><Upload /></el-icon>
        {{ uploading ? '上传中...' : '选择文件' }}
      </el-button>
    </el-upload>
    
    <div v-if="fileUrl" class="file-preview">
      <div class="file-info">
        <el-icon><Document /></el-icon>
        <span class="file-name">{{ fileName }}</span>
        <el-button type="text" @click="clearFile">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      <div class="file-url">
        <el-input v-model="fileUrl" readonly>
          <template #append>
            <el-button @click="copyUrl">复制</el-button>
          </template>
        </el-input>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Document, Close } from '@element-plus/icons-vue'
import { getDocumentSignature, uploadToOss } from '@/api/oss'

interface Props {
  modelValue?: string
  accept?: string
  maxSize?: number // MB
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  accept: '*',
  maxSize: 100
})

const emit = defineEmits<Emits>()

const uploadRef = ref()
const uploading = ref(false)
const uploadUrl = ref('')
const uploadHeaders = ref({})
const uploadData = ref({})
const fileName = ref('')

const fileUrl = computed({
  get: () => props.modelValue || '',
  set: (value: string) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

const beforeUpload = async (file: File) => {
  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB!`)
    return false
  }

  uploading.value = true
  fileName.value = file.name

  try {
    // 获取OSS上传签名
    const signature = await getDocumentSignature()

    // 直接上传到OSS
    const result = await uploadToOss(file, signature, (percent) => {
      // 这里可以添加进度处理
    })

    fileUrl.value = result.url
    uploading.value = false
    ElMessage.success('上传成功')

    return false // 阻止element-plus的默认上传行为
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
    uploading.value = false
    return false
  }
}

// 这些方法不再需要，因为我们直接在beforeUpload中处理上传

const clearFile = () => {
  fileUrl.value = ''
  fileName.value = ''
}

const copyUrl = () => {
  navigator.clipboard.writeText(fileUrl.value).then(() => {
    ElMessage.success('URL已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}
</script>

<style scoped>
.single-file-upload {
  width: 100%;
}

.file-preview {
  margin-top: 12px;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.file-name {
  margin-left: 8px;
  flex: 1;
  font-size: 14px;
  color: #606266;
}

.file-url {
  width: 100%;
}
</style>
