package com.cy.education.controller;

import com.cy.education.model.entity.study.Course;
import com.cy.education.model.vo.ApiResponse;
import com.cy.education.model.vo.CourseVO;
import com.cy.education.model.vo.PageResponse;
import com.cy.education.service.study.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/courses")
public class CourseController {

    @Autowired
    private CourseService courseService;

    /**
     * 获取课程列表
     *
     * @param pageNum
     * @param pageSize
     * @param name
     * @return
     */
    @GetMapping
    public ApiResponse<PageResponse<Course>> getCourses(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name) {
        PageResponse<Course> courseList = courseService.getCourseList(pageNum, pageSize, name);
        return ApiResponse.success(courseList);
    }

    /**
     * 获取课程详情 包含当前用户学习进度
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ApiResponse<CourseVO> getCourseById(@PathVariable Integer id) {
        CourseVO courseVO = courseService.getCourseByIdWithStudyRecord(id);
        return ApiResponse.success(courseVO);
    }
}
