package com.cy.education.service.impl.points;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cy.education.model.entity.points.PointsProduct;
import com.cy.education.model.params.PointsProductQueryParam;
import com.cy.education.repository.PointsProductMapper;
import com.cy.education.service.points.PointsProductService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 积分商品服务实现类
 */
@Service
public class PointsProductServiceImpl extends ServiceImpl<PointsProductMapper, PointsProduct> implements PointsProductService {

    @Override
    public IPage<PointsProduct> page(PointsProductQueryParam param) {
        // 创建分页对象
        Page<PointsProduct> page = new Page<>(param.getPageNum(), param.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<PointsProduct> wrapper = new LambdaQueryWrapper<>();

        // 商品名称条件
        if (StringUtils.isNotBlank(param.getName())) {
            wrapper.like(PointsProduct::getName, param.getName());
        }

        // 商品分类条件
        if (StringUtils.isNotBlank(param.getCategory())) {
            wrapper.eq(PointsProduct::getCategory, param.getCategory());
        }

        // 状态条件
        wrapper.eq(PointsProduct::getStatus, "active");

        // 库存
        wrapper.gt(PointsProduct::getStock, 0);

        // 排序
        if (StringUtils.isNotBlank(param.getSortBy())) {
            boolean isAsc = "asc".equalsIgnoreCase(param.getSortOrder());
            switch (param.getSortBy()) {
                case "points":
                    wrapper.orderBy(true, isAsc, PointsProduct::getPoints);
                    break;
                case "stock":
                    wrapper.orderBy(true, isAsc, PointsProduct::getStock);
                    break;
                case "exchangeCount":
                    wrapper.orderBy(true, isAsc, PointsProduct::getExchangeCount);
                    break;
                case "createdAt":
                    wrapper.orderBy(true, isAsc, PointsProduct::getCreatedAt);
                    break;
                default:
                    wrapper.orderBy(true, false, PointsProduct::getCreatedAt);
                    break;
            }
        } else {
            // 默认按创建时间降序
            wrapper.orderByDesc(PointsProduct::getCreatedAt);
        }

        // 执行查询
        return page(page, wrapper);
    }

    @Override
    public PointsProduct getById(Integer id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean decreaseStock(Integer id, Integer count) {
        // 减少库存
        return baseMapper.decreaseStock(id, count) > 0;
    }
}
