package com.cy.education.model.entity.study;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("resources")
public class Resource {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private String type;

    private String content;

    private String tags;

    private Long uploaderId;

    private Date createTime;

    private Date updateTime;
}
