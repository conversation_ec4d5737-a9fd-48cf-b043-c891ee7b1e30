import { get } from './request'

/**
 * 检查token是否存在
 */
export function hasToken(): boolean {
  const token = uni.getStorageSync('token')
  return !!token && token.trim() !== ''
}

/**
 * 获取token
 */
export function getToken(): string {
  return uni.getStorageSync('token') || ''
}

/**
 * 设置token
 */
export function setToken(token: string): void {
  uni.setStorageSync('token', token)
}

/**
 * 移除token
 */
export function removeToken(): void {
  uni.removeStorageSync('token')
  uni.removeStorageSync('userInfo')
  uni.removeStorageSync('userId')
}

/**
 * 验证token是否有效
 * 通过调用后端接口验证token
 */
export async function validateToken(): Promise<boolean> {
  if (!hasToken()) {
    return false
  }

  try {
    // 调用后端接口验证token
    await get('/user/info')
    return true
  } catch (error) {
    // token无效，清除本地存储
    removeToken()
    return false
  }
}

/**
 * 检查用户是否已登录
 */
export async function checkAuth(): Promise<boolean> {
  return await validateToken()
}

/**
 * 跳转到登录页
 */
export function redirectToLogin(): void {
  uni.reLaunch({
    url: '/pages/login/index'
  })
}

/**
 * 跳转到首页
 */
export function redirectToHome(): void {
  uni.reLaunch({
    url: '/pages/home/<USER>'
  })
} 