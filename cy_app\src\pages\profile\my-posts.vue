<template>
  <view class="page-container">
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @click="goBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">我的帖子</text>
        <view class="placeholder"></view>
      </view>
    </view>
    <view class="main-content">
      <view class="posts-container">
        <view
            v-for="post in postList"
            :key="post.id"
            class="post-card"
            @click="goToPostDetail(post)"
        >
          <view class="post-header">
            <up-avatar :src="post.authorAvatar" size="40"></up-avatar>
            <view class="user-info">
              <text class="username">{{ post.author }}</text>
            </view>
          </view>
          <view class="post-content">
            <view class="post-title-row">
              <text class="post-title">{{ post.title }}</text>
              <view class="post-time-wrapper">
                <text class="post-time">{{ formatTime(post.createdAt) }}</text>
              </view>
            </view>
            <text class="post-excerpt">{{ post.content }}</text>
          </view>
          <view class="post-actions">
            <view class="action-stats">
              <view class="stat-item">
                <up-icon color="#909399" name="chat" size="16"></up-icon>
                <text>{{ post.replyCount || 0 }}</text>
              </view>
              <view class="stat-item">
                <up-icon color="#909399" name="thumb-up" size="16"></up-icon>
                <text>{{ post.likeCount || 0 }}</text>
              </view>
              <view class="stat-item">
                <up-icon color="#909399" name="star" size="16"></up-icon>
                <text>{{ post.collectCount || 0 }}</text>
              </view>
              <view class="stat-item">
                <up-icon color="#909399" name="eye" size="16"></up-icon>
                <text>{{ post.viewCount }}</text>
              </view>
            </view>
          </view>
        </view>
        <up-empty
            v-if="postList.length === 0 && !loading"
            mode="list"
            text="暂无帖子"
            textColor="#909399"
            textSize="14"
        >
          <template #bottom>
            <up-button size="small" text="去发帖" type="primary" @click="goToForum"></up-button>
          </template>
        </up-empty>
        <view v-if="postList.length > 0" class="load-more">
          <view v-if="loadMoreStatus === 'loading'" class="loading-text">
            <up-loading-icon color="#667eea" size="16"></up-loading-icon>
            <text>加载中...</text>
          </view>
          <view v-else-if="loadMoreStatus === 'noMore'" class="nomore-text">
            <text>没有更多了</text>
          </view>
          <view v-else class="loadmore-text">
            <text>加载更多</text>
          </view>
        </view>
      </view>
      <up-gap height="80"></up-gap>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {getPostListByUserId} from '@/api/forum'
import {formatTime} from '@/utils/timeUtil'
import {onPullDownRefresh, onReachBottom} from "@dcloudio/uni-app";

const postList = ref<any[]>([])
const loading = ref(true)
const hasMore = ref(true)
const pageNum = ref(1)
const pageSize = ref(5)
const total = ref(0)
const isLoadingMore = ref(false)
const loadMoreStatus = ref<'more' | 'loading' | 'noMore'>('more')

const userInfo = uni.getStorageSync('userInfo') || {}
const userId = userInfo.id

const goBack = () => {
  uni.navigateBack()
}

const fetchPosts = async (isLoadMore = false) => {
  if (isLoadMore) {
    isLoadingMore.value = true
    loadMoreStatus.value = 'loading'
  } else {
    loading.value = true
    pageNum.value = 1
    postList.value = []
    hasMore.value = true
  }
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value
    }
    const res = await getPostListByUserId(parseInt(userId), params)
    total.value = res.total || 0
    if (isLoadMore) {
      postList.value = postList.value.concat(res.list || [])
    } else {
      postList.value = res.list || []
    }
    if (postList.value.length >= total.value) {
      hasMore.value = false
      loadMoreStatus.value = 'noMore'
    } else {
      hasMore.value = true
      loadMoreStatus.value = 'more'
    }
  } catch (e) {
    uni.showToast({title: '加载失败', icon: 'none'})
  } finally {
    loading.value = false
    isLoadingMore.value = false
    uni.stopPullDownRefresh()
  }
}

onMounted(() => {
  fetchPosts()
})

// 下拉刷新
onPullDownRefresh(() => {
  pageNum.value = 1
  postList.value = []
  hasMore.value = true
  fetchPosts()
})
// 上拉加载更多
onReachBottom(() => {
  if (!hasMore.value || isLoadingMore.value) return
  pageNum.value += 1
  fetchPosts(true)
})

const goToPostDetail = (post: any) => {
  uni.navigateTo({url: `/pages/forum/detail?id=${post.id}`})
}
const goToForum = () => {
  uni.reLaunch({url: '/pages/forum/index'})
}
</script>

<style lang="scss" scoped>
@import '@/styles/pages/forum/forum.scss';
</style>
