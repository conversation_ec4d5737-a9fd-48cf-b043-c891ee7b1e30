<template>
  <view class="page-container">
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="back-btn" @tap="navigateBack">
          <up-icon color="#fff" name="arrow-left" size="12"></up-icon>
        </view>
        <text class="navbar-title">积分明细</text>
        <view class="placeholder"></view>
      </view>
    </view>
    <!-- 加载更多 -->
    <view v-if="loading" class="loading-more">
      <up-loading-icon color="#667eea"></up-loading-icon>
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <up-empty
        v-if="pointsRecords.length === 0 && !loading"
        mode="list"
        text="空空如也"
        textColor="#909399"
        textSize="14"
    >
    </up-empty>
    <view v-else class="records-list">
      <view v-for="record in pointsRecords" :key="record.id" class="record-card">
        <view class="record-info">
          <text class="record-desc">{{ record.description }}</text>
          <text class="record-date">{{ record.date }}</text>
        </view>
        <text class="record-points" :class="{ positive: record.points > 0 }">
          {{ record.points > 0 ? '+' : '' }}{{ record.points }}
        </text>
      </view>
      <!-- 加载更多 -->
      <view class="load-more" v-if="pointsRecords.length > 0">
        <view v-if="loadMoreStatus === 'loading'" class="loading-text">
          <text>加载中...</text>
        </view>
        <view v-else-if="loadMoreStatus === 'noMore'" class="nomore-text">
          <text>没有更多了</text>
        </view>
        <view v-else class="loadmore-text">
          <text>加载更多</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {onPullDownRefresh, onReachBottom} from '@dcloudio/uni-app'
import {getPointsRecords} from '@/api/points'

const loading = ref(true)
const pointsRecords = ref<any[]>([])
const loadMoreStatus = ref<'more' | 'loading' | 'noMore'>('more')
const pageNum = ref(1)
const pageSize = ref(4)
const total = ref(0)
const isLoadingMore = ref(false)
const hasMore = ref(true)

const navigateBack = () => {
  uni.navigateBack()
}

// 下拉刷新
onPullDownRefresh(() => {
  pageNum.value = 1
  pointsRecords.value = []
  hasMore.value = true
  fetchRecords()
})

// 上拉加载更多
onReachBottom(() => {
  if (!hasMore.value || isLoadingMore.value) return
  pageNum.value += 1
  fetchRecords(true)
})

const fetchRecords = async (isLoadMore = false) => {
  if (isLoadMore) {
    isLoadingMore.value = true
    loadMoreStatus.value = 'loading'
  } else {
    loading.value = true
    pageNum.value = 1
    pointsRecords.value = []
    hasMore.value = true
  }
  try {
    const res = await getPointsRecords({pageNum: pageNum.value, pageSize: pageSize.value})
    total.value = res.total || 0
    if (isLoadMore) {
      pointsRecords.value = pointsRecords.value.concat(res.list)
    } else {
      pointsRecords.value = res.list
    }
    if (pointsRecords.value.length >= total.value) {
      hasMore.value = false
      loadMoreStatus.value = 'noMore'
    } else {
      hasMore.value = true
      loadMoreStatus.value = 'more'
    }
  } finally {
    loading.value = false
    isLoadingMore.value = false
    uni.stopPullDownRefresh();
  }
}

onMounted(() => {
  fetchRecords()
})
</script>

<style lang="scss" scoped>
.page-container {
  background: #f5f6fa;
  min-height: 100vh;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px 16px 32px 16px;
}

.record-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
  padding: 18px 20px;
  transition: box-shadow 0.2s;

  &:active {
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
  }
}

.record-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.record-desc {
  font-size: 15px;
  color: #1a1d2e;
  font-weight: 500;
  margin-bottom: 2px;
}

.record-date {
  font-size: 12px;
  color: #8e8e93;
}

.record-points {
  font-size: 18px;
  font-weight: 600;
  color: #ff4d4f;
  min-width: 60px;
  text-align: right;

  &.positive {
    color: var(--primary-color);
  }
}
</style>
